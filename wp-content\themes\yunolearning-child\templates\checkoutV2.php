
<?php 
	/**
		* Template Name: Yuno Checkout V2
		* Template Post Type: page
		*
		* @package WordPress
		* @subpackage Yuno
	*/

    $page = "checkoutV2";

	function enqueue_styles() {
        global $page;
        wp_enqueue_style('buefy', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');		
		wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/'. $page .'/dist/'. $page .'.min.css', array(), false, 'all');		
	}

	function enqueue_scripts() {
        global $page;
		wp_enqueue_script('razorpay', 'https://checkout.razorpay.com/v1/checkout.js', array(), 2.9, true);
		wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/'. $page .'/dist/'. $page .'.min.js#deferload', array(), 1.0, true);
	}

	add_action( 'wp_enqueue_scripts', 'enqueue_styles' ); // default priority: 10
	add_action( 'wp_enqueue_scripts', 'enqueue_scripts', 9); // default priority: 10

	get_header();
	
?>

	<yuno-checkout-v2></yuno-checkout-v2>
	
<?php get_footer();?>
