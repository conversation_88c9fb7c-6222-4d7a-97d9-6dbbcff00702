<?php

namespace V4;

/**
 * Counselor model
 */

class CounselorModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    public function getCounselor($query, $filter = [])
    {

        $counselorDataResponse = $this->loadModel('user')->getUser($query, $filter); // counselor

        if ($counselorDataResponse !== false && $this->userModel->checkRole($counselorDataResponse['role'], $this->userModel->yn_Counselor) !== false) {
            return $counselorDataResponse;
        }
        return false;
    }

    /**
     * Generates counselor filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The ID of the instructor (for filtering instructor-specific counselors).
     * @param int $learnerId The ID of the learner (for filtering learner-specific counselors).
     * @param int $counselorId The selected counselor ID (if any).
     * @return array Returns an array containing counselor filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentCounselorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'counselor_id',
            'title' => 'Counselor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Counselor',
            'ui_control_type' => 'dynamic_dropdown',
            'selected' => $counselorId, //  Pre-select counselor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];

    }
    public function generateEnrollmentCounselorFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'counselor',
            'title' => 'Counselor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Counselor',
            'ui_control_type' => 'dropdown',
            'selected' => $counselorId, //  Pre-select counselor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "counselor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select counselor if provided
        if ($counselorId > 0) {
            $counselorData = $this->getCounselor($counselorId);
            if (!empty($counselorData)) {
                $filterData['selected'] = $counselorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all counselors (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'instructor' && $instructorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.instructor_id" => $instructorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'learner' && $learnerId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.learner_id" => $learnerId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Counselor Query: " . json_encode($customQuery));

        //  Fetch Counselors from Elasticsearch
        $counselorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($counselorRecords['status_code']) && $counselorRecords['status_code'] === 200) {
            $counselors = $counselorRecords['body']['hits']['hits'];

            foreach ($counselors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundCounselorId = $details['user_id'] ?? 0;
                $counselorName = $userDetails['name'] ?? '';
                $counselorEmail = $userDetails['email'] ?? '';

                if ($foundCounselorId) {
                    $filterData['items'][] = [
                        'id' => $foundCounselorId,
                        'label' => $counselorName . " (" . $counselorEmail . ")",
                        'filter' => 'counselor'
                    ];
                }
            }
        }

        return $filterData;
    }

    /**
     * Generates counselor filters for payments based on the user's role.
     *
     * This function fetches a list of counselors from Elasticsearch and applies role-based filtering.
     * The available counselors depend on the user's role, with yuno-admin having access to all counselors,
     * while org-admins may have organization-specific restrictions.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user requesting the filter.
     * @param int $counselorId The selected counselor ID (if any).
     * @return array Returns an array containing counselor filter data.
     * <AUTHOR>
     */
    public function generatePaymentmentCounselorFilters($userId, $counselorId){
        return [
            'filter' => 'counselor_id',
            'title' => 'Counselor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Counselor',
            'ui_control_type' => 'dynamic_dropdown',
            'selected' => $counselorId, //  Pre-select counselor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];
    }
    public function generatePaymentmentCounselorFiltersOld($userId, $counselorId)
    {
        $filterData = [
            'filter' => 'counselor',
            'title' => 'Counselor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Counselor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $counselorId, // Pre-select counselor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "counselor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select counselor if provided
        if ($counselorId > 0) {
            $counselorData = $this->getCounselor($counselorId);
            if (!empty($counselorData)) {
                $filterData['selected'] = $counselorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all counselors (no additional filters)
        } elseif ($role === 'org-admin') {
        
        }

        // Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Counselor Query: " . json_encode($customQuery));

        //  Fetch Counselors from Elasticsearch
        $counselorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($counselorRecords['status_code']) && $counselorRecords['status_code'] === 200) {
            $counselors = $counselorRecords['body']['hits']['hits'];

            foreach ($counselors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundCounselorId = $details['user_id'] ?? 0;
                $counselorName = $userDetails['name'] ?? '';
                $counselorEmail = $userDetails['email'] ?? '';

                if ($foundCounselorId) {
                    $filterData['items'][] = [
                        'id' => $foundCounselorId,
                        'label' => $counselorName . " (" . $counselorEmail . ")",
                        'filter' => 'counselor'
                    ];
                }
            }
        }

        return $filterData;
    }

}
