<?php
namespace V4;

/**
 * VirtualClassroomModel model
 * 
 * This model handles all virtual classroom related operations including Zoom and Google Meet integration.
 * It provides functionality to manage virtual classroom settings, authentication, and platform-specific operations.
 * 
 * <AUTHOR>
 * @package V4
 */

class VirtualClassroomModel extends Model {
    
    /**
     * Constructor to initialize the VirtualClassroomModel
     * 
     * Loads required libraries for schema validation and utility functions
     * 
     * <AUTHOR>
     */
    function __construct()
    {
        parent::__construct();
        $this->loadLibary('schema');
        $this->loadLibary('utility');
    }
    /**
     * Retrieves the virtual classroom details for a specific platform
     *
     * This function retrieves virtual classroom information including platform details,
     * authentication status, and configuration for either Zoom or Google Meet.
     *
     * @param array|string $query Query parameters or ID to fetch virtual classroom details
     * @param array $filter Optional filter parameters for schema validation
     * @return array|bool Returns virtual classroom details if found, false otherwise
     * 
     * <AUTHOR>
     */
    public function getVirtualClassroom($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['custom'])) {
            $userData['instructorId'] = $query['custom']['id'];
            $userData['platform'] = $query['custom']['platform'];
            $userData['orgId'] = $query['custom']['orgId'];
        } else {
            return false;
        }

        if (is_array($userData) && !empty($userData)) {
            $connection = false;
            if ($userData['platform'] == 'zoom') {  //zoom 
                $state = base64_encode(json_encode(['org_id' => $userData['orgId']]));
                $permission_url = getting_zoom_oauth_app_token();
                $zoomVC = get_user_meta($userData['instructorId'], 'zoom_vc', true);
                $zoomRefreshToken = get_user_meta($userData['instructorId'], 'zoom_refresh_token', true);
                if ($zoomVC === true || $zoomVC === "true") {
                    if (!empty($zoomRefreshToken) && $zoomRefreshToken != "test") {
                        $connection = true;
                    }
                }
                // Build the structured response
                $virtualClassroomResponse = array(
                    'platform' => $userData['platform'], //The virtual classroom platform used: Zoom, Google Meet, etc. Example: meet
                    'name' => 'Zoom', //  Name of the virtual classroom platform , Example: GMEET
                    'permission_url' => preg_replace('/(state=)[^&]+/', '${1}' . $state, $permission_url['path'], 1), //Permission URL of the virtual classroom platform
                    'connection' => $connection, //It represents that platform is active or not , Example: true
                    'logo_url' => [
                        'url' =>  ynAssetURL("images/zoomIcon.png"),
                        "alt_text" => 'zoomLogoIcon'
                    ], //Logo image of the virtual classroom platform
                    'fav_icon_url' => [
                        'url' =>  ynAssetURL("images/zoomIcon.png"),
                        "alt_text" => 'zoomFavIcon'
                    ], //Fav icon of the virtual classroom platform
                    'auth' => $this->load->subData("oauth", "getOauth", $userData['instructorId'], ['schema' => 'Oauth_Minimal']), //oAuth_Minimal
                );
            } else if ($userData['platform'] == 'gmeet') {    //google meet
                $email = get_user_meta($userData['instructorId'], 'yuno_gplus_email', true);
                if (strpos($email, '@gmail.com') !== false) {
                    $permission_url = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
                        'response_type' => 'code',
                        'access_type' => 'offline', // Allows a refresh token for long-term access
                        'client_id' => \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID,
                        'redirect_uri' => \YUNO_OAUTH_APP_REDIRECT_URL,
                        'state' => json_encode([
                            'org_details' => [
                                'auth_ref' => 'virtual-classroom',
                                'org_id' => $userData['orgId'],
                                'org_url' => '',
                                'device_type' => ''
                            ]
                        ]),
                        'scope' => implode(' ', [
                            'email',
                            'profile',
                            'https://www.googleapis.com/auth/calendar',
                            'https://www.googleapis.com/auth/drive.file',
                            'https://www.googleapis.com/auth/calendar.events',
                            'https://www.googleapis.com/auth/admin.reports.audit.readonly'
                        ]),
                        'prompt' => 'consent', // Forces Google to prompt the user for consent each time
                        'flowName' => 'GeneralOAuthFlow'
                    ]);
                } else {
                    $permission_url = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
                        'response_type' => 'code',
                        'access_type' => 'offline',
                        'client_id' => \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID, //YUNO_OAUTH_APP_CLIENT_ID
                        'redirect_uri' => \YUNO_OAUTH_APP_REDIRECT_URL,
                        'state' => json_encode([
                            'org_details' => [
                                'auth_ref' => 'virtual-classroom',
                                'org_id' => $userData['orgId'],
                                'org_url' => '',
                                'device_type' => ''
                            ]
                        ]),
                        'scope' => implode(' ', [
                            'email',
                            'profile',
                            'https://www.googleapis.com/auth/calendar',
                            'https://www.googleapis.com/auth/drive.file',
                            'https://www.googleapis.com/auth/calendar.events',
                            'https://www.googleapis.com/auth/admin.reports.audit.readonly'
                        ]),
                        'prompt' => 'consent',
                        'flowName' => 'GeneralOAuthFlow'
                    ]);
                }
                $googleMeetVC = true;
                $userAccessToken = get_google_meet_access_token($userData['instructorId'], $userData['orgId']);
                $meetUrl = GOOGLE_MEET_API_URL;
    
                $headers = [
                    'Authorization: Bearer ' .$userAccessToken
                ];
                $params = '';
                $jsonString = $this->utility->curlRequest($meetUrl, 'GET',  $params, $headers);
                // Decode JSON into associative array
                if (isset($jsonString['response']['error'])) {
                    $googleMeetVC = false;
                }
                $virtualClassroomData = get_user_meta($userData['instructorId'], 'virtual_classroom_data', true);
                if (!empty($virtualClassroomData)) {
                // Loop through each item in the data array
                foreach ($virtualClassroomData['data'] as $item) {
                    if ($item['org_id'] == $userData['orgId']) {
                        // Check if the email exists in either 'meet' or 'zoom' within 'virtual_classroom'
                        if (isset($item['virtual_classroom']['meet']['email'])) {
                            $scope = $item['virtual_classroom']['meet']['scope'];
                            $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events', 'https://www.googleapis.com/auth/drive.file', 'https://www.googleapis.com/auth/admin.reports.audit.readonly'];  
                            $meet_scopes = explode(' ', $scope ?? '');
                            $has_required_scopes = !array_diff($required_scopes, $meet_scopes);
                            if ($googleMeetVC === true) {
                                if ($has_required_scopes) {
                                    $connection = true;
                                }
                            }
                            break;
                        }
                    }
                }
                }
                // Build the structured response
                $virtualClassroomResponse = array(
                    'platform' => $userData['platform'], //The virtual classroom platform used: Zoom, Google Meet, etc. Example: meet
                    'name' => 'Google Meet', //  Name of the virtual classroom platform , Example: GMEET
                    'permission_url' => $permission_url, //Permission URL of the virtual classroom platform
                    'connection' => $connection, //It represents that platform is active or not , Example: true
                    'logo_url' => [
                        'url' =>  ynAssetURL("images/googleMeetIcon.png"),
                        "alt_text" => 'googleMeetLogoIcon'
                    ], //Logo image of the virtual classroom platform
                    'fav_icon_url' => [
                        'url' =>  ynAssetURL("images/googleMeetIcon.png"),
                        "alt_text" => 'googleMeetFavIcon'
                    ], //Fav icon of the virtual classroom platform
                    'auth' => $this->load->subData("oauth", "getOauth", $userData['instructorId'], ['schema' => 'Oauth_Minimal']), //oAuth_Minimal
                );
            } else {
                $virtualClassroomResponse = array(
                    'platform' => 'Free', //The virtual classroom platform used: Zoom, Google Meet, etc. Example: meet
                    'name' => '', //  Name of the virtual classroom platform , Example: GMEET
                    'permission_url' => '', //Permission URL of the virtual classroom platform
                    'connection' => false, //It represents that platform is active or not , Example: true
                    'logo_url' => [
                        'url' =>  '',
                        "alt_text" => ''
                    ], //Logo image of the virtual classroom platform
                    'fav_icon_url' => [
                        'url' =>  '',
                        "alt_text" => ''
                    ], //Fav icon of the virtual classroom platform
                    'auth' => ['auth_email'=>''], //oAuth_Minimal
                );
            }

            return $this->schema->validate($virtualClassroomResponse, 'Virtual_Classroom', $filter);
        }
        return false;
    }
}
