
Vue.component('yuno-org-theme', {
    props: ["data", "options", "resourceloaded"],
    template: `
        <span></span>
    `,
    data() {
        return {
            
        }
    },
    watch: {
        resourceloaded(data) {
            if (data) {
                this.resourceFetched();
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin',
        ]),
        isNotYunoLearning() {
            const allowedHostnames = ['yunolearning.com'];
            return !allowedHostnames.includes(window.location.hostname);
        }
    },
    async created() {
        this.fetchOrgInfo(this.$props.options.orgID);
    },
    mounted() {
        
    },
    methods: {
        loadGoogleFont(fontName) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=' + encodeURIComponent(fontName) + '&display=swap';
            document.head.appendChild(link);
        },
        resourceFetched() {
            const theme = this.orgAdmin.data.theme;

            this.loadGoogleFont(theme.font_family);
            document.documentElement.style.setProperty('--primary-color', theme.primary_color);
            document.documentElement.style.setProperty('--body-bg-color', theme.background_color);
            document.documentElement.style.setProperty('--font-family', theme.font_family);
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                if (this.$props.resourceloaded === undefined) {
                    this.resourceFetched();
                }

                this.$emit("orgFetched");
            };
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

            console.log(this.isNotYunoLearning)

            if (this.isNotYunoLearning) {
                // Dispatch the API call
                this.$store.dispatch('fetchData', options);     
            }
        },
    }
});
Vue.component('yuno-powered-by', {
    props: ["isOrg"],
    template: `
        <footer class="leadFormFooter">
            <div class="container">
                <div class="columns is-mobile is-centered">
                    <div class="poweredBy">
                        <p>Powered By</p>
                        <img
                            :src="logo"
                            alt="Yuno Learning"
                            width="51"
                            height="24"
                        >
                    </div>
                </div>
            </div>
        </footer>
    `,
    data() {
        return {
            logo: this.$store.state.themeURL + "/assets/images/yuno-logo-grey.svg",
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});
Vue.component('yuno-batch', {
    props: ["data", "options"],
    template: `
        <div class="batch">
            <div class="row">
                <div class="col-12">
                    <div class="spaceBetween">
                        <h2 class="price">
                            <template v-if="data.price_included_gst !== 0 && data.price_included_gst !== '0' && data.price_included_gst !== 'Free'">
                                ₹{{ data.price_included_gst }}
                            </template>
                            <template v-else>
                                Free
                            </template>
                        </h2>
                        <b-button 
                            @click="updateBatch()"
                            class="yunoPrimaryCTA wired">
                            Change Batch
                        </b-button>
                    </div>
                    <ul class="batchInfo">
                        <li>Your enrollment starts on {{ data.enrollment_starts }}</li>
                        <li>Class time: {{ data.class_start_time }}</li>
                        <li class="grid">Class days: 
                            <ul class="classDays">
                                <li 
                                    v-for="(day, d) in data.class_days" 
                                    :key="d"
                                    :class="[day.is_available ? '' : 'disabled']">
                                    {{day.label}}
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        updateBatch() {
            Event.$emit('updateBatch');
        }
    }
});
window.Event = new Vue();

const validationMsg = {
    messages: {
        required: "This field is required",
        numeric: "Numbers only",
        min: "Minimum 10 numbers required",
        max: "Maximum 15 numbers required",
        is: "Required",
        is_not: "",
        required_if: ""
    }
};
YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-batch-page-v2', {
    template: `
        <div :class="[isOrg ? 'orgEnabled' : '']">
            <yuno-page-loader v-if="isPageLoading"></yuno-page-loader>
            <yuno-org-theme v-if="isOrg" :options="{
                    'orgID': orgID
                }" 
                :resourceloaded="moduleWithoutTab.data"
            >
            </yuno-org-theme>
            <div v-show="isPageReady">
                <yuno-loader></yuno-loader>
                <yuno-header-revamp :hasnav="false"></yuno-header-revamp>
                <yuno-org-header v-if="isOrg"></yuno-org-header>
                <main id="yunoMain">
                    <section class="container checkout">
                        <div class="mainHeader" v-if="moduleWithoutTab.success">
                            <div class="block">
                                <h1 class="pageTitle">{{ pageHeader.title }}</h1>
                            </div>
                        </div>
                        <template v-if="noBatchSelected">
                            <yuno-empty-states :options="{'state': 'dataNotFound'}"></yuno-empty-states>
                        </template>
                        <template v-if="moduleWithoutTab.loading || subform.loading">
                            <div class="smallLoader"></div>
                        </template>
                        <template v-if="moduleWithoutTab.success">
                            <template v-if="moduleWithoutTab.error">
                                <p>{{ moduleWithoutTab.errorData }}</p>
                            </template>
                            <template v-else>
                                <div class="row">
                                    <div class="col-12 col-md-5">
                                        <yuno-batch :data="moduleWithoutTab.data"></yuno-batch>
                                        <yuno-user-list :data="moduleWithoutTab.data"></yuno-user-list>
                                    </div>
                                    <div class="col-12 col-md-1"></div>
                                    <div class="col-12 col-md-6">
                                        <yuno-course :data="moduleWithoutTab.data" :options="courseOptions" :hasReferralCode="hasReferralCode"></yuno-course>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </section>
                    <b-modal 
                        :active.sync="paymentSuccess.modal" 
                        :width="500" 
                        :can-cancel="['x']"
                        :on-cancel="paymentSuccessClose"
                        class="yunoModal loginSignupModal">
                            <template v-if="paymentSuccess.modal">
                                <div class="modalHeader">
                                    <h3 class="modalTitle">Successfully Enrolled</h3>
                                </div>
                                <div class="modalBody">
                                    <div class="successMsg">
                                        <h3>Hi {{userProfile.data.yuno_display_name}}</h3>
                                        <template v-if="paymentSuccess.enrollmentType !== 'free'">
                                            <h4 class="subTitle">
                                                Thanks for enrolling the <span v-html="paymentSuccess.batch.title"></span>
                                            </h4>
                                            <ul class="alertGrid">
                                                <li class="alertGridLabel">Transaction ID:</li>
                                                <li class="alertGridVal">{{paymentSuccess.rzp.razorpay_payment_id}}</li>
                                                <li class="alertGridLabel">Order Amount:</li>
                                                <li class="alertGridVal">₹{{paymentSuccess.batch.amount}}</li>
                                            </ul>
                                            <p v-if="false">{{paymentSuccess.message}}</p>
                                        </template>
                                        <template v-else>
                                            <h4 class="subTitle">
                                                Thank you for enrolling. As soon as your instructor schedules a class, you'll be notified.
                                            </h4>
                                            <b-button 
                                                tag="a"
                                                href="/learner"
                                                @click="paymentSuccessClose"
                                                class="yunoSecondaryCTA">
                                                Upcoming Classes
                                            </b-button>
                                        </template>
                                    </div>
                                </div>
                            </template>
                    </b-modal>
                    <b-modal 
                        :active.sync="updateBatches.modal" 
                        :width="1000"
                        :can-cancel="['x']"
                        :on-cancel="updateBatchesClose"
                        class="yunoModal loginSignupModal hasMinHeight">
                            <template v-if="updateBatches.modal">
                                <div class="modalHeader">
                                    <h3 class="modalTitle">Availability</h3>
                                </div>
                                <div class="modalBody">
                                    <yuno-batches :data="courseBatches" :options="{'batchesOptions': batchesOptions, 'filters': filters}"></yuno-batches>
                                </div>
                            </template>
                    </b-modal>
                    <b-modal 
                        :active.sync="chooseCourse.modal" 
                        :width="500"
                        :can-cancel="false"
                        :on-cancel="chooseCourseClose"
                        class="yunoModal loginSignupModal">
                            <template v-if="chooseCourse.modal">
                                <div class="modalHeader">
                                    <h3 class="modalTitle">Choose Course</h3>
                                </div>
                                <div class="modalBody">
                                    <p class="marginBtm16">Please select a course that you would like to enroll in</p>
                                    <template v-for="(course, i) in chooseCourse.data">
                                        <b-field :key="i">
                                            <b-radio v-model="chooseCourse.selected"
                                                :native-value="course.id"
                                                @input="onCourseSelect(course)"
                                            >
                                                {{ course.title }}
                                            </b-radio>
                                        </b-field>
                                    </template>
                                </div>
                            </template>
                    </b-modal>
                </main>
                <yuno-powered-by v-if="isOrg" :isOrg="isOrg"></yuno-powered-by>
                <yuno-footer :isnav="false"></yuno-footer>
            </div>
        </div>
    `,
    data: () => ({
        isOrg: false,
        orgID: "",
        chooseCourse: {
            modal: false,
            data: [],
            selected: ""
        },
        noBatchSelected: false,
        courseOptions: {
            isLoading: false
        },
        pageHeader: {
            title: "Checkout"
        },
        paymentSuccess: {
            modal: false,
            batch: [],
            rzp: [],
            enrollmentType: "",
            message: "You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"
        },
        updateBatches: {
            modal: false
        },
        batchesOptions: {
            courseID: "",
            batch_count: "",
            limit: 20,
            offset: 0
        },
        payload: {
            batch_days: ["sun", "mon", "tue", "wed", "thu", "fri", "sat"],
            batch_time: ["morning", "afternoon", "evening", "night"],
            personalisation: "one_to_many"
        },
        filters: [
            {
                type: "groupMultiSelect",
                slug: "class_days_time",
                items: [
                    {
                        slug: "class_days",
                        label: "Class Days",
                        items: [
                            {
                                label: "Sun",
                                filter: "class_days_time",
                                slug: "sun",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Mon",
                                filter: "class_days_time",
                                slug: "mon",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Tue",
                                filter: "class_days_time",
                                slug: "tue",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Wed",
                                filter: "class_days_time",
                                slug: "wed",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Thu",
                                filter: "class_days_time",
                                slug: "thu",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Fri",
                                filter: "class_days_time",
                                slug: "fri",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Sat",
                                filter: "class_days_time",
                                slug: "sat",
                                parentSlug: "class_days",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: false
                            }
                        ]
                    },
                    {
                        slug: "class_time",
                        label: "Class Time",
                        items: [
                            {
                                label: "Morning",
                                filter: "class_days_time",
                                slug: "morning",
                                parentSlug: "class_time",
                                interval: "6 - 12",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Afternoon",
                                filter: "class_days_time",
                                slug: "afternoon",
                                parentSlug: "class_time",
                                interval: "12 - 4",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: false
                            },
                            {
                                label: "Evening",
                                filter: "class_days_time",
                                slug: "evening",
                                parentSlug: "class_time",
                                interval: "4 - 8",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            },
                            {
                                label: "Night",
                                filter: "class_days_time",
                                slug: "night",
                                parentSlug: "class_time",
                                interval: "8 - 11",
                                isActive: true,
                                isChecked: false,
                                placeHolder: "Class Days & Time",
                                displayStatus: true
                            }
                        ]
                    }
                ],
                is_highlighted: false,
                is_active: true,
                is_disabled: false,
                is_focus: false,
                selected: [],
                current: [],
                placeholder: "Class Days & Time"
            }
        ],
        hasReferralCode: false,
        enrolmentPayload: {
            id: "",
            receipt: "",
            title: "",
            user_id: isLoggedIn,
            course_id: "",
            batch_id: "",
            batch_name: "",
            batch_end_date: "",
            payment_gateway: "razorpay",
            payment_mode: "",
            amount: "",
            amount_due: "",
            total_instalments: 0,
            instalment_amount: 0,
            duration: "",
            status: "",
            description: "",
            self_notes: "",
            currency: "INR",
            counselor_id: 0,
            short_url: "",
            zoho_product_id: "",
            referral_code_status: "not_applied"
        },
        isBatchLoading: false,
    }),
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'moduleWithoutTab',
            'enrollmentV2',
            'courseBatches',
            'form',
            'loader',
            'referralCode',
            'subform',
            'module',
            'orgAdmin'
        ]),
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading: {
            get() {
                return this.userInfo.loading;
            }
        },
        isPageReady: {
            get() {
                return this.user.isLoggedin ? this.userInfo.success : true;
            }
        }
    },
    created() {
        this.manageUserQueryParams();
        this.emitEvents();
    },
    destroyed() {
        Event.$off('removeCode');
        Event.$off('initForm');
        Event.$off('initPayment');
        Event.$off('onApplyFilter');
        Event.$off('onFilterChange');
        Event.$off('onFilterItemSelect');
        Event.$off('updateBatch');
        Event.$off('checkLoggedInState');
    },
    mounted() {
        this.init();
    },
    methods: {
        manageState() {
            const paymentData = localStorage.getItem('paymentState');
            if (paymentData !== null) {
                this.initPayment(false);
            }
        },
        manageUserQueryParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const userid = urlParams.get('user_id');
            const token = urlParams.get('yuno_token');
            const orgID = urlParams.get('org_id');

            if (orgID) {
                this.orgID = orgID;
                this.isOrg = true;
            }

            if (userid && token) {
                isLoggedIn = userid;
                this.$store.state.config.yunoAPIToken = "Bearer " + token;
                this.user.userID = userid;
                this.user.isLoggedin = true;
            }
        },  
        init() {
            const urlParams = new URLSearchParams(window.location.search);
            const courseId = urlParams.get('course_id');

            if (courseId) {
                this.batchesOptions.courseID = courseId;
                this.fetchResult(this.batchesOptions.courseID, batchID)
            } else {
                this.fetchBatch();
            }
        },
        onCourseSelect() {
            this.batchesOptions.courseID = this.chooseCourse.selected;
            this.chooseCourse.modal = false;
            this.fetchResult(this.batchesOptions.courseID, batchID)
        },
        chooseCourseClose() {
            this.chooseCourse.data = [];
            this.chooseCourse.selected = "";
            this.chooseCourse.modal = false;
        },
        resetResult() {
            this.moduleWithoutTab.success = false;
            this.moduleWithoutTab.error = null;
            this.moduleWithoutTab.errorData = [];
            this.moduleWithoutTab.data = [];
        },
        resetBatches(emptyCourse) {
            this.courseBatches.success = false;
            this.courseBatches.error = null;
            this.courseBatches.errorData = [];
            this.courseBatches.data = [];

            if (emptyCourse) {
                this.batchesOptions.courseID = "";    
            }
            
            this.batchesOptions.offset = 0;
        },
        manageClassDaysTime(data, filter) {
            // Implementation remains the same
        },
        updateBatchesClose() {
            // Implementation remains the same
        },
        updateBatch() {
            this.updateBatches.modal = true;
            this.resetBatches(false);
            this.fetchBatches();
        },
        setEnrolmentPayload(data) {
            let paymentMode = "",
                enrolmentType = "";

            if (data.price_included_gst === "0") {
                paymentMode = "free";
            } else {
                paymentMode = "direct_payment";
            }

            if (data.price_included_gst === "0") {
                enrolmentType = "free";
            } else {
                enrolmentType = "regular";
            };
            
            this.enrolmentPayload.course_id = data.course_id;
            this.enrolmentPayload.title = data.course_title;
            this.enrolmentPayload.batch_id = data.batch_id;
            this.enrolmentPayload.batch_name = data.batch_label;
            this.enrolmentPayload.batch_end_date = data.batch_end_date;
            this.enrolmentPayload.payment_mode = paymentMode;
            this.enrolmentPayload.amount = data.price_included_gst;
            this.enrolmentPayload.amount_due = data.price_included_gst;
            this.enrolmentPayload.duration = data.duration_weeks;
            this.enrolmentPayload.zoho_product_id = data.zoho_product_id;

            const course = {
                id: data.course_id,
                title: data.course_title
            }

            let createPayObj = {
                redirectURL: window.location.pathname + window.location.search + "?course_id="+ course.id +"",
                batch: data,
                course: course,
                payload: this.enrolmentPayload,
                type: enrolmentType
            };

            localStorage.setItem('paymentState', JSON.stringify(createPayObj));
        },
        gotBatch(options) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data,
                    courseID = data.course_id;
                
                if (courseID.length > 1) {
                    this.chooseCourse.modal = true;
                    this.chooseCourse.data = data.courses;
                } else {
                    this.batchesOptions.courseID = courseID[0];
                    this.fetchResult(this.batchesOptions.courseID, batchID)
                };
            }
        },
        fetchBatch() {
            if (this.isBatchLoading) {
                return;
            }
            
            this.isBatchLoading = true;
            const options = {
                apiURL: YUNOCommon.config.batchDetailAPI(batchID),
                module: "gotData",
                store: "subform",
                callback: true,
                callbackFunc: (options) => {
                    this.isBatchLoading = false;
                    this.gotBatch(options);
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotBatches(options) {
            this.courseBatches.loading = false;

            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
                this.courseBatches.data = data;
                this.batchesOptions.batch_count = options.response.data.batch_count;
            }
        },
        fetchBatches() {
            this.courseBatches.loading = true;

            const options = {
                apiURL: YUNOCommon.config.availableBatches(this.batchesOptions.courseID, 20, this.batchesOptions.offset),
                module: "gotData",
                store: "courseBatches",
                payload: this.payload,
                callback: true,
                callbackFunc: (options) => this.gotBatches(options)
            };

            this.$store.dispatch('postData', options);
        },
        gotReferralCode(options) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
                this.form.payload.referral_code = data.buyer_referral_code;
                this.initForm();
            }
        },
        fetchReferralCode(resetModule) {
            if (resetModule) {
                this.referralCode.data = [];
                this.referralCode.success = false;
                this.referralCode.error = null;
            }

            const options = {
                apiURL: YUNOCommon.config.referrerID(0, isLoggedIn),
                module: "gotData",
                store: "referralCode",
                callback: true,
                callbackFunc: (options) => this.gotReferralCode(options)
            };

            this.$store.dispatch('fetchData', options);
        },
        setupForm() {
            this.form.payload = {
                batch_id: this.moduleWithoutTab.data.batch_id,
                course_id: this.moduleWithoutTab.data.course_id,
                payment_method: "direct_payment",
                referral_code: "",
                user_id: isLoggedIn,
                action: this.hasReferralCode ? "remove" : "create"
            }
        },
        gotResult(options, courseID, batchID) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
                yunoCategory = data.category_slug
                this.setupForm();
            }
        },
        fetchResult(courseID, batchID) {
            const options = {
                apiURL: YUNOCommon.config.checkout(courseID, batchID),
                module: "gotData",
                store: "moduleWithoutTab",
                callback: true,
                callbackFunc: (options) => this.gotResult(options, courseID, batchID)
            };

            this.$store.dispatch('fetchData', options);
        },
        manageLoader(state) {
            this.loader.isActive = state;
            this.loader.overlay = state;  
        },
        gotEnrollmentStatus(options) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
                
                if (!data.is_enrolled) {
                    this.manageLoader(false)
                    this.setEnrolmentPayload(this.moduleWithoutTab.data);
                    this.processPayment();
                } else {
                    this.$buefy.dialog.alert({
                        title: 'Alert',
                        message: `You have already enrolled a batch in this course`,
                        confirmText: 'Ok'
                    });

                    this.manageLoader(false)
                }
            }
        },
        verifyEnrollmentStatus(batch) {
            const options = {
                apiURL: YUNOCommon.config.courseEnrollmentStatus(this.batchesOptions.courseID, isLoggedIn),
                module: "gotData",
                store: "module",
                callback: true,
                callbackFunc: (options) => this.gotEnrollmentStatus(options)
            };

            this.$store.dispatch('fetchData', options);
        },
        onPaymentSuccess(data, payload, rzpObj, batch, course, type) {
            this.paymentSuccess.rzp = rzpObj !== false ? rzpObj : [];
            this.paymentSuccess.batch = payload;    
            this.paymentSuccess.modal = true;
            this.paymentSuccess.enrollmentType = type;
            batch.is_enrolled = true;
            course.is_enrolled = true;
        },
        updateQueryParams(params) {
            const url = new URL(window.location);
            Object.keys(params).forEach(key => url.searchParams.set(key, params[key]));
            history.pushState({}, '', url);
        },
        paymentSuccessClose() {
            this.paymentSuccess.rzp = [];
            this.paymentSuccess.batch = [];    
            this.paymentSuccess.enrollmentType = "";
            localStorage.removeItem('paymentState');
        
            const urlParams = new URLSearchParams(window.location.search);
            const userid = urlParams.get('user_id');
            const token = urlParams.get('yuno_token');
        
            if (userid && token) {
                this.updateQueryParams({ is_success: true });

                const redirectUrl = urlParams.get('redirect_url');
                window.location.href = redirectUrl;
            } else {
                const redirectUrl = this.userInfo.data.is_signup_completed === "pending" 
                    ? "/sign-up/" 
                    : "/learner";
                window.location.href = YUNOCommon.config.host() + redirectUrl;
            }
        },
        paymentCancelDone(options) {
            if (options.response?.data?.code === 201) {
                const data = options.response.data.data;
                this.paymentSuccessClose();
            }
        },
        onPaymentCancel(data, payload) {
            let apiURL = YUNOCommon.config.updatePaymentAPi(
                payload.batch_id,  
                payload.course_id, 
                isLoggedIn, 
                data.receipt_id, 
                data.zoho_deal_id
            );

            const options = {
                apiURL: apiURL,
                module: "gotData",
                store: "paymentDismiss",
                callback: true,
                callbackFunc: (options) => this.paymentCancelDone(options)
            };

            this.$store.dispatch('postData', options);
        },
        initEnrollmentDone(options, payload, batch, course, type) {
            this.courseOptions.isLoading = false;

            if (options.response?.data?.code === 201) {
                const data = options.response.data.data;

                if (type !== "free") {
                    data.modal = {
                        ondismiss: () => this.onPaymentCancel(data, payload)
                    };

                    data.handler = (response) => {
                        this.onPaymentSuccess(data, payload, response, batch, course, type);
                        localStorage.removeItem('paymentState');
                    };

                    const rzp1 = new Razorpay(data);
                    rzp1.open();     
                } else {
                    this.onPaymentSuccess(data, payload, false, batch, course, type);
                    localStorage.removeItem('paymentState');
                }
            } else {
                const data = options.response.data;
                
                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${data.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });

                localStorage.removeItem('paymentState');
            }
        },
        processEnrollment(type, payload, batch, course) {
            this.courseOptions.isLoading = true;

            const options = {
                apiURL: YUNOCommon.config.enrollmentsV4("createLink", false),
                module: "gotData",
                store: "enrollmentV2",
                payload: payload,
                callback: true,
                callbackFunc: (options) => this.initEnrollmentDone(options, payload, batch, course, type)
            };

            this.$store.dispatch('postData', options);
        },
        paymentPayload(data) {
            const payload = {
                user_id: isLoggedIn,
                course_id: data.course.id,
                batch_id: data.batch.batch_id,
                payment_gateway: "direct_payment",
                payment_mode: "online",
                amount: data.payload.amount,
                amount_due: data.payload.amount_due,
                total_instalments: data.payload.total_instalments,
                instalment_amount: data.payload.total_instalments,
                self_notes: data.payload.self_notes,
                currency_code: data.payload.currency,
                counselor_id: data.payload.counselor_id,
                org_id: this.moduleWithoutTab.data.org_id,
                org_user_id: "",
                org_user_phone: "",
                org_user_name: "",
                org_user_email: "",
                org_crm_id: "",
                org_cohort: "",
                org_programs: "",
                org_parents: [],
                org_business_unit: "",
                referral_code_status: data.payload.referral_code_status,
                amount_paid: data.payload.amount,
                enrolled_by: isLoggedIn
            }

            return payload;
        },
        processPayment() {
            const paymentData = localStorage.getItem('paymentState');

            if (paymentData !== null) {
                const data = JSON.parse(paymentData);
                this.processEnrollment(data.type, this.paymentPayload(data), data.batch, data.course);
            }
        },
        initPayment(e) {
            if (this.user.isLoggedin) {
                // this.manageLoader(true)
                this.setEnrolmentPayload(this.moduleWithoutTab.data);
                this.processPayment();
            } else {
                Event.$emit('initLoginModal', e);
                this.setEnrolmentPayload(this.moduleWithoutTab.data);
            }
        },
        updateAmount(isCodeRemove) {
            const paymentData = localStorage.getItem('paymentState');

            if (paymentData !== null) {
                let data = JSON.parse(paymentData);
                data.payload.amount = isCodeRemove ? data.batch.price : this.form.data.discounted_price;
                data.payload.amount_due = isCodeRemove ? data.batch.price : this.form.data.discounted_price;
                data.payload.referral_code_status = isCodeRemove ? "not_applied" : "applied";
                localStorage.setItem('paymentState', JSON.stringify(data));
            }
        },
        formPosted(options) {
            this.manageLoader(false);
            
            if (options.response?.data?.code === 201) {
                const response = options.response.data;
                this.form.data = response.data;

                if (this.form.payload.action === "create") {
                    this.hasReferralCode = true;    
                    this.updateAmount(false);
                } else {
                    this.hasReferralCode = false;    
                    this.updateAmount(true);
                }

                this.form.additional = response.message;
            } else {
                const response = options.response.data;
                this.hasReferralCode = false
                this.updateAmount(true);
            }
        },
        initForm() {
            this.manageLoader(true);
            this.form.data = [];
            this.form.error = null;
            this.form.success = false;
            this.form.additional = [];
            this.form.errorData = [];

            const options = {
                apiURL: YUNOCommon.config.referrerDetails(),
                module: "gotData",
                store: "form",
                payload: this.form.payload,
                callback: true,
                callbackFunc: (options) => this.formPosted(options)
            };

            this.$store.dispatch('postData', options);
        },
        emitEvents() {  
            Event.$on('removeCode', () => {
                this.form.payload.action = "remove";
                this.form.payload.referral_code = "";
                this.initForm();
            });

            Event.$on('initForm', () => {
                this.form.payload.action = "create";
                this.initForm();
            });

            Event.$on('initPayment', (e) => {
                this.initPayment(e);
            });

            Event.$on('onApplyFilter', (filter) => {
                const defaultTime = ["morning","afternoon","evening","night"],
                    defaultDays = ["sun","mon","tue","wed","thu","fri","sat"];

                if (filter.current.length !== 0) {
                    this.payload.batch_days = YUNOCommon.findObjectByKey(filter.current, "parentSlug", "class_days") !== null ? [] : defaultDays;
                    this.payload.batch_time = YUNOCommon.findObjectByKey(filter.current, "parentSlug", "class_time") !== null ? [] : defaultTime;

                    for (let i = 0; i < filter.current.length; i++) {
                        const item = filter.current[i];
                        
                        if (item.parentSlug === "class_time") {
                            this.payload.batch_time.push(item.slug);
                        }
                        
                        if (item.parentSlug === "class_days") {
                            this.payload.batch_days.push(item.slug);
                        }
                    }
                } else {
                    this.payload.batch_days = defaultDays;
                    this.payload.batch_time = defaultTime;
                } 

                this.resetBatches(false);
                this.fetchBatches();
            });
            
            Event.$on('onFilterChange', (e, filter) => {
                // Implementation remains the same
            });

            Event.$on('onFilterItemSelect', (data, filter) => {
                switch (filter.slug) {
                    case "class_days_time":
                        this.manageClassDaysTime(data, filter);                        
                        break;
                }
            });

            Event.$on('updateBatch', () => {
                this.updateBatch();
            });

            Event.$on('checkLoggedInState', (status) => {
                if (status) {
                    Event.$on('gotUserRole', (role, userData) => {
                        this.init();
                    });
                } else {
                    this.init();
                }
            });
        }
    }
});

Vue.component('yuno-batches', {
    props: ["data", "options"],
    template: `
        <section class="batchesWrapper">
            <section class="filtersWrapper">
                <ul>
                    <template v-for="(filter, i) in options.filters">
                        <template v-if="filter.type === 'dropdown'">
                            <li 
                                :key="i"
                                v-if="filter.is_active"
                                class="filter">
                                <b-dropdown
                                    :key="i"
                                    v-model="filter.selected"
                                    :multiple="false"
                                    aria-role="list"
                                    :disabled="filter.is_disabled"
                                    @change="onFilterChange($event, filter)"
                                    class="filterMenu"
                                    :class="[filter.is_highlighted ? 'active' : '']">
                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">
                                        <template v-if="filter.selected === ''">
                                            <span>{{filter.placeholder}}</span>
                                        </template>
                                        <template v-else>
                                            <span>{{filter.selected.label}}</span>
                                        </template>
                                        <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                    </button>
                                    <template v-for="(item, j) in filter.items">
                                        <b-dropdown-item 
                                            @click="onFilterItemSelect(item, filter)"
                                            :class="[item.slug]"
                                            :value="item"
                                            :key="item.slug + '-' + j"
                                            aria-role="listitem">
                                            <span class="itemLabel">{{item.label}}</span>
                                        </b-dropdown-item>
                                    </template>
                                </b-dropdown>
                            </li>
                        </template>
                        <template v-if="filter.type === 'groupMultiSelect'">
                            <li 
                                :key="i"
                                v-if="filter.is_active"
                                class="filter">
                                <b-dropdown
                                    :key="i"
                                    v-model="filter.selected"
                                    ref="groupMultiSelect"
                                    :multiple="true"
                                    aria-role="list"
                                    :disabled="filter.is_disabled"
                                    @change="onFilterChange($event, filter)"
                                    class="filterMenu"
                                    :class="[filter.is_highlighted ? 'active' : '']">
                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">
                                        <span>{{filter.placeholder}}</span>
                                        <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                    </button>
                                    <template v-for="(item, j) in filter.items">
                                        <div class="filterSet" :class="[item.slug]">
                                            <p :key="item.slug + '-' + j" class="listCaption">
                                                {{item.label}}
                                            </p>
                                            <div class="innerWrapper">
                                                <template v-for="(subItem, l) in item.items">
                                                    <b-dropdown-item 
                                                        @click="onFilterItemSelect(subItem, filter)"
                                                        :class="[subItem.slug]"
                                                        :value="subItem.slug"
                                                        :key="item.slug + '-sub-' + l"
                                                        aria-role="listitem">
                                                        <span class="itemLabel">{{subItem.label}}</span>
                                                    </b-dropdown-item>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="ctaWrapper">
                                        <b-button class="noBorder" @click="clearMe(filter)">Clear All</b-button>
                                        <b-button class="yunoSecondaryCTA" @click="applyFilter(filter)">Apply</b-button>
                                    </div>
                                </b-dropdown>
                            </li>
                        </template>
                    </template>
                </ul>
            </section>
            <template v-if="data.loading">
                <div class="batches">
                    <article class="batchCard" v-for="i in loadingResult" :key="i">
                        <div class="wrapper">
                            <h4 class="batchdate"><b-skeleton active></b-skeleton></h4>
                            <ul class="scheduleInfo">
                                <li>
                                    <b-skeleton active width="50%"></b-skeleton>
                                </li>
                            </ul>
                            <figure class="mappedInstructor">
                                <div class="imgWrapper">
                                    <b-skeleton circle width="44px" height="44px"></b-skeleton>
                                </div>
                                <figcaption>
                                    <h3 class="insName"><b-skeleton active></b-skeleton></h3>
                                </figcaption>
                            </figure>
                        </div>
                    </article>
                </div>
            </template>
            <template v-if="data.success">
                <template v-if="data.error">
                    <p>{{ data.errorData }}</p>
                </template>
                <template v-else>
                    <p class="batchesCount">{{ options.batchesOptions.batch_count }}</p>
                    <div class="batches">
                        <article class="batchCard" v-for="(batch, i) in data.data" :key="i">
                            <div class="wrapper">
                                <h4 class="batchdate"><span class="material-icons">schedule</span>{{ batch.batch_start_time }}</h4>
                                <ul class="days">
                                    <li 
                                        v-for="(day, j) in batch.class_days" 
                                        :key="'day-' + j"
                                        :class="[day.is_available ? 'isActive' : '']">
                                        {{ day.label }}
                                    </li>
                                </ul>
                                <ul class="scheduleInfo">
                                    <li>
                                        <span class="material-icons-outlined">event</span>
                                        <span class="itemCaption">{{ batch.starts_on }} {{ batch.ends_on }}</span>
                                    </li>
                                    <li class="hasGrid">
                                        <div class="hasBG">
                                            <span class="material-icons-outlined">groups</span>
                                            <b-tooltip :label="tooltipLabel(batch)"
                                                type="is-dark"
                                                :multilined="true"
                                                position="is-top">
                                                Group classes
                                            </b-tooltip>
                                        </div>
                                        <span class="itemCaption">
                                            <b-tooltip label="Live classes will happen through the Zoom app. You can attend from your laptop or mobile device"
                                                type="is-dark"
                                                :multilined="true"
                                                position="is-top">
                                                | Online
                                            </b-tooltip>
                                        </span>
                                    </li>
                                </ul>
                                <figure class="mappedInstructor">
                                    <div class="imgWrapper">
                                        <img width="44" height="44" :src="batch.instructor.image" :alt="batch.instructor.name">
                                    </div>
                                    <figcaption>
                                        <h3 class="insName"><a :href="batch.instructor.profile_url" target="_blank">{{batch.instructor.name}}</a></h3>
                                        <div class="insRating" v-if="batch.instructor.rating !== 0">
                                            <span class="material-icons">star</span>
                                            <span class="caption">{{ batch.instructor.rating }} of 5</span>
                                        </div>
                                    </figcaption>
                                    <small class="studentCount">{{ batch.successful_enrollment }} successful students</small>
                                </figure>
                                <ul class="cardFooter">
                                    <li class="price">
                                        <template v-if="batch.price === 0 && batch.subscription.length === 0">
                                            {{"Free"}}
                                        </template>
                                        <template v-else-if="batch.price === 0 && batch.subscription.length !== 0">
                                            ₹{{batch.subscription[0].value}}
                                        </template>
                                        <template v-else>
                                            ₹{{batch.price}}
                                        </template>
                                    </li>
                                    <li class="ctaWrapper"><b-button tag="a" :href="refineBatchURL(batch.batch_url)" class="yunoSecondaryCTA">Change to this</b-button></li>
                                </ul>
                            </div>
                        </article>
                    </div>
                </template>
            </template>
        </section>
    `,
    data() {
        return {
            loadingResult: 3
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        refineBatchURL(url) {
            const params = window.location.search;

            return url + params;
        },
        tooltipLabel(data) {
            return "You'll be one of the maximum of "+ data.max_seats +" students in the class"
        },
        changeBatch(data) {
            Event.$emit('changeBatch', data);
        },
        clearMe(filter) {
            filter.selected = [];
            filter.current = [];
        },
        applyFilter(filter) {
            this.$refs.groupMultiSelect[0].isActive = false;
            Event.$emit('onApplyFilter', filter);
        },
        onFilterChange(e, filter) {
            Event.$emit('onFilterChange', e, filter);
        },
        onFilterItemSelect(data, filter) {
            if (data.filter === "class_days_time") {
                const option = YUNOCommon.findInArray(filter.selected, data.slug);

                if (option) {
                    filter.current.push(data)
                } else {
                    YUNOCommon.removeObjInArr(filter.current, "slug", data.slug); 
                }
            };
            
            Event.$emit('onFilterItemSelect', data, filter);
        }
    }
});
Vue.component('yuno-course', {
    props: ["data", "options", "hasReferralCode"],
    template: `
        <div class="course">
            <h2 class="name">{{ data.course_title }}</h2>
            <p class="description">{{ data.course_excerpt }}</p>
            <h3 class="subtitle">Summary</h3>
            <ul class="orderDetail">
                <li>
                    <span>Course fee</span>
                    <span>₹{{ data.price_excluded_gst }}</span>
                </li>
                <li v-if="hasReferralCode && form.data.applied_discount_percentage !== 0">
                    <span>Referral discount ({{ form.data.applied_discount_percentage + '%' }})</span>
                    <span class="withGrid">-₹{{ form.data.applied_discount_amount }} <a href="#" @click.prevent="removeCode()"><span class="material-icons-outlined">highlight_off</span></a></span>
                </li>
                <li>
                    <span>GST (18%)</span>
                    <span>₹{{ data.gst }}</span>
                </li>
                <li class="primary">
                    <span>Total</span>
                    <span>
                        <template v-if="hasReferralCode">
                            ₹{{ form.data.discounted_price }}
                        </template>
                        <template v-else>
                            ₹{{ data.price_included_gst }}
                        </template>
                    </span>
                </li>
            </ul>
            <validation-observer 
                v-if="false"
                tag="div" 
                class="formWrapper"
                ref="formObserver" 
                v-slot="{ handleSubmit, invalid }">
                <form id="yunoForm" @submit.prevent="handleSubmit(initForm)">
                    <template v-if="referralCode.loading">
                        <b-field>
                            <template #label>
                                Referral code
                                <b-tooltip 
                                    type="is-dark" 
                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"
                                    size="is-large"
                                    multilined
                                >
                                    <span class="material-icons-outlined">info</span>
                                </b-tooltip>
                            </template>
                            <b-skeleton active height="36px"></b-skeleton>
                        </b-field>
                    </template>
                    <template v-if="referralCode.success">
                        <b-field>
                            <template #label>
                                Referral code
                                <b-tooltip 
                                    type="is-dark" 
                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"
                                    size="is-large"
                                    multilined
                                >
                                    <span class="material-icons-outlined">info</span>
                                </b-tooltip>
                            </template>
                            <validation-provider 
                                :rules="{required_if: ''}" v-slot="{ errors, classes }">
                                <b-input @input="referralCodeInput" maxlength="6" :has-counter="false" v-model="form.payload.referral_code"></b-input>
                            </validation-provider>
                        </b-field>
                        <p class="msg" v-if="form.success" :class="[form.error === null ? 'success' : 'error']">
                            <template v-if="form.error === null">
                                <span class="material-icons">check_circle</span> {{ form.additional }}
                            </template>
                            <template v-else>
                                <span class="material-icons">cancel</span> {{ form.errorData }}
                            </template>
                        </p>
                    </template>
                </form>
            </validation-observer>
            <b-button 
                @click="initPayment($event)"
                :loading="options.isLoading ? true : false"
                :disabled="options.isLoading ? true : false" 
                class="yunoSecondaryCTA big">
                Proceed to Pay <template v-if="hasReferralCode">₹{{ form.data.discounted_price }}</template><template v-else>₹{{ data.price_included_gst }}</template>
            </b-button>
            <template v-if="data.price_excluded_gst !== 0">
                <h4 class="caption">Cancellation Policy</h4>
                <p class="note">Full refund: get back 100% of your money within the first 3 days of starting your classes</p>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'form',
            'referralCode'
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        referralCodeInput(e) {
            if (e.length === 6) {
                console.log("sss")
                Event.$emit('initForm');
            }
        },
        removeCode() {
            Event.$emit('removeCode');
        },
        initForm() {
            Event.$emit('initForm');
        },
        initPayment(e) {
            Event.$emit('initPayment', e);
        }
    }
});
Vue.component('yuno-org-header', {
    template: `
        <div class="yunoHeader">
            <div class="container-fluid noOverflow">
                <figure class="logo navbar-brand">
                    <template v-if="orgAdmin.loading">
                        <b-skeleton height="72px" width="106px"></b-skeleton>
                    </template>
                    <template v-else-if="orgAdmin.success && orgAdmin.error === null">
                        <img :src="orgAdmin.data.logo_image_url" :alt="orgAdmin.data.organisation_name" :class="imageClass"  @load="calculateAspectRatio">
                    </template>
                </figure>
            </div>
        </div>
    `,
    data() {
        return {
            imageClass: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        calculateAspectRatio(event) {
            const img = event.target;
            const aspectRatio = img.naturalWidth / img.naturalHeight;
            if (aspectRatio > 1) {
                this.imageClass = 'landscape';
            } else if (aspectRatio < 1) {
                this.imageClass = 'portrait';
            } else {
                this.imageClass = 'square';
            }
        }
    }
});
Vue.component('yuno-user-list', {
    props: ["data", "options"],
    template: `
        <div class="userList">
            <figure class="mappedInstructor">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        :src="data.instructor.image" 
                        :alt="data.instructor.name">
                </div>
                <figcaption>
                    <small class="studentCount">instructor</small>
                    <h3 class="insName">{{ data.instructor.name }}</h3>
                </figcaption>
            </figure>
            <figure class="mappedInstructor" v-if="false">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        src="https://lh3.googleusercontent.com/a-/AFdZucqaT_XL5RJsrCMlYly_jIvNtgymihOsjzVEOH2J=s96-c" 
                        alt="asd">
                </div>
                <figcaption>
                    <small class="studentCount">counselor</small>
                    <h3 class="insName">Reena Nagpal</h3>
                </figcaption>
            </figure>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});