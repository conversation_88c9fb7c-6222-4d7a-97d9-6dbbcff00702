@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.paymentLink {
		padding-top: $gapLargest;
		padding-bottom: $gap15;

		@media (min-width: 768px) {
			padding-top: $gapLargest;
			padding-bottom: $gapLargest * 2;
		}
		
		.sectionTitle {
			font-size: $fontSizeLargest;
			font-weight: 500;
			margin-bottom: $gap15;

			@media (min-width: 768px) {
				
			}
		}

		#previousFields, #nextFields {
			padding: $gap15;

			&.hideMe {
				display: none;
			}

			@media (min-width: 768px) {
				padding: $gapLargest $gapLargest $gapSmall * 2;
			}
		}

		#previousFields {
			&.noBtmGap {
				padding-bottom: 0;
			}
		}

		#nextFields {
			padding-top: 0;
			padding-bottom: $gapLargest;
		}

		.formWrapper {
			@include setBGColor(#F7F9FB, 1);
			// padding: $gap15;
			border: 1px solid #D9D9D9;
			border-radius: 6px;

			@media (min-width: 768px) {
				// padding: $gapLargest;
			}

			button.button {
				padding: $gapSmall $gapLargest;
			}

			.field {
				.control {
					textarea {
						height: 200px;
					}
				}

				.paymentLinkBatch {
					.dropdown-content {
						.dropdown-item {
							white-space: normal;
							word-break: break-all;
						}
					}
				}
			}
		}

		.ctaWrapper {
			padding-top: $gapLargest;
		}

		.inlineFields {
			display: flex;
			margin: 0 (-$gapSmall);
			flex-wrap: wrap;
			margin-bottom: $gap15;

			> .field {
				flex: 0 0 100%;
				padding: 0 $gapSmall;

				&:last-child {
					margin-bottom: $gap15;
				}

				@media (min-width: 768px) {
					flex: 0 0 auto;
					margin-bottom: 0;

					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}

		.taginput {
			.taginput-container {
				.autocomplete {
					input {
						background: none;
						border: 0;
					}
				}
			}
		}

		.inviteWrapper {
			margin-top: $gapLargest;

			@media (min-width: 768px) {
				margin-top: 0;
			}
		}

		.count {
			font-size: $fontSizeLarge;
			font-weight: 500;
			margin-bottom: $gap15;

			span {
				font-weight: 400;
			}
		}

		.selectedLearners {
			li {
				@include setBorderColor($primaryCopyColor, 0.2);
				padding: $gapSmaller $gap15;
				@include setBGColor($primaryCopyColor, 0.1);
				font-size: $fontSizeSmall;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.caption {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.fa {
					font-size: $fontSizeLarger;
					cursor: pointer;
				}
			}
		}
	}

	.classFields {
		> li {
			font-size: $fontSizeLarge;
			line-height: normal;
			@include setFontColor($primaryCopyColor, 0.5);
			margin-bottom: $gap15;

			.caption {
				font-weight: 500;
				color: $primaryCopyColor;
				display: block;
				margin-bottom: $gapSmaller
			}

			&:last-child {
				margin-bottom: 0;
			}

			.selectedLearners {
				margin-top: $gapSmall;
				li {
					padding: $gapSmaller $gapSmall;
					font-weight: 400;

					.caption {
						font-weight: 400;
						margin: 0;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-size: $fontSizeSmall;
					}
				}
			}

			.clipboard {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.control {
					flex: 0 0 85%;
				}

				.trigger {
					margin-left: $gap15;
					cursor: pointer;
					width: 36px;
					height: 36px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}

	.courseList {
		display: flex;
		align-items: center;

		.img {
			flex: 0 0 40px;

			.fa {
				font-size: 40px;
				@include setFontColor($primaryCopyColor, 0.5);
			}

			img {
				width: 40px;
				height: 40px;
				transform: scale(1, 1);
				border-radius: 50%;
				@include setBGColor($primaryCopyColor, 0.5);
				padding: 2px;
				overflow: hidden;
			}
		}

		.courseContent {
			padding-left: $gap15;

			.instructorName {
				text-transform: capitalize;
			}
		}
	}

	.referralCode {
		margin-bottom: $gap15;
		display: flex;
		align-items: flex-end;

		.field {
			flex: 0 0 calc(70% - 15px);
			margin-right: $gap15;
			margin-bottom: 0;
		}

		.button {
			height: 36px;
			flex: 0 0 30%;
		}
	}
}

.notices {
	.toast {
		flex-basis: auto;

		&.is-danger {
			background-color: red;
		}
	}
}
