Vue.component('yuno-batch', {
    props: ["data", "options"],
    template: `
        <div class="batch">
            <div class="row">
                <div class="col-12">
                    <div class="spaceBetween">
                        <h2 class="price">
                            <template v-if="data.price_included_gst !== 0 && data.price_included_gst !== '0' && data.price_included_gst !== 'Free'">
                                ₹{{ data.price_included_gst }}
                            </template>
                            <template v-else>
                                Free
                            </template>
                        </h2>
                        <b-button 
                            @click="updateBatch()"
                            class="yunoPrimaryCTA wired">
                            Change Batch
                        </b-button>
                    </div>
                    <ul class="batchInfo">
                        <li>Your enrollment starts on {{ data.enrollment_starts }}</li>
                        <li>Class time: {{ data.class_start_time }}</li>
                        <li class="grid">Class days: 
                            <ul class="classDays">
                                <li 
                                    v-for="(day, d) in data.class_days" 
                                    :key="d"
                                    :class="[day.is_available ? '' : 'disabled']">
                                    {{day.label}}
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        updateBatch() {
            Event.$emit('updateBatch');
        }
    }
});