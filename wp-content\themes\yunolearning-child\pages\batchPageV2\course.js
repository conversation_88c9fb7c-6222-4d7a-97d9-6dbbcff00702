Vue.component('yuno-course', {
    props: ["data", "options", "hasReferralCode"],
    template: `
        <div class="course">
            <h2 class="name">{{ data.course_title }}</h2>
            <p class="description">{{ data.course_excerpt }}</p>
            <h3 class="subtitle">Summary</h3>
            <ul class="orderDetail">
                <li>
                    <span>Course fee</span>
                    <span>₹{{ data.price_excluded_gst }}</span>
                </li>
                <li v-if="hasReferralCode && form.data.applied_discount_percentage !== 0">
                    <span>Referral discount ({{ form.data.applied_discount_percentage + '%' }})</span>
                    <span class="withGrid">-₹{{ form.data.applied_discount_amount }} <a href="#" @click.prevent="removeCode()"><span class="material-icons-outlined">highlight_off</span></a></span>
                </li>
                <li>
                    <span>GST (18%)</span>
                    <span>₹{{ data.gst }}</span>
                </li>
                <li class="primary">
                    <span>Total</span>
                    <span>
                        <template v-if="hasReferralCode">
                            ₹{{ form.data.discounted_price }}
                        </template>
                        <template v-else>
                            ₹{{ data.price_included_gst }}
                        </template>
                    </span>
                </li>
            </ul>
            <validation-observer 
                v-if="false"
                tag="div" 
                class="formWrapper"
                ref="formObserver" 
                v-slot="{ handleSubmit, invalid }">
                <form id="yunoForm" @submit.prevent="handleSubmit(initForm)">
                    <template v-if="referralCode.loading">
                        <b-field>
                            <template #label>
                                Referral code
                                <b-tooltip 
                                    type="is-dark" 
                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"
                                    size="is-large"
                                    multilined
                                >
                                    <span class="material-icons-outlined">info</span>
                                </b-tooltip>
                            </template>
                            <b-skeleton active height="36px"></b-skeleton>
                        </b-field>
                    </template>
                    <template v-if="referralCode.success">
                        <b-field>
                            <template #label>
                                Referral code
                                <b-tooltip 
                                    type="is-dark" 
                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"
                                    size="is-large"
                                    multilined
                                >
                                    <span class="material-icons-outlined">info</span>
                                </b-tooltip>
                            </template>
                            <validation-provider 
                                :rules="{required_if: ''}" v-slot="{ errors, classes }">
                                <b-input @input="referralCodeInput" maxlength="6" :has-counter="false" v-model="form.payload.referral_code"></b-input>
                            </validation-provider>
                        </b-field>
                        <p class="msg" v-if="form.success" :class="[form.error === null ? 'success' : 'error']">
                            <template v-if="form.error === null">
                                <span class="material-icons">check_circle</span> {{ form.additional }}
                            </template>
                            <template v-else>
                                <span class="material-icons">cancel</span> {{ form.errorData }}
                            </template>
                        </p>
                    </template>
                </form>
            </validation-observer>
            <b-button 
                @click="initPayment($event)"
                :loading="options.isLoading ? true : false"
                :disabled="options.isLoading ? true : false" 
                class="yunoSecondaryCTA big">
                Proceed to Pay <template v-if="hasReferralCode">₹{{ form.data.discounted_price }}</template><template v-else>₹{{ data.price_included_gst }}</template>
            </b-button>
            <template v-if="data.price_excluded_gst !== 0">
                <h4 class="caption">Cancellation Policy</h4>
                <p class="note">Full refund: get back 100% of your money within the first 3 days of starting your classes</p>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'form',
            'referralCode'
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        referralCodeInput(e) {
            if (e.length === 6) {
                console.log("sss")
                Event.$emit('initForm');
            }
        },
        removeCode() {
            Event.$emit('removeCode');
        },
        initForm() {
            Event.$emit('initForm');
        },
        initPayment(e) {
            Event.$emit('initPayment', e);
        }
    }
});