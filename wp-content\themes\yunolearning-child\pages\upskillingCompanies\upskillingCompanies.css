.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .studyMaterialWrapper .carousel-arrow .icon .mdi-chevron-left::after, #app .studyMaterialWrapper .carousel-arrow .icon .mdi-chevron-right::after {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .yunoPartnerList .sectionTitle, #app .resources .resourceFilters .control .button, #app .studyMaterialWrapper .sectionTitle, #app .studyMaterialWrapper .resourceCard .resourceTitle {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .resources .resourceCount, #app .studyMaterialWrapper .resourceCard .tag, #app .studyMaterialWrapper .resourceCard .resourceDes, #app .studyMaterialWrapper .resourceCard.webinar .hasDate {
  color: rgba(0, 0, 0, 0.6);
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

@media (min-width: 768px) {
  #app .yunoInstructorList {
    padding: 100px 0 0;
  }
}

#app .yunoInstructorList .tns-liveregion, #app .yunoInstructorList button[data-action="stop"], #app .yunoInstructorList button[data-action="start"] {
  display: none;
}

#app .yunoPartnerList .tns-liveregion, #app .yunoPartnerList button[data-action="stop"], #app .yunoPartnerList button[data-action="start"] {
  display: none;
}

#app .emptyStateV2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 5%;
}

#app .emptyStateV2 figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .emptyStateV2 figure img {
  width: 158px;
  height: auto;
}

#app .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .emptyStateV2 figure .button {
  margin-top: 15px;
}

#app .yunoInstructorList .sectionTitle {
  font-weight: 500;
}

@media (min-width: 768px) {
  #app .verticalTabs.practice .b-tabs .tabs {
    width: 350px;
  }
}

#app .verticalTabs.practice .carousel .carousel-item figure {
  background-color: rgba(0, 0, 0, 0.02);
}

#app .verticalTabs.practice .carousel .carousel-item figure img {
  height: auto;
  max-width: 100%;
  width: auto;
  margin: 0 auto;
  display: block;
  padding: 30px 0;
}

#app .verticalTabs.practice .carousel .carousel-item figure .itemInfo {
  background-color: #FFF;
}

#app .yunoPartnerList .sectionTitle {
  font-weight: 500;
}

#app .resources {
  position: relative;
  min-height: 393px;
  margin-top: 30px;
}

@media (min-width: 768px) {
  #app .resources {
    margin-top: 100px;
  }
}

#app .resources .resourceFilters .field-body .field {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .resources .resourceFilters .control .button {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 100px;
  margin: 0 10px 0 0;
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .resources .resourceFilters .control .button.is-primary {
  background-color: #A81E22;
  color: white;
}

#app .resources .resourceCount {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  text-align: center;
}

#app .resources .sectionTitle {
  text-align: center;
  font-size: 32px;
  line-height: 40px;
  font-weight: 500;
  margin-bottom: 30px;
}

#app .resources .yunoPrimaryCTA {
  height: 40px;
  width: 100%;
}

#app .studyMaterialWrapper {
  padding: 20px 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .studyMaterialWrapper .tns-outer .tns-liveregion, #app .studyMaterialWrapper .tns-outer button {
  display: none;
}

#app .studyMaterialWrapper .sectionTitle {
  font-size: 32px;
  text-align: center;
  margin: 0 0 0;
  line-height: 40px;
}

#app .studyMaterialWrapper .tns-inner {
  overflow: hidden;
  margin: 0 -15px;
  padding: 15px 15px 30px;
}

@media (min-width: 768px) {
  #app .studyMaterialWrapper .tns-inner {
    margin: 0 -30px;
  }
}

#app .studyMaterialWrapper .carousel-arrow .icon {
  width: 44px;
  height: 44px;
  background-color: #FFF;
  -webkit-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.5);
          box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.5);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 0;
}

#app .studyMaterialWrapper .carousel-arrow .icon.has-icons-left {
  left: 15px;
}

#app .studyMaterialWrapper .carousel-arrow .icon.has-icons-right {
  right: 40px;
}

#app .studyMaterialWrapper .carousel-arrow .icon .mdi-chevron-left::after {
  content: "\e5cb";
}

#app .studyMaterialWrapper .carousel-arrow .icon .mdi-chevron-right::after {
  content: "\e5cc";
}

#app .studyMaterialWrapper .carousel-slides {
  margin: 0 -15px;
}

#app .studyMaterialWrapper .carousel-slide {
  border: 0;
  padding: 0 15px;
}

#app .studyMaterialWrapper .carousel-slide a {
  display: block;
  background-color: #FFF;
  height: 100%;
  padding: 15px;
  border: 1px solid #E6E6E6;
  border-radius: 4px;
}

#app .studyMaterialWrapper .carousel-slide a:hover {
  text-decoration: none;
}

#app .studyMaterialWrapper .userList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .studyMaterialWrapper .userList li {
  padding: 0 15px;
}

#app .studyMaterialWrapper .userList li.tns-slide-active > a {
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
}

#app .studyMaterialWrapper .userList li > a {
  display: block;
  background-color: #FFF;
  -webkit-box-shadow: 0px 4px 34px rgba(255, 255, 255, 0);
          box-shadow: 0px 4px 34px rgba(255, 255, 255, 0);
  height: 100%;
  padding: 15px;
}

#app .studyMaterialWrapper .userList li > a:hover {
  text-decoration: none;
}

#app .studyMaterialWrapper .resourceCard {
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

#app .studyMaterialWrapper .resourceCard.listView {
  padding: 15px;
}

#app .studyMaterialWrapper .resourceCard.listView .tag {
  text-align: center;
  margin: 10px 0 0 0;
}

#app .studyMaterialWrapper .resourceCard.listView .resourceTitle {
  margin-top: 0;
}

#app .studyMaterialWrapper .resourceCard .cardImg {
  background: #930e1a;
  background: linear-gradient(212deg, #930e1a 0%, #002f5a 100%);
}

#app .studyMaterialWrapper .resourceCard .instructorInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-top: 1px solid #E6E6E6;
  padding-top: 15px;
  margin-top: 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .studyMaterialWrapper .resourceCard .instructorInfo img {
  width: 40px;
  height: 40px;
  border: 1px solid #a62027;
  border-radius: 50%;
}

#app .studyMaterialWrapper .resourceCard .instructorInfo figcaption {
  padding-left: 10px;
  font-size: 14px;
  color: black;
}

#app .studyMaterialWrapper .resourceCard .instructorInfo figcaption .infoLabel {
  display: block;
  color: rgba(0, 0, 0, 0.5);
}

#app .studyMaterialWrapper .resourceCard .tag {
  border-radius: 0;
  background: none;
  text-align: left;
  display: block;
  font-size: 10px;
  line-height: 16px;
  text-transform: uppercase;
  padding: 0 0 15px;
  height: auto;
}

#app .studyMaterialWrapper .resourceCard > a {
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
  border-radius: 4px;
  display: block;
  border: 1px solid transparent;
  width: 100%;
}

#app .studyMaterialWrapper .resourceCard > a:hover {
  text-decoration: none;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .studyMaterialWrapper .resourceCard > a .resourceTitle {
  color: black;
}

#app .studyMaterialWrapper .resourceCard > a .resourceDes {
  color: rgba(0, 0, 0, 0.6);
}

#app .studyMaterialWrapper .resourceCard .resourceImgWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: 100%;
  width: 100%;
}

#app .studyMaterialWrapper .resourceCard .resourceImg {
  width: 100%;
  height: auto;
}

#app .studyMaterialWrapper .resourceCard .imgFrame {
  background-color: rgba(0, 0, 0, 0.02);
}

#app .studyMaterialWrapper .resourceCard .imgFrame .resourceImg {
  width: auto;
  max-width: 100%;
  max-height: 109px;
  margin: 0 auto;
  display: block;
}

@media (max-width: 767px) {
  #app .studyMaterialWrapper .resourceCard .imgFrame .resourceImg {
    max-height: 160px;
  }
}

#app .studyMaterialWrapper .resourceCard .imgFrame .fa {
  margin: 15px 0 0;
  font-size: 32px;
  color: #a62027;
}

#app .studyMaterialWrapper .resourceCard.document a .imgFrame .fa, #app .studyMaterialWrapper .resourceCard.article a .imgFrame .fa {
  font-size: 24px;
}

#app .studyMaterialWrapper .resourceCard.video .imgFrame {
  position: relative;
  padding: 0;
  min-height: 124px;
}

#app .studyMaterialWrapper .resourceCard.video .imgFrame .fa {
  position: absolute;
  left: calc(50% - 16px);
  top: calc(50% - 16px);
  margin: 0;
  color: #a62027;
  width: 31px;
  height: 31px;
  background-color: white;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .studyMaterialWrapper .resourceCard.video .resourceImg {
  width: 100%;
}

#app .studyMaterialWrapper .resourceCard .resourceTitle {
  font-size: 16px;
  line-height: 20px;
  padding: 15px 0 5px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

#app .studyMaterialWrapper .resourceCard .resourceDes {
  font-size: 12px;
  line-height: 16px;
  padding: 0 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

#app .studyMaterialWrapper .resourceCard .resourceMeta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .studyMaterialWrapper .resourceCard.webinar .resourceTitle {
  margin-bottom: 5px;
}

#app .studyMaterialWrapper .resourceCard.webinar .hasDate {
  font-size: 14px;
  line-height: 20px;
  padding: 0 15px;
}
/*# sourceMappingURL=upskillingCompanies.css.map */