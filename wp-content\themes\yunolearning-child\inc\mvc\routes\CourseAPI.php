<?php
//Course Controller API's
return [
    "/courses/search" => [
        "controller" => "CourseController",
        "methods" => [
            "POST" => ["callback" => "getCourseSuggestions", "args" => [], "auth" => false]
        ]
    ],
    "/courses/search/result" => [
        "controller" => "CourseController",
        "methods" => [
            "GET" => ["callback" => "getCourseResult", "args" => [], "auth" => false]
        ]
    ],
    "/courses/(?P<courseId>\d+)" => [
        "controller" => "CourseController",
        "methods" => [
            "GET" => ["callback" => "getCourse", "args" => [], "auth" => true]
        ]
    ]

];
