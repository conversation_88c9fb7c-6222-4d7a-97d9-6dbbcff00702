<?php
//Enrollment Migration Controller API's
return [
    "/enrollment/migration/es/(?P<limit>\d+)/(?P<offset>\d+)" => [
        "controller" => "EnrollmentMigrationController",
        "methods" => [
            "GET" => ["callback" => "migrateAndUpdateBatchEnrollmentEvent", "args" => [], "auth" => false]
        ]
    ],
    "/enrollment/migration/db/" => [
        "controller" => "EnrollmentMigrationController",
        "methods" => [
            "PUT" => ["callback" => "migrateEnrollmentDataInChunks", "args" => [], "auth" => false]
        ]
    ]
];