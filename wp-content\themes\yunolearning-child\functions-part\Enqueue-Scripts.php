<?php
class Enqueue_Scripts
{
    /**
     * deregister function will unlink files (script and styles) from the page or post,
     * where no need.
     */
    function deregisterResources()
    {
        $styles = array(
            "tribe-common-skeleton-style",
            "wp-block-library",
            "um_fonticons_ii",
            "um_fonticons_fa",
            "select2",
            "um_crop",
            "um_modal",
            "um_styles",
            "um_profile",
            "um_account",
            "um_misc",
            "um_fileupload",
            "um_datetime",
            "um_datetime_date",
            "um_datetime_time",
            "um_raty",
            "um_scrollbar",
            "um_tipsy",
            "um_responsive",
            "um_default_css",
            "ai1ec_style",
            "avada-styleshee",
            "simcal-qtip",
            "simcal-default-calendar-grid",
            "simcal-default-calendar-list",
            "fusion-dynamic-css",
            "avada-max-shbp-max-sh-shbp",
            "wprevpro_w3",
            "unslider",
            "avada-stylesheet",
            "avada-IE-fontawesome",
            "avada-IE",
            "avada-max-1c-max-sh-640",
            "avada-max-2c",
            "avada-min-2c-max-3c",
            "avada-min-3c-max-4c",
            "avada-min-4c-max-5c",
            "avada-min-5c-max-6c",
            "avada-min-shbp",
            "avada-max-shbp-max-sh-shbp",
            "avada-min-768-max-1024-p",
            "avada-min-768-max-1024-l",
            "avada-max-sh-cbp-max-sh-cbp-eslider",
            "avada-max-sh-sbp",
            "avada-max-shbp-18",
            "avada-max-shbp-32",
            "avada-min-sh-cbp",
            "avada-max-640",
            "tribe-events-pro-mini-calendar-block-styles-css",
            "wpProQuiz_front_style-css",
            "fusion-core-comment-form-css",
            "fusion-core-privacy-css",
            "fusion-core-faqs-css"
        );

        $scripts = array(
            "contact-form-7",
            "jquery",
            "writing-test-javascript",
            "fusion-ie9",
            "simcal-qtip",
            "simcal-fullcal-moment",
            "simcal-moment-timezone",
            "simcal-default-calendar",
            "avada-faqs",
            "avada-portfolio",
            "fusion-chart",
            "fusion-gallery",
            "fusion-tabs",
            "fusion-count-down",
            "fusion-content-boxes",
            "fusion-progress",
            "fusion-google-map",
            "fusion-counters-circle",
            "fusion-toggles",
            "fusion-testimonials",
            "fusion-column-bg-image",
            "fusion-column",
            "fusion-counters-box",
            "fusion-syntax-highlighter",
            "fusion-flip-boxes",
            "fusion-events",
            "fusion-container",
            "fusion-title",
            "fusion-recent-posts",
            "jquery-event-move",
            "fusion-image-before-after",
            "fusion-modal",
            "fusion-animations",
            "jquery-count-to",
            "jquery-count-down",
            "fusion-video",
            "fusion-chartjs",
            "avada-vertical-menu-widget",
            "cssua",
            "modernizr",
            "isotope",
            "packery",
            "bootstrap-collapse",
            "bootstrap-modal",
            "bootstrap-tooltip",
            "bootstrap-popover",
            "bootstrap-transition",
            "bootstrap-tab",
            "jquery-waypoints",
            "jquery-request-animation-frame",
            "jquery-appear",
            "jquery-caroufredsel",
            "jquery-cycle",
            "jquery-easing",
            "jquery-easy-pie-chart",
            "jquery-fitvids",
            "jquery-flexslider",
            "jquery-fusion-maps",
            "jquery-hover-flow",
            "jquery-hover-intent",
            "jquery-lightbox",
            "jquery-infinite-scroll",
            "jquery-mousewheel",
            "jquery-placeholder",
            "jquery-touch-swipe",
            "jquery-fade",
            "images-loaded",
            "fusion-alert",
            "fusion-equal-heights",
            "fusion-parallax",
            "fusion-video-bg",
            "fusion-video-general",
            "fusion-waypoints",
            "fusion-lightbox",
            "fusion-carousel",
            "fusion-flexslider",
            "fusion-popover",
            "fusion-tooltip",
            "fusion-sharing-box",
            "fusion-blog",
            "fusion-button",
            "vimeo-player",
            "fusion-general-global",
            "fusion-ie1011",
            "fusion-scroll-to-anchor",
            "fusion-responsive-typography",
            "bootstrap-scrollspy",
            "avada-comments",
            "avada-general-footer",
            "avada-quantity",
            "avada-scrollspy",
            "avada-select",
            "avada-sidebars",
            "jquery-sticky-kit",
            "avada-tabs-widget",
            "avada-header",
            "avada-menu",
            "jquery-to-top",
            "avada-to-top",
            "avada-drop-down",
            "avada-contact-form-7",
            "jquery-elastic-slider",
            "avada-elastic-slider",
            "avada-events",
            "avada-fusion-slider",
            "select2",
            "um_modal",
            "um_fileupload",
            "um_datetime",
            "um_datetime_date",
            "um_datetime_time",
            "um_datetime_legacy",
            "um_raty",
            "um_crop",
            "um_tipsy",
            "um_functions",
            "um-gdpr",
            "um_responsive",
            "um_conditional",
            "um_scripts",
            "um_profile",
            "um_account",
            "wp-review-slider-pro_plublic-min",
            "wp-review-slider-pro_unslider_comb-min",
            "simplecalendar-imagesloaded",
            "cssua-js",
            "modernizr-js",
            "fusion-js-extra",
            "fusion-js",
            "bootstrap-transition-js",
            "bootstrap-tooltip-js",
            "fusion-tooltip-js",
            "vimeo-player-js"
        );

        if (!is_admin()) {
            for ($i = 0; $i < count($styles); $i++) {
                wp_deregister_style($styles[$i]);
            }

            for ($i = 0; $i < count($scripts); $i++) {
                wp_deregister_script($scripts[$i]);
            }
        }
    }


    function loadCommonStyles()
    {
        if (!is_admin()) {
            wp_register_style('yunoCommonCSS', get_stylesheet_directory_uri() . '/dist/common/common.min.css', array(), false, 'all');

            wp_enqueue_style('yunoCommonCSS');
        }
    }

    function loadCommonJS()
    {
        if (!is_admin()) {
            wp_register_script('yunoJquery', get_stylesheet_directory_uri() . '/assets/js/lib/dist/jqueryWithBootstrap/jqueryWithBootstrap.min.js', array(), 3.5, false);

            wp_enqueue_script('yunoJquery');

            if (site_url() == YUNO_PROD_ENV_HTTPS) {
                wp_register_script('yunoCommonJS', get_stylesheet_directory_uri() . '/dist/common/common.min.prod.js#deferload', array(), 2.0, true);
                wp_enqueue_script('yunoCommonJS');
            } else {
                wp_register_script('yunoCommonJS', get_stylesheet_directory_uri() . '/dist/common/common.min.js#deferload', array(), 2.0, true);
                wp_enqueue_script('yunoCommonJS');
            }
        }
    }
    /**
     * Yuno enqueue scripts and styles
     */
    function pageSpecificResource()
    {
        global $post;
        global $template;

        if (is_page() || is_single()) {
            switch ($post->post_name) // post_name is the post slug which is more consistent for matching to here
            {
                case 'other-page':
                    wp_enqueue_script('custom1', get_stylesheet_directory_uri() . '/components/other/other.js#deferload', array(), 1.0, true);
                    break;
            }
        }

        if (is_singular('report')) {
            wp_enqueue_style('yunoBuefyCSS', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/enrolmentReport/dist/enrolmentReport.min.css', array(), false, 'all');
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/enrolmentReport/dist/enrolmentReport.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('learning_content') || is_page_template('templates/learningContentV2.php')) {
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/learningContentV2/dist/learningContentV2.min.css', array(), false, 'all');
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/learningContentV2/dist/learningContentV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('tribe_events')) {
            if (get_post_meta(get_the_ID(), '_webinar_class', true) == "1") {
                if (is_user_logged_in() && is_array(get_post_meta(get_the_ID(), 'YunoClassPrivateLearners', true))) {
                    if (in_array(get_userdata(get_current_user_id())->roles[0], array("SEO Manager")) && !in_array(get_current_user_id(), get_post_meta(get_the_ID(), 'YunoClassPrivateLearners', true))) {
                        wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/webinar/dist/webinar.min.css', array(), false, 'all');
                        wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/webinar/dist/webinar.min.js#deferload', array(), 1.0, true);
                    }
                } else {
                    wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/webinar/dist/webinar.min.css', array(), false, 'all');
                    wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/webinar/dist/webinar.min.js#deferload', array(), 1.0, true);
                }
            }
        }

        if (is_singular('article') || is_page_template('templates/singleResource.php')) {
            wp_enqueue_style('yunoArticleCSS', get_stylesheet_directory_uri() . '/pages/articleV2/dist/articleV2.min.css', array(), false, 'all');
            wp_enqueue_script('yunoArticleJS', get_stylesheet_directory_uri() . '/pages/articleV2/dist/articleV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('quiz')) {
            wp_enqueue_style('buefy', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/quizAttemptV2/dist/quizAttemptV2.min.css', array(), false, 'all');

            wp_enqueue_script('veeValidate', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 3.0, true);
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/quizAttemptV2/dist/quizAttemptV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('writing_test')) {
            wp_enqueue_style('buefy', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/writingTask/dist/writingTask.min.css', array(), false, 'all');

            wp_enqueue_script('veeValidate', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 3.0, true);
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/writingTask/dist/writingTask.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('academy')) {

            wp_dequeue_script('yunoJquery');
            wp_dequeue_script('yunoCommonJS');
            

            wp_enqueue_style('buefy', 'https://cdn.jsdelivr.net/npm/buefy@0.9.25/dist/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoAcademy', get_stylesheet_directory_uri() . '/pages/academyV2/dist/academyV2.min.css', array(), false, 'all');

            wp_enqueue_script('jquerys', 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js', array(), 3.7, true);
            wp_enqueue_script('axios', get_stylesheet_directory_uri() . '/assets/js/lib/axios.min.js', array(), 2.6, true);

            if (site_url() == YUNO_PROD_ENV_HTTPS) {
                wp_enqueue_script('vueDev', get_stylesheet_directory_uri() . '/assets/js/lib/vue.js', array(), 2.6, true);
            } else {
                wp_enqueue_script('vueDev', get_stylesheet_directory_uri() . '/assets/js/lib/vue.dev.min.js', array(), 2.6, true);
            }
            wp_enqueue_script('vuex', get_stylesheet_directory_uri() . '/assets/js/lib/vuex.js', array(), 2.6, true);
            wp_enqueue_script('buefy', 'https://cdn.jsdelivr.net/npm/buefy@0.9.25/dist/buefy.min.js', array(), 2.14, true);
            wp_enqueue_script('yunoAcademy', get_stylesheet_directory_uri() . '/pages/academyV2/dist/academyV2.min.js#deferload', array(), 1.0, true);
        }


        // if ( is_page_template('templates/auth.php') ) {
        //   if (!empty(CURRENT_LOGGED_IN_USER_ID)) {
        //     $userId = CURRENT_LOGGED_IN_USER_ID;	
        //     error_log("user logged in:".$userId);
        //     $userdata = get_userdata($userId);
        //     if ($userdata->roles) {
        //       if (in_array('SEO Manager', $userdata->roles)) {
        //       $urlRedirect = site_url('/learner/');	
        //       } 
        //       else if (in_array('um_instructor', $userdata->roles)) {
        //         $urlRedirect = site_url('/instructor/');
        //       }
        //       else if (in_array('um_content-admin', $userdata->roles)) {
        //         $urlRedirect = site_url('/published-e-books//');
        //       }
        //       else {
        //         $urlRedirect = site_url('/enrollments/all/');          
        //       }
        //       wp_redirect($urlRedirect);		
        //     }
        //   }
        // }

        if (is_page_template('templates/vedicMaths.php')) {
            wp_enqueue_style('yunoVedicMaths', get_stylesheet_directory_uri() . '/pages/vedicMaths/dist/vedicMaths.min.css', array(), false, 'all');
            wp_enqueue_script('yunoVedicMaths', get_stylesheet_directory_uri() . '/pages/vedicMaths/dist/vedicMaths.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/recording.php')) {
            wp_enqueue_style('yunoRecording', get_stylesheet_directory_uri() . '/pages/recording/recording.min.css', array(), false, 'all');
            wp_enqueue_script('yunoRecording', get_stylesheet_directory_uri() . '/pages/recording/recording.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/createBatch.php')) {
            wp_enqueue_style('yunoCreateBatch', get_stylesheet_directory_uri() . '/pages/createBatch/createBatch.min.css', array(), false, 'all');
            wp_enqueue_script('yunoCreateBatch', get_stylesheet_directory_uri() . '/pages/createBatch/createBatch.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/instructorInsights.php')) {
            wp_enqueue_style('yunoInstructorInsights', get_stylesheet_directory_uri() . '/pages/instructorInsights/instructorInsights.min.css', array(), false, 'all');
            wp_enqueue_script('yunoInstructorInsights', get_stylesheet_directory_uri() . '/pages/instructorInsights/instructorInsights.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/videoListing.php')) {
            wp_enqueue_style('yunoVideoListingCSS', get_stylesheet_directory_uri() . '/pages/eBookListing/eBookListing.min.css', array(), false, 'all');
            wp_enqueue_script('yunoVideoListingJS', get_stylesheet_directory_uri() . '/pages/videoListing/videoListing.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('video') || is_page_template('templates/video.php') || is_page_template('templates/singleResource.php')) {
            wp_enqueue_style('yunoVideo', get_stylesheet_directory_uri() . '/pages/videoV2/dist/videoV2.min.css', array(), false, 'all');
            wp_enqueue_script('yunoVideoPlayer', 'https://player.vimeo.com/api/player.js', array(), 1.0, true);
            wp_enqueue_script('yunoVideo', get_stylesheet_directory_uri() . '/pages/videoV2/dist/videoV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('documents') || is_page_template('templates/document.php') || is_page_template('templates/singleResource.php')) {
            wp_enqueue_style('yunoDocCSS', get_stylesheet_directory_uri() . '/pages/documentV2/dist/documentV2.min.css', array(), false, 'all');
            wp_enqueue_script('pdf', 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.min.js', array(), 2.6, true);
            wp_enqueue_script('hammer', 'https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js', array(), 2.6, true);
            wp_enqueue_script('yunoDocJS', get_stylesheet_directory_uri() . '/pages/documentV2/dist/documentV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/eBookListing.php')) {
            wp_enqueue_style('yunoEBookListingCSS', get_stylesheet_directory_uri() . '/pages/eBookListing/eBookListing.min.css', array(), false, 'all');
            wp_enqueue_script('yunoEBookListingJS', get_stylesheet_directory_uri() . '/pages/eBookListing/eBookListing.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('ebooks') || is_page_template('templates/eBook.php') || is_page_template('templates/singleResource.php')) {
            wp_enqueue_style('yunoEBookCSS', get_stylesheet_directory_uri() . '/pages/eBookV2/dist/eBookV2.min.css', array(), false, 'all');
            wp_enqueue_script('yunoEBookJS', get_stylesheet_directory_uri() . '/pages/eBookV2/dist/eBookV2.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/mapCourses.php')) {
            wp_enqueue_style('yunoMapCoursesCSS', get_stylesheet_directory_uri() . '/pages/mapCourses/mapCourses.min.css', array(), false, 'all');
            wp_enqueue_script('yunoMapCoursesJS', get_stylesheet_directory_uri() . '/pages/mapCourses/mapCourses.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/invite.php')) {
            wp_enqueue_style('yunoInviteCSS', get_stylesheet_directory_uri() . '/pages/invite/invite.css', array(), false, 'all');
            wp_enqueue_script('yunoInviteJS', get_stylesheet_directory_uri() . '/pages/invite/invite.js#deferload', array(), 1.0, true);
        }

        // if (is_page_template('templates/settings.php')) {
        //     wp_enqueue_style('yunoSettingsCSS', get_stylesheet_directory_uri() . '/pages/settings/settings.min.css', array(), false, 'all');
        //     wp_enqueue_script('yunoSettingsJS', get_stylesheet_directory_uri() . '/pages/settings/settings.min.js#deferload', array(), 1.0, true);
        // }

        if (is_page_template('templates/anilLamba.php')) {
            wp_enqueue_style('yunoAnilLambaCSS', get_stylesheet_directory_uri() . '/pages/anilLamba/anilLamba.min.css', array(), false, 'all');
            wp_enqueue_script('yunoAnilLambaJS', get_stylesheet_directory_uri() . '/pages/anilLamba/anilLamba.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/writingTest.php')) {
            wp_enqueue_style('yunoPostCSS', get_stylesheet_directory_uri() . '/pages/post/post.css', array(), false, 'all');

            wp_enqueue_script('yunoWritingTestJS', get_stylesheet_directory_uri() . '/pages/writingTest/writingTest.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/blogList.php')) {
            wp_enqueue_style('yunoBuefyCSS', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_script('vee', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 1.0, true);
            wp_enqueue_script('yunoBlogListJS', get_stylesheet_directory_uri() . '/pages/blogList/blogList.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/paymentLinkList.php')) {
            wp_enqueue_style('yunoPaymentLinkListCSS', get_stylesheet_directory_uri() . '/pages/paymentLinkList/paymentLinkList.min.css', array(), false, 'all');

            wp_enqueue_script('yunoPaymentLinkListJS', get_stylesheet_directory_uri() . '/pages/paymentLinkList/paymentLinkList.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/demoClasses.php')) {
            wp_enqueue_style('yunoBatchCardCSS', get_stylesheet_directory_uri() . '/pages/demo/demo.min.css', array(), false, 'all');

            wp_enqueue_script('yunoDemoJS', get_stylesheet_directory_uri() . '/pages/demo/demo.min.js#deferload', array(), 1.0, true);
        }


        if (is_page_template('templates/auth.php')) {
            wp_enqueue_script('yunoAuthJS', get_stylesheet_directory_uri() . '/pages/auth/auth.js#deferload', array(), 1.0, true);
        }

        if (is_404()) {
        }

        if ('writing_test' == get_post_type()) {
            wp_enqueue_style('yunoBuefyCSS', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPostCSS', get_stylesheet_directory_uri() . '/pages/post/post.css', array(), false, 'all');
            wp_enqueue_script('vee', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 1.0, true);
            wp_enqueue_script('socialSharing', get_stylesheet_directory_uri() . '/assets/js/lib/vue-social-sharing.js#deferload', array(), 1.0, true);
            wp_enqueue_script('yunoPostJS', get_stylesheet_directory_uri() . '/pages/post/post.js#deferload', array(), 1.0, true);
        }

        // if (is_archive()) {
        //     wp_enqueue_style('yunoBuefyCSS', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
        //     wp_enqueue_script('vee', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 1.0, true);
        //     wp_enqueue_script('yunoArchiveJS', get_stylesheet_directory_uri() . '/pages/blogsByCategory/blogsByCategory.js#deferload', array(), 1.0, true);
        // }

        if (basename(get_page_template()) === 'page.php') {
            /* wp_enqueue_style('yunoBuefyCSS', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all'); */
            wp_enqueue_style('yunoDefaultPageCSS', get_stylesheet_directory_uri() . '/pages/defaultPage/defaultPage.css', array(), false, 'all');
            wp_enqueue_script('vee', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 1.0, true);
            wp_enqueue_script('yunoDefaultPageJS', get_stylesheet_directory_uri() . '/pages/defaultPage/defaultPage.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/paymentLink.php')) {
            wp_enqueue_style('yunoPaymentLink', get_stylesheet_directory_uri() . '/pages/paymentLink/paymentLink.min.css', array(), false, 'all');

            wp_enqueue_script('yunoPaymentLink', get_stylesheet_directory_uri() . '/pages/paymentLink/paymentLink.min.js#deferload', array(), 1.0, true);
        }



        if (is_page_template('templates/auth.php')) {
            wp_enqueue_script('yunoAuthJS', get_stylesheet_directory_uri() . '/pages/auth/auth.js#deferload', array(), 1.0, true);
        }

        if (is_singular('profile')) {
            wp_enqueue_style('buefy', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/instructorProfileV2/dist/instructorProfileV2.min.css', array(), false, 'all');

            wp_enqueue_script('veeValidate', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 3.0, true);
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/instructorProfileV2/dist/instructorProfileV2.min.js#deferload', array(), 1.0, true);
        };

        if (is_page_template('templates/ielts.php')) {
            wp_enqueue_style('yunoIELTSCSS', get_stylesheet_directory_uri() . '/pages/ielts/dist/ielts.min.css', array(), false, 'all');

            wp_enqueue_script('yunoIELTSJS', get_stylesheet_directory_uri() . '/pages/ielts/dist/ielts.min.js#deferload', array(), 1.0, true);
        };

        if (is_page_template('templates/ielts.php')) {
            wp_enqueue_style('yunoIELTSCSS', get_stylesheet_directory_uri() . '/pages/ielts/ielts.min.css', array(), false, 'all');

            wp_enqueue_script('yunoIELTSJS', get_stylesheet_directory_uri() . '/pages/ielts/ielts.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/spokenEnglish.php')) {
            wp_enqueue_style('yunoSpokenEnglishCSS', get_stylesheet_directory_uri() . '/pages/spokenEnglish/spokenEnglish.min.css', array(), false, 'all');

            wp_enqueue_script('yunoSpokenEnglishJS', get_stylesheet_directory_uri() . '/pages/spokenEnglish/spokenEnglish.min.js#deferload', array(), 1.0, true);
        }

        if (is_page_template('templates/codingForKids.php')) {
            wp_enqueue_style('yunoCodingForKidsCSS', get_stylesheet_directory_uri() . '/pages/codingForKids/codingForKids.min.css', array(), false, 'all');

            wp_enqueue_script('yunoCodingForKidsJS', get_stylesheet_directory_uri() . '/pages/codingForKids/codingForKids.min.js#deferload', array(), 1.0, true);
        }

        if (is_singular('batch')) {
            wp_enqueue_style('buefy', get_stylesheet_directory_uri() . '/assets/css/lib/buefy.min.css', array(), false, 'all');
            wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/batchPageV2/dist/batchPageV2.min.css', array(), false, 'all');

            wp_enqueue_script('razorpay', 'https://checkout.razorpay.com/v1/checkout.js', array(), 2.9, true);
            wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/batchPageV2/dist/batchPageV2.min.js#deferload', array(), 1.0, true);
        }

        if (!is_admin()) {
            wp_register_script('yunoApp', get_stylesheet_directory_uri() . '/components/app.js#deferload', array(), 1.0, true);
            wp_enqueue_script('yunoApp');
        }
    }

    function remove_specific_css()
    {
        $styles = wp_styles();

        foreach ($styles->registered as $style) {
            if (strpos($style->src, 'tribe-events-pro-mini-calendar-block.min.css') !== false) {
                wp_deregister_style($style->handle);
                wp_dequeue_style($style->handle);
            }
        }
    }

    function deregister_custom_css()
    {
        wp_dequeue_style('wpProQuiz_front_style');
        wp_deregister_style('wpProQuiz_front_style');

        wp_dequeue_style('fusion-core-comment-form');
        wp_deregister_style('fusion-core-comment-form');

        wp_dequeue_style('fusion-core-privacy');
        wp_deregister_style('fusion-core-privacy');

        wp_dequeue_style('fusion-core-faqs');
        wp_deregister_style('fusion-core-faqs');

        wp_dequeue_style('fusion-core-portfolio');
        wp_deregister_style('fusion-core-portfolio');
    }

    // Hook the filter to the script_loader_tag action
    function add_defer_attribute($tag, $handle)
    {
        // Check if the script handle matches the one you want to modify
        if ($handle === 'yunoPage' || $handle === 'veeValidate' || $handle === 'yunoDefaultPageJS' || $handle === 'vee' || $handle === 'yunoCommonJS' || $handle === 'yunoApp' || $handle === 'custom1' || $handle === 'yunoArticleJS' || $handle === 'yunoVedicMaths' || $handle === 'yunoRecording' || $handle === 'yunoCreateBatch' || $handle === 'yunoInstructorInsights' || $handle === 'yunoVideoListingJS' || $handle === 'yunoVideo' || $handle === 'yunoDocJS' || $handle === 'yunoEBookListingJS' || $handle === 'yunoEBookJS' || $handle === 'yunoMapCoursesJS' || $handle === 'yunoInviteJS' || $handle === 'yunoSettingsJS' || $handle === 'yunoAnilLambaJS' || $handle === 'yunoWritingTestJS' || $handle === 'yunoBlogListJS' || $handle === 'yunoPaymentLinkListJS' || $handle === 'yunoDemoJS' || $handle === 'yunoAuthJS' || $handle === 'socialSharing' || $handle === 'headerLHS1' || $handle === 'headerLHS2' || $handle === 'headerLHS3' || $handle === 'headerLHS' || $handle === 'searchBar1' || $handle === 'searchBar' || $handle === 'yunoPostJS' || $handle === 'yunoPaymentLink' || $handle === 'yunoIELTSJS' || $handle === 'yunoSpokenEnglishJS' || $handle === 'yunoCodingForKidsJS' || $handle === 'yunoStudentInterview' || $handle === 'yunoVeeValidateJS' || $handle === 'yunoModalContent' || $handle === 'yunoIELTSSignUpJS' || $handle === 'yunoChooseCategory' || $handle === 'yunolodash' || $handle === 'yunoTable' || $handle === 'yunoTabsJS' || $handle === 'yunoMomentJS' || $handle === 'yunoHightlight' || $handle === 'yunoComponents' || $handle === 'ckEditor' || $handle === 'ckEditorVue' || $handle === 'yunoCreatePost' || $handle === 'sortable' || $handle === 'draggable' || $handle === 'momentJS' || $handle === 'lodashJS' || $handle === 'yunoCreatePost' || $handle === 'yunoCreate' || $handle === 'radial' || $handle === 'yunoLearnerProfile' || $handle === 'yunoStaticPage' || $handle === 'yunoResources' || $handle === 'yunoSEJS') {
            // Add the defer attribute with the value "defer" to the script tag
            $tag = str_replace('&#039;', '"', $tag);
        }
        return $tag;
    }
}
