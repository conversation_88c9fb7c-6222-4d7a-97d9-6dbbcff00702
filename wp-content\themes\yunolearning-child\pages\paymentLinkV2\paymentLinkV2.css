#app .paymentLink {
  padding-top: 30px;
  padding-bottom: 15px;
}

@media (min-width: 768px) {
  #app .paymentLink {
    padding-top: 30px;
    padding-bottom: 60px;
  }
}

#app .paymentLink .sectionTitle {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .paymentLink #previousFields, #app .paymentLink #nextFields {
  padding: 15px;
}

#app .paymentLink #previousFields.hideMe, #app .paymentLink #nextFields.hideMe {
  display: none;
}

@media (min-width: 768px) {
  #app .paymentLink #previousFields, #app .paymentLink #nextFields {
    padding: 30px 30px 20px;
  }
}

#app .paymentLink #previousFields.noBtmGap {
  padding-bottom: 0;
}

#app .paymentLink #nextFields {
  padding-top: 0;
  padding-bottom: 30px;
}

#app .paymentLink .formWrapper {
  background-color: #f7f9fb;
  border: 1px solid #D9D9D9;
  border-radius: 6px;
}

#app .paymentLink .formWrapper button.button {
  padding: 10px 30px;
}

#app .paymentLink .formWrapper .field .control textarea {
  height: 200px;
}

#app .paymentLink .formWrapper .field .paymentLinkBatch .dropdown-content .dropdown-item {
  white-space: normal;
  word-break: break-all;
}

#app .paymentLink .ctaWrapper {
  padding-top: 30px;
}

#app .paymentLink .inlineFields {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 15px;
}

#app .paymentLink .inlineFields > .field {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 10px;
}

#app .paymentLink .inlineFields > .field:last-child {
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  #app .paymentLink .inlineFields > .field {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
  }
  #app .paymentLink .inlineFields > .field:last-child {
    margin-bottom: 0;
  }
}

#app .paymentLink .taginput .taginput-container .autocomplete input {
  background: none;
  border: 0;
}

#app .paymentLink .inviteWrapper {
  margin-top: 30px;
}

@media (min-width: 768px) {
  #app .paymentLink .inviteWrapper {
    margin-top: 0;
  }
}

#app .paymentLink .count {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .paymentLink .count span {
  font-weight: 400;
}

#app .paymentLink .selectedLearners li {
  border-color: rgba(0, 0, 0, 0.2);
  padding: 5px 15px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .paymentLink .selectedLearners li .caption {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#app .paymentLink .selectedLearners li .fa {
  font-size: 18px;
  cursor: pointer;
}

#app .classFields > li {
  font-size: 16px;
  line-height: normal;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

#app .classFields > li .caption {
  font-weight: 500;
  color: #000;
  display: block;
  margin-bottom: 5px;
}

#app .classFields > li:last-child {
  margin-bottom: 0;
}

#app .classFields > li .selectedLearners {
  margin-top: 10px;
}

#app .classFields > li .selectedLearners li {
  padding: 5px 10px;
  font-weight: 400;
}

#app .classFields > li .selectedLearners li .caption {
  font-weight: 400;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

#app .classFields > li .clipboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .classFields > li .clipboard .control {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 85%;
          flex: 0 0 85%;
}

#app .classFields > li .clipboard .trigger {
  margin-left: 15px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .courseList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .courseList .img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
}

#app .courseList .img .fa {
  font-size: 40px;
  color: rgba(0, 0, 0, 0.5);
}

#app .courseList .img img {
  width: 40px;
  height: 40px;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px;
  overflow: hidden;
}

#app .courseList .courseContent {
  padding-left: 15px;
}

#app .courseList .courseContent .instructorName {
  text-transform: capitalize;
}

#app .referralCode {
  margin-bottom: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

#app .referralCode .field {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(70% - 15px);
          flex: 0 0 calc(70% - 15px);
  margin-right: 15px;
  margin-bottom: 0;
}

#app .referralCode .button {
  height: 36px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 30%;
          flex: 0 0 30%;
}

.notices .toast {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
}

.notices .toast.is-danger {
  background-color: red;
}
/*# sourceMappingURL=paymentLinkV2.css.map */