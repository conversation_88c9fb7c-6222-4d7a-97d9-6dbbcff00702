.fontAwesomeIcon, #app .yunoTable .b-table .table thead th.is-current-sort .icon .mdi-arrow-up:after, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined, #app .yunoModal.drawerModal .modal-close::after, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slide-in-right, #app .yunoModal.drawerModal .modal-content {
  -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

.dark87, .largerTitle, .body3, #app .yunoTable .b-table .table thead, #app .yunoModal.drawerModal .modal-content, #app .yunoModal.drawerModal .learners .userImg figcaption .primaryTitle, #app .yunoModal.drawerModal .learners .userImg .inlineList li .listLabel, #app .yunoModal.drawerModal .filterNav > li > a, #app .yunoDropdown.multiSelect .dropdown-menu .listCaption, #app .yunoDropdown.hideColumn .dropdown-menu .listCaption {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, .body1, #app .yunoDropdown .timesDays small, .body2, .overline, #app .yunoTable .note, #app .yunoTable .b-table .table tbody, #app .yunoModal.drawerModal .learners .userImg figcaption .secondaryCaption, #app .yunoModal.drawerModal .learners .userImg .inlineList li .value, #app .yunoModal.drawerModal .classDate, #app .yunoModal.drawerModal .stats li .itemLabel, #app .yunoModal.drawerModal .classDetail .barWrapper .barValue, #app .yunoDropdown .dropdown-menu a, #app .yunoDropdown .labelWrapper .placeHolder, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active {
  color: rgba(0, 0, 0, 0.6);
}

.dark38, #app .yunoModal.drawerModal .intro .introLabel, #app .yunoModal.drawerModal .classDetail .classDateV2, #app .yunoModal.drawerModal .classDetail .classInfo .itemLabel, #app .yunoModal.drawerModal .classes .classesLabel, #app .yunoModal.drawerModal .classes .days li .disc {
  color: rgba(0, 0, 0, 0.38);
}

.fontColorDark {
  color: #201A19;
}

.fontColorDarkVariant {
  color: #534342;
}

.largestTitle {
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .largestTitle {
    font-size: 48px;
    line-height: 62px;
    font-weight: 700;
    margin-bottom: 10px;
  }
}

.largerTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 10px;
}

.largeTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallerCaption {
  font-size: 16px;
  line-height: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

.smallestCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.body1, #app .yunoDropdown .timesDays small {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

.body2 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.body3 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}

.overline {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.progress-wrapper .progress-value {
  color: rgba(0, 0, 0, 0.7);
}

.progress-wrapper .progress {
  border: 1px solid #FFF;
  height: 8px;
}

.progress-wrapper .progress.is-orange::-webkit-progress-value {
  background: #FC9927;
}

.progress-wrapper .progress.is-red::-webkit-progress-value {
  background: #CA0813;
}

.progress-wrapper .progress.is-yellow::-webkit-progress-value {
  background: #F0C042;
}

.progress-wrapper .progress.is-lightGreen::-webkit-progress-value {
  background: #669D4F;
}

.progress-wrapper .progress.is-darkGreen::-webkit-progress-value {
  background: #356B21;
}

#app .yunoTable {
  margin-top: 10px;
}

#app .yunoTable .note {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .yunoTable .table-mobile-sort .field.has-addons .control:not(.is-expanded) {
  display: none;
}

#app .yunoTable .table-mobile-sort .field.has-addons .control:first-child:not(:only-child) .select select {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}

#app .yunoTable .b-table .table-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
}

#app .yunoTable .b-table .field.table-mobile-sort {
  display: none;
}

#app .yunoTable .b-table .table thead {
  font-size: 14px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

#app .yunoTable .b-table .table thead th {
  padding: 10px 10px 10px 0;
}

#app .yunoTable .b-table .table thead th.is-current-sort .icon .mdi-arrow-up:after {
  content: "\f077";
  position: relative;
  top: -1px;
}

#app .yunoTable .b-table .table thead th .sort-icon.is-invisible {
  display: none;
}

#app .yunoTable .b-table .table thead th .th-wrap .is-relative {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

#app .yunoTable .b-table .table tbody {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .yunoTable .b-table .table tbody tr:hover .actions {
  visibility: visible;
}

#app .yunoTable .b-table .table tbody td {
  vertical-align: middle;
  padding: 5px 10px 5px 0;
}

#app .yunoTable .b-table .table tbody td div {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

#app .yunoTable .b-table .table tbody .userWithPhoto {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 60px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

#app .yunoTable .b-table .table tbody .userWithPhoto img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 5px;
  font-size: 0;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive .material-icons, #app .yunoTable .b-table .table tbody .hasActiveInactive .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .hasActiveInactive .mdi::after, #app .yunoTable .b-table .table tbody .hasActiveInactive .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .hasActiveInactive .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .hasActiveInactive .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .hasActiveInactive .dropdown-item.is-active::before {
  font-size: 20px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive.active {
  color: #356B21;
  font-weight: 500;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive.inactive {
  color: #FC9927;
  font-weight: 500;
}

#app .yunoTable .b-table .table tbody .grid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 -3px;
}

#app .yunoTable .b-table .table tbody .grid .gridItem {
  padding: 0 3px;
}

#app .yunoTable .b-table .table tbody .grid .material-icons-outlined, #app .yunoTable .b-table .table tbody .grid .yunoModal.drawerModal .modal-close::after, #app .yunoModal.drawerModal .yunoTable .b-table .table tbody .grid .modal-close::after, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item::before, #app .yunoTable .b-table .table tbody .grid .material-icons, #app .yunoTable .b-table .table tbody .grid .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .mdi::after, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item.is-active::before {
  font-size: 12px;
}

#app .yunoTable .b-table .table tbody .percentageBlock {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 150px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .yunoTable .b-table .table tbody .percentageBlock .progress-wrapper {
  width: 60%;
  margin: 0;
}

#app .yunoTable .b-table .table tbody .percentageBlock .percentage a {
  padding-left: 5px;
}

#app .yunoTable .b-table .table tbody .percentageBlock .material-icons, #app .yunoTable .b-table .table tbody .percentageBlock .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .percentageBlock .mdi::after, #app .yunoTable .b-table .table tbody .percentageBlock .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .percentageBlock .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .percentageBlock .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .percentageBlock .dropdown-item.is-active::before {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .yunoTable .b-table .table tbody .actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  visibility: hidden;
}

#app .yunoTable .b-table .table tbody .actions li a {
  display: block;
  position: relative;
}

#app .yunoTable .b-table .table tbody .actions li a:hover {
  text-decoration: none;
}

#app .yunoTable .b-table .table tbody .actions li .itemLabel {
  font-size: 0;
  position: absolute;
  left: -999999px;
}

#app .yunoTable .b-table .table tbody .actions li .itemIcon {
  font-size: 18px;
  display: block;
  padding: 2px 8px;
}

#app .yunoTable .b-table .table tbody .actions.copyToClipboard .wrapper {
  cursor: pointer;
  color: #A81E22;
}

#app .yunoTable .b-table .pagination {
  margin: 0;
}

#app .yunoTable .b-table .pagination .pagination-ellipsis {
  font-size: 0;
}

#app .yunoTable .b-table .pagination .pagination-ellipsis::after {
  content: "...";
  font-size: 14px;
  position: relative;
  top: 17px;
  color: #000;
}

#app .yunoTable .b-table .pagination-link {
  color: #000;
}

#app .yunoTable .b-table .pagination-link:hover {
  text-decoration: none;
}

#app .yunoTable .b-table .pagination-link.is-current {
  background-color: #A81E22;
  border-color: #A81E22;
  color: #FFF;
}

#app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f060";
}

#app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f061";
}

#app .emptyStateV2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 5%;
}

#app .emptyStateV2 figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .emptyStateV2 figure img {
  width: 80px;
  height: auto;
}

#app .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
  color: #534342;
}

#app .emptyStateV2 figure .button {
  margin-top: 15px;
}

#app .emptyStateV2 .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .yunoModal.drawerModal {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

#app .yunoModal.drawerModal .scrollable {
  overflow-y: auto;
}

#app .yunoModal.drawerModal .drawerTitle {
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .modal-close {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.6);
  right: 0;
  top: 0;
}

#app .yunoModal.drawerModal .modal-close::after {
  content: "\e5cd";
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoModal.drawerModal .modal-content {
  height: 100%;
  max-height: none;
  margin: 0;
  border-radius: 0;
}

#app .yunoModal.drawerModal .learners {
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .learners.noBtmGap {
  margin-bottom: 0;
}

#app .yunoModal.drawerModal .learners .userImg {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 15px;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .learners .userImg img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 0;
  background-color: #E6E6E6;
}

#app .yunoModal.drawerModal .learners .userImg figcaption {
  font-size: 16px;
  font-weight: 500;
  margin-left: 10px;
}

#app .yunoModal.drawerModal .learners .userImg figcaption .primaryTitle {
  font-size: 16px;
  line-height: auto;
  font-weight: 500;
  text-transform: capitalize;
}

#app .yunoModal.drawerModal .learners .userImg figcaption .secondaryCaption {
  font-size: 12px;
  line-height: 16px;
}

#app .yunoModal.drawerModal .learners .userImg .inlineList {
  margin-top: 10px;
}

@media (min-width: 768px) {
  #app .yunoModal.drawerModal .learners .userImg .inlineList li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .yunoModal.drawerModal .learners .userImg .inlineList li .listLabel {
  font-size: 12px;
  font-weight: 500;
}

#app .yunoModal.drawerModal .learners .userImg .inlineList li .value {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  #app .yunoModal.drawerModal .learners .userImg .inlineList li .value {
    margin-left: 10px;
  }
}

#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span {
  font-size: 12px;
}

#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span::after {
  content: "|";
  color: rgba(0, 0, 0, 0.08);
  padding-left: 5px;
  margin-right: 5px;
}

#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span:last-child::after {
  display: none;
}

#app .yunoModal.drawerModal .learners.larger .userImg img {
  width: 96px;
  height: 96px;
  border-radius: 50%;
}

#app .yunoModal.drawerModal .classDate {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .intro .introLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}

#app .yunoModal.drawerModal .intro .primaryTitle {
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .intro .secondaryTitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .stats {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .stats li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
}

#app .yunoModal.drawerModal .stats li .itemValue {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoModal.drawerModal .stats li .itemLabel {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
}

#app .yunoModal.drawerModal .classDetail {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 15px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: 15px;
  background: rgba(0, 0, 0, 0.02);
}

#app .yunoModal.drawerModal .classDetail .loaderWrapper {
  height: 24px;
}

#app .yunoModal.drawerModal .classDetail .smallLoader {
  font-size: 4px;
  margin: 0 auto;
}

#app .yunoModal.drawerModal .classDetail .inform {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: #A81E22;
}

#app .yunoModal.drawerModal .classDetail .classDateV2 {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}

#app .yunoModal.drawerModal .classDetail .classTitle {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .classDetail .classInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoModal.drawerModal .classDetail .classInfo li {
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  padding-right: 15px;
  margin-right: 15px;
}

#app .yunoModal.drawerModal .classDetail .classInfo li:last-child {
  border-right: 0;
}

#app .yunoModal.drawerModal .classDetail .classInfo figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoModal.drawerModal .classDetail .classInfo img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0;
  margin-right: 10px;
}

#app .yunoModal.drawerModal .classDetail .classInfo .itemLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}

#app .yunoModal.drawerModal .classDetail .classInfo .itemValue {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
}

#app .yunoModal.drawerModal .classDetail .barWrapper {
  margin-top: 15px;
}

#app .yunoModal.drawerModal .classDetail .barWrapper .barHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .yunoModal.drawerModal .classDetail .barWrapper .barValue {
  font-size: 12px;
  line-height: 16px;
}

#app .yunoModal.drawerModal .classes {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 15px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

#app .yunoModal.drawerModal .classes .classesLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}

#app .yunoModal.drawerModal .classes .days {
  margin: 5px -12px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoModal.drawerModal .classes .days li {
  padding: 0 12px;
  margin-top: 10px;
}

#app .yunoModal.drawerModal .classes .days li .disc {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.08);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
}

#app .yunoModal.drawerModal .classes .days li.active .disc {
  background-color: rgba(37, 211, 102, 0.37);
  color: white;
}

#app .yunoModal.drawerModal .classes .days li.active .disc:hover {
  background-color: rgba(37, 211, 102, 0.74);
}

#app .yunoModal.drawerModal .classes .days li.active .disc:active {
  background-color: #25D366;
}

#app .yunoModal.drawerModal .classes .days li.active.isClicked .disc {
  background-color: #25D366;
}

#app .yunoModal.drawerModal .classes .days li.disable .disc:hover {
  background-color: rgba(0, 0, 0, 0.38);
  color: white;
}

#app .yunoModal.drawerModal .classes .days li.disable .disc:active {
  background-color: rgba(0, 0, 0, 0.87);
  color: white;
}

#app .yunoModal.drawerModal .classes .days li.disable.isClicked .disc {
  background-color: rgba(0, 0, 0, 0.87);
  color: white;
}

#app .yunoModal.drawerModal .yunoTable {
  padding: 0;
  border-radius: 4px;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

#app .yunoModal.drawerModal .yunoTable .b-table .table-wrapper {
  overflow-y: auto;
  margin-bottom: 15px;
}

#app .yunoModal.drawerModal .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover {
  background-color: #FFF;
}

#app .yunoModal.drawerModal .yunoTable .b-table .level {
  display: none;
}

@media (min-width: 768px) {
  #app .yunoModal.drawerModal .yunoTable .b-table thead tr th {
    border-bottom: 1px solid #E6E6E6;
  }
}

@media (min-width: 768px) {
  #app .yunoModal.drawerModal .yunoTable .b-table tbody tr td {
    border-top: 0;
  }
}

#app .yunoModal.drawerModal .yunoTable .b-table tbody tr.is-empty td {
  border-bottom: 0;
}

#app .yunoModal.drawerModal .yunoTable .b-table tbody tr:hover {
  background-color: #FFF;
}

#app .yunoModal.drawerModal .filterNav > li > a {
  font-size: 16px;
  display: block;
  padding: 15px;
  border-bottom: 1px solid;
  border-color: rgba(0, 0, 0, 0.08);
  font-weight: 500;
  line-height: 24px;
}

#app .yunoModal.drawerModal .filterNav > li > a:hover {
  text-decoration: none;
}

#app .yunoModal.drawerModal .filterNav > li > a.is-active {
  background-color: rgba(0, 0, 0, 0.02);
}

#app .yunoModal.drawerModal .coursesMain {
  margin-bottom: 30px;
}

#app .yunoModal.drawerModal .coursesMain.hideMe {
  display: none;
}

#app .yunoModal .availabilityWrapper .ctaWrapper {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

#app .yunoModal .hasScroll {
  min-height: 280px;
}

#app .filtersWrapper {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 9;
  background-color: #FFF;
}

#app .filtersWrapper .filterHeader {
  display: none;
}

#app .filtersWrapper.mobileView {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0 15px 15px;
}

#app .filtersWrapper.mobileView .filterHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .filters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
  padding: 0;
  background-color: #FFFFFF;
}

#app .filters.categoryFilter {
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  #app .filters.categoryFilter {
    margin-bottom: 0;
  }
}

#app .filters.categoryFilter.mobileView {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .filters.otherFilters {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .filters.otherFilters.mobileView {
  display: block;
}

#app .filters.noBtmGap {
  padding-bottom: 0;
}

#app .filters.noTopGap {
  padding-top: 0;
}

#app .filters .yunoAutocompleteSearch {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}

@media (min-width: 768px) {
  #app .filters .yunoAutocompleteSearch {
    display: block;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 250px;
            flex: 0 0 250px;
    margin: 15px 0 0;
  }
}

#app .filters .yunoAutocompleteSearch .field .control input[type="text"] {
  border-color: #E6E6E6;
  font-weight: 500;
  font-size: 14px;
  height: 43px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .mdi {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  content: "\e5c9";
  color: #A81E22;
}

#app .filters .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}

#app .filters .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 43px);
          flex: 0 0 calc(100% - 43px);
}

@media (min-width: 768px) {
  #app .filters .yunoDropdown:first-child {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

@media (min-width: 768px) {
  #app .filters .yunoDropdown {
    display: block;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin: 15px 0 0;
  }
}

#app .filters.mobileView .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  display: block;
  margin-bottom: 15px;
}

#app .filters.mobileView .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .filters.mobileView .filterTrigger {
  display: none;
}

#app .filters .filterTrigger {
  display: block;
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
  padding-right: 15px;
}

#app .filters .filterTrigger .material-icons, #app .filters .filterTrigger .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .filterTrigger .mdi::after, #app .filters .filterTrigger .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before, #app .filters .filterTrigger .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before {
  font-size: 28px;
}

@media (min-width: 768px) {
  #app .filters .filterTrigger {
    display: none;
  }
}

#app .yunoDropdown {
  min-width: 0;
}

#app .yunoDropdown.slider .dropdown-menu {
  padding: 15px;
}

#app .yunoDropdown.slider .b-slider.is-primary .b-slider-fill {
  background: #A81E22 !important;
}

#app .yunoDropdown.slider .b-tooltip.is-primary .tooltip-content {
  background: #A81E22 !important;
}

#app .yunoDropdown .timesDays {
  padding-bottom: 10px;
}

#app .yunoDropdown .timesDays .wrapper {
  padding: 10px 15px 0;
}

#app .yunoDropdown .timesDays ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 10px -5px 0;
}

#app .yunoDropdown .timesDays ul li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  padding: 0 5px 5px;
}

#app .yunoDropdown .timesDays ul li .inner {
  text-align: center;
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  border-radius: 4px;
  border: 1px solid #E6E6E6;
  padding: 5px;
  cursor: pointer;
}

#app .yunoDropdown .timesDays ul li .inner:hover {
  border-color: #A81E22;
  color: #A81E22;
}

#app .yunoDropdown .timesDays ul li .material-icons, #app .yunoDropdown .timesDays ul li .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoDropdown .timesDays ul li .mdi::after, #app .yunoDropdown.multiSelect .timesDays ul li .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .timesDays ul li .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .timesDays ul li .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .timesDays ul li .dropdown-item.is-active::before {
  display: block;
  margin-bottom: 5px;
}

#app .yunoDropdown .timesDays ul li.active .inner {
  background-color: #A81E22;
  color: white;
}

#app .yunoDropdown .dropdown {
  width: 100%;
}

#app .yunoDropdown .dropdown-trigger {
  width: 100%;
}

#app .yunoDropdown.availability .dropdown-menu {
  width: 300px;
  left: auto;
  right: 0;
}

#app .yunoDropdown .dropdown-menu {
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
}

#app .yunoDropdown .dropdown-menu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
}

#app .yunoDropdown .dropdown-menu a {
  padding: 5px 15px;
}

#app .yunoDropdown .dropdown-menu a.dropdown-item.is-active {
  background-color: #A81E22;
  color: white;
}

#app .yunoDropdown .labelWrapper {
  padding: 10px 35px 10px 15px;
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}

#app .yunoDropdown .labelWrapper .icon {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}

#app .yunoDropdown .labelWrapper .placeHolder {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
}

#app .yunoDropdown .labelWrapper .clearFilter {
  position: absolute;
  right: 35px;
  bottom: 2px;
}

#app .yunoDropdown .labelWrapper .selectedItem {
  font-weight: 400;
  padding-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  display: block;
  padding-right: 27px;
  font-size: 14px;
}

#app .yunoDropdown .labelWrapper .selectedItem span {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem span:last-child::after {
  display: none;
}

#app .yunoDropdown .labelWrapper .selectedItem span::after {
  content: ",";
  position: relative;
  left: -2px;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-right: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  padding-right: 27px;
  margin-right: 5px;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item:last-child {
  margin-right: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item span::after {
  display: none;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .clearFilter {
  position: absolute;
  right: 0;
  top: -2px;
}

#app .yunoDropdown.iconOnly .labelWrapper {
  border: 0;
  padding: 0 5px 0;
}

#app .yunoDropdown.iconOnly .labelWrapper .icon {
  top: calc(50% - 14px);
}

#app .yunoDropdown.multiSelect .dropdown-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

#app .yunoDropdown.multiSelect .dropdown-menu .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}

#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
}

#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 6px;
  font-size: 18px;
}

#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active {
  background-color: #FFF;
}

#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}

#app .yunoDropdown.hideColumn .dropdown-menu {
  left: auto;
  right: 0;
}

#app .yunoDropdown.hideColumn .dropdown-menu .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 6px;
  font-size: 18px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active {
  background-color: #FFF;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}
/*# sourceMappingURL=tableGridV2.css.map */