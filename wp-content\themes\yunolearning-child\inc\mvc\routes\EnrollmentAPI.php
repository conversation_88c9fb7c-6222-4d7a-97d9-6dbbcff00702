<?php
//Enrollment Controller API's
return [
    "/enrollments" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "POST" => ["callback" => "addEnrollment", "args" => [], "auth" => false]
        ]
    ],
    "/enrollment/webhook" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "handleRazorpayWebhook", "args" => [], "auth" => true]
        ]
    ],
    "/enrollments/(?P<viewType>list|grid)" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "getEnrollments", "args" => [], "auth" => true]
        ]
    ],
    "/enrollments/(?P<enrollmentId>\d+)" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "PUT" => ["callback" => "updateEnrollment", "args" => [], "auth" => true]
        ]
    ],
    "/enrollments/filters" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "getEnrollmentsFilters", "args" => [], "auth" => true]
        ]
        ],
    "/enrollments/(?P<enrollmentId>\d+)/unenroll" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "PUT" => ["callback" => "updEnrollmentUnenroll", "args" => [], "auth" => false]
        ]
    ],
    "/enrollments/active/(?P<entityType>[a-zA-Z_-]+)/(?P<entityId>\d+)" => [
        "controller" => "EnrollmentController",
        "methods" => [
            "GET" => ["callback" => "getActiveEnrollmentsByEntity", "args" => [], "auth" => false]
        ]
    ]
];
