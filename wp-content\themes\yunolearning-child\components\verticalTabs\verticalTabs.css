.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .verticalTabs .carousel .carousel-arrow .icon::after {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .verticalTabs, #app .verticalTabs .largerTitle, #app .verticalTabs .b-tabs .tabs a {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .verticalTabs .smallerBody, #app .verticalTabs .b-tabs .tabs .secondaryLabel {
  color: rgba(0, 0, 0, 0.6);
}

#app .verticalTabs {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .verticalTabs {
    padding: 100px 0;
  }
}

@media (min-width: 768px) {
  #app .verticalTabs.topSpace {
    padding: 100px 0 0 0;
  }
}

#app .verticalTabs .alignC {
  text-align: center;
}

#app .verticalTabs .largestTitle {
  font-size: 32px;
  line-height: 40px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .verticalTabs .largerTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .verticalTabs .largeTitle {
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .verticalTabs .largeTitle {
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    margin-bottom: 0;
  }
}

#app .verticalTabs .smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .verticalTabs .smallerBody {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .verticalTabs .b-tabs {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

#app .verticalTabs .b-tabs.is-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (min-width: 768px) {
  #app .verticalTabs .b-tabs.is-vertical {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

#app .verticalTabs .b-tabs .tabs {
  padding-top: 30px;
  display: block;
  white-space: normal;
  width: 100%;
}

@media (min-width: 768px) {
  #app .verticalTabs .b-tabs .tabs {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 440px;
  }
}

#app .verticalTabs .b-tabs .tabs a {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: block;
  padding: 24px;
  border-left: 1px solid transparent;
  border-top: 1px solid transparent;
}

#app .verticalTabs .b-tabs .tabs .primaryLabel, #app .verticalTabs .b-tabs .tabs .secondaryLabel {
  display: block;
}

#app .verticalTabs .b-tabs .tabs .primaryLabel {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .verticalTabs .b-tabs .tabs .secondaryLabel {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 0.4px;
}

#app .verticalTabs .b-tabs .tabs ul {
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

#app .verticalTabs .b-tabs .tabs li.is-active a {
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px 0px 0px 4px;
  background-color: #FFF;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
}

#app .verticalTabs .b-tabs .tabs li.is-active a::after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #FFF;
  position: absolute;
  right: -1px;
  top: 0;
}

#app .verticalTabs .b-tabs .tab-content {
  padding: 30px;
}

@media (min-width: 768px) {
  #app .verticalTabs .b-tabs .tab-content {
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content;
  }
}

#app .verticalTabs .b-tabs .tab-content .tab-item {
  height: 100%;
}

#app .verticalTabs .carousel {
  padding-bottom: 45px;
}

#app .verticalTabs .carousel .carousel-item {
  float: none;
  display: block;
  margin: 0;
}

#app .verticalTabs .carousel .carousel-item figure .itemInfo {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .verticalTabs .carousel .carousel-item figure .itemInfo {
    min-height: 72px;
  }
}

#app .verticalTabs .carousel .carousel-item figure img {
  width: 100%;
  height: auto;
}

@media (min-width: 768px) {
  #app .verticalTabs .carousel .carousel-item figure img {
    height: 343px;
  }
}

#app .verticalTabs .carousel .carousel-arrow .icon {
  background-color: #D9D9D9;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.15);
}

#app .verticalTabs .carousel .carousel-arrow .icon::after {
  content: "\e5cb";
}

#app .verticalTabs .carousel .carousel-arrow .icon.has-icons-right::after {
  content: "\e5cc";
}

#app .verticalTabs .carousel .carousel-indicator .indicator-item .indicator-style {
  border-color: #D9D9D9;
  background-color: #D9D9D9;
}

#app .verticalTabs .carousel .carousel-indicator .indicator-item.is-active .indicator-style {
  background-color: rgba(0, 0, 0, 0.87);
}
/*# sourceMappingURL=verticalTabs.css.map */