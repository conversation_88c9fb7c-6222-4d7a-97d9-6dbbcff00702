<?php

namespace V4;

class EnrollmentMigrationController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('validate');
        $this->loadLibary('elasticSearch', 'es');
    }
    public function migrateEnrollmentDataInChunks($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log";
            global $wpdb;
            $customTable = $wpdb->prefix . 'enrollment';  // Replace with your actual table name
            $limit = (int)$request['limit'];  // Default limit is 100
            $offset = (int)$request['offset']; // Default offset is 0

            // Fetch a chunk of records where 'migrated' is 'no'
            $results = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT * FROM $customTable WHERE migrated = %s LIMIT %d OFFSET %d",
                    'no',
                    $limit,
                    $offset
                ),
                ARRAY_A
            );

            if (empty($results)) {
                return [
                    'status'  => 'completed',
                    'message' => 'No more records to migrate.',
                ];
            }

            foreach ($results as $row) {
                $postId = wp_insert_post([
                    'post_title'    => 'Enrollment for User ID ' . $row['user_id'],
                    'post_type'     => 'enrollment',
                    'post_status'   => 'publish',
                ]);

                if (is_wp_error($postId)) {
                    // throw new \Exception('Failed to insert post: ' . $postId->get_error_message());
                    return $this->response->error('GET_FAIL', ['message' => 'Failed to insert post: ' . $postId->get_error_message()]);
                }

                // Add post meta data
                $this->addEnrollmentMeta($postId, $row);
                error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollmentES: " . json_encode($row) . "\n\n", 3, $logFile);

                // Fetch Payment Records for the specific enrollment
                $payments = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT * FROM {$wpdb->prefix}payment_status 
                 WHERE user_id = %d 
                 AND course_id = %d 
                 AND batch_id = %d",
                        $row['user_id'],
                        $row['product_db_id'], // course_id
                        $row['batch_id']
                    ),
                    ARRAY_A
                );

                foreach ($payments as $payment) {
                    // Determine Payment Type
                    $paymentType = $this->getPaymentType($payment);

                    // Create CPT for Payment
                    $paymentPostId = wp_insert_post([
                        'post_title'  => 'Payment for Enrollment ID ' . $row['id'],
                        'post_type'   => 'payment',
                        'post_status' => 'publish',
                    ]);

                    if (!$paymentPostId) {
                        continue;
                    }

                    // Add Payment Meta
                    $this->addPaymentMeta($paymentPostId, $payment, $row['id'], $paymentType);

                    // Fetch Payment Links (If Exists)
                    $paymentLinks = $wpdb->get_results(
                        $wpdb->prepare(
                            "SELECT * FROM {$wpdb->prefix}payment_links
                     WHERE wp_payment_status_id = %d",
                            $payment['id']
                        ),
                        ARRAY_A
                    );

                    foreach ($paymentLinks as $link) {
                        update_post_meta($paymentPostId, 'razorpay_invoice_id', $link['razerpay_invoice_id']);
                        update_post_meta($paymentPostId, 'receipt_id', $link['receipt_id']);
                        update_post_meta($paymentPostId, 'payment_link', $link['payment_link']);
                        update_post_meta($paymentPostId, 'link_status', $link['link_status']);
                        update_post_meta($paymentPostId, 'link_expiry', $link['link_expiry']);
                    }
                }

                // Mark the record as migrated
                $wpdb->update(
                    $customTable,
                    ['migrated' => 'yes'],
                    ['id' => $row['id']],
                    ['%s'],
                    ['%d']
                );
            }

            // return [
            //     'status'  => 'success',
            //     'message' => sprintf('Migrated %d records starting from offset %d.', count($results), $offset),
            //     'next_offset' => $offset + $limit
            // ];
            return $this->response->success('GET_SUCESS', ['message' => sprintf('Migrated %d records starting from offset %d.', count($results), $offset)]);
        } catch (\Exception $e) {
            //return new WP_Error('migrate_enrollment_error', $e->getMessage(), ['status' => 500]);
            return $this->response->error('GET_FAIL', ['message' => 'migrate_enrollment_error']);
        }
    }

    /**
     * Determine the payment type based on payment_mode, payment_gateway, and total_instalments.
     *
     * @param array $payment Payment record from wp_payment_status
     * @return string Payment type classification
     */
    private function getPaymentType($payment)
    {
        if ($payment['payment_mode'] === 'offline') {
            return 'Offline Payment (Admin)';
        }

        if ($payment['payment_mode'] === 'direct_payment' && strtolower($payment['payment_gateway']) === 'razorpay') {
            return 'Online Full Payment (User)';
        }

        if ($payment['payment_mode'] === 'generate_link') {
            if ($payment['total_instalments'] == 0) {
                return 'Admin Generated Link (Full Payment)';
            } elseif ($payment['total_instalments'] > 0) {
                return 'Admin Generated Link (Installments)';
            }
        }

        return 'Unknown Payment Type';
    }

    /**
     * Add meta fields to the custom post type.
     *
     * @param int   $postId
     * @param array $row
     * @return void
     */
    private function addEnrollmentMeta($postId, $row)
    {
        update_post_meta($postId, 'enrollment_id', $row['id']);
        update_post_meta($postId, 'user_id', $row['user_id']);
        update_post_meta($postId, 'course_type', $row['product_type']);
        update_post_meta($postId, 'product_zoho_id', $row['product_zoho_id']);
        update_post_meta($postId, 'user_deal_status', $row['user_deal_status']);
        update_post_meta($postId, 'payment_status', $row['payment_status']);
        update_post_meta($postId, 'instructor_id', $row['instructor_id']);
        update_post_meta($postId, 'batch_id', $row['batch_id']);
        update_post_meta($postId, 'course_id', $row['product_db_id']);
        update_post_meta($postId, 'deal_zoho_id', $row['deal_zoho_id']);
        update_post_meta($postId, 'org_id', $row['org_id']);
        update_post_meta($postId, 'payment_id', $row['payment_id']);
        update_post_meta($postId, 'enroll_time', $row['enroll_time']);
        update_post_meta($postId, 'enrollment_status', $row['enrollment_status']);
        update_post_meta($postId, 'enrollment_duration', $row['enrollment_duration']);
        update_post_meta($postId, 'enrollment_start_date', $row['enrollment_start_date']);
        update_post_meta($postId, 'enrollment_end_date', $row['enrollment_end_date']);

        // New Objects
        update_post_meta($postId, 'enrolled_by', $row['user_id']);

        // Fetch Listing Price
        $this->loadModel('course');
        $this->loadModel('enrollment');
        $economics = $this->courseModel->getCourse($row['product_db_id'])['economics'] ?? null;
        $listingPrice = $this->enrollmentModel->getListingPrice($economics);
        update_post_meta($postId, 'listing_price', $listingPrice);

        // Fetch Selling Price
        $query = [
            'cntryCode' => 'IN',
            'basePrice' => $row['amount'] ?? 0,
            'taxPer' => 18.0
        ];
        $sellingPrice = $this->enrollmentModel->getSellingPrice($query);
        update_post_meta($postId, 'selling_price', $sellingPrice);
    }

    /**
     * Add meta data to the payment post.
     *
     * @param int   $postId
     * @param array $payment
     * @param int   $enrollmentId
     * @param string $paymentType
     */
    private function addPaymentMeta($postId, $payment, $enrollmentId, $paymentType)
    {
        update_post_meta($postId, 'payment_id', $payment['id']);
        update_post_meta($postId, 'enrollment_id', $enrollmentId);
        update_post_meta($postId, 'user_id', $payment['user_id']);
        update_post_meta($postId, 'batch_id', $payment['batch_id']);
        update_post_meta($postId, 'course_id', $payment['course_id']);
        update_post_meta($postId, 'order_id', $payment['order_id']);
        update_post_meta($postId, 'amount', $payment['amount']);
        update_post_meta($postId, 'amount_paid', $payment['amount_paid']);
        update_post_meta($postId, 'amount_due', $payment['amount_due']);
        update_post_meta($postId, 'total_instalments', $payment['total_instalments']);
        update_post_meta($postId, 'paid_instalments', $payment['paid_instalments']);
        update_post_meta($postId, 'currency', $payment['currency']);
        update_post_meta($postId, 'payment_mode', $payment['payment_mode']);
        update_post_meta($postId, 'payment_status', $payment['payment_status']);
        update_post_meta($postId, 'payment_gateway', $payment['payment_gateway']);
        update_post_meta($postId, 'payment_type', $paymentType);
    }

    ////////////////////////lasttttttttttttttttttttttttttt
    /**
     * Migrate and update enrollment data in Elasticsearch.
     *
     * @param array $request
     * @return WP_REST_Response|WP_Error
     */
    public function migrateAndUpdateBatchEnrollmentEvent($request)
    {
        global $wpdb;
        $curl = curl_init();
        $limit = (int) $request['limit'];
        $offset = (int) $request['offset'];

        // Fetch existing enrollments from Elasticsearch
        $existingEnrollments = $this->fetchExistingEnrollmentsFromElasticsearch($curl, $limit, $offset);

        if (empty($existingEnrollments)) {
            return $this->response->error('GET_FAIL', ['message' => 'No more records to migrate.']);
        }

        // Store only updated records
        $updatedEnrollments = [];
        // Process each enrollment
        foreach ($existingEnrollments as $esRecord) {

            // Log the response
            error_log("migrateEnrollmentDataInChunks - esRecord  in loop"  . date("Y-m-d H:i:s") . " === enrollmentES: " . json_encode($esRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            $esRecordId = $esRecord['_id'];
            error_log("migrateEnrollmentDataInChunks - esRecordId " . date("Y-m-d H:i:s") . " === enrollmentES: " . json_encode($esRecordId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            $updatedRecord =  $this->updateOrInsertEnrollmentRecord($esRecord, $curl, $esRecordId);
            error_log("migrateEnrollmentDataInChunks - updatedRecord  response" . date("Y-m-d H:i:s") . " === updatedRecord: " . json_encode($updatedRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            if ($updatedRecord) {
                $updatedEnrollments[] = $updatedRecord;
                error_log("migrateEnrollmentDataInChunks - updatedRecord ifff " . date("Y-m-d H:i:s") . " === updatedRecord: " . json_encode($updatedRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            }
        }
        curl_close($curl);
        return $this->response->success('GET_SUCCESS', $updatedEnrollments, ['message' => 'Enrollment data successfully updated in Elasticsearch.']);
    }

    /**
     * Fetch existing enrollment data from Elasticsearch.
     *
     * @param resource $curl
     * @param int      $limit
     * @param int      $offset
     * @return array
     */
    private function fetchExistingEnrollmentsFromElasticsearch($curl, $limit, $offset)
    {
        curl_setopt_array($curl, [
            CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search?from={$offset}&size={$limit}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER     => [
                "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "Content-Type: application/json"
            ],
            CURLOPT_CUSTOMREQUEST  => "GET",
        ]);

        $response = curl_exec($curl);
        $error = curl_error($curl);

        if ($error) {
            throw new \Exception("Elasticsearch fetch error: {$error}");
        }

        $data = json_decode($response, true);
        return $data['hits']['hits'] ?? [];
    }

    /**
     * Update or insert enrollment records in Elasticsearch.
     *
     * @param array   $esRecord
     * @param resource $curl
     * @param \wpdb   $wpdb
     */
    private function updateOrInsertEnrollmentRecord($esRecord, $curl, $esRecordId)
    {
        // Initialize WordPress database object
        error_log("updateOrInsertEnrollmentRecord - request " . date("Y-m-d H:i:s") . " === esRecord: " . json_encode($esRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        error_log("updateOrInsertEnrollmentRecord - request " . date("Y-m-d H:i:s") . " === esRecordId: " . json_encode($esRecordId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        global $wpdb;
        $courseId = $esRecord['_source']['data']['details']['course_id'] ?? null;
        $batchId = $esRecord['_source']['data']['details']['batch_id'] ?? null;
        $userId = $esRecord['_source']['data']['details']['user_id'] ?? null;

        if (!$esRecordId) {
            return;
        }
        error_log("updateOrInsertEnrollmentRecord - esRecordId after return " . date("Y-m-d H:i:s") . " === esRecordId: " . json_encode($esRecordId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if ($esRecordId) {
            // Fetch pricing details
            $pricingDetails = $this->getPricingDetails($courseId, $batchId, $userId);
            error_log("updateOrInsertEnrollmentRecord  " . date("Y-m-d H:i:s") . " === pricingDetails: " . json_encode($pricingDetails) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Fetch enrollment record from MySQL
            $enrollment = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}enrollment WHERE product_db_id = %d AND batch_id = %d AND user_id = %d",
                    $courseId,
                    $batchId,
                    $userId
                ),
                ARRAY_A
            );
            error_log("updateOrInsertEnrollmentRecord  " . date("Y-m-d H:i:s") . " === enrollment: " . json_encode($enrollment) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Format data for Elasticsearch
            $formattedData = $this->formatEnrollmentData($enrollment, $pricingDetails);
            error_log("updateOrInsertEnrollmentRecord " . date("Y-m-d H:i:s") . " === formattedData: " . json_encode($formattedData) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Check if record exists in Elasticsearch
            $esResponse = $this->getElasticsearchRecord($curl, $esRecordId);
            error_log("updateOrInsertEnrollmentRecord - " . date("Y-m-d H:i:s") . " === esResponse: " . json_encode($esResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            if ($esResponse && isset($esResponse['found']) && $esResponse['found']) {
                $updESRecord = $this->updateElasticsearchRecord($curl, $esRecordId, $formattedData);
                $enrollmentDataResponse = $this->es->read('batchenrollmentevent', $esRecordId);
                error_log("updateOrInsertEnrollmentRecord - " . date("Y-m-d H:i:s") . " === enrollmentDataResponse: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
                error_log("updateOrInsertEnrollmentRecord - " . date("Y-m-d H:i:s") . " === updESRecord: " . json_encode($updESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
                return $enrollmentDataResponse;
            } else {
                $insESRecord = $this->insertElasticsearchRecord($curl, $esRecordId, $formattedData);
                error_log("updateOrInsertEnrollmentRecord - row " . date("Y-m-d H:i:s") . " === insESRecord: " . json_encode($insESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
                $enrollmentDataResponse = $this->es->read('batchenrollmentevent', $esRecordId);
                error_log("updateOrInsertEnrollmentRecord - row " . date("Y-m-d H:i:s") . " === enrollmentDataResponse: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
                return $enrollmentDataResponse;
            }

            error_log("updateOrInsertEnrollmentRecord - ennnnddddddddd " . date("Y-m-d H:i:s") . " === Marked as migrated: " . json_encode($esRecordId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        }
    }
    private function updateOrInsertEnrollmentRecordOLDN($esRecord, $curl)
    {
        // Initialize WordPress database object
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === esRecord: " . json_encode($esRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        global $wpdb;
        $enrollmentId = $esRecord['_source']['data']['details']['enrollment_id'] ?? null;
        $courseId = $esRecord['_source']['data']['details']['course_id'] ?? null;
        $batchId = $esRecord['_source']['data']['details']['batch_id'] ?? null;
        $userId = $esRecord['_source']['data']['details']['user_id'] ?? null;

        // Check if enrollment ID is available
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollmentId: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if (!$enrollmentId) {
            return;
        }

        // Initialize conditions for the WHERE clause
        $whereConditions = ["id = %d"]; // Always include enrollmentId in the query
        $bindings = [$enrollmentId]; // Bind enrollmentId

        // Add additional conditions only if the values are not null
        if ($courseId !== null) {
            $whereConditions[] = "product_db_id = %d";
            $bindings[] = $courseId;
        }

        if ($batchId !== null) {
            $whereConditions[] = "batch_id = %d";
            $bindings[] = $batchId;
        }

        if ($userId !== null) {
            $whereConditions[] = "user_id = %d";
            $bindings[] = $userId;
        }

        // Build the final query with dynamic conditions
        $query = "SELECT * FROM {$wpdb->prefix}enrollment WHERE " . implode(' AND ', $whereConditions);
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === query: " . json_encode($query) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        $preparedQuery = $wpdb->prepare($query, ...$bindings);

        error_log("migrateEnrollmentDataInChunks - preparedQuery " . date("Y-m-d H:i:s") . " === query: " . $preparedQuery . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        // Fetch the enrollment record from MySQL with dynamic conditions
        $enrollment = $wpdb->get_row(
            $wpdb->prepare($query, ...$bindings),
            ARRAY_A
        );

        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollment: " . json_encode($enrollment) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if (!$enrollment) {
            return;
        }

        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollment: " . json_encode($enrollment) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if ($enrollment) {
            // Fetch pricing details
            $pricingDetails = $this->getPricingDetails($courseId, $batchId, $userId);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === pricingDetails: " . json_encode($pricingDetails) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Format data for Elasticsearch
            $formattedData = $this->formatEnrollmentData($enrollment, $pricingDetails);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === formattedData: " . json_encode($formattedData) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Check if record exists in Elasticsearch
            $esResponse = $this->getElasticsearchRecord($curl, $enrollmentId);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === esResponse: " . json_encode($esResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            if ($esResponse && isset($esResponse['found']) && $esResponse['found']) {
                $updESRecord = $this->updateElasticsearchRecord($curl, $esRecord['_id'], $formattedData);
                error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === updESRecord: " . json_encode($updESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            } else {
                $insESRecord = $this->insertElasticsearchRecord($curl, $enrollmentId, $formattedData);
                error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === insESRecord: " . json_encode($insESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            }

            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === Marked as migrated: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        }
    }
    private function updateOrInsertEnrollmentRecordOLD($esRecord, $curl)
    {
        // Initialize WordPress database object
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === esRecord: " . json_encode($esRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        global $wpdb;
        $enrollmentId = $esRecord['_source']['data']['details']['enrollment_id'] ?? null;

        // Check if enrollment ID is available
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollmentId: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if (!$enrollmentId) {
            return;
        }

        // Fetch enrollment record from MySQL
        $enrollment = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}enrollment WHERE id = %d", $enrollmentId),
            ARRAY_A
        );
        error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === enrollment: " . json_encode($enrollment) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        if ($enrollment) {
            // Fetch pricing details
            $pricingDetails = $this->getPricingDetails($enrollmentId);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === pricingDetails: " . json_encode($pricingDetails) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Format data for Elasticsearch
            $formattedData = $this->formatEnrollmentData($enrollment, $pricingDetails);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === formattedData: " . json_encode($formattedData) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            // Check if record exists in Elasticsearch
            $esResponse = $this->getElasticsearchRecord($curl, $enrollmentId);
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === esResponse: " . json_encode($esResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

            if ($esResponse && isset($esResponse['found']) && $esResponse['found']) {
                $updESRecord = $this->updateElasticsearchRecord($curl, $esRecord['_id'], $formattedData);
                error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === updESRecord: " . json_encode($updESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            } else {
                $insESRecord = $this->insertElasticsearchRecord($curl, $enrollmentId, $formattedData);
                error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === insESRecord: " . json_encode($insESRecord) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            }

            // Mark as migrated in MySQL
            $wpdb->update(
                "{$wpdb->prefix}enrollment",
                ['migrated' => 'yes'],
                ['id' => $enrollmentId],
                ['%s'],
                ['%d']
            );
            error_log("migrateEnrollmentDataInChunks - row " . date("Y-m-d H:i:s") . " === Marked as migrated: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        }
    }

    /**
     * Get pricing details (listing price and selling price).
     *
     * @param int  $enrollmentId
     * @param \wpdb $wpdb
     * @return array
     */
    private function getPricingDetails($courseId, $batchId, $userId)
    {
        global $wpdb;
        // Check if courseId, batchId, and userId are provided
        error_log("getPricingDetails - courseId " . date("Y-m-d H:i:s") . " === courseId: " . json_encode($courseId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        error_log("getPricingDetails - batchId " . date("Y-m-d H:i:s") . " === batchId: " . json_encode($batchId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        error_log("getPricingDetails - userId " . date("Y-m-d H:i:s") . " === userId: " . json_encode($userId) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        if (empty($courseId) || empty($batchId) || empty($userId)) {
            return [
                'listing_price' => [],
                'selling_price' => [],
                'full_part'     => [
                    'type' => '',
                    'total_installments' => 0
                ]
            ];
        }
        // Prepare the SQL query to fetch payment status details
        $rawQuery = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}payment_status WHERE course_id = %d AND batch_id = %d AND user_id = %d",
            $courseId,
            $batchId,
            $userId
        );

        error_log("getPricingDetails - QUERY: " . date("Y-m-d H:i:s") . " === SQL: " . $rawQuery . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");

        // Fetch payment status details with course_id, batch_id, and user_id
        $paymentStatus = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}payment_status WHERE course_id = %d AND batch_id = %d AND user_id = %d",
                $courseId,
                $batchId,
                $userId
            ),
            ARRAY_A
        );
        // Check if payment status is available
        error_log("getPricingDetails - paymentStatus " . date("Y-m-d H:i:s") . " === paymentStatus: " . json_encode($paymentStatus) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");



        if (!$paymentStatus) {
            return [
                'listing_price' => [],
                'selling_price' => [],
                'full_part' => [
                    'type' => '',
                    'total_installments' => 0
                ]
            ];
        }

        // Fetch payment link details (if available)
        $paymentLink = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}payment_links WHERE wp_payment_status_id = %d", $paymentStatus['id']),
            ARRAY_A
        );

        $amount = (float) $paymentStatus['amount'];
        $amountPaid = (float) $paymentStatus['amount_paid'];

        // Discount Calculation
        $discountAmount = $amount - $amountPaid;
        $discountPercentage = $amount > 0 ? ($discountAmount / $amount) * 100 : 0;

        // Extract payment details for full_part logic
        $paymentGateway = strtolower($paymentStatus['payment_gateway'] ?? '');
        $paymentMode = strtolower($paymentStatus['payment_mode'] ?? '');
        $totalInstallments = (int) $paymentStatus['total_instalments'];
        //$paidInstallments = (int) $paymentStatus['paid_instalments'];

        $fullPart = [
            'type' => '',
            'total_installments' => $totalInstallments
        ];

        if (
            // Offline mode + offline gateway + no installments
            ($paymentMode === 'offline' && $paymentGateway === 'offline' && $totalInstallments === 0) ||

            // Online mode + razorpay/generate_link gateway + no installments
            ($paymentMode === 'online' && in_array($paymentGateway, ['razorpay', 'generate_link']) && $totalInstallments === 0) ||

            // Online mode + touchless + no installments
            ($paymentMode === 'online' && $paymentGateway === 'touchless' && $totalInstallments === 0)
        ) {
            $fullPart['type'] = 'FULLPAYMENT';
        } elseif (
            // Online mode + razorpay/generate_link + total_installments in 1-3
            $paymentMode === 'online' &&
            in_array($paymentGateway, ['razorpay', 'generate_link']) &&
            in_array($totalInstallments, [1, 2, 3])
        ) {
            $fullPart['type'] = 'ININSTALLMENTS';
        }


        $listingPrice = [
            'currency_code'  => $paymentStatus['currency'] ?? 'INR',
            'exclusive_tax'  => (float) round($amount, 2),
            'inclusive_tax'  => (float) round($amount * 1.18, 2),
            'tax'            => [
                'amount'     => (float) round($amount * 0.18, 2),
                'percentage' => 18,
                'type'       => 'GST'
            ]
        ];

        $sellingPrice = [
            'currency_code'  => $paymentStatus['currency'] ?? 'INR',
            'exclusive_tax'  => (float) round($amountPaid, 2),
            'inclusive_tax'  => (float) round($amountPaid * 1.18, 2),
            'tax'            => [
                'amount'     => (float) round($amountPaid * 0.18, 2),
                'percentage' => 18,
                'type'       => 'GST'
            ],
            'discount'       => [
                'amount'     => (float) round($discountAmount, 2),
                'percentage' => (float) round($discountPercentage, 2),
            ]
        ];

        return [
            'listing_price' => $listingPrice,
            'selling_price' => $sellingPrice,
            'full_part'     => $fullPart
        ];
    }

    /**
     * Format enrollment data for Elasticsearch.
     *
     * @param array $enrollment
     * @param array $pricingDetails
     * @return array
     */
    private function formatEnrollmentData($enrollment, $pricingDetails)
    {
        return [
            "data" => [
                "details" => [
                    "enrollment_id"     => $enrollment['id'],
                    "user_id"           => $enrollment['user_id'],
                    "course_id"         => $enrollment['product_db_id'],
                    "batch_id"          => $enrollment['batch_id'],
                    //"instructor_id"     => $enrollment['instructor_id'],
                    //"enrollment_status" => $enrollment['enrollment_status'],
                    //"enrollment_type"   => $enrollment['product_type'],
                    //"enrolled_on"       => $enrollment['enroll_time'],
                    //"enrolled_end"      => $enrollment['enrollment_end_date'],
                    "listing_price"     => $pricingDetails['listing_price'],
                    "selling_price"     => $pricingDetails['selling_price'],
                    "full_part"         => $pricingDetails['full_part'],
                ]
            ]
        ];
    }

    /**
     * Get an existing Elasticsearch record.
     */
    private function getElasticsearchRecord($curl, $esRecordId)
    {
        curl_setopt_array($curl, [
            CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_doc/{$esRecordId}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER     => [
                "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "Content-Type: application/json"
            ],
            CURLOPT_CUSTOMREQUEST  => "GET",
        ]);

        return json_decode(curl_exec($curl), true);
    }

    /**
     * Update an Elasticsearch record.
     */
    private function updateElasticsearchRecord($curl, $esRecordId, $enrollmentData)
    {
        $enrollmentDataResponse = $this->es->update('batchenrollmentevent', $esRecordId, $enrollmentData);
        error_log("updateElasticsearchRecord - callll " . date("Y-m-d H:i:s") . " === enrollmentDataResponse: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
        if ($enrollmentDataResponse['status_code'] == 200) {
            error_log("updateElasticsearchRecord - successs " . date("Y-m-d H:i:s") . " === enrollmentDataResponse: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            return true;
        } else {
            error_log("updateElasticsearchRecord - error " . date("Y-m-d H:i:s") . " === enrollmentDataResponse: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/migrateEnrollmentDataInChunks.log");
            return false;
        }
        // curl_setopt_array($curl, [
        //     CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_update/{$esRecordId}",
        //     CURLOPT_CUSTOMREQUEST  => "POST",
        //     CURLOPT_POSTFIELDS     => json_encode($enrollmentData),
        //     CURLOPT_HTTPHEADER     => [
        //         "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
        //         "Content-Type: application/json"
        //     ],
        // ]);

        // curl_exec($curl);
    }
    /**
     * Insert or Update Enrollment Record in Elasticsearch
     *
     * @param resource $curl cURL instance
     * @param int $enrollmentId Enrollment ID
     * @param array $formattedData Data to be inserted into Elasticsearch
     */
    private function insertElasticsearchRecord($curl, $esRecordId, $formattedData)
    {
        $esIndex = 'batchenrollmentevent';
        $esUrl = ELASTIC_SEARCH_END_URL . "/{$esIndex}/_doc/{$esRecordId}";

        // Check if the enrollment already exists in Elasticsearch
        curl_setopt_array($curl, [
            CURLOPT_URL => $esUrl,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $recordExists = ($httpCode === 200);

        // Convert data to JSON
        $jsonData = json_encode($formattedData);

        // Insert or Update Elasticsearch Record
        curl_setopt_array($curl, [
            CURLOPT_URL => $esUrl,
            CURLOPT_CUSTOMREQUEST => $recordExists ? "POST" : "PUT",
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $error = curl_error($curl);

        if ($error) {
            error_log("Elasticsearch Error for Enrollment ID {$esRecordId}: " . $error);
        } else {
            error_log("Elasticsearch Response for Enrollment ID {$esRecordId}: " . $response);
        }
    }
}
