Vue.component("yuno-section-scroller", {
  props: {
    data: {
      type: Array,
      required: true,
    },
  },
  template: `
    <section class="sectionScroller">
      <div class="container">
        <div class="row justify-content-center">
          <div class="sectionTabs col-md-8">
            <b-tabs
              type="is-toggle-rounded"
              class="d-flex justify-content-center"
              v-model="activeTab" 
              @input="onTabChange"
            >
              <b-tab-item
                v-for="(tab, index) in data"
                :key="index"
                :label="tab.label"
              >
              </b-tab-item>
            </b-tabs>
          </div>
        </div>
      </div>
    </section>
  `,
  data() {
    return {
      activeTab: 0,
      isScrolling: false, // flag to control scroll events
    };
  },
  mounted() {
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  created() {
    // Create a debounced version of handleScroll to reduce event frequency
    this.debouncedHandleScroll = this.debounce(this.handleScroll, 50);
  },
  methods: {
    onTabChange(newIndex) {
      this.activeTab = newIndex;
      this.scrollToSection(this.data[newIndex].targetClass);
    },
    scrollToSection(targetClass) {
      const element = document.querySelector(`.${targetClass}`);
      if (element) {
        const offset = 100;
        const top =
          element.getBoundingClientRect().top + window.scrollY - offset;
        // Indicate that a programmatic scroll is in progress
        this.isScrolling = true;
        window.scrollTo({
          top: top,
          behavior: "smooth",
        });
        // Reset the flag after an estimated time (adjust as needed)
        setTimeout(() => {
          this.isScrolling = false;
        }, 600);
      } else {
        console.warn(`Element with class '${targetClass}' not found.`);
      }
    },
    handleScroll() {
      // Ignore scroll events while a programmatic scroll is active
      if (this.isScrolling) return;
      const threshold = 150; // adjust threshold as needed
      let newActiveTab = 0;
      this.data.forEach((tab, index) => {
        const element = document.querySelector(`.${tab.targetClass}`);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= threshold) {
            newActiveTab = index;
          }
        }
      });
      if (newActiveTab !== this.activeTab) {
        this.activeTab = newActiveTab;
      }
    },
    // Debounce helper to limit how frequently a function is called
    debounce(func, wait) {
      let timeout;
      return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },
  },
});
