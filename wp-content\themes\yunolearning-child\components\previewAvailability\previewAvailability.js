Vue.component("yuno-preview-availability", {
  props: {
    data: {
      type: Object,
      required: true,
    },
    options: {
      type: Object,
      required: true,
    },
  },
  template: `
    <div class="yunoPreviewAvailability">
      <!-- Days header -->
      <div class="days-row">
        <div v-for="(week, index) in data.days" :key="'day-'+index" class="dayHeader caption1 bolder">
          {{ week.day }}
        </div>
      </div>
      <!-- Main grid container -->
      <div class="grid-container">
        <!-- Slots grid -->
        <div class="slots-grid">
          <template v-for="(time, timeIndex) in timeSlots">
            <div v-for="(day, dayIndex) in data.days" 
                 :key="'slot-'+dayIndex+'-'+timeIndex"
                 class="slot"
                 :class="{ 'available': isTimeSlotAvailable(day, time) }">
            </div>
          </template>
        </div>
        <!-- Time labels -->
        <div class="time-labels">
          <div v-for="(time, index) in timeSlots" 
               :key="'time-'+index" 
               class="time-label">
            {{ time }}
          </div>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      timeSlots: [
        "12:00 AM",
        "12:30 AM",
        "1:00 AM",
        "1:30 AM",
        "2:00 AM",
        "2:30 AM",
        "3:00 AM",
        "3:30 AM",
        "4:00 AM",
        "4:30 AM",
        "5:00 AM",
        "5:30 AM",
        "6:00 AM",
        "6:30 AM",
        "7:00 AM",
        "7:30 AM",
        "8:00 AM",
        "8:30 AM",
        "9:00 AM",
        "9:30 AM",
        "10:00 AM",
        "10:30 AM",
        "11:00 AM",
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "1:00 PM",
        "1:30 PM",
        "2:00 PM",
        "2:30 PM",
        "3:00 PM",
        "3:30 PM",
        "4:00 PM",
        "4:30 PM",
        "5:00 PM",
        "5:30 PM",
        "6:00 PM",
        "6:30 PM",
        "7:00 PM",
        "7:30 PM",
        "8:00 PM",
        "8:30 PM",
        "9:00 PM",
        "9:30 PM",
        "10:00 PM",
        "10:30 PM",
        "11:00 PM",
        "11:30 PM",
      ],
    };
  },
  methods: {
    convertTo24Hour(time) {
      const [hour, minute, period] = time
        .match(/(\d+):(\d+)\s*(AM|PM)/)
        .slice(1);
      let hours = parseInt(hour);
      if (period === "PM" && hours !== 12) hours += 12;
      if (period === "AM" && hours === 12) hours = 0;
      return `${hours.toString().padStart(2, "0")}:${minute}:00`;
    },
    timeToMinutes(time) {
      const [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },
    isTimeSlotAvailable(day, timeSlot) {
      if (!day.is_available || !day.time_slots || day.time_slots.length === 0) {
        return false;
      }

      const slotTime = this.convertTo24Hour(timeSlot);
      const slotMinutes = this.timeToMinutes(slotTime);

      return day.time_slots.some((slot) => {
        const startMinutes = this.timeToMinutes(slot.start);
        const endMinutes = this.timeToMinutes(slot.end);

        // Include the slot if it starts at or after the start time
        // and starts before or at the end time
        return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
      });
    },
  },
});
