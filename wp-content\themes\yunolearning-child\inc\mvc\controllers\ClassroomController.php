<?php

namespace V4;

/**
 *  Place Controller
 */


class ClassroomController extends Controller
{
    /**
     * Constructor to initialize the UserController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');

        $this->loadModel('place');
        $this->loadModel('classroom');
    }

    /**
     * Get user information
     *
     * @param WP_REST_Request $request The request object
     * @return void
     */
    public function addClassroom($request)
    {
        try {
            // Get request data
            $payload = json_decode($request->get_body(), true);

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            // Validate each field using the common functions
            $validation_checks = [
                'place_id' => 'integer', // Must be an integer
                'title' => '/^.{1,255}$/', // String with 1 to 255 characters
                'floor' => [
                    'type' => '/^(GROUND_PLUS|ONE_PLUS)$/', // Enum: GROUNDPLUS or ONEPLUS
                    'number' => 'string', // Positive integer (e.g., 1, 2, 3, ...)
                ],
                'area' => '/^\d+(\.\d+)?$/', // Float (e.g., 123.45)
                'seating_capacity' => 'integer', // Integer
                'facilities' => [
                    'wifi' => 'boolean', // Boolean (true or false, case-insensitive)
                    'whiteboard' => 'boolean',
                    'blackboard' => 'boolean',
                    'projector' => 'boolean',
                    'lcd_monitor' => 'boolean',
                    'air_conditioning' => 'boolean',
                    'power_backup' => 'boolean',
                    'computer_terminals' => 'integer', // Integer
                ],
            ];
            
            $errors = $this->validate->validateData($payload, $validation_checks);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            //GET ES record with payload

            $placeData = $this->placeModel->getPlace($payload['place_id'],['schema' => 'Place_Minimal']);

            if (!$placeData) {
                return $this->response->error('GET_FAIL',['replace'=>'Place']);
            }

            if ($classroomId = $this->classroomModel->addClassroom($payload)) {
                return $this->response->success('POST_INSERT', ["classroomId"=>$classroomId], ['replace'=>'Classroom'] );
            } else {
                return $this->response->error('POST_INSERT_FAIL', ['replace'=>'Classroom'] );
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('POST_INSERT_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieve classroom information by its ID.
     *
     * @param int $placeId The ID of the place to retrieve.
     * @return WP_REST_Response|WP_Error The response object or an error object.
     */
    public function getClassroom($request)
    {
        try {
            // Get request data
            $classroomId = (int)$request['classroomId'];

            // Validate each field using the common functions
            $validation_checks = [
                'classroomId' => 'numeric'
            ];

            $prmBody = [
                'classroomId' => $classroomId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }
            // Retrieve place data
            $classroomData = $this->classroomModel->getClassroom($classroomId);

            // If place not found, return an error
            if (!$classroomData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Classroom']);
            }

            // If you need additional validation checks on the retrieved data, add them here
            // For example:
            // if (!$this->someAdditionalValidation($placeData)) {
            //     return $this->response->error('VALIDATION_FAIL', ['message' => 'Additional validation failed.']);
            // }

            // Return success response with place details
            return $this->response->success('GET_SUCCESS', $classroomData, ['message' => 'Classroom details retrieved successfully']);

        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    function getClassrooms($request)
    {
        try {
            $getVars = $request->get_query_params();
            $viewType = $request->get_param('viewType');

            if (isset($getVars['place_id'])) {
                $query['custom'] = [
                    "query" => [
                        "term" => [
                            "place_id" => $getVars['place_id']
                        ]
                    ],
                    "sort" => [
                        [
                            "classroom_id" => [
                                "order" => "asc"
                            ]
                        ]
                    ]
                ];
            } else {
                $query['custom'] = [
                    "query" => [
                        "match_all" => (object)[]
                    ],
                    "sort" => [
                        [
                            "classroom_id" => [
                                "order" => "asc"
                            ]
                        ]
                    ]
                ];
            }

            if (isset($getVars['limit']) && isset($getVars['offset'])) {
                $query['qryStr'] = [
                    "from" => $getVars['offset'],
                    "size" => $getVars['limit']
                ];
            }

            $classroomsData = $this->classroomModel->getClassrooms($query);
            // If place not found, return an error
            if (!$classroomsData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Classrooms']);
            }

            if($viewType == 'grid'){
                
                $columns =[
                            [
                                "field"    => "title",
                                "label"    => "Title",
                                "sortable" => true
                            ],
                            [
                                "field"    => "floor.type",
                                "label"    => "Type",
                                "sortable" => true
                            ],
                            [
                                "field"    => "floor.number",
                                "label"    => "Number",
                                "sortable" => true
                            ],
                            [
                                "field"    => "area",
                                "label"    => "Area",
                                "sortable" => true
                            ],
                            [
                                "field"    => "seating_capacity",
                                "label"    => "Seating Capacity",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.wifi",
                                "label"    => "Wifi",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.whiteboard",
                                "label"    => "Whiteboard",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.blackboard",
                                "label"    => "Blackboard",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.projector",
                                "label"    => "Projector",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.lcd_monitor",
                                "label"    => "LCD Monitor",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.air_conditioning",
                                "label"    => "Air Conditioning",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.power_backup",
                                "label"    => "Power Backup",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.computer_terminals",
                                "label"    => "Computer Terminals",
                                "sortable" => true
                            ]

                        ];
                        
                $classroomsData['data'] = [
                    "columns" => $columns,
                    "rows" => $classroomsData['data']
                ];

                return $this->response->success('GET_SUCCESS', $classroomsData, ['message' => 'Places data retrieved successfully']);
            }
            // Return success response with place details
            return $this->response->success('GET_SUCCESS', $classroomsData, ['message' => 'Classrooms data retrieved successfully']);
        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }
    /**
     * Update user region information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function updClassroom($request)
    {
        try {

            $classroomId = (int)$request['classroomId'];

            // Get request data
            $payload = json_decode($request->get_body(), true);

            // Validate each field using the common functions
            $validation_checks = [
                'classroomId' => 'numeric'
            ];

            $prmBody = [
                'classroomId' => $classroomId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            // Validate each field using the common functions
            $validation_checks = [
                'place_id' => 'integer', // Must be an integer
                'title' => '/^.{1,255}$/', // String with 1 to 255 characters
                'floor' => [
                    'type' => '/^(GROUND_PLUS|ONE_PLUS)$/', // Enum: GROUNDPLUS or ONEPLUS
                    'number' => 'string', // Positive integer (e.g., 1, 2, 3, ...)
                ],
                'area' => '/^\d+(\.\d+)?$/', // Float (e.g., 123.45)
                'seating_capacity' => 'integer', // Integer
                'facilities' => [
                    'wifi' => 'boolean', // Boolean (true or false, case-insensitive)
                    'whiteboard' => 'boolean',
                    'blackboard' => 'boolean',
                    'projector' => 'boolean',
                    'lcd_monitor' => 'boolean',
                    'air_conditioning' => 'boolean',
                    'power_backup' => 'boolean',
                    'computer_terminals' => 'integer', // Integer
                ],
            ];
            
            $errors = $this->validate->validateData($payload, $validation_checks);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            //GET ES record with payload

            $placeData = $this->placeModel->getPlace($payload['place_id'],['schema' => 'Place_Minimal']);

            if (!$placeData) {
                return $this->response->error('GET_FAIL',['replace'=>'Place']);
            }

            if ($this->classroomModel->updClassroom($classroomId, $payload)) {
                return $this->response->success('PUT_UPDATE', null, ['replace'=>'Classroom'] );
            } else {
                return $this->response->error('PUT_UPDATE_FAIL', ['replace'=>'Classroom'] );
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Delete place information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function delClassroom($request)
    {
        try {
            $classroomId = (int)$request['classroomId'];

            // Validate required fields
            $validation_checks = [
                'classroomId' => 'numeric'
            ];

            $prmBody = [
                'classroomId' => $classroomId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Check if the place exists before deleting
            /*
            $classroomData = $this->classroomModel->getClassroom($classroomId, ['schema' => 'Classroom_Minimal']);
            if (!$classroomData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Classroom']);
            }
            */

            // Perform delete operation
            if ($this->classroomModel->delClassroom($classroomId)) {
                return $this->response->success('DELETE_DATA', null, ['replace' => 'ClassroomId']);
            } else {
                return $this->response->error('DELETE_DATA_FAIL', ['replace' => 'ClassroomId']);
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('DELETE_DATA_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }
}
