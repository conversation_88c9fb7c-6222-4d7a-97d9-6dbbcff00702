Vue.component('yuno-empty-state-v2', {
    props: {
        options: {
            type: Object,
            required: true,
        },
        cta: {
            type: Array,
            default: () => []
        },
        image: {
            type: String,
            default: '/assets/images/noDataFound.svg'
        }
    },
    template: `
        <section class="emptyStateV2 " :class="[options.type]">
            <figure>
                <img 
                    width="80" 
                    height="59" 
                    :src="wpThemeURL + image" 
                    alt="Yuno Learning"
                >
                <figcaption>
                    {{ options.message }}
                </figcaption>
                <div class="ctaWrapper" v-if="cta.length">
                    <b-button 
                        @click="ctaTrigger()"
                        :tag="ctaItem.type === 'link' ? 'a' : 'button'"
                        :href="ctaItem.url"
                        :target="ctaItem.target"
                        class="yunoPrimaryCTA wired"
                        v-for="(ctaItem, index) in cta"
                    >
                        {{ ctaItem.label }}
                    </b-button>
                </div>
            </figure>
        </section>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'user'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        ctaTrigger() {
            Event.$emit('ctaTrigger');
        }
    }
});