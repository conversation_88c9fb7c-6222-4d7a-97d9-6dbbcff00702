<?php
class CourseActivities
{
    /**
     * country course price option table insertion
     */
    function country_course_price()
    {
        $options_array = array(
            'yuno_india_configuration' => array(
                'tax_rate' => '18',
                'country_code' => 'IN',
                'currency' => 'INR',
                //Indian rupee
            ),
            'yuno_usa_configuration' => array(
                'yuno_india_tax_rate' => '19',
                'country_code' => 'USA',
                'currency' => 'USD',
                //United States Dollar
            ),
            'yuno_uae_configuration' => array(
                'yuno_india_tax_rate' => '20',
                'country_code' => 'UAE',
                'currency' => 'AED',
                //Arab Emirates Dirham
            )
        );
        update_option('yuno_country_course_price_options', $options_array);
    }


    /**
     * common course price calculation in common area, we will use this action to get pricee of course as string. also it is country based. 
     */
    function country_based_course_price_distribution($course_price, $country = null, $course_id = null)
    {
        $options = get_option('yuno_country_course_price_options');
        if (empty($country)) {
            $tax_percantage = $options['yuno_india_configuration']['tax_rate'];
            $country_code = $options['yuno_india_configuration']['country_code'];
            $currency = $options['yuno_india_configuration']['currency'];
        } else {
            if ($country == "usa") {
                $tax_percantage = $options['yuno_usa_configuration']['tax_rate'];
                $country_code = $options['yuno_usa_configuration']['country_code'];
                $currency = $options['yuno_usa_configuration']['currency'];
            } else if ($country == "uae") {
                $tax_percantage = $options['yuno_uae_configuration']['tax_rate'];
                $country_code = $options['yuno_uae_configuration']['country_code'];
                $currency = $options['yuno_uae_configuration']['currency'];
            } else if ($country == "in") {
                $tax_percantage = $options['yuno_india_configuration']['tax_rate'];
                $country_code = $options['yuno_india_configuration']['country_code'];
                $currency = $options['yuno_india_configuration']['currency'];
            }
        }

        if ($course_price == 0 || $course_price == "0" || $course_price == "free" || $course_price == "Free") {
            $price = 0;
        }
        $price = (int) $course_price;
        if (!empty($course_id)) {
            $price = (int) get_post_meta($course_id, 'Unit_Price', true);
        }
        $calculated_amount = 1 + $tax_percantage / 100;
        $price_with_gst = $price * $calculated_amount;
        $gst_amount = $price_with_gst - $price;
        if ($course_price == 0 || $course_price == "0" || $course_price == "free" || $course_price == "Free") {
            $price_with_gst = 0;
            $gst_amount = 0;
            return $response = [
                "price_with_gst" => (string) $course_price,
                "price_without_gst" => $price,
                "gst_amount" => $gst_amount,
                "country_code" => $country_code,
                "currency" => $currency,
                "tax_percantage" => $tax_percantage
            ];
        }


        if (strpos($price_with_gst, '.') !== false) {
            $correct_price_with_gst = ceil($price_with_gst);
        } else {
            $correct_price_with_gst = $price_with_gst;
        }

        if (strpos($gst_amount, '.') !== false) {
            $correct_gst_amount = ceil($gst_amount);
        } else {
            $correct_gst_amount = $gst_amount;
        }

        if (strpos($price, '.') !== false) {
            $correct_price = ceil($price);
        } else {
            $correct_price = $price;
        }

        return $response = [
            "price_with_gst" => (string) $correct_price_with_gst,
            "price_without_gst" => $correct_price,
            "gst_amount" => $correct_gst_amount,
            "country_code" => $country_code,
            "currency" => $currency,
            "tax_percantage" => $tax_percantage
        ];
    }

    /**
     * common course price calculation in common area,  we will use this action to get pricee of course as string.
     */
    function course_price_distribution($course_price, $course_id = null)
    {
        if ($course_price == 0 || $course_price == "0" || $course_price == "free" || $course_price == "Free") {
            $price = 0;
        }
        $price = (int) $course_price;
        if (!empty($course_id)) {
            $price = (int) get_post_meta($course_id, 'Unit_Price', true);
        }
        $options = get_option('yuno_country_course_price_options');
        $tax_percantage = $options['yuno_india_configuration']['tax_rate'];
        $country_code = $options['yuno_india_configuration']['country_code'];
        $currency = $options['yuno_india_configuration']['currency'];
        $calculated_amount = 1 + $tax_percantage / 100;
        $price_with_gst = $price * $calculated_amount;
        $gst_amount = $price_with_gst - $price;
        if ($course_price == 0 || $course_price == "0" || $course_price == "free" || $course_price == "Free") {
            $price_with_gst = 0;
            $gst_amount = 0;
            return $response = [
                "price_with_gst" => (int) $price,
                "price_without_gst" => $price,
                "gst_amount" => $gst_amount,
                "country_code" => $country_code,
                "currency" => $currency,
                "tax_percantage" => $tax_percantage
            ];
        }

        if (strpos($price_with_gst, '.') !== false) {
            $correct_price_with_gst = ceil($price_with_gst);
        } else {
            $correct_price_with_gst = $price_with_gst;
        }

        if (strpos($gst_amount, '.') !== false) {
            $correct_gst_amount = ceil($gst_amount);
        } else {
            $correct_gst_amount = $gst_amount;
        }

        if (strpos($price, '.') !== false) {
            $correct_price = ceil($price);
        } else {
            $correct_price = $price;
        }

        return $response = [
            "price_with_gst" => (int) $correct_price_with_gst,
            "price_without_gst" => $correct_price,
            "gst_amount" => $correct_gst_amount,
            "country_code" => $country_code,
            "currency" => $currency,
            "tax_percantage" => $tax_percantage
        ];
    }

    /**
     * Getting category of course in referrer case
     * return course category on the basis of referrer id
     */

    function getting_referrer_category_of_course()
    {
        global $wpdb;
        $uri         = explode("/", $_SERVER['REQUEST_URI']);

        if ($uri !== null) {
            $filteredURI = array_values(array_filter($uri));
            // Continue with further processing of the filtered array
        } else {
            // Handle the case when the array is null
        }

        //$filteredURI = array_values(array_filter($uri));
        $count       = count($filteredURI);
        $result      = '';
        if ($count == 2) {
            $encoded_value = base64_decode($filteredURI[$count - 1]);
            $encoded_val   = explode("@@@", $encoded_value);
            $course_id     = $encoded_val[0];
            $categoryData  = $wpdb->get_results("SELECT category from wp_signup_template", ARRAY_A);
            $categoryArray = [];
            foreach ($categoryData as $key => $value) {
                $categoryArray[] = strtolower($value['category']);
            }
            $categories = wp_get_post_terms($course_id, 'course_category');
            foreach ($categories as $key => $value) {
                $category_slug = strtolower($value->slug);

                if (in_array($category_slug, $categoryArray)) {
                    $result = $category_slug;
                }
            }
        }
        return $result;
    }

    /**
     * To migrate course active enrollments into ES document- script-  CRON Set up
     */
    function migrate_course_active_enrollments_script()
    {
        global $wpdb;
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Calcutta');
        $collections = [];
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id"
            ]
        ];
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search?size=" . ELASTIC_RECORDS_COUNT,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $results = json_decode($response);
        if ($results !== null) {
            foreach ($results->hits->hits as $result) {
                $record_id = $result->_source->data->details->record_id;
                $active_enrollments_count = $wpdb->get_results("SELECT * FROM `wp_enrollment` WHERE `product_db_id` = $record_id AND `enrollment_end_date` >= NOW() AND `enrollment_status` = 'ACTIVE'");

                $active_batches_count = $wpdb->get_row("SELECT count(t1.id) as active_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now() and t1.id IN (select batch_id from wp_enrollment where enrollment_status = 'ACTIVE' AND `product_db_id` = $record_id)", ARRAY_A);

                $upcoming_batches_count = $wpdb->get_row("SELECT count(t1.id) as ongoing_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now()", ARRAY_A);

                //return new WP_REST_Response($active_batches_count['active_batches'], 200);
                $enrollmentDataCount = $wpdb->get_results("SELECT * FROM `wp_enrollment` WHERE `product_db_id` = $record_id AND `enrollment_end_date` <= NOW() AND `enrollment_status` = 'INACTIVE'", ARRAY_A);
                $enrollmentCount = count($enrollmentDataCount);
                $collections = array(
                    'update_event_type' => "course",
                    'record_id' => (int) $record_id,
                    'active_enrollments' => (int) count($active_enrollments_count),
                    'active_batches' => (int) $active_batches_count['active_batches'],
                    'ongoing_batches' => (int) $upcoming_batches_count['ongoing_batches'],
                    'successful_students' => (int) $enrollmentCount
                );
                $curlData = [
                    "data" => [
                        'details' => $collections,
                        '@timestamp' => date("Y-m-d H:i:s")
                    ]
                ];
                try {
                    update_elastic_event($curlData);
                } catch (Exception $e) {
                    //error_log(" catchhh migrate_course_active_enrollments".json_encode($e));   
                }
            }
        }
        return true;
    }

    /**
     * update batch enrollment status as active/inactive
     */
    function update_course_batch_object($limit)
    {
        require_once(get_stylesheet_directory() . '/inc/classes/rest_api/v1/CSVController.php');

        global $wpdb;
        date_default_timezone_set('Asia/Calcutta');
        $currentDateTime = date("Y-m-d H:i:s");
        //error_log("course batch object update of es course doc case start: ".date("Y-m-d H:i:s")." === ".$currentDateTime."\n", 3, ABSPATH."course-batch-object-update-errors.log"); 
        //$getBatches = $wpdb->get_results("SELECT wp_batches.id FROM wp_batches inner join wp_enrollment on wp_enrollment.batch_id=wp_batches.id WHERE wp_batches.end_date>now() and wp_enrollment.enrollment_status ='ACTIVE' order by wp_batches.id desc limit $limit, 5", ARRAY_A);
        $getBatches = $wpdb->get_results("SELECT wp_batches.id FROM wp_batches WHERE wp_batches.end_date>now() order by id desc limit $limit, 5", ARRAY_A);
        foreach ($getBatches as $getBatchData) {
            //error_log("course batch object update of es course doc case foreach: ".date("Y-m-d H:i:s")." === ".$currentDateTime."\n", 3, ABSPATH."course-batch-object-update-errors.log"); 
            $batch_id = $getBatchData['id'];
            $csvController = new CSVController();
            $csvController->course_batch($batch_id);
        }
        //error_log("course batch object update of es course doc case end: ".date("Y-m-d H:i:s")." === ".$class_id."\n", 3, ABSPATH."course-batch-object-update-errors.log"); 
    }

    /** cron only once 01:15 am
     * instructor active batch update per course
     * once in a day
     * course batch object will be updated to es
     */
    function course_batch_object_update_done()
    {
        date_default_timezone_set('Asia/Calcutta');
        global $wpdb;
        $currentDateTime = date("Y-m-d H:i:s");
        $sql = "SELECT wp_batches.id FROM wp_batches WHERE wp_batches.end_date>now() order by id desc";
        $result = $wpdb->get_results($sql);
        $total = count($result);
        //$results=$wpdb->query("DELETE FROM wp_class_pending_data WHERE event_type = 'course_batch_object_update'"); 
        $i = 1;
        for ($x = 0; $x <= $total; $x += 5) {
            $data_argument = [
                "limit_count" => $x
            ];
            $j = 2 * $i;
            $after_five_minutes_datetime = date('Y-m-d H:i:s', strtotime("+" . $j . " minutes"));
            $insert["event_type"] = "course_batch_object_update";
            $insert["data"] = json_encode($data_argument);
            $insert["status"] = "pending";
            $insert["created"] = $after_five_minutes_datetime;
            $results = $wpdb->insert('wp_class_pending_data', $insert);
            $i++;
        }
    }

    /**
     * Providing current env course_category taxonomy infrmation to yuno popular api
     */
    function get_course_category_info()
    {
        if (site_url() == YUNO_PROD_ENV_HTTPS) {
            define('POPULAR_SEARCH_CAT_1', 2834);
            define('POPULAR_SEARCH_TEXT_1', "IELTS classes");
            define('POPULAR_SEARCH_CAT_2', 2852);
            define('POPULAR_SEARCH_TEXT_2', "IELTS classes for General Training");
            define('POPULAR_SEARCH_CAT_3', 2851);
            define('POPULAR_SEARCH_TEXT_3', "IELTS classes for Academic");
            define('POPULAR_SEARCH_CAT_4', 2834);
            define('POPULAR_SEARCH_TEXT_4', "IELTS crash course");
            define('POPULAR_SEARCH_CAT_5', 2821);
            define('POPULAR_SEARCH_TEXT_5', "English speaking classes for working professionals");
            define('POPULAR_SEARCH_CAT_6', 2819);
            define('POPULAR_SEARCH_TEXT_6', "English speaking classes for kids");
            define('POPULAR_SEARCH_CAT_7', 2814);
            define('POPULAR_SEARCH_TEXT_7', "English speaking beginners classes");
            define('POPULAR_SEARCH_CAT_8', 2806);
            define('POPULAR_SEARCH_TEXT_8', "Coding classes for kids");
            define('POPULAR_SEARCH_CAT_9', 3321);
            define('POPULAR_SEARCH_TEXT_9', "Python tutoring");
            define('POPULAR_SEARCH_CAT_10', 3321);
            define('POPULAR_SEARCH_TEXT_10', "Python programming classes");
            define('POPULAR_SEARCH_CAT_11', 2834);
            define('POPULAR_SEARCH_TEXT_11', "IELTS writing classes");
            define('POPULAR_SEARCH_CAT_12', 2834);
            define('POPULAR_SEARCH_TEXT_12', "IELTS speaking classes");
            define('POPULAR_SEARCH_CAT_13', 2811);
            define('POPULAR_SEARCH_TEXT_13', "English speaking classes");
            define('POPULAR_SEARCH_CAT_14', 2834);
            define('POPULAR_SEARCH_TEXT_14', "IELTS tutor");
            define('POPULAR_SEARCH_CAT_15', 2852);
            define('POPULAR_SEARCH_TEXT_15', "IELTS for PR");
            define('POPULAR_SEARCH_CAT_16', 2851);
            define('POPULAR_SEARCH_TEXT_16', "IELTS for study abroad");
            define('POPULAR_SEARCH_CAT_17', 3649);
            define('POPULAR_SEARCH_TEXT_17', "PTE Classes");
            define('POPULAR_SEARCH_CAT_18', 2834);
            define('POPULAR_SEARCH_TEXT_18', "Comprehensive Course");
            define('POPULAR_SEARCH_CAT_19', 4130);
            define('POPULAR_SEARCH_TEXT_19', "TOEFL classes");
            define('POPULAR_SEARCH_CAT_20', 2811);
            define('POPULAR_SEARCH_TEXT_20', "Duolingo test classes");
        } else if (site_url() == YUNO_STAGE_ENV_HTTPS) {
            define('POPULAR_SEARCH_CAT_1', 3060);
            define('POPULAR_SEARCH_TEXT_1', "IELTS classes");
            define('POPULAR_SEARCH_CAT_2', 3083);
            define('POPULAR_SEARCH_TEXT_2', "IELTS classes for General Training");
            define('POPULAR_SEARCH_CAT_3', 3082);
            define('POPULAR_SEARCH_TEXT_3', "IELTS classes for Academic");
            define('POPULAR_SEARCH_CAT_4', 3060);
            define('POPULAR_SEARCH_TEXT_4', "IELTS crash course");
            define('POPULAR_SEARCH_CAT_5', 3824);
            define('POPULAR_SEARCH_TEXT_5', "English speaking classes for working professionals");
            define('POPULAR_SEARCH_CAT_6', 3821);
            define('POPULAR_SEARCH_TEXT_6', "English speaking classes for kids");
            define('POPULAR_SEARCH_CAT_7', 3088);
            define('POPULAR_SEARCH_TEXT_7', "English speaking beginners classes");
            define('POPULAR_SEARCH_CAT_8', 3089);
            define('POPULAR_SEARCH_TEXT_8', "Coding classes for kids");
            define('POPULAR_SEARCH_CAT_9', 4230);
            define('POPULAR_SEARCH_TEXT_9', "Python tutoring");
            define('POPULAR_SEARCH_CAT_10', 4230);
            define('POPULAR_SEARCH_TEXT_10', "Python programming classes");
            define('POPULAR_SEARCH_CAT_11', 3060);
            define('POPULAR_SEARCH_TEXT_11', "IELTS writing classes");
            define('POPULAR_SEARCH_CAT_12', 3060);
            define('POPULAR_SEARCH_TEXT_12', "IELTS speaking classes");
            define('POPULAR_SEARCH_CAT_13', 3084);
            define('POPULAR_SEARCH_TEXT_13', "English speaking classes");
            define('POPULAR_SEARCH_CAT_14', 3060);
            define('POPULAR_SEARCH_TEXT_14', "IELTS tutor");
            define('POPULAR_SEARCH_CAT_15', 3083);
            define('POPULAR_SEARCH_TEXT_15', "IELTS for PR");
            define('POPULAR_SEARCH_CAT_16', 3082);
            define('POPULAR_SEARCH_TEXT_16', "IELTS for study abroad");
            define('POPULAR_SEARCH_CAT_17', 4362);
            define('POPULAR_SEARCH_TEXT_17', "PTE Classes");
            define('POPULAR_SEARCH_CAT_18', 3060);
            define('POPULAR_SEARCH_TEXT_18', "Comprehensive Course");
            define('POPULAR_SEARCH_CAT_19', 4545);
            define('POPULAR_SEARCH_TEXT_19', "TOEFL classes");
            define('POPULAR_SEARCH_CAT_20', 3084);
            define('POPULAR_SEARCH_TEXT_20', "Duolingo test classes");
        } else {
            define('POPULAR_SEARCH_CAT_1', 3060);
            define('POPULAR_SEARCH_TEXT_1', "IELTS classes");
            define('POPULAR_SEARCH_CAT_2', 3083);
            define('POPULAR_SEARCH_TEXT_2', "IELTS classes for General Training");
            define('POPULAR_SEARCH_CAT_3', 3082);
            define('POPULAR_SEARCH_TEXT_3', "IELTS classes for Academic");
            define('POPULAR_SEARCH_CAT_4', 3060);
            define('POPULAR_SEARCH_TEXT_4', "IELTS crash course");
            define('POPULAR_SEARCH_CAT_5', 3824);
            define('POPULAR_SEARCH_TEXT_5', "English speaking classes for working professionals");
            define('POPULAR_SEARCH_CAT_6', 3821);
            define('POPULAR_SEARCH_TEXT_6', "English speaking classes for kids");
            define('POPULAR_SEARCH_CAT_7', 3088);
            define('POPULAR_SEARCH_TEXT_7', "English speaking beginners classes");
            define('POPULAR_SEARCH_CAT_8', 3089);
            define('POPULAR_SEARCH_TEXT_8', "Coding classes for kids");
            define('POPULAR_SEARCH_CAT_9', 4328);
            define('POPULAR_SEARCH_TEXT_9', "Python tutoring");
            define('POPULAR_SEARCH_CAT_10', 4328);
            define('POPULAR_SEARCH_TEXT_10', "Python programming classes");
            define('POPULAR_SEARCH_CAT_11', 3060);
            define('POPULAR_SEARCH_TEXT_11', "IELTS writing classes");
            define('POPULAR_SEARCH_CAT_12', 3060);
            define('POPULAR_SEARCH_TEXT_12', "IELTS speaking classes");
            define('POPULAR_SEARCH_CAT_13', 3084);
            define('POPULAR_SEARCH_TEXT_13', "English speaking classes");
            define('POPULAR_SEARCH_CAT_14', 3060);
            define('POPULAR_SEARCH_TEXT_14', "IELTS tutor");
            define('POPULAR_SEARCH_CAT_15', 3083);
            define('POPULAR_SEARCH_TEXT_15', "IELTS for PR");
            define('POPULAR_SEARCH_CAT_16', 3082);
            define('POPULAR_SEARCH_TEXT_16', "IELTS for study abroad");
            define('POPULAR_SEARCH_CAT_17', 4722);
            define('POPULAR_SEARCH_TEXT_17', "PTE Classes");
            define('POPULAR_SEARCH_CAT_18', 3060);
            define('POPULAR_SEARCH_TEXT_18', "Comprehensive Course");
            define('POPULAR_SEARCH_CAT_19', 5227);
            define('POPULAR_SEARCH_TEXT_19', "TOEFL classes");
            define('POPULAR_SEARCH_CAT_20', 3084);
            define('POPULAR_SEARCH_TEXT_20', "Duolingo test classes");
        }
    }

    /**
     * Delete course from zoho crm after delete course from wp-admin
     */
    function course_delete_crm($post_id)
    {
        $event_types = ["course"];
        foreach ($event_types as $event_type) {
            $deleteData["record_id"] = $post_id;
            $deleteData["event_type"] = $event_type;
            $status = delete_elastic_event($deleteData);
            if ($status) {
                //error_log("course deleted successfully".$post_id);	
            }
        }
        global $post_type;
        $product_zoho_id = get_post_meta($post_id, 'id', true);
    }

    /**
     * to display error message to wp-admin, if course has active batches
     */
    function post_updated_messages_action($messages)
    {
        if (!empty($_SESSION['my_admin_notices'])) {
            $messages['post'][1] = '<div class="error notice"><p>You can not disable this course, because it has an active batches</p></div>';
            echo '<style type="text/css">
                .wp-core-ui .notice.is-dismissible { display:none; }
                </style>';
            unset($_SESSION['my_admin_notices']);
            return $messages;
        }
        return $messages;
    }

    function filter_post_data($data, $postarr)
    {
        global $wpdb;
        $post_id = $postarr["ID"];
        $row = $wpdb->get_results($wpdb->prepare("SELECT DISTINCT e.batch_id AS total FROM wp_batches AS b JOIN wp_enrollment AS e ON e.batch_id = b.id WHERE b.end_date > NOW() AND e.enrollment_status = %s AND e.product_db_id = %d", 'ACTIVE', $post_id), ARRAY_A);

        $totals = count($row);
        $status = $postarr["acf"]["field_634e408403fc2"] ?? "";
        if ($data["post_type"] == "course" && $status == "0" && $totals > 0) {
            $_SESSION['my_admin_notices'] = '<div class="error notice"><p>You can not disable this course, because it has an active batches</p></div>';
        } else {
            return $data;
        }
    }

    function call_after_save_update_course($post_id)
    {
        if (get_post_type($post_id) !== 'course') {
            return;
        }
        global $wpdb;
        date_default_timezone_set('asia/Calcutta');
        //error_log("post course to es entry: ".date("Y-m-d H:i:s")." === ".$post_id."\n", 3, ABSPATH."items-post-es-errors.log");
        error_log("get async call_after_save_update_course step 1 : " . $post_id . " == " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
        /*$lateObj = new LateController();
        $lateObj->late_test1($post_id);*/
        //$this->late_test1($post_id);
        // late_test1(21219);
        //late_test1($post_id);
        //$post_data = get_post($post_id);
        //error_log("6802 line --  get_post ".print_r($post_data,true)."=== ==== === ".date("Y-m-d H:i:s")."\n", 3, ABSPATH . "get-async-errors.log");
        //$myvals = get_post_meta($post_id);
        // error_log("6804 line --  get_post  ==meta ==".print_r($myvals,true)."=== ==== === ".date("Y-m-d H:i:s")."\n", 3, ABSPATH . "get-async-errors.log");
        //wp_schedule_single_event(strtotime('+30 seconds'), 'course_add_update_crm', array($post_id));
        $this->course_add_update_crm($post_id);
        //$lateObj->register_routes(); 
    }

    /**
     * Custom post type: course
     * When we save custom post type and update custom post type
     * course will be added or updated to zoho crm
     */
    function course_add_update_crm($post_id)
    {
        date_default_timezone_set('Asia/Kolkata');
        error_log("111111111111111111 post_id " . print_r($post_id, true) . "=== " . $post_id . " ==== === " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
        $product_zoho_id = get_post_meta($post_id, 'id', true);
        error_log("222222222222222 - product_zoho_id " . print_r($product_zoho_id, true) . "=== " . $product_zoho_id . " ==== === " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
        $published_at = get_the_time('M j, Y g:i A', $post_id);
        $categories = wp_get_post_terms($post_id, 'course_category');
        error_log("3333333333333 categories " . print_r($categories, true) . "==== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
        // Initialize the top-level category structure
        $categoryStructure = [];
        // Temporary storage to easily access categories by their ID
        $tempCategories = [];
        // First, let's map out all categories by their ID for easy access
        foreach ($categories as $category) {
            $tempCategories[$category->term_id] = [
                'id' => $category->term_id,
                'name' => $category->name,
                'slug' => $category->slug,
                'parent_id' => $category->parent,
                'sub_category' => []
            ];
        }
        // Now, let's build the structure
        foreach ($tempCategories as &$tempCategory) {
            if ($tempCategory['parent_id'] === 0) {
                // This is the top-level category
                $categoryStructure[] = &$tempCategory;
            } else {
                // This is a sub-category, add it to its parent
                $tempCategories[$tempCategory['parent_id']]['sub_category'][] = &$tempCategory;
            }
        }
        $category_id = [];
        foreach ($categories as $key => $value) {
            $category_id[] = $value->term_id;
        }
        if (count($category_id) > 0) {
            $term = get_term(min($category_id), 'course_category');
        }
        error_log(" course_add_update_crm --- category_id " . json_encode($category_id) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");

        error_log(" course_add_update_crm --- categoryStructure " . json_encode($categoryStructure) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log"); 

        $slug = $term->slug;
        if (has_post_thumbnail($post_id)) {
            $featured_thumb_img = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'single-post-thumbnail');
            $thumbnail = $featured_thumb_img[0];
        } else {
            $thumbnail = "";
        }

        $content_post = get_post($post_id);
        error_log("4444444444444444444 content_post " . print_r($content_post, true) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");

        $content = $content_post->post_content;
        $content = apply_filters('the_content', $content);
        $content = str_replace(']]>', ']]&gt;', $content);
        $org_id = get_post_meta($post_id, 'organization', true);
        $academy_id = get_post_meta($post_id, 'academies', true);

        error_log("333337777777666666777 academy_id " . json_encode($academy_id) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");

        if (is_array($academy_id)) {
            $academies = $academy_id;
        } else {
            $academies = [$academy_id];
        }
        error_log("33333333333 academy_id " . print_r($academy_id, true) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
        error_log("333335555555 academy_id " . json_encode($academies) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");

        //Other currency mapping
        $course_prices = array();
        $course_prices[] = [
            'code' => 'INR',
            'group_price' => (float) get_field('Group_Price', $post_id),
            '1_to_1_price' => (float) get_field('1_to_1_Price', $post_id)
        ];
        if (function_exists('get_field')) {
            $priceUSD = get_field('Price_USD', $post_id);
            if ($priceUSD) {
                    $course_prices[] = [
                        'code' => 'USD',
                        'group_price' => (float) $priceUSD['Group_Price'],
                        '1_to_1_price' => (float) $priceUSD['1_to_1_Price']
                    ];
            }
            $priceAED = get_field('Price_AED', $post_id);
            if ($priceAED) {
                    $course_prices[] = [
                        'code' => 'AED',
                        'group_price' => (float) $priceAED['Group_Price'],
                        '1_to_1_price' => (float) $priceAED['1_to_1_Price']
                    ];
            }
        }

        $curlPost = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "match" => [
                            "data.details.record_id" => $post_id
                        ]
                    ]
                ]
            ]
        ];
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search?size=" . ELASTIC_RECORDS_COUNT,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $res = json_decode($response);

        error_log("555555555555555555555 content_post " . print_r($res, true) . "== " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");

        if ($res->status == 404) {
        } else {
            $is_course_enable = (int) get_post_meta($post_id, 'is_course_enable', true);
            if ($is_course_enable == 1) {
                $course_enable = true;
            } else {
                $course_enable = false;
            }
            $curlPost = [
                "data" => [
                    "details" => [
                        "record_id" => (int) $post_id,
                        "user_id" => 2,
                        "resource_type" => "course",
                        "update_event_type" => "course",
                        "title" => html_entity_decode(get_the_title($post_id), ENT_NOQUOTES, 'UTF-8'),
                        "url" => get_permalink($post_id),
                        "description" => html_entity_decode(preg_replace('/<!--.*?-->|[^a-zA-Z0-9\s]/', '', $content), ENT_NOQUOTES, 'UTF-8'),
                        "excerpt" => html_entity_decode(get_the_excerpt($post_id), ENT_NOQUOTES, 'UTF-8'),
                        "taxonomy" => $category_id,
                        "parent_taxonomy" => !empty($slug) ? $slug : "",
                        'course_category' => $categoryStructure,
                        'type' => get_post_meta($post_id, 'Media_Type', true),
                        'live_classes' => (int) get_post_meta($post_id, 'live_classes', true),
                        'enrollment_type' => get_post_meta($post_id, 'enrollment_type', true),
                        'media_url' => get_post_meta($post_id, 'Media_URL', true),
                        "featuredImage" => $thumbnail,
                        "product_name" => html_entity_decode(get_post_meta($post_id, 'Product_Name', true), ENT_NOQUOTES, 'UTF-8'),
                        "product_code" => get_post_meta($post_id, 'Product_Code', true),
                        "product_description" =>  get_post_meta($post_id, 'Description', true),
                        //"product_description" => html_entity_decode(preg_replace('/<!--.*?-->|[^a-zA-Z0-9\s]/', '', get_post_meta($post_id, 'Description', true)), ENT_NOQUOTES, 'UTF-8'),
                        "unit_price" => (int) get_post_meta($post_id, 'Unit_Price', true),
                        "group_price" => (int) get_post_meta($post_id, 'Group_Price', true),
                        "one_to_one_price" => (int) get_post_meta($post_id, '1_to_1_Price', true),
                        "prices" => $course_prices,
                        "group_type" => get_post_meta($post_id, 'Group_Type', true),
                        "featured_course" => (int) get_post_meta($post_id, 'Featured_Course', true),
                        "short_description" => get_post_meta($post_id, 'Short_Description', true),
                        "price_month_1" => (int) get_post_meta($post_id, 'Price_Month_1', true),
                        "price_month_2" => (int) get_post_meta($post_id, 'Price_Month_2', true),
                        "price_month_3" => (int) get_post_meta($post_id, 'Price_Month_3', true),
                        "price_month_4" => (int) get_post_meta($post_id, 'Price_Month_4', true),
                        "price_month_5" => (int) get_post_meta($post_id, 'Price_Month_5', true),
                        "price_month_6" => (int) get_post_meta($post_id, 'Price_Month_6', true),
                        "price_month_7" => (int) get_post_meta($post_id, 'Price_Month_7', true),
                        "price_month_8" => (int) get_post_meta($post_id, 'Price_Month_8', true),
                        "price_month_9" => (int) get_post_meta($post_id, 'Price_Month_9', true),
                        "price_month_10" => (int) get_post_meta($post_id, 'Price_Month_10', true),
                        "price_month_11" => (int) get_post_meta($post_id, 'Price_Month_11', true),
                        "price_month_12" => (int) get_post_meta($post_id, 'Price_Month_12', true),
                        "duration_days" => (int) get_post_meta($post_id, 'Duration_Days', true),
                        "duration_weeks" => (int) get_post_meta($post_id, 'Duration_Weeks', true),
                        "duration_months" => (int) get_post_meta($post_id, 'Duration_Months', true),
                        "segment_english_speaking" => get_post_meta($post_id, 'Segment_English_Speaking', true),
                        "modules_covered" => get_post_meta($post_id, 'Modules_Covered', true),
                        "mock_tests" => get_post_meta($post_id, 'Mock_Tests', true),
                        "writing_assignments" => get_post_meta($post_id, 'Writing_Assignments', true),
                        "speaking_sessions" => get_post_meta($post_id, 'Speaking_Sessions', true),
                        "post_description" => html_entity_decode(preg_replace('/<!--.*?-->|[^a-zA-Z0-9\s]/', '', get_post_meta($post_id, 'Description', true)), ENT_NOQUOTES, 'UTF-8'),
                        "age_limit" => get_post_meta($post_id, 'Age_Limit', true),
                        "cognitive_level" => get_post_meta($post_id, 'Cognitive_Level', true),
                        "per_class_price" => (int) get_post_meta($post_id, 'Per_Class_Price', true),
                        "highlight_1" => html_entity_decode(get_post_meta($post_id, 'Highlight_1', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_2" => html_entity_decode(get_post_meta($post_id, 'Highlight_2', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_3" => html_entity_decode(get_post_meta($post_id, 'Highlight_3', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_4" => html_entity_decode(get_post_meta($post_id, 'Highlight_4', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_5" => html_entity_decode(get_post_meta($post_id, 'Highlight_5', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_6" => html_entity_decode(get_post_meta($post_id, 'Highlight_6', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_7" => html_entity_decode(get_post_meta($post_id, 'Highlight_7', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_8" => html_entity_decode(get_post_meta($post_id, 'Highlight_8', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_9" => html_entity_decode(get_post_meta($post_id, 'Highlight_9', true), ENT_NOQUOTES, 'UTF-8'),
                        "highlight_10" => html_entity_decode(get_post_meta($post_id, 'Highlight_10', true), ENT_NOQUOTES, 'UTF-8'),
                        "id" => (int) get_post_meta($post_id, 'id', true),
                        "max_seats" => (int) get_post_meta($post_id, 'max_seats', true),
                        "product_order" => (int) get_post_meta($post_id, 'Product_Order', true),
                        "instructor_hours" => (int) get_post_meta($post_id, 'Instructor_Hours', true),
                        "schedule_content" => (int) html_entity_decode(get_post_meta($post_id, 'Schedule_Content', true), ENT_NOQUOTES, 'UTF-8'),
                        "any_time" => (int) get_post_meta($post_id, 'Any_Time', true),
                        "is_course_have_levels" => (int) get_post_meta($post_id, 'is_course_have_levels', true),
                        "mapped_instructor_count" => (int) get_post_meta($post_id, 'mapped_instructor_count', true),
                        "cancellation_policy" => get_post_meta($post_id, 'cancellation_policy_data', true),
                        "policy_for_instructors" => get_post_meta($post_id, 'policy_for_instructors_data', true),
                        "is_enable" => $course_enable,
                        "org_id" => $org_id,
                        "academies" => $academies
                    ],
                    "@timestamp" => date("Y-m-d H:i:s")
                ]
            ];

            error_log("666666666666666666666 content_post " . print_r($curlPost, true) . "=== ==== === " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
            //$this->replaceElasticsearchDocument('course-'.$post_id, $curlPost);
            //$this->updateElasticsearchDocument('course-'.$post_id,$curlPost["data"]["details"]);
            update_elastic_event($curlPost);
            
        }
        $product_name = get_post_meta($post_id, 'Product_Name', true);
        $product_code = get_post_meta($post_id, 'Product_Code', true);
        $unit_price = get_post_meta($post_id, 'Unit_Price', true);
        $price_month_1 = get_post_meta($post_id, 'Price_Month_1', true);
        $price_month_2 = get_post_meta($post_id, 'Price_Month_2', true);
        $price_month_3 = get_post_meta($post_id, 'Price_Month_3', true);
        $price_month_4 = get_post_meta($post_id, 'Price_Month_4', true);
        $price_month_5 = get_post_meta($post_id, 'Price_Month_5', true);
        $price_month_6 = get_post_meta($post_id, 'Price_Month_6', true);
        $price_month_7 = get_post_meta($post_id, 'Price_Month_7', true);
        $price_month_8 = get_post_meta($post_id, 'Price_Month_8', true);
        $price_month_9 = get_post_meta($post_id, 'Price_Month_9', true);
        $price_month_10 = get_post_meta($post_id, 'Price_Month_10', true);
        $price_month_11 = get_post_meta($post_id, 'Price_Month_11', true);
        $price_month_12 = get_post_meta($post_id, 'Price_Month_12', true);
        $category_name = get_post_meta($post_id, 'Course_Categories', true);

                $product_zoho_id = 0;
                update_post_meta($post_id, 'id', $product_zoho_id);
                $is_course_enable = (int) get_post_meta($post_id, 'is_course_enable', true);
                if ($is_course_enable == 1) {
                    $course_enable = true;
                } else {
                    $course_enable = false;
                }
                $curlPost = [
                    "data" => [
                        "details" => [
                            "record_id" => (int) $post_id,
                            "user_id" => 2,
                            "event_type" => "course",
                            "event_label" => "Course Publish",
                            "resource_type" => "course",
                            "title" => html_entity_decode(get_the_title($post_id), ENT_NOQUOTES, 'UTF-8'),
                            "url" => get_permalink($post_id),
                            "description" => html_entity_decode(preg_replace('/<!--.*?-->|[^a-zA-Z0-9\s]/', '', $content), ENT_NOQUOTES, 'UTF-8'),
                            "excerpt" => html_entity_decode(get_the_excerpt($post_id), ENT_NOQUOTES, 'UTF-8'),
                            "taxonomy" => $category_id,
                            "parent_taxonomy" => !empty($slug) ? $slug : "",
                            'course_category' => $categoryStructure,
                            'type' => get_post_meta($post_id, 'Media_Type', true),
                            'live_classes' => (int) get_post_meta($post_id, 'live_classes', true),
                            'enrollment_type' => get_post_meta($post_id, 'enrollment_type', true),
                            'media_url' => get_post_meta($post_id, 'Media_URL', true),
                            "featuredImage" => $thumbnail,
                            "product_name" => html_entity_decode(get_post_meta($post_id, 'Product_Name', true), ENT_NOQUOTES, 'UTF-8'),
                            "product_code" => get_post_meta($post_id, 'Product_Code', true),
                            "product_description" => get_post_meta($post_id, 'Description', true),
                            "unit_price" => (int) get_post_meta($post_id, 'Unit_Price', true),
                            "group_price" => (int) get_post_meta($post_id, 'Group_Price', true),
                            "one_to_one_price" => (int) get_post_meta($post_id, '1_to_1_Price', true),
                            "prices" => $course_prices,
                            "group_type" => get_post_meta($post_id, 'Group_Type', true),
                            "featured_course" => (int) get_post_meta($post_id, 'Featured_Course', true),
                            "short_description" => get_post_meta($post_id, 'Short_Description', true),
                            //"short_description" => html_entity_decode(get_post_meta($post_id, 'Short_Description', true), ENT_NOQUOTES, 'UTF-8'),
                            "price_month_1" => (int) get_post_meta($post_id, 'Price_Month_1', true),
                            "price_month_2" => (int) get_post_meta($post_id, 'Price_Month_2', true),
                            "price_month_3" => (int) get_post_meta($post_id, 'Price_Month_3', true),
                            "price_month_4" => (int) get_post_meta($post_id, 'Price_Month_4', true),
                            "price_month_5" => (int) get_post_meta($post_id, 'Price_Month_5', true),
                            "price_month_6" => (int) get_post_meta($post_id, 'Price_Month_6', true),
                            "price_month_7" => (int) get_post_meta($post_id, 'Price_Month_7', true),
                            "price_month_8" => (int) get_post_meta($post_id, 'Price_Month_8', true),
                            "price_month_9" => (int) get_post_meta($post_id, 'Price_Month_9', true),
                            "price_month_10" => (int) get_post_meta($post_id, 'Price_Month_10', true),
                            "price_month_11" => (int) get_post_meta($post_id, 'Price_Month_11', true),
                            "price_month_12" => (int) get_post_meta($post_id, 'Price_Month_12', true),
                            "duration_days" => (int) get_post_meta($post_id, 'Duration_Days', true),
                            "duration_weeks" => (int) get_post_meta($post_id, 'Duration_Weeks', true),
                            "duration_months" => (int) get_post_meta($post_id, 'Duration_Months', true),
                            "segment_english_speaking" => get_post_meta($post_id, 'Segment_English_Speaking', true),
                            "modules_covered" => get_post_meta($post_id, 'Modules_Covered', true),
                            "mock_tests" => get_post_meta($post_id, 'Mock_Tests', true),
                            "writing_assignments" => get_post_meta($post_id, 'Writing_Assignments', true),
                            "speaking_sessions" => get_post_meta($post_id, 'Speaking_Sessions', true),
                            "post_description" => html_entity_decode(preg_replace('/<!--.*?-->|[^a-zA-Z0-9\s]/', '', get_post_meta($post_id, 'Description', true)), ENT_NOQUOTES, 'UTF-8'),
                            "age_limit" => get_post_meta($post_id, 'Age_Limit', true),
                            "cognitive_level" => get_post_meta($post_id, 'Cognitive_Level', true),
                            "per_class_price" => (int) get_post_meta($post_id, 'Per_Class_Price', true),
                            "highlight_1" => html_entity_decode(get_post_meta($post_id, 'Highlight_1', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_2" => html_entity_decode(get_post_meta($post_id, 'Highlight_2', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_3" => html_entity_decode(get_post_meta($post_id, 'Highlight_3', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_4" => html_entity_decode(get_post_meta($post_id, 'Highlight_4', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_5" => html_entity_decode(get_post_meta($post_id, 'Highlight_5', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_6" => html_entity_decode(get_post_meta($post_id, 'Highlight_6', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_7" => html_entity_decode(get_post_meta($post_id, 'Highlight_7', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_8" => html_entity_decode(get_post_meta($post_id, 'Highlight_8', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_9" => html_entity_decode(get_post_meta($post_id, 'Highlight_9', true), ENT_NOQUOTES, 'UTF-8'),
                            "highlight_10" => html_entity_decode(get_post_meta($post_id, 'Highlight_10', true), ENT_NOQUOTES, 'UTF-8'),
                            "id" => (int) get_post_meta($post_id, 'id', true),
                            "max_seats" => (int) get_post_meta($post_id, 'max_seats', true),
                            "product_order" => (int) get_post_meta($post_id, 'Product_Order', true),
                            "instructor_hours" => (int) get_post_meta($post_id, 'Instructor_Hours', true),
                            "schedule_content" => (int) html_entity_decode(get_post_meta($post_id, 'Schedule_Content', true), ENT_NOQUOTES, 'UTF-8'),
                            "any_time" => (int) get_post_meta($post_id, 'Any_Time', true),
                            "is_course_have_levels" => (int) get_post_meta($post_id, 'is_course_have_levels', true),
                            "mapped_instructor_count" => (int) get_post_meta($post_id, 'mapped_instructor_count', true),
                            "published_at" => $published_at,
                            "cancellation_policy" => get_post_meta($post_id, 'cancellation_policy_data', true),
                            "policy_for_instructors" => get_post_meta($post_id, 'policy_for_instructors_data', true),
                            "is_enable" => $course_enable,
                            "org_id" => $org_id,
                            "academies" => $academies
                        ],
                        "@timestamp" => date("Y-m-d H:i:s")
                    ]
                ];
                error_log("888888888888888888888888888 content_post " . print_r($response, true) . "=== ==== === " . date("Y-m-d H:i:s") . "\n", 3, ABSPATH . "get-async-errors.log");
                post_elastic_event($curlPost);
                if (has_post_thumbnail($post_id)) {
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'single-post-thumbnail');
                    $featured_thumb_img = $image[0];
                } else {
                    $featured_thumb_img = get_post_meta($post_id, 'Media_URL', true);
                }
                $yoast_title = get_post_meta($post_id, 'Product_Name', true);
                $yoast_description = get_post_meta($post_id, 'Description', true);
                update_post_meta($post_id, '_yoast_wpseo_opengraph-image', $featured_thumb_img);
                update_post_meta($post_id, '_yoast_wpseo_opengraph-title', $yoast_title);
                update_post_meta($post_id, '_yoast_wpseo_opengraph-description', $yoast_description);
                update_post_meta($post_id, '_yoast_wpseo_twitter-title', $yoast_title);
                update_post_meta($post_id, '_yoast_wpseo_twitter-description', $yoast_description);
    }

    /**
     * Getting category of course
     * return course category on the basis of course id
     */
    function getting_category_of_course()
    {
        global $wpdb;
        $uri         = explode("/", $_SERVER['REQUEST_URI']);
        //$filteredURI = array_values(array_filter($uri));
        if ($uri !== null) {
            $filteredURI = array_values(array_filter($uri));
            // Continue with further processing of the filtered array
        } else {
            // Handle the case when the array is null
        }


        $count       = count($filteredURI);
        if ($count == 3) {
            $courseId = get_the_ID();
        } else {
            $parentSlug = $filteredURI[$count - 3];
            $post       = get_page_by_path($parentSlug, OBJECT, 'course');
            $courseId   = $post->ID;
            $children   = get_children($courseId, OBJECT);
            $courseId   = current(array_keys($children));
        }

        $categoryData  = $wpdb->get_results("SELECT category from wp_signup_template", ARRAY_A);
        $categoryArray = [];
        foreach ($categoryData as $key => $value) {
            $categoryArray[] = strtolower($value['category']);
        }
        $result     = '';
        $categories = wp_get_post_terms($courseId, 'course_category');
        foreach ($categories as $key => $value) {
            $category_slug = strtolower($value->slug);

            if (in_array($category_slug, $categoryArray)) {
                $result = $category_slug;
            }
        }
        return $result;
    }

    /**
     * Getting parent course id
     * return parent course id on the basis of uri/slug
     */
    function getting_parent_course_id()
    {
        $uri = explode("/", $_SERVER['REQUEST_URI']);
        //$filteredURI = array_values(array_filter($uri));
        if ($uri !== null) {
            $filteredURI = array_values(array_filter($uri));
            // Continue with further processing of the filtered array
        } else {
            // Handle the case when the array is null
        }
        $count = count($filteredURI);
        if ($count == 2) {
            return get_the_ID();
        } else {
            $parentSlug = $filteredURI[$count - 2];
            $post = get_page_by_path($parentSlug, OBJECT, 'course');
            return get_the_ID();
        }
    }

    //create a custom taxonomy name it "type" for your posts
    function create_course_custom_taxonomy()
    {

        $labels = array(
            'name' => _x('Course Categories', 'taxonomy general name'),
            'singular_name' => _x('Course Category', 'taxonomy singular name'),
            'search_items' => __('Search Course Categories'),
            'all_items' => __('All Course Categories'),
            'parent_item' => __('Parent Course Category'),
            'parent_item_colon' => __('Parent Course Category:'),
            'edit_item' => __('Edit Course Category'),
            'update_item' => __('Update Course Category'),
            'add_new_item' => __('Add New Course Category'),
            'new_item_name' => __('New Course Category Name'),
            'menu_name' => __('Course Categories'),
        );

        register_taxonomy(
            'course_category',
            array('course', 'student_results', 'writing_test', 'avada_faq', 'posts', 'ebooks', 'video', 'videotestimonial', 'examresult', 'documents', 'article', 'tribe_events', 'learning_content', 'report', 'class_video_clipping', 'org', 'orgadminprofile', 'referrer_code', 'quiz', 'writing_test', 'page', 'academy'),
            array(
                'hierarchical' => true,
                'labels' => $labels,
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => '.', 'with_front' => false),
                'show_in_rest' => true,
                'rest_base' => 'raman',
                'rest_controller_class' => 'WP_REST_Terms_Controller',
                'public' => true,
                'show_in_nav_menus' => true,
                'show_tagcloud' => true
            )
        );
    }
    /**
     * Get the course id based on the batch id.
     *
     * @return int The course id if found, otherwise 0.
     */
    // function getting_batch_course_id()
    // {
    //     global $wpdb;
    //     // Get the current post's ID.
    //     $batch_post_id = get_the_ID();

    //     // If get_the_ID() fails, it returns 0. Check for this case.
    //     if ($batch_post_id === 0) {
    //         // Handle the error appropriately.
    //         // This is just a placeholder. Replace it with your actual error handling code.
    //         die('Error: Failed to get the post ID.');
    //     }

    //     // Get the 'created_batch_id' meta field for the post.
    //     $batch_id_meta = get_post_meta($batch_post_id, 'created_batch_id', true);

    //     // If get_post_meta() fails or 'created_batch_id' is not set, it returns an empty string.
    //     // Use strict comparison to check for this case.
    //     $batch_id = ($batch_id_meta === '') ? 0 : $batch_id_meta;

    //     // Use a prepared statement to prevent SQL injection.
    //     $query = $wpdb->prepare("SELECT product_id FROM wp_group_relationships WHERE batch_id=%d", $batch_id);
    //     $is_course_exist = $wpdb->get_row($query, ARRAY_A);

    //     // Initialize course_id to 0.
    //     $course_id = 0;

    //     // If a matching row is found, set course_id to the product_id.
    //     if (!empty($is_course_exist)) {
    //         $course_id = (int) $is_course_exist['product_id'];
    //     }

    //     // Return the course id if found, otherwise 0.
    //     return $course_id;
    // }

    public function replaceElasticsearchDocument($documentId, $newDocumentData) {
        // Convert the array to JSON
        $jsonData = json_encode($newDocumentData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    
        // Elasticsearch API Endpoint
        $endpoint = ELASTIC_SEARCH_END_URL ."/course/_update/".$documentId;
    
        // Initialize cURL
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_PORT, ELASTIC_SEARCH_PORT);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ]);
    
        // Execute cURL Request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $result =  json_decode($response, true);

        error_log(" replaceElasticsearchDocument response  === " . date('Y-m-d H:i:s') . json_encode($result)."\n\n", 3, ABSPATH . 'error-logs/course_update_es.log');
    
        // Return response and HTTP code
        // return [
        //     'http_code' => $httpCode,
        //     'response' => json_decode($response, true)
        // ];
    }
    public function updateElasticsearchDocument($documentId, $newData) {
        // Elasticsearch API Endpoint
        $endpoint = ELASTIC_SEARCH_END_URL . "/course/_update/" . $documentId;
    
        // Prepare Elasticsearch script to MERGE fields AND preserve arrays
        $script = "
            if (ctx._source.data.details == null) { 
                ctx._source.data.details = new HashMap(); 
            }
            for (key in params.details.keySet()) {
                if (ctx._source.data.details.containsKey(key) && ctx._source.data.details[key] instanceof List) {
                    // Preserve arrays: If existing key is an array, append new values
                    ctx._source.data.details[key].addAll(params.details[key]);
                } else {
                    // Normal field update
                    ctx._source.data.details[key] = params.details[key];
                }
            }
        ";
    
        // Prepare request body
        $requestBody = [
            "script" => [
                "source" => $script,
                "lang"   => "painless",
                "params" => ["details" => $newData]
            ]
        ];
    
        // Convert to JSON
        $jsonData = json_encode($requestBody, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    
        // Initialize cURL
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_PORT, ELASTIC_SEARCH_PORT);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ]);
    
        // Execute cURL request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
    
        error_log("updateElasticsearchDocument response === " . json_encode($response) . "\n", 3, ABSPATH . 'error-logs/course_update_es.log');
    }
    
    
    public function updateElasticsearchDocument333($documentId, $newData) {
        // Fetch existing document from Elasticsearch
        $existingData = $this->getElasticsearchDocument($documentId);
    
        // Get only changed fields
        $updateFields = $this->getUpdatedFields($existingData, $newData);
    
        // If no changes, return early
        if (empty($updateFields)) {
            error_log("No changes detected for Elasticsearch update.");
            return;
        }
    
        // Prepare script statements
        $scriptStatements = [];
        $params = [];
    
        foreach ($updateFields as $key => $value) {
            $scriptStatements[] = "ctx._source.data.details.{$key} = params.{$key}";
            $params[$key] = $value;
        }
    
        // Construct the request body
        $requestBody = [
            "script" => [
                "source" => implode("; ", $scriptStatements) . ";",
                "lang"   => "painless",
                "params" => $params
            ]
        ];
    
        // Convert to JSON
        $jsonData = json_encode($requestBody, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    
        // Send cURL request to update
        $endpoint = ELASTIC_SEARCH_END_URL . "/course/_update/" . $documentId;
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_PORT, ELASTIC_SEARCH_PORT);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ]);
    
        $response = curl_exec($ch);
        curl_close($ch);
    
        error_log("updateElasticsearchDocument response === " . json_encode($response) . "\n", 3, ABSPATH . 'error-logs/course_update_es.log');
    }
    public function getUpdatedFields($existingData, $newData) {
        $updatedFields = [];
    
        foreach ($newData as $key => $value) {
            // Check if field exists in the existing data and is different
            if (!isset($existingData[$key]) || $existingData[$key] !== $value) {
                $updatedFields[$key] = $value;
            }
        }
    
        return $updatedFields;
    }
    public function getElasticsearchDocument($documentId) {
        $endpoint = ELASTIC_SEARCH_END_URL . "/course/_doc/" . $documentId;
        
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_PORT, ELASTIC_SEARCH_PORT);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            'Content-Type: application/json'
        ]);
    
        $response = curl_exec($ch);
        curl_close($ch);
    
        return json_decode($response, true)["_source"]["data"]["details"] ?? [];
    }
        
    
    
}
