/**
 * Minified by jsDelivr 
 * Original file: /npm/moment@2.30.1/moment.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var H;function _(){return H.apply(null,arguments)}function y(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function F(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function c(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function L(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(c(e,t))return;return 1}function g(e){return void 0===e}function w(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function V(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function G(e,t){for(var n=[],s=e.length,i=0;i<s;++i)n.push(t(e[i],i));return n}function E(e,t){for(var n in t)c(t,n)&&(e[n]=t[n]);return c(t,"toString")&&(e.toString=t.toString),c(t,"valueOf")&&(e.valueOf=t.valueOf),e}function l(e,t,n,s){return Wt(e,t,n,s,!0).utc()}function p(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function A(e){var t,n,s=e._d&&!isNaN(e._d.getTime());return s&&(t=p(e),n=j.call(t.parsedDateParts,function(e){return null!=e}),s=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(s=s&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?s:(e._isValid=s,e._isValid)}function I(e){var t=l(NaN);return null!=e?E(p(t),e):p(t).userInvalidated=!0,t}var j=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,s=0;s<n;s++)if(s in t&&e.call(this,t[s],s,t))return!0;return!1},Z=_.momentProperties=[],z=!1;function q(e,t){var n,s,i,r=Z.length;if(g(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),g(t._i)||(e._i=t._i),g(t._f)||(e._f=t._f),g(t._l)||(e._l=t._l),g(t._strict)||(e._strict=t._strict),g(t._tzm)||(e._tzm=t._tzm),g(t._isUTC)||(e._isUTC=t._isUTC),g(t._offset)||(e._offset=t._offset),g(t._pf)||(e._pf=p(t)),g(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)g(i=t[s=Z[n]])||(e[s]=i);return e}function $(e){q(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===z&&(z=!0,_.updateOffset(this),z=!1)}function k(e){return e instanceof $||null!=e&&null!=e._isAMomentObject}function B(e){!1===_.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function e(r,a){var o=!0;return E(function(){if(null!=_.deprecationHandler&&_.deprecationHandler(null,r),o){for(var e,t,n=[],s=arguments.length,i=0;i<s;i++){if(e="","object"==typeof arguments[i]){for(t in e+="\n["+i+"] ",arguments[0])c(arguments[0],t)&&(e+=t+": "+arguments[0][t]+", ");e=e.slice(0,-2)}else e=arguments[i];n.push(e)}B(r+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),o=!1}return a.apply(this,arguments)},a)}var J={};function Q(e,t){null!=_.deprecationHandler&&_.deprecationHandler(e,t),J[e]||(B(t),J[e]=!0)}function a(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function X(e,t){var n,s=E({},e);for(n in t)c(t,n)&&(F(e[n])&&F(t[n])?(s[n]={},E(s[n],e[n]),E(s[n],t[n])):null!=t[n]?s[n]=t[n]:delete s[n]);for(n in e)c(e,n)&&!c(t,n)&&F(e[n])&&(s[n]=E({},s[n]));return s}function K(e){null!=e&&this.set(e)}_.suppressDeprecationWarnings=!1,_.deprecationHandler=null;var ee=Object.keys||function(e){var t,n=[];for(t in e)c(e,t)&&n.push(t);return n};function r(e,t,n){var s=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-s.length)).toString().substr(1)+s}var te=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ne=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,se={},ie={};function s(e,t,n,s){var i="string"==typeof s?function(){return this[s]()}:s;e&&(ie[e]=i),t&&(ie[t[0]]=function(){return r(i.apply(this,arguments),t[1],t[2])}),n&&(ie[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function re(e,t){return e.isValid()?(t=ae(t,e.localeData()),se[t]=se[t]||function(s){for(var e,i=s.match(te),t=0,r=i.length;t<r;t++)ie[i[t]]?i[t]=ie[i[t]]:i[t]=(e=i[t]).match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"");return function(e){for(var t="",n=0;n<r;n++)t+=a(i[n])?i[n].call(e,s):i[n];return t}}(t),se[t](e)):e.localeData().invalidDate()}function ae(e,t){var n=5;function s(e){return t.longDateFormat(e)||e}for(ne.lastIndex=0;0<=n&&ne.test(e);)e=e.replace(ne,s),ne.lastIndex=0,--n;return e}var oe={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function o(e){return"string"==typeof e?oe[e]||oe[e.toLowerCase()]:void 0}function ue(e){var t,n,s={};for(n in e)c(e,n)&&(t=o(n))&&(s[t]=e[n]);return s}var le={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};var de=/\d/,t=/\d\d/,he=/\d{3}/,ce=/\d{4}/,fe=/[+-]?\d{6}/,n=/\d\d?/,me=/\d\d\d\d?/,_e=/\d\d\d\d\d\d?/,ye=/\d{1,3}/,ge=/\d{1,4}/,we=/[+-]?\d{1,6}/,pe=/\d+/,ke=/[+-]?\d+/,Me=/Z|[+-]\d\d:?\d\d/gi,ve=/Z|[+-]\d\d(?::?\d\d)?/gi,i=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,u=/^[1-9]\d?/,d=/^([1-9]\d|\d)/;function h(e,n,s){Ye[e]=a(n)?n:function(e,t){return e&&s?s:n}}function De(e,t){return c(Ye,e)?Ye[e](t._strict,t._locale):new RegExp(f(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,s,i){return t||n||s||i})))}function f(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function m(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function M(e){var e=+e,t=0;return t=0!=e&&isFinite(e)?m(e):t}var Ye={},Se={};function v(e,n){var t,s,i=n;for("string"==typeof e&&(e=[e]),w(n)&&(i=function(e,t){t[n]=M(e)}),s=e.length,t=0;t<s;t++)Se[e[t]]=i}function Oe(e,i){v(e,function(e,t,n,s){n._w=n._w||{},i(e,n._w,n,s)})}function be(e){return e%4==0&&e%100!=0||e%400==0}var D=0,Y=1,S=2,O=3,b=4,T=5,Te=6,xe=7,Ne=8;function We(e){return be(e)?366:365}s("Y",0,0,function(){var e=this.year();return e<=9999?r(e,4):"+"+e}),s(0,["YY",2],0,function(){return this.year()%100}),s(0,["YYYY",4],0,"year"),s(0,["YYYYY",5],0,"year"),s(0,["YYYYYY",6,!0],0,"year"),h("Y",ke),h("YY",n,t),h("YYYY",ge,ce),h("YYYYY",we,fe),h("YYYYYY",we,fe),v(["YYYYY","YYYYYY"],D),v("YYYY",function(e,t){t[D]=2===e.length?_.parseTwoDigitYear(e):M(e)}),v("YY",function(e,t){t[D]=_.parseTwoDigitYear(e)}),v("Y",function(e,t){t[D]=parseInt(e,10)}),_.parseTwoDigitYear=function(e){return M(e)+(68<M(e)?1900:2e3)};var x,Pe=Re("FullYear",!0);function Re(t,n){return function(e){return null!=e?(Ue(this,t,e),_.updateOffset(this,n),this):Ce(this,t)}}function Ce(e,t){if(!e.isValid())return NaN;var n=e._d,s=e._isUTC;switch(t){case"Milliseconds":return s?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return s?n.getUTCSeconds():n.getSeconds();case"Minutes":return s?n.getUTCMinutes():n.getMinutes();case"Hours":return s?n.getUTCHours():n.getHours();case"Date":return s?n.getUTCDate():n.getDate();case"Day":return s?n.getUTCDay():n.getDay();case"Month":return s?n.getUTCMonth():n.getMonth();case"FullYear":return s?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Ue(e,t,n){var s,i,r;if(e.isValid()&&!isNaN(n)){switch(s=e._d,i=e._isUTC,t){case"Milliseconds":return i?s.setUTCMilliseconds(n):s.setMilliseconds(n);case"Seconds":return i?s.setUTCSeconds(n):s.setSeconds(n);case"Minutes":return i?s.setUTCMinutes(n):s.setMinutes(n);case"Hours":return i?s.setUTCHours(n):s.setHours(n);case"Date":return i?s.setUTCDate(n):s.setDate(n);case"FullYear":break;default:return}t=n,r=e.month(),e=29!==(e=e.date())||1!==r||be(t)?e:28,i?s.setUTCFullYear(t,r,e):s.setFullYear(t,r,e)}}function He(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(n=(t%(n=12)+n)%n,e+=(t-n)/12,1==n?be(e)?29:28:31-n%7%2)}x=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},s("M",["MM",2],"Mo",function(){return this.month()+1}),s("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),s("MMMM",0,0,function(e){return this.localeData().months(this,e)}),h("M",n,u),h("MM",n,t),h("MMM",function(e,t){return t.monthsShortRegex(e)}),h("MMMM",function(e,t){return t.monthsRegex(e)}),v(["M","MM"],function(e,t){t[Y]=M(e)-1}),v(["MMM","MMMM"],function(e,t,n,s){s=n._locale.monthsParse(e,s,n._strict);null!=s?t[Y]=s:p(n).invalidMonth=e});var Fe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Le="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ve=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ge=i,Ee=i;function Ae(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=M(t);else if(!w(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,He(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Ie(e){return null!=e?(Ae(this,e),_.updateOffset(this,!0),this):Ce(this,"Month")}function je(){function e(e,t){return t.length-e.length}for(var t,n,s=[],i=[],r=[],a=0;a<12;a++)n=l([2e3,a]),t=f(this.monthsShort(n,"")),n=f(this.months(n,"")),s.push(t),i.push(n),r.push(n),r.push(t);s.sort(e),i.sort(e),r.sort(e),this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Ze(e,t,n,s,i,r,a){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,s,i,r,a),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,s,i,r,a),o}function ze(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function qe(e,t,n){n=7+t-n;return n-(7+ze(e,0,n).getUTCDay()-t)%7-1}function $e(e,t,n,s,i){var r,t=1+7*(t-1)+(7+n-s)%7+qe(e,s,i),n=t<=0?We(r=e-1)+t:t>We(e)?(r=e+1,t-We(e)):(r=e,t);return{year:r,dayOfYear:n}}function Be(e,t,n){var s,i,r=qe(e.year(),t,n),r=Math.floor((e.dayOfYear()-r-1)/7)+1;return r<1?s=r+N(i=e.year()-1,t,n):r>N(e.year(),t,n)?(s=r-N(e.year(),t,n),i=e.year()+1):(i=e.year(),s=r),{week:s,year:i}}function N(e,t,n){var s=qe(e,t,n),t=qe(e+1,t,n);return(We(e)-s+t)/7}s("w",["ww",2],"wo","week"),s("W",["WW",2],"Wo","isoWeek"),h("w",n,u),h("ww",n,t),h("W",n,u),h("WW",n,t),Oe(["w","ww","W","WW"],function(e,t,n,s){t[s.substr(0,1)]=M(e)});function Je(e,t){return e.slice(t,7).concat(e.slice(0,t))}s("d",0,"do","day"),s("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),s("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),s("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),s("e",0,0,"weekday"),s("E",0,0,"isoWeekday"),h("d",n),h("e",n),h("E",n),h("dd",function(e,t){return t.weekdaysMinRegex(e)}),h("ddd",function(e,t){return t.weekdaysShortRegex(e)}),h("dddd",function(e,t){return t.weekdaysRegex(e)}),Oe(["dd","ddd","dddd"],function(e,t,n,s){s=n._locale.weekdaysParse(e,s,n._strict);null!=s?t.d=s:p(n).invalidWeekday=e}),Oe(["d","e","E"],function(e,t,n,s){t[s]=M(e)});var Qe="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Xe="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ke="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),et=i,tt=i,nt=i;function st(){function e(e,t){return t.length-e.length}for(var t,n,s,i=[],r=[],a=[],o=[],u=0;u<7;u++)s=l([2e3,1]).day(u),t=f(this.weekdaysMin(s,"")),n=f(this.weekdaysShort(s,"")),s=f(this.weekdays(s,"")),i.push(t),r.push(n),a.push(s),o.push(t),o.push(n),o.push(s);i.sort(e),r.sort(e),a.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function it(){return this.hours()%12||12}function rt(e,t){s(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function at(e,t){return t._meridiemParse}s("H",["HH",2],0,"hour"),s("h",["hh",2],0,it),s("k",["kk",2],0,function(){return this.hours()||24}),s("hmm",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)}),s("hmmss",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)+r(this.seconds(),2)}),s("Hmm",0,0,function(){return""+this.hours()+r(this.minutes(),2)}),s("Hmmss",0,0,function(){return""+this.hours()+r(this.minutes(),2)+r(this.seconds(),2)}),rt("a",!0),rt("A",!1),h("a",at),h("A",at),h("H",n,d),h("h",n,u),h("k",n,u),h("HH",n,t),h("hh",n,t),h("kk",n,t),h("hmm",me),h("hmmss",_e),h("Hmm",me),h("Hmmss",_e),v(["H","HH"],O),v(["k","kk"],function(e,t,n){e=M(e);t[O]=24===e?0:e}),v(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),v(["h","hh"],function(e,t,n){t[O]=M(e),p(n).bigHour=!0}),v("hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s)),p(n).bigHour=!0}),v("hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i)),p(n).bigHour=!0}),v("Hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s))}),v("Hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i))});i=Re("Hours",!0);var ot,ut={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Fe,monthsShort:Le,week:{dow:0,doy:6},weekdays:Qe,weekdaysMin:Ke,weekdaysShort:Xe,meridiemParse:/[ap]\.?m?\.?/i},W={},lt={};function dt(e){return e&&e.toLowerCase().replace("_","-")}function ht(e){for(var t,n,s,i,r=0;r<e.length;){for(t=(i=dt(e[r]).split("-")).length,n=(n=dt(e[r+1]))?n.split("-"):null;0<t;){if(s=ct(i.slice(0,t).join("-")))return s;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),s=0;s<n;s+=1)if(e[s]!==t[s])return s;return n}(i,n)>=t-1)break;t--}r++}return ot}function ct(t){var e,n;if(void 0===W[t]&&"undefined"!=typeof module&&module&&module.exports&&(n=t)&&n.match("^[^/\\\\]*$"))try{e=ot._abbr,require("./locale/"+t),ft(e)}catch(e){W[t]=null}return W[t]}function ft(e,t){return e&&((t=g(t)?P(e):mt(e,t))?ot=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),ot._abbr}function mt(e,t){if(null===t)return delete W[e],null;var n,s=ut;if(t.abbr=e,null!=W[e])Q("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),s=W[e]._config;else if(null!=t.parentLocale)if(null!=W[t.parentLocale])s=W[t.parentLocale]._config;else{if(null==(n=ct(t.parentLocale)))return lt[t.parentLocale]||(lt[t.parentLocale]=[]),lt[t.parentLocale].push({name:e,config:t}),null;s=n._config}return W[e]=new K(X(s,t)),lt[e]&&lt[e].forEach(function(e){mt(e.name,e.config)}),ft(e),W[e]}function P(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return ot;if(!y(e)){if(t=ct(e))return t;e=[e]}return ht(e)}function _t(e){var t=e._a;return t&&-2===p(e).overflow&&(t=t[Y]<0||11<t[Y]?Y:t[S]<1||t[S]>He(t[D],t[Y])?S:t[O]<0||24<t[O]||24===t[O]&&(0!==t[b]||0!==t[T]||0!==t[Te])?O:t[b]<0||59<t[b]?b:t[T]<0||59<t[T]?T:t[Te]<0||999<t[Te]?Te:-1,p(e)._overflowDayOfYear&&(t<D||S<t)&&(t=S),p(e)._overflowWeeks&&-1===t&&(t=xe),p(e)._overflowWeekday&&-1===t&&(t=Ne),p(e).overflow=t),e}var yt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,gt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,wt=/Z|[+-]\d\d(?::?\d\d)?/,pt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],kt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Mt=/^\/?Date\((-?\d+)/i,vt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Dt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Yt(e){var t,n,s,i,r,a,o=e._i,u=yt.exec(o)||gt.exec(o),o=pt.length,l=kt.length;if(u){for(p(e).iso=!0,t=0,n=o;t<n;t++)if(pt[t][1].exec(u[1])){i=pt[t][0],s=!1!==pt[t][2];break}if(null==i)e._isValid=!1;else{if(u[3]){for(t=0,n=l;t<n;t++)if(kt[t][1].exec(u[3])){r=(u[2]||" ")+kt[t][0];break}if(null==r)return void(e._isValid=!1)}if(s||null==r){if(u[4]){if(!wt.exec(u[4]))return void(e._isValid=!1);a="Z"}e._f=i+(r||"")+(a||""),xt(e)}else e._isValid=!1}}else e._isValid=!1}function St(e,t,n,s,i,r){e=[function(e){e=parseInt(e,10);{if(e<=49)return 2e3+e;if(e<=999)return 1900+e}return e}(e),Le.indexOf(t),parseInt(n,10),parseInt(s,10),parseInt(i,10)];return r&&e.push(parseInt(r,10)),e}function Ot(e){var t,n,s=vt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));s?(t=St(s[4],s[3],s[2],s[5],s[6],s[7]),function(e,t,n){if(!e||Xe.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;p(n).weekdayMismatch=!0,n._isValid=!1}(s[1],t,e)&&(e._a=t,e._tzm=(t=s[8],n=s[9],s=s[10],t?Dt[t]:n?0:60*(((t=parseInt(s,10))-(n=t%100))/100)+n),e._d=ze.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0)):e._isValid=!1}function bt(e,t,n){return null!=e?e:null!=t?t:n}function Tt(e){var t,n,s,i,r,a,o,u,l,d,h,c=[];if(!e._d){for(s=e,i=new Date(_.now()),n=s._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],e._w&&null==e._a[S]&&null==e._a[Y]&&(null!=(i=(s=e)._w).GG||null!=i.W||null!=i.E?(u=1,l=4,r=bt(i.GG,s._a[D],Be(R(),1,4).year),a=bt(i.W,1),((o=bt(i.E,1))<1||7<o)&&(d=!0)):(u=s._locale._week.dow,l=s._locale._week.doy,h=Be(R(),u,l),r=bt(i.gg,s._a[D],h.year),a=bt(i.w,h.week),null!=i.d?((o=i.d)<0||6<o)&&(d=!0):null!=i.e?(o=i.e+u,(i.e<0||6<i.e)&&(d=!0)):o=u),a<1||a>N(r,u,l)?p(s)._overflowWeeks=!0:null!=d?p(s)._overflowWeekday=!0:(h=$e(r,a,o,u,l),s._a[D]=h.year,s._dayOfYear=h.dayOfYear)),null!=e._dayOfYear&&(i=bt(e._a[D],n[D]),(e._dayOfYear>We(i)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),d=ze(i,0,e._dayOfYear),e._a[Y]=d.getUTCMonth(),e._a[S]=d.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=c[t]=n[t];for(;t<7;t++)e._a[t]=c[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[O]&&0===e._a[b]&&0===e._a[T]&&0===e._a[Te]&&(e._nextDay=!0,e._a[O]=0),e._d=(e._useUTC?ze:Ze).apply(null,c),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[O]=24),e._w&&void 0!==e._w.d&&e._w.d!==r&&(p(e).weekdayMismatch=!0)}}function xt(e){if(e._f===_.ISO_8601)Yt(e);else if(e._f===_.RFC_2822)Ot(e);else{e._a=[],p(e).empty=!0;for(var t,n,s,i,r,a=""+e._i,o=a.length,u=0,l=ae(e._f,e._locale).match(te)||[],d=l.length,h=0;h<d;h++)n=l[h],(t=(a.match(De(n,e))||[])[0])&&(0<(s=a.substr(0,a.indexOf(t))).length&&p(e).unusedInput.push(s),a=a.slice(a.indexOf(t)+t.length),u+=t.length),ie[n]?(t?p(e).empty=!1:p(e).unusedTokens.push(n),s=n,r=e,null!=(i=t)&&c(Se,s)&&Se[s](i,r._a,r,s)):e._strict&&!t&&p(e).unusedTokens.push(n);p(e).charsLeftOver=o-u,0<a.length&&p(e).unusedInput.push(a),e._a[O]<=12&&!0===p(e).bigHour&&0<e._a[O]&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[O]=function(e,t,n){if(null==n)return t;return null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[O],e._meridiem),null!==(o=p(e).era)&&(e._a[D]=e._locale.erasConvertYear(o,e._a[D])),Tt(e),_t(e)}}function Nt(e){var t,n,s,i=e._i,r=e._f;if(e._locale=e._locale||P(e._l),null===i||void 0===r&&""===i)return I({nullInput:!0});if("string"==typeof i&&(e._i=i=e._locale.preparse(i)),k(i))return new $(_t(i));if(V(i))e._d=i;else if(y(r)){var a,o,u,l,d,h,c=e,f=!1,m=c._f.length;if(0===m)p(c).invalidFormat=!0,c._d=new Date(NaN);else{for(l=0;l<m;l++)d=0,h=!1,a=q({},c),null!=c._useUTC&&(a._useUTC=c._useUTC),a._f=c._f[l],xt(a),A(a)&&(h=!0),d=(d+=p(a).charsLeftOver)+10*p(a).unusedTokens.length,p(a).score=d,f?d<u&&(u=d,o=a):(null==u||d<u||h)&&(u=d,o=a,h)&&(f=!0);E(c,o||a)}}else if(r)xt(e);else if(g(r=(i=e)._i))i._d=new Date(_.now());else V(r)?i._d=new Date(r.valueOf()):"string"==typeof r?(n=i,null!==(t=Mt.exec(n._i))?n._d=new Date(+t[1]):(Yt(n),!1===n._isValid&&(delete n._isValid,Ot(n),!1===n._isValid)&&(delete n._isValid,n._strict?n._isValid=!1:_.createFromInputFallback(n)))):y(r)?(i._a=G(r.slice(0),function(e){return parseInt(e,10)}),Tt(i)):F(r)?(t=i)._d||(s=void 0===(n=ue(t._i)).day?n.date:n.day,t._a=G([n.year,n.month,s,n.hour,n.minute,n.second,n.millisecond],function(e){return e&&parseInt(e,10)}),Tt(t)):w(r)?i._d=new Date(r):_.createFromInputFallback(i);return A(e)||(e._d=null),e}function Wt(e,t,n,s,i){var r={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==n&&!1!==n||(s=n,n=void 0),(F(e)&&L(e)||y(e)&&0===e.length)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=n,r._i=e,r._f=t,r._strict=s,(i=new $(_t(Nt(i=r))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function R(e,t,n,s){return Wt(e,t,n,s,!1)}_.createFromInputFallback=e("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),_.ISO_8601=function(){},_.RFC_2822=function(){};me=e("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:I()}),_e=e("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:I()});function Pt(e,t){var n,s;if(!(t=1===t.length&&y(t[0])?t[0]:t).length)return R();for(n=t[0],s=1;s<t.length;++s)t[s].isValid()&&!t[s][e](n)||(n=t[s]);return n}var Rt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ct(e){var e=ue(e),t=e.year||0,n=e.quarter||0,s=e.month||0,i=e.week||e.isoWeek||0,r=e.day||0,a=e.hour||0,o=e.minute||0,u=e.second||0,l=e.millisecond||0;this._isValid=function(e){var t,n,s=!1,i=Rt.length;for(t in e)if(c(e,t)&&(-1===x.call(Rt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<i;++n)if(e[Rt[n]]){if(s)return!1;parseFloat(e[Rt[n]])!==M(e[Rt[n]])&&(s=!0)}return!0}(e),this._milliseconds=+l+1e3*u+6e4*o+1e3*a*60*60,this._days=+r+7*i,this._months=+s+3*n+12*t,this._data={},this._locale=P(),this._bubble()}function Ut(e){return e instanceof Ct}function Ht(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ft(e,n){s(e,0,0,function(){var e=this.utcOffset(),t="+";return e<0&&(e=-e,t="-"),t+r(~~(e/60),2)+n+r(~~e%60,2)})}Ft("Z",":"),Ft("ZZ",""),h("Z",ve),h("ZZ",ve),v(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Vt(ve,e)});var Lt=/([\+\-]|\d\d)/gi;function Vt(e,t){var t=(t||"").match(e);return null===t?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Lt)||["-",0,0])[1]+M(e[2]))?0:"+"===e[0]?t:-t}function Gt(e,t){var n;return t._isUTC?(t=t.clone(),n=(k(e)||V(e)?e:R(e)).valueOf()-t.valueOf(),t._d.setTime(t._d.valueOf()+n),_.updateOffset(t,!1),t):R(e).local()}function Et(e){return-Math.round(e._d.getTimezoneOffset())}function At(){return!!this.isValid()&&this._isUTC&&0===this._offset}_.updateOffset=function(){};var It=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,jt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function C(e,t){var n,s=e;return Ut(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:w(e)||!isNaN(+e)?(s={},t?s[t]=+e:s.milliseconds=+e):(t=It.exec(e))?(n="-"===t[1]?-1:1,s={y:0,d:M(t[S])*n,h:M(t[O])*n,m:M(t[b])*n,s:M(t[T])*n,ms:M(Ht(1e3*t[Te]))*n}):(t=jt.exec(e))?(n="-"===t[1]?-1:1,s={y:Zt(t[2],n),M:Zt(t[3],n),w:Zt(t[4],n),d:Zt(t[5],n),h:Zt(t[6],n),m:Zt(t[7],n),s:Zt(t[8],n)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(t=function(e,t){var n;if(!e.isValid()||!t.isValid())return{milliseconds:0,months:0};t=Gt(t,e),e.isBefore(t)?n=zt(e,t):((n=zt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months);return n}(R(s.from),R(s.to)),(s={}).ms=t.milliseconds,s.M=t.months),n=new Ct(s),Ut(e)&&c(e,"_locale")&&(n._locale=e._locale),Ut(e)&&c(e,"_isValid")&&(n._isValid=e._isValid),n}function Zt(e,t){e=e&&parseFloat(e.replace(",","."));return(isNaN(e)?0:e)*t}function zt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function qt(s,i){return function(e,t){var n;return null===t||isNaN(+t)||(Q(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),n=e,e=t,t=n),$t(this,C(e,t),s),this}}function $t(e,t,n,s){var i=t._milliseconds,r=Ht(t._days),t=Ht(t._months);e.isValid()&&(s=null==s||s,t&&Ae(e,Ce(e,"Month")+t*n),r&&Ue(e,"Date",Ce(e,"Date")+r*n),i&&e._d.setTime(e._d.valueOf()+i*n),s)&&_.updateOffset(e,r||t)}C.fn=Ct.prototype,C.invalid=function(){return C(NaN)};Fe=qt(1,"add"),Qe=qt(-1,"subtract");function Bt(e){return"string"==typeof e||e instanceof String}function Jt(e){return k(e)||V(e)||Bt(e)||w(e)||function(t){var e=y(t),n=!1;e&&(n=0===t.filter(function(e){return!w(e)&&Bt(t)}).length);return e&&n}(e)||function(e){var t,n,s=F(e)&&!L(e),i=!1,r=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],a=r.length;for(t=0;t<a;t+=1)n=r[t],i=i||c(e,n);return s&&i}(e)||null==e}function Qt(e,t){var n,s;return e.date()<t.date()?-Qt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(s=e.clone().add(n,"months"))<0?(t-s)/(s-e.clone().add(n-1,"months")):(t-s)/(e.clone().add(1+n,"months")-s)))||0}function Xt(e){return void 0===e?this._locale._abbr:(null!=(e=P(e))&&(this._locale=e),this)}_.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",_.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";Ke=e("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});function Kt(){return this._locale}var en=126227808e5;function tn(e,t){return(e%t+t)%t}function nn(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-en:new Date(e,t,n).valueOf()}function sn(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-en:Date.UTC(e,t,n)}function rn(e,t){return t.erasAbbrRegex(e)}function an(){for(var e,t,n,s=[],i=[],r=[],a=[],o=this.eras(),u=0,l=o.length;u<l;++u)e=f(o[u].name),t=f(o[u].abbr),n=f(o[u].narrow),i.push(e),s.push(t),r.push(n),a.push(e),a.push(t),a.push(n);this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function on(e,t){s(0,[e,e.length],0,t)}function un(e,t,n,s,i){var r;return null==e?Be(this,s,i).year:(r=N(e,s,i),function(e,t,n,s,i){e=$e(e,t,n,s,i),t=ze(e.year,0,e.dayOfYear);return this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,s,i))}s("N",0,0,"eraAbbr"),s("NN",0,0,"eraAbbr"),s("NNN",0,0,"eraAbbr"),s("NNNN",0,0,"eraName"),s("NNNNN",0,0,"eraNarrow"),s("y",["y",1],"yo","eraYear"),s("y",["yy",2],0,"eraYear"),s("y",["yyy",3],0,"eraYear"),s("y",["yyyy",4],0,"eraYear"),h("N",rn),h("NN",rn),h("NNN",rn),h("NNNN",function(e,t){return t.erasNameRegex(e)}),h("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),v(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,s){s=n._locale.erasParse(e,s,n._strict);s?p(n).era=s:p(n).invalidEra=e}),h("y",pe),h("yy",pe),h("yyy",pe),h("yyyy",pe),h("yo",function(e,t){return t._eraYearOrdinalRegex||pe}),v(["y","yy","yyy","yyyy"],D),v(["yo"],function(e,t,n,s){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[D]=n._locale.eraYearOrdinalParse(e,i):t[D]=parseInt(e,10)}),s(0,["gg",2],0,function(){return this.weekYear()%100}),s(0,["GG",2],0,function(){return this.isoWeekYear()%100}),on("gggg","weekYear"),on("ggggg","weekYear"),on("GGGG","isoWeekYear"),on("GGGGG","isoWeekYear"),h("G",ke),h("g",ke),h("GG",n,t),h("gg",n,t),h("GGGG",ge,ce),h("gggg",ge,ce),h("GGGGG",we,fe),h("ggggg",we,fe),Oe(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,s){t[s.substr(0,2)]=M(e)}),Oe(["gg","GG"],function(e,t,n,s){t[s]=_.parseTwoDigitYear(e)}),s("Q",0,"Qo","quarter"),h("Q",de),v("Q",function(e,t){t[Y]=3*(M(e)-1)}),s("D",["DD",2],"Do","date"),h("D",n,u),h("DD",n,t),h("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),v(["D","DD"],S),v("Do",function(e,t){t[S]=M(e.match(n)[0])});ge=Re("Date",!0);s("DDD",["DDDD",3],"DDDo","dayOfYear"),h("DDD",ye),h("DDDD",he),v(["DDD","DDDD"],function(e,t,n){n._dayOfYear=M(e)}),s("m",["mm",2],0,"minute"),h("m",n,d),h("mm",n,t),v(["m","mm"],b);var ln,ce=Re("Minutes",!1),we=(s("s",["ss",2],0,"second"),h("s",n,d),h("ss",n,t),v(["s","ss"],T),Re("Seconds",!1));for(s("S",0,0,function(){return~~(this.millisecond()/100)}),s(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),s(0,["SSS",3],0,"millisecond"),s(0,["SSSS",4],0,function(){return 10*this.millisecond()}),s(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),s(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),s(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),s(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),s(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),h("S",ye,de),h("SS",ye,t),h("SSS",ye,he),ln="SSSS";ln.length<=9;ln+="S")h(ln,pe);function dn(e,t){t[Te]=M(1e3*("0."+e))}for(ln="S";ln.length<=9;ln+="S")v(ln,dn);fe=Re("Milliseconds",!1),s("z",0,0,"zoneAbbr"),s("zz",0,0,"zoneName");u=$.prototype;function hn(e){return e}u.add=Fe,u.calendar=function(e,t){1===arguments.length&&(arguments[0]?Jt(arguments[0])?(e=arguments[0],t=void 0):function(e){for(var t=F(e)&&!L(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i=0;i<s.length;i+=1)n=n||c(e,s[i]);return t&&n}(arguments[0])&&(t=arguments[0],e=void 0):t=e=void 0);var e=e||R(),n=Gt(e,this).startOf("day"),n=_.calendarFormat(this,n)||"sameElse",t=t&&(a(t[n])?t[n].call(this,e):t[n]);return this.format(t||this.localeData().calendar(n,this,R(e)))},u.clone=function(){return new $(this)},u.diff=function(e,t,n){var s,i,r;if(!this.isValid())return NaN;if(!(s=Gt(e,this)).isValid())return NaN;switch(i=6e4*(s.utcOffset()-this.utcOffset()),t=o(t)){case"year":r=Qt(this,s)/12;break;case"month":r=Qt(this,s);break;case"quarter":r=Qt(this,s)/3;break;case"second":r=(this-s)/1e3;break;case"minute":r=(this-s)/6e4;break;case"hour":r=(this-s)/36e5;break;case"day":r=(this-s-i)/864e5;break;case"week":r=(this-s-i)/6048e5;break;default:r=this-s}return n?r:m(r)},u.endOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-tn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-tn(t,1e3)-1;break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.format=function(e){return e=e||(this.isUtc()?_.defaultFormatUtc:_.defaultFormat),e=re(this,e),this.localeData().postformat(e)},u.from=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.fromNow=function(e){return this.from(R(),e)},u.to=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.toNow=function(e){return this.to(R(),e)},u.get=function(e){return a(this[e=o(e)])?this[e]():this},u.invalidAt=function(){return p(this).overflow},u.isAfter=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},u.isBefore=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},u.isBetween=function(e,t,n,s){return e=k(e)?e:R(e),t=k(t)?t:R(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(s=s||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===s[1]?this.isBefore(t,n):!this.isAfter(t,n))},u.isSame=function(e,t){var e=k(e)?e:R(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},u.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},u.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},u.isValid=function(){return A(this)},u.lang=Ke,u.locale=Xt,u.localeData=Kt,u.max=_e,u.min=me,u.parsingFlags=function(){return E({},p(this))},u.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)c(e,t)&&n.push({unit:t,priority:le[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=ue(e)),s=n.length,i=0;i<s;i++)this[n[i].unit](e[n[i].unit]);else if(a(this[e=o(e)]))return this[e](t);return this},u.startOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=tn(t,6e4);break;case"second":t=this._d.valueOf(),t-=tn(t,1e3);break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.subtract=Qe,u.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},u.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},u.toDate=function(){return new Date(this.valueOf())},u.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?re(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):a(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",re(t,"Z")):re(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},u.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+(e+'[")]'))):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(u[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),u.toJSON=function(){return this.isValid()?this.toISOString():null},u.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},u.unix=function(){return Math.floor(this.valueOf()/1e3)},u.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},u.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},u.eraName=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},u.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},u.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},u.eraYear=function(){for(var e,t,n=this.localeData().eras(),s=0,i=n.length;s<i;++s)if(e=n[s].since<=n[s].until?1:-1,t=this.clone().startOf("day").valueOf(),n[s].since<=t&&t<=n[s].until||n[s].until<=t&&t<=n[s].since)return(this.year()-_(n[s].since).year())*e+n[s].offset;return this.year()},u.year=Pe,u.isLeapYear=function(){return be(this.year())},u.weekYear=function(e){return un.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},u.isoWeekYear=function(e){return un.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},u.quarter=u.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},u.month=Ie,u.daysInMonth=function(){return He(this.year(),this.month())},u.week=u.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},u.isoWeek=u.isoWeeks=function(e){var t=Be(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},u.weeksInYear=function(){var e=this.localeData()._week;return N(this.year(),e.dow,e.doy)},u.weeksInWeekYear=function(){var e=this.localeData()._week;return N(this.weekYear(),e.dow,e.doy)},u.isoWeeksInYear=function(){return N(this.year(),1,4)},u.isoWeeksInISOWeekYear=function(){return N(this.isoWeekYear(),1,4)},u.date=ge,u.day=u.days=function(e){var t,n,s;return this.isValid()?(t=Ce(this,"Day"),null!=e?(n=e,s=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=s.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},u.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},u.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},u.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},u.hour=u.hours=i,u.minute=u.minutes=ce,u.second=u.seconds=we,u.millisecond=u.milliseconds=fe,u.utcOffset=function(e,t,n){var s,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?i:Et(this);if("string"==typeof e){if(null===(e=Vt(ve,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(s=Et(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==e&&(!t||this._changeInProgress?$t(this,C(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,_.updateOffset(this,!0),this._changeInProgress=null)),this},u.utc=function(e){return this.utcOffset(0,e)},u.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Et(this),"m"),this},u.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Vt(Me,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},u.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?R(e).utcOffset():0,(this.utcOffset()-e)%60==0)},u.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},u.isLocal=function(){return!!this.isValid()&&!this._isUTC},u.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},u.isUtc=At,u.isUTC=At,u.zoneAbbr=function(){return this._isUTC?"UTC":""},u.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},u.dates=e("dates accessor is deprecated. Use date instead.",ge),u.months=e("months accessor is deprecated. Use month instead",Ie),u.years=e("years accessor is deprecated. Use year instead",Pe),u.zone=e("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),u.isDSTShifted=e("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){var e,t;return g(this._isDSTShifted)&&(q(e={},this),(e=Nt(e))._a?(t=(e._isUTC?l:R)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0,a=0;a<s;a++)(n&&e[a]!==t[a]||!n&&M(e[a])!==M(t[a]))&&r++;return r+i}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted});d=K.prototype;function cn(e,t,n,s){var i=P(),s=l().set(s,t);return i[n](s,e)}function fn(e,t,n){if(w(e)&&(t=e,e=void 0),e=e||"",null!=t)return cn(e,t,n,"month");for(var s=[],i=0;i<12;i++)s[i]=cn(e,i,n,"month");return s}function mn(e,t,n,s){t=("boolean"==typeof e?w(t)&&(n=t,t=void 0):(t=e,e=!1,w(n=t)&&(n=t,t=void 0)),t||"");var i,r=P(),a=e?r._week.dow:0,o=[];if(null!=n)return cn(t,(n+a)%7,s,"day");for(i=0;i<7;i++)o[i]=cn(t,(i+a)%7,s,"day");return o}d.calendar=function(e,t,n){return a(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},d.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(te).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},d.invalidDate=function(){return this._invalidDate},d.ordinal=function(e){return this._ordinal.replace("%d",e)},d.preparse=hn,d.postformat=hn,d.relativeTime=function(e,t,n,s){var i=this._relativeTime[n];return a(i)?i(e,t,n,s):i.replace(/%d/i,e)},d.pastFuture=function(e,t){return a(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},d.set=function(e){var t,n;for(n in e)c(e,n)&&(a(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},d.eras=function(e,t){for(var n,s=this._eras||P("en")._eras,i=0,r=s.length;i<r;++i){switch(typeof s[i].since){case"string":n=_(s[i].since).startOf("day"),s[i].since=n.valueOf();break}switch(typeof s[i].until){case"undefined":s[i].until=1/0;break;case"string":n=_(s[i].until).startOf("day").valueOf(),s[i].until=n.valueOf();break}}return s},d.erasParse=function(e,t,n){var s,i,r,a,o,u=this.eras();for(e=e.toUpperCase(),s=0,i=u.length;s<i;++s)if(r=u[s].name.toUpperCase(),a=u[s].abbr.toUpperCase(),o=u[s].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(a===e)return u[s];break;case"NNNN":if(r===e)return u[s];break;case"NNNNN":if(o===e)return u[s];break}else if(0<=[r,a,o].indexOf(e))return u[s]},d.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?_(e.since).year():_(e.since).year()+(t-e.offset)*n},d.erasAbbrRegex=function(e){return c(this,"_erasAbbrRegex")||an.call(this),e?this._erasAbbrRegex:this._erasRegex},d.erasNameRegex=function(e){return c(this,"_erasNameRegex")||an.call(this),e?this._erasNameRegex:this._erasRegex},d.erasNarrowRegex=function(e){return c(this,"_erasNarrowRegex")||an.call(this),e?this._erasNarrowRegex:this._erasRegex},d.months=function(e,t){return e?(y(this._months)?this._months:this._months[(this._months.isFormat||Ve).test(t)?"format":"standalone"])[e.month()]:y(this._months)?this._months:this._months.standalone},d.monthsShort=function(e,t){return e?(y(this._monthsShort)?this._monthsShort:this._monthsShort[Ve.test(t)?"format":"standalone"])[e.month()]:y(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},d.monthsParse=function(e,t,n){var s,i;if(this._monthsParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],s=0;s<12;++s)r=l([2e3,s]),this._shortMonthsParse[s]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[s]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))||-1!==(i=x.call(this._longMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))||-1!==(i=x.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),s=0;s<12;s++){if(i=l([2e3,s]),n&&!this._longMonthsParse[s]&&(this._longMonthsParse[s]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[s]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[s]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[s]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[s].test(e))return s;if(n&&"MMM"===t&&this._shortMonthsParse[s].test(e))return s;if(!n&&this._monthsParse[s].test(e))return s}},d.monthsRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsStrictRegex:this._monthsRegex):(c(this,"_monthsRegex")||(this._monthsRegex=Ee),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},d.monthsShortRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(c(this,"_monthsShortRegex")||(this._monthsShortRegex=Ge),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},d.week=function(e){return Be(e,this._week.dow,this._week.doy).week},d.firstDayOfYear=function(){return this._week.doy},d.firstDayOfWeek=function(){return this._week.dow},d.weekdays=function(e,t){return t=y(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?Je(t,this._week.dow):e?t[e.day()]:t},d.weekdaysMin=function(e){return!0===e?Je(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},d.weekdaysShort=function(e){return!0===e?Je(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},d.weekdaysParse=function(e,t,n){var s,i;if(this._weekdaysParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],s=0;s<7;++s)r=l([2e3,1]).day(s),this._minWeekdaysParse[s]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[s]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[s]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),s=0;s<7;s++){if(i=l([2e3,1]).day(s),n&&!this._fullWeekdaysParse[s]&&(this._fullWeekdaysParse[s]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[s]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[s]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[s]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[s]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[s].test(e))return s;if(n&&"ddd"===t&&this._shortWeekdaysParse[s].test(e))return s;if(n&&"dd"===t&&this._minWeekdaysParse[s].test(e))return s;if(!n&&this._weekdaysParse[s].test(e))return s}},d.weekdaysRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(c(this,"_weekdaysRegex")||(this._weekdaysRegex=et),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},d.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(c(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=tt),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},d.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(c(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=nt),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},d.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},d.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},ft("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===M(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),_.lang=e("moment.lang is deprecated. Use moment.locale instead.",ft),_.langData=e("moment.langData is deprecated. Use moment.localeData instead.",P);var _n=Math.abs;function yn(e,t,n,s){t=C(t,n);return e._milliseconds+=s*t._milliseconds,e._days+=s*t._days,e._months+=s*t._months,e._bubble()}function gn(e){return e<0?Math.floor(e):Math.ceil(e)}function wn(e){return 4800*e/146097}function pn(e){return 146097*e/4800}function kn(e){return function(){return this.as(e)}}de=kn("ms"),t=kn("s"),ye=kn("m"),he=kn("h"),Fe=kn("d"),_e=kn("w"),me=kn("M"),Qe=kn("Q"),i=kn("y"),ce=de;function Mn(e){return function(){return this.isValid()?this._data[e]:NaN}}var we=Mn("milliseconds"),fe=Mn("seconds"),ge=Mn("minutes"),Pe=Mn("hours"),d=Mn("days"),vn=Mn("months"),Dn=Mn("years");var Yn=Math.round,Sn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function On(e,t,n,s){var i=C(e).abs(),r=Yn(i.as("s")),a=Yn(i.as("m")),o=Yn(i.as("h")),u=Yn(i.as("d")),l=Yn(i.as("M")),d=Yn(i.as("w")),i=Yn(i.as("y")),r=(r<=n.ss?["s",r]:r<n.s&&["ss",r])||(a<=1?["m"]:a<n.m&&["mm",a])||(o<=1?["h"]:o<n.h&&["hh",o])||(u<=1?["d"]:u<n.d&&["dd",u]);return(r=(r=null!=n.w?r||(d<=1?["w"]:d<n.w&&["ww",d]):r)||(l<=1?["M"]:l<n.M&&["MM",l])||(i<=1?["y"]:["yy",i]))[2]=t,r[3]=0<+e,r[4]=s,function(e,t,n,s,i){return i.relativeTime(t||1,!!n,e,s)}.apply(null,r)}var bn=Math.abs;function Tn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,s,i,r,a,o,u,l,d;return this.isValid()?(e=bn(this._milliseconds)/1e3,t=bn(this._days),n=bn(this._months),(o=this.asSeconds())?(s=m(e/60),i=m(s/60),e%=60,s%=60,r=m(n/12),n%=12,a=e?e.toFixed(3).replace(/\.?0+$/,""):"",u=Tn(this._months)!==Tn(o)?"-":"",l=Tn(this._days)!==Tn(o)?"-":"",d=Tn(this._milliseconds)!==Tn(o)?"-":"",(o<0?"-":"")+"P"+(r?u+r+"Y":"")+(n?u+n+"M":"")+(t?l+t+"D":"")+(i||s||e?"T":"")+(i?d+i+"H":"")+(s?d+s+"M":"")+(e?d+a+"S":"")):"P0D"):this.localeData().invalidDate()}var U=Ct.prototype;return U.isValid=function(){return this._isValid},U.abs=function(){var e=this._data;return this._milliseconds=_n(this._milliseconds),this._days=_n(this._days),this._months=_n(this._months),e.milliseconds=_n(e.milliseconds),e.seconds=_n(e.seconds),e.minutes=_n(e.minutes),e.hours=_n(e.hours),e.months=_n(e.months),e.years=_n(e.years),this},U.add=function(e,t){return yn(this,e,t,1)},U.subtract=function(e,t){return yn(this,e,t,-1)},U.as=function(e){if(!this.isValid())return NaN;var t,n,s=this._milliseconds;if("month"===(e=o(e))||"quarter"===e||"year"===e)switch(t=this._days+s/864e5,n=this._months+wn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(pn(this._months)),e){case"week":return t/7+s/6048e5;case"day":return t+s/864e5;case"hour":return 24*t+s/36e5;case"minute":return 1440*t+s/6e4;case"second":return 86400*t+s/1e3;case"millisecond":return Math.floor(864e5*t)+s;default:throw new Error("Unknown unit "+e)}},U.asMilliseconds=de,U.asSeconds=t,U.asMinutes=ye,U.asHours=he,U.asDays=Fe,U.asWeeks=_e,U.asMonths=me,U.asQuarters=Qe,U.asYears=i,U.valueOf=ce,U._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,s=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*gn(pn(n)+t),n=t=0),s.milliseconds=e%1e3,e=m(e/1e3),s.seconds=e%60,e=m(e/60),s.minutes=e%60,e=m(e/60),s.hours=e%24,t+=m(e/24),n+=e=m(wn(t)),t-=gn(pn(e)),e=m(n/12),n%=12,s.days=t,s.months=n,s.years=e,this},U.clone=function(){return C(this)},U.get=function(e){return e=o(e),this.isValid()?this[e+"s"]():NaN},U.milliseconds=we,U.seconds=fe,U.minutes=ge,U.hours=Pe,U.days=d,U.weeks=function(){return m(this.days()/7)},U.months=vn,U.years=Dn,U.humanize=function(e,t){var n,s;return this.isValid()?(n=!1,s=Sn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(s=Object.assign({},Sn,t),null!=t.s)&&null==t.ss&&(s.ss=t.s-1),e=this.localeData(),t=On(this,!n,s,e),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},U.toISOString=xn,U.toString=xn,U.toJSON=xn,U.locale=Xt,U.localeData=Kt,U.toIsoString=e("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),U.lang=Ke,s("X",0,0,"unix"),s("x",0,0,"valueOf"),h("x",ke),h("X",/[+-]?\d+(\.\d{1,3})?/),v("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),v("x",function(e,t,n){n._d=new Date(M(e))}),_.version="2.30.1",H=R,_.fn=u,_.min=function(){return Pt("isBefore",[].slice.call(arguments,0))},_.max=function(){return Pt("isAfter",[].slice.call(arguments,0))},_.now=function(){return Date.now?Date.now():+new Date},_.utc=l,_.unix=function(e){return R(1e3*e)},_.months=function(e,t){return fn(e,t,"months")},_.isDate=V,_.locale=ft,_.invalid=I,_.duration=C,_.isMoment=k,_.weekdays=function(e,t,n){return mn(e,t,n,"weekdays")},_.parseZone=function(){return R.apply(null,arguments).parseZone()},_.localeData=P,_.isDuration=Ut,_.monthsShort=function(e,t){return fn(e,t,"monthsShort")},_.weekdaysMin=function(e,t,n){return mn(e,t,n,"weekdaysMin")},_.defineLocale=mt,_.updateLocale=function(e,t){var n,s;return null!=t?(s=ut,null!=W[e]&&null!=W[e].parentLocale?W[e].set(X(W[e]._config,t)):(t=X(s=null!=(n=ct(e))?n._config:s,t),null==n&&(t.abbr=e),(s=new K(t)).parentLocale=W[e],W[e]=s),ft(e)):null!=W[e]&&(null!=W[e].parentLocale?(W[e]=W[e].parentLocale,e===ft()&&ft(e)):null!=W[e]&&delete W[e]),W[e]},_.locales=function(){return ee(W)},_.weekdaysShort=function(e,t,n){return mn(e,t,n,"weekdaysShort")},_.normalizeUnits=o,_.relativeTimeRounding=function(e){return void 0===e?Yn:"function"==typeof e&&(Yn=e,!0)},_.relativeTimeThreshold=function(e,t){return void 0!==Sn[e]&&(void 0===t?Sn[e]:(Sn[e]=t,"s"===e&&(Sn.ss=t-1),!0))},_.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},_.prototype=u,_.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},_});
//# sourceMappingURL=moment.min.js.map
/*
 MIT
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
*****************************************************************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(m){var p=0;return function(){return p<m.length?{done:!1,value:m[p++]}:{done:!0}}};$jscomp.arrayIterator=function(m){return{next:$jscomp.arrayIteratorImpl(m)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(m,p,t){if(m==Array.prototype||m==Object.prototype)return m;m[p]=t.value;return m};$jscomp.getGlobal=function(m){m=["object"==typeof globalThis&&globalThis,m,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var p=0;p<m.length;++p){var t=m[p];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(m,p){var t=$jscomp.propertyToPolyfillSymbol[p];if(null==t)return m[p];t=m[t];return void 0!==t?t:m[p]};
$jscomp.polyfill=function(m,p,t,u){p&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(m,p,t,u):$jscomp.polyfillUnisolated(m,p,t,u))};$jscomp.polyfillUnisolated=function(m,p,t,u){t=$jscomp.global;m=m.split(".");for(u=0;u<m.length-1;u++){var v=m[u];if(!(v in t))return;t=t[v]}m=m[m.length-1];u=t[m];p=p(u);p!=u&&null!=p&&$jscomp.defineProperty(t,m,{configurable:!0,writable:!0,value:p})};
$jscomp.polyfillIsolated=function(m,p,t,u){var v=m.split(".");m=1===v.length;u=v[0];u=!m&&u in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var z=0;z<v.length-1;z++){var A=v[z];if(!(A in u))return;u=u[A]}v=v[v.length-1];t=$jscomp.IS_SYMBOL_NATIVE&&"es6"===t?u[v]:null;p=p(t);null!=p&&(m?$jscomp.defineProperty($jscomp.polyfills,v,{configurable:!0,writable:!0,value:p}):p!==t&&($jscomp.propertyToPolyfillSymbol[v]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(v):$jscomp.POLYFILL_PREFIX+v,v=
$jscomp.propertyToPolyfillSymbol[v],$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:p})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(m){if(m)return m;var p=function(v,z){this.$jscomp$symbol$id_=v;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:z})};p.prototype.toString=function(){return this.$jscomp$symbol$id_};var t=0,u=function(v){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return new p("jscomp_symbol_"+(v||"")+"_"+t++,v)};return u},"es6","es3");$jscomp.initSymbolIterator=function(){};
$jscomp.polyfill("Symbol.iterator",function(m){if(m)return m;m=Symbol("Symbol.iterator");for(var p="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<p.length;t++){var u=$jscomp.global[p[t]];"function"===typeof u&&"function"!=typeof u.prototype[m]&&$jscomp.defineProperty(u.prototype,m,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return m},"es6",
"es3");$jscomp.initSymbolAsyncIterator=function(){};$jscomp.iteratorPrototype=function(m){m={next:m};m[Symbol.iterator]=function(){return this};return m};
(function(m,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("vue")):"function"===typeof define&&define.amd?define(["exports","vue"],p):(m=m||self,p(m.VeeValidate={},m.Vue))})(this,function(m,p){function t(a,b,c,d){return new (c||(c=Promise))(function(e,k){function h(g){try{l(d.next(g))}catch(n){k(n)}}function f(g){try{l(d["throw"](g))}catch(n){k(n)}}function l(g){g.done?e(g.value):(new c(function(n){n(g.value)})).then(h,f)}l((d=d.apply(a,b||[])).next())})}function u(a,
b){function c(g){return function(n){return d([g,n])}}function d(g){if(k)throw new TypeError("Generator is already executing.");for(;e;)try{if(k=1,h&&(f=g[0]&2?h["return"]:g[0]?h["throw"]||((f=h["return"])&&f.call(h),0):h.next)&&!(f=f.call(h,g[1])).done)return f;if(h=0,f)g=[g[0]&2,f.value];switch(g[0]){case 0:case 1:f=g;break;case 4:return e.label++,{value:g[1],done:!1};case 5:e.label++;h=g[1];g=[0];continue;case 7:g=e.ops.pop();e.trys.pop();continue;default:if(!(f=e.trys,f=0<f.length&&f[f.length-
1])&&(6===g[0]||2===g[0])){e=0;continue}if(3===g[0]&&(!f||g[1]>f[0]&&g[1]<f[3]))e.label=g[1];else if(6===g[0]&&e.label<f[1])e.label=f[1],f=g;else if(f&&e.label<f[2])e.label=f[2],e.ops.push(g);else{f[2]&&e.ops.pop();e.trys.pop();continue}}g=b.call(a,e)}catch(n){g=[6,n],h=0}finally{k=f=0}if(g[0]&5)throw g[1];return{value:g[0]?g[1]:void 0,done:!0}}var e={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},k,h,f,l;return l={next:c(0),"throw":c(1),"return":c(2)},"function"===typeof Symbol&&
(l[Symbol.iterator]=function(){return this}),l}function v(){for(var a=0,b=0,c=arguments.length;b<c;b++)a+=arguments[b].length;a=Array(a);var d=0;for(b=0;b<c;b++)for(var e=arguments[b],k=0,h=e.length;k<h;k++,d++)a[d]=e[k];return a}function z(a){return null===a||void 0===a}function A(a,b){if(a instanceof RegExp&&b instanceof RegExp)return A(a.source,b.source)&&A(a.flags,b.flags);if(Array.isArray(a)&&Array.isArray(b)){if(a.length!==b.length)return!1;for(var c=0;c<a.length;c++)if(!A(a[c],b[c]))return!1;
return!0}return B(a)&&B(b)?Object.keys(a).every(function(d){return A(a[d],b[d])})&&Object.keys(b).every(function(d){return A(a[d],b[d])}):a!==a&&b!==b?!0:a===b}function Z(a){return""===a?!1:!z(a)}function y(a){return"function"===typeof a}function E(a){return y(a)&&!!a.__locatorRef}function aa(a,b){var c=Array.isArray(a)?a:M(a);if(y(c.findIndex))return c.findIndex(b);for(var d=0;d<c.length;d++)if(b(c[d],d))return d;return-1}function za(a,b){var c=Array.isArray(a)?a:M(a),d=aa(c,b);return-1===d?void 0:
c[d]}function F(a,b){return-1!==a.indexOf(b)}function M(a){if(y(Array.from))return Array.from(a);for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function N(a){return y(Object.values)?Object.values(a):Object.keys(a).map(function(b){return a[b]})}function O(a,b){Object.keys(b).forEach(function(c){B(b[c])?(a[c]||(a[c]={}),O(a[c],b[c])):a[c]=b[c]});return a}function P(){return{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,
failed:!1}}function Aa(a){return a}function ba(a,b,c){void 0===b&&(b=0);void 0===c&&(c={cancelled:!1});if(0===b)return a;var d;return function(){for(var e=[],k=0;k<arguments.length;k++)e[k]=arguments[k];clearTimeout(d);d=setTimeout(function(){d=void 0;c.cancelled||a.apply(void 0,e)},b)}}function Q(a,b){return a.replace(/{([^}]+)}/g,function(c,d){return d in b?b[d]:"{"+d+"}"})}function Ba(a){var b;if(null===(b=a.params)||void 0===b?0:b.length)a.params=a.params.map(function(c){return"string"===typeof c?
{name:c}:c});return a}function I(a){var b={};Object.defineProperty(b,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1});return a?B(a)&&a._$$isNormalized?a:B(a)?Object.keys(a).reduce(function(c,d){var e=!0===a[d]?[]:Array.isArray(a[d])?a[d]:B(a[d])?a[d]:[a[d]];!1!==a[d]&&(c[d]=ca(d,e));return c},b):"string"!==typeof a?(console.warn("[vee-validate] rules must be either a string or an object."),b):a.split("|").reduce(function(c,d){var e=[],k=d.split(":")[0];F(d,":")&&(e=d.split(":").slice(1).join(":").split(","));
if(!k)return c;c[k]=ca(k,e);return c},b):b}function ca(a,b){var c=x.getRuleDefinition(a);if(!c)return b;var d={};if(!c.params&&!Array.isArray(b))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(b)&&!c.params)return b;if(!c.params||c.params.length<b.length&&Array.isArray(b)){var e;var k=b.map(function(g,n){var q,w=null===(q=c.params)||void 0===q?void 0:q[n];e=w||e;w||(w=e);return w})}else k=c.params;for(var h=0;h<k.length;h++){var f=k[h],l=f["default"];
Array.isArray(b)?h in b&&(l=b[h]):f.name in b?l=b[f.name]:1===k.length&&(l=b);f.isTarget&&(l=da(l,f.cast));"string"===typeof l&&"@"===l[0]&&(l=da(l.slice(1),f.cast));!E(l)&&f.cast&&(l=f.cast(l));d[f.name]?(d[f.name]=Array.isArray(d[f.name])?d[f.name]:[d[f.name]],d[f.name].push(l)):d[f.name]=l}return d}function da(a,b){var c=function(d){d=d[a];return b?b(d):d};c.__locatorRef=a;return c}function Ca(a){return Array.isArray(a)?a.filter(E):Object.keys(a).filter(function(b){return E(a[b])}).map(function(b){return a[b]})}
function ea(a,b,c){void 0===c&&(c={});var d,e,k,h,f,l;return t(this,void 0,void 0,function(){var g,n,q,w,D,J,R;return u(this,function(fa){switch(fa.label){case 0:return g=null===(d=c)||void 0===d?void 0:d.bails,n=null===(e=c)||void 0===e?void 0:e.skipIfEmpty,q={name:(null===(k=c)||void 0===k?void 0:k.name)||"{field}",rules:I(b),bails:null!==g&&void 0!==g?g:!0,skipIfEmpty:null!==n&&void 0!==n?n:!0,forceRequired:!1,crossTable:(null===(h=c)||void 0===h?void 0:h.values)||{},names:(null===(f=c)||void 0===
f?void 0:f.names)||{},customMessages:(null===(l=c)||void 0===l?void 0:l.customMessages)||{}},[4,Da(q,a,c)];case 1:return w=fa.sent(),D=[],J={},R={},w.errors.forEach(function(L){var ha=L.msg();D.push(ha);J[L.rule]=ha;R[L.rule]=L.msg}),[2,{valid:w.valid,errors:D,failedRules:J,regenerateMap:R}]}})})}function Da(a,b,c){c=(void 0===c?{}:c).isInitial;var d=void 0===c?!1:c;return t(this,void 0,void 0,function(){var e,k,h,f,l,g,n,q;return u(this,function(w){switch(w.label){case 0:return[4,Ea(a,b)];case 1:e=
w.sent();k=e.shouldSkip;h=e.errors;if(k)return[2,{valid:!h.length,errors:h}];f=Object.keys(a.rules).filter(function(D){return!x.isRequireRule(D)});l=f.length;g=0;w.label=2;case 2:if(!(g<l))return[3,5];if(d&&x.isLazy(f[g]))return[3,4];n=f[g];return[4,ia(a,b,{name:n,params:a.rules[n]})];case 3:q=w.sent();if(!q.valid&&q.error&&(h.push(q.error),a.bails))return[2,{valid:!1,errors:h}];w.label=4;case 4:return g++,[3,2];case 5:return[2,{valid:!h.length,errors:h}]}})})}function Ea(a,b){return t(this,void 0,
void 0,function(){var c,d,e,k,h,f,l,g,n;return u(this,function(q){switch(q.label){case 0:c=Object.keys(a.rules).filter(x.isRequireRule);d=c.length;e=[];var w;(w=z(b)||""===b)||(w=Array.isArray(b)&&0===b.length);h=(k=w)&&a.skipIfEmpty;f=!1;l=0;q.label=1;case 1:if(!(l<d))return[3,4];g=c[l];return[4,ia(a,b,{name:g,params:a.rules[g]})];case 2:n=q.sent();if(!B(n))throw Error("Require rules has to return an object (see docs)");n.required&&(f=!0);if(!n.valid&&n.error&&(e.push(n.error),a.bails))return[2,
{shouldSkip:!0,errors:e}];q.label=3;case 3:return l++,[3,1];case 4:return k&&!f&&!a.skipIfEmpty||!a.bails&&!h?[2,{shouldSkip:!1,errors:e}]:[2,{shouldSkip:!f&&k,errors:e}]}})})}function ia(a,b,c){return t(this,void 0,void 0,function(){var d,e,k,h,f;return u(this,function(l){switch(l.label){case 0:d=x.getRuleDefinition(c.name);if(!d||!d.validate)throw Error("No such validator '"+c.name+"' exists.");e=d.castValue?d.castValue(b):b;k=Fa(c.params,a.crossTable);return[4,d.validate(e,k)];case 1:h=l.sent();
if("string"===typeof h)return f=r(r({},k||{}),{_field_:a.name,_value_:b,_rule_:c.name}),[2,{valid:!1,error:{rule:c.name,msg:function(){return Q(h,f)}}}];B(h)||(h={valid:h});return[2,{valid:h.valid,required:h.required,error:h.valid?void 0:Ga(a,b,d,c.name,k)}]}})})}function Ga(a,b,c,d,e){var k,h=(k=a.customMessages[d],null!==k&&void 0!==k?k:c.message);k=Ha(a,c,d);c=Ia(a,c,d,h);h=c.userTargets;var f=c.userMessage,l=r(r(r(r({},e||{}),{_field_:a.name,_value_:b,_rule_:d}),k),h);return{msg:function(){var g=
f||C.defaultMessage;var n=a.name;g="function"===typeof g?g(n,l):Q(g,r(r({},l),{_field_:n}));return g},rule:d}}function Ha(a,b,c){b=b.params;if(!b||0>=b.filter(function(f){return f.isTarget}).length)return{};var d={},e=a.rules[c];!Array.isArray(e)&&B(e)&&(e=b.map(function(f){return e[f.name]}));for(c=0;c<b.length;c++){var k=b[c],h=e[c];E(h)&&(h=h.__locatorRef,d[k.name]=a.names[h]||h,d["_"+k.name+"_"]=a.crossTable[h])}return d}function Ia(a,b,c,d){var e={},k=a.rules[c],h=b.params||[];if(!k)return{};
Object.keys(k).forEach(function(f,l){var g=k[f];if(!E(g))return{};var n=h[l];if(!n)return{};g=g.__locatorRef;e[n.name]=a.names[g]||g;e["_"+n.name+"_"]=a.crossTable[g]});return{userTargets:e,userMessage:d}}function Fa(a,b){if(Array.isArray(a))return a;var c={};Object.keys(a).forEach(function(d){var e=a[d];e=E(e)?e(b):e;c[d]=e});return c}function ja(){S.$emit("change:locale")}function Ja(a){var b,c;if(!a||!("undefined"!==typeof Event&&y(Event)&&a instanceof Event||a&&a.srcElement))return a;a=a.target;
return"file"===a.type&&a.files?M(a.files):(null===(b=a._vModifiers)||void 0===b?0:b.number)?(b=parseFloat(a.value),b!==b?a.value:b):(null===(c=a._vModifiers)||void 0===c?0:c.trim)?"string"===typeof a.value?a.value.trim():a.value:a.value}function T(a){if(a.data){var b=a.data;if("model"in b)return b.model;if(a.data.directives)return za(a.data.directives,function(c){return"model"===c.name})}}function U(a){var b,c,d,e=T(a);if(e)return{value:e.value};e=(null===(b=V(a))||void 0===b?void 0:b.prop)||"value";
if((null===(c=a.componentOptions)||void 0===c?0:c.propsData)&&e in a.componentOptions.propsData)return{value:a.componentOptions.propsData[e]};if((null===(d=a.data)||void 0===d?0:d.domProps)&&"value"in a.data.domProps)return{value:a.data.domProps.value}}function Ka(a){return Array.isArray(a)?a:Array.isArray(a.children)?a.children:a.componentOptions&&Array.isArray(a.componentOptions.children)?a.componentOptions.children:[]}function ka(a){return Array.isArray(a)||void 0===U(a)?Ka(a).reduce(function(b,
c){var d=ka(c);d.length&&b.push.apply(b,d);return b},[]):[a]}function V(a){return a.componentOptions?a.componentOptions.Ctor.options.model:null}function K(a,b,c){z(a[b])?a[b]=[c]:y(a[b])&&a[b].fns?(a=a[b],a.fns=Array.isArray(a.fns)?a.fns:[a.fns],F(a.fns,c)||a.fns.push(c)):(y(a[b])&&(a[b]=[a[b]]),Array.isArray(a[b])&&!F(a[b],c)&&a[b].push(c))}function W(a,b,c){a.componentOptions?a.componentOptions&&(a.componentOptions.listeners||(a.componentOptions.listeners={}),K(a.componentOptions.listeners,b,c)):
(a.data||(a.data={}),z(a.data.on)&&(a.data.on={}),K(a.data.on,b,c))}function la(a,b){var c;return a.componentOptions?(V(a)||{event:"input"}).event:(null===(c=null===b||void 0===b?void 0:b.modifiers)||void 0===c?0:c.lazy)?"change":ma(a)?"input":"change"}function La(a,b){return Object.keys(a).reduce(function(c,d){a[d].forEach(function(e){e.context||(a[d].context=b,e.data||(e.data={}),e.data.slot=d)});return c.concat(a[d])},[])}function na(a,b){return a.$scopedSlots["default"]?a.$scopedSlots["default"](b)||
[]:a.$slots["default"]||[]}function oa(a){return r(r({},a.flags),{errors:a.errors,classes:a.classes,failedRules:a.failedRules,reset:function(){return a.reset()},validate:function(){for(var b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return a.validate.apply(a,b)},ariaInput:{"aria-invalid":a.flags.invalid?"true":"false","aria-required":a.isRequired?"true":"false","aria-errormessage":"vee_"+a.id},ariaMsg:{id:"vee_"+a.id,"aria-live":a.errors.length?"assertive":"off"}})}function pa(a,b){a.initialized||
(a.initialValue=b);var c=!a._ignoreImmediate&&a.immediate||a.value!==b&&a.normalizedEvents.length||a._needsValidation||!a.initialized&&void 0===b?!0:!1;a._needsValidation=!1;a.value=b;a._ignoreImmediate=!0;if(c){var d=function(){if(a.immediate||a.flags.validated)return X(a);a.validateSilent()};a.initialized?d():a.$once("hook:mounted",function(){return d()})}}function qa(a){return(y(a.mode)?a.mode:ra[a.mode])(a)}function X(a){var b=a.validateSilent();a._pendingValidation=b;return b.then(function(c){b===
a._pendingValidation&&(a.applyResult(c),a._pendingValidation=void 0);return c})}function sa(a){a.$veeOnInput||(a.$veeOnInput=function(k){a.syncValue(k);a.setFlags({dirty:!0,pristine:!1})});var b=a.$veeOnInput;a.$veeOnBlur||(a.$veeOnBlur=function(){a.setFlags({touched:!0,untouched:!1})});var c=a.$veeOnBlur,d=a.$veeHandler,e=qa(a);d&&a.$veeDebounce===a.debounce||(d=ba(function(){a.$nextTick(function(){a._pendingReset||X(a);a._pendingReset=!1})},e.debounce||a.debounce),a.$veeHandler=d,a.$veeDebounce=
a.debounce);return{onInput:b,onBlur:c,onValidate:d}}function Ma(a,b){var c=U(b);a._inputEventName=a._inputEventName||la(b,T(b));pa(a,null===c||void 0===c?void 0:c.value);c=sa(a);var d=c.onBlur,e=c.onValidate;W(b,a._inputEventName,c.onInput);W(b,"blur",d);a.normalizedEvents.forEach(function(k){W(b,k,e)});a.initialized=!0}function Na(a,b){for(var c={},d=Object.keys(b),e=d.length,k=function(f){f=d[f];var l=a&&a[f]||f,g=b[f];if(z(g)||("valid"===f||"invalid"===f)&&!b.validated)return"continue";"string"===
typeof l?c[l]=g:Array.isArray(l)&&l.forEach(function(n){c[n]=g})},h=0;h<e;h++)k(h);return c}function Oa(a){var b=a.$_veeObserver.refs;return a.fieldDeps.reduce(function(c,d){if(!b[d])return c;c.values[d]=b[d].value;c.names[d]=b[d].name;return c},{names:{},values:{}})}function Pa(a){if(a.vid)return a.vid;if(a.name)return a.name;if(a.id)return a.id;if(a.fieldName)return a.fieldName;ta++;return"_vee_"+ta}function Qa(){return{refs:{},observe:function(a){this.refs[a.id]=a},unobserve:function(a){delete this.refs[a]}}}
function ua(a,b,c){void 0===c&&(c=!0);var d=a.$_veeObserver.refs;a._veeWatchers||(a._veeWatchers={});if(!d[b]&&c)return a.$once("hook:mounted",function(){ua(a,b,!1)});!y(a._veeWatchers[b])&&d[b]&&(a._veeWatchers[b]=d[b].$watch("value",function(){a.flags.validated&&(a._needsValidation=!0,a.validate())}))}function va(a){a.$_veeObserver&&a.$_veeObserver.unobserve(a.id,"observer")}function wa(a){a.$_veeObserver&&a.$_veeObserver.observe(a,"observer")}function xa(){return r(r({},P()),{valid:!0,invalid:!1})}
function Ra(){for(var a=v(N(this.refs),this.observers),b={},c=xa(),d={},e=a.length,k=0;k<e;k++){var h=a[k];Array.isArray(h.errors)?(b[h.id]=h.errors,d[h.id]=r({id:h.id,name:h.name,failedRules:h.failedRules},h.flags)):(b=r(r({},b),h.errors),d=r(r({},d),h.fields))}Sa.forEach(function(f){var l=f[0];c[l]=a[f[1]](function(g){return g.flags[l]})});return{errors:b,flags:c,fields:d}}p=p&&p.hasOwnProperty("default")?p["default"]:p;var r=function(){r=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<
d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return r.apply(this,arguments)},B=function(a){return null!==a&&a&&"object"===typeof a&&!Array.isArray(a)},G={},x=function(){function a(){}a.extend=function(b,c){var d=Ba(c);G[b]=G[b]?O(G[b],c):r({lazy:!1,computesRequired:!1},d)};a.isLazy=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.lazy)};a.isRequireRule=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.computesRequired)};a.getRuleDefinition=
function(b){return G[b]};return a}(),C=r({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Y=function(a){C=r(r({},C),a)},ra={aggressive:function(){return{on:["input","blur"]}},eager:function(a){return a.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},
S=new p,Ta=function(){function a(b,c){this.container={};this.locale=b;this.merge(c)}a.prototype.resolve=function(b,c,d){return this.format(this.locale,b,c,d)};a.prototype.format=function(b,c,d,e){var k,h,f,l,g,n,q,w;(d=(null===(f=null===(h=null===(k=this.container[b])||void 0===k?void 0:k.fields)||void 0===h?void 0:h[c])||void 0===f?void 0:f[d])||(null===(g=null===(l=this.container[b])||void 0===l?void 0:l.messages)||void 0===g?void 0:g[d]))||(d="{field} is not valid");c=(w=null===(q=null===(n=this.container[b])||
void 0===n?void 0:n.names)||void 0===q?void 0:q[c],null!==w&&void 0!==w?w:c);return y(d)?d(c,e):Q(d,r(r({},e),{_field_:c}))};a.prototype.merge=function(b){O(this.container,b)};a.prototype.hasRule=function(b){var c,d;return!(null===(d=null===(c=this.container[this.locale])||void 0===c?void 0:c.messages)||void 0===d||!d[b])};return a}(),H,ma=function(a){var b,c=(null===(b=a.data)||void 0===b?void 0:b.attrs)||a.elm;return("input"!==a.tag||c&&c.type)&&"textarea"!==a.tag?F("text password search email tel url number".split(" "),
null===c||void 0===c?void 0:c.type):!0},ta=0,ya=p.extend({inject:{$_veeObserver:{from:"$_veeObserver","default":function(){this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver=Qa());return this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,"default":""},name:{type:String,"default":null},mode:{type:[String,Function],"default":function(){return C.mode}},rules:{type:[Object,String],"default":null},immediate:{type:Boolean,"default":!1},bails:{type:Boolean,"default":function(){return C.bails}},
skipIfEmpty:{type:Boolean,"default":function(){return C.skipOptional}},debounce:{type:Number,"default":0},tag:{type:String,"default":"span"},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1},customMessages:{type:Object,"default":function(){return{}}}},watch:{rules:{deep:!0,handler:function(a,b){this._needsValidation=!A(a,b)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:P(),failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var a=
this;return Object.keys(this.normalizedRules).reduce(function(b,c){var d=Ca(a.normalizedRules[c]).map(function(e){return e.__locatorRef});b.push.apply(b,d);d.forEach(function(e){ua(a,e)});return b},[])},normalizedEvents:function(){var a=this;return(qa(this).on||[]).map(function(b){return"input"===b?a._inputEventName:b})},isRequired:function(){var a=r(r({},this._resolvedRules),this.normalizedRules);a=Object.keys(a).some(x.isRequireRule);this.flags.required=!!a;return a},classes:function(){return Na(C.classes,
this.flags)},normalizedRules:function(){return I(this.rules)}},created:function(){var a=this,b=function(){if(a.flags.validated){var c=a._regenerateMap;if(c){var d=[],e={};Object.keys(c).forEach(function(k){var h=c[k]();d.push(h);e[k]=h});a.applyResult({errors:d,failedRules:e,regenerateMap:c})}else a.validate()}};S.$on("change:locale",b);this.$on("hook:beforeDestroy",function(){S.$off("change:locale",b)})},render:function(a){var b=this;this.registerField();var c=oa(this);c=na(this,c);ka(c).forEach(function(d){var e,
k,h,f,l;if(C.useConstraintAttrs){var g,n=null===(g=d.data)||void 0===g?void 0:g.attrs;if(F(["input","select","textarea"],d.tag)&&n)if(g={},"required"in n&&!1!==n.required&&x.getRuleDefinition("required")&&(g.required="checkbox"===n.type?[!0]:!0),ma(d)){n=r;g=r({},g);var q=null===(l=d.data)||void 0===l?void 0:l.attrs;l={};q&&("email"===q.type&&x.getRuleDefinition("email")&&(l.email=["multiple"in q]),q.pattern&&x.getRuleDefinition("regex")&&(l.regex=q.pattern),0<=q.maxlength&&x.getRuleDefinition("max")&&
(l.max=q.maxlength),0<=q.minlength&&x.getRuleDefinition("min")&&(l.min=q.minlength),"number"===q.type&&(Z(q.min)&&x.getRuleDefinition("min_value")&&(l.min_value=Number(q.min)),Z(q.max)&&x.getRuleDefinition("max_value")&&(l.max_value=Number(q.max))));l=I(n(g,l))}else l=I(g);else l={}}else l={};A(b._resolvedRules,l)||(b._needsValidation=!0);F(["input","select","textarea"],d.tag)&&(b.fieldName=(null===(k=null===(e=d.data)||void 0===e?void 0:e.attrs)||void 0===k?void 0:k.name)||(null===(f=null===(h=d.data)||
void 0===h?void 0:h.attrs)||void 0===f?void 0:f.id));b._resolvedRules=l;Ma(b,d)});return this.slim&&1>=c.length?c[0]:a(this.tag,c)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(a){var b=this;Object.keys(a).forEach(function(c){b.flags[c]=a[c]})},syncValue:function(a){this.value=a=Ja(a);this.flags.changed=this.initialValue!==a},reset:function(){var a=this;this.errors=[];this.initialValue=
this.value;var b=P();b.required=this.isRequired;this.setFlags(b);this.failedRules={};this.validateSilent();this._pendingValidation=void 0;this._pendingReset=!0;setTimeout(function(){a._pendingReset=!1},this.debounce)},validate:function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return t(this,void 0,void 0,function(){return u(this,function(c){0<a.length&&this.syncValue(a[0]);return[2,X(this)]})})},validateSilent:function(){return t(this,void 0,void 0,function(){var a,b;return u(this,
function(c){switch(c.label){case 0:return this.setFlags({pending:!0}),a=r(r({},this._resolvedRules),this.normalizedRules),Object.defineProperty(a,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,ea(this.value,a,r(r({name:this.name||this.fieldName},Oa(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return b=c.sent(),this.setFlags({pending:!1,valid:b.valid,invalid:!b.valid}),[2,b]}})})},setErrors:function(a){this.applyResult({errors:a,
failedRules:{}})},applyResult:function(a){var b=a.errors,c=a.failedRules;a=a.regenerateMap;this.errors=b;this._regenerateMap=a;this.failedRules=r({},c||{});this.setFlags({valid:!b.length,passed:!b.length,invalid:!!b.length,failed:!!b.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var a=Pa(this),b=this.id;!this.isActive||b===a&&this.$_veeObserver.refs[b]||(b!==a&&this.$_veeObserver.refs[b]===this&&this.$_veeObserver.unobserve(b),this.id=a,this.$_veeObserver.observe(this))}}}),
Sa=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],Ua=0,Va=p.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver","default":function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,"default":"span"},vid:{type:String,
"default":function(){return"obs_"+Ua++}},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:xa(),fields:{}}},created:function(){var a=this;this.id=this.vid;wa(this);var b=ba(function(c){var d=c.flags,e=c.fields;a.errors=c.errors;a.flags=d;a.fields=e},16);this.$watch(Ra,b)},activated:function(){wa(this)},deactivated:function(){va(this)},beforeDestroy:function(){va(this)},render:function(a){var b=na(this,r(r({},this.flags),
{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=b.length?b[0]:a(this.tag,{on:this.$listeners},b)},methods:{observe:function(a,b){var c;void 0===b&&(b="provider");"observer"===b?this.observers.push(a):this.refs=r(r({},this.refs),(c={},c[a.id]=a,c))},unobserve:function(a,b){void 0===b&&(b="provider");if("provider"===b)this.refs[a]&&this.$delete(this.refs,a);else{var c=aa(this.observers,function(d){return d.id===
a});-1!==c&&this.observers.splice(c,1)}},validate:function(a){a=(void 0===a?{}:a).silent;var b=void 0===a?!1:a;return t(this,void 0,void 0,function(){var c;return u(this,function(d){switch(d.label){case 0:return[4,Promise.all(v(N(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[b?"validateSilent":"validate"]().then(function(k){return k.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:b})})))];case 1:return c=d.sent(),
[2,c.every(function(e){return e})]}})})},handleSubmit:function(a){return t(this,void 0,void 0,function(){var b;return u(this,function(c){switch(c.label){case 0:return[4,this.validate()];case 1:return(b=c.sent())&&a?[2,a()]:[2]}})})},reset:function(){return v(N(this.refs),this.observers).forEach(function(a){return a.reset()})},setErrors:function(a){var b=this;Object.keys(a).forEach(function(c){var d=b.refs[c];d&&(c=a[c]||[],c="string"===typeof c?[c]:c,d.setErrors(c))});this.observers.forEach(function(c){c.setErrors(a)})}}});
m.ValidationObserver=Va;m.ValidationProvider=ya;m.configure=function(a){Y(a)};m.extend=function(a,b){if(!y(b)&&!y(b.validate)&&!x.getRuleDefinition(a))throw Error("Extension Error: The validator '"+a+"' must be a function or have a 'validate' method.");"object"===typeof b?x.extend(a,b):x.extend(a,{validate:b})};m.localeChanged=ja;m.localize=function(a,b){var c;H||(H=new Ta("en",{}),Y({defaultMessage:function(d,e){return H.resolve(d,null===e||void 0===e?void 0:e._rule_,e||{})}}));"string"===typeof a?
(H.locale=a,b&&H.merge((c={},c[a]=b,c)),ja()):H.merge(a)};m.normalizeRules=I;m.setInteractionMode=function(a,b){Y({mode:a});if(b){if(!y(b))throw Error("A mode implementation must be a function");ra[a]=b}};m.validate=ea;m.version="3.2.3";m.withValidation=function(a,b){void 0===b&&(b=Aa);var c,d="options"in a?a.options:a,e=ya.options;e={name:(d.name||"AnonymousHoc")+"WithValidation",props:r({},e.props),data:e.data,computed:r({},e.computed),methods:r({},e.methods),beforeDestroy:e.beforeDestroy,inject:e.inject};
var k=(null===(c=null===d||void 0===d?void 0:d.model)||void 0===c?void 0:c.event)||"input";e.render=function(h){var f;this.registerField();var l=oa(this),g=r({},this.$listeners),n=T(this.$vnode);this._inputEventName=this._inputEventName||la(this.$vnode,n);var q=U(this.$vnode);pa(this,null===q||void 0===q?void 0:q.value);q=sa(this);var w=q.onBlur,D=q.onValidate;K(g,k,q.onInput);K(g,"blur",w);this.normalizedEvents.forEach(function(J){K(g,J,D)});q=(V(this.$vnode)||{prop:"value"}).prop;l=r(r(r({},this.$attrs),
(f={},f[q]=null===n||void 0===n?void 0:n.value,f)),b(l));return h(d,{attrs:this.$attrs,props:l,on:g},La(this.$slots,this.$vnode.context))};return e};Object.defineProperty(m,"__esModule",{value:!0})});



!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r=r||self).VeeValidateRules={})}(this,function(r){"use strict";var i={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},u={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},o=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return o(r,{locale:n})}):n?(i[n]||i.en).test(e):Object.keys(i).some(function(r){return i[r].test(e)})},e={validate:o,params:[{name:"locale"}]},l=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return l(r,{locale:n})}):n?(s[n]||s.en).test(e):Object.keys(s).some(function(r){return s[r].test(e)})},t={validate:l,params:[{name:"locale"}]},c=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return c(r,{locale:n})}):n?(u[n]||u.en).test(e):Object.keys(u).some(function(r){return u[r].test(e)})},n={validate:c,params:[{name:"locale"}]},A=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return A(r,{locale:n})}):n?(a[n]||a.en).test(e):Object.keys(a).some(function(r){return a[r].test(e)})},f={validate:A,params:[{name:"locale"}]},m=function(r,e){var t=void 0===e?{}:e,n=t.min,i=t.max;return Array.isArray(r)?r.every(function(r){return!!m(r,{min:n,max:i})}):Number(n)<=r&&Number(i)>=r},v={validate:m,params:[{name:"min"},{name:"max"}]},$={validate:function(r,e){var t=e.target;return String(r)===String(t)},params:[{name:"target",isTarget:!0}]},d=function(r,e){var t=e.length;if(Array.isArray(r))return r.every(function(r){return d(r,{length:t})});var n=String(r);return/^[0-9]*$/.test(n)&&n.length===t},y={validate:d,params:[{name:"length",cast:function(r){return Number(r)}}]},g={validate:function(r,e){var u=e.width,s=e.height,t=[];r=Array.isArray(r)?r:[r];for(var n=0;n<r.length;n++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(r[n].name))return Promise.resolve(!1);t.push(r[n])}return Promise.all(t.map(function(r){return t=r,n=u,i=s,a=window.URL||window.webkitURL,new Promise(function(r){var e=new Image;e.onerror=function(){return r(!1)},e.onload=function(){return r(e.width===n&&e.height===i)},e.src=a.createObjectURL(t)});var t,n,i,a})).then(function(r){return r.every(function(r){return r})})},params:[{name:"width",cast:function(r){return Number(r)}},{name:"height",cast:function(r){return Number(r)}}]},Z={validate:function(r,e){var t=(void 0===e?{}:e).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return t&&!Array.isArray(r)&&(r=String(r).split(",").map(function(r){return r.trim()})),Array.isArray(r)?r.every(function(r){return n.test(String(r))}):n.test(String(r))},params:[{name:"multiple",default:!1}]};function p(r){return null==r}function h(r){return Array.isArray(r)&&0===r.length}function x(r){return"function"==typeof Array.from?Array.from(r):function(r){for(var e=[],t=r.length,n=0;n<t;n++)e.push(r[n]);return e}(r)}function _(r){return h(r)||-1!==[!1,null,void 0].indexOf(r)||!String(r).trim().length}var b=function(e,t){return Array.isArray(e)?e.every(function(r){return b(r,t)}):x(t).some(function(r){return r==e})},w={validate:b},S={validate:function(r,e){return!b(r,e)}},N={validate:function(r,e){var t=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.name)}):t.test(r.name)}},j={validate:function(r){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(r)?r.every(function(r){return e.test(r.name)}):e.test(r.name)}},k={validate:function(r){return Array.isArray(r)?r.every(function(r){return/^-?[0-9]+$/.test(String(r))}):/^-?[0-9]+$/.test(String(r))}},z={validate:function(r,e){return r===e.other},params:[{name:"other"}]},F={validate:function(r,e){return r!==e.other},params:[{name:"other"}]},R={validate:function(r,e){var t=e.length;return!p(r)&&("number"==typeof r&&(r=String(r)),r.length||(r=x(r)),r.length===t)},params:[{name:"length",cast:function(r){return Number(r)}}]},O=function(r,e){var t=e.length;return p(r)?0<=t:Array.isArray(r)?r.every(function(r){return O(r,{length:t})}):String(r).length<=t},q={validate:O,params:[{name:"length",cast:function(r){return Number(r)}}]},C=function(r,e){var t=e.max;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return C(r,{max:t})}):Number(r)<=t)},P={validate:C,params:[{name:"max",cast:function(r){return Number(r)}}]},E={validate:function(r,e){var t=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.type)}):t.test(r.type)}},L=function(r,e){var t=e.length;return!p(r)&&(Array.isArray(r)?r.every(function(r){return L(r,{length:t})}):String(r).length>=t)},U={validate:L,params:[{name:"length",cast:function(r){return Number(r)}}]},T=function(r,e){var t=e.min;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return T(r,{min:t})}):Number(r)>=t)},V={validate:T,params:[{name:"min",cast:function(r){return Number(r)}}]},I=/^[٠١٢٣٤٥٦٧٨٩]+$/,M=/^[0-9]+$/,B={validate:function(r){function e(r){var e=String(r);return M.test(e)||I.test(e)}return Array.isArray(r)?r.every(e):e(r)}},D=function(r,e){var t=e.regex;return Array.isArray(r)?r.every(function(r){return D(r,{regex:t})}):t.test(String(r))},G={validate:D,params:[{name:"regex",cast:function(r){return"string"==typeof r?new RegExp(r):r}}]},H={validate:function(r,e){var t=(void 0===e?{allowFalse:!0}:e).allowFalse,n={valid:!1,required:!0};return p(r)||h(r)||!1===r&&!t||(n.valid=!!String(r).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},J={validate:function(r,e){var t,n=e.target,i=e.values;return(t=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(r){return r==String(n).trim()})):!_(n))?{valid:!_(r),required:t}:{valid:!0,required:t}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},K={validate:function(r,e){var t=e.size;if(isNaN(t))return!1;var n=1024*t;if(!Array.isArray(r))return r.size<=n;for(var i=0;i<r.length;i++)if(r[i].size>n)return!1;return!0},params:[{name:"size",cast:function(r){return Number(r)}}]};r.alpha=e,r.alpha_dash=t,r.alpha_num=n,r.alpha_spaces=f,r.between=v,r.confirmed=$,r.digits=y,r.dimensions=g,r.email=Z,r.excluded=S,r.ext=N,r.image=j,r.integer=k,r.is=z,r.is_not=F,r.length=R,r.max=q,r.max_value=P,r.mimes=E,r.min=U,r.min_value=V,r.numeric=B,r.oneOf=w,r.regex=G,r.required=H,r.required_if=J,r.size=K,Object.defineProperty(r,"__esModule",{value:!0})});
Vue.component('yuno-page-grid', {
    props: {
        authorizedRoles: {
            type: Array,
            required: false,
            default: () => []
        },
        hasPageHeader: {
            type: Boolean,
            required: false,
            default: true
        },
        hasPageFooter: {
            type: Boolean,
            required: false,
            default: true
        },
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        },
        zohoMeta: {
            type: Object,
            required: false,
            default: null
        }
    },
    template: `
        <div>
            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>
            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>
            <div class="pageGrid">
                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>
                <slot name="aboveMain"></slot>
                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? 'miniSidebar' : '', loginStatus ? 'postLogin' : 'preLogin', loginStatus && !hasPageHeader && !hasPageFooter ? 'noHeaderFooter' : '']">
                    <template v-if="userInfo.loading">
                        <div class="container hasTopGap">
                            <figure class="infiniteSpinner">
                                <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                            </figure>
                        </div>
                    </template>
                    <template v-if="userInfo.success || !user.isLoggedin">
                        <template v-if="isUserAuthorized">
                            <slot name="main"></slot>     
                        </template>
                        <template v-else>
                            <div class="container">
                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                            </div>
                        </template>
                    </template>
                </main>
            </div>
            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> 
            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>
            <slot name="belowFooter"></slot>
        </div>
    `,
    data() {
        return {
            isMiniSidebar: false,
            loginStatus: isLoggedIn !== '0' ? true : false
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'user',
            'header',
            'footer'
        ]),
        isUserAuthorized : {
            get() {
                if (YUNOCommon.findInArray(this.$props.authorizedRoles, this.userRole.data)) { //
                    return true;
                } else if (this.$props.authorizedRoles.length === 0) {
                    return true;
                } else {
                    return false;
                }
            }
        },
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading() {
            return this.userInfo.loading || this.header.loading || this.footer.loading;
        },
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        onUserInfo(data) {
            this.$emit('onUserInfo', data);
            // if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
            //     this.fetchModules(data.role);
            // }
            
        },
        onMini(data) {
            this.isMiniSidebar = data;
        },
    }
});
Vue.component('yuno-page-header', {
    props: {
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    template: `
        <div class="yunoPageHeader">
            <figure class="logo">
                <img width="68" height="32" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
            </figure>
            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>
            <ul class="actions">
                <li v-if="manageOrgSwitchVisiblity()">
                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown 
                        v-model="selectedOrg" 
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="list"
                        :class="['orgSwitchWrapper']"
                    >
                        <template #trigger>
                            <div class="orgSwitch">
                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">
                                <span class="name">{{ selectedOrg.name }}</span>
                                <span class="icon"></span>
                            </div>
                        </template>
                        <b-dropdown-item 
                            aria-role="menuitem"
                            v-for="(org, i) in activeUser.org_id"
                            :key="i"
                            @click="manageOrg(org)"
                            :value="org"
                        >
                            
                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        
                            
                        </b-dropdown-item>
                    </b-dropdown>
                </li>
                <li>
                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown
                        v-model="navigation"
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="menu"
                    >
                        <template #trigger>
                            <div class="userIcon">
                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                            </div>
                        </template>
                        <b-dropdown-item custom aria-role="menuitem" :class="['normal']">
                            <figure class="userCard">
                                <div class="imgWrapper">
                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                                </div>
                                <figcaption>
                                    <h3>{{ activeUser.yuno_display_name }}</h3>
                                    <p>{{ activeUser.email }}</p>
                                    <p>{{ activeUser.role }}</p>
                                </figcaption>
                            </figure>
                        </b-dropdown-item>
                        <b-dropdown-item 
                            has-link 
                            aria-role="menuitem"
                            v-for="(menu, i) in accountMenu.items"
                            @click="manageMenuItem($event, menu)"
                            :key="i"
                        >
                            <a :href="menu.url">
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        
                            </a>
                        </b-dropdown-item>
                        
                    </b-dropdown>
                </li>
            </ul>
        </div>
    `,
    data() {
        return {
            navigation: "",
            selectedOrg: null,
            isLoading: true
        }
    },
    computed: {
        ...Vuex.mapState([
            'header',
            'userInfo',
            'userRole',
            'subform3'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
        accountMenu() {
            return YUNOCommon.findObjectByKey(this.header.data, "section", "Account")
        },
        activeUser() {
            return this.userInfo.data
        },
    },
    watch: {
        'userInfo.data': {
            handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.init();   
                }
            },
            deep: true
        }
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        manageMenuItem(e, data) {
            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                sessionStorage.clear();
            }
        },
        manageOrgSwitchVisiblity() {
            if (this.userRole.data === "org-admin" && this.userInfo.data.org_id.length > 1) {
                return true;
            } else  {
                return false;
            }
        },
        manageOrg(org) {
            this.updateActiveOrg(org.id);
        },
        orgUpdated(options) {
            const response = options?.response?.data;

            if (response?.code === 201) {
                sessionStorage.clear();
                window.location.reload(true);
            } else if (response?.message) { 
                console.log(response.message)
            }
        },
        updateActiveOrg(orgID) {
            this.$buefy.loading.open();

            const options = {
                apiURL: YUNOCommon.config.academy("activeOrg"), 
                module: "gotData", 
                store: "subform3", 
                payload: {
                    "user_id": isLoggedIn,
                    "org_id": orgID
                }, 
                callback: true, 
                callbackFunc: (options) => this.orgUpdated(options)
            };

            this.dispatchData('postData', options);
        },
        dispatchData(action, options) {
            this.$store.dispatch(action, options);
        },
        init() {
            if (this.userInfo.data.role === "org-admin") {
                const activeOrg = YUNOCommon.findObjectByKey(this.userInfo.data.org_id, "id", Number(this.activeOrg()));
                this.selectedOrg = activeOrg;    
            }
        },
        searchBar() {
            if (this.userRole.data === "Learner") {
                return true;    
            } else {
                return false;
            }
            
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        }
    }
});
Vue.component('yuno-header-v2', {
    props: ["data", "options"],
    template: `
        <div class="sidebarWrapper">
            <div class="sidebar-page yunoSidebar" :class="[isMobile ? 'isMobile' : 'isDesktop', reduce ? 'collapseView' : 'expandView']">
                <section class="sidebar-layout">
                    <b-sidebar
                        position="static"
                        :mobile="mobile"
                        :expand-on-hover="expandOnHover"
                        :reduce="reduce"
                        :delay="expandWithDelay ? 500 : null"
                        type="is-light"
                        open
                    >
                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? 'isMobile' : 'isDesktop']">
                            <span class="material-icons">
                                <template v-if="isMobile">
                                    menu
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                        </a>
                        <figure class="logo" v-if="!isPageGrid">
                            <a href="#">
                                <img width="106" height="50" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
                            </a>
                        </figure>
                        <yuno-main-nav
                            :options="{'isMini': reduce}"
                            :isPageGrid="isPageGrid"
                        >
                        </yuno-main-nav>
                    </b-sidebar>
                </section>
                <b-modal 
                    :active.sync="config.unauthorizedModal" 
                    :width="450" 
                    :can-cancel="['escape', 'x']" 
                    :on-cancel="unauthorizedModalClose"
                    class="yunoModal">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Session Expired</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper">
                                <p>{{sessionExpired}}</p>
                            </div>
                        </div>
                        <div class="modalFooter">
                            <div class="unauthorizedLogin">
                                <a 
                                    @click.prevent="setState()"
                                    href="#">
                                    <span class="g_icon"></span>
                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>
                                </a>
                            </div>
                        </div>
                </b-modal>
            </div>
        </div>
    `,
    data() {
        return {
            isMobile: false,
            menuLoading: 3,
            expandOnHover: false,
            expandWithDelay: false,
            mobile: "reduce",
            reduce: false,
            tokenExpiry: {
                payload: {
                    "userID": isLoggedIn,
                    "token": this.$store.state.config.yunoAPIToken
                }
            },
            sessionExpired: YUNOCommon.config.errorMsg.sesstionExpired,
            storage: {
                name: "activeUser",
                version: 1
            },
            isPageGrid: true,
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'userProfile',
            'config',
            'header',
            'apiTokenExpiryTime',
            'apiTokenRefresh',
            'referralCode'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
    },
    async created() {
        window.addEventListener("resize", this.manageOnResize);
        this.emitEvents();
    },
    destroyed() {
        window.removeEventListener("resize", this.manageOnResize);
    },
    mounted() {
        this.checkMenuState();
        this.manageOnResize();
        this.fetchModule();
    },
    methods: {
        emitEvents() {
            Event.$on('fetchReferralCode', () => {
                this.referralCode.success = false;
                this.referralCode.error = null;
                this.referralCode.errorData = [];
                this.referralCode.data = [];
                this.fetchReferralCode();
            });
        },
        manageOnResize() {
            let windowWidth = window.outerWidth;

            if (windowWidth >= 768) {
                this.isMobile = false;
            } else {
                this.isMobile = true;
                this.reduce = true;
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchReferralCode() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "referralCode",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        checkMenuState() {
            if (this.$parent && this.$parent.$options.name !== "yuno-page-grid") {
                this.isPageGrid = false;
            }

            const store = sessionStorage.getItem('isLHSMenu');

            if (store !== null && !this.isMobile) {
                this.reduce = store === 'true' ? true : false;
                this.sidebarToggle(true);
            } else {
                this.reduce = false
            }
        },
        sidebarToggle(noToggle) {
            if (!noToggle) {
                if (this.reduce) {
                    sessionStorage.setItem("isLHSMenu", false);
                    this.reduce = false
                } else {
                    sessionStorage.setItem("isLHSMenu", true);
                    this.reduce = true
                }    
            }

            this.$emit('isMini', this.reduce);
        },
        chooseAccountState() {
            localStorage.setItem('userState', window.location.pathname);
            localStorage.setItem('isChooseAccountState', true);
        },
        unauthorizedModalClose() {
            window.location.href = "/logout";
        },
        fetchModule() {
            this.getStorage();
        },
        initTokenTime(minutes) {
            let setMinutes = parseInt(minutes - 10),
                timeout = parseInt(60000 * setMinutes); // minutes to miliseconds;

            setTimeout(() => {
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            }, timeout);
        },
        doneRefreshAPIToken(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data;

                this.config.yunoAPIToken = "Bearer "+ data.token +"";
                this.tokenExpiry.payload.token = "Bearer "+ data.token +"";
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            };
        },
        refreshAPIToken(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenRefresh(),
                module: "gotData",
                store: "apiTokenRefresh",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.doneRefreshAPIToken(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        gotAPITokenExpiryTime(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data,
                    minTime = 10;

                if (data.minutes <= minTime) {
                    let payload = {
                        user_id: isLoggedIn,
                        id_token: this.config.yunoAPIToken
                    };

                    this.refreshAPIToken(payload);
                } else {
                    this.initTokenTime(data.minutes);
                };
            };
        },
        fetchAPITokenExpiryTime(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenExpiry(isLoggedIn),
                module: "gotData",
                store: "apiTokenExpiryTime",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotAPITokenExpiryTime(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        extractSlugFromURL(str) {
            // Remove the trailing slash from URl if it exists
            const currentUrl = str.replace(/\/$/, ''),
                urlParts = currentUrl.split("/");

            // Remove empty string from the end if the URL ends with a slash
            if (urlParts[urlParts.length - 1] === "") {
                urlParts.pop();
            }

            // Get the value between the last two slashes
            
            const value = urlParts[urlParts.length - 1];

            return value;
        },
        manageCurrentPage(data) {
            const normalizeUrl = url => url.replace(/\/$/, '');
            const currentUrl = normalizeUrl(window.location.origin + window.location.pathname);
        
            // Iterate through the array and set is_active to true if url matches currentUrl
            data.forEach(section => {
                section.items.forEach(item => {
                    item.is_active = currentUrl === normalizeUrl(item.url);
        
                    let isAnySubItemActive = false;
                    item.sub_items.forEach(subItem => {
                        subItem.is_active = currentUrl === normalizeUrl(subItem.url);
        
                        // Determine if any subItem is active
                        if (subItem.is_active && subItem.parent_id === item.id) {
                            isAnySubItemActive = true;
                        } 
                    });
        
                    // Set item.is_expended based on subItem activity and item activity
                    if (isAnySubItemActive) {
                        item.is_expended = true;
                    } else {
                        item.is_expended = false;
                    }
                });
            });
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        },
        gotPostLoginMenu(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.header.data;
                    this.header.success = true;
                } else {
                    data = options.response.data.data;
                }

                this.manageCurrentPage(data);
                this.header.data = data;
                this.setStorage()
                this.$emit('menuLoaded');
            };
        },
        fetchPostLoginMenu(role) {
            const props = {
                userID: isLoggedIn,
                orgID: this.userInfo.data.role === "org-admin" ? this.activeOrg() : 0,
            };

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.header("menu", props),
                module: "gotData",
                store: "header",
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotPostLoginMenu(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageOrgAdmin(userData) {
            const { host } = YUNOCommon.config;
            const { has_org, org_id } = userData;
            const activeOrg = sessionStorage.getItem("activeOrg");

            if (activeOrg === null) {
                if (!has_org) {
                    window.location.href = `${host()}/create-organization-account`;
                } else if (org_id.length > 1) {
                    window.location.href = `${host()}/select-an-organization`;
                    sessionStorage.setItem('redirectURL', window.location.pathname + window.location.search);
                } else {
                    sessionStorage.setItem('activeOrg', JSON.stringify(org_id[0].id));
                }    
            }
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.userInfo.data;
                    this.userInfo.success = true;
                } else {
                    data = options.response.data.data;
                };

                if (this.header.data.length !== 0) {
                    this.gotPostLoginMenu(false);
                } else {
                    this.fetchPostLoginMenu(data.role);
                }
                
                this.userRole.data = data.role;
                this.userProfile.data = data;
                this.userProfile.success = true;

                if (data.role === "Instructor") {
                    // this.fetchReferralCode();
                }

                if (data.role === "Learner") {
                    this.fetchReferralCode();
                }

                if (data.role === "Learner" && data.is_signup_completed === "pending") {
                    const userState = localStorage.getItem('userState'),
                        windowURL = window.location.pathname + window.location.search;

                    if (windowURL !== userState) {
                        window.location.href = YUNOCommon.config.host() + "/sign-up";
                        setTimeout(() => { 
                            localStorage.removeItem('skipSignUp');
                        }, 10);
                    };
                }

                if (data.role === "org-admin") {
                    // this.manageOrgAdmin(data);    
                }

                this.$emit('userInfo', data);
            }
        },
        fetchUserInfo() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotUserInfo(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        getStorage() {
            const storage = this.storage;
            let version = Number(JSON.parse(JSON.stringify(storage.version)));
                lastStorage = storage.name + "V" + (--version);

            sessionStorage.removeItem(lastStorage);
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store !== null) {
                const data = JSON.parse(store);
                this.header.data = data.menu;
            };

            this.loginStatus();
        },
        setStorage() {
            const storage = this.storage;
            const store = {
                menu: this.header.data
            };
            

            if (this.userInfo.data.is_signup_completed === "completed") {
                sessionStorage.setItem(storage.name + "V" + storage.version, JSON.stringify(store));
            };
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                
                if (this.userInfo.data.length !== 0) {
                    this.gotUserInfo(false)
                } else {
                    this.fetchUserInfo();
                }
                
                // this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
                this.$emit('login', this.user.isLoggedin);
            } else {
                const storage = this.storage;

                sessionStorage.removeItem(storage.name + "V" + storage.version);
                this.user.isLoggedin = false;
                this.$emit('login', this.user.isLoggedin);
            }
        },
    }
});
Vue.component('yuno-main-nav', {
    props: ["data", "options", "isPageGrid"],
    template: `
        <b-menu class="is-custom-mobile">
            <nav class="menuWrapper">
                <template v-if="header.loading || userInfo.loading">
                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>
                </template>
                <template v-if="header.success">
                    <template v-if="header.error">
                        {{ header.errorData }}
                    </template>
                    <template v-else>
                        <template v-if="isPageGrid">
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                                v-if="section.section !== 'Account'"
                            >       
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list> 
                        </template>
                        <template v-else>
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                            >       
                                <template v-if="section.section === 'Account'">
                                    <template v-if="header.loading">
                                        <figure class="menuFooter loading">
                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>
                                            <figcaption>
                                                <p class="userName"><b-skeleton active></b-skeleton></p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                    <template v-if="header.success">
                                        <figure class="menuFooter" :class="[options.isMini ? 'isMini' : '']">
                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">
                                            <figcaption>
                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>
                                                <p class="userEmail">{{ userInfo.data.email }}</p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                </template>
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list>  
                        </template>
                    </template>
                </template>
            </nav>
        </b-menu>
    `,
    data() {
        return {
            menuLoading: 3
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'header',
            'referralCode',
            'generateCode'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        gotReferralCode(options) {
            this.generateCode.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;
                Event.$emit('fetchReferralCode');
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateReferralCode() {
            this.generateCode.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "generateCode",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        manageNavItem(e, data) {
            if (data.sub_items.length !== 0) {
                e.preventDefault();
            }

            if (data.slug === "generate-code") {
                e.preventDefault();
            };

            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                // sessionStorage.removeItem("activeUserV1");
                sessionStorage.clear();
            }
        },
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list :label="manageLabel(userRole.data)">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-' + i"
                        :active="menu.isActive"
                        :expanded="menu.isExpanded"
                        :class="[menu.submenu !== undefined ? 'hasSubmenu' : '', generateClass(menu)]"
                        :href="menu.url"
                        tag="a"
                    >
                        <template #label="props">
                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">
                                <template v-if="props.expanded">
                                    expand_more
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                        <template v-if="menu.submenu !== undefined">
                            <template v-for="(submenu, j) in menu.submenu">
                                <b-menu-item
                                    :key="'submenu-' + j"
                                    :active="submenu.isActive"
                                    :href="submenu.url"
                                    tag="a"
                                >
                                    <template #label="props">
                                        <template v-if="options.isMini">
                                            <b-tooltip :label="submenu.label"
                                                type="is-dark"
                                                position="is-right">
                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>
                                            </b-tooltip>
                                        </template>
                                        <template v-else>
                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>
                                        </template>
                                    </template>
                                </b-menu-item>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>  
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-referral-code', {
    props: ["data", "options"],
    template: `
        <div>
            <template v-if="options.isMini">
                <b-tooltip label="Referral Code"
                    type="is-dark"
                    position="is-right">
                    <div class="referralField isMini">
                        <b-field>
                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                        </b-field>
                        <a href="#" @click.prevent="copyToClipboard('referralCode')">
                            <span>Copy</span>
                        </a>
                    </div>
                </b-tooltip>
            </template>
            <template v-else>
                <div class="referralField">
                    <span class="referralIcon"></span>
                    <b-field>
                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                    </b-field>
                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                        <span class="caption">Copy</span>
                    </a>
                </div>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        }
    }
});

Vue.component('yuno-referral-code-generate', {
    props: ["data", "options"],
    template: `
        <div class="fluid">
            <template v-if="referralCode.error">
                <template v-if="moduleWithoutTab.success">
                    <template v-if="moduleWithoutTab.loading">
                        <div class="referralField">
                            <span class="referralIcon"></span>
                            <b-skeleton active></b-skeleton>
                        </div>
                    </template>
                    <template v-if="moduleWithoutTab.success">
                        <yuno-referral-code :options="options"></yuno-referral-code>    
                    </template>
                </template>
                <template v-else>
                    <template v-if="options.isMini">
                        <b-tooltip label="Generate Code"
                            type="is-dark"
                            position="is-right">
                            <div class="referralField" @click="generateCode()">
                                <span class="referralIcon"></span>
                            </div>
                        </b-tooltip>
                    </template>
                    <template v-else>
                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">
                            <span class="referralIcon"></span>
                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">
                                Generate Code
                            </a>
                        </div>
                        <template v-if="moduleWithoutTab.loading">
                            <div class="referralField">
                                <span class="referralIcon"></span>
                                <b-skeleton active></b-skeleton>
                            </div>
                        </template>
                    </template>
                </template>
            </template>
            <template v-else>
                <yuno-referral-code :options="options"></yuno-referral-code>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;

                this.referralCode.data = data;
            }
        },
        fetchReferralCode() {
            this.moduleWithoutTab.data = [];
            this.moduleWithoutTab.error = null;
            this.moduleWithoutTab.success = false;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "moduleWithoutTab",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotCode(options) {
            this.moduleWithoutTab.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;

                // this.$buefy.toast.open({
                //     duration: 5000,
                //     message: `${response.message}`,
                //     position: 'is-bottom'
                // });

                this.fetchReferralCode();
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateCode() {
            this.moduleWithoutTab.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "moduleWithoutTab",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
    }
});

Vue.component('yuno-referral-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper referral">
            <b-menu-list :label="manageLabel(userRole.data)">
                <b-menu-item 
                    href="#"
                    tag="a"
                >
                    <template #label="props">
                        <template v-if="userRole.data === 'Instructor'">
                            <yuno-referral-code :options="options"></yuno-referral-code>
                        </template>
                        <template v-if="userRole.data === 'Learner'">
                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>
                        </template>
                    </template>
                </b-menu-item>
                <template v-for="(menu, i) in otherItems">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>
        </nav>
    `,
    data() {
        return {
            otherItems: [
                {
                    label: "Earnings",
                    slug: "earnings",
                    role: ["Instructor", "Learner"],
                    icon: "currency_rupee",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/earnings/",
                    isActive: false,
                    callbackFunc: false
                },
                {
                    label: "How it works",
                    slug: "howItWorks",
                    role: ["Instructor", "Learner"],
                    icon: "help_outline",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/how-it-works/",
                    isActive: false,
                    callbackFunc: false
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Referral"
            } else {
                return "Referral Earnings"
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
    }
});
Vue.component('yuno-static-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list label="Account">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list> 
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        }
    }
});
window.Event = new Vue();
const validationMsg = {
	messages: {
		required: "This field is required",
		numeric: "Numbers only",
		min: "Minium 10 numbers required",
		max: "Maxium 15 numbers required",
		is: "This field is required",
		email: "Please enter the valid email id",
	},
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component("yuno-class-schedule-v2", {
	template: `
        <yuno-page-grid
			@onUserInfo="onUserInfo"
			:authorizedRoles="authorizedRoles"
			:hasSearchBar="false"
		>
			<template v-slot:main>
				<b-loading :is-full-page="true" :active="isInProcess" :can-cancel="false"></b-loading>
				<template v-if="userProfile.data.vc_permission">
					<template v-if="isFormLoading">
						<div class="smallLoader"></div>
					</template>
					<template v-if="isFormReady">
						<section id="classSchedule" class="container formSection classSchedule">
							<template v-if="isEditClass()">
								<h1 class="sectionTitle">Edit Class</h1>    
							</template>
							<template v-else>
								<h1 class="sectionTitle">Schedule Class</h1>    
							</template>
							<template v-if="isFormLoading">
								<div class="smallLoader"></div>
							</template>
							<validation-observer ref="classScheduleObserver" v-slot="{ handleSubmit }">
								<form id="classScheduleForm" @submit.prevent="handleSubmit(initForm)">
									<div class="row">
										<div class="col-12 col-md-7 col-lg-7">
											<div class="formWrapper">
												<b-field label="Choose Category" v-if="hideField">
													<b-autocomplete
														v-model="name"
														placeholder="e.g. Anne"
														:data="filteredDataObj"
														field="user.first_name"
														@select="option => (selected = option)"
														:clearable="true">
													</b-autocomplete>
												</b-field>
												<b-field label="Choose sub-category" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<b-field label="Class Title">
													<validation-provider :rules="{required:true, isSelected:filteredTitleObj.length}" v-slot="{ errors, classes }">
														<b-autocomplete
															:class="classes"
															ref="classTitleAutocomplete"
															v-model="classSchedule.payload.ClassTitle"
															:data="filteredTitleObj"
															placeholder="Search class title"
															field="title"
															@select="onTitleSelect($event)"
															:clearable="true">
															<template slot="header">
																<a @click="addNewTitle">
																	<span> Add new... </span>
																</a> 
															</template>
															<template slot="empty">No results for {{classSchedule.payload.ClassTitle}}</template>
														</b-autocomplete>
														<p class="error">{{errors[0]}}</p>
													</validation-provider>
												</b-field>
												<b-field label="Related Course">
													<b-select 
														@input="onRelatedCourceChange($event)"
														v-model="classSchedule.relatedCourses"
														placeholder="Select">
														<option value="">Select</option>
														<option 
															v-if="relatedCourses.data.length !== 0"
															v-for="(course, courseIndex) in relatedCourses.data" 
															:key="courseIndex"
															:value="course.course_id">
															{{course.course_name}}
														</option>
													</b-select>
												</b-field>
												<div class="field cardRadio" v-if="batches.isActive" label="Batch">
													<label class="label">Batch <small class="helper">(Choose one option)</small></label>
													<validation-provider 
														tag="div"
														:rules="{required: false}" 
														v-slot="{ errors, classes }">
															<div class="listWrapper">
																<div class="noBatchFound" v-if="allBatches.error">
																	<i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{allBatches.errorData}}
																</div>
																<template v-for="(item, index) in allBatches.data">
																	<b-radio-button 
																		:key="index"
																		v-model="classSchedule.payload.BatchID"
																		@input="onBatchSelect(item)"
																		:native-value="item.batch_id">
																		<ul class="batchList">
																			<li>
																				<div class="listValue">{{ item.time }}</div>
																				<div class="listValue">{{ item.class_days }}</div>
																				<div class="listValue">({{ 'Batch ID: ' + item.batch_id}})</div>
																			</li>
																		</ul>
																	</b-radio-button>
																</template>
															</div>
														<p class="error">{{errors[0]}}</p>
													</validation-provider>
												</div>
												<b-field label="Description">
													<b-input 
														v-model="classSchedule.payload.ClassDescription"
														type="textarea" 
														disabled>
													</b-input>
												</b-field>
												<b-field label="Related to a course" v-if="hideField">
													<b-taginput
														v-model="tags"
														:data="filteredTags"
														autocomplete
														field="user.first_name"
														icon="label"
														placeholder="Add a course"
														@typing="getFilteredTags">
														<template slot-scope="props">
															<strong>{{props.option.id}}</strong>: {{props.option.user.first_name}}
														</template>
														<template slot="empty">
															There are no items
														</template>
													</b-taginput>
												</b-field>
												<b-field label="Class Privacy" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<div class="inlineFields">
													<b-field label="Date">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-datepicker
																:class="classes"
																v-model="classSchedule.date"
																:date-formatter="formatDate"
																placeholder="Pick date"
																:mobile-native="false"
																trap-focus>
															</b-datepicker>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
													<b-field label="Start Time">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-timepicker
																:class="classes"
																v-model="classSchedule.time"
																placeholder="Pick time"
																hour-format="12"
																:mobile-native="false"
																icon="clock">
															</b-timepicker>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
													<b-field label="Duration">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-select 
																:class="classes"
																v-model="classSchedule.payload.ClassDuration"
																placeholder="Select">
																<option value="">Select</option>
																<option value="15">15 Minutes</option>
																<option value="30">30 Minutes</option>
																<option value="45">45 Minutes</option>
																<option value="60">1 Hour</option>
																<option value="75"> 1 Hour 15 Minutes</option>
																<option value="90">1 Hour 30 Minutes</option>
																<option value="105">1 Hour 45 Minutes</option>
																<option value="120">2 Hours</option>
															</b-select>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
												</div>
												<b-field label="Instructor" v-if="hideField">
													<b-autocomplete
														v-model="name"
														placeholder="e.g. Anne"
														:data="filteredDataObj"
														field="user.first_name"
														@select="option => (selected = option)"
														:clearable="true">
													</b-autocomplete>
												</b-field>
												<b-field label="Zoom account to be used" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<div class="radioList groupElement" v-if="hideField">
													<p class="fieldLabel">Recording</p>
													<small class="helper">Choose one option</small>
													<div class="field">
														<b-radio 
															native-value="Do not record"
															name="recording">
															Do not record
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record but do not automatically share with anyone"
															name="recording">
															Record but do not automatically share with anyone
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record and share with anyone who enrolls"
															name="recording">
															Record and share with anyone who enrolls
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record and share with only those who attend"
															name="recording">
															Record and share with only those who attend
														</b-radio>
													</div>
												</div>
											</div>
											<div class="ctaWrapper">
												<b-button
													native-type="submit"
													class="yunoSecondaryCTA">
														<template v-if="isEditClass()">
															Update Class
														</template>
														<template v-else>
															Schedule       
														</template>
												</b-button>   
											</div>
										</div>
										<div class="col-12 col-md-5 col-lg-5 inviteWrapper">
											<b-field id="enrollLearners" label="Add People">
												<validation-provider ref="inviteUser" :rules="{email:inviteEmail}" v-slot="{ errors, classes }">
													<b-autocomplete
														v-model="classSchedule.currentLearner"
														ref="inviteUserAutocomplete"
														:class="classes"
														:data="filteredLernerObj"
														placeholder="Enter learner or group name"
														field="full_name"
														@select="onLernerSelect($event)"
														:clearable="true">
														<template slot="header">
															<a @click="inviteOtherUser">
																<span>Add this user...</span>
															</a> 
														</template>
														<template slot="empty">No results for {{classSchedule.currentLearner}}</template>
														<template slot-scope="props">
															<div class="userItem">
																<figure class="userImg">
																	<template v-if="props.option.image_url !== undefined && props.option.image_url !== ''">
																		<img :src="props.option.image_url" :alt="props.option.full_name">
																	</template>
																	<template v-else-if="props.option.image_url === ''">
																		<i class="fa fa-user-circle" aria-hidden="true"></i>
																	</template>
																	<template v-else>
																		<i class="fa fa-users" aria-hidden="true"></i>
																	</template>
																</figure>
																<div class="userName">{{props.option.full_name}}</div>
															</div>
														</template>
													</b-autocomplete>
													<p class="error">{{errors[0]}}</p>
												</validation-provider>
											</b-field>
											<template v-if="classSchedule.payload.learner.length">
												<p class="count">People Added: <span>{{classSchedule.learnerSelected}}</span></p>
												<ul class="selectedLearners list-group">
													<li
														class="list-group-item"
														v-for="(learner, learnerIndex) in classSchedule.payload.learner"
														:key="learnerIndex">
														<figure class="userImg">
															<template v-if="learner.image_url !== undefined && learner.image_url !== ''">
																<img :src="learner.image_url" :alt="learner.full_name">
															</template>
															<template v-else-if="learner.image_url === ''">
																<i class="fa fa-user-circle" aria-hidden="true"></i>
															</template>
															<template v-else>
																<template v-if="learner.group_id !== undefined">
																	<i class="fa fa-users" aria-hidden="true"></i>
																</template>
																<template v-else>
																	<i class="fa fa-user-circle" aria-hidden="true"></i>
																</template>
															</template>
														</figure>
														<div class="userName">
															<template v-if="learner.users !== undefined">
																<b-collapse animation="slide" :open="false" aria-id="contentIdForA11y1">
																	<div class="trigger" slot="trigger" aria-controls="contentIdForA11y1">
																		<span class="caption">{{learner.full_name}}</span>
																		<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>
																	</div>
																	<div class="groupLearners">
																		<ul>
																			<li v-for="(groupLearner, groupLearnerIndex) in learner.users">
																				{{groupLearner.name}} ({{groupLearner.email}})
																			</li>
																		</ul>
																	</div>
																</b-collapse>
															</template>
															<template v-else>
																<span class="caption">{{learner.full_name}}</span>
																<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>
															</template>
														</div>
													</li>
												</ul>
											</template>
											<template v-if="allLearners.loading">
												<ul class="suggestUser">
													<li class="title has2Col">
														<div class="caption">Suggested People</div>
													</li>
													<li v-for="i in suggestUser.loadingResult" :key="i" class="loading">
														<div class="media">
															<b-skeleton circle width="24px" height="24px"></b-skeleton>
															<b-skeleton active width="100px"></b-skeleton>
														</div>
													</li>
												</ul>
											</template>
											<template v-if="allLearners.success">
												<template v-if="allLearners.error === null">
													<ul class="suggestUser">
														<li class="title has2Col">
															<div class="caption">Suggested People</div>
															<div class="secondaryElement">
																<b-checkbox class="selectAll" v-model="suggestUser.selectAll" @input="suggestUserSelect" :disabled="suggestUser.selectAll">
																	Select All
																</b-checkbox>
															</div>
														</li>
														<li v-for="(item, i) in allLearners.data">
															<div class="media">
																<img class="userImg" :src="item.image_url" :alt="item.full_name" width="40" height="40">
																<span class="name">{{item.full_name}}</span>
															</div>
															<a href="#" @click.prevent="picksuggestedUser(item)" class="addToPeople"><span class="material-icons">add</span></a>
														</li>
													</ul>
												</template>
												<template v-else>
													<ul class="suggestUser">
														<li class="title has2Col">
															<div class="caption">Suggested People</div>
														</li>
														<li>
															{{ allLearners.errorData }}    
														</li>
													</ul>
												</template>
											</template>
										</div>
									</div>
								</form>    
							</validation-observer>
						</section>
						<b-modal 
							:active.sync="googlePermission.modal" 
							:width="500" 
							:can-cancel="false" 
							class="yunoModal">
								<div class="modalHeader">
									<h2 class="modalTitle">Google Permissions</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper">
										<p class="marginBtm16">Please grant following the permission to schedule a class on Google Meet:</p>
										<ul class="classFields">
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar</span>
											</li>
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar Events</span>
											</li>
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Reports</span>
											</li>
										</ul>
										<p class="userNote"><i class="fa fa-info-circle" aria-hidden="true"></i> You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span></p>
									</div>
								</div>
								<div class="modalFooter">
									<b-button
										tag="a"
										:href="getGoogleMeetURL"
										@click="initGooglePermission()"
										class="yunoSecondaryCTA">
										Grant Permission
									</b-button>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="isCourseSelect" 
							:width="450" 
							:can-cancel="['escape', 'x']" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Please Select Academy</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<b-field label="Academy">
										<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
											<b-select 
											:class="classes"
											v-model="classSchedule.payload.academy_id"
											placeholder="Select Academy"
												expanded>
											<option value="">Select Academy</option>
											<template v-for="item in userInfo.data.academies">
												<option :value="item.id">{{item.name}}</option>
											</template>
											</b-select>
											<p class="error">{{errors[0]}}</p>
										</validation-provider>
										</b-field>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button
											:loading="classSchedule.isLoading === true ? true : false"
											:disabled="classSchedule.isLoading === true ? true : false" 
											native-type="submit"
											@click="confirmClass"
											class="yunoSecondaryCTA">
											Save
										</b-button>
									</div>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="classSchedule.modal" 
							:width="450" 
							:can-cancel="['escape', 'x']" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Confirm before scheduling</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<ul class="classFields">
											<li>
												<span class="caption">Class Title:</span>
												{{classSchedule.payload.ClassTitle}}
											</li>
											<li>
												<span class="caption">Date & Time:</span>
												{{dateTimeFormat}}
											</li>
											<li>
												<span class="caption">Duration:</span>
												{{classSchedule.payload.ClassDuration}} Minutes
											</li>
											<li v-if="classSchedule.payload.learner.length !== 0">
												<span class="caption">People({{ classSchedule.learnerSelected }}):</span>

												<ul class="selectedLearners list-group">
													<li
														class="list-group-item"
														v-for="(learner, learnerIndex) in classSchedule.payload.learner"
														:key="learnerIndex">
														<span class="caption">{{learner.full_name}}</span>
													</li>
												</ul>
											</li>
										</ul>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button
											:loading="classSchedule.isLoading === true ? true : false"
											:disabled="classSchedule.isLoading === true ? true : false" 
											native-type="submit"
											@click="classSchedule.payload.learner.length !== 0 ? submitForm() : noPeople()"
											class="yunoSecondaryCTA">
											Go ahead and schedule
										</b-button>
									</div>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="classSchedule.successModal" 
							:width="500" 
							:can-cancel="false" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Class scheduled successfully</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<ul class="classFields">
											<li>
												<span class="caption">Class Title:</span>
												{{classSchedule.data.Title}}
											</li>
											<li>
												<span class="caption">Date & Time:</span>
												{{dateTimeFormat}}
											</li>
											<li>
												<span class="caption">Guest link:</span>
												<p class="listSubtitle">Your learner will receive email and WhatsApp notifications regarding this class</p>
												<div class="clipboard">
													<b-input id="yunoClassLink" :value="classSchedule.data.guestURL" readonly></b-input>
													<i @click="copyToClipboard('yunoClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
												Note: anyone with a Yuno account can attend the class with this link
											</li>
											<li v-if="false">
												<span class="caption">Zoom Meeting ID:</span>
												<div class="clipboard">
													<b-input id="yunoZoomMeetingID" :value="classSchedule.data.zoom_meeting_id" readonly></b-input>
													<i @click="copyToClipboard('yunoZoomMeetingID')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
											<li v-if="false">
												<span class="caption">Yuno class link:</span>
												<div class="clipboard">
													<b-input id="yunoClassLink" :value="classSchedule.data.ClassLink" readonly></b-input>
													<i @click="copyToClipboard('yunoClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
											<li v-if="false">
												<span class="caption">Zoom class link:</span>
												<div class="clipboard">
													<b-input id="zoomClassLink" :value="classSchedule.data.ZoomClassLink" readonly></b-input>
													<i @click="copyToClipboard('zoomClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
										</ul>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button tag="a"
											href="/instructor"
											class="yunoSecondaryCTA">
											Go to My Schedule
										</b-button>
									</div>
								</div>
						</b-modal>
					</template>
				</template>
				<template v-else>
					<section class="disableAccount">
						<div class="container">
							<div class="row">
								<div class="col-12 col-md-8 offset-md-2">
									<div class="wrapper">
										<span class="material-icons-outlined disableIcon">
											event
										</span>
										<p class="desciprtion">
											You can not schedule a class yet. To schedule classes please contact at <a href="mailto:<EMAIL>"><EMAIL></a>
										</p>
									</div>
								</div>
							</div>
						</div>
					</section>
				</template>
			</template>
		</yuno-page-grid>
    `,
	data() {
		return {
			isInProcess: false,
			isMiniSidebar: false,
			authorizedRoles: ["Instructor"],
			hideField: false,
			selected: null,
			isSelectOnly: false,
			tags: [],
			dateTimeFormat: "",
			inviteEmail: false,
			isCourseSelect: false,
			googlePermission: {
				modal: false,
				isLoading: false,
			},
			message: {
				common: "Error occurred, please try again later",
			},
			batches: {
				isActive: false,
				selected: null,
				current: "",
				isRequired: false,
			},
			suggestUser: {
				isActive: false,
				selectAll: false,
				loadingResult: 3,
				users: [
					{
						email: "<EMAIL>",
						id: 6939,
						name: "yunotesting 4 (<EMAIL>)",
						photo:
							"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg",
					},
					{
						email: "<EMAIL>",
						id: 6940,
						name: "yunotesting 64 (<EMAIL>)",
						photo:
							"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg",
					},
				],
			},
		};
	},
	computed: {
		...Vuex.mapState([
			"user",
			"header",
			"userProfile",
			"userRole",
			"footer",
			"classSchedule",
			"classTitle",
			"myLearners",
			"classEdit",
			"loader",
			"addClassTitle",
			"relatedCourses",
			"userInfo",
			"allBatches",
			"allLearners",
		]),
		filterBatches() {
			return this.allBatches.data.filter((option) => {
				return (
					option.search
						.toString()
						.toLowerCase()
						.indexOf(this.batches.current.toLowerCase()) >= 0
				);
			});
		},
		getHomeURL() {
			return this.$store.state.homeURL;
		},
		getGoogleMeetURL() {
			return this.$store.getters.googleMeet;
		},
		isFormLoading: {
			get() {
				let module = "";

				if (this.isEditClass()) {
					module =
						this.classTitle.loading ||
						this.myLearners.loading ||
						this.relatedCourses.loading ||
						this.classEdit.loading;
				} else {
					module =
						this.classTitle.loading ||
						this.myLearners.loading ||
						this.relatedCourses.loading;
				}

				return module;
			},
		},
		isFormReady: {
			get() {
				let module = "";
				if (this.isEditClass()) {
					module =
						this.classTitle.success &&
						this.myLearners.success &&
						this.relatedCourses.success &&
						this.classEdit.success;
				} else {
					module =
						this.classTitle.success &&
						this.myLearners.success &&
						this.relatedCourses.success;
				}

				return module;
			},
		},
		filteredTitleObj() {
			if (this.classTitle.data.length !== 0) {
				return this.classTitle.data.filter((option) => {
					return (
						option.title
							.toString()
							.toLowerCase()
							.indexOf(this.classSchedule.payload.ClassTitle.toLowerCase()) >= 0
					);
				});
			} else {
				return [];
			}
		},
		filteredLernerObj() {
			return this.myLearners.data.filter((option) => {
				return (
					option.full_name
						.toString()
						.toLowerCase()
						.indexOf(this.classSchedule.currentLearner.toLowerCase()) >= 0
				);
			});
		},
	},
	async created() { },
	mounted() {
		this.initiateEdit();
		localStorage.removeItem("userState");
	},
	methods: {
		onUserInfo(data) {
			if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
				// this.isPermissionModal(data);
				this.fetchMyLearners();
				this.fetchClassTitle();
				this.fetchRelatedCourses();
			}
		},
		formatDate(d) {
			return moment(d).format("ddd, MMM DD, YYYY");
		},
		suggestUserSelect(e) {
			let learners = this.classSchedule.payload.learner,
				users = this.allLearners.data;

			if (e) {
				for (let i = 0; i < users.length; i++) {
					const user = users[i],
						hasAdded = YUNOCommon.findObjectByKey(learners, "id", user.id);

					if (hasAdded === null) {
						learners.push(user);
						this.classSchedule.learnerSelected = parseInt(
							this.classSchedule.learnerSelected + 1
						);
					}
				}

				// this.classSchedule.learnerSelected = parseInt(this.classSchedule.learnerSelected + users.length);
			} else {
				this.classSchedule.payload.learner = [];
				this.classSchedule.learnerSelected = 0;
				// this.classSchedule.learnerSelected = Math.abs(parseInt(this.classSchedule.learnerSelected - users.length));
			}
		},
		onBatchSelect(data) {
			this.suggestUser.isActive = true;
			this.classSchedule.payload.academy_id = data.academy_id;
			this.fetchBatchLearners(data.batch_id);
		},
		gotBatchLearners(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				const data = options.response.data.data;
			}
		},
		fetchBatchLearners(batchID) {
			this.allLearners.success = false;
			this.allLearners.error = null;
			this.allLearners.errorData = [];
			this.allLearners.data = [];

			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.batch("learners", {batchID: batchID}),
				module: "gotData",
				store: "allLearners",
				callback: true,
				callbackFunc: function (options) {
					return instance.gotBatchLearners(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		onRelatedCourceChange(data) {
			let isData = data !== "";

			if (isData) {
				this.classSchedule.payload.BatchID = "";
				this.batches.isActive = true;
				this.batches.isRequired = true;
				this.enableLoader(true);
				this.fetchAllBatches(data);
			} else {
				this.batches.isActive = false;
				this.batches.isRequired = false;
				this.classSchedule.payload.BatchID = "";
			}
		},
		gotAllBatches(options, batchID) {
			this.enableLoader(false);

			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let getData = options.response.data.data;

				if (getData.length === 1) {
					// If there is only one batch
					this.classSchedule.payload.BatchID = getData[0].batch_id;
					this.onBatchSelect(getData[0]);
				}

				this.allBatches.data = getData;
				this.allBatches.error = null;

				if (this.isEditClass()) {
					//For Edit mode
					this.classSchedule.payload.BatchID = batchID;
				}
			} else {
				this.allBatches.data = [];
			}
		},
		fetchAllBatches(courseID, batchID) {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.courseListAPI("batch", courseID, isLoggedIn),
				module: "gotData",
				store: "allBatches",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotAllBatches(options, batchID);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		enableLoader(state) {
			this.isInProcess = state;
		},
		initGooglePermission() {
			localStorage.setItem("userState", window.location.pathname);
		},
		isPermissionModal(data) {
			if (
				data.vc_google &&
				data.ins_meet_permission &&
				!data.ins_meet_permission_verified
			) {
				this.googlePermission.modal = true;
			}
		},
		inviteOtherUser() {
			this.inviteEmail = true;

			setTimeout(() => {
				if (this.$refs.inviteUser.flags.valid) {
					let getLerners = this.classSchedule.payload.learner,
						userObj = {
							email: this.classSchedule.currentLearner,
							id: "",
							name: this.classSchedule.currentLearner,
							photo: "",
						};

					this.$refs.inviteUserAutocomplete.setSelected(userObj);
				}
			}, 40);
		},
		newTitleAdded(options) {
			this.loader.overlay = false;
			this.loader.isActive = false;

			if (options.response !== undefined && options.response.status === 201) {
				this.$buefy.toast.open({
					duration: 5000,
					message: `${options.response.data.message}`,
					position: "is-bottom",
				});

				let newTitle = {
					description: "",
					title: this.addClassTitle.payload.title,
				};

				this.classTitle.data.push(newTitle);
				this.$refs.classTitleAutocomplete.setSelected(newTitle);
			} else {
				this.$buefy.toast.open({
					duration: 5000,
					message: `${options.response.data.message}`,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		postNewTitle() {
			let payload = this.addClassTitle.payload,
				instance = this;

			const options = {
				apiURL: YUNOCommon.config.addClassTitleAPi(),
				module: "gotData",
				store: "addClassTitle",
				payload: payload,
				callback: true,
				callbackFunc: function (options) {
					return instance.newTitleAdded(options);
				},
			};

			this.$store.dispatch("postData", options);
		},
		addNewTitle() {
			let payload = this.addClassTitle.payload;

			payload.instructor_id = isLoggedIn;
			payload.title = this.classSchedule.payload.ClassTitle;

			this.loader.overlay = true;
			this.loader.isActive = true;
			this.postNewTitle();
		},
		updateLearners(userID, getMyLearners, classObj, userType) {
			let typeOfUser = "";

			if (userType === "group") {
				typeOfUser = "group_id";
			}

			if (userType === "learner") {
				typeOfUser = "id";
			}

			let selectedGroup = YUNOCommon.findObjectByKey(
				getMyLearners,
				typeOfUser,
				Number(userID)
			);

			classObj.push(selectedGroup);

			YUNOCommon.removeObjInArr(getMyLearners, "name", selectedGroup.name);

			if (selectedGroup.user_count !== undefined) {
				this.classSchedule.learnerSelected = parseInt(
					selectedGroup.user_count + this.classSchedule.learnerSelected
				);
			} else {
				this.classSchedule.learnerSelected =
					this.classSchedule.learnerSelected === ""
						? 1
						: parseInt(this.classSchedule.learnerSelected + 1);
			}
		},
		hasGroupID() {
			let isGroupID = YUNOCommon.getQueryParameter("groupID"),
				isLearnerID = YUNOCommon.getQueryParameter("learnerID"),
				getMyLearners = this.myLearners.data,
				classObj = this.classSchedule.payload.learner;

			if (isGroupID) {
				this.updateLearners(isGroupID, getMyLearners, classObj, "group");
			}

			if (isLearnerID) {
				this.updateLearners(isLearnerID, getMyLearners, classObj, "learner");
			}
		},
		isEditClass() {
			const getClassState = localStorage.getItem("classEditState");

			if (getClassState !== null) {
				return true;
			} else {
				return false;
			}
		},
		initiateEdit() {
			if (this.isEditClass()) {
				let getClassState = localStorage.getItem("classEditState"),
					parseIt = JSON.parse(getClassState);

				this.prefillClass(parseIt.id, isLoggedIn);
			}
		},
		gotClassDetail(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let getData = options.response.data.data,
					moduleObj = this.classEdit,
					myLearnerObj = this.myLearners,
					classObj = this.classSchedule,
					classPayload = classObj.payload,
					dateTime = new Date(getData.start + ":" + getData.time);

				classObj.date = dateTime;
				classObj.time = dateTime;

				classPayload.ClassTitle = getData.title;
				classPayload.ClassDescription = getData.description;
				classPayload.ClassDuration = getData.duration;
				classObj.relatedCourses = getData.related_course;
				classPayload.academy_id = getData.academy_id;

				if (getData.related_course !== "") {
					this.batches.isActive = true;
					this.batches.isRequired = true;
					this.fetchAllBatches(getData.related_course, getData.batch_id);
				}

				if (getData.users_groups.length !== 0) {
					for (var i = 0; i < getData.users_groups.length; i++) {
						classPayload.learner.push(getData.users_groups[i]);

						YUNOCommon.removeObjInArr(
							myLearnerObj.data,
							"name",
							getData.users_groups[i].name
						);

						if (getData.users_groups[i].user_count !== undefined) {
							classObj.learnerSelected = parseInt(
								getData.users_groups[i].user_count + classObj.learnerSelected
							);
						} else {
							classObj.learnerSelected =
								classObj.learnerSelected === ""
									? 1
									: parseInt(classObj.learnerSelected + 1);
						}
					}
				}

				moduleObj.data = getData;
			}
		},
		prefillClass(classID, userID) {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.editClassAPI(classID, userID),
				module: "gotData",
				store: "classEdit",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotClassDetail(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		initForm() {
			let dateObj = {
				date: moment(this.classSchedule.date).format("MMM DD, YYYY"),
				readableDate: moment(this.classSchedule.date).format(
					"ddd, MMM DD, YYYY"
				),
				time: moment(this.classSchedule.time).format("HH:mm"),
				time12Hour: moment(this.classSchedule.time).format("hh:mm a"),
			};

			let dateToUTC = moment(
				dateObj.date + " " + dateObj.time,
				"MMMM Do YYYY, HH:mm"
			).utc();

			this.classSchedule.payload.ClassDate = "" + dateToUTC._d + "";
			this.classSchedule.payload.ClassTime = "" + dateToUTC._d + "";
			this.classSchedule.payload.RelatedCourses = "";
			this.classSchedule.payload.RelatedCourses =
				this.classSchedule.relatedCourses;
			this.dateTimeFormat = dateObj.readableDate + ", " + dateObj.time12Hour;
			this.classSchedule.payload.academy_id !== ""
				? (this.classSchedule.modal = true)
				: (this.isCourseSelect = true);

			if (this.isEditClass()) {
				//For Edit mode
				let getClassState = localStorage.getItem("classEditState"),
					parseIt = JSON.parse(getClassState);

				this.classSchedule.payload.ClassId = parseIt.id;
			}
		},
		classScheduleDone(options) {
			this.classSchedule.isLoading = false;
			this.enableLoader(false);

			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 201
			) {
				localStorage.removeItem("classEditState");
				this.classSchedule.data = options.response.data.data;
				this.classSchedule.modal = false;
				this.classSchedule.successModal = true;
			} else {
				// let msg = this.classSchedule.errorData.TimeCheck !== undefined ? this.classSchedule.errorData.TimeCheck : this.classSchedule.errorData;

				this.$buefy.toast.open({
					duration: 5000,
					message: this.classSchedule.errorData,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		confirmClass() {
			if (this.classSchedule.payload.academy_id !== "") {
				this.classSchedule.modal = true;
				this.isCourseSelect = false;
			} else {
				this.classSchedule.modal = false;
				this.$buefy.toast.open({
					duration: 1000,
					message: `Please select an academy`,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		proceedSchedule() {
			this.enableLoader(true);
			this.submitForm();
		},
		noPeople() {
			this.classSchedule.modal = false;
			this.$buefy.dialog.confirm({
				title: "Add People",
				message: "You have not added any people in this class",
				cancelText: "Go Back",
				confirmText: "Proceed Anyway",
				onConfirm: () => this.proceedSchedule(),
			});
		},
		submitForm() {
			this.classSchedule.isLoading = true;
			let setAPIURL = "";

			if (this.isEditClass()) {
				//For Edit mode
				setAPIURL = YUNOCommon.config.updateClassAPi(isLoggedIn);
			} else {
				setAPIURL = YUNOCommon.config.createClassAPi(isLoggedIn);
			}
			const instance = this,
				options = {
					apiURL: setAPIURL,
					module: "gotData",
					store: "classSchedule",
					payload: this.classSchedule.payload,
					callback: true,
					callbackFunc: function (options) {
						return instance.classScheduleDone(options);
					},
				};

			this.$store.dispatch("postData", options);
		},
		copyToClipboard(ele) {
			let copyText = document.getElementById(ele);

			copyText.select();
			copyText.setSelectionRange(0, 99999);
			document.execCommand("copy");

			this.$buefy.toast.open({
				duration: 1000,
				message: `Copy to clipboard`,
				position: "is-bottom",
				type: "is-info",
			});
		},
		updateDateFormat(getDate, getTime) {
			this.dateTimeFormat = getDate + ", " + getTime;
		},
		onModalClose() { },
		removeLearner(obj) {
			let getLerners = this.classSchedule.payload.learner,
				refineLearnerObj = YUNOCommon.removeObjInArr(
					getLerners,
					"full_name",
					obj.full_name
				);

			this.myLearners.data.push(obj);
			getLerners = refineLearnerObj;

			if (obj.user_count !== undefined) {
				this.classSchedule.learnerSelected = Math.abs(
					parseInt(obj.user_count - this.classSchedule.learnerSelected)
				);
			} else {
				this.classSchedule.learnerSelected = Math.abs(
					parseInt(this.classSchedule.learnerSelected - 1)
				);
			}

			if (this.classSchedule.payload.learner.length === 0) {
				this.suggestUser.selectAll = false;
			}
		},
		onTitleSelect($event) {
			if ($event !== null) {
				let courseID = "";

				if ($event.course_id !== undefined) {
					courseID = $event.course_id !== "" ? Number($event.course_id) : "";
					this.onRelatedCourceChange(courseID);
				} else {
					courseID = "";
				}

				this.classSchedule.payload.ClassDescription = $event.description;
				this.classSchedule.relatedCourses = courseID;
			} else {
				this.classSchedule.payload.ClassDescription = "";
				this.classSchedule.relatedCourses = "";
				this.classSchedule.payload.BatchID = "";
				this.batches.isActive = false;
			}
		},
		picksuggestedUser(data) {
			let learners = this.classSchedule.payload.learner,
				userData = YUNOCommon.findObjectByKey(learners, "id", data.id);

			if (userData === null) {
				learners.push(data);

				this.classSchedule.learnerSelected =
					this.classSchedule.learnerSelected === ""
						? 1
						: parseInt(this.classSchedule.learnerSelected + 1);
			}
		},
		onLernerSelect($event) {
			if ($event !== null) {
				let getLerners = this.classSchedule.payload.learner;

				getLerners.push($event);

				let refineLearnerObj = YUNOCommon.removeObjInArr(
					this.myLearners.data,
					"full_name",
					$event.full_name
				);
				this.myLearners.data = refineLearnerObj;

				if ($event.user_count !== undefined) {
					this.classSchedule.learnerSelected = parseInt(
						$event.user_count + this.classSchedule.learnerSelected
					);
				} else {
					this.classSchedule.learnerSelected =
						this.classSchedule.learnerSelected === ""
							? 1
							: parseInt(this.classSchedule.learnerSelected + 1);
				}

				setTimeout(() => (this.classSchedule.currentLearner = ""), 10);
			}
		},
		gotMyLearner(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				for (let i = 0; i < data.length; i++) {
					const learner = data[i];
					const obj = {
						id: learner.id,
						full_name: learner.name,
						image_url: learner.photo,
						email: learner.email
					};

					this.myLearners.data.push(obj);
				}

				
				this.hasGroupID();
			} else {
				this.myLearners.data = [];
			}
		},
		fetchMyLearners() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.myLearnersAPi(isLoggedIn),
				module: "gotData",
				store: "myLearners",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotMyLearner(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		gotRelatedCourses(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				this.relatedCourses.data = data;
			} else {
				this.relatedCourses.data = [];
			}
		},
		fetchRelatedCourses() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.relatedCoursesAPI(isLoggedIn),
				module: "gotData",
				store: "relatedCourses",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotRelatedCourses(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		gotClassTitle(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				this.classTitle.data = data;
			} else {
				this.classTitle.data = [];
			}
		},
		fetchClassTitle() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.classTitleAPi(isLoggedIn),
				module: "gotData",
				store: "classTitle",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotClassTitle(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		getFilteredTags(text) {
			this.filteredTags = data.filter((option) => {
				return (
					option.user.first_name
						.toString()
						.toLowerCase()
						.indexOf(text.toLowerCase()) >= 0
				);
			});
		},
	},
});
