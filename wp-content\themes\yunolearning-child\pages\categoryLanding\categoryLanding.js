window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 10 numbers required",
        "is": "Required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);
Vue.component('star-rating', VueStarRating.default);
YUNOAccordion.accordion();
const VueSocialSharing = window.VueSocialSharing;
Vue.component('ShareNetwork', VueSocialSharing.ShareNetwork)

Vue.component('yuno-category-landing', {
    template: `
        <yuno-page-grid
            :class="[isFilterMobile ? 'filterActive' : '', user.isLoggedin ? 'postLogin' : '']"
            :zohoMeta="zohoMeta"
            :hasPageHeader="isElementVisible"
            :hasPageFooter="isElementVisible"
        >
            <template v-slot:aboveMain>
                <div id="headerTop"></div>
            </template>
            <template v-slot:main>
                <template v-if="!isElementVisible && categoryTaxonomy.loading">
                    <b-skeleton height="350px"></b-skeleton>
                </template>
                <yuno-hero-banner 
                    v-show="isElementVisible" 
                    @categoryData="categoryData"
                    :hideSignupToCategories="['sat', 'gre', 'gmat', 'cuet']"
                    :isElementVisible="isElementVisible"
                >
                </yuno-hero-banner>
                
                <div class="container" v-if="categoryTaxonomy.success">
                    <yuno-tabs v-show="isElementVisible"></yuno-tabs>
                    <template v-for="(tab, j) in categoryFeatures">
                        <template v-if="tab.feature_slug === 'home' && tab.is_active">
                            <yuno-category-home></yuno-category-home>
                        </template>
                        <template v-if="tab.feature_slug === 'courses' && tab.is_active">
                        <yuno-category-courses-v2 @manageFilters="onManageFilters" :options="{'isElementVisible': isElementVisible}"></yuno-category-courses-v2>
                        </template>
                        <template v-if="tab.feature_slug === 'study-material' && tab.is_active"">
                            <yuno-category-study-material></yuno-category-study-material>
                        </template>
                        <template v-if="tab.feature_slug === 'results' && tab.is_active"">
                            <yuno-category-results></yuno-category-results>
                        </template>
                        <template v-if="tab.feature_slug === 'videos' && tab.is_active"">
                            <yuno-category-videos></yuno-category-videos>
                        </template>
                        <template v-if="tab.feature_slug === 'practice-tests' && tab.is_active"">
                            <yuno-category-practice-tests></yuno-category-practice-tests>
                        </template>
                    </template>
                </div>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            isMiniSidebar: false,
            isFilterMobile: false,
            pageHeader: {
                title: "Writing Task",
            },
            faqOptions: {
                type: "faq",
                hasColumn: "col-md-10 offset-md-1",
                fetchData: true
            },
            isElementVisible: true,
            zohoMeta: {
                "content_type": "",
                "content_id": "",
                "productcode": "",
                "leadstatus": ""
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'faqIELTS',
            'categoryTaxonomy',
            'loader'
        ]),
        isPageLoading: {
            get() {
                const module =  this.userInfo.loading 
                                || this.header.loading 
                                || this.footer.loading 
                return module
            }
        },
        isPageReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module =    this.header.success
                                && this.footer.success 
                                && this.userInfo.success
                } else {
                    module =    this.header.success
                                && this.footer.success 
                                
                }

                return module
            }
        },
        isUserLoading: {
            get() {
                const module =  this.userInfo.loading 

                return module
            }
        },
        isUserReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module = this.userInfo.success
                } else {
                    module = true 
                }

                return module
            }
        },
        categoryFeatures() {
            return this.categoryTaxonomy.data.category_features
        }
    },
    async created() {
        this.fetchFooter();
    },
    mounted() {
        this.manageElementVisibility();
    },
    methods: {
        manageElementVisibility() {
            const forad = YUNOCommon.getQueryParameter("forad") === "true" ? true : false;

            if (forad) {
                this.isElementVisible = false;
            } else {
                this.isElementVisible = true;
            }
        },
        onManageFilters(isActive) {
            this.isFilterMobile = isActive;
        },
        categoryData(data) {
            this.zohoMeta.content_type = data.zoho_meta.content_type;
            this.zohoMeta.content_id = data.zoho_meta.content_id;
            this.zohoMeta.productcode = data.zoho_meta.productcode;
            this.zohoMeta.leadstatus = data.zoho_meta.leadstatus;
        },
        fetchFooter() {
            const options = {
                apiURL: YUNOCommon.config.footerAPI(),
                module: "gotData",
                store: "footer",
                callback: false
            };

            this.$store.dispatch('fetchData', options);
        }
    }
});