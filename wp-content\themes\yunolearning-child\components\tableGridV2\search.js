Vue.component('yuno-search', {
    props: {
        data: {
            type: Object,
            required: true
        },
        defaultFilters: {
            type: Object,
            required: true
        },
        payload: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoAutocompleteSearch" :class="[data.filter]">
            <b-field>
                <b-input 
                    v-model="data.selected"
                    :placeholder="data.placeholder"
                    @input="onSearchInput($event, data)"
                >
                </b-input>
                
            </b-field>
        </div>
    `,
    data() {
        return {
            selectedOption: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onSearchInput: _.debounce(function (name, filter) {
            const payload = this.$props.payload; 

            if (name.length > 2) {
                filter.loading = true;
                filter.items = [];

                payload[this.$props.data.filter] = name;

                this.$emit('onSearchInput', name, filter, false);
            } else {
                filter.items = []
                payload[this.$props.data.filter] = this.$props.defaultFilters[this.$props.data.filter]
                this.$emit('onSearchInput', name, filter, false);
            }
        }, 500),
    }
});