Vue.component('yuno-hero-banner', {
    props: ["data", "options", "hideSignupToCategories", "isElementVisible"],
    template: `
        <section>
            <template v-if="categoryTaxonomy.loading">
                <b-skeleton height="350px"></b-skeleton>
            </template>
            <template v-if="categoryTaxonomy.success">
                <template v-if="categoryTaxonomy.error">
                    {{ categoryTaxonomy.errorData }}
                </template>    
                <template v-else>
                    <div class="heroBanner" :style="heroStyle()">
                        <div class="container">
                            <div class="row">
                                <div class="col-12 col-md-8 colLeft">
                                    <h1 class="largestTitle">{{ category.category_heading }}</h1>
                                    <div class="ctaWrapper">
                                        <b-button 
                                            @click="manageVideoModal()"
                                            class="yunoSecondaryCTA withIcon">
                                            Watch Video <span class="material-icons">smart_display</span>
                                        </b-button>
                                    </div>
                                </div>
                                <div class="col-12 col-md-4 colRight">
                                    <div class="wrapper" v-if="!user.isLoggedin && isSignUp(hideSignupToCategories)">
                                        <h3 class="largerTitle">{{ category.category_name }}</h3>
                                        <p class="body1">{{ category.why_sign_up }}</p>
                                        <validation-observer 
                                            tag="div" 
                                            class="observer"
                                            ref="categorySignupObserver" 
                                            v-slot="{ handleSubmit, invalid }">
                                            <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">
                                                <b-field>
                                                    <validation-provider :customMessages="{ required: 'Phone number is required'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">
                                                        <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>    
                                                </b-field>
                                                <div class="ctaWrapper">
                                                    <button class="googleLogin" type="submit">
                                                        <img :src="yunoStore.themeURL + '/assets/images/google.svg'" alt="google"></img> Signup with Google
                                                    </button>
                                                </div>
                                            </form>
                                        </validation-observer>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <b-modal 
                            :active.sync="video.modal" 
                            :width="800" 
                            :can-cancel="['escape', 'x']"
                            :on-cancel="videoModalClose"
                            class="yunoModal lightTheme">
                                <div class="modalBody">
                                    <div class="videoLPPlayer">
                                        <iframe width="800" height="450" :src="category.video_url + '?autoplay=1'" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                    </div>
                                </div>
                        </b-modal>
                    </div>
                </template>    
            </template>
        </section>
    `,
    data() {
        return {
            signIn: {
                mobile: "",
                categoryURL: "",
                productCode: "",
                leadStatus: "",
                variant: "",
                utmSource: "",
                utmCampaign: "",
                utmMedium: "",
                adGroupID: "",
                adContent: "",
                utmTerm: "",
                gclid: "",
                content: {
                    type: "",
                    id: ""
                },
                landing_page: {
                    url: "",
                    title: ""
                }
            },
            video: {
                modal: false,
                data: []
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'categoryTaxonomy',
            'user',
            'course'
        ]),
        yunoStore() {
            return this.$store.state
        },
        category() {
            return this.categoryTaxonomy.data
        }
    },
    async created() {
        this.fetchCategory();
    },
    mounted() {
        
    },
    methods: {
        isSignUp(categories) {
            if (YUNOCommon.findInArray(categories, categorySlug)) {
                return false
            } else {
                return true
            }
        },
        heroStyle() {
            return {
                "background-image": "url("+ this.category.banner_image +")"
            }
        },
        videoModalClose() {
            this.video.modal = false;
            this.video.data = [];
        },
        setPayload() {
            let payload = this.signIn,
                isCategory = "";

                if (typeof yunoCategory !== 'undefined') {
                    isCategory = yunoCategory !== "" ? yunoCategory : "general";
                } else {
                    isCategory = "general";
                };

            payload.categoryURL = `/${isCategory}`;
            payload.landing_page.url = window.location.origin + window.location.pathname;
            payload.landing_page.title = document.title;
            payload.productCode = "";
            payload.leadStatus = "";
            payload.utmSource = YUNOCommon.getQueryParameter("utm_source");
            payload.utmCampaign = YUNOCommon.getQueryParameter("utm_campaign");
            payload.utmMedium = YUNOCommon.getQueryParameter("utm_medium");
            payload.adGroupID = YUNOCommon.getQueryParameter("adgroupid");
            payload.adContent = YUNOCommon.getQueryParameter("ad_content");
            payload.utmTerm = YUNOCommon.getQueryParameter("utm_term");
            payload.gclid = YUNOCommon.getQueryParameter("gclid");
            payload.content.type = "";
            payload.content.id = "";
        },
        initCommonSignup() {
            this.setPayload();
            localStorage.setItem('userState', window.location.pathname);
            setTimeout(() => { window.location.href = YUNOCommon.config.signInURLWithState(this.signIn) }, 50);
        },
        manageVideoModal() {
            this.video.modal = true;
        },
        gotCategory(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                this.$emit('categoryData', data);
            };
        },
        fetchCategory() {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.categoryLandingPage("category", categoryID),
                module: "gotData",
                store: "categoryTaxonomy",
                callback: true,
                callbackFunc: (options) => this.gotCategory(options)
            };


            if (!this.categoryTaxonomy.loading) {
                 // Dispatch the API call
                this.$store.dispatch('fetchData', options);     
            }
        }
    }
});