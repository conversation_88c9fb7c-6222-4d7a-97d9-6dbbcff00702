.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .carousel-list .carousel-arrow .icon::after {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 30px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

#app .carousel-dots {
  padding-bottom: 45px;
}

#app .carousel-dots .dot {
  display: inline-block;
  height: 10px;
  width: 10px;
  margin: 0 5px;
  background-color: #bbb;
  border-radius: 50%;
  cursor: pointer;
}

#app .carousel-dots .dot.active {
  background-color: #A81E22;
}

#app .carousel-arrow {
  opacity: 1 !important;
  visibility: visible !important;
}

#app .carousel-list.has-shadow {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .carousel-list .carousel-slides .hero-body {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 2rem 1.5rem 0 1.5rem;
}

@media (min-width: 768px) {
  #app .carousel-list .carousel-slides .hero-body {
    padding-top: 15px;
  }
}

#app .carousel-list .carousel-slides .hero-body .contentWrapper {
  text-align: center;
}

@media (max-width: 768px) {
  #app .carousel-list .carousel-slides .hero-body .contentWrapper {
    padding: 30px;
    text-align: center;
  }
}

#app .carousel-list .carousel-slides .hero-body .contentWrapper .description {
  max-width: 600px;
  margin: auto;
}

@media (max-width: 768px) {
  #app .carousel-list .carousel-slides .hero-body .videoWrapper {
    padding: 0px;
    text-align: center;
  }
}

#app .carousel-list .carousel-slides figure .itemInfo {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .carousel-list .carousel-slides figure .itemInfo {
    min-height: 72px;
  }
}

#app .carousel-list .carousel-slides figure img {
  width: 100%;
  height: auto;
}

@media (min-width: 768px) {
  #app .carousel-list .carousel-slides figure img {
    height: 343px;
  }
}

#app .carousel-list .carousel-arrow .icon {
  color: #0000005e;
}

#app .carousel-list .carousel-arrow .icon::after {
  content: "\e5cb";
}

#app .carousel-list .carousel-arrow .icon.has-icons-right {
  right: 0.3rem !important;
  top: 61% !important;
}

#app .carousel-list .carousel-arrow .icon.has-icons-right::after {
  content: "\e5cc";
}

#app .carousel-list .carousel-arrow .icon.has-icons-left {
  left: 0.3rem !important;
  top: 61% !important;
}

#app .carousel-list .carousel-arrow .icon:hover {
  color: #7957d5;
  border: none;
}

#app .carousel-list .carousel-indicator .indicator-item .indicator-style {
  border-color: #d9d9d9;
  background-color: #d9d9d9;
}

#app .carousel-list .carousel-indicator .indicator-item.is-active .indicator-style {
  background-color: #A81E22;
}

#app .carousel-list .cta .secondaryCTA {
  padding: 8px 20px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #a81e22;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

#app .carousel-list .cta .secondaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}
/*# sourceMappingURL=TwoColumnCarouselSlides.css.map */