openapi: 3.1.0
info:
  version: 3.0.0
  title: Yuno V3 APIS -
  summary: Yuno V3 API Description.
  description: This API is accessible to authorized users. Please find the detailed API guide at [ReDocly Documentation](https://dev.yunolearning.com/swaggerHub/yuno-v3-apis/redoc-static.html) 
  contact:
    name: <PERSON><PERSON><PERSON>ning
    url: 'https://yunolearning.com'
    email: gagan<PERSON>t<PERSON><PERSON>@gmail.com
  license:
    name: <PERSON><PERSON>arning
    url: 'https://dev.yunolearning.com/'
servers:
- url: https://dev.yunolearning.com/wp-json/yuno/v3
  description: Yuno Dev Server
- url: https://stage.yunolearning.com/wp-json/yuno/v3
  description: Yuno Stage Server
- url: https://www.yunolearning.com/wp-json/yuno/v3
  description: Yuno Prod Server
externalDocs:
  description: API Guide Documentation Details
  url: https://dev.yunolearning.com/swaggerHub/yuno-v3-apis/redoc-static.html
paths:
  /user/timezones:
    get:
      summary: Timezones List
      parameters:
        - name: accept
          in: header
          description: Timezones List
          schema:
            type: string
          example: application/json
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  status:
                    type: string
                  data:
                    type: array
                    items:
                      type: string
                x-examples:
                  Example 1:
                    code: 200
                    message: Timezones were found
                    status: SUCCESS
                    data:
                      - Africa/Abidjan
                      - Africa/Accra
                      - Africa/Addis_Ababa
                      - Africa/Algiers
                      - Africa/Asmara
                      - Africa/Bamako
                      - Africa/Bangui
                      - Africa/Banjul
                      - Africa/Bissau
                      - Africa/Blantyre
                      - Africa/Brazzaville
                      - Africa/Bujumbura
                      - Africa/Cairo
                      - Africa/Casablanca
                      - Africa/Ceuta
                      - Africa/Conakry
                      - Africa/Dakar
                      - Africa/Dar_es_Salaam
                      - Africa/Djibouti
                      - Africa/Douala
                      - Africa/El_Aaiun
                      - Africa/Freetown
                      - Africa/Gaborone
                      - Africa/Harare
                      - Africa/Johannesburg
                      - Africa/Juba
                      - Africa/Kampala
                      - Africa/Khartoum
                      - Africa/Kigali
                      - Africa/Kinshasa
                      - Africa/Lagos
                      - Africa/Libreville
                      - Africa/Lome
                      - Africa/Luanda
                      - Africa/Lubumbashi
                      - Africa/Lusaka
                      - Africa/Malabo
                      - Africa/Maputo
                      - Africa/Maseru
                      - Africa/Mbabane
                      - Africa/Mogadishu
                      - Africa/Monrovia
                      - Africa/Nairobi
                      - Africa/Ndjamena
                      - Africa/Niamey
                      - Africa/Nouakchott
                      - Africa/Ouagadougou
                      - Africa/Porto-Novo
                      - Africa/Sao_Tome
                      - Africa/Tripoli
                      - Africa/Tunis
                      - Africa/Windhoek
                      - America/Adak
                      - America/Anchorage
                      - America/Anguilla
                      - America/Antigua
                      - America/Araguaina
                      - America/Argentina/Buenos_Aires
                      - America/Argentina/Catamarca
                      - America/Argentina/Cordoba
                      - America/Argentina/Jujuy
                      - America/Argentina/La_Rioja
                      - America/Argentina/Mendoza
                      - America/Argentina/Rio_Gallegos
                      - America/Argentina/Salta
                      - America/Argentina/San_Juan
                      - America/Argentina/San_Luis
                      - America/Argentina/Tucuman
                      - America/Argentina/Ushuaia
                      - America/Aruba
                      - America/Asuncion
                      - America/Atikokan
                      - America/Bahia
                      - America/Bahia_Banderas
                      - America/Barbados
                      - America/Belem
                      - America/Belize
                      - America/Blanc-Sablon
                      - America/Boa_Vista
                      - America/Bogota
                      - America/Boise
                      - America/Cambridge_Bay
                      - America/Campo_Grande
                      - America/Cancun
                      - America/Caracas
                      - America/Cayenne
                      - America/Cayman
                      - America/Chicago
                      - America/Chihuahua
                      - America/Ciudad_Juarez
                      - America/Costa_Rica
                      - America/Creston
                      - America/Cuiaba
                      - America/Curacao
                      - America/Danmarkshavn
                      - America/Dawson
                      - America/Dawson_Creek
                      - America/Denver
                      - America/Detroit
                      - America/Dominica
                      - America/Edmonton
                      - America/Eirunepe
                      - America/El_Salvador
                      - America/Fort_Nelson
                      - America/Fortaleza
                      - America/Glace_Bay
                      - America/Goose_Bay
                      - America/Grand_Turk
                      - America/Grenada
                      - America/Guadeloupe
                      - America/Guatemala
                      - America/Guayaquil
                      - America/Guyana
                      - America/Halifax
                      - America/Havana
                      - America/Hermosillo
                      - America/Indiana/Indianapolis
                      - America/Indiana/Knox
                      - America/Indiana/Marengo
                      - America/Indiana/Petersburg
                      - America/Indiana/Tell_City
                      - America/Indiana/Vevay
                      - America/Indiana/Vincennes
                      - America/Indiana/Winamac
                      - America/Inuvik
                      - America/Iqaluit
                      - America/Jamaica
                      - America/Juneau
                      - America/Kentucky/Louisville
                      - America/Kentucky/Monticello
                      - America/Kralendijk
                      - America/La_Paz
                      - America/Lima
                      - America/Los_Angeles
                      - America/Lower_Princes
                      - America/Maceio
                      - America/Managua
                      - America/Manaus
                      - America/Marigot
                      - America/Martinique
                      - America/Matamoros
                      - America/Mazatlan
                      - America/Menominee
                      - America/Merida
                      - America/Metlakatla
                      - America/Mexico_City
                      - America/Miquelon
                      - America/Moncton
                      - America/Monterrey
                      - America/Montevideo
                      - America/Montserrat
                      - America/Nassau
                      - America/New_York
                      - America/Nome
                      - America/Noronha
                      - America/North_Dakota/Beulah
                      - America/North_Dakota/Center
                      - America/North_Dakota/New_Salem
                      - America/Nuuk
                      - America/Ojinaga
                      - America/Panama
                      - America/Paramaribo
                      - America/Phoenix
                      - America/Port-au-Prince
                      - America/Port_of_Spain
                      - America/Porto_Velho
                      - America/Puerto_Rico
                      - America/Punta_Arenas
                      - America/Rankin_Inlet
                      - America/Recife
                      - America/Regina
                      - America/Resolute
                      - America/Rio_Branco
                      - America/Santarem
                      - America/Santiago
                      - America/Santo_Domingo
                      - America/Sao_Paulo
                      - America/Scoresbysund
                      - America/Sitka
                      - America/St_Barthelemy
                      - America/St_Johns
                      - America/St_Kitts
                      - America/St_Lucia
                      - America/St_Thomas
                      - America/St_Vincent
                      - America/Swift_Current
                      - America/Tegucigalpa
                      - America/Thule
                      - America/Tijuana
                      - America/Toronto
                      - America/Tortola
                      - America/Vancouver
                      - America/Whitehorse
                      - America/Winnipeg
                      - America/Yakutat
                      - Antarctica/Casey
                      - Antarctica/Davis
                      - Antarctica/DumontDUrville
                      - Antarctica/Macquarie
                      - Antarctica/Mawson
                      - Antarctica/McMurdo
                      - Antarctica/Palmer
                      - Antarctica/Rothera
                      - Antarctica/Syowa
                      - Antarctica/Troll
                      - Antarctica/Vostok
                      - Arctic/Longyearbyen
                      - Asia/Aden
                      - Asia/Almaty
                      - Asia/Amman
                      - Asia/Anadyr
                      - Asia/Aqtau
                      - Asia/Aqtobe
                      - Asia/Ashgabat
                      - Asia/Atyrau
                      - Asia/Baghdad
                      - Asia/Bahrain
                      - Asia/Baku
                      - Asia/Bangkok
                      - Asia/Barnaul
                      - Asia/Beirut
                      - Asia/Bishkek
                      - Asia/Brunei
                      - Asia/Chita
                      - Asia/Choibalsan
                      - Asia/Colombo
                      - Asia/Damascus
                      - Asia/Dhaka
                      - Asia/Dili
                      - Asia/Dubai
                      - Asia/Dushanbe
                      - Asia/Famagusta
                      - Asia/Gaza
                      - Asia/Hebron
                      - Asia/Ho_Chi_Minh
                      - Asia/Hong_Kong
                      - Asia/Hovd
                      - Asia/Irkutsk
                      - Asia/Jakarta
                      - Asia/Jayapura
                      - Asia/Jerusalem
                      - Asia/Kabul
                      - Asia/Kamchatka
                      - Asia/Karachi
                      - Asia/Kathmandu
                      - Asia/Khandyga
                      - Asia/Kolkata
                      - Asia/Krasnoyarsk
                      - Asia/Kuala_Lumpur
                      - Asia/Kuching
                      - Asia/Kuwait
                      - Asia/Macau
                      - Asia/Magadan
                      - Asia/Makassar
                      - Asia/Manila
                      - Asia/Muscat
                      - Asia/Nicosia
                      - Asia/Novokuznetsk
                      - Asia/Novosibirsk
                      - Asia/Omsk
                      - Asia/Oral
                      - Asia/Phnom_Penh
                      - Asia/Pontianak
                      - Asia/Pyongyang
                      - Asia/Qatar
                      - Asia/Qostanay
                      - Asia/Qyzylorda
                      - Asia/Riyadh
                      - Asia/Sakhalin
                      - Asia/Samarkand
                      - Asia/Seoul
                      - Asia/Shanghai
                      - Asia/Singapore
                      - Asia/Srednekolymsk
                      - Asia/Taipei
                      - Asia/Tashkent
                      - Asia/Tbilisi
                      - Asia/Tehran
                      - Asia/Thimphu
                      - Asia/Tokyo
                      - Asia/Tomsk
                      - Asia/Ulaanbaatar
                      - Asia/Urumqi
                      - Asia/Ust-Nera
                      - Asia/Vientiane
                      - Asia/Vladivostok
                      - Asia/Yakutsk
                      - Asia/Yangon
                      - Asia/Yekaterinburg
                      - Asia/Yerevan
                      - Atlantic/Azores
                      - Atlantic/Bermuda
                      - Atlantic/Canary
                      - Atlantic/Cape_Verde
                      - Atlantic/Faroe
                      - Atlantic/Madeira
                      - Atlantic/Reykjavik
                      - Atlantic/South_Georgia
                      - Atlantic/St_Helena
                      - Atlantic/Stanley
                      - Australia/Adelaide
                      - Australia/Brisbane
                      - Australia/Broken_Hill
                      - Australia/Darwin
                      - Australia/Eucla
                      - Australia/Hobart
                      - Australia/Lindeman
                      - Australia/Lord_Howe
                      - Australia/Melbourne
                      - Australia/Perth
                      - Australia/Sydney
                      - Europe/Amsterdam
                      - Europe/Andorra
                      - Europe/Astrakhan
                      - Europe/Athens
                      - Europe/Belgrade
                      - Europe/Berlin
                      - Europe/Bratislava
                      - Europe/Brussels
                      - Europe/Bucharest
                      - Europe/Budapest
                      - Europe/Busingen
                      - Europe/Chisinau
                      - Europe/Copenhagen
                      - Europe/Dublin
                      - Europe/Gibraltar
                      - Europe/Guernsey
                      - Europe/Helsinki
                      - Europe/Isle_of_Man
                      - Europe/Istanbul
                      - Europe/Jersey
                      - Europe/Kaliningrad
                      - Europe/Kirov
                      - Europe/Kyiv
                      - Europe/Lisbon
                      - Europe/Ljubljana
                      - Europe/London
                      - Europe/Luxembourg
                      - Europe/Madrid
                      - Europe/Malta
                      - Europe/Mariehamn
                      - Europe/Minsk
                      - Europe/Monaco
                      - Europe/Moscow
                      - Europe/Oslo
                      - Europe/Paris
                      - Europe/Podgorica
                      - Europe/Prague
                      - Europe/Riga
                      - Europe/Rome
                      - Europe/Samara
                      - Europe/San_Marino
                      - Europe/Sarajevo
                      - Europe/Saratov
                      - Europe/Simferopol
                      - Europe/Skopje
                      - Europe/Sofia
                      - Europe/Stockholm
                      - Europe/Tallinn
                      - Europe/Tirane
                      - Europe/Ulyanovsk
                      - Europe/Vaduz
                      - Europe/Vatican
                      - Europe/Vienna
                      - Europe/Vilnius
                      - Europe/Volgograd
                      - Europe/Warsaw
                      - Europe/Zagreb
                      - Europe/Zurich
                      - Indian/Antananarivo
                      - Indian/Chagos
                      - Indian/Christmas
                      - Indian/Cocos
                      - Indian/Comoro
                      - Indian/Kerguelen
                      - Indian/Mahe
                      - Indian/Maldives
                      - Indian/Mauritius
                      - Indian/Mayotte
                      - Indian/Reunion
                      - Pacific/Apia
                      - Pacific/Auckland
                      - Pacific/Bougainville
                      - Pacific/Chatham
                      - Pacific/Chuuk
                      - Pacific/Easter
                      - Pacific/Efate
                      - Pacific/Fakaofo
                      - Pacific/Fiji
                      - Pacific/Funafuti
                      - Pacific/Galapagos
                      - Pacific/Gambier
                      - Pacific/Guadalcanal
                      - Pacific/Guam
                      - Pacific/Honolulu
                      - Pacific/Kanton
                      - Pacific/Kiritimati
                      - Pacific/Kosrae
                      - Pacific/Kwajalein
                      - Pacific/Majuro
                      - Pacific/Marquesas
                      - Pacific/Midway
                      - Pacific/Nauru
                      - Pacific/Niue
                      - Pacific/Norfolk
                      - Pacific/Noumea
                      - Pacific/Pago_Pago
                      - Pacific/Palau
                      - Pacific/Pitcairn
                      - Pacific/Pohnpei
                      - Pacific/Port_Moresby
                      - Pacific/Rarotonga
                      - Pacific/Saipan
                      - Pacific/Tahiti
                      - Pacific/Tarawa
                      - Pacific/Tongatapu
                      - Pacific/Wake
                      - Pacific/Wallis
                      - UTC
              examples:
                Example 1:
                  value:
                    code: 200
                    message: Timezones were found
                    status: SUCCESS
                    data:
                      - Africa/Abidjan
                      - Africa/Accra
                      - Africa/Addis_Ababa
                      - Africa/Algiers
                      - Africa/Asmara
                      - Africa/Bamako
                      - Africa/Bangui
                      - Africa/Banjul
                      - Africa/Bissau
                      - Africa/Blantyre
                      - Africa/Brazzaville
                      - Africa/Bujumbura
                      - Africa/Cairo
                      - Africa/Casablanca
                      - Africa/Ceuta
                      - Africa/Conakry
                      - Africa/Dakar
                      - Africa/Dar_es_Salaam
                      - Africa/Djibouti
                      - Africa/Douala
                      - Africa/El_Aaiun
                      - Africa/Freetown
                      - Africa/Gaborone
                      - Africa/Harare
                      - Africa/Johannesburg
                      - Africa/Juba
                      - Africa/Kampala
                      - Africa/Khartoum
                      - Africa/Kigali
                      - Africa/Kinshasa
                      - Africa/Lagos
                      - Africa/Libreville
                      - Africa/Lome
                      - Africa/Luanda
                      - Africa/Lubumbashi
                      - Africa/Lusaka
                      - Africa/Malabo
                      - Africa/Maputo
                      - Africa/Maseru
                      - Africa/Mbabane
                      - Africa/Mogadishu
                      - Africa/Monrovia
                      - Africa/Nairobi
                      - Africa/Ndjamena
                      - Africa/Niamey
                      - Africa/Nouakchott
                      - Africa/Ouagadougou
                      - Africa/Porto-Novo
                      - Africa/Sao_Tome
                      - Africa/Tripoli
                      - Africa/Tunis
                      - Africa/Windhoek
                      - America/Adak
                      - America/Anchorage
                      - America/Anguilla
                      - America/Antigua
                      - America/Araguaina
                      - America/Argentina/Buenos_Aires
                      - America/Argentina/Catamarca
                      - America/Argentina/Cordoba
                      - America/Argentina/Jujuy
                      - America/Argentina/La_Rioja
                      - America/Argentina/Mendoza
                      - America/Argentina/Rio_Gallegos
                      - America/Argentina/Salta
                      - America/Argentina/San_Juan
                      - America/Argentina/San_Luis
                      - America/Argentina/Tucuman
                      - America/Argentina/Ushuaia
                      - America/Aruba
                      - America/Asuncion
                      - America/Atikokan
                      - America/Bahia
                      - America/Bahia_Banderas
                      - America/Barbados
                      - America/Belem
                      - America/Belize
                      - America/Blanc-Sablon
                      - America/Boa_Vista
                      - America/Bogota
                      - America/Boise
                      - America/Cambridge_Bay
                      - America/Campo_Grande
                      - America/Cancun
                      - America/Caracas
                      - America/Cayenne
                      - America/Cayman
                      - America/Chicago
                      - America/Chihuahua
                      - America/Ciudad_Juarez
                      - America/Costa_Rica
                      - America/Creston
                      - America/Cuiaba
                      - America/Curacao
                      - America/Danmarkshavn
                      - America/Dawson
                      - America/Dawson_Creek
                      - America/Denver
                      - America/Detroit
                      - America/Dominica
                      - America/Edmonton
                      - America/Eirunepe
                      - America/El_Salvador
                      - America/Fort_Nelson
                      - America/Fortaleza
                      - America/Glace_Bay
                      - America/Goose_Bay
                      - America/Grand_Turk
                      - America/Grenada
                      - America/Guadeloupe
                      - America/Guatemala
                      - America/Guayaquil
                      - America/Guyana
                      - America/Halifax
                      - America/Havana
                      - America/Hermosillo
                      - America/Indiana/Indianapolis
                      - America/Indiana/Knox
                      - America/Indiana/Marengo
                      - America/Indiana/Petersburg
                      - America/Indiana/Tell_City
                      - America/Indiana/Vevay
                      - America/Indiana/Vincennes
                      - America/Indiana/Winamac
                      - America/Inuvik
                      - America/Iqaluit
                      - America/Jamaica
                      - America/Juneau
                      - America/Kentucky/Louisville
                      - America/Kentucky/Monticello
                      - America/Kralendijk
                      - America/La_Paz
                      - America/Lima
                      - America/Los_Angeles
                      - America/Lower_Princes
                      - America/Maceio
                      - America/Managua
                      - America/Manaus
                      - America/Marigot
                      - America/Martinique
                      - America/Matamoros
                      - America/Mazatlan
                      - America/Menominee
                      - America/Merida
                      - America/Metlakatla
                      - America/Mexico_City
                      - America/Miquelon
                      - America/Moncton
                      - America/Monterrey
                      - America/Montevideo
                      - America/Montserrat
                      - America/Nassau
                      - America/New_York
                      - America/Nome
                      - America/Noronha
                      - America/North_Dakota/Beulah
                      - America/North_Dakota/Center
                      - America/North_Dakota/New_Salem
                      - America/Nuuk
                      - America/Ojinaga
                      - America/Panama
                      - America/Paramaribo
                      - America/Phoenix
                      - America/Port-au-Prince
                      - America/Port_of_Spain
                      - America/Porto_Velho
                      - America/Puerto_Rico
                      - America/Punta_Arenas
                      - America/Rankin_Inlet
                      - America/Recife
                      - America/Regina
                      - America/Resolute
                      - America/Rio_Branco
                      - America/Santarem
                      - America/Santiago
                      - America/Santo_Domingo
                      - America/Sao_Paulo
                      - America/Scoresbysund
                      - America/Sitka
                      - America/St_Barthelemy
                      - America/St_Johns
                      - America/St_Kitts
                      - America/St_Lucia
                      - America/St_Thomas
                      - America/St_Vincent
                      - America/Swift_Current
                      - America/Tegucigalpa
                      - America/Thule
                      - America/Tijuana
                      - America/Toronto
                      - America/Tortola
                      - America/Vancouver
                      - America/Whitehorse
                      - America/Winnipeg
                      - America/Yakutat
                      - Antarctica/Casey
                      - Antarctica/Davis
                      - Antarctica/DumontDUrville
                      - Antarctica/Macquarie
                      - Antarctica/Mawson
                      - Antarctica/McMurdo
                      - Antarctica/Palmer
                      - Antarctica/Rothera
                      - Antarctica/Syowa
                      - Antarctica/Troll
                      - Antarctica/Vostok
                      - Arctic/Longyearbyen
                      - Asia/Aden
                      - Asia/Almaty
                      - Asia/Amman
                      - Asia/Anadyr
                      - Asia/Aqtau
                      - Asia/Aqtobe
                      - Asia/Ashgabat
                      - Asia/Atyrau
                      - Asia/Baghdad
                      - Asia/Bahrain
                      - Asia/Baku
                      - Asia/Bangkok
                      - Asia/Barnaul
                      - Asia/Beirut
                      - Asia/Bishkek
                      - Asia/Brunei
                      - Asia/Chita
                      - Asia/Choibalsan
                      - Asia/Colombo
                      - Asia/Damascus
                      - Asia/Dhaka
                      - Asia/Dili
                      - Asia/Dubai
                      - Asia/Dushanbe
                      - Asia/Famagusta
                      - Asia/Gaza
                      - Asia/Hebron
                      - Asia/Ho_Chi_Minh
                      - Asia/Hong_Kong
                      - Asia/Hovd
                      - Asia/Irkutsk
                      - Asia/Jakarta
                      - Asia/Jayapura
                      - Asia/Jerusalem
                      - Asia/Kabul
                      - Asia/Kamchatka
                      - Asia/Karachi
                      - Asia/Kathmandu
                      - Asia/Khandyga
                      - Asia/Kolkata
                      - Asia/Krasnoyarsk
                      - Asia/Kuala_Lumpur
                      - Asia/Kuching
                      - Asia/Kuwait
                      - Asia/Macau
                      - Asia/Magadan
                      - Asia/Makassar
                      - Asia/Manila
                      - Asia/Muscat
                      - Asia/Nicosia
                      - Asia/Novokuznetsk
                      - Asia/Novosibirsk
                      - Asia/Omsk
                      - Asia/Oral
                      - Asia/Phnom_Penh
                      - Asia/Pontianak
                      - Asia/Pyongyang
                      - Asia/Qatar
                      - Asia/Qostanay
                      - Asia/Qyzylorda
                      - Asia/Riyadh
                      - Asia/Sakhalin
                      - Asia/Samarkand
                      - Asia/Seoul
                      - Asia/Shanghai
                      - Asia/Singapore
                      - Asia/Srednekolymsk
                      - Asia/Taipei
                      - Asia/Tashkent
                      - Asia/Tbilisi
                      - Asia/Tehran
                      - Asia/Thimphu
                      - Asia/Tokyo
                      - Asia/Tomsk
                      - Asia/Ulaanbaatar
                      - Asia/Urumqi
                      - Asia/Ust-Nera
                      - Asia/Vientiane
                      - Asia/Vladivostok
                      - Asia/Yakutsk
                      - Asia/Yangon
                      - Asia/Yekaterinburg
                      - Asia/Yerevan
                      - Atlantic/Azores
                      - Atlantic/Bermuda
                      - Atlantic/Canary
                      - Atlantic/Cape_Verde
                      - Atlantic/Faroe
                      - Atlantic/Madeira
                      - Atlantic/Reykjavik
                      - Atlantic/South_Georgia
                      - Atlantic/St_Helena
                      - Atlantic/Stanley
                      - Australia/Adelaide
                      - Australia/Brisbane
                      - Australia/Broken_Hill
                      - Australia/Darwin
                      - Australia/Eucla
                      - Australia/Hobart
                      - Australia/Lindeman
                      - Australia/Lord_Howe
                      - Australia/Melbourne
                      - Australia/Perth
                      - Australia/Sydney
                      - Europe/Amsterdam
                      - Europe/Andorra
                      - Europe/Astrakhan
                      - Europe/Athens
                      - Europe/Belgrade
                      - Europe/Berlin
                      - Europe/Bratislava
                      - Europe/Brussels
                      - Europe/Bucharest
                      - Europe/Budapest
                      - Europe/Busingen
                      - Europe/Chisinau
                      - Europe/Copenhagen
                      - Europe/Dublin
                      - Europe/Gibraltar
                      - Europe/Guernsey
                      - Europe/Helsinki
                      - Europe/Isle_of_Man
                      - Europe/Istanbul
                      - Europe/Jersey
                      - Europe/Kaliningrad
                      - Europe/Kirov
                      - Europe/Kyiv
                      - Europe/Lisbon
                      - Europe/Ljubljana
                      - Europe/London
                      - Europe/Luxembourg
                      - Europe/Madrid
                      - Europe/Malta
                      - Europe/Mariehamn
                      - Europe/Minsk
                      - Europe/Monaco
                      - Europe/Moscow
                      - Europe/Oslo
                      - Europe/Paris
                      - Europe/Podgorica
                      - Europe/Prague
                      - Europe/Riga
                      - Europe/Rome
                      - Europe/Samara
                      - Europe/San_Marino
                      - Europe/Sarajevo
                      - Europe/Saratov
                      - Europe/Simferopol
                      - Europe/Skopje
                      - Europe/Sofia
                      - Europe/Stockholm
                      - Europe/Tallinn
                      - Europe/Tirane
                      - Europe/Ulyanovsk
                      - Europe/Vaduz
                      - Europe/Vatican
                      - Europe/Vienna
                      - Europe/Vilnius
                      - Europe/Volgograd
                      - Europe/Warsaw
                      - Europe/Zagreb
                      - Europe/Zurich
                      - Indian/Antananarivo
                      - Indian/Chagos
                      - Indian/Christmas
                      - Indian/Cocos
                      - Indian/Comoro
                      - Indian/Kerguelen
                      - Indian/Mahe
                      - Indian/Maldives
                      - Indian/Mauritius
                      - Indian/Mayotte
                      - Indian/Reunion
                      - Pacific/Apia
                      - Pacific/Auckland
                      - Pacific/Bougainville
                      - Pacific/Chatham
                      - Pacific/Chuuk
                      - Pacific/Easter
                      - Pacific/Efate
                      - Pacific/Fakaofo
                      - Pacific/Fiji
                      - Pacific/Funafuti
                      - Pacific/Galapagos
                      - Pacific/Gambier
                      - Pacific/Guadalcanal
                      - Pacific/Guam
                      - Pacific/Honolulu
                      - Pacific/Kanton
                      - Pacific/Kiritimati
                      - Pacific/Kosrae
                      - Pacific/Kwajalein
                      - Pacific/Majuro
                      - Pacific/Marquesas
                      - Pacific/Midway
                      - Pacific/Nauru
                      - Pacific/Niue
                      - Pacific/Norfolk
                      - Pacific/Noumea
                      - Pacific/Pago_Pago
                      - Pacific/Palau
                      - Pacific/Pitcairn
                      - Pacific/Pohnpei
                      - Pacific/Port_Moresby
                      - Pacific/Rarotonga
                      - Pacific/Saipan
                      - Pacific/Tahiti
                      - Pacific/Tarawa
                      - Pacific/Tongatapu
                      - Pacific/Wake
                      - Pacific/Wallis
                      - UTC
          headers:
            Authorization:
              schema:
                type: string
              description: Bearer
      tags:
        - User API
      operationId: yunoUserTzones
      description: This will fetch list of timezones available in PHP DateTimeZone class
      x-stoplight:
        id: l7dnh43cuhosj
    parameters: []
    servers: []
  /user/currencies:
    get:
      summary: Currencies List
      parameters:
        - name: accept
          in: header
          description: Currencies List
          schema:
            type: string
          example: application/json
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    code: 200
                    message: Currencies were found
                    status: SUCCESS
                    data:
                      - code: INR
                        name: Indian Rupee
                        symbol: ₹
                        symbol_html: '&#8377;'
                      - code: USD
                        name: US Dollar
                        symbol: $
                        symbol_html: '&#36;'
                      - code: AED
                        name: UAE Dirham
                        symbol: د.إ
                        symbol_html: '&#x62f;&#x2e;&#x625;'
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  status:
                    type: string
                  data:
                    $ref: '#/components/schemas/Currency'
              examples:
                Example 2:
                  value:
                    code: 0
                    message: string
                    status: string
                    data:
                      code: INR
                      name: Indian Rupee
                      symbol: ₹
                      symbol_html: '&#8377;'
          headers:
            Authorization:
              schema:
                type: string
              description: Bearer
      tags:
        - User API
      operationId: yunoUserCurrcis
      description: This will fetch list of supported currencies.
      x-stoplight:
        id: tgn7cvld5qgro
  /create/class/{instructor_id}:
    parameters:
      - schema:
          type: integer
        name: instructor_id
        in: path
        required: true
        description: instructor id of class
    post:
      summary: Create Class
      description: Creates a new class by instructor with zoom i.e. class is equal to zoom meetings.
      responses:
        '200':
          description: The Class was scheduled successfully
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    code: 201
                    message: The Class was scheduled successfully
                    data:
                      Title: test class
                      StartDate: '2024-09-12'
                      StartTime: '10:49'
                      Category:
                        - null
                      ClassLink: 'https://www.yunolearning.com/live-class/test-class-99/'
                      ZoomLink: 'https://yunolearning.zoom.us/j/93788910668'
                      SuccessText: Scheduled
                      WpClassLink: 'https://www.yunolearning.com/live-class/test-class-99/'
                      ZoomClassLink: 'https://yunolearning.zoom.us/s/93788910668?zak=eyJ0eXAiOiJKV1QiLCJzdiI6IjAwMDAwMSIsInptX3NrbSI6InptX28ybSIsImFsZyI6IkhTMjU2In0.eyJpc3MiOiJ3ZWIiLCJjbHQiOjAsIm1udW0iOiI5Mzc4ODkxMDY2OCIsImF1ZCI6ImNsaWVudHNtIiwidWlkIjoiVkpRZW9nbVVTeGFmanBnSTQxdTJQZyIsInppZCI6ImIxZjk3Mjg2MTMzODRlZDViOTg4NGU1YTU3ZjM1MjI0Iiwic2siOiIwIiwic3R5IjoxLCJ3Y2QiOiJhdzEiLCJleHAiOjE3MjYxMjUyNzYsImlhdCI6MTcyNjExODA3NiwiYWlkIjoiMDRvUlB0azhUNjJfaTlzUFctTWtEUSIsImNpZCI6IiJ9.ZLYMzdnY2G6bFIIusFdQ60F7MOQDghlhIv7T2mCDelg'
                      StartMeridiem: am
                      guestURL: 'https://www.yunolearning.com/open/OTM3ODg5MTA2NjhAQHVuZGVmaW5lZEBAMTE2MTc1QEA='
                      classID: 116175
                      resource_id: 116175
                      zoom_meeting_id: 93788910668
                properties:
                  code:
                    type: integer
                    description: success response code
                  message:
                    type: string
                    description: success message line
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                        x-stoplight:
                          id: ny2em7jpsooh8
                      start_date:
                        type: string
                        x-stoplight:
                          id: r03370ze23owl
                        description: The date when the class starts
                      start_time:
                        type: string
                        x-stoplight:
                          id: f7krbtj14ulgz
                        description: The time when the class starts
                      category:
                        type: array
                        description: The category associated with the class
                        items:
                          type: string
                          x-stoplight:
                            id: fr2dda4ij9een
                      class_link:
                        type: string
                        x-stoplight:
                          id: 8viwhm6yfrrxn
                        description: Link to the class
                      zoom_link:
                        type: string
                        x-stoplight:
                          id: d1lszk93uc8r0
                        description: Link for the Zoom meeting
                      success_text:
                        type: string
                        x-stoplight:
                          id: y3aahniqnvq4w
                      wp_class_link:
                        type: string
                        x-stoplight:
                          id: mpda773z5dvj4
                      zoom_class_link:
                        type: string
                        x-stoplight:
                          id: chei093ryxwef
                        description: Link for the Zoom class
                      start_meridiem:
                        type: string
                        x-stoplight:
                          id: krdimb8of70as
                      guest_url:
                        type: string
                        x-stoplight:
                          id: l991ubpt2dl8l
                        description: URL for guest access
                      class_id:
                        type: integer
                        x-stoplight:
                          id: tex5il06n1erl
                        description: Unique identifier for the class
                      resource_id:
                        type: integer
                      zoom_meeting_id:
                        type: integer
              examples:
                Example 1:
                  value:
                    code: 201
                    message: Class scheduled successfully
                    data:
                      title: English Speaking Class
                      start_date: '2024-09-30'
                      start_time: '14:30'
                      category:
                        - string
                      class_link: 'https://example.com/class/12345'
                      zoom_link: 'https://zoom.us/j/987654321'
                      success_text: Scheduled
                      wp_class_link: 'https://example.com/class/12345'
                      zoom_class_link: 'https://zoom.us/j/987654321'
                      start_meridiem: PM
                      guest_url: 'https://example.com/open/encodedvalue'
                      class_id: 12345
                      resource_id: 12345
        '204':
          description: Can not able to schedule a class
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                x-examples:
                  Example 1:
                    code: 204
                    message: Can not able to schedule a class
                    data:
                      status: FAIL
              examples:
                Example 1:
                  value:
                    code: 204
                    message: Can not able to schedule a class
                    data:
                      status: FAIL
      operationId: post-create-class-instructor_id
      x-stoplight:
        id: 0557xcqz3cjdp
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  ClassTitle: test class
                  ClassDescription: test
                  ClassDate: 'Mon Jan 17 2023 14:40:00 GMT+0530 (India Standard Time)'
                  ClassTime: 'Mon Jan 17 2023 14:40:00 GMT+0530 (India Standard Time)'
                  ClassDuration: '15'
                  classSchedule: ''
                  RelatedCourses: ''
                  BatchID: ''
                  learner:
                    - id: 8875
                      name: yunofinaltest xor (<EMAIL>)
                      email: <EMAIL>
                      photo: 'https://lh3.googleusercontent.com/a/AEdFTp6VvmwLY8c5CGf8__pjtvm0Wftm9MphLrU0Pkew=s96-c'
              properties:
                class_title:
                  type: string
                  x-stoplight:
                    id: efo0q2ay05zma
                  description: Title of the class
                class_description:
                  type: string
                  x-stoplight:
                    id: 2i5s8fz093zxu
                  description: Brief description of the class
                excerpt:
                  type: string
                  x-stoplight:
                    id: s7hylrpoadsyv
                class_date_time:
                  type: string
                  x-stoplight:
                    id: 5vu547f893yyq
                  format: date-time
                meeting_type:
                  type: string
                  x-stoplight:
                    id: vt5ukojhcg035
                class_duration:
                  type: integer
                  x-stoplight:
                    id: dmbaxb5p4unkb
                  description: Duration of the class in minutes.
                related_courses:
                  type: array
                  x-stoplight:
                    id: d4y714ot5am5p
                  description: Related courses for the class
                  items:
                    x-stoplight:
                      id: ao6nuvyn0zxcv
                    type: integer
                batch_id:
                  type: integer
                  x-stoplight:
                    id: tzga4m25h8088
                  description: The batch ID associated with the class
                learner:
                  type: array
                  description: List of learners enrolled in the class
                  items:
                    type: integer
                academy_id:
                  type: integer
                  x-stoplight:
                    id: 9ztf261m4vt73
                  description: Unique identifier for the academy
            examples:
              Example 1:
                value:
                  class_title: English Speaking Class
                  excerpt: Improve your English speaking skills.
                  class_description: This class will help you improve your English speaking through practical exercises.
                  class_date_time: '2024-09-30T14:30:00Z'
                  class_duration: 60
                  meeting_type: private
                  learner:
                    - 4561
                  batch_id: 12345
                  related_courses:
                    - 234
                    - 345
                  academy_id: 26548
      tags:
        - Class
  /update/class/{instructor_id}:
    put:
      summary: Update Class
      tags:
        - Class
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    code: 201
                    message: The Class was updated successfully
                    data:
                      Title: test class zoom
                      StartDate: '2024-09-12'
                      StartTime: '11:31'
                      Category:
                        - general
                      ClassLink: 'https://www.yunolearning.com/live-class/test-class-zoom/'
                      ZoomLink: 'https://yunolearning.zoom.us/j/94328370513'
                      SuccessText: Updated
                      WpClassLink: 'https://www.yunolearning.com/live-class/test-class-zoom/'
                      ZoomClassLink: 'https://yunolearning.zoom.us/j/94328370513'
                      guestURL: 'https://www.yunolearning.com/open/OTQzMjgzNzA1MTNAQHVuZGVmaW5lZEBAMTE2MTc2QEA='
                      classID: '116176'
                      resource_id: '116176'
                      zoom_meeting_id: '94328370513'
                      StartMeridiem: am
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                        x-stoplight:
                          id: al7srvqc66g7d
                      start_date:
                        type: string
                        x-stoplight:
                          id: i8155oqhopy0l
                        description: The date when the class starts
                      start_time:
                        type: string
                        x-stoplight:
                          id: 7nrdlqiwc0vjm
                        description: The time when the class starts
                      category:
                        type: array
                        description: The category associated with the class
                        items:
                          type: string
                          x-stoplight:
                            id: mgikrs9x7dzhz
                      class_link:
                        type: string
                        x-stoplight:
                          id: l1n4mvq57sjm6
                        description: Link to the class
                      zoom_link:
                        type: string
                        x-stoplight:
                          id: hym01ana5o4g4
                        description: Link for the Zoom meeting
                      success_text:
                        type: string
                        x-stoplight:
                          id: xes1asnua4epw
                      wp_class_link:
                        type: string
                        x-stoplight:
                          id: 404hvw2oc64vz
                      zoom_class_link:
                        type: string
                        x-stoplight:
                          id: n36i1w1ry2asv
                        description: Link for the Zoom class
                      guest_url:
                        type: string
                        x-stoplight:
                          id: x53nikutff3km
                        description: URL for guest access
                      class_id:
                        type: integer
                        x-stoplight:
                          id: qu9f95vrvnobb
                      resource_id:
                        type: integer
                      zoom_meeting_id:
                        type: integer
                      start_meridiem:
                        type: string
                        x-stoplight:
                          id: tv643etq72isg
      operationId: put-update-class-instructor_id
      x-stoplight:
        id: umbmowlvpjslv
      description: updates the already created class by instructor with zoom i.e. class is equal to zoom meetings.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  ClassTitle: test class zoom
                  ClassDescription: ''
                  ClassDate: 'Thu Sep 12 2024 11:31:00 GMT+0530 (India Standard Time)'
                  ClassTime: 'Thu Sep 12 2024 11:31:00 GMT+0530 (India Standard Time)'
                  ClassDuration: 15
                  classSchedule: ''
                  learner:
                    - id: 14896
                      name: yunofinaltest xor (<EMAIL>)
                      email: <EMAIL>
                      photo: 'https://lh3.googleusercontent.com/a/ACg8ocJeLmML-bjN9zHhUBj115bRR_uczaUgQUmESlO0rZLYtklFjq0=s96-c'
                  RelatedCourses: ''
                  BatchID: ''
                  ClassId: '116176'
              properties:
                class_title:
                  type: string
                  x-stoplight:
                    id: mv99vocn9p7sq
                  description: Title of the class
                class_description:
                  type: string
                  x-stoplight:
                    id: qiyqud33dilhp
                  description: Brief description of the class
                excerpt:
                  type: string
                  x-stoplight:
                    id: n3g9fi0a0hon1
                class_date_time:
                  type: string
                  x-stoplight:
                    id: xrrzgyzh4vnpc
                  format: date-time
                class_duration:
                  type: integer
                  x-stoplight:
                    id: qursosm69gngt
                  description: Duration of the class in minutes.
                learner:
                  type: array
                  description: 'List of learners enrolled '
                  items:
                    type: integer
                batch_id:
                  type: integer
                  x-stoplight:
                    id: c90465hxrkd14
                  description: |
                    batch id to be edited
                class_id:
                  type: integer
                  x-stoplight:
                    id: 5agsn4w69x6rn
                academy_id:
                  type: integer
                  x-stoplight:
                    id: or3bafy96ywlg
    parameters:
      - schema:
          type: string
        name: instructor_id
        in: path
        required: true
        description: Creates a new class by instructor
  /user/state:
    put:
      summary: User Current State
      tags:
        - User State
      responses:
        '201':
          description: |-
            ```json
            {
              "code": 204,
              "message": "Details has been updated Successly.",
              "status": "SUCCESS"
            }
            ```
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    code: 204
                    message: Details has been updated Successly.
                    status: SUCCESS
                properties:
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: Details has been updated Successly.
                  status:
                    type: string
                    example: SUCCESS
        '204':
          $ref: '#/components/responses/Content-Not-Available-Response'
        '401':
          $ref: '#/components/responses/Authentication-Fail-Response'
      operationId: put-user-state
      x-stoplight:
        id: k4bdlylyhq724
      requestBody:
        description: |-
          ```json
          {
            "user_id": 8875,
            "category_slug": "ielts",
            "org_id": 18630
          }
          ```
        content:
          application/json:
            schema:
              type: object
              x-examples:
              properties:
                user_id:
                  type: integer
                  example: 8875
                  x-stoplight:
                    id: 6u6wes8r5tr85
                  description: unique number of user
                category_slug:
                  type: string
                  example: ielts
                  x-stoplight:
                    id: kw14abrarangg
                  description: unique string per category
                org_id:
                  type: integer
                  example: 18630
                  x-stoplight:
                    id: dj6nla5545d14
                  description: unique id of org
  /all/category/signup:
    get:
      tags:
        - Categories
      summary: Parent categories GET endpoint
      description: |
        The **List of Parent Categories API** is designed to fetch a list of parent categories for selection while making resources and classes. In other words, we can say that this GET api endpoint gives list of subjects.
      operationId: get-all-category-signup
      responses:
        '200':
          $ref: '#/components/responses/Parent-Categories'
        '204':
          $ref: '#/components/responses/Content-Not-Available-Response'
        '401':
          $ref: '#/components/responses/Authentication-Fail-Response'
      security:
        - Authorization: []
      servers:
        - url: 'https://dev.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Dev Server
        - url: 'https://stage.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Stage Server
        - url: 'https://www.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Prod Server
  /org/academies:
    post:
      tags:
        - Academies
      summary: Org Academies POST endpoint
      description: |
        The **List of Org Academies API** is designed to fetch a list of available Academies of an org. we can fetch all org's academies or set of org's academies.
      operationId: post-org-academies-org_id
      requestBody:
        description: |-
          ```json
          {
            "org_ids": [18630,23480],
            "limit":5,
            "offset":0,
            "category": 3060
          }
          ```
        content:
          application/json:
            schema:
              type: object
              properties:
                org_ids:
                  type: array
                  description: array of org ids. it may contain ids of org or a string for all academies
                  items:
                    type: integer
                limit:
                  type: integer
                  x-stoplight:
                    id: 7dtt5oundywhl
                  default: 5
                  example: 5
                  description: number of records
                offset:
                  type: integer
                  x-stoplight:
                    id: e0p3aljqbip91
                  example: 0
                  default: 0
                  description: starting index of records
                category:
                  type: integer
                  x-stoplight:
                    id: 89tt5oundywhl
                  example: 3060
                  default: 0
                  description: category of org
            examples:
              Example 1:
                value:
                  org_ids:
                    - 18630
                    - 23480
                  limit: 5
                  offset: 0
                  category: 3060
      responses:
        '200':
          $ref: '#/components/responses/Org-Academies'
        '204':
          $ref: '#/components/responses/Content-Not-Available-Response'
        '401':
          $ref: '#/components/responses/Authentication-Fail-Response'
      security:
        - Authorization: []
      servers:
        - url: 'https://dev.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Dev Server
        - url: 'https://stage.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Stage Server
        - url: 'https://www.yunolearning.com/wp-json/yuno/v3'
          description: Yuno Prod Server
  /class/titles/{instructor_id}:
    parameters:
      - schema:
          type: string
        name: instructor_id
        description: instructor id
        in: path
        required: true
    get:
      summary: Class titles GET endpoint
      tags:
        - Titles
      responses:
        '200':
          $ref: '#/components/responses/Class-Titles-Of-Instructor'
        '204':
          $ref: '#/components/responses/Content-Not-Available-Response'
        '401':
          $ref: '#/components/responses/Authentication-Fail-Response'
      operationId: get-class-titles-instructor_id
      x-stoplight:
        id: m2ymzajozw65o
      description: |
        The **List of Class Titles API** is designed to fetch a list of available class titles for auto suggestion while making classes. This GET response gives you list of class title of particular instructor.
      parameters: []
  /academy-subscription:
    post:
      description: It will give all subscription plans from MySQL, by default,it gives US plans.
      operationId: getAllYunoAcademySubscriptionPlan
      requestBody:
        content:
          application/json:
            schema:
              properties:
                country_code:
                  description: country code
                  enum:
                  - IN
                  - US
                  example: IN
                  type: string
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: It will give all subscription plans from MySQL, default US plans.
      tags:
      - Yuno Academy Subscription API
  /academy-subscription/{org_id}:
    delete:
      description: This endpoint will cancel the active subscription of org and it
        will be changed to a basic
      operationId: deleteOrgYunoAcademySubscriptionPlan
      parameters:
      - description: Enter org_id
        in: path
        name: org_id
        required: true
        schema:
          type: integer
      - description: Enter country_code
        in: path
        name: country_code
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will cancel the active subscription of org and it will
        be changed to a basic
      tags:
      - Yuno Academy Subscription API
    get:
      description: This endpoint will fetch active yuno academy subscription plan
        of org.
      operationId: getYunoAcademySubscriptionPlan
      parameters:
      - description: org id
        in: path
        name: org_id
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch active yuno academy subscription plan of org.
      tags:
      - Yuno Academy Subscription API
    post:
      description: This endpoint will assign basic free subscription.
      operationId: CrateOrgYunoAcademySubscriptionPlan
      parameters:
      - description: Organization ID
        in: path
        name: org_id
        required: true
        schema:
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                country_code:
                  description: country code
                  enum:
                  - IN
                  - US
                  example: IN
                  type: string
                org_id:
                  description: org id
                  example: 18630
                  type: integer
                plan_id:
                  description: plan id
                  enum:
                  - 1
                  - 2
                  - 3
                  example: 1
                  type: integer
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will assign basic free subscription.
      tags:
      - Yuno Academy Subscription API
    put:
      description: This endpoint will active the given subscription in payload to
        the org.
      operationId: UpdateOrgYunoAcademySubscriptionPlan
      parameters:
      - description: Organization ID
        in: path
        name: org_id
        required: true
        schema:
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                country_code:
                  description: country id
                  enum:
                  - IN
                  - US
                  example: IN
                  type: string
                org_id:
                  description: org id
                  example: 18630
                  type: integer
                plan_id:
                  description: plan id
                  enum:
                  - 1
                  - 2
                  - 3
                  example: 1
                  type: integer
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will active the given subscription in payload to the
        org.
      tags:
      - Yuno Academy Subscription API
  /category/practice:
    post:
      description: This end point gets practice tests data according to specific category
        from Elastic Search.
      operationId: categoryPracticeTestsDataV3
      requestBody:
        content:
          application/json:
            schema:
              properties:
                id:
                  description: Enter category id
                  example: 3060
                  type: integer
                limit:
                  description: Enter limit required
                  example: 20
                  required:
                  - limit
                  type: integer
                offset:
                  description: Enter offset required
                  example: 0
                  required:
                  - offset
                  type: integer
                sub_category:
                  example: []
                  items:
                    type: integer
                  type: array
                view:
                  description: Enter view like grid-view, list-view optional
                  example: list-view
                  type: string
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This end point gets practice tests data according to specific category
        from Elastic Search.
      tags:
      - Category Practice Tests Listing
  /org/academies/{org_id}:
    get:
      summary: Get Academies by Org ID
      tags:
        - Org Academies
      parameters:
        - name: org_id
          in: path
          required: true
          description: ID of the organization
          schema:
            type: integer
      responses:
        '200':
          description: Academies found
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: Academies were found
                  count:
                    type: integer
                    example: 10
                  status:
                    type: string
                    example: SUCCESS
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: integer
                          description: Academy ID
                          example: 28507
                        label:
                          type: string
                          description: Academy name
                          example: Test Academy
        '204':
          description: No Academies found
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 204
                  message:
                    type: string
                    example: We couldn't find any Academies
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                        example: FAIL
  /classes/{criteria}/{role_id}/{view}/{limit}/{offset}:
    post:
      description: This endpoint will fetch the class details for the keyword that
        will be searched.
      operationId: classV3ClassSearch
      parameters:
      - description: Enter view value like grid-view, list-view
        in: path
        name: view
        required: true
        schema:
          type: string
      - description: Enter role_id
        in: path
        name: role_id
        required: true
        schema:
          type: integer
      - description: Enter limit
        in: path
        name: limit
        required: true
        schema:
          type: integer
      - description: Enter offset
        in: path
        name: offset
        required: true
        schema:
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                academy:
                  default: []
                  description: Filters classes by the academy offering the class.
                  items:
                    type: integer
                  type: array
                attendance:
                  default: All
                  description: 'Filter for attendance records to be shown. Possible
                    values: "All", "0%", "1-30%", "31-50%", "51-70%", "71-90%", "91-100%".'
                  type: string
                batch_id:
                  default: null
                  description: Filters classes based on batch id.
                  type: integer
                category:
                  default: []
                  description: Filters classes by subject category.
                  items:
                    type: integer
                  type: array
                class_duration:
                  default: All
                  description: 'Filters classes by their duration. Possible values:
                    "30 min or less", "1 hr or less", "2 hr or less", "3 hr or less",
                    "4 hr or less", etc.'
                  type: string
                course:
                  default: null
                  description: Filters classes by course.
                  type: integer
                instructors:
                  default: []
                  description: Filters classes by instructor. The instructor search
                    suggestion API will provide a list of instructors to choose from.
                  items:
                    type: integer
                  type: array
                keyword:
                  default: All
                  description: Text input used to search for past classes.
                  type: string
                learner:
                  default: null
                  description: Filters classes by learners.
                  type: integer
                month:
                  default: Current month (e.g., "Aug 24" if the request is made in
                    August 2024)
                  description: 'Filters classes based on the month of the current
                    year. Possible values: "Jan 24", "Feb 24", "Mar 24", "Apr 24",
                    "May 24", "Jun 24", "Jul 24", etc.'
                  type: string
              type: object
        required: true
      responses:
        '200':
          description: Successful response
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch the class details for the keyword that will
        be searched.
      tags:
      - Upcoming and past Class Search API
  /classes/{view}/{role_id}/{instructor_id}/{type}/{course_id}/{attendance}/past/{limit}/{offset}/{grand_totals}: 
    get:
      description: This endpoint will fetch the more class detail for instructor with
        id.
      operationId: classV3AdminDetail
      parameters:
      - description: Enter view value like grid-view, list-view
        in: path
        name: view
        required: true
        schema:
          type: string
      - description: Enter role_id
        in: path
        name: role_id
        required: true
        schema:
          type: integer
      - description: Enter yuno instructor id
        in: path
        name: instructor_id
        required: true
        schema:
          type: integer
      - description: Enter course_id
        in: path
        name: course_id
        required: true
        schema:
          type: integer
      - description: Enter type
        in: path
        name: type
        required: true
        schema:
          type: string
      - description: Enter attendance
        in: path
        name: attendance
        required: true
        schema:
          type: integer
      - description: Enter limit
        in: path
        name: limit
        required: true
        schema:
          type: integer
      - description: Enter offset
        in: path
        name: offset
        required: true
        schema:
          type: integer
      - description: Enter grand total
        in: path
        name: grand_totals
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch the more class detail for instructor with
        id.
      tags:
      - Class API
  /course/detail/{course_id}/{user_id}:
    get:
      description: This endpoint will fetch course details, default value of user_id
        is 0
      operationId: getCourseDetail
      parameters:
      - description: Enter course_id required
        in: path
        name: course_id
        required: true
        schema:
          type: integer
      - description: Enter user_id required
        in: path
        name: user_id
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch course details, default value of user_id is
        0
      tags:
      - Course Detail API
  /org/academy/batch/{academy_id}:
    get:
      description: This endpoint will fetch batches of academy.
      operationId: getV3AcademyCoursesDetails
      parameters:
      - description: Enter academy_id required
        in: path
        name: academy_id
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch batches of academy.
      tags:
      - Org API
  /org/classes/{criteria}/{role_id}/{org_id}/{view}/{limit}/{offset}:
    post:
      description: This endpoint will fetch the class details for the keyword that
        will be searched for org-admin.
      operationId: classV3ClassSearchOrg
      parameters:
      - description: Enter view value like grid-view, list-view
        in: path
        name: view
        required: true
        schema:
          type: string
      - description: Enter role_id
        in: path
        name: role_id
        required: true
        schema:
          type: integer
      - description: Enter limit
        in: path
        name: limit
        required: true
        schema:
          type: integer
      - description: Enter offset
        in: path
        name: offset
        required: true
        schema:
          type: integer
      - description: Enter org_id
        in: path
        name: org_id
        required: true
        schema:
          type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                academy:
                  default: []
                  description: Filters classes by the academy offering the class.
                  items:
                    type: integer
                  type: array
                attendance:
                  default: All
                  description: 'Filter for attendance records to be shown. Possible
                    values: "All", "0%", "1-30%", "31-50%", "51-70%", "71-90%", "91-100%".'
                  type: string
                batch_id:
                  default: null
                  description: Filters classes based on batch id.
                  type: integer
                category:
                  default: []
                  description: Filters classes by subject category.
                  items:
                    type: integer
                  type: array
                class_duration:
                  default: All
                  description: 'Filters classes by their duration. Possible values:
                    "30 min or less", "1 hr or less", "2 hr or less", "3 hr or less",
                    "4 hr or less", etc.'
                  type: string
                course:
                  default: null
                  description: Filters classes by course.
                  type: integer
                instructors:
                  default: []
                  description: Filters classes by instructor. The instructor search
                    suggestion API will provide a list of instructors to choose from.
                  items:
                    type: integer
                  type: array
                keyword:
                  default: All
                  description: Text input used to search for past classes.
                  type: string
                learner:
                  default: null
                  description: Filters classes by learners.
                  type: integer
                month:
                  default: Current month (e.g., "Aug 24" if the request is made in
                    August 2024)
                  description: 'Filters classes based on the month of the current
                    year. Possible values: "Jan 24", "Feb 24", "Mar 24", "Apr 24",
                    "May 24", "Jun 24", "Jul 24", etc.'
                  type: string
              type: object
        required: true
      responses:
        '200':
          description: Successful response
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch the class details for the keyword that will
        be searched for org-admin.
      tags:
      - Upcoming and past Class Search API For Org
  /org/course/economics/edit:
    post:
      description: This endpoint used to edit and update the course economics by org
        admin
      operationId: courseEconomicsEdit
      requestBody:
        content:
          application/json:
            schema:
              properties:
                assignments_not_require:
                  properties:
                    time_investment:
                      example: ''
                      type: string
                    total:
                      example: 0
                      type: integer
                  type: object
                assignments_require:
                  properties:
                    effort:
                      example: ''
                      type: string
                    time_investment:
                      example: ''
                      type: string
                    total:
                      example: 0
                      type: integer
                  type: object
                course_id:
                  description: Enter course ID
                  example: 8736
                  type: integer
                diagnostic_test:
                  properties:
                    duration:
                      example: ''
                      type: string
                    effort:
                      example: ''
                      type: string
                    feedback:
                      example: false
                      type: boolean
                    is_active:
                      example: false
                      type: boolean
                  type: object
                group_classes_duration:
                  properties:
                    120_min:
                      example: 2
                      type: integer
                    150_min:
                      example: 0
                      type: integer
                    180_min:
                      example: 2
                      type: integer
                    30_min:
                      example: 0
                      type: integer
                    45_min:
                      example: 0
                      type: integer
                    60_min:
                      example: 0
                      type: integer
                    75_min:
                      example: 0
                      type: integer
                    90_min:
                      example: 0
                      type: integer
                  type: object
                id:
                  description: Enter category ID
                  example: 20510
                  type: integer
                mock_exam:
                  properties:
                    effort:
                      example: 30
                      type: integer
                    total:
                      example: 2
                      type: integer
                  type: object
                one_to_one_classes_duration:
                  properties:
                    120_min:
                      example: 0
                      type: integer
                    150_min:
                      example: 0
                      type: integer
                    180_min:
                      example: 0
                      type: integer
                    30_min:
                      example: 0
                      type: integer
                    45_min:
                      example: 0
                      type: integer
                    60_min:
                      example: 0
                      type: integer
                    75_min:
                      example: 0
                      type: integer
                    90_min:
                      example: 0
                      type: integer
                  type: object
                org_id:
                  description: Enter organization ID
                  example: '18630'
                  type: string
                personalization:
                  description: Enter personalization type
                  example: 1-Many
                  type: string
                post_test:
                  properties:
                    effort:
                      example: ''
                      type: string
                    feedback:
                      example: false
                      type: boolean
                    is_active:
                      example: false
                      type: boolean
                    time_investment:
                      example: ''
                      type: string
                  type: object
                user_id:
                  description: Enter user ID
                  example: '14241'
                  type: string
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint used to edit and update the course economics by org admin
      tags:
      - course economics edit
  /org/enrollments:
    post:
      description: This endpoint gives the enrollments of org.
      operationId: GetOrgAdminEnrollments
      requestBody:
        content:
          application/json:
            schema:
              properties:
                attendance:
                  description: Enter attendance
                  example: 100
                  required:
                  - attendance
                  type: integer
                counselor_id:
                  description: Enter counselor id
                  example: 0
                  required:
                  - counselor_id
                  type: integer
                course_id:
                  description: Enter course_id
                  example: 7138
                  required:
                  - course_id
                  type: integer
                grand_totals:
                  description: Enter grand total
                  example: 0
                  required:
                  - grand_totals
                  type: integer
                instructor_id:
                  description: Enter yuno instructor id
                  example: 0
                  required:
                  - instructor_id
                  type: integer
                learner_id:
                  description: Enter learner id
                  example: 0
                  required:
                  - learner_id
                  type: integer
                limit:
                  description: Enter limit
                  example: 20
                  required:
                  - limit
                  type: integer
                offset:
                  description: Enter offset
                  example: 0
                  required:
                  - offset
                  type: integer
                org_id:
                  description: Enter org id
                  example: 0
                  required:
                  - org_id
                  type: integer
                payment_status:
                  description: Enter payment status value like pending, full, all
                  enum:
                  - pending
                  - full
                  - all
                  example: all
                  required:
                  - payment_status
                  type: string
                referrals:
                  description: Enter view value like referrals, all
                  enum:
                  - referrals
                  - all
                  example: all
                  required:
                  - referrals
                  type: string
                role:
                  description: Enter role
                  example: org-admin
                  required:
                  - role
                  type: string
                role_id:
                  description: Enter role_id
                  example: 7937
                  required:
                  - role_id
                  type: integer
                status:
                  description: Enter enrollment status value like active, inactive,
                    all
                  enum:
                  - active
                  - inactive
                  - all
                  example: all
                  required:
                  - status
                  type: string
                type:
                  description: Enter type
                  enum:
                  - all
                  example: all
                  required:
                  - type
                  type: string
                view:
                  description: Enter view value like grid-view, list-view
                  example: grid-view
                  required:
                  - view
                  type: string
              type: object
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint gives the enrollments of org.
      tags:
      - Org Admin Enrollments API
  /user/info/{user_id}:
    get:
      description: This endpoint will fetch the user details for each role.
      operationId: infoV3StatList
      parameters:
      - description: Enter user id
        in: path
        name: user_id
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: This endpoint will fetch the user details for each role.
      tags:
      - User API
  /user/region/{userId}:
    get:
      summary: Get User Info-Region
      parameters:
        - name: accept
          in: header
          schema:
            type: string
          example: application/json
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Example 1: {}
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      yuno_user_info_country_code:
                        type: string
                      yuno_user_info_country:
                        type: string
                      yuno_user_info_timezone:
                        type: string
                      yuno_user_info_timezone_utc:
                        type: string
                      yuno_user_info_currency:
                        type: string
                      yuno_user_info_currency_symbol:
                        type: string
                      yuno_user_info_language:
                        type: string
                      yuno_user_info_language_code:
                        type: string
                x-examples:
                  Example 1:
                    code: 200
                    message: User Info were found
                    status: SUCCESS
                    data:
                      yuno_user_info_country_code: IN
                      yuno_user_info_country: India
                      yuno_user_info_timezone: Asia/Kolkota
                      yuno_user_info_timezone_utc: 'UTC+5:30'
                      yuno_user_info_currency: INR
                      yuno_user_info_currency_symbol: ₹
                      yuno_user_info_language: English
                      yuno_user_info_language_code: en
      operationId: yunoUserRegionFetch
      x-stoplight:
        id: g9li7pgurrxk3
      tags:
        - User API
      description: This will get the user region details.
    post:
      summary: Upd User Info-Region
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  yuno_user_info_country_code: IN
                  yuno_user_info_country: India
                  yuno_user_info_timezone: Asia/Kolkota
                  yuno_user_info_timezone_utc: 'UTC+5:30'
                  yuno_user_info_currency: INR
                  yuno_user_info_currency_symbol: ₹
                  yuno_user_info_language: English
                  yuno_user_info_language_code: en
              required:
                - yuno_user_info_country_code
                - yuno_user_info_country
                - yuno_user_info_timezone
                - yuno_user_info_timezone_utc
                - yuno_user_info_currency
                - yuno_user_info_currency_symbol
                - yuno_user_info_language
                - yuno_user_info_language_code
              properties:
                yuno_user_info_country_code:
                  type: string
                  description: Country Code of selected country like "IN"
                yuno_user_info_country:
                  type: string
                  description: Country Name like "India"
                yuno_user_info_timezone:
                  type: string
                  description: Timezone name like "Asia/Kolkota"
                yuno_user_info_timezone_utc:
                  type: string
                  description: 'Time zone on UTC format like "UTC+5:30"'
                yuno_user_info_currency:
                  type: string
                  description: 'Currency code like "INR","USD"'
                yuno_user_info_currency_symbol:
                  type: string
                  description: Currency Code like "$"
                yuno_user_info_language:
                  type: string
                  description: Language Name like "English"
                yuno_user_info_language_code:
                  type: string
                  description: Language code like "en"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  status:
                    type: string
                x-examples:
                  Example 1:
                    code: 201
                    message: User Info updated successfully
                    status: SUCCESS
      x-stoplight:
        id: grhi8zqgx26sq
      tags:
        - User API
      operationId: yunoUserRegionUpdate
      description: This will save the user region details.
    parameters:
      - schema:
          type: integer
        name: userId
        in: path
        required: true
      - schema:
          type: string
        in: header
        name: Authorization
        required: true
  /user/languages:
    get:
      summary: Get User Languages
      parameters:
        - name: accept
          in: header
          schema:
            type: string
          example: application/json
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      en:
                        type: object
                        properties:
                          name:
                            type: string
                          nativeName:
                            type: string
                x-examples:
                  Example 1:
                    code: 200
                    message: User Info were found
                    status: SUCCESS
                    data:
                      en:
                        name: English
                        nativeName: English
          headers:
            Authorization:
              schema:
                type: string
              description: Bearer
      tags:
        - User API
      operationId: yunoUserLangs
      description: This will fetch list of languages    
  /classes/details/{classID}/{role}/{userID}:
    get:
      description: This endpoint fetches class details for the specified classID, role, and userID.
      operationId: getClassDetails
      parameters:
      - description: Enter classID, required
        in: path
        name: classID
        required: true
        schema:
          type: integer
      - description: Enter role, required (e.g., learner, instructor)
        in: path
        name: role
        required: true
        schema:
          type: string
      - description: Enter userID, required
        in: path
        name: userID
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: ''
        '400':
          description: Bad Request
      security:
      - BearerAuth: []
      summary: Fetch class details based on classID, role, and userID
      tags:
      - Class Details API
  /available-courses:
    post:
      tags:
        - Courses
      summary: Get available courses based on filters
      description: |
        This API returns a list of available courses based on the filters provided in the request body, such as category, instructor, class time, personalization type, duration, and price per hour.
      operationId: getAvailableCourses
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  example: 20
                  description: Number of courses to return
                offset:
                  type: integer
                  example: 0
                  description: Pagination offset
                personalization:
                  type: string
                  example: all
                  description: 'all,one_to_one,one_to_many'
                category:
                  type: array
                  description: Array of category IDs
                  items:
                    type: integer
                    example: 3060
                category_level_1:
                  type: array
                  description: First-level subcategories
                  items:
                    type: integer
                  example: []
                category_level_2:
                  type: array
                  description: Second-level subcategories
                  items:
                    type: integer
                  example: []
                class_days_time:
                  type: array
                  description: Days and time slot filters
                  items:
                    type: object
                    properties:
                      selected:
                        type: array
                        items:
                          type: string
                        example: []
                      slug:
                        type: string
                        example: class_time
                instructor_id:
                  type: integer
                  example: 0
                  description: Instructor ID (0 for all instructors)
                price_per_hour:
                  type: integer
                  example: 10000
                  description: Maximum price per hour
                total_duration:
                  type: integer
                  example: 24
                  description: Desired course duration (in hours)
              required:
                - limit
                - offset
                - personalization
                - category
      responses:
        '200':
          description: Courses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "Course detail were found"
                  count:
                    type: integer
                    example: 8
                  status:
                    type: string
                    example: "SUCCESS"
                  data:
                    type: object
                    properties:
                      filters:
                        type: array
                        description: Filter metadata
                        items:
                          type: object
                      course_count:
                        type: integer
                        example: 8
                      instructors_count:
                        type: integer
                        example: 39
                      courses:
                        type: array
                        description: List of available courses
                        items:
                          type: object
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 400
                  message:
                    type: string
                    example: "Invalid input"
                  status:
                    type: string
                    example: "FAIL"
  /academy/demo-instructors/add:
    post:
      tags:
        - Demo Instructors
      summary: Add demo instructors
      description: This API allows adding demo instructors to an academy.
      operationId: addDemoInstructors
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                org_admin_id:
                  type: integer
                  example: 34
                  description: "ID of the organization admin"
                org_id:
                  type: integer
                  example: 18630
                  description: "ID of the organization (optional)"
                  nullable: true
                academy_id:
                  type: integer
                  example: 34
                  description: "ID of the academy"
                category:
                  type: array
                  description: "Category for which the instructors are added"
                  items:
                    type: object
                    properties:
                      slug:
                        type: string
                        example: ielts
                        description: "Slug of the category"
                    required:
                      - slug
                instructors:
                  type: array
                  description: "Array of instructor objects to be added"
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 15201
                        description: "Instructor ID"
                    required:
                      - id
              required:
                - org_admin_id
                - academy_id
                - category
                - instructors
      responses:
        '201':
          description: Demo instructors added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: "Demo Instructors added successfully"
                  status:
                    type: string
                    example: "SUCCESS"
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "FAIL"
                  message:
                    type: string
                    example: "You are not authorized to view this page"
  /academy/demo-instructors/edit:
    put:
      tags:
        - Demo Instructors
      summary: Edit demo instructors
      description: This API allows editing demo instructors for an academy.
      operationId: editDemoInstructors
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                org_admin_id:
                  type: integer
                  example: 34
                  description: "ID of the organization admin"
                org_id:
                  type: integer
                  example: 18630
                  description: "ID of the organization (optional)"
                  nullable: true
                academy_id:
                  type: integer
                  example: 34
                  description: "ID of the academy"
                category:
                  type: array
                  description: "Category for which the instructors are added"
                  items:
                    type: object
                    properties:
                      slug:
                        type: string
                        example: ielts
                        description: "Slug of the category"
                    required:
                      - slug
                instructors:
                  type: array
                  description: "Array of instructor objects to be updated"
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 15201
                        description: "Instructor ID"
                    required:
                      - id
              required:
                - org_admin_id
                - academy_id
                - category
                - instructors
      responses:
        '201':
          description: Demo instructors updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: "Demo Instructors updated successfully"
                  status:
                    type: string
                    example: "SUCCESS"
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                        example: "FAIL"
                  message:
                    type: string
                    example: "You are not authorized to view this page"
  /academy/demo-instructors/{org_admin_id}/{org_id}/{academy_id}:
    get:
      tags:
        - Demo Instructors
      summary: Get demo instructors
      description: This API retrieves the details of inserted or updated demo instructors for an academy.
      operationId: getDemoInstructors
      parameters:
        - name: org_admin_id
          in: path
          required: true
          schema:
            type: integer
          description: "ID of the organization admin"
        - name: org_id
          in: path
          required: true
          schema:
            type: integer
          description: "ID of the organization"
        - name: academy_id
          in: path
          required: true
          schema:
            type: integer
          description: "ID of the academy"
      responses:
        '200':
          description: Demo instructors details found
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "Demo instructors details found"
                  status:
                    type: string
                    example: "SUCCESS"
                  data:
                    type: object
                    properties:
                      org_admin_id:
                        type: integer
                        example: 34
                      org_id:
                        type: integer
                        example: 18630
                      academy_id:
                        type: integer
                        example: 34
                      category:
                        type: array
                        description: "Category for which the instructors are added"
                        items:
                          type: object
                          properties:
                            slug:
                              type: string
                              example: ielts
                              description: "Slug of the category"
                          required:
                            - slug
                      instructors:
                        type: array
                        description: "Array of instructor objects to be added"
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 15201
                              description: "Instructor ID"
                          required:
                            - id
        '204':
          description: Demo instructors not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 204
                  message:
                    type: string
                    example: "No demo instructors found"
                  status:
                    type: string
                    example: "FAIL"
  /payments:
    post:
      tags:
        - Payment Insights
      summary: POST - Payment Insights
      description: 'This API provides insights into payments made within the system, tailored to different user roles and view preferences. It allows the retrieval of payment records with options to filter, paginate, and display data in various formats such as list or grid view. The API is designed to support yunoadmin and org admin to access payment information relevant to their needs.'
      operationId: post-v3-payments-role-view-limit-offset
      requestBody:
        description: "\r\n"
        content:
          application/json:
            schema:
              type: object
              properties:
                role:
                  type: string
                  description: 'Specifies the role of the user accessing the API. Example: "yuno-admin" or "org-admin".'
                user_id:
                  type: integer
                  description: The unique identifier of the user making the request.
                org_id:
                  type: integer
                  description: The unique identifier of the organization associated with the user.
                academy_id:
                  type: array
                  description: 'An array of academy IDs to filter payments related to specific academies. Example: [1, 2]'
                  items:
                    type: integer
                view:
                  type: string
                  description: Specifies the type of view to display the payment data. Options are "grid-view" or "list-view
                limit:
                  type: integer
                  description: The maximum number of records to retrieve in the response. Default is 20.
                offset:
                  type: integer
                  description: The number of records to skip before starting to collect the result set. Default is 0.
                duration:
                  type: string
                  description: 'Filters payments based on the specified time range. Options include "Last Payment Days", "Last 90 days", "60 days", "30 days", or "7 days".'
              x-examples:
                Example 1:
                  role: yuno-admin
                  user_id: 123
                  org_id: 1
                  academy_id:
                    - 1
                    - 2
                  view: grid-view
                  limit: 20
                  offset: 0
                  duration: '90'
            examples:
              Example 1:
                value:
                  role: yuno-admin
                  user_id: 1
                  org_id: 1
                  academy_id:
                    - 1
                  view: list-view
                  limit: 20
                  offset: 0
                  duration: Last 90 days
      responses:
        '200':
          description: Success Response
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Response code indicating the result.
                  count:
                    type: integer
                    description: Count is the total count of results of payment transactions
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    description: Main data object containing the rows and columns.
                    properties:
                      rows:
                        type: array
                        items:
                          type: object
                          properties:
                            batch:
                              type: object
                              description: 'This is the batch associated with the associated enrollment. Batch ID, name, status (active or inactive)'
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the batch.
                                name:
                                  type: string
                                  description: Name of the batch.
                                status:
                                  type: string
                                  description: 'Status of the batch (e.g., active, inactive).'
                            course:
                              type: object
                              description: "Object containing information about the course associated with the enrollment. \t\r\n"
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the course.
                                title:
                                  type: string
                                  description: Title of the course.
                            category:
                              type: string
                              description: 'Category of the course (e.g., IELTS, PTE).'
                            counselor:
                              type: object
                              description: 'an object with counselor name, counselor ID and counselor image'
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the counselor.
                                name:
                                  type: string
                                  description: Name of the counselor.
                                image:
                                  type: string
                                  description: URL of the counselor's image.
                            learner:
                              type: object
                              description: 'an object with Learner id, name, image, email and phone'
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the learner.
                                name:
                                  type: string
                                  description: Name of the learner.
                                image:
                                  type: string
                                  description: URL of the learner's image.
                                email:
                                  type: string
                                  description: Email address of the learner.
                                phone:
                                  type: string
                                  description: Phone number of the learner.
                            enrollment:
                              type: object
                              description: Object containing details about the enrollment.
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the enrollment.
                                org_id:
                                  type: integer
                                  description: Organization ID associated with the enrollment.
                                status:
                                  type: string
                                  description: 'Status of the enrollment (e.g., active, inactive).'
                                enrollment_start_date:
                                  type: string
                                  description: Start date of the enrollment.
                                enrollment_end_date:
                                  type: string
                                  description: End date of the enrollment.
                                days_left:
                                  type: integer
                                  description: Number of days left until the enrollment ends.
                            instructor:
                              type: object
                              description: Object containing information about the instructor.
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the instructor.
                                name:
                                  type: string
                                  description: Name of the instructor.
                                image:
                                  type: string
                                  description: URL of the instructor's image.
                            payment:
                              type: object
                              description: "\t\r\nObject containing details about the payment."
                              properties:
                                payment_id:
                                  type: integer
                                  description: Unique identifier of the payment.
                                payment_date:
                                  type: string
                                  description: "\t\r\nDate of the payment."
                                transaction_id:
                                  type: integer
                                  description: Unique identifier of the transaction.
                                payment_mode:
                                  type: string
                                  description: 'Mode of payment (e.g., offline, online).'
                                pending_amount:
                                  type: number
                                  description: The remaining amount which is selling price - amount received
                                  format: float
                                amount_received:
                                  type: number
                                  description: 'This is the total amount received from the learner for this enrollment, in total i.e. the sum of all installments he’s paid so far'
                                  format: float
                                discount_amount:
                                  type: number
                                  description: The total discount applied to the enrollment
                                  format: float
                                payment_type:
                                  type: string
                                  description: 'Type of payment (e.g., ''Full Payment'', ''Installment'')'
                                payment_gateway:
                                  type: string
                                  description: 'Payment gateway used (e.g., RAZORPAY).'
                                currency:
                                  type: string
                                  description: 'Currency code for the payment (e.g., INR).'
                                gst:
                                  type: number
                                  description: GST amount applied to the payment.
                                  format: float
                                listed_price:
                                  type: number
                                  description: Listed price of the course without GST.
                                  format: float
                                selling_price:
                                  type: number
                                  description: 'It’s the price that’s set by the publisher in some cases (academy owner) of the course and in some yuno-admin '
                                  format: float
                                discount_percentage:
                                  type: number
                                  description: The total discount applied to the enrollment in percentage
                                  format: float
                                follow_date_installment_2:
                                  type: string
                                  format: datetime
                                  description: 'The follow-up date for the second installment of payment'             
                                follow_date_installment_3:
                                  type: string
                                  format: datetime
                                  description: 'The follow-up date for the third installment of payment'
                            org:
                              type: object
                              description: Object containing information about the organization.
                              properties:
                                id:
                                  type: integer
                                  description: Unique identifier of the organization.
                                name:
                                  type: string
                                  description: Name of the organization.
                                image:
                                  type: string
                                  description: URL of the organization's image.
                            personalization:
                              type: string
                              description: 'Personalization type (e.g., group, one-to-one).'
                      columns:
                        type: array
                        description: Array of column definitions for display
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                              description: 'Data type of the column value (e.g., string, integer).'
                            field:
                              type: string
                              description: Field name corresponding to the data row.
                            label:
                              type: string
                              description: Display label for the column.
                            sortable:
                              type: boolean
                              description: Indicates if the column is sortable.
                x-examples:
                  Example 1:
                    code: 0
                    count: 0
                    status: string
                    message: string
                    data:
                      rows:
                        - batch:
                            id: 0
                            name: string
                            status: string
                          course:
                            id: 0
                            title: string
                          category: string
                          counselor:
                            id: 0
                            name: string
                            image: string
                          learner:
                            id: 0
                            name: string
                            image: string
                            email: string
                            phone: string
                          enrollment:
                            id: string
                            org_id: 0
                            status: string
                            enrollment_start_date: string
                            enrollment_end_date: string
                            days_left: 0
                          instructor:
                            id: 0
                            name: string
                            image: string
                          payment:
                            payment_id: 0
                            payment_date: string
                            transaction_id: 0
                            payment_mode: online
                            pending_amount: 0
                            amount_received: 0
                            discount_amount: 0
                            payment_type: FULL PAYMENT
                            payment_gateway: RAZORPAY
                            currency: INR
                            gst: 0
                            listed_price: 0
                            selling_price: 0
                            discount_percentage: 1
                            follow_date_installment_2: "2023-10-01T10:00:00Z"
                            follow_date_installment_3: "2023-11-01T10:00:00Z"
                          org:
                            id: 0
                            name: string
                            image: string
                          personalization: string
                      columns:
                        - type: integer
                          field: batch.id
                          label: Batch ID
                          sortable: true
                        - type: string
                          field: batch.name
                          label: Batch Name
                          sortable: true
                        - type: string
                          field: batch.status
                          label: Batch Status
                          sortable: true
                        - type: integer
                          field: course.id
                          label: Course ID
                          sortable: true
                        - type: string
                          field: course.title
                          label: Course Title
                          sortable: true
                        - type: string
                          field: category
                          label: Category
                          sortable: true
                        - type: integer
                          field: counselor.id
                          label: Counselor ID
                          sortable: true
                        - type: string
                          field: counselor.name
                          label: Counselor Name
                          sortable: true
                        - type: string
                          field: counselor.image
                          label: Counselor Image
                          sortable: false
                        - type: integer
                          field: learner.id
                          label: Learner ID
                          sortable: true
                        - type: string
                          field: learner.name
                          label: Learner Name
                          sortable: true
                        - type: string
                          field: learner.image
                          label: Learner Image
                          sortable: false
                        - type: string
                          field: learner.email
                          label: Learner Email
                          sortable: true
                        - type: string
                          field: learner.phone
                          label: Learner Phone
                          sortable: true
                        - type: string
                          field: enrollment.id
                          label: Enrollment ID
                          sortable: true
                        - type: integer
                          field: enrollment.org_id
                          label: Organization ID
                          sortable: true
                        - type: string
                          field: enrollment.status
                          label: Enrollment Status
                          sortable: true
                        - type: string
                          field: enrollment.enrollment_start_date
                          label: Enrollment Start Date
                          sortable: true
                        - type: string
                          field: enrollment.enrollment_end_date
                          label: Enrollment End Date
                          sortable: true
                        - type: integer
                          field: enrollment.days_left
                          label: Days Left
                          sortable: true
                        - type: integer
                          field: instructor.id
                          label: Instructor ID
                          sortable: true
                        - type: string
                          field: instructor.name
                          label: Instructor Name
                          sortable: true
                        - type: string
                          field: instructor.image
                          label: Instructor Image
                          sortable: false
                        - type: integer
                          field: payment.payment_id
                          label: Payment ID
                          sortable: true
                        - type: string
                          field: payment.payment_date
                          label: Payment Date
                          sortable: true
                        - type: integer
                          field: payment.transaction_id
                          label: Transaction ID
                          sortable: true
                        - type: string
                          field: payment.payment_mode
                          label: Payment Mode
                          sortable: true
                        - type: float
                          field: payment.pending_amount
                          label: Pending Amount
                          sortable: true
                        - type: float
                          field: payment.amount_received
                          label: Amount Received
                          sortable: true
                        - type: float
                          field: payment.discount_amount
                          label: Discount Amount
                          sortable: true
                        - type: string
                          field: payment.payment_type
                          label: Payment Type
                          sortable: true
                        - type: string
                          field: payment.payment_gateway
                          label: Payment Gateway
                          sortable: true
                        - type: string
                          field: payment.currency
                          label: Currency
                          sortable: true
                        - type: float
                          field: payment.gst
                          label: GST
                          sortable: true
                        - type: float
                          field: payment.listed_price
                          label: Listed Price
                          sortable: true
                        - type: float
                          field: payment.selling_price
                          label: Selling Price
                          sortable: true
                        - type: float
                          field: payment.discount_percentage
                          label: Discount Percentage
                          sortable: true
                        - type: string
                          field: payment.follow_date_installment_2
                          label: Follow-up Date Installment 2
                          sortable: true
                        - type: string
                          field: payment.follow_date_installment_3
                          label: Follow-up Date Installment 3
                          sortable: true
                        - type: integer
                          field: org.id
                          label: Organization ID
                          sortable: true
                        - type: string
                          field: org.name
                          label: Organization Name
                          sortable: true
                        - type: string
                          field: org.image
                          label: Organization Image
                          sortable: false
                        - type: string
                          field: personalization
                          label: Personalization
                          sortable: true
              examples:
                Example 1:
                  value:
                    code: 0
                    count: 0
                    status: string
                    message: string
                    data:
                      rows:
                        - batch:
                            id: 0
                            name: string
                            status: string
                          course:
                            id: 0
                            title: string
                          category: string
                          counselor:
                            id: 0
                            name: string
                            image: string
                          learner:
                            id: 0
                            name: string
                            image: string
                            email: string
                            phone: string
                          enrollment:
                            id: string
                            org_id: 0
                            status: string
                            enrollment_start_date: string
                            enrollment_end_date: string
                            days_left: 0
                          instructor:
                            id: 0
                            name: string
                            image: string
                          payment:
                            payment_id: 0
                            payment_date: string
                            transaction_id: 0
                            payment_mode: string
                            pending_amount: 0
                            amount_received: 0
                            discount_amount: 0
                            payment_type: string
                            payment_gateway: string
                            currency: string
                            gst: 0
                            listed_price: -3.402823669209385e+38
                            selling_price: -3.402823669209385e+38
                            discount_percentage: -3.402823669209385e+38
                          org:
                            id: 0
                            name: string
                            image: string
                          personalization: string
                      columns:
                        - type: string
                          field: string
                          label: string
                          sortable: true
        '204':
          description: No Content
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                x-examples:
                  Example 1:
                    code: 204
                    message: We couldn't find the any payment transaction
                    data:
                      status: FAIL
              examples:
                Example 1:
                  value:
                    code: 204
                    message: We couldn't find the details of payment transaction
                    data:
                      status: FAIL
        '401':
          description: You are not authorized to view this page
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                x-examples:
                  Example 1:
                    code: 401
                    message: You are not authorized to view this page
                    data:
                      status: FAIL
              examples:
                Example 1:
                  value:
                    code: 401
                    message: You are not authorized to view this page
                    data:
                      status: FAIL
      security:
        - apiKey_1: []
      servers:
        - url: 'https://dev.yunolearning.com/'
          description: Development
  /org/course/{role_id}/{org_id}/{academy_id}/{course_id}/{category_id}/{instructor_format}/{view}/{limit}/{offset}:
    get:
      tags:
        - Org Courses
      parameters:
        - name: role_id
          in: path
          required: true
          description: ID of the role (e.g., learner, instructor)
          schema:
            type: integer
            example: 14438
        - name: org_id
          in: path
          required: true
          description: ID of the organization
          schema:
            type: string
            example: "29272"
        - name: academy_id
          in: path
          required: true
          description: ID of the academy within the organization
          schema:
            type: string
            example: "29273"
        - name: course_id
          in: path
          required: true
          description: ID of the course. Use 'all' to fetch all courses.
          schema:
            type: string
            example: "all"
        - name: category_id
          in: path
          required: true
          description: ID of the category. Use '0' to fetch uncategorized courses.
          schema:
            type: string
            example: "0"
        - name: instructor_format
          in: path
          required: true
          description: Filter by instructor format (e.g., 'all' or specific format).
          schema:
            type: string
            example: "all"
        - name: view
          in: path
          required: true
          description: Format of the view (e.g., 'grid-view', 'list-view').
          schema:
            type: string
            example: "grid-view"
        - name: limit
          in: path
          required: true
          description: Maximum number of results to return.
          schema:
            type: integer
            example: 20
        - name: offset
          in: path
          required: true
          description: Offset for pagination (start from this record).
          schema:
            type: integer
            example: 0
      responses:
      '200':
        description: Course data found
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: integer
                  example: 200
                message:
                  type: string
                  example: Courses were found
                status:
                  type: string
                  example: SUCCESS
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      course_id:
                        type: string
                        description: Course ID
                        example: "12345"
                      course_name:
                        type: string
                        description: Course name
                        example: "Advanced English Speaking"
                      academy_id:
                        type: string
                        description: Academy ID
                        example: "29273"
                      category_id:
                        type: string
                        description: Category ID
                        example: "0"
                      instructor_format:
                        type: string
                        description: Instructor format
                        example: "online"
                      view:
                        type: string
                        description: View type
                        example: "grid-view"
                      limit:
                        type: integer
                        description: Maximum number of courses returned
                        example: 20
                      offset:
                        type: integer
                        description: Offset for pagination
                        example: 0
                      total_courses:
                        type: integer
                        description: Total number of available courses
                        example: 150
      '404':
        description: Courses not found
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: integer
                  example: 404
                message:
                  type: string
                  example: No courses found for the given criteria
                status:
                  type: string
                  example: ERROR
      '400':
        description: Invalid parameters
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: integer
                  example: 400
                message:
                  type: string
                  example: Bad request. Invalid parameters provided.
                status:
                  type: string
                  example: ERROR
      security:
        - apiKey_1: []
      servers:
        - url: 'https://dev.yunolearning.com/'
          description: Development
tags:
  - name: Academies
    description: Operations related to class titles
  - name: Categories
    description: Operations related to parent categories
  - name: Titles
    description: Operations related to class titles
  - name: User State
    description: Operations related to User State
components:
  responses:
    Parent-Categories:
      description: |-
        ```json
        {
          "code": 200,
          "message": "Categories were found",
          "status": "SUCCESS",
          "data": [
            {
              "id": 3964,
              "label": "Anil Lamba on Finance",
              "slug": "anil-lamba-on-finance",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2020/11/Finance-course1.jpg"
            },
            {
              "id": 3089,
              "label": "Coding for kids",
              "slug": "coding-for-kids",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2020/10/Codingforkids.jpg"
            },
            {
              "id": 5845,
              "label": "Data Science and Analytics",
              "slug": "data-science-and-analytics",
              "image": null
            },
            {
              "id": 5426,
              "label": "Duolingo",
              "slug": "duolingo",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2023/11/duolingo-1-768x432.png"
            },
            {
              "id": 3084,
              "label": "English Speaking",
              "slug": "english-speaking",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2020/10/EnglishSpeaking.jpg"
            },
            {
              "id": 5590,
              "label": "French",
              "slug": "french",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2024/06/French-name-image-768x432.png"
            },
            {
              "id": 3060,
              "label": "IELTS",
              "slug": "ielts",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg"
            },
            {
              "id": 4324,
              "label": "Microsoft Excel",
              "slug": "microsoft-excel",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2021/10/how long should a cover letter be-768x591.jpg"
            },
            {
              "id": 4722,
              "label": "PTE",
              "slug": "pte",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2022/09/3_PTE-768x511.jpg"
            },
            {
              "id": 4328,
              "label": "Python Programming",
              "slug": "python-programming",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2021/10/10-Benefits-of-Learning-Python-Programming-Language-and-Its-Use-Cases.png"
            },
            {
              "id": 5227,
              "label": "TOEFL",
              "slug": "toefl",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2023/05/7_TOEFL name Page-768x512.jpeg"
            },
            {
              "id": 4117,
              "label": "Vedic Maths",
              "slug": "vedic-maths",
              "image": "https://dev.yunolearning.com/wp-content/uploads/2021/04/EnglishSpeaking.jpg"
            }
          ]
        }
        ```
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: response code
                examples:
                  - 200
              message:
                type: string
                description: response message
                examples:
                  - Categories were found
              status:
                type: string
                description: response status
                examples:
                  - SUCCESS
              data:
                type: array
                description: response data object
                items:
                  $ref: '#/components/schemas/Parent-Categories'
            x-examples:
              Example 1:
                code: 200
                message: Class Titles were found
                status: SUCCESS
                data: []
          examples:
            Example 1:
              value:
                code: 200
                message: Categories were found
                status: SUCCESS
                data:
                  - id: 3964
                    label: Anil Lamba on Finance
                    slug: anil-lamba-on-finance
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2020/11/Finance-course1.jpg'
                  - id: 3089
                    label: Coding for kids
                    slug: coding-for-kids
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2020/10/Codingforkids.jpg'
                  - id: 5845
                    label: Data Science and Analytics
                    slug: data-science-and-analytics
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2020/10/Codingforkids.jpg'
                  - id: 5426
                    label: Duolingo
                    slug: duolingo
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2023/11/duolingo-1-768x432.png'
                  - id: 3084
                    label: English Speaking
                    slug: english-speaking
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2020/10/EnglishSpeaking.jpg'
                  - id: 5590
                    label: French
                    slug: french
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2024/06/French-name-image-768x432.png'
                  - id: 3060
                    label: IELTS
                    slug: ielts
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg'
                  - id: 4324
                    label: Microsoft Excel
                    slug: microsoft-excel
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2021/10/how long should a cover letter be-768x591.jpg'
                  - id: 4722
                    label: PTE
                    slug: pte
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2022/09/3_PTE-768x511.jpg'
                  - id: 4328
                    label: Python Programming
                    slug: python-programming
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2021/10/10-Benefits-of-Learning-Python-Programming-Language-and-Its-Use-Cases.png'
                  - id: 5227
                    label: TOEFL
                    slug: toefl
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2023/05/7_TOEFL name Page-768x512.jpeg'
                  - id: 4117
                    label: Vedic Maths
                    slug: vedic-maths
                    image: 'https://dev.yunolearning.com/wp-content/uploads/2021/04/EnglishSpeaking.jpg'
    Org-Academies:
      description: |-
        ```json
        {
            "code": 200,
            "message": "Academies were found",
            "status": "SUCCESS",
            "count": 10,
            "data": [
                {
                    "id": 29874,
                    "name": "shavys english hut",
                    "logo_url": "https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png",
                    "fav_icon_url": "https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png",
                    "excerpt": "Shavy’s English Hut is a leading institute specializing in activity-based learning methods for English language development. We offer engaging, interactive classes tailored for both young children and adults, providing a fresh and fun approach to mastering English.",
                    "description": "Founded in 2016 by Shavy Jain, a CELTA-certified trainer and content creator, Shavy’s English Hut is known for its innovative, activity-based learning methods, combining storytelling, puppetry, and interactive techniques to create memorable learning experiences for all ages. We are a certified Jolly Phonics and Jolly Grammar Institute, catering to students across India through our online platform. Beyond conventional learning, we focus on helping professionals and businesses excel in communication skills through engaging corporate training sessions. Whether it's delivering impactful pitches, crafting effective business emails, or improving meeting communication, our goal is to enhance confidence and clarity in English communication.",
                    "courses_count": 2,
                    "is_active": false,// to use on frontend
                    "mapped_instructors_count": 4,
                    "active_learners_count": 5,
                    "past_learners_count": 10            
                },
                {
                  "id": 30269,
                  "name": "shavys english hut",
                  "logo_url": "https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png",
                  "fav_icon_url": "https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png",
                  "excerpt": "Shavy’s English Hut is a leading institute specializing in activity-based learning methods for English language development. We offer engaging, interactive classes tailored for both young children and adults, providing a fresh and fun approach to mastering English.",
                  "description": "Founded in 2016 by Shavy Jain, a CELTA-certified trainer and content creator, Shavy’s English Hut is known for its innovative, activity-based learning methods, combining storytelling, puppetry, and interactive techniques to create memorable learning experiences for all ages. We are a certified Jolly Phonics and Jolly Grammar Institute, catering to students across India through our online platform. Beyond conventional learning, we focus on helping professionals and businesses excel in communication skills through engaging corporate training sessions. Whether it's delivering impactful pitches, crafting effective business emails, or improving meeting communication, our goal is to enhance confidence and clarity in English communication.",
                  "is_active": false,// to use on frontend
                  "courses_count": 2,
                  "mapped_instructors_count": 4,
                  "active_learners_count": 5,
                  "past_learners_count": 10     
                }
            ]
        }
        ```
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: response code
                examples:
                  - 200
              message:
                type: string
                description: response message
                examples:
                  - Academies were found
              status:
                type: string
                description: response status
                examples:
                  - SUCCESS
              count:
                type: integer
                description: response count of results
                examples:
                  - 10
              data:
                type: array
                description: response data object
                items:
                  $ref: '#/components/schemas/Org-Academies'
            x-examples:
              Example 1:
                code: 200
                message: Academies were found
                status: SUCCESS
    Class-Titles-Of-Instructor:
      description: |-
        ```json
        {
            "code": 200,
            "message": "Class Titles were found",
            "status": "SUCCESS",
            "data": [
                {
                    "title": "Analysis of your answers",
                    "description": "Your answers to the mock exam will be analyzed and you will be provided with feedback. We will disuss the areas you need to improve so that your overall score improves."
                },
                {
                    "title": "Multiple Choice Questions",
                    "description": "Multiple choice questions in the Listening test are the most difficult for many IELTS test takers. This class will help you to understand the nuances of such questions."
                }
            ]
        }
        ```
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                code: 200
                message: Class Titles were found
                status: SUCCESS
                data:
                  - title: Analysis of your answers
                    description: Your answers to the mock exam will be analyzed and you will be provided with feedback. We will disuss the areas you need to improve so that your overall score improves.
                  - title: Multiple Choice Questions
                    description: Multiple choice questions in the Listening test are the most difficult for many IELTS test takers. This class will help you to understand the nuances of such questions.
            properties:
              code:
                type: number
                example: 200
              message:
                type: string
                example: Class Titles were found
              status:
                type: string
                example: SUCCESS
              data:
                type: array
                items:
                  type: object
                  $ref: '#/components/schemas/Class-titles'
          examples:
            Example 1:
              value:
                code: 200
                message: Class Titles were found
                status: SUCCESS
                data:
                  - title: Analysis of your answers
                    description: Your answers to the mock exam will be analyzed and you will be provided with feedback. We will disuss the areas you need to improve so that your overall score improves.
                  - title: Multiple Choice Questions
                    description: Multiple choice questions in the Listening test are the most difficult for many IELTS test takers. This class will help you to understand the nuances of such questions.
    Authentication-Fail-Response:
      description: |-
        ```json
        {
          "code": 401,
          "message": "Sorry, you are not allowed to do that.",
          "status": "rest_forbidden"
        }
        ```
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                description: response code
                examples:
                  - 401
              message:
                type: string
                description: reponse message
                examples:
                  - 'Sorry, you are not allowed to do that.'
              status:
                type: string
                description: response status
                examples:
                  - rest_forbidden
            x-examples:
              Example 1:
                code: rest_forbidden
                message: 'Sorry, you are not allowed to do that.'
                data:
                  status: 401
          examples:
            Example 1:
              value:
                code: 401
                message: 'Sorry, you are not allowed to do that.'
                status: rest_forbidden
    Content-Not-Available-Response:
      description: |-
        ```json
        {
          "code": 204,
          "message": "Sorry, we couldn't find any course that match your search",
          "status": "FAIL"
        }
        ```
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                description: response code
                examples:
                  - 204
              message:
                type: string
                description: response message
                examples:
                  - 'Sorry, we couldn''t find any course that match your search'
              status:
                type: string
                description: reponse status
                examples:
                  - FAIL
            x-examples:
              Example 1:
                code: 204
                message: 'Sorry, we couldn''t find any course that match your search'
                data:
                  status: FAIL
          examples:
            Example 1:
              value:
                code: 204
                message: 'Sorry, we couldn''t find any course that match your search'
                status: FAIL
  requestBodies: {}
  schemas:
    Class-titles:
      type: object
      x-examples:
        Example 1:
          title: Analysis of your answers
          description: Your answers to the mock exam will be analyzed and you will be provided with feedback. We will disuss the areas you need to improve so that your overall score improves.
      properties:
        title:
          type: string
          example: Analysis of your answers
        description:
          type: string
          example: Your answers to the mock exam will be analyzed and you will be provided with feedback. We will disuss the areas you need to improve so that your overall score improves.
      description: Class titles object
      title: Class Titles
    Parent-Categories:
      type: object
      title: Parent-Categories
      description: parent-categories
      properties:
        id:
          type: integer
          description: unique number of category
          examples:
            - 3060
        label:
          type: string
          description: name of category to display for selection
          examples:
            - IELTS
        slug:
          type: string
          description: unique string of category
          examples:
            - ielts
        image:
          type: string
          description: image url of category
          examples:
            - 'https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg'
      required:
        - id
        - label
        - slug
    Org-Academies:
      type: object
      title: Org academies
      description: academies of org
      properties:
        id:
          type: integer
          description: unique id of academy
          examples:
            - 29874
        name:
          type: string
          description: name of academy
          examples:
            - shavys english hut
        logo_url:
          type: string
          description: logo url of academy
          format: uri
          examples:
            - 'https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png'
        fav_icon_url:
          type: string
          description: fav icon url of academy
          format: uri
          examples:
            - 'https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png'
        excerpt:
          type: string
          description: excerpt of academy
          examples:
            - 'Shavy’s English Hut is a leading institute specializing in activity-based learning methods for English language development. We offer engaging, interactive classes tailored for both young children and adults, providing a fresh and fun approach to mastering English.'
        description:
          type: string
          description: description of academy
          examples:
            - 'Founded in 2016 by Shavy Jain, a CELTA-certified trainer and content creator, Shavy’s English Hut is known for its innovative, activity-based learning methods, combining storytelling, puppetry, and interactive techniques to create memorable learning experiences for all ages. We are a certified Jolly Phonics and Jolly Grammar Institute, catering to students across India through our online platform. Beyond conventional learning, we focus on helping professionals and businesses excel in communication skills through engaging corporate training sessions. Whether it''s delivering impactful pitches, crafting effective business emails, or improving meeting communication, our goal is to enhance confidence and clarity in English communication.'
        courses_count:
          type: integer
          description: number of courses
          examples:
            - 2
        mapped_instructors_count:
          type: integer
          description: number of mapped instructors
          examples:
            - 4
        active_learners_count:
          type: integer
          description: number of active learners
          examples:
            - 5
        past_learners_count:
          type: integer
          description: number of past learners
          examples:
            - 10
      required:
        - id
        - excerpt
      x-examples:
        Example 1:
          id: 29874
          name: shavys english hut
          logo_url: 'https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png'
          fav_icon_url: 'https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png'
          excerpt: 'Shavy’s English Hut is a leading institute specializing in activity-based learning methods for English language development. We offer engaging, interactive classes tailored for both young children and adults, providing a fresh and fun approach to mastering English.'
          description: 'Founded in 2016 by Shavy Jain, a CELTA-certified trainer and content creator, Shavy’s English Hut is known for its innovative, activity-based learning methods, combining storytelling, puppetry, and interactive techniques to create memorable learning experiences for all ages. We are a certified Jolly Phonics and Jolly Grammar Institute, catering to students across India through our online platform. Beyond conventional learning, we focus on helping professionals and businesses excel in communication skills through engaging corporate training sessions. Whether it''s delivering impactful pitches, crafting effective business emails, or improving meeting communication, our goal is to enhance confidence and clarity in English communication.'
          is_active: false // to use on frontend
          courses_count: 2
          mapped_instructors_count: 4
          active_learners_count: 5
          past_learners_count: 10  
  securitySchemes:
    Authorization:
      bearerFormat: JWT
      scheme: bearer
      type: http
security:
  - Authorization: []