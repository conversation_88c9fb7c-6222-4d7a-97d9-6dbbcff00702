const gulp = require('gulp'),
	gp_concat = require('gulp-concat'),
 	gp_rename = require('gulp-rename'),
    terser = require('terser');
	gulpTerser = require('gulp-terser'),
	uglifycss = require('gulp-uglifycss');

const folder = "batchPageV2";

gulp.task(''+ folder +'JS', function () {
    const fileName = folder,
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            // ''+ path +'/assets/js/lib/checkout.js',
            ''+ path +'/components/orgTheme/*.js',
            ''+ path +'/components/poweredBy/*.js',
            ''+ path +'/pages/'+ fileName +'/*.js'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.js`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}Combined.js`))
    .pipe(gulp.dest(saveToFolder))
    .pipe(gp_rename(renameFile))
    .pipe(gulpTerser({}, terser.minify))
    .pipe(gulp.dest(saveToFolder));
});

gulp.task(''+ folder +'CSS', function () {
    const fileName = folder,
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/pages/'+ fileName +'/*.css',
            ''+ path +'/components/poweredBy/*.css',
            ''+ path +'/components/orgTheme/*.css'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.css`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}.min.css`))
    .pipe(uglifycss())
    .pipe(gulp.dest(saveToFolder))
});

gulp.task(folder, function() {
    const pages = "wp-content/themes/yunolearning-child/pages/",
        components = "wp-content/themes/yunolearning-child/components/";

    gulp.watch([
        ''+ components +'orgTheme/*.js',
        ''+ components +'poweredBy/*.js',
        ''+ pages + folder +'/*.js'
    ], gulp.series(''+ folder +'JS'));

    gulp.watch([
        ''+ components +'orgTheme/*.css',
        ''+ components +'poweredBy/*.css',
        ''+ pages + folder +'/*.css'
    ], gulp.series(''+ folder +'CSS'));
});
