<?php

namespace CPT;

use Google\Service\StreetViewPublish\Place;

class PlaceCPT
{
    public function __construct()
    {
        add_action('init', array($this, 'registerPlaceCPT'));
        add_action('init', array($this, 'addPlaceAcfFields'));
    }

    /**
     * To create custom post type - Place
     * */
    public function registerPlaceCPT()
    {
        // Set UI labels for Custom Post Type
        $labels = array(
            'name'                  => _x('Place', 'Post Type General Name', 'yunolearning'),
            'singular_name'         => _x('Place', 'Post Type Singular Name', 'yunolearning'),
            'menu_name'             => __('Place', 'yunolearning'),
            'parent_item_colon'     => __('Parent Place', 'yunolearning'),
            'all_items'             => __('All Place', 'yunolearning'),
            'view_item'             => __('View Place', 'yunolearning'),
            'add_new_item'          => __('Add New Place', 'yunolearning'),
            'add_new'               => __('Add New', 'yunolearning'),
            'edit_item'             => __('Edit Place', 'yunolearning'),
            'update_item'           => __('Update Place', 'yunolearning'),
            'search_items'          => __('Search Place', 'yunolearning'),
            'not_found'             => __('Not Found', 'yunolearning'),
            'not_found_in_trash'    => __('Not found in Trash', 'yunolearning'),
        );
    
        // Updated arguments to hide the CPT from admin and front end
        $args = array(
            'label'               => __('Place', 'yunolearning'),
            'description'         => __('Place Detail', 'yunolearning'),
            'labels'              => $labels,
            'supports'            => array('title', 'editor', 'excerpt', 'thumbnail', 'custom-fields'),
            'hierarchical'        => false,
            'public'              => false, // not publicly accessible
            'show_ui'             => false, // hide from admin UI
            'show_in_menu'        => false, // don't show in admin menu
            'show_in_nav_menus'   => false,
            'show_in_admin_bar'   => false,
            'exclude_from_search' => true,
            'publicly_queryable'  => false, // not queryable on the front end
            'capability_type'     => 'post',
            'show_in_rest'        => false, // hide from REST API
            'query_var'           => false,
            'rewrite'             => false,
        );
        /*
        //For Testing in WP-Admin
        $args['public']  = true;
        $args['show_ui'] = true; // hide from admin UI
        $args['show_in_menu'] = true; // don't show in admin menu
        $args['show_in_nav_menus'] = true;
        $args['show_in_admin_bar'] = true;
        */
        // Registering your Custom Post Type
        register_post_type('place', $args);
    }

    public function addPlaceAcfFields()
    {
        if (function_exists('acf_add_local_field_group')) {

            acf_add_local_field_group(array(
                'key' => 'group_place_fields',
                'title' => 'Place Fields',
                'fields' => array(
                    array(
                        'key' => 'field_place_org_id',
                        'label' => 'Org ID',
                        'name' => 'org_id',
                        'type' => 'number'
                    ),
                    array(
                        'key' => 'field_place_type',
                        'label' => 'Type',
                        'name' => 'type',
                        'type' => 'select',
                        'choices' => array(
                            'SCHOOL' => 'SCHOOL',
                            'COLLEGE' => 'COLLEGE',
                            'COMMERCIAL_BUILDING' => 'COMMERCIAL BUILDING',
                        ),
                        'ui' => 1,
                        'instructions' => 'The type of place'
                    ),
                    array(
                        'key' => 'field_place_address',
                        'label' => 'Address',
                        'name' => 'address',
                        'type' => 'group',
                        'instructions' => 'Address of the place',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_place_address_type',
                                'label' => 'Type',
                                'name' => 'type',
                                'type' => 'select',
                                'choices' => array(
                                    'HOME' => 'HOME',
                                    'OFFICE' => 'OFFICE',
                                    'OTHER' => 'OTHER',
                                ),
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_place_address_title',
                                'label' => 'Title',
                                'name' => 'title',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_formatted_address',
                                'label' => 'Formatted Address',
                                'name' => 'formatted_address',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_address_1',
                                'label' => 'Address Line 1',
                                'name' => 'address_1',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_address_2',
                                'label' => 'Address Line 2',
                                'name' => 'address_2',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_floor',
                                'label' => 'Floor',
                                'name' => 'floor',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_floor_type',
                                        'label' => 'Type',
                                        'name' => 'type',
                                        'type' => 'select',
                                        'choices' => array(
                                            'GROUNDPLUS' => 'GROUND PLUS',
                                            'ONEPLUS' => 'ONE PLUS',
                                        ),
                                        'ui' => 1
                                    ),
                                    array(
                                        'key' => 'field_place_floor_number',
                                        'label' => 'Number',
                                        'name' => 'number',
                                        'type' => 'number'
                                    ),
                                )
                            ),
                            array(
                                'key' => 'field_place_landmark',
                                'label' => 'Landmark',
                                'name' => 'landmark',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_city',
                                'label' => 'City',
                                'name' => 'city',
                                'type' => 'text'
                            ),
                            array(
                                'key' => 'field_place_state',
                                'label' => 'State',
                                'name' => 'state',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_state_name',
                                        'label' => 'Name',
                                        'name' => 'name',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_state_code',
                                        'label' => 'Code',
                                        'name' => 'code',
                                        'type' => 'text'
                                    )
                                )
                            ),
                            array(
                                'key' => 'field_place_country',
                                'label' => 'Country',
                                'name' => 'country',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_country_id',
                                        'label' => 'Country ID',
                                        'name' => 'id',
                                        'type' => 'number'
                                    ),
                                    array(
                                        'key' => 'field_place_country_name',
                                        'label' => 'Country Name',
                                        'name' => 'name',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_country_code',
                                        'label' => 'Country Code',
                                        'name' => 'code',
                                        'type' => 'text',
                                        'maxlength' => 2
                                    ),
                                )
                            ),
                            array(
                                'key' => 'field_place_pin_zip',
                                'label' => 'PIN/ZIP',
                                'name' => 'pin_zip',
                                'type' => 'text',
                                'instructions' => 'PIN code or zip code'
                            ),
                            array(
                                'key' => 'field_place_coordinates',
                                'label' => 'Coordinates',
                                'name' => 'coordinates',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_latitude',
                                        'label' => 'Latitude',
                                        'name' => 'lat',
                                        'type' => 'number',
                                        'step' => '0.000001'
                                    ),
                                    array(
                                        'key' => 'field_place_longitude',
                                        'label' => 'Longitude',
                                        'name' => 'lon',
                                        'type' => 'number',
                                        'step' => '0.000001'
                                    ),
                                )
                            ),
                            array(
                                'key' => 'field_place_google_maps',
                                'label' => 'Google Maps',
                                'name' => 'google_maps',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_gm_place_id',
                                        'label' => 'Place ID',
                                        'name' => 'place_id',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_type',
                                        'label' => 'Type',
                                        'name' => 'type',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_colloquial_area',
                                        'label' => 'Colloquial Area',
                                        'name' => 'colloquial_area',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_locality',
                                        'label' => 'Locality',
                                        'name' => 'locality',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_sublocality',
                                        'label' => 'Sublocality',
                                        'name' => 'sublocality',
                                        'type' => 'group',
                                        'sub_fields' => array(
                                            array(
                                                'key' => 'field_place_sublocality_level_1',
                                                'label' => 'Sublocality Level 1',
                                                'name' => 'sublocality_level_1',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_sublocality_level_2',
                                                'label' => 'Sublocality Level 2',
                                                'name' => 'sublocality_level_2',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_sublocality_level_3',
                                                'label' => 'Sublocality Level 3',
                                                'name' => 'sublocality_level_3',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_sublocality_level_4',
                                                'label' => 'Sublocality Level 4',
                                                'name' => 'sublocality_level_4',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_sublocality_level_5',
                                                'label' => 'Sublocality Level 5',
                                                'name' => 'sublocality_level_5',
                                                'type' => 'text'
                                            ),
                                        )
                                    ),
                                    array(
                                        'key' => 'field_place_gm_neighborhood',
                                        'label' => 'Neighborhood',
                                        'name' => 'neighborhood',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_postal_code',
                                        'label' => 'Postal Code',
                                        'name' => 'postal_code',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_floor',
                                        'label' => 'Floor',
                                        'name' => 'floor',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_landmark',
                                        'label' => 'Landmark',
                                        'name' => 'landmark',
                                        'type' => 'text'
                                    ),
                                    array(
                                        'key' => 'field_place_gm_administrative_area',
                                        'label' => 'Administrative Area',
                                        'name' => 'administrative_area',
                                        'type' => 'group',
                                        'sub_fields' => array(
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_1',
                                                'label' => 'Administrative Area Level 1',
                                                'name' => 'administrative_area_level_1',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_2',
                                                'label' => 'Administrative Area Level 2',
                                                'name' => 'administrative_area_level_2',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_3',
                                                'label' => 'Administrative Area Level 3',
                                                'name' => 'administrative_area_level_3',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_4',
                                                'label' => 'Administrative Area Level 4',
                                                'name' => 'administrative_area_level_4',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_5',
                                                'label' => 'Administrative Area Level 5',
                                                'name' => 'administrative_area_level_5',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_6',
                                                'label' => 'Administrative Area Level 6',
                                                'name' => 'administrative_area_level_6',
                                                'type' => 'text'
                                            ),
                                            array(
                                                'key' => 'field_place_gm_admin_area_level_7',
                                                'label' => 'Administrative Area Level 7',
                                                'name' => 'administrative_area_level_7',
                                                'type' => 'text'
                                            )
                                        )
                                    )
                                )
                            ),
                        )
                    ),
                    array(
                        'key' => 'field_place_facilities',
                        'label' => 'Facilities',
                        'name' => 'facilities',
                        'type' => 'group',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_place_fac_car_parking',
                                'label' => 'Car Parking',
                                'name' => 'car_parking',
                                'type' => 'group',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_fac_self_parking',
                                        'label' => 'Self Parking',
                                        'name' => 'self_parking',
                                        'type' => 'true_false',
                                        'ui' => 1
                                    ),
                                    array(
                                        'key' => 'field_place_fac_valet_service',
                                        'label' => 'Valet Service',
                                        'name' => 'valet_service',
                                        'type' => 'true_false',
                                        'ui' => 1
                                    ),
                                )
                            ),
                            array(
                                'key' => 'field_place_fac_bike_parking',
                                'label' => 'Bike Parking',
                                'name' => 'bike_parking',
                                'type' => 'true_false',
                                'ui' => 1
                            ),
                        )
                    ),
                    array(
                        'key' => 'field_place_open_hours',
                        'label' => 'Open Hours',
                        'name' => 'open_hours',
                        'type' => 'repeater',
                        'instructions' => 'Working hours of the place',
                        'sub_fields' => array(
                            // Each item in open_hours array
                            array(
                                'key' => 'field_place_oh_day',
                                'label' => 'Day',
                                'name' => 'day',
                                'type' => 'select',
                                'choices' => array(
                                    'MON' => 'Monday',
                                    'TUE' => 'Tuesday',
                                    'WED' => 'Wednesday',
                                    'THU' => 'Thurdasy',
                                    'FRI' => 'Friday',
                                    'SAT' => 'Saturday',
                                    'SUN' => 'Sunday',
                                ),
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_place_oh_day_is_available',
                                'label' => 'Is Available',
                                'name' => 'is_available',
                                'type' => 'true_false',
                                'default_value' => 1,
                                'ui' => 1
                            ),
                            array(
                                'key' => 'field_place_oh_day_time_slot',
                                'label' => 'Time Slot',
                                'name' => 'time_slot',
                                'type' => 'repeater',
                                'instructions' => 'One or more time slots for this day',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_place_oh_day_time_slot_start',
                                        'label' => 'Start Time',
                                        'name' => 'start',
                                        'type' => 'time_picker',
                                        'display_format' => 'H:i',
                                        'return_format' => 'H:i',
                                    ),
                                    array(
                                        'key' => 'field_place_oh_day_time_slot_end',
                                        'label' => 'End Time',
                                        'name' => 'end',
                                        'type' => 'time_picker',
                                        'display_format' => 'H:i',
                                        'return_format' => 'H:i',
                                    ),
                                ),
                                'layout' => 'table',
                                'button_label' => 'Add Time Slot'
                            ),
                        ),
                        'layout' => 'block',
                        'button_label' => 'Add Day'
                    ),
                ),
                'location' => array(
                    array(
                        array(
                            'param' => 'post_type',
                            'operator' => '==',
                            'value' => 'place',
                        ),
                    ),
                ),
            ));
        }
    }
}
