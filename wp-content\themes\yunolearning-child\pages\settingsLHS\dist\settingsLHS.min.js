/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function t(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function e(t,e,n,a){for(var i=-1,o=null==t?0:t.length;++i<o;){var r=t[i];e(a,r,n(r),t)}return a}function n(t,e){for(var n=-1,a=null==t?0:t.length;++n<a&&!1!==e(t[n],n,t););return t}function a(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}function i(t,e){for(var n=-1,a=null==t?0:t.length;++n<a;)if(!e(t[n],n,t))return!1;return!0}function o(t,e){for(var n=-1,a=null==t?0:t.length,i=0,o=[];++n<a;){var r=t[n];e(r,n,t)&&(o[i++]=r)}return o}function r(t,e){return!(null==t||!t.length)&&g(t,e,0)>-1}function s(t,e,n){for(var a=-1,i=null==t?0:t.length;++a<i;)if(n(e,t[a]))return!0;return!1}function l(t,e){for(var n=-1,a=null==t?0:t.length,i=Array(a);++n<a;)i[n]=e(t[n],n,t);return i}function c(t,e){for(var n=-1,a=e.length,i=t.length;++n<a;)t[i+n]=e[n];return t}function u(t,e,n,a){var i=-1,o=null==t?0:t.length;for(a&&o&&(n=t[++i]);++i<o;)n=e(n,t[i],i,t);return n}function d(t,e,n,a){var i=null==t?0:t.length;for(a&&i&&(n=t[--i]);i--;)n=e(n,t[i],i,t);return n}function p(t,e){for(var n=-1,a=null==t?0:t.length;++n<a;)if(e(t[n],n,t))return!0;return!1}function f(t){return t.match(Qt)||[]}function m(t,e,n){var a;return n(t,(function(t,n,i){if(e(t,n,i))return a=n,!1})),a}function h(t,e,n,a){for(var i=t.length,o=n+(a?1:-1);a?o--:++o<i;)if(e(t[o],o,t))return o;return-1}function g(t,e,n){return e==e?function(t,e,n){for(var a=n-1,i=t.length;++a<i;)if(t[a]===e)return a;return-1}(t,e,n):h(t,b,n)}function v(t,e,n,a){for(var i=n-1,o=t.length;++i<o;)if(a(t[i],e))return i;return-1}function b(t){return t!=t}function y(t,e){var n=null==t?0:t.length;return n?S(t,e)/n:X}function _(t){return function(e){return null==e?V:e[t]}}function w(t){return function(e){return null==t?V:t[e]}}function C(t,e,n,a,i){return i(t,(function(t,i,o){n=a?(a=!1,t):e(n,t,i,o)})),n}function S(t,e){for(var n,a=-1,i=t.length;++a<i;){var o=e(t[a]);o!==V&&(n=n===V?o:n+o)}return n}function k(t,e){for(var n=-1,a=Array(t);++n<t;)a[n]=e(n);return a}function I(t){return t?t.slice(0,j(t)+1).replace(Gt,""):t}function A(t){return function(e){return t(e)}}function T(t,e){return l(e,(function(e){return t[e]}))}function x(t,e){return t.has(e)}function L(t,e){for(var n=-1,a=t.length;++n<a&&g(e,t[n],0)>-1;);return n}function O(t,e){for(var n=t.length;n--&&g(e,t[n],0)>-1;);return n}function R(t){return"\\"+Ke[t]}function U(t){return ze.test(t)}function M(t){return Ye.test(t)}function N(t){var e=-1,n=Array(t.size);return t.forEach((function(t,a){n[++e]=[a,t]})),n}function $(t,e){return function(n){return t(e(n))}}function D(t,e){for(var n=-1,a=t.length,i=0,o=[];++n<a;){var r=t[n];r!==e&&r!==Y||(t[n]=Y,o[i++]=n)}return o}function P(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}function E(t){return U(t)?function(t){for(var e=Ve.lastIndex=0;Ve.test(t);)++e;return e}(t):mn(t)}function F(t){return U(t)?function(t){return t.match(Ve)||[]}(t):function(t){return t.split("")}(t)}function j(t){for(var e=t.length;e--&&qt.test(t.charAt(e)););return e}function W(t){return t.match(Be)||[]}var V,B="Expected a function",z="__lodash_hash_undefined__",Y="__lodash_placeholder__",G=16,q=32,H=64,J=128,K=256,Q=1/0,Z=9007199254740991,X=NaN,tt=4294967295,et=tt-1,nt=tt>>>1,at=[["ary",J],["bind",1],["bindKey",2],["curry",8],["curryRight",G],["flip",512],["partial",q],["partialRight",H],["rearg",K]],it="[object Arguments]",ot="[object Array]",rt="[object Boolean]",st="[object Date]",lt="[object Error]",ct="[object Function]",ut="[object GeneratorFunction]",dt="[object Map]",pt="[object Number]",ft="[object Object]",mt="[object Promise]",ht="[object RegExp]",gt="[object Set]",vt="[object String]",bt="[object Symbol]",yt="[object WeakMap]",_t="[object ArrayBuffer]",wt="[object DataView]",Ct="[object Float32Array]",St="[object Float64Array]",kt="[object Int8Array]",It="[object Int16Array]",At="[object Int32Array]",Tt="[object Uint8Array]",xt="[object Uint8ClampedArray]",Lt="[object Uint16Array]",Ot="[object Uint32Array]",Rt=/\b__p \+= '';/g,Ut=/\b(__p \+=) '' \+/g,Mt=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Nt=/&(?:amp|lt|gt|quot|#39);/g,$t=/[&<>"']/g,Dt=RegExp(Nt.source),Pt=RegExp($t.source),Et=/<%-([\s\S]+?)%>/g,Ft=/<%([\s\S]+?)%>/g,jt=/<%=([\s\S]+?)%>/g,Wt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Vt=/^\w*$/,Bt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,zt=/[\\^$.*+?()[\]{}|]/g,Yt=RegExp(zt.source),Gt=/^\s+/,qt=/\s/,Ht=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Jt=/\{\n\/\* \[wrapped with (.+)\] \*/,Kt=/,? & /,Qt=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Zt=/[()=,{}\[\]\/\s]/,Xt=/\\(\\)?/g,te=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,ee=/\w*$/,ne=/^[-+]0x[0-9a-f]+$/i,ae=/^0b[01]+$/i,ie=/^\[object .+?Constructor\]$/,oe=/^0o[0-7]+$/i,re=/^(?:0|[1-9]\d*)$/,se=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,le=/($^)/,ce=/['\n\r\u2028\u2029\\]/g,ue="\\ud800-\\udfff",de="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pe="\\u2700-\\u27bf",fe="a-z\\xdf-\\xf6\\xf8-\\xff",me="A-Z\\xc0-\\xd6\\xd8-\\xde",he="\\ufe0e\\ufe0f",ge="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ve="['’]",be="["+ue+"]",ye="["+ge+"]",_e="["+de+"]",we="\\d+",Ce="["+pe+"]",Se="["+fe+"]",ke="[^"+ue+ge+we+pe+fe+me+"]",Ie="\\ud83c[\\udffb-\\udfff]",Ae="[^"+ue+"]",Te="(?:\\ud83c[\\udde6-\\uddff]){2}",xe="[\\ud800-\\udbff][\\udc00-\\udfff]",Le="["+me+"]",Oe="\\u200d",Re="(?:"+Se+"|"+ke+")",Ue="(?:"+Le+"|"+ke+")",Me="(?:['’](?:d|ll|m|re|s|t|ve))?",Ne="(?:['’](?:D|LL|M|RE|S|T|VE))?",$e="(?:"+_e+"|"+Ie+")"+"?",De="["+he+"]?",Pe=De+$e+("(?:"+Oe+"(?:"+[Ae,Te,xe].join("|")+")"+De+$e+")*"),Ee="(?:"+[Ce,Te,xe].join("|")+")"+Pe,Fe="(?:"+[Ae+_e+"?",_e,Te,xe,be].join("|")+")",je=RegExp(ve,"g"),We=RegExp(_e,"g"),Ve=RegExp(Ie+"(?="+Ie+")|"+Fe+Pe,"g"),Be=RegExp([Le+"?"+Se+"+"+Me+"(?="+[ye,Le,"$"].join("|")+")",Ue+"+"+Ne+"(?="+[ye,Le+Re,"$"].join("|")+")",Le+"?"+Re+"+"+Me,Le+"+"+Ne,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",we,Ee].join("|"),"g"),ze=RegExp("["+Oe+ue+de+he+"]"),Ye=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Ge=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],qe=-1,He={};He[Ct]=He[St]=He[kt]=He[It]=He[At]=He[Tt]=He[xt]=He[Lt]=He[Ot]=!0,He[it]=He[ot]=He[_t]=He[rt]=He[wt]=He[st]=He[lt]=He[ct]=He[dt]=He[pt]=He[ft]=He[ht]=He[gt]=He[vt]=He[yt]=!1;var Je={};Je[it]=Je[ot]=Je[_t]=Je[wt]=Je[rt]=Je[st]=Je[Ct]=Je[St]=Je[kt]=Je[It]=Je[At]=Je[dt]=Je[pt]=Je[ft]=Je[ht]=Je[gt]=Je[vt]=Je[bt]=Je[Tt]=Je[xt]=Je[Lt]=Je[Ot]=!0,Je[lt]=Je[ct]=Je[yt]=!1;var Ke={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qe=parseFloat,Ze=parseInt,Xe="object"==typeof global&&global&&global.Object===Object&&global,tn="object"==typeof self&&self&&self.Object===Object&&self,en=Xe||tn||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,an=nn&&"object"==typeof module&&module&&!module.nodeType&&module,on=an&&an.exports===nn,rn=on&&Xe.process,sn=function(){try{var t=an&&an.require&&an.require("util").types;return t||rn&&rn.binding&&rn.binding("util")}catch(t){}}(),ln=sn&&sn.isArrayBuffer,cn=sn&&sn.isDate,un=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,fn=sn&&sn.isTypedArray,mn=_("length"),hn=w({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),gn=w({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=w({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),bn=function w(qt){function Qt(t){if(ji(t)&&!Os(t)&&!(t instanceof pe)){if(t instanceof de)return t;if(Oo.call(t,"__wrapped__"))return pi(t)}return new de(t)}function ue(){}function de(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=V}function pe(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=tt,this.__views__=[]}function fe(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function me(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function he(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var a=t[e];this.set(a[0],a[1])}}function ge(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new he;++e<n;)this.add(t[e])}function ve(t){this.size=(this.__data__=new me(t)).size}function be(t,e){var n=Os(t),a=!n&&Ls(t),i=!n&&!a&&Us(t),o=!n&&!a&&!i&&Ps(t),r=n||a||i||o,s=r?k(t.length,So):[],l=s.length;for(var c in t)!e&&!Oo.call(t,c)||r&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ka(c,l))||s.push(c);return s}function ye(t){var e=t.length;return e?t[Mn(0,e-1)]:V}function _e(t,e){return li(ua(t),Le(e,0,t.length))}function we(t){return li(ua(t))}function Ce(t,e,n){(n===V||Ui(t[e],n))&&(n!==V||e in t)||Te(t,e,n)}function Se(t,e,n){var a=t[e];Oo.call(t,e)&&Ui(a,n)&&(n!==V||e in t)||Te(t,e,n)}function ke(t,e){for(var n=t.length;n--;)if(Ui(t[n][0],e))return n;return-1}function Ie(t,e,n,a){return xr(t,(function(t,i,o){e(a,t,n(t),o)})),a}function Ae(t,e){return t&&da(e,to(e),t)}function Te(t,e,n){"__proto__"==e&&Ho?Ho(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function xe(t,e){for(var n=-1,a=e.length,i=go(a),o=null==t;++n<a;)i[n]=o?V:Zi(t,e[n]);return i}function Le(t,e,n){return t==t&&(n!==V&&(t=t<=n?t:n),e!==V&&(t=t>=e?t:e)),t}function Oe(t,e,a,i,o,r){var s,l=1&e,c=2&e,u=4&e;if(a&&(s=o?a(t,i,o,r):a(t)),s!==V)return s;if(!Fi(t))return t;var d=Os(t);if(d){if(s=function(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&Oo.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(t),!l)return ua(t,s)}else{var p=jr(t),f=p==ct||p==ut;if(Us(t))return ia(t,l);if(p==ft||p==it||f&&!o){if(s=c||f?{}:Ha(t),!l)return c?function(t,e){return da(t,Fr(t),e)}(t,function(t,e){return t&&da(e,eo(e),t)}(s,t)):function(t,e){return da(t,Er(t),e)}(t,Ae(s,t))}else{if(!Je[p])return o?t:{};s=function(t,e,n){var a=t.constructor;switch(e){case _t:return oa(t);case rt:case st:return new a(+t);case wt:return function(t,e){return new t.constructor(e?oa(t.buffer):t.buffer,t.byteOffset,t.byteLength)}(t,n);case Ct:case St:case kt:case It:case At:case Tt:case xt:case Lt:case Ot:return ra(t,n);case dt:return new a;case pt:case vt:return new a(t);case ht:return function(t){var e=new t.constructor(t.source,ee.exec(t));return e.lastIndex=t.lastIndex,e}(t);case gt:return new a;case bt:return function(t){return Ir?wo(Ir.call(t)):{}}(t)}}(t,p,l)}}r||(r=new ve);var m=r.get(t);if(m)return m;r.set(t,s),Ds(t)?t.forEach((function(n){s.add(Oe(n,e,a,n,t,r))})):Ns(t)&&t.forEach((function(n,i){s.set(i,Oe(n,e,a,i,t,r))}));var h=d?V:(u?c?ja:Fa:c?eo:to)(t);return n(h||t,(function(n,i){h&&(n=t[i=n]),Se(s,i,Oe(n,e,a,i,t,r))})),s}function Re(t,e,n){var a=n.length;if(null==t)return!a;for(t=wo(t);a--;){var i=n[a],o=e[i],r=t[i];if(r===V&&!(i in t)||!o(r))return!1}return!0}function Ue(t,e,n){if("function"!=typeof t)throw new ko(B);return Br((function(){t.apply(V,n)}),e)}function Me(t,e,n,a){var i=-1,o=r,c=!0,u=t.length,d=[],p=e.length;if(!u)return d;n&&(e=l(e,A(n))),a?(o=s,c=!1):e.length>=200&&(o=x,c=!1,e=new ge(e));t:for(;++i<u;){var f=t[i],m=null==n?f:n(f);if(f=a||0!==f?f:0,c&&m==m){for(var h=p;h--;)if(e[h]===m)continue t;d.push(f)}else o(e,m,a)||d.push(f)}return d}function Ne(t,e){var n=!0;return xr(t,(function(t,a,i){return n=!!e(t,a,i)})),n}function $e(t,e,n){for(var a=-1,i=t.length;++a<i;){var o=t[a],r=e(o);if(null!=r&&(s===V?r==r&&!zi(r):n(r,s)))var s=r,l=o}return l}function De(t,e){var n=[];return xr(t,(function(t,a,i){e(t,a,i)&&n.push(t)})),n}function Pe(t,e,n,a,i){var o=-1,r=t.length;for(n||(n=Ja),i||(i=[]);++o<r;){var s=t[o];e>0&&n(s)?e>1?Pe(s,e-1,n,a,i):c(i,s):a||(i[i.length]=s)}return i}function Ee(t,e){return t&&Or(t,e,to)}function Fe(t,e){return t&&Rr(t,e,to)}function Ve(t,e){return o(e,(function(e){return Di(t[e])}))}function Be(t,e){for(var n=0,a=(e=na(e,t)).length;null!=t&&n<a;)t=t[ci(e[n++])];return n&&n==a?t:V}function ze(t,e,n){var a=e(t);return Os(t)?a:c(a,n(t))}function Ye(t){return null==t?t===V?"[object Undefined]":"[object Null]":qo&&qo in wo(t)?function(t){var e=Oo.call(t,qo),n=t[qo];try{t[qo]=V;var a=!0}catch(t){}var i=Mo.call(t);return a&&(e?t[qo]=n:delete t[qo]),i}(t):function(t){return Mo.call(t)}(t)}function Ke(t,e){return t>e}function Xe(t,e){return null!=t&&Oo.call(t,e)}function tn(t,e){return null!=t&&e in wo(t)}function nn(t,e,n){for(var a=n?s:r,i=t[0].length,o=t.length,c=o,u=go(o),d=1/0,p=[];c--;){var f=t[c];c&&e&&(f=l(f,A(e))),d=rr(f.length,d),u[c]=!n&&(e||i>=120&&f.length>=120)?new ge(c&&f):V}f=t[0];var m=-1,h=u[0];t:for(;++m<i&&p.length<d;){var g=f[m],v=e?e(g):g;if(g=n||0!==g?g:0,!(h?x(h,v):a(p,v,n))){for(c=o;--c;){var b=u[c];if(!(b?x(b,v):a(t[c],v,n)))continue t}h&&h.push(v),p.push(g)}}return p}function an(e,n,a){var i=null==(e=ii(e,n=na(n,e)))?e:e[ci(vi(n))];return null==i?V:t(i,e,a)}function rn(t){return ji(t)&&Ye(t)==it}function sn(t,e,n,a,i){return t===e||(null==t||null==e||!ji(t)&&!ji(e)?t!=t&&e!=e:function(t,e,n,a,i,o){var r=Os(t),s=Os(e),l=r?ot:jr(t),c=s?ot:jr(e);l=l==it?ft:l,c=c==it?ft:c;var u=l==ft,d=c==ft,p=l==c;if(p&&Us(t)){if(!Us(e))return!1;r=!0,u=!1}if(p&&!u)return o||(o=new ve),r||Ps(t)?Pa(t,e,n,a,i,o):function(t,e,n,a,i,o,r){switch(n){case wt:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case _t:return!(t.byteLength!=e.byteLength||!o(new Fo(t),new Fo(e)));case rt:case st:case pt:return Ui(+t,+e);case lt:return t.name==e.name&&t.message==e.message;case ht:case vt:return t==e+"";case dt:var s=N;case gt:var l=1&a;if(s||(s=P),t.size!=e.size&&!l)return!1;var c=r.get(t);if(c)return c==e;a|=2,r.set(t,e);var u=Pa(s(t),s(e),a,i,o,r);return r.delete(t),u;case bt:if(Ir)return Ir.call(t)==Ir.call(e)}return!1}(t,e,l,n,a,i,o);if(!(1&n)){var f=u&&Oo.call(t,"__wrapped__"),m=d&&Oo.call(e,"__wrapped__");if(f||m){var h=f?t.value():t,g=m?e.value():e;return o||(o=new ve),i(h,g,n,a,o)}}return!!p&&(o||(o=new ve),function(t,e,n,a,i,o){var r=1&n,s=Fa(t),l=s.length;if(l!=Fa(e).length&&!r)return!1;for(var c=l;c--;){var u=s[c];if(!(r?u in e:Oo.call(e,u)))return!1}var d=o.get(t),p=o.get(e);if(d&&p)return d==e&&p==t;var f=!0;o.set(t,e),o.set(e,t);for(var m=r;++c<l;){var h=t[u=s[c]],g=e[u];if(a)var v=r?a(g,h,u,e,t,o):a(h,g,u,t,e,o);if(!(v===V?h===g||i(h,g,n,a,o):v)){f=!1;break}m||(m="constructor"==u)}if(f&&!m){var b=t.constructor,y=e.constructor;b!=y&&"constructor"in t&&"constructor"in e&&!("function"==typeof b&&b instanceof b&&"function"==typeof y&&y instanceof y)&&(f=!1)}return o.delete(t),o.delete(e),f}(t,e,n,a,i,o))}(t,e,n,a,sn,i))}function mn(t,e,n,a){var i=n.length,o=i,r=!a;if(null==t)return!o;for(t=wo(t);i--;){var s=n[i];if(r&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++i<o;){var l=(s=n[i])[0],c=t[l],u=s[1];if(r&&s[2]){if(c===V&&!(l in t))return!1}else{var d=new ve;if(a)var p=a(c,u,l,t,e,d);if(!(p===V?sn(u,c,3,a,d):p))return!1}}return!0}function yn(t){return!(!Fi(t)||function(t){return!!Uo&&Uo in t}(t))&&(Di(t)?Do:ie).test(ui(t))}function _n(t){return"function"==typeof t?t:null==t?lo:"object"==typeof t?Os(t)?An(t[0],t[1]):In(t):fo(t)}function wn(t){if(!ti(t))return ir(t);var e=[];for(var n in wo(t))Oo.call(t,n)&&"constructor"!=n&&e.push(n);return e}function Cn(t){if(!Fi(t))return function(t){var e=[];if(null!=t)for(var n in wo(t))e.push(n);return e}(t);var e=ti(t),n=[];for(var a in t)("constructor"!=a||!e&&Oo.call(t,a))&&n.push(a);return n}function Sn(t,e){return t<e}function kn(t,e){var n=-1,a=Mi(t)?go(t.length):[];return xr(t,(function(t,i,o){a[++n]=e(t,i,o)})),a}function In(t){var e=Ya(t);return 1==e.length&&e[0][2]?ni(e[0][0],e[0][1]):function(n){return n===t||mn(n,t,e)}}function An(t,e){return Za(t)&&ei(e)?ni(ci(t),e):function(n){var a=Zi(n,t);return a===V&&a===e?Xi(n,t):sn(e,a,3)}}function Tn(t,e,n,a,i){t!==e&&Or(e,(function(o,r){if(i||(i=new ve),Fi(o))!function(t,e,n,a,i,o,r){var s=oi(t,n),l=oi(e,n),c=r.get(l);if(c)return Ce(t,n,c),V;var u=o?o(s,l,n+"",t,e,r):V,d=u===V;if(d){var p=Os(l),f=!p&&Us(l),m=!p&&!f&&Ps(l);u=l,p||f||m?Os(s)?u=s:Ni(s)?u=ua(s):f?(d=!1,u=ia(l,!0)):m?(d=!1,u=ra(l,!0)):u=[]:Vi(l)||Ls(l)?(u=s,Ls(s)?u=Ki(s):Fi(s)&&!Di(s)||(u=Ha(l))):d=!1}d&&(r.set(l,u),i(u,l,a,o,r),r.delete(l)),Ce(t,n,u)}(t,e,r,n,Tn,a,i);else{var s=a?a(oi(t,r),o,r+"",t,e,i):V;s===V&&(s=o),Ce(t,r,s)}}),eo)}function xn(t,e){var n=t.length;if(n)return Ka(e+=e<0?n:0,n)?t[e]:V}function Ln(t,e,n){e=e.length?l(e,(function(t){return Os(t)?function(e){return Be(e,1===t.length?t[0]:t)}:t})):[lo];var a=-1;return e=l(e,A(Ba())),function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}(kn(t,(function(t,n,i){return{criteria:l(e,(function(e){return e(t)})),index:++a,value:t}})),(function(t,e){return function(t,e,n){for(var a=-1,i=t.criteria,o=e.criteria,r=i.length,s=n.length;++a<r;){var l=sa(i[a],o[a]);if(l)return a>=s?l:l*("desc"==n[a]?-1:1)}return t.index-e.index}(t,e,n)}))}function On(t,e,n){for(var a=-1,i=e.length,o={};++a<i;){var r=e[a],s=Be(t,r);n(s,r)&&En(o,na(r,t),s)}return o}function Rn(t,e,n,a){var i=a?v:g,o=-1,r=e.length,s=t;for(t===e&&(e=ua(e)),n&&(s=l(t,A(n)));++o<r;)for(var c=0,u=e[o],d=n?n(u):u;(c=i(s,d,c,a))>-1;)s!==t&&zo.call(s,c,1),zo.call(t,c,1);return t}function Un(t,e){for(var n=t?e.length:0,a=n-1;n--;){var i=e[n];if(n==a||i!==o){var o=i;Ka(i)?zo.call(t,i,1):Hn(t,i)}}return t}function Mn(t,e){return t+Xo(cr()*(e-t+1))}function Nn(t,e){var n="";if(!t||e<1||e>Z)return n;do{e%2&&(n+=t),(e=Xo(e/2))&&(t+=t)}while(e);return n}function $n(t,e){return zr(ai(t,e,lo),t+"")}function Dn(t){return ye(ao(t))}function Pn(t,e){var n=ao(t);return li(n,Le(e,0,n.length))}function En(t,e,n,a){if(!Fi(t))return t;for(var i=-1,o=(e=na(e,t)).length,r=o-1,s=t;null!=s&&++i<o;){var l=ci(e[i]),c=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return t;if(i!=r){var u=s[l];(c=a?a(u,l,s):V)===V&&(c=Fi(u)?u:Ka(e[i+1])?[]:{})}Se(s,l,c),s=s[l]}return t}function Fn(t){return li(ao(t))}function jn(t,e,n){var a=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(n=n>i?i:n)<0&&(n+=i),i=e>n?0:n-e>>>0,e>>>=0;for(var o=go(i);++a<i;)o[a]=t[a+e];return o}function Wn(t,e){var n;return xr(t,(function(t,a,i){return!(n=e(t,a,i))})),!!n}function Vn(t,e,n){var a=0,i=null==t?a:t.length;if("number"==typeof e&&e==e&&i<=nt){for(;a<i;){var o=a+i>>>1,r=t[o];null!==r&&!zi(r)&&(n?r<=e:r<e)?a=o+1:i=o}return i}return Bn(t,e,lo,n)}function Bn(t,e,n,a){var i=0,o=null==t?0:t.length;if(0===o)return 0;for(var r=(e=n(e))!=e,s=null===e,l=zi(e),c=e===V;i<o;){var u=Xo((i+o)/2),d=n(t[u]),p=d!==V,f=null===d,m=d==d,h=zi(d);if(r)var g=a||m;else g=c?m&&(a||p):s?m&&p&&(a||!f):l?m&&p&&!f&&(a||!h):!f&&!h&&(a?d<=e:d<e);g?i=u+1:o=u}return rr(o,et)}function zn(t,e){for(var n=-1,a=t.length,i=0,o=[];++n<a;){var r=t[n],s=e?e(r):r;if(!n||!Ui(s,l)){var l=s;o[i++]=0===r?0:r}}return o}function Yn(t){return"number"==typeof t?t:zi(t)?X:+t}function Gn(t){if("string"==typeof t)return t;if(Os(t))return l(t,Gn)+"";if(zi(t))return Ar?Ar.call(t):"";var e=t+"";return"0"==e&&1/t==-Q?"-0":e}function qn(t,e,n){var a=-1,i=r,o=t.length,l=!0,c=[],u=c;if(n)l=!1,i=s;else if(o>=200){var d=e?null:Dr(t);if(d)return P(d);l=!1,i=x,u=new ge}else u=e?[]:c;t:for(;++a<o;){var p=t[a],f=e?e(p):p;if(p=n||0!==p?p:0,l&&f==f){for(var m=u.length;m--;)if(u[m]===f)continue t;e&&u.push(f),c.push(p)}else i(u,f,n)||(u!==c&&u.push(f),c.push(p))}return c}function Hn(t,e){return null==(t=ii(t,e=na(e,t)))||delete t[ci(vi(e))]}function Jn(t,e,n,a){return En(t,e,n(Be(t,e)),a)}function Kn(t,e,n,a){for(var i=t.length,o=a?i:-1;(a?o--:++o<i)&&e(t[o],o,t););return n?jn(t,a?0:o,a?o+1:i):jn(t,a?o+1:0,a?i:o)}function Qn(t,e){var n=t;return n instanceof pe&&(n=n.value()),u(e,(function(t,e){return e.func.apply(e.thisArg,c([t],e.args))}),n)}function Zn(t,e,n){var a=t.length;if(a<2)return a?qn(t[0]):[];for(var i=-1,o=go(a);++i<a;)for(var r=t[i],s=-1;++s<a;)s!=i&&(o[i]=Me(o[i]||r,t[s],e,n));return qn(Pe(o,1),e,n)}function Xn(t,e,n){for(var a=-1,i=t.length,o=e.length,r={};++a<i;)n(r,t[a],a<o?e[a]:V);return r}function ta(t){return Ni(t)?t:[]}function ea(t){return"function"==typeof t?t:lo}function na(t,e){return Os(t)?t:Za(t,e)?[t]:Yr(Qi(t))}function aa(t,e,n){var a=t.length;return n=n===V?a:n,!e&&n>=a?t:jn(t,e,n)}function ia(t,e){if(e)return t.slice();var n=t.length,a=jo?jo(n):new t.constructor(n);return t.copy(a),a}function oa(t){var e=new t.constructor(t.byteLength);return new Fo(e).set(new Fo(t)),e}function ra(t,e){return new t.constructor(e?oa(t.buffer):t.buffer,t.byteOffset,t.length)}function sa(t,e){if(t!==e){var n=t!==V,a=null===t,i=t==t,o=zi(t),r=e!==V,s=null===e,l=e==e,c=zi(e);if(!s&&!c&&!o&&t>e||o&&r&&l&&!s&&!c||a&&r&&l||!n&&l||!i)return 1;if(!a&&!o&&!c&&t<e||c&&n&&i&&!a&&!o||s&&n&&i||!r&&i||!l)return-1}return 0}function la(t,e,n,a){for(var i=-1,o=t.length,r=n.length,s=-1,l=e.length,c=or(o-r,0),u=go(l+c),d=!a;++s<l;)u[s]=e[s];for(;++i<r;)(d||i<o)&&(u[n[i]]=t[i]);for(;c--;)u[s++]=t[i++];return u}function ca(t,e,n,a){for(var i=-1,o=t.length,r=-1,s=n.length,l=-1,c=e.length,u=or(o-s,0),d=go(u+c),p=!a;++i<u;)d[i]=t[i];for(var f=i;++l<c;)d[f+l]=e[l];for(;++r<s;)(p||i<o)&&(d[f+n[r]]=t[i++]);return d}function ua(t,e){var n=-1,a=t.length;for(e||(e=go(a));++n<a;)e[n]=t[n];return e}function da(t,e,n,a){var i=!n;n||(n={});for(var o=-1,r=e.length;++o<r;){var s=e[o],l=a?a(n[s],t[s],s,n,t):V;l===V&&(l=t[s]),i?Te(n,s,l):Se(n,s,l)}return n}function pa(t,n){return function(a,i){var o=Os(a)?e:Ie,r=n?n():{};return o(a,t,Ba(i,2),r)}}function fa(t){return $n((function(e,n){var a=-1,i=n.length,o=i>1?n[i-1]:V,r=i>2?n[2]:V;for(o=t.length>3&&"function"==typeof o?(i--,o):V,r&&Qa(n[0],n[1],r)&&(o=i<3?V:o,i=1),e=wo(e);++a<i;){var s=n[a];s&&t(e,s,a,o)}return e}))}function ma(t,e){return function(n,a){if(null==n)return n;if(!Mi(n))return t(n,a);for(var i=n.length,o=e?i:-1,r=wo(n);(e?o--:++o<i)&&!1!==a(r[o],o,r););return n}}function ha(t){return function(e,n,a){for(var i=-1,o=wo(e),r=a(e),s=r.length;s--;){var l=r[t?s:++i];if(!1===n(o[l],l,o))break}return e}}function ga(t){return function(e){var n=U(e=Qi(e))?F(e):V,a=n?n[0]:e.charAt(0),i=n?aa(n,1).join(""):e.slice(1);return a[t]()+i}}function va(t){return function(e){return u(ro(oo(e).replace(je,"")),t,"")}}function ba(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=Tr(t.prototype),a=t.apply(n,e);return Fi(a)?a:n}}function ya(e,n,a){var i=ba(e);return function o(){for(var r=arguments.length,s=go(r),l=r,c=Va(o);l--;)s[l]=arguments[l];var u=r<3&&s[0]!==c&&s[r-1]!==c?[]:D(s,c);return(r-=u.length)<a?Oa(e,n,Ca,o.placeholder,V,s,u,V,V,a-r):t(this&&this!==en&&this instanceof o?i:e,this,s)}}function _a(t){return function(e,n,a){var i=wo(e);if(!Mi(e)){var o=Ba(n,3);e=to(e),n=function(t){return o(i[t],t,i)}}var r=t(e,n,a);return r>-1?i[o?e[r]:r]:V}}function wa(t){return Ea((function(e){var n=e.length,a=n,i=de.prototype.thru;for(t&&e.reverse();a--;){var o=e[a];if("function"!=typeof o)throw new ko(B);if(i&&!r&&"wrapper"==Wa(o))var r=new de([],!0)}for(a=r?a:n;++a<n;){var s=Wa(o=e[a]),l="wrapper"==s?Pr(o):V;r=l&&Xa(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?r[Wa(l[0])].apply(r,l[3]):1==o.length&&Xa(o)?r[s]():r.thru(o)}return function(){var t=arguments,a=t[0];if(r&&1==t.length&&Os(a))return r.plant(a).value();for(var i=0,o=n?e[i].apply(this,t):a;++i<n;)o=e[i].call(this,o);return o}}))}function Ca(t,e,n,a,i,o,r,s,l,c){var u=e&J,d=1&e,p=2&e,f=24&e,m=512&e,h=p?V:ba(t);return function g(){for(var v=arguments.length,b=go(v),y=v;y--;)b[y]=arguments[y];if(f)var _=Va(g),w=function(t,e){for(var n=t.length,a=0;n--;)t[n]===e&&++a;return a}(b,_);if(a&&(b=la(b,a,i,f)),o&&(b=ca(b,o,r,f)),v-=w,f&&v<c)return Oa(t,e,Ca,g.placeholder,n,b,D(b,_),s,l,c-v);var C=d?n:this,S=p?C[t]:t;return v=b.length,s?b=function(t,e){for(var n=t.length,a=rr(e.length,n),i=ua(t);a--;){var o=e[a];t[a]=Ka(o,n)?i[o]:V}return t}(b,s):m&&v>1&&b.reverse(),u&&l<v&&(b.length=l),this&&this!==en&&this instanceof g&&(S=h||ba(S)),S.apply(C,b)}}function Sa(t,e){return function(n,a){return function(t,e,n,a){return Ee(t,(function(t,i,o){e(a,n(t),i,o)})),a}(n,t,e(a),{})}}function ka(t,e){return function(n,a){var i;if(n===V&&a===V)return e;if(n!==V&&(i=n),a!==V){if(i===V)return a;"string"==typeof n||"string"==typeof a?(n=Gn(n),a=Gn(a)):(n=Yn(n),a=Yn(a)),i=t(n,a)}return i}}function Ia(e){return Ea((function(n){return n=l(n,A(Ba())),$n((function(a){var i=this;return e(n,(function(e){return t(e,i,a)}))}))}))}function Aa(t,e){var n=(e=e===V?" ":Gn(e)).length;if(n<2)return n?Nn(e,t):e;var a=Nn(e,Zo(t/E(e)));return U(e)?aa(F(a),0,t).join(""):a.slice(0,t)}function Ta(e,n,a,i){var o=1&n,r=ba(e);return function n(){for(var s=-1,l=arguments.length,c=-1,u=i.length,d=go(u+l),p=this&&this!==en&&this instanceof n?r:e;++c<u;)d[c]=i[c];for(;l--;)d[c++]=arguments[++s];return t(p,o?a:this,d)}}function xa(t){return function(e,n,a){return a&&"number"!=typeof a&&Qa(e,n,a)&&(n=a=V),e=Gi(e),n===V?(n=e,e=0):n=Gi(n),function(t,e,n,a){for(var i=-1,o=or(Zo((e-t)/(n||1)),0),r=go(o);o--;)r[a?o:++i]=t,t+=n;return r}(e,n,a=a===V?e<n?1:-1:Gi(a),t)}}function La(t){return function(e,n){return"string"==typeof e&&"string"==typeof n||(e=Ji(e),n=Ji(n)),t(e,n)}}function Oa(t,e,n,a,i,o,r,s,l,c){var u=8&e;e|=u?q:H,4&(e&=~(u?H:q))||(e&=-4);var d=[t,e,i,u?o:V,u?r:V,u?V:o,u?V:r,s,l,c],p=n.apply(V,d);return Xa(t)&&Vr(p,d),p.placeholder=a,ri(p,t,e)}function Ra(t){var e=_o[t];return function(t,n){if(t=Ji(t),(n=null==n?0:rr(qi(n),292))&&nr(t)){var a=(Qi(t)+"e").split("e");return+((a=(Qi(e(a[0]+"e"+(+a[1]+n)))+"e").split("e"))[0]+"e"+(+a[1]-n))}return e(t)}}function Ua(t){return function(e){var n=jr(e);return n==dt?N(e):n==gt?function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=[t,t]})),n}(e):function(t,e){return l(e,(function(e){return[e,t[e]]}))}(e,t(e))}}function Ma(t,e,n,a,i,o,r,s){var l=2&e;if(!l&&"function"!=typeof t)throw new ko(B);var c=a?a.length:0;if(c||(e&=-97,a=i=V),r=r===V?r:or(qi(r),0),s=s===V?s:qi(s),c-=i?i.length:0,e&H){var u=a,d=i;a=i=V}var p=l?V:Pr(t),f=[t,e,n,a,i,u,d,o,r,s];if(p&&function(t,e){var n=t[1],a=e[1],i=n|a,o=i<131,r=a==J&&8==n||a==J&&n==K&&t[7].length<=e[8]||384==a&&e[7].length<=e[8]&&8==n;if(!o&&!r)return t;1&a&&(t[2]=e[2],i|=1&n?0:4);var s=e[3];if(s){var l=t[3];t[3]=l?la(l,s,e[4]):s,t[4]=l?D(t[3],Y):e[4]}s=e[5],s&&(l=t[5],t[5]=l?ca(l,s,e[6]):s,t[6]=l?D(t[5],Y):e[6]),s=e[7],s&&(t[7]=s),a&J&&(t[8]=null==t[8]?e[8]:rr(t[8],e[8])),null==t[9]&&(t[9]=e[9]),t[0]=e[0],t[1]=i}(f,p),t=f[0],e=f[1],n=f[2],a=f[3],i=f[4],!(s=f[9]=f[9]===V?l?0:t.length:or(f[9]-c,0))&&24&e&&(e&=-25),e&&1!=e)m=8==e||e==G?ya(t,e,s):e!=q&&33!=e||i.length?Ca.apply(V,f):Ta(t,e,n,a);else var m=function(t,e,n){var a=1&e,i=ba(t);return function e(){return(this&&this!==en&&this instanceof e?i:t).apply(a?n:this,arguments)}}(t,e,n);return ri((p?Ur:Vr)(m,f),t,e)}function Na(t,e,n,a){return t===V||Ui(t,To[n])&&!Oo.call(a,n)?e:t}function $a(t,e,n,a,i,o){return Fi(t)&&Fi(e)&&(o.set(e,t),Tn(t,e,V,$a,o),o.delete(e)),t}function Da(t){return Vi(t)?V:t}function Pa(t,e,n,a,i,o){var r=1&n,s=t.length,l=e.length;if(s!=l&&!(r&&l>s))return!1;var c=o.get(t),u=o.get(e);if(c&&u)return c==e&&u==t;var d=-1,f=!0,m=2&n?new ge:V;for(o.set(t,e),o.set(e,t);++d<s;){var h=t[d],g=e[d];if(a)var v=r?a(g,h,d,e,t,o):a(h,g,d,t,e,o);if(v!==V){if(v)continue;f=!1;break}if(m){if(!p(e,(function(t,e){if(!x(m,e)&&(h===t||i(h,t,n,a,o)))return m.push(e)}))){f=!1;break}}else if(h!==g&&!i(h,g,n,a,o)){f=!1;break}}return o.delete(t),o.delete(e),f}function Ea(t){return zr(ai(t,V,hi),t+"")}function Fa(t){return ze(t,to,Er)}function ja(t){return ze(t,eo,Fr)}function Wa(t){for(var e=t.name+"",n=br[e],a=Oo.call(br,e)?n.length:0;a--;){var i=n[a],o=i.func;if(null==o||o==t)return i.name}return e}function Va(t){return(Oo.call(Qt,"placeholder")?Qt:t).placeholder}function Ba(){var t=Qt.iteratee||co;return t=t===co?_n:t,arguments.length?t(arguments[0],arguments[1]):t}function za(t,e){var n=t.__data__;return function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}(e)?n["string"==typeof e?"string":"hash"]:n.map}function Ya(t){for(var e=to(t),n=e.length;n--;){var a=e[n],i=t[a];e[n]=[a,i,ei(i)]}return e}function Ga(t,e){var n=function(t,e){return null==t?V:t[e]}(t,e);return yn(n)?n:V}function qa(t,e,n){for(var a=-1,i=(e=na(e,t)).length,o=!1;++a<i;){var r=ci(e[a]);if(!(o=null!=t&&n(t,r)))break;t=t[r]}return o||++a!=i?o:!!(i=null==t?0:t.length)&&Ei(i)&&Ka(r,i)&&(Os(t)||Ls(t))}function Ha(t){return"function"!=typeof t.constructor||ti(t)?{}:Tr(Wo(t))}function Ja(t){return Os(t)||Ls(t)||!!(Yo&&t&&t[Yo])}function Ka(t,e){var n=typeof t;return!!(e=null==e?Z:e)&&("number"==n||"symbol"!=n&&re.test(t))&&t>-1&&t%1==0&&t<e}function Qa(t,e,n){if(!Fi(n))return!1;var a=typeof e;return!!("number"==a?Mi(n)&&Ka(e,n.length):"string"==a&&e in n)&&Ui(n[e],t)}function Za(t,e){if(Os(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!zi(t))||Vt.test(t)||!Wt.test(t)||null!=e&&t in wo(e)}function Xa(t){var e=Wa(t),n=Qt[e];if("function"!=typeof n||!(e in pe.prototype))return!1;if(t===n)return!0;var a=Pr(n);return!!a&&t===a[0]}function ti(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||To)}function ei(t){return t==t&&!Fi(t)}function ni(t,e){return function(n){return null!=n&&n[t]===e&&(e!==V||t in wo(n))}}function ai(e,n,a){return n=or(n===V?e.length-1:n,0),function(){for(var i=arguments,o=-1,r=or(i.length-n,0),s=go(r);++o<r;)s[o]=i[n+o];o=-1;for(var l=go(n+1);++o<n;)l[o]=i[o];return l[n]=a(s),t(e,this,l)}}function ii(t,e){return e.length<2?t:Be(t,jn(e,0,-1))}function oi(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}function ri(t,e,n){var a=e+"";return zr(t,function(t,e){var n=e.length;if(!n)return t;var a=n-1;return e[a]=(n>1?"& ":"")+e[a],e=e.join(n>2?", ":" "),t.replace(Ht,"{\n/* [wrapped with "+e+"] */\n")}(a,di(function(t){var e=t.match(Jt);return e?e[1].split(Kt):[]}(a),n)))}function si(t){var e=0,n=0;return function(){var a=sr(),i=16-(a-n);if(n=a,i>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(V,arguments)}}function li(t,e){var n=-1,a=t.length,i=a-1;for(e=e===V?a:e;++n<e;){var o=Mn(n,i),r=t[o];t[o]=t[n],t[n]=r}return t.length=e,t}function ci(t){if("string"==typeof t||zi(t))return t;var e=t+"";return"0"==e&&1/t==-Q?"-0":e}function ui(t){if(null!=t){try{return Lo.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function di(t,e){return n(at,(function(n){var a="_."+n[0];e&n[1]&&!r(t,a)&&t.push(a)})),t.sort()}function pi(t){if(t instanceof pe)return t.clone();var e=new de(t.__wrapped__,t.__chain__);return e.__actions__=ua(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}function fi(t,e,n){var a=null==t?0:t.length;if(!a)return-1;var i=null==n?0:qi(n);return i<0&&(i=or(a+i,0)),h(t,Ba(e,3),i)}function mi(t,e,n){var a=null==t?0:t.length;if(!a)return-1;var i=a-1;return n!==V&&(i=qi(n),i=n<0?or(a+i,0):rr(i,a-1)),h(t,Ba(e,3),i,!0)}function hi(t){return null!=t&&t.length?Pe(t,1):[]}function gi(t){return t&&t.length?t[0]:V}function vi(t){var e=null==t?0:t.length;return e?t[e-1]:V}function bi(t,e){return t&&t.length&&e&&e.length?Rn(t,e):t}function yi(t){return null==t?t:ur.call(t)}function _i(t){if(!t||!t.length)return[];var e=0;return t=o(t,(function(t){if(Ni(t))return e=or(t.length,e),!0})),k(e,(function(e){return l(t,_(e))}))}function wi(e,n){if(!e||!e.length)return[];var a=_i(e);return null==n?a:l(a,(function(e){return t(n,V,e)}))}function Ci(t){var e=Qt(t);return e.__chain__=!0,e}function Si(t,e){return e(t)}function ki(t,e){return(Os(t)?n:xr)(t,Ba(e,3))}function Ii(t,e){return(Os(t)?a:Lr)(t,Ba(e,3))}function Ai(t,e){return(Os(t)?l:kn)(t,Ba(e,3))}function Ti(t,e,n){return e=n?V:e,e=t&&null==e?t.length:e,Ma(t,J,V,V,V,V,e)}function xi(t,e){var n;if("function"!=typeof e)throw new ko(B);return t=qi(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=V),n}}function Li(t,e,n){function a(e){var n=l,a=c;return l=c=V,m=e,d=t.apply(a,n)}function i(t){var n=t-f;return f===V||n>=e||n<0||g&&t-m>=u}function o(){var t=bs();return i(t)?r(t):(p=Br(o,function(t){var n=e-(t-f);return g?rr(n,u-(t-m)):n}(t)),V)}function r(t){return p=V,v&&l?a(t):(l=c=V,d)}function s(){var t=bs(),n=i(t);if(l=arguments,c=this,f=t,n){if(p===V)return function(t){return m=t,p=Br(o,e),h?a(t):d}(f);if(g)return $r(p),p=Br(o,e),a(f)}return p===V&&(p=Br(o,e)),d}var l,c,u,d,p,f,m=0,h=!1,g=!1,v=!0;if("function"!=typeof t)throw new ko(B);return e=Ji(e)||0,Fi(n)&&(h=!!n.leading,u=(g="maxWait"in n)?or(Ji(n.maxWait)||0,e):u,v="trailing"in n?!!n.trailing:v),s.cancel=function(){p!==V&&$r(p),m=0,l=f=c=p=V},s.flush=function(){return p===V?d:r(bs())},s}function Oi(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new ko(B);var n=function(){var a=arguments,i=e?e.apply(this,a):a[0],o=n.cache;if(o.has(i))return o.get(i);var r=t.apply(this,a);return n.cache=o.set(i,r)||o,r};return n.cache=new(Oi.Cache||he),n}function Ri(t){if("function"!=typeof t)throw new ko(B);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}function Ui(t,e){return t===e||t!=t&&e!=e}function Mi(t){return null!=t&&Ei(t.length)&&!Di(t)}function Ni(t){return ji(t)&&Mi(t)}function $i(t){if(!ji(t))return!1;var e=Ye(t);return e==lt||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!Vi(t)}function Di(t){if(!Fi(t))return!1;var e=Ye(t);return e==ct||e==ut||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Pi(t){return"number"==typeof t&&t==qi(t)}function Ei(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Z}function Fi(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function ji(t){return null!=t&&"object"==typeof t}function Wi(t){return"number"==typeof t||ji(t)&&Ye(t)==pt}function Vi(t){if(!ji(t)||Ye(t)!=ft)return!1;var e=Wo(t);if(null===e)return!0;var n=Oo.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&Lo.call(n)==No}function Bi(t){return"string"==typeof t||!Os(t)&&ji(t)&&Ye(t)==vt}function zi(t){return"symbol"==typeof t||ji(t)&&Ye(t)==bt}function Yi(t){if(!t)return[];if(Mi(t))return Bi(t)?F(t):ua(t);if(Go&&t[Go])return function(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}(t[Go]());var e=jr(t);return(e==dt?N:e==gt?P:ao)(t)}function Gi(t){return t?(t=Ji(t))===Q||t===-Q?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function qi(t){var e=Gi(t),n=e%1;return e==e?n?e-n:e:0}function Hi(t){return t?Le(qi(t),0,tt):0}function Ji(t){if("number"==typeof t)return t;if(zi(t))return X;if(Fi(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Fi(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=I(t);var n=ae.test(t);return n||oe.test(t)?Ze(t.slice(2),n?2:8):ne.test(t)?X:+t}function Ki(t){return da(t,eo(t))}function Qi(t){return null==t?"":Gn(t)}function Zi(t,e,n){var a=null==t?V:Be(t,e);return a===V?n:a}function Xi(t,e){return null!=t&&qa(t,e,tn)}function to(t){return Mi(t)?be(t):wn(t)}function eo(t){return Mi(t)?be(t,!0):Cn(t)}function no(t,e){if(null==t)return{};var n=l(ja(t),(function(t){return[t]}));return e=Ba(e),On(t,n,(function(t,n){return e(t,n[0])}))}function ao(t){return null==t?[]:T(t,to(t))}function io(t){return cl(Qi(t).toLowerCase())}function oo(t){return(t=Qi(t))&&t.replace(se,hn).replace(We,"")}function ro(t,e,n){return t=Qi(t),(e=n?V:e)===V?M(t)?W(t):f(t):t.match(e)||[]}function so(t){return function(){return t}}function lo(t){return t}function co(t){return _n("function"==typeof t?t:Oe(t,1))}function uo(t,e,a){var i=to(e),o=Ve(e,i);null!=a||Fi(e)&&(o.length||!i.length)||(a=e,e=t,t=this,o=Ve(e,to(e)));var r=!(Fi(a)&&"chain"in a&&!a.chain),s=Di(t);return n(o,(function(n){var a=e[n];t[n]=a,s&&(t.prototype[n]=function(){var e=this.__chain__;if(r||e){var n=t(this.__wrapped__);return(n.__actions__=ua(this.__actions__)).push({func:a,args:arguments,thisArg:t}),n.__chain__=e,n}return a.apply(t,c([this.value()],arguments))})})),t}function po(){}function fo(t){return Za(t)?_(ci(t)):function(t){return function(e){return Be(e,t)}}(t)}function mo(){return[]}function ho(){return!1}var go=(qt=null==qt?en:bn.defaults(en.Object(),qt,bn.pick(en,Ge))).Array,vo=qt.Date,bo=qt.Error,yo=qt.Function,_o=qt.Math,wo=qt.Object,Co=qt.RegExp,So=qt.String,ko=qt.TypeError,Io=go.prototype,Ao=yo.prototype,To=wo.prototype,xo=qt["__core-js_shared__"],Lo=Ao.toString,Oo=To.hasOwnProperty,Ro=0,Uo=function(){var t=/[^.]+$/.exec(xo&&xo.keys&&xo.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),Mo=To.toString,No=Lo.call(wo),$o=en._,Do=Co("^"+Lo.call(Oo).replace(zt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Po=on?qt.Buffer:V,Eo=qt.Symbol,Fo=qt.Uint8Array,jo=Po?Po.allocUnsafe:V,Wo=$(wo.getPrototypeOf,wo),Vo=wo.create,Bo=To.propertyIsEnumerable,zo=Io.splice,Yo=Eo?Eo.isConcatSpreadable:V,Go=Eo?Eo.iterator:V,qo=Eo?Eo.toStringTag:V,Ho=function(){try{var t=Ga(wo,"defineProperty");return t({},"",{}),t}catch(t){}}(),Jo=qt.clearTimeout!==en.clearTimeout&&qt.clearTimeout,Ko=vo&&vo.now!==en.Date.now&&vo.now,Qo=qt.setTimeout!==en.setTimeout&&qt.setTimeout,Zo=_o.ceil,Xo=_o.floor,tr=wo.getOwnPropertySymbols,er=Po?Po.isBuffer:V,nr=qt.isFinite,ar=Io.join,ir=$(wo.keys,wo),or=_o.max,rr=_o.min,sr=vo.now,lr=qt.parseInt,cr=_o.random,ur=Io.reverse,dr=Ga(qt,"DataView"),pr=Ga(qt,"Map"),fr=Ga(qt,"Promise"),mr=Ga(qt,"Set"),hr=Ga(qt,"WeakMap"),gr=Ga(wo,"create"),vr=hr&&new hr,br={},yr=ui(dr),_r=ui(pr),wr=ui(fr),Cr=ui(mr),Sr=ui(hr),kr=Eo?Eo.prototype:V,Ir=kr?kr.valueOf:V,Ar=kr?kr.toString:V,Tr=function(){function t(){}return function(e){if(!Fi(e))return{};if(Vo)return Vo(e);t.prototype=e;var n=new t;return t.prototype=V,n}}();Qt.templateSettings={escape:Et,evaluate:Ft,interpolate:jt,variable:"",imports:{_:Qt}},Qt.prototype=ue.prototype,Qt.prototype.constructor=Qt,de.prototype=Tr(ue.prototype),de.prototype.constructor=de,pe.prototype=Tr(ue.prototype),pe.prototype.constructor=pe,fe.prototype.clear=function(){this.__data__=gr?gr(null):{},this.size=0},fe.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},fe.prototype.get=function(t){var e=this.__data__;if(gr){var n=e[t];return n===z?V:n}return Oo.call(e,t)?e[t]:V},fe.prototype.has=function(t){var e=this.__data__;return gr?e[t]!==V:Oo.call(e,t)},fe.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=gr&&e===V?z:e,this},me.prototype.clear=function(){this.__data__=[],this.size=0},me.prototype.delete=function(t){var e=this.__data__,n=ke(e,t);return!(n<0||(n==e.length-1?e.pop():zo.call(e,n,1),--this.size,0))},me.prototype.get=function(t){var e=this.__data__,n=ke(e,t);return n<0?V:e[n][1]},me.prototype.has=function(t){return ke(this.__data__,t)>-1},me.prototype.set=function(t,e){var n=this.__data__,a=ke(n,t);return a<0?(++this.size,n.push([t,e])):n[a][1]=e,this},he.prototype.clear=function(){this.size=0,this.__data__={hash:new fe,map:new(pr||me),string:new fe}},he.prototype.delete=function(t){var e=za(this,t).delete(t);return this.size-=e?1:0,e},he.prototype.get=function(t){return za(this,t).get(t)},he.prototype.has=function(t){return za(this,t).has(t)},he.prototype.set=function(t,e){var n=za(this,t),a=n.size;return n.set(t,e),this.size+=n.size==a?0:1,this},ge.prototype.add=ge.prototype.push=function(t){return this.__data__.set(t,z),this},ge.prototype.has=function(t){return this.__data__.has(t)},ve.prototype.clear=function(){this.__data__=new me,this.size=0},ve.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},ve.prototype.get=function(t){return this.__data__.get(t)},ve.prototype.has=function(t){return this.__data__.has(t)},ve.prototype.set=function(t,e){var n=this.__data__;if(n instanceof me){var a=n.__data__;if(!pr||a.length<199)return a.push([t,e]),this.size=++n.size,this;n=this.__data__=new he(a)}return n.set(t,e),this.size=n.size,this};var xr=ma(Ee),Lr=ma(Fe,!0),Or=ha(),Rr=ha(!0),Ur=vr?function(t,e){return vr.set(t,e),t}:lo,Mr=Ho?function(t,e){return Ho(t,"toString",{configurable:!0,enumerable:!1,value:so(e),writable:!0})}:lo,Nr=$n,$r=Jo||function(t){return en.clearTimeout(t)},Dr=mr&&1/P(new mr([,-0]))[1]==Q?function(t){return new mr(t)}:po,Pr=vr?function(t){return vr.get(t)}:po,Er=tr?function(t){return null==t?[]:(t=wo(t),o(tr(t),(function(e){return Bo.call(t,e)})))}:mo,Fr=tr?function(t){for(var e=[];t;)c(e,Er(t)),t=Wo(t);return e}:mo,jr=Ye;(dr&&jr(new dr(new ArrayBuffer(1)))!=wt||pr&&jr(new pr)!=dt||fr&&jr(fr.resolve())!=mt||mr&&jr(new mr)!=gt||hr&&jr(new hr)!=yt)&&(jr=function(t){var e=Ye(t),n=e==ft?t.constructor:V,a=n?ui(n):"";if(a)switch(a){case yr:return wt;case _r:return dt;case wr:return mt;case Cr:return gt;case Sr:return yt}return e});var Wr=xo?Di:ho,Vr=si(Ur),Br=Qo||function(t,e){return en.setTimeout(t,e)},zr=si(Mr),Yr=function(t){var e=Oi(t,(function(t){return 500===n.size&&n.clear(),t})),n=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(Bt,(function(t,n,a,i){e.push(a?i.replace(Xt,"$1"):n||t)})),e})),Gr=$n((function(t,e){return Ni(t)?Me(t,Pe(e,1,Ni,!0)):[]})),qr=$n((function(t,e){var n=vi(e);return Ni(n)&&(n=V),Ni(t)?Me(t,Pe(e,1,Ni,!0),Ba(n,2)):[]})),Hr=$n((function(t,e){var n=vi(e);return Ni(n)&&(n=V),Ni(t)?Me(t,Pe(e,1,Ni,!0),V,n):[]})),Jr=$n((function(t){var e=l(t,ta);return e.length&&e[0]===t[0]?nn(e):[]})),Kr=$n((function(t){var e=vi(t),n=l(t,ta);return e===vi(n)?e=V:n.pop(),n.length&&n[0]===t[0]?nn(n,Ba(e,2)):[]})),Qr=$n((function(t){var e=vi(t),n=l(t,ta);return(e="function"==typeof e?e:V)&&n.pop(),n.length&&n[0]===t[0]?nn(n,V,e):[]})),Zr=$n(bi),Xr=Ea((function(t,e){var n=null==t?0:t.length,a=xe(t,e);return Un(t,l(e,(function(t){return Ka(t,n)?+t:t})).sort(sa)),a})),ts=$n((function(t){return qn(Pe(t,1,Ni,!0))})),es=$n((function(t){var e=vi(t);return Ni(e)&&(e=V),qn(Pe(t,1,Ni,!0),Ba(e,2))})),ns=$n((function(t){var e=vi(t);return e="function"==typeof e?e:V,qn(Pe(t,1,Ni,!0),V,e)})),as=$n((function(t,e){return Ni(t)?Me(t,e):[]})),is=$n((function(t){return Zn(o(t,Ni))})),os=$n((function(t){var e=vi(t);return Ni(e)&&(e=V),Zn(o(t,Ni),Ba(e,2))})),rs=$n((function(t){var e=vi(t);return e="function"==typeof e?e:V,Zn(o(t,Ni),V,e)})),ss=$n(_i),ls=$n((function(t){var e=t.length,n=e>1?t[e-1]:V;return n="function"==typeof n?(t.pop(),n):V,wi(t,n)})),cs=Ea((function(t){var e=t.length,n=e?t[0]:0,a=this.__wrapped__,i=function(e){return xe(e,t)};return!(e>1||this.__actions__.length)&&a instanceof pe&&Ka(n)?((a=a.slice(n,+n+(e?1:0))).__actions__.push({func:Si,args:[i],thisArg:V}),new de(a,this.__chain__).thru((function(t){return e&&!t.length&&t.push(V),t}))):this.thru(i)})),us=pa((function(t,e,n){Oo.call(t,n)?++t[n]:Te(t,n,1)})),ds=_a(fi),ps=_a(mi),fs=pa((function(t,e,n){Oo.call(t,n)?t[n].push(e):Te(t,n,[e])})),ms=$n((function(e,n,a){var i=-1,o="function"==typeof n,r=Mi(e)?go(e.length):[];return xr(e,(function(e){r[++i]=o?t(n,e,a):an(e,n,a)})),r})),hs=pa((function(t,e,n){Te(t,n,e)})),gs=pa((function(t,e,n){t[n?0:1].push(e)}),(function(){return[[],[]]})),vs=$n((function(t,e){if(null==t)return[];var n=e.length;return n>1&&Qa(t,e[0],e[1])?e=[]:n>2&&Qa(e[0],e[1],e[2])&&(e=[e[0]]),Ln(t,Pe(e,1),[])})),bs=Ko||function(){return en.Date.now()},ys=$n((function(t,e,n){var a=1;if(n.length){var i=D(n,Va(ys));a|=q}return Ma(t,a,e,n,i)})),_s=$n((function(t,e,n){var a=3;if(n.length){var i=D(n,Va(_s));a|=q}return Ma(e,a,t,n,i)})),ws=$n((function(t,e){return Ue(t,1,e)})),Cs=$n((function(t,e,n){return Ue(t,Ji(e)||0,n)}));Oi.Cache=he;var Ss=Nr((function(e,n){var a=(n=1==n.length&&Os(n[0])?l(n[0],A(Ba())):l(Pe(n,1),A(Ba()))).length;return $n((function(i){for(var o=-1,r=rr(i.length,a);++o<r;)i[o]=n[o].call(this,i[o]);return t(e,this,i)}))})),ks=$n((function(t,e){return Ma(t,q,V,e,D(e,Va(ks)))})),Is=$n((function(t,e){return Ma(t,H,V,e,D(e,Va(Is)))})),As=Ea((function(t,e){return Ma(t,K,V,V,V,e)})),Ts=La(Ke),xs=La((function(t,e){return t>=e})),Ls=rn(function(){return arguments}())?rn:function(t){return ji(t)&&Oo.call(t,"callee")&&!Bo.call(t,"callee")},Os=go.isArray,Rs=ln?A(ln):function(t){return ji(t)&&Ye(t)==_t},Us=er||ho,Ms=cn?A(cn):function(t){return ji(t)&&Ye(t)==st},Ns=un?A(un):function(t){return ji(t)&&jr(t)==dt},$s=dn?A(dn):function(t){return ji(t)&&Ye(t)==ht},Ds=pn?A(pn):function(t){return ji(t)&&jr(t)==gt},Ps=fn?A(fn):function(t){return ji(t)&&Ei(t.length)&&!!He[Ye(t)]},Es=La(Sn),Fs=La((function(t,e){return t<=e})),js=fa((function(t,e){if(ti(e)||Mi(e))return da(e,to(e),t),V;for(var n in e)Oo.call(e,n)&&Se(t,n,e[n])})),Ws=fa((function(t,e){da(e,eo(e),t)})),Vs=fa((function(t,e,n,a){da(e,eo(e),t,a)})),Bs=fa((function(t,e,n,a){da(e,to(e),t,a)})),zs=Ea(xe),Ys=$n((function(t,e){t=wo(t);var n=-1,a=e.length,i=a>2?e[2]:V;for(i&&Qa(e[0],e[1],i)&&(a=1);++n<a;)for(var o=e[n],r=eo(o),s=-1,l=r.length;++s<l;){var c=r[s],u=t[c];(u===V||Ui(u,To[c])&&!Oo.call(t,c))&&(t[c]=o[c])}return t})),Gs=$n((function(e){return e.push(V,$a),t(Qs,V,e)})),qs=Sa((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=Mo.call(e)),t[e]=n}),so(lo)),Hs=Sa((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=Mo.call(e)),Oo.call(t,e)?t[e].push(n):t[e]=[n]}),Ba),Js=$n(an),Ks=fa((function(t,e,n){Tn(t,e,n)})),Qs=fa((function(t,e,n,a){Tn(t,e,n,a)})),Zs=Ea((function(t,e){var n={};if(null==t)return n;var a=!1;e=l(e,(function(e){return e=na(e,t),a||(a=e.length>1),e})),da(t,ja(t),n),a&&(n=Oe(n,7,Da));for(var i=e.length;i--;)Hn(n,e[i]);return n})),Xs=Ea((function(t,e){return null==t?{}:function(t,e){return On(t,e,(function(e,n){return Xi(t,n)}))}(t,e)})),tl=Ua(to),el=Ua(eo),nl=va((function(t,e,n){return e=e.toLowerCase(),t+(n?io(e):e)})),al=va((function(t,e,n){return t+(n?"-":"")+e.toLowerCase()})),il=va((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),ol=ga("toLowerCase"),rl=va((function(t,e,n){return t+(n?"_":"")+e.toLowerCase()})),sl=va((function(t,e,n){return t+(n?" ":"")+cl(e)})),ll=va((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()})),cl=ga("toUpperCase"),ul=$n((function(e,n){try{return t(e,V,n)}catch(t){return $i(t)?t:new bo(t)}})),dl=Ea((function(t,e){return n(e,(function(e){e=ci(e),Te(t,e,ys(t[e],t))})),t})),pl=wa(),fl=wa(!0),ml=$n((function(t,e){return function(n){return an(n,t,e)}})),hl=$n((function(t,e){return function(n){return an(t,n,e)}})),gl=Ia(l),vl=Ia(i),bl=Ia(p),yl=xa(),_l=xa(!0),wl=ka((function(t,e){return t+e}),0),Cl=Ra("ceil"),Sl=ka((function(t,e){return t/e}),1),kl=Ra("floor"),Il=ka((function(t,e){return t*e}),1),Al=Ra("round"),Tl=ka((function(t,e){return t-e}),0);return Qt.after=function(t,e){if("function"!=typeof e)throw new ko(B);return t=qi(t),function(){if(--t<1)return e.apply(this,arguments)}},Qt.ary=Ti,Qt.assign=js,Qt.assignIn=Ws,Qt.assignInWith=Vs,Qt.assignWith=Bs,Qt.at=zs,Qt.before=xi,Qt.bind=ys,Qt.bindAll=dl,Qt.bindKey=_s,Qt.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return Os(t)?t:[t]},Qt.chain=Ci,Qt.chunk=function(t,e,n){e=(n?Qa(t,e,n):e===V)?1:or(qi(e),0);var a=null==t?0:t.length;if(!a||e<1)return[];for(var i=0,o=0,r=go(Zo(a/e));i<a;)r[o++]=jn(t,i,i+=e);return r},Qt.compact=function(t){for(var e=-1,n=null==t?0:t.length,a=0,i=[];++e<n;){var o=t[e];o&&(i[a++]=o)}return i},Qt.concat=function(){var t=arguments.length;if(!t)return[];for(var e=go(t-1),n=arguments[0],a=t;a--;)e[a-1]=arguments[a];return c(Os(n)?ua(n):[n],Pe(e,1))},Qt.cond=function(e){var n=null==e?0:e.length,a=Ba();return e=n?l(e,(function(t){if("function"!=typeof t[1])throw new ko(B);return[a(t[0]),t[1]]})):[],$n((function(a){for(var i=-1;++i<n;){var o=e[i];if(t(o[0],this,a))return t(o[1],this,a)}}))},Qt.conforms=function(t){return function(t){var e=to(t);return function(n){return Re(n,t,e)}}(Oe(t,1))},Qt.constant=so,Qt.countBy=us,Qt.create=function(t,e){var n=Tr(t);return null==e?n:Ae(n,e)},Qt.curry=function t(e,n,a){var i=Ma(e,8,V,V,V,V,V,n=a?V:n);return i.placeholder=t.placeholder,i},Qt.curryRight=function t(e,n,a){var i=Ma(e,G,V,V,V,V,V,n=a?V:n);return i.placeholder=t.placeholder,i},Qt.debounce=Li,Qt.defaults=Ys,Qt.defaultsDeep=Gs,Qt.defer=ws,Qt.delay=Cs,Qt.difference=Gr,Qt.differenceBy=qr,Qt.differenceWith=Hr,Qt.drop=function(t,e,n){var a=null==t?0:t.length;return a?jn(t,(e=n||e===V?1:qi(e))<0?0:e,a):[]},Qt.dropRight=function(t,e,n){var a=null==t?0:t.length;return a?jn(t,0,(e=a-(e=n||e===V?1:qi(e)))<0?0:e):[]},Qt.dropRightWhile=function(t,e){return t&&t.length?Kn(t,Ba(e,3),!0,!0):[]},Qt.dropWhile=function(t,e){return t&&t.length?Kn(t,Ba(e,3),!0):[]},Qt.fill=function(t,e,n,a){var i=null==t?0:t.length;return i?(n&&"number"!=typeof n&&Qa(t,e,n)&&(n=0,a=i),function(t,e,n,a){var i=t.length;for((n=qi(n))<0&&(n=-n>i?0:i+n),(a=a===V||a>i?i:qi(a))<0&&(a+=i),a=n>a?0:Hi(a);n<a;)t[n++]=e;return t}(t,e,n,a)):[]},Qt.filter=function(t,e){return(Os(t)?o:De)(t,Ba(e,3))},Qt.flatMap=function(t,e){return Pe(Ai(t,e),1)},Qt.flatMapDeep=function(t,e){return Pe(Ai(t,e),Q)},Qt.flatMapDepth=function(t,e,n){return n=n===V?1:qi(n),Pe(Ai(t,e),n)},Qt.flatten=hi,Qt.flattenDeep=function(t){return null!=t&&t.length?Pe(t,Q):[]},Qt.flattenDepth=function(t,e){return null!=t&&t.length?Pe(t,e=e===V?1:qi(e)):[]},Qt.flip=function(t){return Ma(t,512)},Qt.flow=pl,Qt.flowRight=fl,Qt.fromPairs=function(t){for(var e=-1,n=null==t?0:t.length,a={};++e<n;){var i=t[e];a[i[0]]=i[1]}return a},Qt.functions=function(t){return null==t?[]:Ve(t,to(t))},Qt.functionsIn=function(t){return null==t?[]:Ve(t,eo(t))},Qt.groupBy=fs,Qt.initial=function(t){return null!=t&&t.length?jn(t,0,-1):[]},Qt.intersection=Jr,Qt.intersectionBy=Kr,Qt.intersectionWith=Qr,Qt.invert=qs,Qt.invertBy=Hs,Qt.invokeMap=ms,Qt.iteratee=co,Qt.keyBy=hs,Qt.keys=to,Qt.keysIn=eo,Qt.map=Ai,Qt.mapKeys=function(t,e){var n={};return e=Ba(e,3),Ee(t,(function(t,a,i){Te(n,e(t,a,i),t)})),n},Qt.mapValues=function(t,e){var n={};return e=Ba(e,3),Ee(t,(function(t,a,i){Te(n,a,e(t,a,i))})),n},Qt.matches=function(t){return In(Oe(t,1))},Qt.matchesProperty=function(t,e){return An(t,Oe(e,1))},Qt.memoize=Oi,Qt.merge=Ks,Qt.mergeWith=Qs,Qt.method=ml,Qt.methodOf=hl,Qt.mixin=uo,Qt.negate=Ri,Qt.nthArg=function(t){return t=qi(t),$n((function(e){return xn(e,t)}))},Qt.omit=Zs,Qt.omitBy=function(t,e){return no(t,Ri(Ba(e)))},Qt.once=function(t){return xi(2,t)},Qt.orderBy=function(t,e,n,a){return null==t?[]:(Os(e)||(e=null==e?[]:[e]),Os(n=a?V:n)||(n=null==n?[]:[n]),Ln(t,e,n))},Qt.over=gl,Qt.overArgs=Ss,Qt.overEvery=vl,Qt.overSome=bl,Qt.partial=ks,Qt.partialRight=Is,Qt.partition=gs,Qt.pick=Xs,Qt.pickBy=no,Qt.property=fo,Qt.propertyOf=function(t){return function(e){return null==t?V:Be(t,e)}},Qt.pull=Zr,Qt.pullAll=bi,Qt.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?Rn(t,e,Ba(n,2)):t},Qt.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?Rn(t,e,V,n):t},Qt.pullAt=Xr,Qt.range=yl,Qt.rangeRight=_l,Qt.rearg=As,Qt.reject=function(t,e){return(Os(t)?o:De)(t,Ri(Ba(e,3)))},Qt.remove=function(t,e){var n=[];if(!t||!t.length)return n;var a=-1,i=[],o=t.length;for(e=Ba(e,3);++a<o;){var r=t[a];e(r,a,t)&&(n.push(r),i.push(a))}return Un(t,i),n},Qt.rest=function(t,e){if("function"!=typeof t)throw new ko(B);return $n(t,e=e===V?e:qi(e))},Qt.reverse=yi,Qt.sampleSize=function(t,e,n){return e=(n?Qa(t,e,n):e===V)?1:qi(e),(Os(t)?_e:Pn)(t,e)},Qt.set=function(t,e,n){return null==t?t:En(t,e,n)},Qt.setWith=function(t,e,n,a){return a="function"==typeof a?a:V,null==t?t:En(t,e,n,a)},Qt.shuffle=function(t){return(Os(t)?we:Fn)(t)},Qt.slice=function(t,e,n){var a=null==t?0:t.length;return a?(n&&"number"!=typeof n&&Qa(t,e,n)?(e=0,n=a):(e=null==e?0:qi(e),n=n===V?a:qi(n)),jn(t,e,n)):[]},Qt.sortBy=vs,Qt.sortedUniq=function(t){return t&&t.length?zn(t):[]},Qt.sortedUniqBy=function(t,e){return t&&t.length?zn(t,Ba(e,2)):[]},Qt.split=function(t,e,n){return n&&"number"!=typeof n&&Qa(t,e,n)&&(e=n=V),(n=n===V?tt:n>>>0)?(t=Qi(t))&&("string"==typeof e||null!=e&&!$s(e))&&(!(e=Gn(e))&&U(t))?aa(F(t),0,n):t.split(e,n):[]},Qt.spread=function(e,n){if("function"!=typeof e)throw new ko(B);return n=null==n?0:or(qi(n),0),$n((function(a){var i=a[n],o=aa(a,0,n);return i&&c(o,i),t(e,this,o)}))},Qt.tail=function(t){var e=null==t?0:t.length;return e?jn(t,1,e):[]},Qt.take=function(t,e,n){return t&&t.length?jn(t,0,(e=n||e===V?1:qi(e))<0?0:e):[]},Qt.takeRight=function(t,e,n){var a=null==t?0:t.length;return a?jn(t,(e=a-(e=n||e===V?1:qi(e)))<0?0:e,a):[]},Qt.takeRightWhile=function(t,e){return t&&t.length?Kn(t,Ba(e,3),!1,!0):[]},Qt.takeWhile=function(t,e){return t&&t.length?Kn(t,Ba(e,3)):[]},Qt.tap=function(t,e){return e(t),t},Qt.throttle=function(t,e,n){var a=!0,i=!0;if("function"!=typeof t)throw new ko(B);return Fi(n)&&(a="leading"in n?!!n.leading:a,i="trailing"in n?!!n.trailing:i),Li(t,e,{leading:a,maxWait:e,trailing:i})},Qt.thru=Si,Qt.toArray=Yi,Qt.toPairs=tl,Qt.toPairsIn=el,Qt.toPath=function(t){return Os(t)?l(t,ci):zi(t)?[t]:ua(Yr(Qi(t)))},Qt.toPlainObject=Ki,Qt.transform=function(t,e,a){var i=Os(t),o=i||Us(t)||Ps(t);if(e=Ba(e,4),null==a){var r=t&&t.constructor;a=o?i?new r:[]:Fi(t)&&Di(r)?Tr(Wo(t)):{}}return(o?n:Ee)(t,(function(t,n,i){return e(a,t,n,i)})),a},Qt.unary=function(t){return Ti(t,1)},Qt.union=ts,Qt.unionBy=es,Qt.unionWith=ns,Qt.uniq=function(t){return t&&t.length?qn(t):[]},Qt.uniqBy=function(t,e){return t&&t.length?qn(t,Ba(e,2)):[]},Qt.uniqWith=function(t,e){return e="function"==typeof e?e:V,t&&t.length?qn(t,V,e):[]},Qt.unset=function(t,e){return null==t||Hn(t,e)},Qt.unzip=_i,Qt.unzipWith=wi,Qt.update=function(t,e,n){return null==t?t:Jn(t,e,ea(n))},Qt.updateWith=function(t,e,n,a){return a="function"==typeof a?a:V,null==t?t:Jn(t,e,ea(n),a)},Qt.values=ao,Qt.valuesIn=function(t){return null==t?[]:T(t,eo(t))},Qt.without=as,Qt.words=ro,Qt.wrap=function(t,e){return ks(ea(e),t)},Qt.xor=is,Qt.xorBy=os,Qt.xorWith=rs,Qt.zip=ss,Qt.zipObject=function(t,e){return Xn(t||[],e||[],Se)},Qt.zipObjectDeep=function(t,e){return Xn(t||[],e||[],En)},Qt.zipWith=ls,Qt.entries=tl,Qt.entriesIn=el,Qt.extend=Ws,Qt.extendWith=Vs,uo(Qt,Qt),Qt.add=wl,Qt.attempt=ul,Qt.camelCase=nl,Qt.capitalize=io,Qt.ceil=Cl,Qt.clamp=function(t,e,n){return n===V&&(n=e,e=V),n!==V&&(n=(n=Ji(n))==n?n:0),e!==V&&(e=(e=Ji(e))==e?e:0),Le(Ji(t),e,n)},Qt.clone=function(t){return Oe(t,4)},Qt.cloneDeep=function(t){return Oe(t,5)},Qt.cloneDeepWith=function(t,e){return Oe(t,5,e="function"==typeof e?e:V)},Qt.cloneWith=function(t,e){return Oe(t,4,e="function"==typeof e?e:V)},Qt.conformsTo=function(t,e){return null==e||Re(t,e,to(e))},Qt.deburr=oo,Qt.defaultTo=function(t,e){return null==t||t!=t?e:t},Qt.divide=Sl,Qt.endsWith=function(t,e,n){t=Qi(t),e=Gn(e);var a=t.length,i=n=n===V?a:Le(qi(n),0,a);return(n-=e.length)>=0&&t.slice(n,i)==e},Qt.eq=Ui,Qt.escape=function(t){return(t=Qi(t))&&Pt.test(t)?t.replace($t,gn):t},Qt.escapeRegExp=function(t){return(t=Qi(t))&&Yt.test(t)?t.replace(zt,"\\$&"):t},Qt.every=function(t,e,n){var a=Os(t)?i:Ne;return n&&Qa(t,e,n)&&(e=V),a(t,Ba(e,3))},Qt.find=ds,Qt.findIndex=fi,Qt.findKey=function(t,e){return m(t,Ba(e,3),Ee)},Qt.findLast=ps,Qt.findLastIndex=mi,Qt.findLastKey=function(t,e){return m(t,Ba(e,3),Fe)},Qt.floor=kl,Qt.forEach=ki,Qt.forEachRight=Ii,Qt.forIn=function(t,e){return null==t?t:Or(t,Ba(e,3),eo)},Qt.forInRight=function(t,e){return null==t?t:Rr(t,Ba(e,3),eo)},Qt.forOwn=function(t,e){return t&&Ee(t,Ba(e,3))},Qt.forOwnRight=function(t,e){return t&&Fe(t,Ba(e,3))},Qt.get=Zi,Qt.gt=Ts,Qt.gte=xs,Qt.has=function(t,e){return null!=t&&qa(t,e,Xe)},Qt.hasIn=Xi,Qt.head=gi,Qt.identity=lo,Qt.includes=function(t,e,n,a){t=Mi(t)?t:ao(t),n=n&&!a?qi(n):0;var i=t.length;return n<0&&(n=or(i+n,0)),Bi(t)?n<=i&&t.indexOf(e,n)>-1:!!i&&g(t,e,n)>-1},Qt.indexOf=function(t,e,n){var a=null==t?0:t.length;if(!a)return-1;var i=null==n?0:qi(n);return i<0&&(i=or(a+i,0)),g(t,e,i)},Qt.inRange=function(t,e,n){return e=Gi(e),n===V?(n=e,e=0):n=Gi(n),function(t,e,n){return t>=rr(e,n)&&t<or(e,n)}(t=Ji(t),e,n)},Qt.invoke=Js,Qt.isArguments=Ls,Qt.isArray=Os,Qt.isArrayBuffer=Rs,Qt.isArrayLike=Mi,Qt.isArrayLikeObject=Ni,Qt.isBoolean=function(t){return!0===t||!1===t||ji(t)&&Ye(t)==rt},Qt.isBuffer=Us,Qt.isDate=Ms,Qt.isElement=function(t){return ji(t)&&1===t.nodeType&&!Vi(t)},Qt.isEmpty=function(t){if(null==t)return!0;if(Mi(t)&&(Os(t)||"string"==typeof t||"function"==typeof t.splice||Us(t)||Ps(t)||Ls(t)))return!t.length;var e=jr(t);if(e==dt||e==gt)return!t.size;if(ti(t))return!wn(t).length;for(var n in t)if(Oo.call(t,n))return!1;return!0},Qt.isEqual=function(t,e){return sn(t,e)},Qt.isEqualWith=function(t,e,n){var a=(n="function"==typeof n?n:V)?n(t,e):V;return a===V?sn(t,e,V,n):!!a},Qt.isError=$i,Qt.isFinite=function(t){return"number"==typeof t&&nr(t)},Qt.isFunction=Di,Qt.isInteger=Pi,Qt.isLength=Ei,Qt.isMap=Ns,Qt.isMatch=function(t,e){return t===e||mn(t,e,Ya(e))},Qt.isMatchWith=function(t,e,n){return n="function"==typeof n?n:V,mn(t,e,Ya(e),n)},Qt.isNaN=function(t){return Wi(t)&&t!=+t},Qt.isNative=function(t){if(Wr(t))throw new bo("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return yn(t)},Qt.isNil=function(t){return null==t},Qt.isNull=function(t){return null===t},Qt.isNumber=Wi,Qt.isObject=Fi,Qt.isObjectLike=ji,Qt.isPlainObject=Vi,Qt.isRegExp=$s,Qt.isSafeInteger=function(t){return Pi(t)&&t>=-Z&&t<=Z},Qt.isSet=Ds,Qt.isString=Bi,Qt.isSymbol=zi,Qt.isTypedArray=Ps,Qt.isUndefined=function(t){return t===V},Qt.isWeakMap=function(t){return ji(t)&&jr(t)==yt},Qt.isWeakSet=function(t){return ji(t)&&"[object WeakSet]"==Ye(t)},Qt.join=function(t,e){return null==t?"":ar.call(t,e)},Qt.kebabCase=al,Qt.last=vi,Qt.lastIndexOf=function(t,e,n){var a=null==t?0:t.length;if(!a)return-1;var i=a;return n!==V&&(i=(i=qi(n))<0?or(a+i,0):rr(i,a-1)),e==e?function(t,e,n){for(var a=n+1;a--;)if(t[a]===e)return a;return a}(t,e,i):h(t,b,i,!0)},Qt.lowerCase=il,Qt.lowerFirst=ol,Qt.lt=Es,Qt.lte=Fs,Qt.max=function(t){return t&&t.length?$e(t,lo,Ke):V},Qt.maxBy=function(t,e){return t&&t.length?$e(t,Ba(e,2),Ke):V},Qt.mean=function(t){return y(t,lo)},Qt.meanBy=function(t,e){return y(t,Ba(e,2))},Qt.min=function(t){return t&&t.length?$e(t,lo,Sn):V},Qt.minBy=function(t,e){return t&&t.length?$e(t,Ba(e,2),Sn):V},Qt.stubArray=mo,Qt.stubFalse=ho,Qt.stubObject=function(){return{}},Qt.stubString=function(){return""},Qt.stubTrue=function(){return!0},Qt.multiply=Il,Qt.nth=function(t,e){return t&&t.length?xn(t,qi(e)):V},Qt.noConflict=function(){return en._===this&&(en._=$o),this},Qt.noop=po,Qt.now=bs,Qt.pad=function(t,e,n){t=Qi(t);var a=(e=qi(e))?E(t):0;if(!e||a>=e)return t;var i=(e-a)/2;return Aa(Xo(i),n)+t+Aa(Zo(i),n)},Qt.padEnd=function(t,e,n){t=Qi(t);var a=(e=qi(e))?E(t):0;return e&&a<e?t+Aa(e-a,n):t},Qt.padStart=function(t,e,n){t=Qi(t);var a=(e=qi(e))?E(t):0;return e&&a<e?Aa(e-a,n)+t:t},Qt.parseInt=function(t,e,n){return n||null==e?e=0:e&&(e=+e),lr(Qi(t).replace(Gt,""),e||0)},Qt.random=function(t,e,n){if(n&&"boolean"!=typeof n&&Qa(t,e,n)&&(e=n=V),n===V&&("boolean"==typeof e?(n=e,e=V):"boolean"==typeof t&&(n=t,t=V)),t===V&&e===V?(t=0,e=1):(t=Gi(t),e===V?(e=t,t=0):e=Gi(e)),t>e){var a=t;t=e,e=a}if(n||t%1||e%1){var i=cr();return rr(t+i*(e-t+Qe("1e-"+((i+"").length-1))),e)}return Mn(t,e)},Qt.reduce=function(t,e,n){var a=Os(t)?u:C,i=arguments.length<3;return a(t,Ba(e,4),n,i,xr)},Qt.reduceRight=function(t,e,n){var a=Os(t)?d:C,i=arguments.length<3;return a(t,Ba(e,4),n,i,Lr)},Qt.repeat=function(t,e,n){return e=(n?Qa(t,e,n):e===V)?1:qi(e),Nn(Qi(t),e)},Qt.replace=function(){var t=arguments,e=Qi(t[0]);return t.length<3?e:e.replace(t[1],t[2])},Qt.result=function(t,e,n){var a=-1,i=(e=na(e,t)).length;for(i||(i=1,t=V);++a<i;){var o=null==t?V:t[ci(e[a])];o===V&&(a=i,o=n),t=Di(o)?o.call(t):o}return t},Qt.round=Al,Qt.runInContext=w,Qt.sample=function(t){return(Os(t)?ye:Dn)(t)},Qt.size=function(t){if(null==t)return 0;if(Mi(t))return Bi(t)?E(t):t.length;var e=jr(t);return e==dt||e==gt?t.size:wn(t).length},Qt.snakeCase=rl,Qt.some=function(t,e,n){var a=Os(t)?p:Wn;return n&&Qa(t,e,n)&&(e=V),a(t,Ba(e,3))},Qt.sortedIndex=function(t,e){return Vn(t,e)},Qt.sortedIndexBy=function(t,e,n){return Bn(t,e,Ba(n,2))},Qt.sortedIndexOf=function(t,e){var n=null==t?0:t.length;if(n){var a=Vn(t,e);if(a<n&&Ui(t[a],e))return a}return-1},Qt.sortedLastIndex=function(t,e){return Vn(t,e,!0)},Qt.sortedLastIndexBy=function(t,e,n){return Bn(t,e,Ba(n,2),!0)},Qt.sortedLastIndexOf=function(t,e){if(null!=t&&t.length){var n=Vn(t,e,!0)-1;if(Ui(t[n],e))return n}return-1},Qt.startCase=sl,Qt.startsWith=function(t,e,n){return t=Qi(t),n=null==n?0:Le(qi(n),0,t.length),e=Gn(e),t.slice(n,n+e.length)==e},Qt.subtract=Tl,Qt.sum=function(t){return t&&t.length?S(t,lo):0},Qt.sumBy=function(t,e){return t&&t.length?S(t,Ba(e,2)):0},Qt.template=function(t,e,n){var a=Qt.templateSettings;n&&Qa(t,e,n)&&(e=V),t=Qi(t),e=Vs({},e,a,Na);var i,o,r=Vs({},e.imports,a.imports,Na),s=to(r),l=T(r,s),c=0,u=e.interpolate||le,d="__p += '",p=Co((e.escape||le).source+"|"+u.source+"|"+(u===jt?te:le).source+"|"+(e.evaluate||le).source+"|$","g"),f="//# sourceURL="+(Oo.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++qe+"]")+"\n";t.replace(p,(function(e,n,a,r,s,l){return a||(a=r),d+=t.slice(c,l).replace(ce,R),n&&(i=!0,d+="' +\n__e("+n+") +\n'"),s&&(o=!0,d+="';\n"+s+";\n__p += '"),a&&(d+="' +\n((__t = ("+a+")) == null ? '' : __t) +\n'"),c=l+e.length,e})),d+="';\n";var m=Oo.call(e,"variable")&&e.variable;if(m){if(Zt.test(m))throw new bo("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(o?d.replace(Rt,""):d).replace(Ut,"$1").replace(Mt,"$1;"),d="function("+(m||"obj")+") {\n"+(m?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var h=ul((function(){return yo(s,f+"return "+d).apply(V,l)}));if(h.source=d,$i(h))throw h;return h},Qt.times=function(t,e){if((t=qi(t))<1||t>Z)return[];var n=tt,a=rr(t,tt);e=Ba(e),t-=tt;for(var i=k(a,e);++n<t;)e(n);return i},Qt.toFinite=Gi,Qt.toInteger=qi,Qt.toLength=Hi,Qt.toLower=function(t){return Qi(t).toLowerCase()},Qt.toNumber=Ji,Qt.toSafeInteger=function(t){return t?Le(qi(t),-Z,Z):0===t?t:0},Qt.toString=Qi,Qt.toUpper=function(t){return Qi(t).toUpperCase()},Qt.trim=function(t,e,n){if((t=Qi(t))&&(n||e===V))return I(t);if(!t||!(e=Gn(e)))return t;var a=F(t),i=F(e);return aa(a,L(a,i),O(a,i)+1).join("")},Qt.trimEnd=function(t,e,n){if((t=Qi(t))&&(n||e===V))return t.slice(0,j(t)+1);if(!t||!(e=Gn(e)))return t;var a=F(t);return aa(a,0,O(a,F(e))+1).join("")},Qt.trimStart=function(t,e,n){if((t=Qi(t))&&(n||e===V))return t.replace(Gt,"");if(!t||!(e=Gn(e)))return t;var a=F(t);return aa(a,L(a,F(e))).join("")},Qt.truncate=function(t,e){var n=30,a="...";if(Fi(e)){var i="separator"in e?e.separator:i;n="length"in e?qi(e.length):n,a="omission"in e?Gn(e.omission):a}var o=(t=Qi(t)).length;if(U(t)){var r=F(t);o=r.length}if(n>=o)return t;var s=n-E(a);if(s<1)return a;var l=r?aa(r,0,s).join(""):t.slice(0,s);if(i===V)return l+a;if(r&&(s+=l.length-s),$s(i)){if(t.slice(s).search(i)){var c,u=l;for(i.global||(i=Co(i.source,Qi(ee.exec(i))+"g")),i.lastIndex=0;c=i.exec(u);)var d=c.index;l=l.slice(0,d===V?s:d)}}else if(t.indexOf(Gn(i),s)!=s){var p=l.lastIndexOf(i);p>-1&&(l=l.slice(0,p))}return l+a},Qt.unescape=function(t){return(t=Qi(t))&&Dt.test(t)?t.replace(Nt,vn):t},Qt.uniqueId=function(t){var e=++Ro;return Qi(t)+e},Qt.upperCase=ll,Qt.upperFirst=cl,Qt.each=ki,Qt.eachRight=Ii,Qt.first=gi,uo(Qt,function(){var t={};return Ee(Qt,(function(e,n){Oo.call(Qt.prototype,n)||(t[n]=e)})),t}(),{chain:!1}),Qt.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){Qt[t].placeholder=Qt})),n(["drop","take"],(function(t,e){pe.prototype[t]=function(n){n=n===V?1:or(qi(n),0);var a=this.__filtered__&&!e?new pe(this):this.clone();return a.__filtered__?a.__takeCount__=rr(n,a.__takeCount__):a.__views__.push({size:rr(n,tt),type:t+(a.__dir__<0?"Right":"")}),a},pe.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),n(["filter","map","takeWhile"],(function(t,e){var n=e+1,a=1==n||3==n;pe.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:Ba(t,3),type:n}),e.__filtered__=e.__filtered__||a,e}})),n(["head","last"],(function(t,e){var n="take"+(e?"Right":"");pe.prototype[t]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(t,e){var n="drop"+(e?"":"Right");pe.prototype[t]=function(){return this.__filtered__?new pe(this):this[n](1)}})),pe.prototype.compact=function(){return this.filter(lo)},pe.prototype.find=function(t){return this.filter(t).head()},pe.prototype.findLast=function(t){return this.reverse().find(t)},pe.prototype.invokeMap=$n((function(t,e){return"function"==typeof t?new pe(this):this.map((function(n){return an(n,t,e)}))})),pe.prototype.reject=function(t){return this.filter(Ri(Ba(t)))},pe.prototype.slice=function(t,e){t=qi(t);var n=this;return n.__filtered__&&(t>0||e<0)?new pe(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),e!==V&&(n=(e=qi(e))<0?n.dropRight(-e):n.take(e-t)),n)},pe.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},pe.prototype.toArray=function(){return this.take(tt)},Ee(pe.prototype,(function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),a=/^(?:head|last)$/.test(e),i=Qt[a?"take"+("last"==e?"Right":""):e],o=a||/^find/.test(e);i&&(Qt.prototype[e]=function(){var e=this.__wrapped__,r=a?[1]:arguments,s=e instanceof pe,l=r[0],u=s||Os(e),d=function(t){var e=i.apply(Qt,c([t],r));return a&&p?e[0]:e};u&&n&&"function"==typeof l&&1!=l.length&&(s=u=!1);var p=this.__chain__,f=!!this.__actions__.length,m=o&&!p,h=s&&!f;if(!o&&u){e=h?e:new pe(this);var g=t.apply(e,r);return g.__actions__.push({func:Si,args:[d],thisArg:V}),new de(g,p)}return m&&h?t.apply(this,r):(g=this.thru(d),m?a?g.value()[0]:g.value():g)})})),n(["pop","push","shift","sort","splice","unshift"],(function(t){var e=Io[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",a=/^(?:pop|shift)$/.test(t);Qt.prototype[t]=function(){var t=arguments;if(a&&!this.__chain__){var i=this.value();return e.apply(Os(i)?i:[],t)}return this[n]((function(n){return e.apply(Os(n)?n:[],t)}))}})),Ee(pe.prototype,(function(t,e){var n=Qt[e];if(n){var a=n.name+"";Oo.call(br,a)||(br[a]=[]),br[a].push({name:e,func:n})}})),br[Ca(V,2).name]=[{name:"wrapper",func:V}],pe.prototype.clone=function(){var t=new pe(this.__wrapped__);return t.__actions__=ua(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=ua(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=ua(this.__views__),t},pe.prototype.reverse=function(){if(this.__filtered__){var t=new pe(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},pe.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=Os(t),a=e<0,i=n?t.length:0,o=function(t,e,n){for(var a=-1,i=n.length;++a<i;){var o=n[a],r=o.size;switch(o.type){case"drop":t+=r;break;case"dropRight":e-=r;break;case"take":e=rr(e,t+r);break;case"takeRight":t=or(t,e-r)}}return{start:t,end:e}}(0,i,this.__views__),r=o.start,s=o.end,l=s-r,c=a?s:r-1,u=this.__iteratees__,d=u.length,p=0,f=rr(l,this.__takeCount__);if(!n||!a&&i==l&&f==l)return Qn(t,this.__actions__);var m=[];t:for(;l--&&p<f;){for(var h=-1,g=t[c+=e];++h<d;){var v=u[h],b=v.iteratee,y=v.type,_=b(g);if(2==y)g=_;else if(!_){if(1==y)continue t;break t}}m[p++]=g}return m},Qt.prototype.at=cs,Qt.prototype.chain=function(){return Ci(this)},Qt.prototype.commit=function(){return new de(this.value(),this.__chain__)},Qt.prototype.next=function(){this.__values__===V&&(this.__values__=Yi(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?V:this.__values__[this.__index__++]}},Qt.prototype.plant=function(t){for(var e,n=this;n instanceof ue;){var a=pi(n);a.__index__=0,a.__values__=V,e?i.__wrapped__=a:e=a;var i=a;n=n.__wrapped__}return i.__wrapped__=t,e},Qt.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof pe){var e=t;return this.__actions__.length&&(e=new pe(this)),(e=e.reverse()).__actions__.push({func:Si,args:[yi],thisArg:V}),new de(e,this.__chain__)}return this.thru(yi)},Qt.prototype.toJSON=Qt.prototype.valueOf=Qt.prototype.value=function(){return Qn(this.__wrapped__,this.__actions__)},Qt.prototype.first=Qt.prototype.head,Go&&(Qt.prototype[Go]=function(){return this}),Qt}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(en._=bn,define((function(){return bn}))):an?((an.exports=bn)._=bn,nn._=bn):en._=bn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid">\n                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(t){this.$emit("onUserInfo",t)},onMini(t){this.isMiniSidebar=t}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(t,e){t!==e&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(t,e){"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(t){this.updateActiveOrg(t.id)},orgUpdated(t){const e=t?.response?.data;201===e?.code?(sessionStorage.clear(),window.location.reload(!0)):e?.message&&console.log(e.message)},updateActiveOrg(t){this.$buefy.loading.open();const e={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:t},callback:!0,callbackFunc:t=>this.orgUpdated(t)};this.dispatchData("postData",e)},dispatchData(t,e){this.$store.dispatch(t,e)},init(){if("org-admin"===this.userInfo.data.role){const t=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=t}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t}}});const YUNOTable=(jQuery,{table:function(){Vue.component("yuno-table",{props:["data","options","tabindex"],template:'\n                <div class="yunoTable" :class="{\'container-fluid\': options.isFluid, \'container\': options.isFluid === undefined || !options.isFluid}">\n                    <template v-if="data.loading">\n                        <b-skeleton height="500px"></b-skeleton>\n                    </template>\n                    <template v-if="data.success">\n                        <ul class="nestedFilters" v-if="data.nestedTabs !== undefined">\n                            <li \n                                v-for="(nestedTab, nestedTabIndex) in data.nestedTabs"\n                                :key="nestedTabIndex"\n                                :class="{\'active\': nestedTab.isActive}">\n                                <a href="#" @click="manageNestedTabs($event, nestedTabIndex, data, nestedTab, tabindex)">{{nestedTab.label}}</a>\n                            </li>\n                        </ul>\n                        <div class="field clearFilters" v-if="data.appliedFilters !== undefined && data.appliedFilters.length !== 0">\n                            <b-tag\n                                v-for="(applied, appliedIndex) in data.appliedFilters"\n                                :key="appliedIndex"\n                                attached\n                                closable\n                                aria-close-label="Close tag"\n                                @close="onFilterClear(applied, tabindex)">\n                                <span v-html="applied.label"></span>\n                            </b-tag>\n                        </div>\n                        <div class="filterWrap" v-if="data.filters !== undefined">\n                            <template v-for="(filter, filterIndex) in data.filters">\n                                <template v-if="filter.isActive && filter.module === \'dropdown\'">\n                                    <b-dropdown\n                                        :key="filterIndex"\n                                        v-model="filter.selected"\n                                        :multiple="filter.canSelectMulti === undefined ? false : filter.canSelectMulti"\n                                        aria-role="list"\n                                        @change="onFilterChange($event, filter.type)"\n                                        class="filterMenu"\n                                        :class="filter.type === \'tableColumn\' ? \'selectColumns\' : \'\'">\n                                        <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n                                            <template v-if="filter.type === \'tableColumn\'">\n                                                <span><i class="fa fa-cog" aria-hidden="true"></i></span>\n                                            </template>\n                                            <template v-else>\n                                                <span>{{filter.selected}}</span>\n                                            </template>\n                                            <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                        </button>\n                                        <b-dropdown-item v-if="filter.type === \'tableColumn\'" class="dropdownTitle">\n                                            Hide Columns\n                                        </b-dropdown-item>\n                                        <template v-for="(item, itemIndex) in filter.items">\n                                            <b-dropdown-item \n                                                @click="onFilterItemSelect(item.val, filter.type, item.label, item.default, tabindex)"\n                                                :value="filter.canSelectMulti === undefined ? item.label : item.val"\n                                                :key="itemIndex"\n                                                aria-role="listitem">\n                                                <span>{{item.label}}</span>\n                                            </b-dropdown-item>\n                                        </template>\n                                    </b-dropdown>\n                                </template>\n                                <template v-if="filter.isActive && filter.module === \'autocomplete\'">\n                                    <b-field :class="\'filter\'+filter.type">\n                                        <b-autocomplete\n                                            v-model="filter.selected"\n                                            :data="filteredAutocomplete(filter, \'search\')"\n                                            :placeholder="filter.placeholder"\n                                            field="search"\n                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"\n                                            :clearable="true">\n                                            <template slot="empty">No results for {{filter.selected}}</template>\n                                            <template slot-scope="props">\n                                                <template v-if="filter.type === \'course\'">\n                                                    <div class="courseList">\n                                                        <figure class="img">\n                                                            <img v-if="props.option.image !== \'\'" width="40" height="40" :src="props.option.image" :alt="props.option.label">\n                                                            <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                                        </figure>\n                                                        <div class="courseContent">\n                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>\n                                                            <div class="groupContent">\n                                                                <span class="instructorName" v-if="props.option.instructor">{{props.option.instructor}},</span>\n                                                                <span class="dateTime">{{props.option.date}}</span>\n                                                                <span class="batchLabel" v-if="props.option.batchLabel">({{props.option.batchLabel}})</span>\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <template v-else> \n                                                    <div class="commonList">\n                                                        <div class="courseContent">\n                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                            </template>\n                                        </b-autocomplete>\n                                    </b-field>\n                                </template>\n                                <template v-if="filter.isActive && filter.module === \'autocompleteQuerySearch\'">\n                                    <b-field :class="\'filter\'+filter.type">\n                                        <b-autocomplete\n                                            v-model="filter.selected"\n                                            :data="filter.items"\n                                            :loading="filter.isLoading"\n                                            :placeholder="filter.placeholder"\n                                            field="search"\n                                            @typing="fetchQueryData($event, filter)"\n                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"\n                                            :clearable="true">\n                                            <template slot="empty">No results for {{filter.selected}}</template>\n                                        </b-autocomplete>\n                                    </b-field>\n                                </template>\n                            </template>\n                        </div>\n                        <template v-if="data.error">\n                            <yuno-empty-states :options="{\'state\': \'dataNotFound\', \'description\': data.errorData}"></yuno-empty-states>\n                        </template>\n                            <b-table\n                                :class="{\'scrollable\': options.scrollable, \'tableInvisible\': data.error}"\n                                :loading="options.pageLoading"\n                                :paginated="true"\n                                :detailed="options.hasDetailed !== undefined ? options.hasDetailed : false"\n                                :backend-pagination="options.apiPaginated !== undefined ? options.apiPaginated : false"\n                                :total="options.totalResult !== undefined ? options.totalResult : 0"\n                                :sticky-header="options.isStickyHeader !== undefined ? options.isStickyHeader : false"\n                                :height="options.height !== undefined ? options.height : \'\'"\n                                @page-change="onPageChange($event, tabindex)"\n                                :per-page="options.perPage"\n                                :current-page="options.currentPage !== undefined ? options.currentPage : 1"\n                                ref="table"\n                                :data="data.data.rows"\n                                :default-sort="options.defaultSort"\n                                :default-sort-direction="options.sortDirection !== undefined ? options.sortDirection : \'asc\'"\n                                :striped="options.hasStriped !== undefined ? options.hasStriped : true">\n                                <template slot-scope="props">\n                                    <b-table-column \n                                        v-for="(col, colIndex) in data.data.columns" \n                                        :key="colIndex" \n                                        :field="col.field" \n                                        :visible="col.isActive === undefined ? true : col.isActive"\n                                        :label="col.label" \n                                        :sortable="col.sortable">\n                                            <template slot-scope="props" slot="header">\n                                                <template v-if="col.info_icon !== undefined && col.info_icon !== \'\'">\n                                                    {{props.column.label}}\n                                                    <b-tooltip :label="col.info_icon"\n                                                        type="is-dark"\n                                                        position="is-bottom">\n                                                        <i class="fa fa-info-circle" aria-hidden="true"></i>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    {{props.column.label}}\n                                                </template>\n                                            </template>\n                                            <template v-if="col.field === \'action\' || col.field === \'actions\'">\n                                                <div class="fieldVal ctaGroup noOverflow">\n                                                    <template v-for="(item, itemIndex) in props.row[\'actions\']">\n                                                        <b-tooltip :label="item.label"\n                                                            type="is-light"\n                                                            :key="itemIndex"\n                                                            position="is-left">\n                                                            <template v-if="item.isAnchor === undefined || !item.isAnchor">\n                                                                <b-button \n                                                                    :loading="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                    :disabled="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                    @click="onActionTrigger(props.row, item, tabindex)"\n                                                                    class="yunoPrimaryCTA small iconOnly">\n                                                                        <i \n                                                                            v-if="item.iconType === \'fa\'" \n                                                                            :class="\'fa \' +  item.icon" \n                                                                            aria-hidden="true">\n                                                                        </i>\n                                                                        <span class="material-icons" v-else>{{item.icon}}</span>\n                                                                </b-button>\n                                                            </template>\n                                                            <template v-else>\n                                                                <b-button \n                                                                    tag="a"\n                                                                    :href="item.href"\n                                                                    :target="item.target"\n                                                                    class="yunoPrimaryCTA small iconOnly anchor">\n                                                                        <i \n                                                                            v-if="item.iconType === \'fa\'" \n                                                                            :class="\'fa \' +  item.icon" \n                                                                            aria-hidden="true">\n                                                                        </i>\n                                                                </b-button>\n                                                            </template>\n                                                        </b-tooltip>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.hasAction">\n                                                \n                                                    <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                        <template v-if="item.hasClickToView">\n                                                            <div class="fieldVal ctaGroup noOverflow">\n                                                                <b-tooltip :label="item.label"\n                                                                    type="is-light"\n                                                                    :key="itemIndex"\n                                                                    position="is-left">\n                                                                    <template v-if="item.isAnchor === undefined || !item.isAnchor">\n                                                                        <b-button \n                                                                            :loading="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                            :disabled="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                            @click="onActionTrigger(props.row, item, tabindex)"\n                                                                            class="yunoPrimaryCTA small iconOnly">\n                                                                                <i \n                                                                                    v-if="item.iconType === \'fa\'" \n                                                                                    :class="\'fa \' +  item.icon" \n                                                                                    aria-hidden="true">\n                                                                                </i>\n                                                                        </b-button>\n                                                                    </template>\n                                                                    <template v-else>\n                                                                        <b-button \n                                                                            tag="a"\n                                                                            :href="item.href"\n                                                                            :target="item.target"\n                                                                            class="yunoPrimaryCTA small iconOnly anchor">\n                                                                                <i \n                                                                                    v-if="item.iconType === \'fa\'" \n                                                                                    :class="\'fa \' +  item.icon" \n                                                                                    aria-hidden="true">\n                                                                                </i>\n                                                                        </b-button>\n                                                                    </template>\n                                                                </b-tooltip>\n                                                            </div>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ item.value }}</div>\n                                                        </template>\n                                                    </template>\n                                            </template>\n                                            <template v-else-if="col.field === \'learner\'">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row.learner.image" :alt="props.row.learner.name"></li>\n                                                    <li class="userName">\n                                                        <a :href="props.row.learner.profile_url" target="_blank">{{props.row.learner.name}}</a>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.field === \'referrer\'">\n                                                <template v-if="props.row[col.field].id !== 0">\n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>\n                                                        <li class="userName">\n                                                            {{props.row[col.field].name}}\n                                                        </li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal" :class="[col.field]">NA</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.field === \'attendance\' && props.row.attendance.percentage !== undefined">\n                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <b-progress \n                                                        :type="{\n                                                            \'is-red\': props.row.attendance.percentage <= 30,\n                                                            \'is-orange\': props.row.attendance.percentage > 30,\n                                                            \'is-yellow\': props.row.attendance.percentage > 50,\n                                                            \'is-lightGreen\': props.row.attendance.percentage > 70,\n                                                            \'is-darkGreen\': props.row.attendance.percentage > 80\n                                                        }"  \n                                                        format="percent"    \n                                                        :value="Number(props.row.attendance.percentage)">\n                                                        {{props.row.attendance.percentage}}\n                                                    </b-progress>\n                                                    <div class="percentage">{{props.row.attendance.percentage}}% <a href="#" @click="nestedTableModal($event, props.row, props.row[col.field])" v-if="props.row.attendance.attendanceModal !== undefined"><i class="fa fa-external-link-square" aria-hidden="true"></i></a></div>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.field === \'batch\' && props.row.batch.percentage !== undefined">\n                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <b-progress \n                                                        :type="{\n                                                            \'is-red\': props.row.batch.percentage <= 30,\n                                                            \'is-orange\': props.row.batch.percentage > 30,\n                                                            \'is-yellow\': props.row.batch.percentage > 50,\n                                                            \'is-lightGreen\': props.row.batch.percentage > 70,\n                                                            \'is-darkGreen\': props.row.batch.percentage > 80\n                                                        }"  \n                                                        format="percent"    \n                                                        :value="Number(props.row.batch.percentage)">\n                                                        {{props.row.batch.percentage}}\n                                                    </b-progress>\n                                                    <div class="percentage">{{props.row.batch.percentage}}%</div>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.field === \'name\' && props.row[\'image\'] !== undefined">\n                                                <ul class="user" :class="{\'hasTags\': props.row.fieldWithTags !== undefined}">\n                                                    <li class="userImg"><img :src="props.row[\'image\']" :alt="props.row[col.field]"></li>\n                                                    <li class="userName">\n                                                        <template v-if="col.customEvent !== undefined && col.customEvent">\n                                                            <a href="#" @click="onCustomEvent($event, props.row, col)">{{props.row[col.field]}}</a>\n                                                        </template>\n                                                        <template v-else>\n                                                            {{props.row[col.field]}}    \n                                                        </template>\n                                                        <template v-if="props.row.fieldWithTags !== undefined && props.row.fieldWithTags.field === \'name\'">\n                                                            <div class="tagsWrapper">\n                                                                <b-tag rounded\n                                                                    v-for="(tag, tagIndex) in props.row.fieldWithTags.tags" :key="tagIndex">\n                                                                    {{ tag }}\n                                                                </b-tag>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.isLink !== undefined && col.isLink">\n                                                <template v-if="col.hasTooltip !== undefined && col.hasTooltip">\n                                                    <b-tooltip :label="props.row[col.field]"\n                                                        type="is-dark"\n                                                        :position="col.tooltipPosition === undefined ? \'is-left\' : col.tooltipPosition">\n                                                        <div class="fieldVal withLink" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a :href="props.row[\'url\']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    \n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal withLink" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a :href="props.row[\'url\']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    \n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.copyToClipboard !== undefined && col.copyToClipboard">\n                                                <template v-if="props.row[col.field].isEmpty === undefined || !props.row[col.field].isEmpty">\n                                                    <div class="clipboard noField">\n                                                        <div class="fieldWrapper">\n                                                            <b-input :id="props.row[col.field].id" :value="props.row[col.field].url" readonly></b-input>\n                                                        </div>\n                                                        <b-tooltip :label="props.row[col.field].label"\n                                                            type="is-light"\n                                                            :position="props.row[col.field].position">\n                                                            <i @click="copyToClipboard(props.row[col.field].id)" class="fa trigger fa-clipboard" aria-hidden="true"></i>    \n                                                        </b-tooltip>\n                                                    </div>\n                                                </template>\n                                                <template v-else>\n                                                    <template v-if="col.withTag !== undefined && col.withTag">\n                                                        <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                            <span class="tag" :class="[{\'is-success\': props.row[col.field].url === \'online\'}, {\'is-warning\': props.row[col.field].url === \'offline\'}]">{{ props.row[col.field].url }}</span>\n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field].url }}</div>\n                                                    </template>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.customColor !== undefined && col.customColor">\n                                                <template v-if="col.customEvent !== undefined && col.customEvent">\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : \'\']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field].val }}</a></div>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : \'\']">{{ props.row[col.field].val }}</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.userWithPic !== undefined && col.userWithPic">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>\n                                                    <li class="userName">{{props.row[col.field].name}}</li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.hasArray !== undefined && col.hasArray">\n                                                <div class="fieldVal arrayList" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                        <span class="item">{{item}}</span>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.hasTooltip !== undefined && col.hasTooltip">\n                                                <b-tooltip :label="props.row[col.field]"\n                                                    type="is-dark"\n                                                    :position="col.tooltipPosition === undefined ? \'is-left\' : col.tooltipPosition">\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field] }}</div>    \n                                                </b-tooltip>\n                                            </template>\n                                            <template v-else-if="col.hasTag !== undefined && col.hasTag">\n                                                <div :id="props.row.scrollID !== undefined ? props.row.scrollID : \'\'" class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <span class="tag" \n                                                        :class="\n                                                            [\n                                                                {\'is-success\': props.row[col.field] === \'PAID\'}, \n                                                                {\'is-danger\': props.row[col.field] === \'EXPIRED\'},\n                                                                {\'is-warning\': props.row[col.field] === \'FAILED\'},\n                                                                {\'is-info\': props.row[col.field] === \'ISSUED\'},\n                                                                {\'is-info noBG\': col.hasSlot === true},\n                                                                {\'is-success\': props.row[col.field] === \'ACTIVE\'},\n                                                                {\'is-success noText\': props.row[col.field] === true},\n                                                                {\'is-light noText\': props.row[col.field] === false},\n                                                                {\'is-warning\': props.row[col.field] === \'INACTIVE\'}\n                                                            ]">\n                                                        {{ props.row[col.field] }}\n                                                    </span>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.isInstructor !== undefined && col.isInstructor">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row[\'instructor\'] !== undefined ? props.row[\'instructor\'].image : props.row[\'instructor_image\']" :alt="props.row[col.field]"></li>\n                                                    <li class="userName">\n                                                        <template v-if="props.row[\'instructor\'] !== undefined">\n                                                            {{props.row[\'instructor\'].name}}\n                                                        </template>\n                                                        <template v-else>\n                                                            {{props.row[col.field]}}\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.isOrg !== undefined && col.isOrg">\n                                                <template v-if="props.row[\'org_admin\'].id !== 0 && props.row[\'org_admin\'].id !== \'\'">\n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[\'org_admin\'].image" :alt="props.row[\'org_admin\'].name"></li>\n                                                        <li class="userName">\n                                                            {{props.row[\'org_admin\'].name}}\n                                                        </li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal">NA</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasCombined !== undefined">\n                                                <div class="fieldVal" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <template v-if="props.row[col.field] === props.row[col.hasCombined.otherField]">\n                                                        <b-tag class="colorRed" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>\n                                                    </template>\n                                                    <template v-else>\n                                                        <b-tag class="colorGreen" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.isCounselor !== undefined && col.isCounselor">\n                                                <template v-if="props.row[col.field] !== \'NA\'"> \n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[\'counselor_image\']" :alt="props.row[col.field]"></li>\n                                                        <li class="userName">{{props.row[col.field]}}</li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    {{props.row[col.field]}}\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasCounselor !== undefined && col.hasCounselor">\n                                                <template v-if="props.row.counselor.name !== \'NA\'"> \n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row.counselor.image" :alt="props.row.counselor.name"></li>\n                                                        <li class="userName">{{props.row.counselor.name}}</li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else> \n                                                    {{props.row.counselor.name}}\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasHierarchy !== undefined && col.hasHierarchy">\n                                                <div class="fieldVal hierarchyList" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <template v-if="props.row[col.field].length > 1">\n                                                        <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                            <template v-if="itemIndex === 0">\n                                                                <span class="item">{{item}} <b-tooltip :label="props.row.isExpand ? \'Click to collapse\' : \'Click to expand\'"\n                                                                    type="is-light"\n                                                                    position="is-left">\n                                                                    <i @click="toggleShowmore($event, props.row)" class="fa" :class="props.row.isExpand ? \'fa-minus-circle\' : \'fa-plus-circle\'" aria-hidden="true"></i>\n                                                                </b-tooltip></span>\n                                                            </template>\n                                                            <template v-else>\n                                                                <template v-if="props.row.isExpand">\n                                                                    <span class="item">{{item}}</span>\n                                                                </template>\n                                                            </template>\n                                                        </template>\n                                                    </template>\n                                                    <template v-else>\n                                                        <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                            <template v-if="itemIndex === 0">\n                                                                <span class="item">{{item}}</span>\n                                                            </template>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.customEvent !== undefined && col.customEvent">\n                                                <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field] }}</a></div>\n                                            </template>\n                                            <template v-else>\n                                                <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field] }}</div>\n                                            </template>    \n                                    </b-table-column>\n                                </template>\n                                <template slot="detail" slot-scope="props" v-if="options.hasDetailed !== undefined && options.hasDetailed">\n                                    <template v-if="props.row.detail.type === \'table-grid\'">\n                                        <b-table\n                                            :data="props.row.detail.data.rows">\n                                            <template slot-scope="propsrow">\n                                                <b-table-column \n                                                    v-for="(nestedCol, nestedColIndex) in props.row.detail.data.columns" \n                                                    :key="nestedColIndex" \n                                                    :field="nestedCol.field" \n                                                    :label="nestedCol.label" \n                                                    :sortable="nestedCol.sortable">\n                                                    <template v-if="nestedCol.field === \'batch\' && propsrow.row[nestedCol.field].attendanceModal !== undefined">\n                                                        <div class="fieldVal">\n                                                            <a href="#" @click="nestedTableModal($event, propsrow.row, propsrow.row[nestedCol.field], true)"><i class="fa fa-external-link-square" aria-hidden="true"></i> {{propsrow.row[nestedCol.field].label}}</a>\n                                                        </div>        \n                                                    </template>\n                                                    <template v-else-if="nestedCol.field === \'instructor\'">\n                                                        <ul class="user">\n                                                            <li class="userImg"><img :src="propsrow.row[nestedCol.field].image" :alt="propsrow.row[nestedCol.field].name"></li>\n                                                            <li class="userName">\n                                                                {{propsrow.row[nestedCol.field].name}}\n                                                            </li>\n                                                        </ul>\n                                                    </template>\n                                                    <template v-else-if="nestedCol.field === \'attendance\' && propsrow.row[nestedCol.field].percentage !== undefined">\n                                                        <div class="fieldVal percentageBlock">\n                                                            <b-progress \n                                                                :type="{\n                                                                    \'is-red\': propsrow.row[nestedCol.field].percentage <= 30,\n                                                                    \'is-orange\': propsrow.row[nestedCol.field].percentage > 30,\n                                                                    \'is-yellow\': propsrow.row[nestedCol.field].percentage > 50,\n                                                                    \'is-lightGreen\': propsrow.row[nestedCol.field].percentage > 70,\n                                                                    \'is-darkGreen\': propsrow.row[nestedCol.field].percentage > 80\n                                                                }"  \n                                                                format="percent"    \n                                                                :value="propsrow.row[nestedCol.field].percentage">\n                                                            </b-progress>\n                                                            <div class="percentage">{{propsrow.row[nestedCol.field].percentage}}%</div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="fieldVal" :class="[nestedCol.field !== undefined ? nestedCol.field : \'\']">{{propsrow.row[nestedCol.field]}}</div>\n                                                    </template>\n                                                </b-table-column>\n                                            </template>\n                                        </b-table>\n                                    </template>\n                                    <template v-if="props.row.detail.type === \'list\'">\n                                        <template v-if="props.row.detail.data.length !== 0">\n                                            <ul class="detailList">\n                                                <li v-for="(item, itemIndex) in props.row.detail.data">\n                                                    <figure class="listImage">\n                                                        <img :src="item.image" :alt="item.name">\n                                                    </figure>\n                                                    <div class="listInfo">\n                                                        <p class="listTitle">{{item.name}}</p>\n                                                        <small>{{item.email}}</small>\n                                                    </div>\n                                                </li>\n                                            </ul> \n                                        </template>\n                                        <template v-else>\n                                            <p class="listMessage"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{props.row.detail.message}}</p>\n                                        </template>\n                                    </template>\n                                </template>\n                            </b-table>\n                        \n                    </template>\n                </div>\n            ',data:()=>({}),computed:{},created(){},mounted(){},methods:{nestedTableModal(t,e,n,a){t.preventDefault(),Event.$emit("nestedTableModal",t,e,n,a)},toggleShowmore(t,e){t.preventDefault(),e.isExpand?e.isExpand=!1:e.isExpand=!0},filteredAutocomplete:(t,e)=>t.items.filter((n=>n[e].toString().toLowerCase().indexOf(t.selected.toLowerCase())>=0)),scrollToEle(t){let e=YUNOCommon.heightOfEle(document.querySelectorAll(".yunoHeader")[0])+YUNOCommon.heightOfEle(document.querySelectorAll(".yunoTabNav")[0],!0)+10;jQuery([document.documentElement,document.body]).animate({scrollTop:jQuery("#"+t.url).offset().top-e},500)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})},onCustomEvent(t,e,n){Event.$emit("onCustomEvent",t,e,n)},onPageChange(t,e){Event.$emit("onTablePageChange",t,e),this.$props.data.manageState&&this.manageState(this.$props.data,e,t)},onActionTrigger(t,e,n){Event.$emit("onActionTrigger",t,e,n)},onFilterChange(t,e){Event.$emit("onFilterChange",e,t)},manageState(t,e,n){let a=JSON.parse(JSON.stringify(t)),i=t.filters,o=YUNOCommon.findObjectByKey(i,"type","tableColumn");const r=function(t,e){for(let n=0;n<t.length;n++){const a=t[n];e.push(a)}},s={tab:a.tab,index:e,defaultFilters:[],appliedFilters:[],nestedTabs:"",hideColumns:[],page:!1!==n?n:1};if(null!==o&&r(o.selected,s.hideColumns),void 0!==t.nestedTabs){let e=YUNOCommon.findObjectByKey(t.nestedTabs,"isActive",!0);s.nestedTabs=e.value}r(a.defaultFilters,s.defaultFilters),r(a.appliedFilters,s.appliedFilters);const l=encodeURI(JSON.stringify(s));if(history.pushState){var c=window.location.protocol+"//"+window.location.host+window.location.pathname+"?state="+l;window.history.pushState({path:c},"",c)}},onFilterItemSelect(t,e,n,a,i){Event.$emit("onFilterItemSelect",t,e,n,a,i),this.$props.data.manageState&&this.manageState(this.$props.data,i,!1)},fetchQueryData:_.debounce((function(t,e){t.length>2?(e.isLoading=!0,Event.$emit("fetchQueryData",t,e)):e.data=[]}),500),onAutocompleteSelect(t,e,n){Event.$emit("onAutocompleteSelect",t,e,n),this.$props.data.manageState&&this.manageState(this.$props.data,e,!1)},onFilterClear(t,e){Event.$emit("onFilterClear",t,e),this.$props.data.manageState&&this.manageState(this.$props.data,e,!1)},manageNestedTabs(t,e,n,a,i){event.preventDefault();let o=n.nestedTabs;for(let t=0;t<o.length;t++)o[t].isActive&&(o[t].isActive=!1);o[e].isActive=!0,Event.$emit("onNestedTabChange",n,a,i),this.$props.data.manageState&&this.manageState(n,i,!1)}}})}});Vue.component("yuno-tabs-v2",{props:{animated:{type:Boolean,default:!1},destroyOnHide:{type:Boolean,default:!1}},template:'\n        <section class="yunoTabsWrapper" :class=[filterResult.tabs.wrapperClass]>\n            <b-tabs \n                class="yunoTabsV5" \n                v-model="filterResult.tabs.activeTab" \n                @input="tabChange" \n                :animated="animated"\n                :destroy-on-hide="destroyOnHide"\n            >\n                <b-tab-item \n                    v-for="(tab, i) in filterResult.tabs.items"\n                    :visible="tab.isVisible"\n                    :key="i"\n                    :label="tab.label">\n                    <slot :name="tab.slug"></slot>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user","filterResult"])},async created(){},destroyed(){},mounted(){},methods:{tabChange(t){this.$emit("tabChange",t)}}});const YUNONotifications=(jQuery,{notifications:function(){Vue.component("yuno-notifications",{props:["data"],template:'\n                <div>\n                    <template v-if="data.loading">\n                        <ul class="settingToggles">\n                            <template v-for="(item, index) in 3">\n                                <li :key="index" class="item">\n                                    <div class="info">\n                                        <h3 class="caption"><b-skeleton active width="100px"></b-skeleton></h3>\n                                    </div>\n                                    <div class="toggleSetting">\n                                        <b-skeleton active width="50px"></b-skeleton>\n                                    </div>\n                                </li>\n                            </template>\n                        </ul>\n                    </template>\n                    <template v-if="data.success">\n                        <ul class="settingToggles">\n                            <template v-for="(item, index) in data.data.channels">\n                                <li :key="index" class="item" v-if="item.is_active">\n                                    <div class="info">\n                                        <div class="trigger" v-if="false"><i @click="expandMe(item, index)" :class="item.isActive ? \'fa-minus-circle\' : \'fa-plus-circle\'" class="fa " aria-hidden="true"></i></div><h3 class="caption">{{item.label}}</h3>\n                                    </div>\n                                    <div class="toggleSetting">\n                                        <b-switch v-model="item.status" @input="updateSetting()"></b-switch>\n                                    </div>\n                                    <a :href="item.status ? \'/channel-events?channel=\' + item.name + \'&title=\' + item.label : \'#\'" @click="manageEvents($event, item)" class="events" :class="[!item.status ? \'disabled\' : \'\']"><span class="material-icons-outlined">arrow_forward_ios</span></a>\n                                    <ul class="subItems" v-if="false">\n                                        <li v-for="(subItems, subItemsIndex) in item.events">\n                                            <div class="subInfo">\n                                                <h4 class="caption">{{subItems.label}}</h4>\n                                            </div>\n                                            <div class="subToggleSetting" v-if="subItems.type === \'toggle\'">\n                                                <b-switch @input="updateSetting()" :disabled="item.status ? false : true" v-model="subItems.status"></b-switch>\n                                            </div>\n                                            <div class="block subRadio colorGrey" v-if="subItems.type === \'radio\'">\n                                                <template v-for="(option, optionIndex) in subItems.items">\n                                                    <b-radio \n                                                        @input="updateSetting()"\n                                                        :key="optionIndex"\n                                                        :disabled="item.status ? false : true"\n                                                        v-model="subItems.status"\n                                                        :name="\'recommended\' + index"\n                                                        :native-value="option.slug">\n                                                        {{ option.label }}\n                                                    </b-radio>\n                                                </template>\n                                            </div>\n                                        </li>\n                                    </ul>\n                                </li>\n                            </template>\n                            <li class="ctaWrapper">\n                                <b-button\n                                    @click="initNotification()"\n                                    :loading="notificationCTA.isLoading ? true : false"\n                                    :disabled="notificationCTA.isDisabled ? true : false" \n                                    native-type="button"\n                                    class="yunoSecondaryCTA big">\n                                    Update\n                                </b-button>  \n                            </li>\n                        </ul>\n                    </template>\n                </div>\n            ',data:()=>({whatsappCTA:"Enable",isUpdateCTA:!1,notificationCTA:{isLoading:!1,isDisabled:!0},expandCollapse:[],payload:{action:"updateNotifications",notification:""}}),computed:{...Vuex.mapState(["loader","settings"])},created(){},mounted(){},methods:{manageEvents(t,e){e.status||t.preventDefault()},enableLoader(t){this.loader.isActive=t,this.loader.overlay=t},notificationDone(t){this.enableLoader(!1);const e=t.response.data.message;void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code?this.$buefy.toast.open({duration:5e3,message:e,position:"is-bottom"}):this.$buefy.toast.open({duration:5e3,message:e,position:"is-bottom",type:"is-danger"})},initNotification(){this.enableLoader(!0);const t=JSON.parse(JSON.stringify(this.$props.data.data)),e=this;delete t.dump,this.payload.notification=t;const n={apiURL:YUNOCommon.config.notificationUpdateAPI(),module:"gotData",store:"notifications",payload:JSON.stringify(this.payload),headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:function(t){return e.notificationDone(t)}};this.$store.dispatch("postData",n)},expandMe(t,e){t.isActive?t.isActive=!1:t.isActive=!0,this.manageCTA()},showDialog(t,e){e[t]?this.whatsappCTA="Enable":this.whatsappCTA="Disable",this.$buefy.dialog.confirm({title:"Whatsapp notification permission",message:"Are you sure, you want to enable whatsapp notifications",confirmText:this.whatsappCTA,onConfirm:()=>this.updateSetting(e),onCancel:()=>this.cancelSettings(t,e)})},cancelSettings(t,e){e[t]?e[t]=!1:e[t]=!0},onPostSuccess(t,e,n){this.loader.overlay=!1,this.loader.isActive=!1,t?this.$buefy.toast.open({duration:5e3,message:"Error occurred, please try again later",position:"is-bottom",type:"is-danger"}):200===e.data.code&&this.$buefy.toast.open({duration:5e3,message:`${e.data.message}`,position:"is-bottom"})},manageCTA(){let t=JSON.parse(JSON.stringify(this.$props.data.data.channels)),e=JSON.parse(this.$props.data.data.dump);compare=_.isEqual(t,e),this.notificationCTA.isDisabled=compare},updateSetting(t){this.manageCTA()}}})}}),YUNOAddress=(jQuery,{address:function(){Vue.component("yuno-address",{props:["data"],template:'\n                <div class="row">\n                    <div class="col-12 col-md-12 col-lg-12">\n                        <template v-if="data.loading">\n                            <div class="addressCard">\n                                <div class="header">\n                                    <h2 class="addressType"><b-skeleton active width="80px"></b-skeleton></h2>\n                                </div>\n                                <p><b-skeleton active width="200px" height="80px"></b-skeleton></p>\n                            </div>\n                        </template>\n                        <template v-else-if="data.success && data.error === null">\n                            <div class="addressCard">\n                                <div class="header">\n                                    <h2 class="addressType">{{data.data.address_type}}</h2>\n                                    <ul class="actionList">\n                                        <li>\n                                            <b-tooltip label="Edit address"\n                                                type="is-light"\n                                                position="is-top">\n                                                <a href="#" @click="onNewAddress(false)"><i class="fa fa-pencil" aria-hidden="true"></i></a>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul>\n                                </div>\n                                <p>{{data.data.flat_house_number}}, {{data.data.street}}, {{data.data.landmark}}</p>\n                                <p>{{data.data.city}} - {{data.data.pin_code}}, {{data.data.state}}, {{data.data.country}}</p>\n                            </div>\n                        </template>\n                        <template v-else-if="data.success">\n                            <div class="action">\n                                <p>{{data.errorMsg}}</p>\n                                <b-button\n                                    @click="onNewAddress(true)" \n                                    class="yunoPrimaryCTA wired">\n                                    <i class="fa fa-plus" aria-hidden="true"></i> Add Address\n                                </b-button>\n                            </div>\n                        </template>\n                    </div>\n                    \n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["countries","states","cities"])},created(){},mounted(){},methods:{onNewAddress(t){Event.$emit("onNewAddress",t)}}})}}),YUNOAccount=(jQuery,{account:function(){Vue.component("yuno-account",{props:["data"],template:'\n                <ul class="row settingList">\n                    <li class="col-12 col-md-4 col-lg-4 settingLabel">Google Account</li>\n                    <li class="col-12 col-md-8 col-lg-8 settingField">\n                        {{userInfo.data.email}} <b-tag rounded><a @click="chooseAccountState()" :href="switchAccountURL">Switch Google account</a></b-tag>\n                        <ul class="permissionList">\n                            <li class="listTitle">Permissions:</li>\n                            <li class="item" :class="{setMinHeight: googleContacts.loading}">\n                                <div v-if="googleContacts.loading" class="smallLoader"></div>\n                                <template v-else>\n                                    <i class="fa" :class="googleContacts.error !== null ? \'fa-times-circle\' : \'fa-check-circle\'" aria-hidden="true"></i> Google contacts <b-tag rounded v-if="googleContacts.error !== null"><a @click="googlePermission()" :href="getGoogleContacts">Grant permission</a></b-tag>\n                                </template>\n                            </li>\n                        </ul>\n                        <div class="ctaWrapper">\n                            <b-button tag="a"\n                                href="https://myaccount.google.com/permissions"\n                                target="_blank"\n                                class="yunoPrimaryCTA wired">\n                                Manage permissions in Google\n                            </b-button>\n                        </div>\n                        <div class="ctaWrapper" v-if="false">\n                            <b-button tag="a"\n                                :href="getGoogleContacts"\n                                @click="googlePermission()"\n                                class="yunoPrimaryCTA wired">\n                                Grant contacts permissions in Google\n                            </b-button>\n                        </div>\n                    </li>\n                    <li class="col-12 col-md-4 col-lg-4 settingLabel">Logout</li>\n                    <li class="col-12 col-md-8 col-lg-8 settingField"><a href="/logout/">Log out of Yunolearning</a></li>\n                    <li class="col-12 col-md-4 col-lg-4 settingLabel" v-if="false">Delete Account</li>\n                    <li class="col-12 col-md-8 col-lg-8 settingField" v-if="false"><a href="#">Delete your account forever</a></li>\n                </ul>\n            ',data:()=>({switchAccountURL:""}),computed:{...Vuex.mapState(["userInfo","googleContacts","header"]),getGoogleContacts(){return this.$store.getters.googleContacts}},created(){},mounted(){this.fetchContacts(),this.manageSwitchAccount()},methods:{manageSwitchAccount(){this.switchAccountURL=this.findItemBySlug(this.header.data,"switch-account").url},findItemBySlug(t,e){for(const n of t){if(n.slug===e)return n;if(n.items){const t=this.findItemBySlug(n.items,e);if(t)return t}}return null},fetchContacts(){const t={apiURL:YUNOCommon.config.googleContactsAPI(isLoggedIn),module:"gotData",store:"googleContacts",callback:!1};this.$store.dispatch("fetchData",t)},googlePermission(){localStorage.setItem("userState",window.location.pathname)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOAvailabilityPreview=(jQuery,{availabilityPreview:function(){Vue.component("yuno-availability-preview",{props:["data","options"],template:'\n                <b-modal \n                    :active.sync="options.modal" \n                    :width="800" \n                    :can-cancel="[\'escape\', \'x\']"\n                    class="yunoModal preview">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">{{options.title}}</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper noBG">\n                                <ul class="legends">\n                                    <li class="yes">Available</li>\n                                    <li class="no">Unavailable</li>\n                                </ul>\n                                <yuno-preview-availability :data="data" :options="tableOptions"></yuno-preview-availability>\n                            </div>\n                        </div>\n                </b-modal>\n            ',data:()=>({tableOptions:{isFluid:!1,pageLoading:!1,apiPaginated:!1,totalResult:"",perPage:50,limit:20,offset:0,hasStriped:!1}}),computed:{...Vuex.mapState(["timeSlots","instructorAvailabilityGrid"])},async created(){},mounted(){},methods:{additionalRow(t){t&&t.length>0&&t.forEach((t=>{t.time&&(t.time=this.formatTime(t.time))}))},formatTime(t){if(!t)return"";if(t.includes(":")){const e=t.split(":");return e.length>2?`${e[0]}:${e[1]}`:t}return t},additionalCols(t){t.push({field:"slot",label:"",sortable:!0,hasSlot:!0});for(let e=0;e<t.length;e++){const n=t[e];"slot"===n.field&&(n.hasTag=!0),"sun"===n.field&&(n.hasTag=!0),"mon"===n.field&&(n.hasTag=!0),"tue"===n.field&&(n.hasTag=!0),"wed"===n.field&&(n.hasTag=!0),"thu"===n.field&&(n.hasTag=!0),"fri"===n.field&&(n.hasTag=!0),"sat"===n.field&&(n.hasTag=!0)}},gotResources(t){const e=this.instructorAvailabilityGrid;if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let n=t.response.data.data,a=n.rows,i=n.columns,o=t.response.data.count;this.additionalCols(i),this.additionalRow(a),this.tableOptions.pageLoading=!1,this.tableOptions.totalResult=o,e.data=n}else e.data=[],this.tableOptions.totalResult=0},fetchResources(t){this.instructorAvailabilityGrid.data=[],this.instructorAvailabilityGrid.success=!1;const e=this,n={apiURL:YUNOCommon.config.availabilityGridAPI(isLoggedIn),module:"gotData",store:"instructorAvailabilityGrid",moduleLoading:t,addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotResources(t)}};this.$store.dispatch("fetchData",n)}}})}}),YUNOAvailability=(jQuery,{availability:function(){Vue.component("yuno-availability-v3",{props:["data","options"],template:'\n\t\t\t\t<div class="availabilityWrapper">\n\t\t\t\t\t<template v-if="isFormLoading">\n\t\t\t\t\t\t<div class="smallLoader"></div>\n\t\t\t\t\t</template>\n\t\t\t\t\t<template v-if="isFormReady">\n\t\t\t\t\t\t<validation-observer tag="div" ref="availabilityObserver" v-slot="{ handleSubmit }">\n\t\t\t\t\t\t\t<form id="formNewAddress" @submit.prevent="handleSubmit(availabilityInit)">\n\t\t\t\t\t\t\t\t<ul class="hoursWrapper">\n\t\t\t\t\t\t\t\t\t<li class="item" v-for="(item, index) in data.data.days" :key="index">\n\t\t\t\t\t\t\t\t\t\t<div class="itemWrapper">\n\t\t\t\t\t\t\t\t\t\t\t<div class="day">{{item.name}}</div>\n\t\t\t\t\t\t\t\t\t\t\t<div class="slots">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-switch v-model="item.is_available"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:true-value="true"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@input="onOpen($event, item)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:false-value="false">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="!item.is_available">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tUnavailable\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tAvailable\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-switch>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div class="hours" v-if="item.is_available"> \n\t\t\t\t\t\t\t\t\t\t\t<template v-for="(slot, slotIndex) in item.time_slots">\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="hourWrapper" :key="slotIndex" v-if="slot.isActive">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:customMessages="{ required: message.required }" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="chooseHour" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{required:true, isOverlapping: slot.isOverlapping}" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div :class="{ hasError: errors && errors.length > 0 }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="\'start-\' + slotIndex"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="slot.startsAt"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="list"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="filterMenu"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span><template v-if="!item.is24Hours">Start</template> {{slot.startsAt}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-for="(time, timeIndex) in timeSlots.data">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown-item \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click="onFilterItemSelect(slot, time, \'start\', item)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="time.label"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="timeIndex"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="listitem">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>{{time.label}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown-item>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error" v-if="false">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:customMessages="{ required: message.required, is_not: message.isNot }" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if="slot.isEnds"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="chooseHour"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{required:true, is_not: slot.startsAt, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div :class="{ hasError: errors && errors.length > 0 }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if="slot.isEnds"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="\'end-\' + slotIndex"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="slot.endsAt"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="list"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="filterMenu"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>End {{slot.endsAt}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-for="(time, timeIndex) in timeSlots.data">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown-item \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click="onFilterItemSelect(slot, time, \'end\', item)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="time.label"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="timeIndex"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="listitem">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>{{time.label}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown-item>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-button \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click="removeSlot(item, slot , slotIndex)" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA iconOnly removeSlot noBG">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined">close</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-tooltip label="Copy time to all"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype="is-dark"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tposition="is-top">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-button \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click="copySlot(item, slot)" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if="slotIndex === 0"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA iconOnly copySlot noBG">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined">content_copy</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-tooltip>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="slot.isEnds && slotIndex === item.time_slots.length - 1">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="addSlotWrapper">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-button \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if="slotIndex === item.time_slots.length - 1"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click="addSlot(item)" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA addSlot noBG">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tAdd Hours\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t<div class="ctaWrapper">\n\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\tnative-type="submit"\n\t\t\t\t\t\t\t\t\t\t:loading="isLoading ? true : false"\n\t\t\t\t\t\t\t\t\t\t:disabled="isLoading ? true : false"\n\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\tSave\n\t\t\t\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\tv-if="typeof options.isPreview === \'undefined\' || options.isPreview"\n\t\t\t\t\t\t\t\t\t\t@click="initPreview()"\n\t\t\t\t\t\t\t\t\t\t:disabled="data.data.hasRecord && preview.isMatched ? false : true" \n\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA wired">\n\t\t\t\t\t\t\t\t\t\tPreview\n\t\t\t\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</form>\n\t\t\t\t\t\t</validation-observer>\n\t\t\t\t\t\t<yuno-availability-preview :data="data.data" :options="preview"></yuno-availability-preview>\n\t\t\t\t\t</template>\n\t\t\t\t</div>\n\t\t\t  ',data:()=>({isLoading:!1,message:{required:"Required",isNot:"Value should not be same as start time",error:""},slot:{id:"0",startsAt:"",endsAt:"",start:"",end:"",isActive:!0,slotID:"",isEnds:!0,isExist:!1,isOverlapping:!1,isEndTime:!1},weeks:[],preview:{modal:!1,title:"Availability",oldData:"",isMatched:!0}}),computed:{...Vuex.mapState(["user","userInfo","userProfile","userRole","timeSlots","instructorAvailability","instructorAvailabilityGrid","settings","resources"]),isFormLoading:{get(){return this.$props.data.loading||this.timeSlots.loading}},isFormReady:{get(){let t=this.$props.data.success&&this.timeSlots.success;return t&&this.initFormReady(),t}}},methods:{timeToMinutes(t){let[e,n]=t.split(":").map(Number);return 24===e&&0===n?1440:60*e+n},manageEndTime(t){let e=this.timeToMinutes(t.start),n=this.timeToMinutes(t.end);n<e&&(n+=1440),t.isEndTime=n<=e},manageOverlappingSlot(t,e){const n=this.timeToMinutes(t.start),a=this.timeToMinutes(t.end);t.isOverlapping=!1,e.time_slots.forEach((e=>{if(e.slotID===t.slotID)return;const i=this.timeToMinutes(e.start),o=this.timeToMinutes(e.end);n<o&&i<a&&(t.isOverlapping=!0)}))},copySlot(t,e){let n="";n=this.$props.options.isSignup?this.instructorAvailabilityGrid.data:this.resources.data,n.days.forEach((e=>{if(e.is_available){const n=JSON.parse(JSON.stringify(t.time_slots));e.time_slots.forEach((t=>{t.isActive=!1})),n.forEach((t=>{t.id="0"})),e.time_slots=n}}))},initFormReady(){""===this.preview.oldData&&(this.preview.oldData=JSON.stringify(this.$props.data.data)),this.enablePreview(JSON.stringify(this.$props.data.data))},initPreview(){this.preview.modal=!0},availabilityDone(t){this.isLoading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code?(this.$buefy.toast.open({duration:5e3,message:`${t.response.data.message}`,position:"is-bottom"}),(void 0===this.options.isPreview||this.options.isPreview)&&(this.days=[],this.preview.oldData="",Event.$emit("initAvailability",this.$props.options.tab,this.$props.options.tabIndex))):this.$buefy.dialog.alert({title:"Availability",message:`${t.response.data.message}`,confirmText:"Ok"})},availabilityInit(){this.isLoading=!0;const t=JSON.parse(JSON.stringify(this.$props.data.data)),e=t.days||[],n=this;let a="";const i={id:isLoggedIn};a=t.hasRecord?YUNOCommon.config.instructor("updateAvailabilityV2",i):YUNOCommon.config.instructor("createAvailabilityV2",i);let o={weeks:[]};for(const t of e){const e={day:t.name,availability:(t.time_slots||[]).map((t=>{if(""===t.start&&""===t.end)return null;const e=(t,e)=>{if(!t)return"";if("start"===e&&"24:00"===t)return"00:00";if(t.includes(":")){const e=t.split(":");return e.length>2?`${e[0]}:${e[1]}`:t}return t};return{id:t.id||"0",start:e(t.start,"start"),end:e(t.end,"end"),is24Hours:!1,isActive:t.isActive||!1}})).filter(Boolean)};o.weeks.push(e)}const r={apiURL:a,module:"gotData",store:"instructorAvailability",payload:o,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:function(t){return n.availabilityDone(t)}};this.$store.dispatch("postData",r)},onFilterItemSelect(t,e,n,a){t[n]=e.slug,"24"!==e.slug?(t.isEnds=!0,a.is24Hours=!1):(a.time_slots=[],a.time_slots.push(t),a.is24Hours=!0,t.isEnds=!1),this.enablePreview(JSON.stringify(this.$props.data.data)),this.manageOverlappingSlot(t,a),this.manageEndTime(t)},removeSlot(t,e,n){const a=t.time_slots;a.splice(n,1);for(let t=0;t<a.length;t++)a[t].slotID=t;0===a.length&&(t.is_available=!1),this.enablePreview(JSON.stringify(this.$props.data.data))},addSlot(t){let e=1===t.time_slots.length?0:t.time_slots.length-1;t.time_slots;const n=JSON.parse(JSON.stringify(this.slot));n.slotID=e+1,t.time_slots.push(n),this.enablePreview(JSON.stringify(this.$props.data.data))},enablePreview(t){t!==this.preview.oldData?this.preview.isMatched=!1:this.preview.isMatched=!0},onOpen(t,e){if(t){e.time_slots||(e.time_slots=[]);const t={id:"0",startsAt:"",endsAt:"",start:"",end:"",isActive:!0,slotID:e.time_slots.length,isEnds:!0,isExist:!1,isOverlapping:!1,isEndTime:!1};e.time_slots.push(t),e.is_available=!0}else e.time_slots=[],e.is_available=!1;this.enablePreview(JSON.stringify(this.$props.data.data))}}})}});Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){t.response.data.data}},fetchReferralCode(){const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const t=sessionStorage.getItem("isLHSMenu");null===t||this.isMobile?this.reduce=!1:(this.reduce="true"===t,this.sidebarToggle(!0))},sidebarToggle(t){t||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(t){let e=parseInt(t-10),n=parseInt(6e4*e);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data;this.config.yunoAPIToken="Bearer "+e.token,this.tokenExpiry.payload.token="Bearer "+e.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.doneRefreshAPIToken(t)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data,n=10;if(e.minutes<=n){let t={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(t)}else this.initTokenTime(e.minutes)}},fetchAPITokenExpiryTime(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.gotAPITokenExpiryTime(t)}};this.$store.dispatch("postData",n)},extractSlugFromURL(t){const e=t.replace(/\/$/,"").split("/");""===e[e.length-1]&&e.pop();return e[e.length-1]},manageCurrentPage(t){const e=t=>t.replace(/\/$/,""),n=e(window.location.origin+window.location.pathname);t.forEach((t=>{t.items.forEach((t=>{t.is_active=n===e(t.url);let a=!1;t.sub_items.forEach((i=>{i.is_active=n===e(i.url),i.is_active&&i.parent_id===t.id&&(a=!0)})),t.is_expended=!!a}))}))},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},gotPostLoginMenu(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";t?e=t.response.data.data:(e=this.header.data,this.header.success=!0),this.manageCurrentPage(e),this.header.data=e,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(t){const e={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,a={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(t){return n.gotPostLoginMenu(t)}};this.$store.dispatch("fetchData",a)},manageOrgAdmin(t){const{host:e}=YUNOCommon.config,{has_org:n,org_id:a}=t;null===sessionStorage.getItem("activeOrg")&&(n?a.length>1?(window.location.href=`${e()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(a[0].id)):window.location.href=`${e()}/create-organization-account`)},gotUserInfo(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";if(t?e=t.response.data.data:(e=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(e.role),this.userRole.data=e.role,this.userProfile.data=e,this.userProfile.success=!0,e.role,"Learner"===e.role&&this.fetchReferralCode(),"Learner"===e.role&&"pending"===e.is_signup_completed){const t=localStorage.getItem("userState");window.location.pathname+window.location.search!==t&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}e.role,this.$emit("userInfo",e)}},fetchUserInfo(){const t=this,e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(e){return t.gotUserInfo(e)}};this.$store.dispatch("fetchData",e)},getStorage(){const t=this.storage;let e=Number(JSON.parse(JSON.stringify(t.version)));lastStorage=t.name+"V"+--e,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(t.name+"V"+t.version);if(null!==n){const t=JSON.parse(n);this.header.data=t.menu}this.loginStatus()},setStorage(){const t=this.storage,e={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const t=this.storage;sessionStorage.removeItem(t.name+"V"+t.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(t){if(this.generateCode.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;Event.$emit("fetchReferralCode")}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("postData",e)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(t,e){0!==e.sub_items.length&&t.preventDefault(),"generate-code"===e.slug&&t.preventDefault(),"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){const e=t.response.data.data;this.referralCode.data=e}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},gotCode(t){if(this.moduleWithoutTab.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;this.fetchReferralCode()}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotCode(e)}};this.$store.dispatch("postData",e)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Referral":"Referral Earnings",isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)}}}),Vue.component("yuno-course-search-bar",{props:["data","options"],template:'\n        <div class="hasSearchBar">\n            <template v-if="userInfo.loading">\n                <b-skeleton height="31px"></b-skeleton>\n            </template>\n            <div class="searchBarWrapper" v-if="userInfo.success && userInfo.data.role === \'Learner\'">\n                <validation-observer  \n                    tag="div" \n                    ref="searchObserver" \n                    v-slot="{ handleSubmit, invalid }">\n                    <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                        <b-field class="searchFieldWrapper">\n                            <validation-provider \n                                tag="div"\n                                class="searchField"\n                                :customMessages="{ isNotBlank: errorMsg.subject }"\n                                :rules="{required:true, isNotBlank:categories.selected}" \n                                v-slot="{ errors, classes }">\n                                <b-autocomplete\n                                    :class="classes"\n                                    v-model="categories.current"\n                                    :data="categories.data"\n                                    autocomplete="courseSearch"\n                                    :loading="categories.isLoading"\n                                    placeholder="Search..."\n                                    @typing="searchOnTyping"\n                                    @select="onSelect($event)"\n                                    :clearable="true"\n                                >\n                                    <template slot-scope="props">\n                                        <template v-if="props.option.course_url">\n                                            <div class="suggestion courseBlock">\n                                                <figure>\n                                                    <div class="imageWrapper">\n                                                        <img :src="props.option.imageurl" :alt="props.option.title">\n                                                    </div>\n                                                    <figcaption>\n                                                        <p class="courseTitle">{{ props.option.title }}</p>\n                                                        <p class="courseDetail">\n                                                            <span class="caption">Course</span>\n                                                            <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                        </p>\n                                                    </figcaption>\n                                                </figure>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                    </template>\n                                </b-autocomplete>\n                            </validation-provider>\n                            <div class="ctaWrapper">\n                                <b-button\n                                    native-type="submit"\n                                    class="doSearch">\n                                    <span class="material-icons-outlined">search</span>\n                                </b-button>  \n                            </div>\n                        </b-field>\n                    </form>\n                </validation-observer>\n            </div>\n        </div>\n    ',data:()=>({errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}),computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","header","module","searchSuggestions"])},async created(){},mounted(){},methods:{onSelect(t){t&&(t.course_url?window.location.href=t.course_url:t.course_count&&(this.categories.selected=t,this.payload.search=t.id,this.searchParams.category=[t.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],t.parent_cat_slug&&(this.searchParams.category=[t.parent_cat_id],this.searchParams.category_level_1=[t.category_level_1],this.searchParams.category_level_2=[t.id]),this.initForm()))},gotCourseSuggestions(t){if(this.categories.isLoading=!1,200===t.response?.data?.code){const{course:e,category:n,sub_category:a}=t.response.data.data;n&&this.categories.data.push(...n),a&&this.categories.data.push(...a),e&&this.categories.data.push(...e)}},fetchCourseSuggestions(t){const e=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",t),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(t){return e.gotCourseSuggestions(t)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(t){t.length>2?(this.categories.isLoading=!0,this.fetchCourseSuggestions(t)):this.categories.data=[]}),700),initForm(){const t=this.categories.selected;t.category_id,t.category,t.categorySlug;void 0===this.$props.hassearchbar&&(window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)))}}}),Vue.component("yuno-resource-search-bar",{props:["data","options"],template:'\n        <div class="hasSearchBar">\n            <div class="searchBarWrapper">\n                <validation-observer \n                    tag="div" \n                    ref="searchObserver" \n                    v-slot="{ handleSubmit, invalid }">\n                    <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                        <b-field class="searchFieldWrapper">\n                            <validation-provider \n                                tag="div"\n                                class="searchField"\n                                :customMessages="{ isNotBlank: errorMsg.subject }"\n                                :rules="{required:true, isNotBlank:categories.selected}" \n                                v-slot="{ errors, classes }">\n                                <b-autocomplete\n                                    :class="classes"\n                                    v-model="categories.current"\n                                    :data="categories.data"\n                                    autocomplete="courseSearch"\n                                    :loading="categories.isLoading"\n                                    placeholder="Search Content..."\n                                    @typing="searchOnTyping"\n                                    @select="onSelect($event)"\n                                    @input="handleInput"\n                                    :clearable="true"\n                                >\n                                    <template slot-scope="props">\n                                        <div class="suggestion courseBlock">\n                                            <figure>\n                                                <div class="imageWrapper">\n                                                    <img :src="props.option.imageurl" :alt="props.option.title">\n                                                </div>\n                                                <figcaption>\n                                                    <p class="courseTitle">{{ props.option.title }}</p>\n                                                    <p class="subtitle">{{ props.option.excerpt }}</p>\n                                                    <ul class="metaDetail">\n                                                        <li>\n                                                            <span class="caption">Category:</span>\n                                                            <span class="value">{{ props.option.category }}</span>\n                                                        </li>\n                                                        <li>\n                                                            <span class="caption">Published,</span>\n                                                            <span class="value">{{ props.option.publish_date }}</span>\n                                                        </li>\n                                                        <li>\n                                                            <span class="caption">Type:</span>\n                                                            <span class="value">{{ props.option.type }}</span>\n                                                        </li>\n                                                        <li>\n                                                            <span class="caption"><a :href="props.option.resource_url" target="_blank">View Resource</a></span>\n                                                        </li>\n                                                        <li>\n                                                            <span class="caption"><a :href="editURL(props.option)" target="_blank">Edit Resource</a></span>\n                                                        </li>\n                                                    </ul>\n                                                </figcaption>\n                                            </figure>\n                                        </div>\n                                    </template>\n                                </b-autocomplete>\n                            </validation-provider>\n                            <div class="ctaWrapper">\n                                <b-button\n                                    native-type="submit"\n                                    class="doSearch">\n                                    <span class="material-icons-outlined">search</span>\n                                </b-button>  \n                            </div>\n                        </b-field>\n                    </form>\n                </validation-observer>\n            </div>\n        </div>\n    ',data:()=>({errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""}}),computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","header","module","searchSuggestions"])},async created(){},mounted(){},methods:{handleInput(t){""===t&&(this.searchSuggestions.data=[],this.searchSuggestions.error=null,this.searchSuggestions.success=!1,this.categories={data:[],selected:null,current:"",isLoading:!1})},editURL(t){const e={video:"create-video",quiz:"create-quiz",article:"create-article",ebook:"create-e-book",document:"create-document",examresult:"create-ielts-result",videotestimonial:"create-video-testimonial",webinar:"create-webinar",learning_content:"create-learning-content",blog:"create-blog",class_video_clipping:"create-class-video-clipping",writing_task:"create-ielts-writing-task"}[t.type];if(e)return`${YUNOCommon.config.host()}/${e}/?isEdit=${t.id}`},onSelect(t){t&&t.resource_url},gotCourseSuggestions(t){if(this.categories.isLoading=!1,200===t.response?.data?.code){const{data:e}=t.response.data;this.categories.data=e}},fetchCourseSuggestions(t){const e=this,n={apiURL:YUNOCommon.config.generic("contentSearch",t),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(t){return e.gotCourseSuggestions(t)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(t){t.length>2?(this.categories.isLoading=!0,this.fetchCourseSuggestions(t)):this.categories.data=[]}),700)}}),Vue.component("yuno-preview-availability",{props:{data:{type:Object,required:!0},options:{type:Object,required:!0}},template:'\n    <div class="yunoPreviewAvailability">\n      \x3c!-- Days header --\x3e\n      <div class="days-row">\n        <div v-for="(week, index) in data.days" :key="\'day-\'+index" class="dayHeader caption1 bolder">\n          {{ week.day }}\n        </div>\n      </div>\n      \x3c!-- Main grid container --\x3e\n      <div class="grid-container">\n        \x3c!-- Slots grid --\x3e\n        <div class="slots-grid">\n          <template v-for="(time, timeIndex) in timeSlots">\n            <div v-for="(day, dayIndex) in data.days" \n                 :key="\'slot-\'+dayIndex+\'-\'+timeIndex"\n                 class="slot"\n                 :class="{ \'available\': isTimeSlotAvailable(day, time) }">\n            </div>\n          </template>\n        </div>\n        \x3c!-- Time labels --\x3e\n        <div class="time-labels">\n          <div v-for="(time, index) in timeSlots" \n               :key="\'time-\'+index" \n               class="time-label">\n            {{ time }}\n          </div>\n        </div>\n      </div>\n    </div>\n  ',data:()=>({timeSlots:["12:00 AM","12:30 AM","1:00 AM","1:30 AM","2:00 AM","2:30 AM","3:00 AM","3:30 AM","4:00 AM","4:30 AM","5:00 AM","5:30 AM","6:00 AM","6:30 AM","7:00 AM","7:30 AM","8:00 AM","8:30 AM","9:00 AM","9:30 AM","10:00 AM","10:30 AM","11:00 AM","11:30 AM","12:00 PM","12:30 PM","1:00 PM","1:30 PM","2:00 PM","2:30 PM","3:00 PM","3:30 PM","4:00 PM","4:30 PM","5:00 PM","5:30 PM","6:00 PM","6:30 PM","7:00 PM","7:30 PM","8:00 PM","8:30 PM","9:00 PM","9:30 PM","10:00 PM","10:30 PM","11:00 PM","11:30 PM"]}),methods:{convertTo24Hour(t){const[e,n,a]=t.match(/(\d+):(\d+)\s*(AM|PM)/).slice(1);let i=parseInt(e);return"PM"===a&&12!==i&&(i+=12),"AM"===a&&12===i&&(i=0),`${i.toString().padStart(2,"0")}:${n}:00`},timeToMinutes(t){const[e,n]=t.split(":").map(Number);return 60*e+n},isTimeSlotAvailable(t,e){if(!t.is_available||!t.time_slots||0===t.time_slots.length)return!1;const n=this.convertTo24Hour(e),a=this.timeToMinutes(n);return t.time_slots.some((t=>{const e=this.timeToMinutes(t.start),n=this.timeToMinutes(t.end);return a>=e&&a<=n}))}}}),Vue.component("yuno-account-tab",{props:{storage:{type:Object,required:!0}},template:'\n        <div class="generalTab">\n            <div class="row borderBottom m-bottom-larger-times-2 p-bottom-larger-times-2">\n                <div class="col-12 col-md-3">\n                    <h2 class="subtitle1">Login with Google</h2>\n                </div>\n                <div class="col-12 col-md-4">\n                    <p class="onSurfaceVariant m-bottom-small-times-1">{{userInfo.data.email}}</p>\n                    <b-skeleton active v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <a \n                        :href="switchAccountURL" \n                        @click="handelSwitchAccount()" \n                        class="caption1 noBold underline"\n                        v-if="header.success"\n                    >\n                        Switch Google account\n                    </a>\n                </div>\n                <div class="col-12 col-md-5">\n                    <div class="makeGrid vAlignCenter m-bottom-small-times-1 skeletonWidthAuto">\n                        <p \n                            class="onSurfaceVariant"\n                        >\n                            Permissions: Google contacts\n                        </p>\n                        <b-skeleton circle v-if="googleContacts.loading" width="18px" height="18px"></b-skeleton>\n                        <span v-else-if="googleContacts.success && googleContacts.error === null" class="material-icons m-left-small-times-1 greenColor" :style="{fontSize: \'18px\'}">check_circle</span>\n                        <span v-else-if="googleContacts.success" class="material-icons m-left-small-times-1" :style="{fontSize: \'18px\'}">cancel</span>\n                    </div>\n                    <a href="https://myaccount.google.com/permissions" target="_blank" class="caption1 noBold underline">Manage permissions in Google</a>\n                </div>\n            </div>\n            <div class="row borderBottom m-bottom-larger-times-2 p-bottom-larger-times-1" v-if="visibleToRole([\'Instructor\'])">\n                <div class="col-12">\n                    <h2 class="subtitle1 p-bottom-small-times-1">Virtual Classroom</h2>\n                    <p class="subtitle2 noBold onSurfaceVariant p-bottom-small-times-3">\n                        If you\'re teaching live online, a virtual classroom is essential. For each academy\'s classes, you\'ll need a separate connection to a virtual classroom, such as Zoom or Google Meet. Yuno Learning will request authorization to connect with your virtual classroom, enabling us to schedule classes and retrieve recordings and attendance details once the class is over.\n                    </p>\n                    <b-table :data="tableData" :striped="false" :show-head="false">\n                        <template slot-scope="props">\n                            <template v-if="moduleWithoutTab.loading">\n                                <td><b-skeleton active></b-skeleton></td>\n                                <td><b-skeleton active></b-skeleton></td>\n                                <td><b-skeleton active></b-skeleton></td>\n                                <td><b-skeleton active></b-skeleton></td>\n                                <td><b-skeleton active></b-skeleton></td>\n                            </template>\n                            <template v-else-if="moduleWithoutTab.success && moduleWithoutTab.error === null">\n                                <td>\n                                    <div class="caption1 onSurfaceVariant">\n                                        {{ props.row.org_name }}\n                                    </div>\n                                </td>\n                                <td>\n                                    <div class="caption1 onSurfaceVariant noBold makeGrid vAlignCenter">\n                                        <div>\n                                            <img v-if="props.row.type === \'meet\'" :src="wpThemeURL + \'/assets/images/googleMeetIcon.png\'" :alt="props.row.type" class="m-right-small-times-1">\n                                            <img v-if="props.row.type === \'zoom\'" :src="wpThemeURL + \'/assets/images/zoomIcon.png\'" :alt="props.row.type" class="m-right-small-times-1">\n                                            <span v-if="props.row.type === \'free\'" class="material-icons m-right-small-times-1" :style="{fontSize: \'18px\', position: \'relative\', top: \'1px\'}">videocam</span>\n                                        </div>\n                                        {{ props.row.type }}\n                                    </div>\n                                </td>\n                                <td>\n                                    <div class="caption1 onSurfaceVariant noBold">\n                                        {{ props.row.email }}\n                                    </div>\n                                </td>\n                                <td>\n                                    <div class="makeGrid vAlignCenter caption1 onSurfaceVariant noBold" v-if="props.row.permission_url !== \'\' && props.row.connection">\n                                        <a :href="props.row.permission_url" class="m-right-small-times-1 underline">Manage permissions</a>\n                                        <span class="material-icons primaryColor" :style="{fontSize: \'18px\'}">open_in_new</span>\n                                    </div>\n                                </td>\n                                <td>\n                                    <div class="caption1 onSurfaceVariant noBold" v-if="props.row.type !== \'Free\'">\n                                        <template v-if="props.row.connection">\n                                            <a href="#" @click.prevent="disconnectVC(props.row)" class="underline">Disconnect</a>    \n                                        </template>\n                                        <template v-else>\n                                            <b-button\n                                                class="yunoPrimaryCTA wired"\n                                                tag="a"\n                                                :href="props.row.permission_url"\n                                            >\n                                                Connect Virtual Classroom\n                                            </b-button>    \n                                        </template>\n                                    </div>\n                                </td>\n                            </template>\n                            <template v-else-if="moduleWithoutTab.success">\n                                {{ moduleWithoutTab.errorData }}\n                            </template>\n                        </template>\n                    </b-table>\n                </div>\n            </div>\n            <div class="row m-bottom-larger-times-2 p-bottom-larger-times-2">\n                <div class="col-12 col-md-3">\n                    <h2 class="subtitle1">Logout</h2>\n                </div>\n                <div class="col-12 col-md-4">\n                    <b-button\n                        tag="a"\n                        href="/logout/"\n                        class="yunoPrimaryCTA wired withIcon"\n                    >\n                        <span class="material-icons">logout</span> Log out of Yunolearning\n                    </b-button>\n                </div>\n                <div class="col-12 col-md-5">\n                    \n                </div>\n            </div>\n        </div>\n    ',data:()=>({switchAccountURL:"",tableData:[{type:"",email:"",name:"",permission_url:"",connection:"",org_id:0,org_name:"",academy_id:0}]}),computed:{...Vuex.mapState(["userInfo","user","userRole","filterResult","header","googleContacts","moduleWithoutTab"]),wpThemeURL(){return this.$store.state.themeURL}},watch:{"header.data":{handler(t,e){t!==e&&this.manageSwitchAccount()},deep:!0}},async created(){this.fetchContacts(),this.fetchVirtualClassRoom()},destroyed(){},mounted(){0!==this.header.data.length&&this.manageSwitchAccount()},methods:{getQueryParam(t){const e=window.location.search;return new URLSearchParams(e).get(t)},visibleToRole(t){return YUNOCommon.findInArray(t,this.userRole.data)},showToastMessage(t){this.$buefy.toast.open({duration:5e3,message:`${t}`,position:"is-bottom"})},vcDisconnected(t){const e=t?.response?.data;(201===e?.code||e?.message)&&this.showToastMessage(e.message)},disconnectVC(t){let e={user_id:isLoggedIn,type:t.type,connection:!t.connection};const n={apiURL:YUNOCommon.config.user("vcDisconnect",!1),module:"gotData",store:"updateLink",callback:!0,payload:e,headers:{accept:"application/json","content-type":"application/json"},callbackFunc:t=>this.vcDisconnected(t)};this.$store.dispatch("putData",n)},handelSwitchAccount(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear()},manageSwitchAccount(){this.switchAccountURL=this.findItemBySlug(this.header.data,"Switch Account").url},findItemBySlug(t,e){for(const n of t){if(n.label===e)return n;if(n.items){const t=this.findItemBySlug(n.items,e);if(t)return t}}return null},setStorage(){const t=this.$props.storage,e={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))}),100)},onTabChanged(t){this.setStorage()},convertToBoolean(t){if(!t)return!1;return"true"===t.toLowerCase()||"1"===t},findObjectByOrgId:(t,e)=>t.find((t=>t.org&&t.org.id===Number(e)))||null,removeQueryParams(){const t=new URL(window.location.href);["org_id","is_connected"].forEach((e=>{t.searchParams.has(e)&&t.searchParams.delete(e)})),window.history.pushState({path:t.href},"",t.href)},manageVCMessage(){const t=this.getQueryParam("org_id"),e=this.convertToBoolean(this.getQueryParam("is_connected")),n=this.findObjectByOrgId(this.moduleWithoutTab.data,t),a=n&&n.org&&n.org.name?n.org.name:"Organization";if(t){const t=e?"You have successfully connected to the virtual classroom.":"Your consent to connect to the virtual classroom has been revoked.";this.$buefy.dialog.alert({title:a,message:t,confirmText:"Ok",onConfirm:()=>this.removeQueryParams()})}},gotVirtualClassRoom(t){const e=t?.response?.data;if(200===e?.code){const{data:t,count:n}=e;this.tableData=[];for(let e=0;e<t.length;e++){const n=t[e],a={type:"",email:"",name:"",permission_url:"",connection:"",org_id:0,org_name:"",academy_id:0};a.type=n.virtual_classroom?.platform||"",a.email=n.virtual_classroom?.auth?.auth_email||"",a.name=n.academy?.name||"",a.permission_url=n.virtual_classroom?.permission_url||"",a.connection=n.virtual_classroom?.connection||"",a.org_id=n.org?.id||0,a.org_name=n.org?.name||"",a.academy_id=n.academy?.id||0,this.tableData.push(a)}"Instructor"===this.userRole.data&&this.manageVCMessage()}},fetchVirtualClassRoom(){const t={apiURL:YUNOCommon.config.user("virtualClassRoomV4",{loggedinUserID:isLoggedIn}),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:this.gotVirtualClassRoom};this.$store.dispatch("fetchData",t)},gotContacts(t){const e=t?.response?.data;console.log(e)},fetchContacts(){const t={apiURL:YUNOCommon.config.googleContactsAPI(isLoggedIn),module:"gotData",store:"googleContacts",callback:!0,callbackFunc:this.gotContacts};this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-address-tab",{props:{storage:{type:Object,required:!0}},template:'\n        <div>\n            <yuno-address :data="resources"></yuno-address>\n            <b-modal \n                :active.sync="address.modal" \n                :width="600" \n                :can-cancel="[\'escape\', \'x\']" \n                :on-cancel="onNewAddressClose"\n                class="yunoModal lightTheme">\n                    <div class="modalHeader">\n                        <h2 class="modalTitle">\n                            <template v-if="address.isEditMode">\n                                Update Address\n                            </template>\n                            <template v-else>\n                                Add New Address\n                            </template>\n                        </h2>\n                    </div>\n                    <validation-observer ref="newAddressObserver" v-slot="{ handleSubmit }">\n                        <form id="formNewAddress" @submit.prevent="handleSubmit(addNewAddress)">\n                            <div class="modalBody">\n                                <b-field label="Flat, House no., Building, Company, Apartment">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-input :class="classes" v-model="address.payload.flat_house_number"></b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Area, Colony, Street, Sector, Village">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-input :class="classes" v-model="address.payload.street"></b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Landmark">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-input :class="classes" v-model="address.payload.landmark"></b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Pincode">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-input :class="classes" v-model="address.payload.pin_code"></b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Country">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-select \n                                            :class="classes"\n                                            @input="onCountryChange($event)"\n                                            v-model="address.payload.country"\n                                            placeholder="Choose your country">\n                                            <option value="">Choose your country</option>\n                                            <template v-for="(country, countryIndex) in countries.data">\n                                                <option :key="countryIndex" :value="country.id">{{country.name}}</option>\n                                            </template>\n                                        </b-select>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="State" :class="{\'fieldLoading\': states.loading}" v-if="statesList.isActive">\n                                    <template v-if="states.loading">\n                                        <div class="smallLoader withField"></div>\n                                    </template>\n                                    <template v-if="states.success">\n                                        <validation-provider v-if="statesList.isField" :rules="{required:true}" v-slot="{ errors, classes }">\n                                            <b-select \n                                                :class="classes"\n                                                @input="onStateChange($event)"\n                                                v-model="address.payload.state"\n                                                placeholder="Choose your state">\n                                                <option value="">Choose your state</option>\n                                                <template v-for="(state, stateIndex) in states.data">\n                                                    <option :key="stateIndex" :value="state.id">{{state.name}}</option>\n                                                </template>\n                                            </b-select>\n                                            <p class="error">{{errors[0]}}</p>\n                                        </validation-provider>\n                                    </template>\n                                </b-field>\n                                <b-field label="Town/City" :class="{\'fieldLoading\': cities.loading}" v-if="citiesList.isActive">\n                                    <template v-if="cities.loading">\n                                        <div class="smallLoader withField"></div>\n                                    </template>\n                                    <template v-if="cities.success">\n                                        <validation-provider v-if="citiesList.isField" :rules="{required:true}" v-slot="{ errors, classes }">\n                                            <b-select \n                                                :class="classes"\n                                                v-model="address.payload.city"\n                                                placeholder="Choose your city">\n                                                <option value="">Choose your city</option>\n                                                <template v-for="(city, cityIndex) in cities.data">\n                                                    <option :key="cityIndex" :value="city.id">{{city.name}}</option>\n                                                </template>\n                                            </b-select>\n                                            <p class="error">{{errors[0]}}</p>\n                                        </validation-provider>\n                                    </template>\n                                </b-field>\n                                <b-field label="Address Type">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-select \n                                            :class="classes"\n                                            v-model="address.payload.address_type"\n                                            placeholder="Select">\n                                            <option value="">Select</option>\n                                            <option value="home">Home</option>\n                                            <option value="Office">Office</option>\n                                            <option value="other">Other</option>\n                                        </b-select>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <div class="ctaWrapper alignLeft">\n                                    <b-button \n                                        :loading="address.isLoading"\n                                        :disabled="address.isLoading" \n                                        native-type="submit"\n                                        class="yunoPrimaryCTA"\n                                    >\n                                    <template v-if="address.isEditMode">\n                                        Update\n                                    </template>\n                                    <template v-else>\n                                        Add\n                                    </template>\n                                </b-button>   \n                                </div>        \n                            </div>\n                            <div class="modalFooter">\n                                \n                            </div>\n                        </form>\n                    </validation-observer>\n            </b-modal>\n        </div>\n    ',data:()=>({address:{modal:!1,isLoading:!1,isEditMode:!1,payload:{user_id:"",country:"",pin_code:"",flat_house_number:"",street:"",landmark:"",city:"",state:"",address_type:""}},statesList:{isLoading:!1,isActive:!1,isField:!1},citiesList:{isLoading:!1,isActive:!1,isField:!1}}),computed:{...Vuex.mapState(["userInfo","user","filterResult","resources","notifications","countries","states","cities"])},async created(){this.emitEvents()},destroyed(){},mounted(){this.fetchAddress()},methods:{gotCities(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){this.citiesList.isField=!0;let t=this.resources;if(""!==t.data.state){const e=this.address.payload;citiesObj=this.cities.data,currentCity=YUNOCommon.findObjectByKey(citiesObj,"name",t.data.city),null!==currentCity&&(e.city=currentCity.id)}}},fetchCities(t){const e=this,n={apiURL:YUNOCommon.config.cityListAPI(t),module:"gotData",store:"cities",callback:!0,callbackFunc:function(t){return e.gotCities(t)}};this.$store.dispatch("fetchData",n)},gotStates(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){this.statesList.isField=!0;let t=this.resources;if(""!==t.data.state){const e=this.address.payload;statesObj=this.states.data,currentState=YUNOCommon.findObjectByKey(statesObj,"name",t.data.state),null!==currentState&&(e.state=currentState.id,this.onStateChange(currentState.id))}}},fetchStates(t){const e=this,n={apiURL:YUNOCommon.config.stateListAPI(t),module:"gotData",store:"states",callback:!0,callbackFunc:function(t){return e.gotStates(t)}};this.$store.dispatch("fetchData",n)},resetAddress(t){let e=this.resources,n=this.address.payload;if(t)for(let t in n)e.data.hasOwnProperty(t)&&(e.data[t]=n[t]);else for(let t in n)if(n.hasOwnProperty(t))if("user_id"===t)n[t]=isLoggedIn;else if("country"===t){const a=this.countries.data,i=YUNOCommon.findObjectByKey(a,"name",e.data[t]);getCountryID=null!==i?i.id:"",n[t]=getCountryID,null!==i&&this.onCountryChange(getCountryID)}else n[t]=e.data[t]},onNewAddressClose(){this.$refs.newAddressObserver.reset(),this.resetAddress(!1)},onStateChange(t){this.citiesList.isActive=!0,this.citiesList.isField=!1,this.address.payload.city="",this.fetchCities(t)},onCountryChange(t){this.statesList.isActive=!0,this.statesList.isField=!1,this.address.payload.state="",this.address.payload.city="",this.fetchStates(t)},onPostSuccess(t){let e=t.response.data;this.address.isLoading=!1,this.address.modal=!1,this.onNewAddressClose(),201===e.code?(this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom"}),this.fetchAddress()):200===e.code?this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom"}):this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})},addNewAddress(){let t=this.address.payload,e=this.countries.data,n=this.states.data,a=YUNOCommon.findObjectByKey(e,"id",t.country).name,i=YUNOCommon.findObjectByKey(n,"id",t.state).name;t.country=a,t.state=i,t.user_id=isLoggedIn,this.address.isLoading=!0;const o=this,r={apiURL:YUNOCommon.config.updateAddressAPI(),module:"gotData",store:"notifications",payload:t,callback:!0,callbackFunc:function(t){return o.onPostSuccess(t)}};this.$store.dispatch("postData",r)},emitEvents(){Event.$on("onNewAddress",(t=>{this.manageAddress(t)}))},manageAddress(t){t||(this.address.isEditMode=!0),this.address.modal=!0},setStorage(){const t=this.$props.storage,e={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))}),100)},onTabChanged(t){this.setStorage()},gotAddress(t){const e=t?.response?.data;if(this.fetchCountries(),200===e?.code){e.data}else this.resources.errorMsg="You don't have any address yet. Go ahead and add address"},fetchAddress(){const t={apiURL:YUNOCommon.config.addressAPI(isLoggedIn,"detail"),module:"gotData",store:"resources",callback:!0,callbackFunc:this.gotAddress};this.$store.dispatch("fetchData",t)},gotCountries(t){const e=t?.response?.data;if(200===e?.code){e.data;this.resetAddress(!1)}},fetchCountries(){const t={apiURL:YUNOCommon.config.countriesListAPI(),module:"gotData",store:"countries",callback:!0,callbackFunc:this.gotCountries};this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-availability-tab",{props:{storage:{type:Object,required:!0}},template:'\n\t\t<div>\n\t\t  <yuno-availability-v3 :data="resources" :options="{ tab: resources, isSignup: false }"></yuno-availability-v3>\n\t\t</div>\n\t  ',data:()=>({}),computed:{...Vuex.mapState(["userInfo","user","header","timeSlots","resources","filterResult"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){this.fetchAvailability()},methods:{formatTime(t){if(!t)return"";const[e,n]=t.split(":"),a=parseInt(e);return`${a%12||12}:${n} ${a>=12?"PM":"AM"}`},setStorage(){const t=this.$props.storage,e={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))}),100)},onTabChanged(t){this.resources.data=[],this.resources.success=!1,this.resources.error=null,this.setStorage()},normalizeAvailability(t){t.time_slot&&!t.time_slots&&(t.time_slots=t.time_slot.map((e=>({id:"0",startsAt:this.formatTime(e.start.time)||"",endsAt:this.formatTime(e.end.time)||"",start:e.start.time||"",end:e.end.time||"",isActive:t.is_available,slotID:0,isEnds:!0,isExist:!1,isOverlapping:!1,isEndTime:!1})))),t.time_slots.forEach((e=>{if(e.isOverlapping=!1,e.isEndTime=!1,e.isActive=t.is_available,e.start&&e.end){const t=YUNOCommon.findObjectByKey(this.timeSlots.data,"slug",e.start),n=YUNOCommon.findObjectByKey(this.timeSlots.data,"slug",e.end);t&&(e.startsAt=t.label),n&&(e.endsAt=n.label)}e.isEnds=!0,e.isExist=!0})),t.daySlug=t.day?.substring(0,3).toLowerCase()},refineTimeSlot(){this.resources.data.days.forEach(this.normalizeAvailability)},gotTimeSlots(t,e){(e||200===t?.response?.data?.code)&&this.refineTimeSlot()},fetchTimeSlots(){const t={apiURL:YUNOCommon.config.timeSlotsAPI(),module:"gotData",store:"timeSlots",callback:!0,callbackFunc:t=>this.gotTimeSlots(t,!1)};this.$store.dispatch("fetchData",t)},gotAvailability(t){const e=t?.response?.data;if(200===e?.code){const t=e.data,n=t.days;t.hasRecord=n.some((t=>t?.is_available)),n.forEach(this.normalizeAvailability),this.resources.data=t,0===this.timeSlots.data.length?this.fetchTimeSlots(t):this.gotTimeSlots(!1,!0)}},fetchAvailability(){const t={apiURL:YUNOCommon.config.instructor("getInstructorAvailability",{id:isLoggedIn}),module:"gotData",store:"resources",callback:!0,addToModule:!1,callbackFunc:t=>this.gotAvailability(t)};this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-form",{props:{fields:{type:Array,required:!0},payload:{type:Object,required:!0},defaultTaxonomy:{type:Number,required:!0},industryData:{type:Object,required:!1},cancelCTA:{type:Boolean,default:!0},submitCTALabel:{type:String,default:"Submit"}},template:'\n        <div class="yunoFormWrapper">\n            <validation-observer \n                tag="div" \n                class="observer"\n                ref="orgSettingsFormObserver" \n                v-slot="{ handleSubmit, invalid }"\n            >\n                <form @submit.prevent="handleSubmit(initForm)">\n                    <template v-for="(field, i) in fields">\n                        <template v-if="field.type === \'text\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-input\n                                        :class="classes"\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'textarea\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-input\n                                    type="textarea"\n                                    v-model="payload[field.name]"\n                                    :placeholder="field.placeholder"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-input>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'checkbox\'">\n                            <b-field :key="i">\n                                <b-checkbox \n                                    :value="payload[field.name]" \n                                    v-model="payload[field.name]"\n                                >\n                                    {{ field.label}}\n                                </b-checkbox>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'dropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="dropdownChange($event)"\n                                    >\n                                        <template v-if="field.optionValue === null">\n                                            <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option"\n                                        >\n                                            {{ option }}\n                                        </option>\n                                        </template>\n                                        <template v-else>\n                                            <option\n                                                v-for="(option, j) in field.options"\n                                                :key="payload[field.name] + \'-\' + j"\n                                                :value="field.optionValue === \'object\' ? option : option[field.optionValue]"\n                                            >\n                                                {{ option[field.optionLabel] }}\n                                            </option>\n                                        </template>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'googleFontFamilydropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.family"\n                                        >\n                                            {{ option.family }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'multiSelectDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <validation-provider \n                                        tag="div" \n                                        class="fieldWrapper" \n                                        :rules="{\n                                            required: field.isRequired\n                                        }" \n                                        v-slot="{ errors, classes }"\n                                    >\n                                        <b-dropdown\n                                            v-model="field.selected"\n                                            :class="classes"\n                                            multiple\n                                            :disabled="field.disabled"\n                                            aria-role="list"\n                                            @change="multiSelectDropdownChange($event, field)"\n                                        >\n                                            <template #trigger>\n                                                <b-button>\n                                                    <template v-if="field.selected.length">\n                                                        <div class="selected">\n                                                            <template v-for="(subOption, k) in field.selected">\n                                                                {{ subOption.name }}{{ k < field.selected.length - 1 ? \', \' : \'\' }}\n                                                            </template>    \n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="placeholder">{{ field.placeholder }}</div>\n                                                    </template>\n                                                    <div class="material-icons">expand_more</div>    \n                                                </b-button>\n                                            </template>\n                                            <b-dropdown-item \n                                                :value="subOption"\n                                                aria-role="listitem"\n                                                v-for="(subOption, k) in field.options"\n                                            >\n                                                <span>{{ subOption.name }}</span>\n                                            </b-dropdown-item>\n                                        </b-dropdown>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'groupDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="industry"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="groupDropdownChange($event, field)"\n                                    >\n                                        <optgroup \n                                            :label="option.label"\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                        >\n                                            <option\n                                                v-for="(subOption, k) in option.sub_industry"\n                                                :key="k"\n                                                :value="subOption"\n                                            >\n                                                {{ subOption.label }}\n                                            </option>\n                                        </optgroup>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'upload\'">\n                            <b-field :label="field.label" :key="i" class="uploadField">\n                                <p class="helper">{{ field.placeholder }}</p>\n                                <b-upload\n                                    v-model="payload[field.name]"\n                                    accept="image/*"\n                                    class="file-label"\n                                >\n                                    <span class="file-cta">\n                                        <span class="material-icons-outlined">file_upload</span>\n                                        <span class="file-label">{{ field.cta }}</span>\n                                    </span>\n                                    <span class="file-name" v-if="payload[field.name]">\n                                        {{ payload[field.name].name }}\n                                    </span>\n                                </b-upload>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'colorpicker\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-colorpicker\n                                    :color-formatter="(color) => color.toString(\'hex\')"\n                                    representation="square"\n                                    @input="updateColor($event, field.name)"\n                                    :value="payload[field.name]"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-colorpicker>\n                            </b-field>\n                        </template>\n                    </template>\n                    <div class="ctaWrapper">\n                        <b-button\n                            v-if="cancelCTA"\n                            native-type="reset"\n                            @click="clearForm"\n                            class="yunoPrimaryCTA wired fat">\n                            Cancel\n                        </b-button>    \n                        <b-button\n                            native-type="submit"\n                            :loading="form.isLoading"\n                            :disabled="form.isLoading"\n                            class="yunoSecondaryCTA fat">\n                            {{ submitCTALabel }}\n                        </b-button>    \n                    </div>\n                </form>\n            </validation-observer>\n        </div>\n    ',data:()=>({industry:null,defaultPayload:[]}),computed:{...Vuex.mapState(["user","categoryTaxonomy","form"])},watch:{industryData:{handler(t,e){t!==e&&(this.industry=t)},deep:!0}},async created(){},destroyed(){},mounted(){this.init()},methods:{updateColor(t,e){this.form.payload[e]=t.toString("hex")},init(){this.defaultPayload=JSON.parse(JSON.stringify(this.form.payload))},clearForm(){this.$refs.orgSettingsFormObserver.reset(),this.form.payload=JSON.parse(JSON.stringify(this.defaultPayload))},jsonToFormData(t){const e=new FormData;for(let n in t)e.append(n,t[n]);return e},initForm(){this.$emit("submitForm",this.form.payload)},dropdownChange(t){},groupDropdownChange({parent:t,slug:e},n){"sub_industry"===n.name&&(this.form.payload.industry=t,this.form.payload.sub_industry=e)},multiSelectDropdownChange(t,e){this.form.payload[e.name]=t}}}),Vue.component("yuno-notifications-tab",{props:{storage:{type:Object,required:!0}},template:'\n        <div>\n            <div class="tabsHeader noFlex">\n                <p class="description">We only send important notifications to your various devices as listed below. We recommend that you keep all of them on so you can enjoy the best possible learning experience</p>\n            </div>\n            <yuno-notifications :data="resources"></yuno-notifications>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userInfo","user","header","resources","filterResult"]),wpThemeURL(){return this.$store.state.themeURL}},watch:{},async created(){this.fetchNotifications()},destroyed(){},mounted(){},methods:{setStorage(){const t=this.$props.storage,e={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))}),100)},onTabChanged(t){this.resources.data=[],this.resources.success=!1,this.resources.error=null,this.setStorage()},getNotifications(t){let e=this.resources,n=e.payload;n.user_id=isLoggedIn,n.whatsapp=e.data.whatsapp,n.email=e.data.email},gotNotifications(t){const e=t?.response?.data;if(200===e?.code){this.getNotifications(t);const n=e.data,a=n.channels;n.dump=JSON.stringify(a),this.resources.data=n}},fetchNotifications(){this.resources.payload={user_id:"",whatsapp:"",email:""};const t={apiURL:YUNOCommon.config.notificationListAPI(isLoggedIn),module:"gotData",store:"resources",callback:!0,addToModule:!1,callbackFunc:this.gotNotifications};this.$store.dispatch("fetchData",t)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",is_not:"Value should be defferent then previous one"}};YUNOCommon.assignVValidationObj(validationMsg),YUNOAccount.account(),YUNONotifications.notifications(),YUNOAddress.address(),YUNOAvailability.availability(),YUNOAvailabilityPreview.availabilityPreview(),YUNOTable.table(),Vue.component("yuno-settings",{template:'\n        <yuno-page-grid\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="true"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <div class="mainHeader">\n                        <h1 class="headline5">{{ pageHeader.title }}</h1>\n                    </div>\n                    <yuno-tabs-v2\n                        :destroyOnHide="true"\n                        @tabChange="tabChange"\n                    >\n                        <template v-slot:account>\n                            <yuno-account-tab\n                                @tabChanged="onTabChanged"\n                                ref="yunoAccount" \n                                :storage="storage"\n                            >\n                            </yuno-account-tab>\n                        </template>\n                        <template v-slot:userInfo>\n                            <yuno-user-info\n                                @tabChanged="onTabChanged"\n                                ref="yunoUserInfo" \n                                :storage="storage"\n                            >\n                            </yuno-user-info>\n                        </template>\n                        <template v-slot:address>\n                            <yuno-address-tab\n                                @tabChanged="onTabChanged"\n                                ref="yunoAddress"\n                                :storage="storage"\n                            >\n                            </yuno-address-tab>\n                        </template>\n                        <template v-slot:availability>\n                            <yuno-availability-tab\n                                @tabChanged="onTabChanged"\n                                ref="yunoAvailability"\n                                :storage="storage"\n                            >\n                            </yuno-availability-tab>\n                        </template>\n                        <template v-slot:notifications>\n                            <yuno-notifications-tab\n                                @tabChanged="onTabChanged"\n                                ref="yunoNotifications"\n                                :storage="storage"\n                            >\n                            </yuno-notifications-tab>\n                        </template>\n                        \n                    </yuno-tabs-v2>\n                </div>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({pageHeader:{title:"Account Settings"},authorizedContentSearch:["content-admin"],isMiniSidebar:!1,statesList:{isLoading:!1,isActive:!1,isField:!1},citiesList:{isLoading:!1,isActive:!1,isField:!1},storage:{name:"settings",version:1}}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","loader","settings","countries","states","cities","timeSlots","instructorAvailabilityGrid","filterResult"]),emptyStates:()=>({state:"notAuthorized"}),getCountries:()=>YUNOCommon.countriesData(),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){let t="";return t=!this.user.isLoggedin||this.userInfo.success,t}},isUserAuthorized:{get(){let t="";return t=!!this.user.isLoggedin,t}}},async created(){},mounted(){},methods:{tabChange(t){setTimeout((()=>{const e=[this.$refs.yunoAccount,this.$refs.yunoUserInfo,this.$refs.yunoAddress,this.$refs.yunoAvailability,this.$refs.yunoNotifications][t];e&&e.onTabChanged(t)}),10)},onTabChanged(){},setupTabs(t,e){function n(t,e,n,a){return{label:t,slug:e,isActive:!1,isVisible:a,class:n}}const a=this.storage,i=sessionStorage.getItem(a.name+"V"+a.version);let o="";o=i?JSON.parse(i).currentTab:0,this.filterResult.tabs={activeTab:o,wrapperClass:t,items:[n("Account","account","yunoAccount",!0),n("Locale","userInfo","yunoUserInfo",!0),n("Address","address","yunoAddress",!0),n("Availability","availability","yunoAvailability","Instructor"===e),n("Notifications","notifications","yunoNotifications","Instructor"===e||"Learner"===e)]}},onUserInfo(t){this.setupTabs("tabsAvailable",t.role)},onMini(t){this.isMiniSidebar=t}}}),Vue.component("yuno-user-info",{props:{storage:{type:Object,required:!0}},template:'\n        <div class="row">\n            <div class="col-12 col-md-6">\n                <template v-if="subform.loading">\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="100px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                </template>\n                <template v-if="subform.success">\n                    <yuno-form\n                        :fields="form.fields"\n                        :payload="form.payload"\n                        :defaultTaxonomy="defaultTaxonomy"\n                        :industryData="industry"\n                        @submitForm="submitForm"\n                        :cancelCTA="false"\n                        submitCTALabel="Update"\n                    >\n                    </yuno-form>  \n                </template>\n            </div>\n        </div>\n    ',data:()=>({defaultTaxonomy:0,industry:null}),computed:{...Vuex.mapState(["userInfo","user","filterResult","form","subform"])},async created(){},destroyed(){},mounted(){this.fetchModules()},methods:{gotCountries(t,e){const{code:n,data:a}=t.response?.data||{};e.loading=!1,200===n&&(e.options=a)},fetchCountry(t){this.fetchData(t,YUNOCommon.config.user("countries",!1),this.gotCountries)},gotCurrency(t,e){const{code:n,data:a}=t.response?.data||{};e.loading=!1,200===n&&(e.options=a)},fetchCurrency(t){this.fetchData(t,YUNOCommon.config.user("currencies",!1),this.gotCurrency)},gotLanguage(t,e){const{code:n,data:a}=t.response?.data||{};e.loading=!1,200===n&&(e.options=a)},fetchLanguage(t){this.fetchData(t,YUNOCommon.config.user("languages",!1),this.gotLanguage)},gotTimezone(t,e){const{code:n,data:a}=t.response?.data||{};e.loading=!1,200===n&&(e.options=a)},fetchTimezone(t){this.fetchData(t,YUNOCommon.config.user("timezones",!1),this.gotTimezone)},fetchData(t,e,n){const a=YUNOCommon.findObjectByKey(this.form.fields,"name",t);a.loading=!0;const i={apiURL:e,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:t=>n(t,a)};this.dispatchData("fetchData",i)},fetchInlineDataData(t){switch(t){case"country":this.fetchCountry(t);break;case"currency":this.fetchCurrency(t);break;case"language":this.fetchLanguage(t);break;case"timezone":this.fetchTimezone(t)}},inlineData(t){t.forEach((t=>{this.fetchInlineDataData(t)}))},gotRegion(t){const{code:e,data:n}=t.response?.data||{};if(200===e){["country","currency","language","timezone"].forEach((t=>{""!==n[t].code&&""!==n[t]||(n[t]=null)})),this.form.payload=n,this.inlineData(["country","currency","language","timezone"])}},fetchRegion(){const t={apiURL:YUNOCommon.config.user("region",{loggedinUserID:isLoggedIn}),module:"gotData",store:"subform",callback:!0,addToModule:!1,callbackFunc:t=>this.gotRegion(t)};this.dispatchData("fetchData",t)},dispatchData(t,e){this.$store.dispatch(t,e)},fetchModules(){this.fetchRegion(),this.setupForm()},showToastMessage(t){this.$buefy.toast.open({duration:5e3,message:`${t}`,position:"is-bottom"})},formPosted(t){this.form.isLoading=!1;const e=t?.response?.data;(201===e?.code||e?.message)&&this.showToastMessage(e.message)},submitForm(t){this.form.isLoading=!0;const e={apiURL:YUNOCommon.config.user("region",{loggedinUserID:isLoggedIn}),module:"gotData",store:"form",payload:t,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:t=>this.formPosted(t)};this.dispatchData("postData",e)},setStorage(){const t=this.$props.storage,e={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))}),100)},onTabChanged(t){this.subform.data=[],this.subform.success=!1,this.subform.error=null,this.setStorage()},setupForm(t,e){const n={isRequired:!1,isDisabled:!1,isLoading:!1},a=[{label:"Country",placeholder:"Select country",type:"dropdown",name:"country",optionLabel:"name",optionValue:"object",options:[],loading:!1,...n},{label:"Time zone",placeholder:"Select time zone",type:"dropdown",name:"timezone",optionLabel:null,optionValue:null,options:[],loading:!1,...n},{label:"Currency",placeholder:"Select currency",type:"dropdown",name:"currency",optionLabel:"name",optionValue:"object",options:[],loading:!1,...n},{label:"Language",placeholder:"Select language",type:"dropdown",name:"language",optionLabel:"name_in_english",optionValue:"object",options:[],loading:!1,...n}];this.form.fields=a,this.form.payload={country:null,timezone:null,currency:null,language:null}}}});