const gulp = require('gulp'),
	gp_concat = require('gulp-concat'),
 	gp_rename = require('gulp-rename'),
    terser = require('terser');
	gulpTerser = require('gulp-terser'),
	uglifycss = require('gulp-uglifycss');

gulp.task('classScheduleV2JS', function () {
    const fileName = "classScheduleV2",
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/assets/js/lib/moment.js',
            ''+ path +'/assets/js/lib/vee-validate.min.js',
            ''+ path +'/components/pageGrid/*.js',
            ''+ path +'/components/headerWithLeftMenu/*.js',
            ''+ path +'/pages/'+ fileName +'/*.js'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.js`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}Combined.js`))
    .pipe(gulp.dest(saveToFolder))
    .pipe(gp_rename(renameFile))
    .pipe(gulpTerser({}, terser.minify))
    .pipe(gulp.dest(saveToFolder));
});

gulp.task('classScheduleV2CSS', function () {
    const fileName = "classScheduleV2",
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/components/pageGrid/*.css',
            ''+ path +'/components/headerWithLeftMenu/headerWithLeftMenu.css',
            ''+ path +'/pages/'+ fileName +'/*.css'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.css`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}.min.css`))
    .pipe(uglifycss())
    .pipe(gulp.dest(saveToFolder))
});

gulp.task('classScheduleV2', function() {
    const pages = "wp-content/themes/yunolearning-child/pages/",
        components = "wp-content/themes/yunolearning-child/components/";

    gulp.watch([
        ''+ components +'headerWithLeftMenu/*.js',
        ''+ components +'pageGrid/*.js',
        ''+ pages +'classScheduleV2/*.js'
    ], gulp.series('classScheduleV2JS'));

    gulp.watch([
        ''+ components +'headerWithLeftMenu/headerWithLeftMenu.css',
        ''+ components +'pageGrid/*.css',
        ''+ pages +'classScheduleV2/*.css'
    ], gulp.series('classScheduleV2CSS'));
});
