@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

%font-color-dark {
  color: $onSurface;
}

%font-color-dark-variant {
  color: $onSurfaceVariant;
}

%font-color-dark-variant-2 {
  @include setFontColor($primaryCopyColor, 0.38);
}

%placegolder {
  color: #201a1947;
}

%headline5 {
  @include setFont($headline5, 28px, 500, 0);
}

%body1 {
  @include setFont($body1, 28px, 500, 0);
}
%body2 {
    @include setFont($body2, 16px, 400, 0);
  }

%smallCaption {
  @include setFont($caption1, 16px, 400, 0);
}

#app {
  @extend %font-color-dark;

  .mainHeader {
    padding: $gap15 $gap15 0;
    margin-bottom: $gapLargest;

    h1 {
      @extend %headline5;
      margin: 14px 0 0 20px;

      @media (min-width: 768px) {
        margin: 0;
      }
    }
  }

  .yunoTabsWrapper {
    z-index: 7 !important;
  }

  .yunoFormWrapper {
    padding-bottom: $gapLargest;

    .ctaWrapper {
      display: flex;
      gap: $gap15;
      margin-top: $gap15;
    }

    .addInstructors {
      @extend %body2;
      color: $secondaryColor;
      cursor: pointer;
    }
  }

  .demoInstructorTagInput {
    .taginput-container {
      flex-direction: row !important;
      .tag {
        margin: 5px 5px 0 !important;
      }
    }
  }

  .addInstructor {
    .tagInputWrapper {
      .ctaWrapper {
        justify-content: end !important;
        gap: 15px !important;
      }
    }
  }
  .demoClass {
    .tab-content {
      padding: 1rem 0 0 0 !important;
    }
  }

  .field {
    .label {
      @extend %body1;
      @extend %font-color-dark;
    }

    .control {
      input[type="text"] {
        border-color: #e6e6e6;
        height: 40px;
      }

      textarea {
        border-color: #e6e6e6;
      }

      .select {
        select {
          border-color: #e6e6e6;
        }
      }

      &.colorpicker {
        .button {
          height: 40px;
          border-radius: 4px;
        }
      }
    }

    &.uploadField {
      .field {
        &.has-addons {
          display: block;
        }
      }

      .helper {
        @extend %smallCaption;
        @extend %font-color-dark-variant;
      }

      .upload {
        margin-top: $gap15;

        .file-cta {
          background-color: $primary;
          color: white;

          .material-icons-outlined {
            font-size: 18px;
            margin-right: $gapSmaller;
          }
        }
      }
    }

    .dropdown {
      width: 100%;

      .dropdown-trigger {
        width: 100%;
      }

      .button {
        width: 100%;
        justify-content: flex-start;
        border-color: #e6e6e6;
        height: 40px;

        > span {
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: space-between;
        }

        .selected {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            margin-right: $gapSmaller;

            &::after {
              content: ",";
            }

            &:last-child {
              &::after {
                content: "";
              }
            }
          }
        }
      }

      .dropdown-menu {
        width: 100%;

        .dropdown-item {
          &:hover {
            text-decoration: none;
            background-color: $hover;
          }

          &.is-active {
            background-color: $active2;
          }
        }
      }

      .placeholder {
        color: #8080808c;
      }

      &.invalid {
        .button {
          border-color: red;
        }
      }
    }
  }

  .categoryTaxonomyWrapper {
    padding-top: $gapLargest;
  }

  .categoryTaxonomy {
    background: $whiteBG;
    border: 1px solid $grey;
    border-top: 0;

    &:first-child {
      border-top: 1px solid $grey;
    }

    .collapse-trigger {
      display: block;
      padding: 0;

      .b-radio {
        margin: 0;
      }
    }

    .collapseHeader {
      position: relative;

      &.menuDown {
        box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
      }

      .b-radio {
        display: flex;
        width: 100%;
        padding: $gap15 $gap15;

        + .error {
          display: none;
          margin: 0;
          padding: 0 0 $gap15 $gapLargest + $gap15;
        }

        &.invalid {
          + .error {
            display: block;
          }
        }
      }

      .fa {
        position: absolute;
        right: $gap15;
        top: calc(50% - 8px);
      }
    }

    .collapse-content {
      display: block;
      padding: $gap15 $gapLargest;
    }

    .collapse {
      margin-bottom: $gap15;

      &:last-child {
        margin-bottom: 0;
      }

      .collapse-trigger {
        position: relative;
        padding-left: 18px;

        .fa {
          position: absolute;
          left: 0;
          top: 3px;
        }
      }

      .collapse-content {
        padding-bottom: 0;
        position: relative;

        &:before {
          content: "";
          width: 1px;
          height: 100%;
          background-color: $grey;
          position: absolute;
          left: 4px;
          top: 0;
        }
      }

      .sub2Content {
        .field {
          display: flex;
          align-items: center;
          position: relative;

          &:before {
            content: "";
            width: 100%;
            height: 1px;
            background-color: $grey;
            position: absolute;
            left: -25px;
            top: 10px;
            z-index: 1;
          }

          &:after {
            content: "";
            background: $whiteBG;
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 2;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .b-checkbox {
            position: relative;
            z-index: 3;
          }
        }
      }

      .trigger {
        .field {
          display: flex;
          align-items: center;
        }
        .b-checkbox {
          margin: 0;
        }
      }
    }
  }
  .academyForm {
    .yunoModal.demoClass {
      z-index: 100000 !important;

      .modal-background {
        background-color: rgba(0, 0, 0, 0.5) !important;
      }

      .modal-content {
        border: 1px solid #e6e6e6 !important;
        position: relative;
        background: white;
        border-radius: 8px;
        padding: 20px;
        max-width: 500px;
        margin: 0 auto;
      }

      .modalTitle {
        @extend %headline5;
        color: $onSurface;
        margin-bottom: 16px;
      }

      .modal-close {
        top: 20px !important;
        right: 20px !important;
      }

      .modalBody {
        padding: 0;
        .description {
          color: $onSurfaceVariant;
          font-size: 14px;
          margin-bottom: 24px;
        }

        .instructorFields {
          .field {
            margin-bottom: 20px;

            .label {
              font-weight: 500;
              margin-bottom: 8px;
            }

            .taginput {
              .taginput-container {
                border-color: #e6e6e6;
                border-radius: 4px;
                flex-direction: row !important;

                .tag {
                  margin: 6px 5px 0 !important;
                }

                input {
                  height: 38px;
                }
              }
            }
          }
        }
      }

      .modalFooter {
        margin-top: 24px;

        .ctaWrapper {
          gap: 10px;

          .primaryCTA {
            padding: 7px 16px;
            border-radius: 4px;
            border: 1px solid $primary;
            background-color: $primary;
            line-height: normal;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            min-width: 80px;

            &:hover {
              background-color: darken($primary, 10%);
            }
          }

          .secondaryCTA {
            padding: 7px 16px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            background-color: #fff;
            line-height: normal;
            font-size: 14px;
            font-weight: 500;
            min-width: 80px;
            color: $onSurface;

            &:hover {
              border-color: $primary;
            }
          }
        }
      }
    }
  }
}

