@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .fontColorDark {
        color: $onSurface;
    }
    
    .fontColorDarkVariant {
        color: $onSurfaceVariant;
    }
    
    .fontColorDarkVariant2 {
        @include setFontColor($primaryCopyColor, 0.38);
    }

    .yunoTabsWrapper {
        position: sticky;
        top: 0;
        background-color: #FFF;
        z-index: 2;

        &.tabsNotAvailable {
            .b-tabs.yunoTabsV5 {
                > .tabs {
                    display: none;
                }
            }

            .tab-content {
                padding-top: 0;
            }

            .mainHeader {
                margin-top: 0;
            }
        }

        &.tabsAvailable {
            .mainHeader {
                margin-top: 0;
            }
        }
    }
    
    .yunoTabsV5 {
        margin-top: $gap15;
    
        ul {
            li {
                a {
                    @extend .fontColorDarkVariant2;
                    font-size: $body2;
                }
    
                &.is-active {
                    a {
                        @extend .fontColorDark;
                        border-bottom-color: $onSurface;
                    }
                }
            }
        }
    
        &.noContent {
            .tab-content {
                display: none;
            }
        }
    }
}

