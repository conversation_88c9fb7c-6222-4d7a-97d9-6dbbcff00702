<?php

use Google\Client as Google_Client;
use Google\Service\Calendar as Google_Service_Calendar;
use Google\Service\Calendar\FreeBusyRequest as Google_Service_Calendar_FreeBusyRequest;
// use DateTime;
// use DateTimeZone;

require_once get_stylesheet_directory() . '/inc/vendor/autoload.php';
class GoogleCalendarApi
{
	public function GetAccessTokenOld($client_id, $redirect_uri, $client_secret, $code) {	
		//error_log("raman test: ".$client_id." === ".$redirect_uri." === ".$client_secret." === ".$code);
		$url = 'https://accounts.google.com/o/oauth2/token';			
		
		$curlPost = 'client_id=' . $client_id . '&redirect_uri=' . $redirect_uri . '&client_secret=' . $client_secret . '&code='. $code . '&grant_type=authorization_code';
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);		
		curl_setopt($ch, CURLOPT_POST, 1);		
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);	
		$data = json_decode(curl_exec($ch), true);
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);		
		if($http_code != 200) 
			throw new Exception('Error : Failed to receieve access token');
			
		return $data;
	}

	public function GetUserCalendarTimezone($access_token) {
		$url_settings = 'https://www.googleapis.com/calendar/v3/users/me/settings/timezone';
		
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_settings);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token));	
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);	
		$data = json_decode(curl_exec($ch), true); //echo '<pre>';print_r($data);echo '</pre>';
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);		
		if($http_code != 200) 
			throw new Exception('Error : Failed to get timezone');

		return $data['value'];
	}

	public function GetCalendarsList($access_token) {
		$url_parameters = array();

		$url_parameters['fields'] = 'items(id,summary,timeZone)';
		$url_parameters['minAccessRole'] = 'owner';

		$url_calendars = 'https://www.googleapis.com/calendar/v3/users/me/calendarList?'. http_build_query($url_parameters);
		
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_calendars);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token));	
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);	
		$data = json_decode(curl_exec($ch), true); //echo '<pre>';print_r($data);echo '</pre>';
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		echo "<pre>";
		print_r($data);	
		//if($http_code != 200) 
			//throw new Exception('Error : Failed to get calendars list');

		return $data['items'];
	}

	public function CreateCalendarEvent($calendar_id, $summary, $eventStartTime, $eventEndTime, $event_timezone, $access_token, $class_exerpt, $classId, $ClassSchdulelearnerArray) {
		global $wpdb;
		//$access_token = $_SESSION["access_token"];
		$instructorId = get_post_meta($classId, '_ecp_custom_13', true);	
		$access_token = get_user_meta($instructorId,'yuno_user_access_token', true);
		//error_log("CreateCalendarEventaccess_token== ". $access_token);
		// $attendeesList = $wpdb->get_results("SELECT email FROM wp_enrolled WHERE classId = $classId", ARRAY_A);
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id .'/events/?conferenceDataVersion=1&&maxAttendees=50&&sendNotifications=false&&sendUpdates=none';
		$curlPost['summary'] = $summary;
		$curlPost['conferenceDataVersion'] = '1';
		$curlPost['start'] = array('dateTime' => $eventStartTime, 'timeZone' => $event_timezone);
		$curlPost['end'] = array('dateTime' => $eventEndTime, 'timeZone' => $event_timezone);
		$curlPost['description'] = $class_exerpt;
		$curlPost['location'] = "".$classId."";
		$curlPost['attendees'] = $ClassSchdulelearnerArray;
		$curlPost['conferenceData'] = array ('createRequest' =>  array ('requestId' => $classId),);
		$curlPost['hangoutLink'] = "";
		$curlPost['visibility'] = "confidential";
		$curlPost['guestsCanInviteOthers'] = false;
		$curlPost['guestsCanSeeOtherGuests'] = false;
		$curlPost['transparency'] = "transparent";
		//$curlPost['reminders'] = array('useDefault' => FALSE,'overrides' => array(array('method' => 'popup', 'minutes' => 10),array('method' => 'popup', 'minutes' => 90),),);
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);		
		curl_setopt($ch, CURLOPT_POST, 1);		
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));	
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($curlPost));	
		$data = json_decode(curl_exec($ch), true);
		
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		//error_log("http_codeResponse==". json_encode($data));
		if($http_code != 200) {
			$this->GetAccessToken($instructorId);
			$this->CreateCalendarEvent($calendar_id, $summary, $eventStartTime, $eventEndTime, $event_timezone, $access_token, $class_exerpt, $classId, $ClassSchdulelearnerArray);
			throw new Exception("$http_code : Failed to insert event ($classId) in calendar");
		}
		$res["ID"] = '';
		$res["WebinarID"] = $data['id'];
		$res["Recording_Share_Url"] = $data['hangoutLink'];
		$res["Recording_video_Url"] = $data['hangoutLink'];
		$res["Recording_audio_Url"] = $data['htmlLink'];
		$res["Uuid"] = $data['iCalUID'];
		$res["Status"] = "completed";
		$res["accountID"] = $data['conferenceData']['signature'];
		$res["HostID"] = $data['etag'];
		$results = $wpdb->insert('wp_zoom_recordings',$res); 		

		error_log("**********.0.2 CreateCalendarEvent , results -----  ".date("Y-m-d H:i:s")." === ".json_encode($results)."\n\n", 3, ABSPATH."error-logs/ClassRecording.log");       
		error_log("**********.0.3 CreateCalendarEvent , data -----  ".date("Y-m-d H:i:s")." === ".json_encode($data)."\n\n", 3, ABSPATH."error-logs/ClassRecording.log");       

		return $data;
	}

	public function UpdateCalendarEvent($calendar_id, $summary, $eventStartTime, $eventEndTime, $event_timezone, $access_token, $class_exerpt, $classId, $ClassSchdulelearnerArray) {
		$instructorId = get_post_meta($classId, '_ecp_custom_13', true);	
		$event_id = get_post_meta($classId, 'YunoClassGoogleCalendarId', true);	
		$access_token = get_user_meta($instructorId,'yuno_user_access_token', true);
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id . '/events/' . $event_id.'?conferenceDataVersion=1&&maxAttendees=50&&sendNotifications=false&&sendUpdates=none';
		$curlPost['summary'] = $summary;
		$curlPost['conferenceDataVersion'] = '1';
		$curlPost['start'] = array('dateTime' => $eventStartTime, 'timeZone' => $event_timezone);
		$curlPost['end'] = array('dateTime' => $eventEndTime, 'timeZone' => $event_timezone);
		$curlPost['description'] = $class_exerpt;
		$curlPost['location'] = "".$classId."";
		$curlPost['attendees'] = $ClassSchdulelearnerArray;
		$curlPost['conferenceData'] = array ('createRequest' =>  array ('requestId' => $classId),);
		$curlPost['hangoutLink'] = "";
		$curlPost['visibility'] = "confidential";
		$curlPost['guestsCanInviteOthers'] = false;
		$curlPost['guestsCanSeeOtherGuests'] = false;
		$curlPost['transparency'] = "transparent";
		//$curlPost['reminders'] = array('useDefault' => FALSE,'overrides' => array(array('method' => 'popup', 'minutes' => 10),array('method' => 'popup', 'minutes' => 90),),);
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);		
		curl_setopt($ch, CURLOPT_POST, 1);	
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');	
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));	
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($curlPost));	
		$data = json_decode(curl_exec($ch), true);
		
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		//error_log("http_codeResponse==". json_encode($data));
		if($http_code != 200) {
			$this->GetAccessToken($instructorId);
			$this->UpdateCalendarEvent($calendar_id, $summary, $eventStartTime, $eventEndTime, $event_timezone, $access_token, $class_exerpt, $classId, $ClassSchdulelearnerArray);
			throw new Exception("$http_code : Failed to insert event ($classId) in calendar");
		}
		return $data;	
	}

	public function DeleteCalendarEvent($event_id, $access_token, $instructorId) {
		$calendar_id = 'primary';
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id . '/events/' .$event_id;
		$access_token = get_user_meta($instructorId, 'yuno_user_access_token', true);
		//error_log('message access_token= '.$access_token);
		//error_log('message event_id= '.$event_id);
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);		
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');		
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));		
		$data = json_decode(curl_exec($ch), true);
		if (is_array($data) || is_object($data)) {
			foreach($data as $value){
				$statusCode=$value['code'];
				$message=$value['message'];
			}
		}
		$data['code'] = $statusCode;
		$data['message'] = $message;
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);			
	}	

	public function getGoogleMeetReports($classCalendarId, $instructorId) {
		global $wpdb;
		$userKey = 'all';
		$applicationName = 'meet';
		//$calendar_event_id = 'rn9v1dlmo7op6hofmmikkjt2tk';
		$url_events = 'https://admin.googleapis.com/admin/reports/v1/activity/users/' . $userKey . '/applications/'.$applicationName.'/?filters=calendar_event_id=='.$classCalendarId;
		
		//error_log("classCalendarIdGoogleMeetReports==". $classCalendarId);
		$access_token = get_user_meta($instructorId, 'yuno_user_access_token', true);

		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token));	
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);	
		$data = json_decode(curl_exec($ch), true); //echo '<pre>';print_r($data);echo '</pre>';
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		if($http_code != 200) {
			//error_log("http_codeResponse==". json_encode($data));
			$data = $data;
			$this->GetAccessToken($instructorId);
			$this->getGoogleMeetReports($classCalendarId, $instructorId);
			//throw new Exception("$http_code : Failed to insert getGoogleMeetReports");
		}
		return $data;
	}
	/**********************************************************************************
	********************* Start: Get New Access Token if Its Expired *****************
	***********************************************************************************/
	public function getDriveLink($classCalendarId, $instructorId) {  
		global $wpdb;
		$attendeesList = $wpdb->get_results("SELECT * FROM wp_postmeta WHERE meta_value = '".$classCalendarId."'", ARRAY_A);
		foreach ($attendeesList as $key => $value) {
            $post_id = $value['post_id'];  
        }
		$class_title = get_the_title($post_id);	
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/primary/events/' .$classCalendarId;		
		$access_token = get_user_meta($instructorId, 'yuno_user_access_token', true);
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token));	
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);	
		$data = json_decode(curl_exec($ch), true); //echo '<pre>';print_r($data);echo '</pre>';
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		if($http_code != 200) {
			//error_log("http_codeResponse==". json_encode($data));
			$data = $data;
			$this->GetAccessToken($instructorId);
			$this->getGoogleMeetReports($classCalendarId, $instructorId);
			//throw new Exception("$http_code : Failed to insert getGoogleMeetReports");
		}else{ 
			$fileId = '';
			$attachment = $data;
			if($attachment['attachments']){		
				foreach ($attachment['attachments'] as $key => $value) {
					if($value['mimeType'] == "video/mp4"){
						$fileId = $value['fileId'];						
						$file_size = '';
						$description='';
						$google_vc = get_user_meta($instructorId,'google_meet_vc',true);
						if($google_vc == "1"){						
						uploadVideoToVimeo("https://docs.google.com/uc?id=".$fileId."&export=download",$class_title, $file_size, $classCalendarId,$description);
						}
					}
				}
		
				// $drive_url = "https://www.googleapis.com/drive/v2/files/".$fileId;
				// $ch = curl_init();		
				// curl_setopt($ch, CURLOPT_URL, $drive_url);		
				// curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	
				// curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
				// curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token));	
				// curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);	
				// $data = json_decode(curl_exec($ch), true); 
				// $http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);
			}else{
				return new WP_REST_Response("Attachment not ready.", 404);
			}
		}
		return $data;
	}
	public function addAttendeeInCalendarEvent($calendar_id, $classCalendarId, $email, $token, $class_id) {
		global $wpdb;
		$oldAttendees = [];
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id . '/events/' . $classCalendarId.'/?maxAttendees=50&&sendNotifications=false&&sendUpdates=none';
		$access_token = $token;	
		$oldAttendees = $wpdb->get_results("SELECT email FROM wp_enrolled WHERE classId = $class_id", ARRAY_A);
		
		$oldAttendees[]["email"] = $email;
		$curlPost['attendees'] = $oldAttendees;
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);		
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');		
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));	
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($curlPost));	
		$data = json_decode(curl_exec($ch), true);
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		//error_log("http_codeResponseaddAttendee==". json_encode($data));
		if($http_code != 200) {
			// $this->GetAccessToken($instructorId);
			$this->addAttendeeInCalendarEvent($calendar_id, $classCalendarId, $email, $token, $class_id);
			throw new Exception("$http_code : Failed to insert addAttendeeInCalendarEvent");
		}
		return $data;
	}	
	
	public function ListCalendarEvent($calendar_id, $access_token) {
		$url_events = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id . '/events';
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $url_events);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);				
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));	

		$data = json_decode(curl_exec($ch), true);
		echo "<pre>";
		print_r($data);
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	

		
	}
	public function ListAcl($calendar_id, $access_token) {
		/*$url_parameters=array (
			  'role' => 'reader',
			  'scope' => 
			  array (
				'type' => 'default',
			  ),
		);*/
		$url_parameters = array();

		$url_parameters['role'] = 'reader';
		$url_parameters['scope'] = array('type' => 'default');
		
		$calendar_aci_url = 'https://www.googleapis.com/calendar/v3/calendars/' . $calendar_id . '/acl?'. http_build_query($url_parameters);
		$ch = curl_init();		
		curl_setopt($ch, CURLOPT_URL, $calendar_aci_url);		
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);				
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer '. $access_token, 'Content-Type: application/json'));	

		$data = json_decode(curl_exec($ch), true);
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);	
		echo "<pre>";
		print_r($data);
		if($http_code != 200) 
			throw new Exception('Error : Failed to create event');
		
		 $data;
		
	}

/**********************************************************************************
********************* Start: Get New Access Token if Its Expired *****************
***********************************************************************************/

/* not in use now
	public function GetAccessToken($userID) {	
		$refresh_token = get_user_meta($userID,'yuno_user_refresh_token', true);
		$api_service_deatils = new yuno_google_api_app_details();
		$client = new Google_Client();		
		$client->setClientId($api_service_deatils->client_id);
		$client->setClientSecret($api_service_deatils->client_secret);
		$client->setRedirectUri($api_service_deatils->redirect_uri);
		$client->addScope("https://www.googleapis.com/auth/calendar");
		$client->addScope("https://www.googleapis.com/auth/calendar.events");
		$client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
		$client->setScopes("https://www.googleapis.com/auth/drive.readonly");
		$client->setState('offline');
		$url_token = 'https://www.googleapis.com/oauth2/v4/token'; 			
		
		$curlPost = 'client_id='.$api_service_deatils->client_id.'&redirect_uri='.$api_service_deatils->redirect_uri.'&client_secret='.$api_service_deatils->client_secret.'&refresh_token='.$refresh_token.'&grant_type=refresh_token';
		//error_log('refresh_token== '. $refresh_token);
		if(!empty($refresh_token)){
		  $ch = curl_init();    
		  curl_setopt($ch, CURLOPT_URL, $url_token);    
		  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);    
		  curl_setopt($ch, CURLOPT_POST, 1);    
		  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		  curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);  
		  $data = json_decode(curl_exec($ch), true);		
		  $newAccessToken = $data['access_token'];
		  if($data['error']){
		  	//error_log('GetAccessTokenaccess_token_if=='. $newAccessToken);
		  }else{
		  	//error_log('GetAccessTokenaccess_token_else=='. $newAccessToken);
		  	update_user_meta($userID, 'yuno_user_access_token', $newAccessToken);		  	
		  }
		  //error_log('newAccessToken=='.json_encode($data));
		  $http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);    
		  if($http_code != 200) {
		  	return new WP_REST_Response($http_code, 404);		    
		  }	    
		  return new WP_REST_Response($data, 404);
		}else{
			return;
		}		
	}
*/
	public function GetAccessToken($userID) {	
		global $wpdb;
		$token = $wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE meta_key = 'google_refresh_token_cal'");
		$res = $wpdb->get_results( $token );
		if (!empty($res) && isset($res[0]->meta_value)) {
			$refresh_token = $res[0]->meta_value;
		};
		
		$yuno_google_login_data = get_option('yuno_google_login_options');
		$g_client_id = $yuno_google_login_data['yuno_google_login_client_id'];
		$g_client_secret = $yuno_google_login_data['yuno_google_login_client_secret'];
		$g_redirect_uri = $yuno_google_login_data['yuno_google_login_redirect_uri'];

		$client = new Google_Client();		
		$client->setClientId($g_client_id);
		$client->setClientSecret($g_client_secret);
		$client->setRedirectUri($g_redirect_uri);
		$client->addScope("https://www.googleapis.com/auth/calendar");
		$client->addScope("https://www.googleapis.com/auth/calendar.events");
		$client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
		$client->setScopes("https://www.googleapis.com/auth/drive.readonly");
		$client->setState('offline');

		$url_token = 'https://www.googleapis.com/oauth2/v4/token'; 			

		$curlPost = 'client_id='.$g_client_id.'&redirect_uri='.$g_redirect_uri.'&client_secret='.$g_client_secret.'&refresh_token='.$refresh_token.'&grant_type=refresh_token';
		
		if(!empty($refresh_token)){
		$ch = curl_init();    
		curl_setopt($ch, CURLOPT_URL, $url_token);    
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);    
		curl_setopt($ch, CURLOPT_POST, 1);    
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, TRUE);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost); 
		
		$data = json_decode(curl_exec($ch), true);
		
		$newAccessToken = $data['access_token'];
		if($data['error']){
		}else{
			$token = $wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key = 'google_access_token_cal'");
			$res = $wpdb->get_results( $token );
			$userID = $res[0]->post_id;
			update_post_meta($userID, 'google_access_token_cal', $newAccessToken);		  	
		}
		$http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);   
		if($http_code != 200) {
			return new WP_REST_Response($http_code, 404);		    
		}	    
		// return new WP_REST_Response($data, 404);
		return new \WP_REST_Response($data, 404);
		}else{
			return;
		}		
	}

	public function get_access_token_from_database(){
		global $wpdb;
		$token = $wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE meta_key = 'google_access_token_cal'");
		$res = $wpdb->get_results( $token );
		if (!empty($res) && isset($res[0]->meta_value)) {
			$accessToken = $res[0]->meta_value;
		} 
		return $accessToken;
	}

	public function GetUsrAccessToken($userID) {	
		$refresh_token = get_user_meta($userID,'user_google_refresh_token', true);
		
		$yuno_google_login_data = get_option('yuno_google_login_options');
		$g_client_id = $yuno_google_login_data['yuno_google_login_client_id'];
		$g_client_secret = $yuno_google_login_data['yuno_google_login_client_secret'];
		$g_redirect_uri = $yuno_google_login_data['yuno_google_login_redirect_uri'];

		$client = new Google_Client();		
		$client->setClientId($g_client_id);
		$client->setClientSecret($g_client_secret);
		$client->setRedirectUri($g_redirect_uri);
		$client->addScope("https://www.googleapis.com/auth/calendar");
		$client->addScope("https://www.googleapis.com/auth/calendar.events");
		$client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
		$client->setScopes("https://www.googleapis.com/auth/drive.readonly");
		$client->setState('offline');
	
		$url_token = 'https://www.googleapis.com/oauth2/v4/token'; 			
	
		$curlPost = 'client_id='.$g_client_id.'&redirect_uri='.$g_redirect_uri.'&client_secret='.$g_client_secret.'&refresh_token='.$refresh_token.'&grant_type=refresh_token';
		
		if(!empty($refresh_token)){
		  $ch = curl_init();    
		  curl_setopt($ch, CURLOPT_URL, $url_token);    
		  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);    
		  curl_setopt($ch, CURLOPT_POST, 1);    
		  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, TRUE);
		  curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost); 
		
		  $data = json_decode(curl_exec($ch), true);
		
		  $newAccessToken = $data['access_token'];
		  if($data['error']){
		  }else{
		  	update_user_meta($userID, 'user_google_access_token', $newAccessToken);		  	
		  }
		  $http_code = curl_getinfo($ch,CURLINFO_HTTP_CODE);   
		  if($http_code != 200) {
		  	return new WP_REST_Response($http_code, 404);		    
		  }	    
		  return new WP_REST_Response($data, 404);
		}else{
			return;
		}		
	}

	public function CreateUserCalendar($userId) {
		global $wpdb;
		$token = $wpdb->prepare("SELECT meta_value FROM $wpdb->postmeta WHERE meta_key = 'google_access_token_cal'");
		$res = $wpdb->get_results( $token );
		if (!empty($res) && isset($res[0]->meta_value)) {
			$access_token = $res[0]->meta_value;
		} 
		$usrId = $wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key = 'google_access_token_cal'");
		$user = $wpdb->get_results( $usrId );
		if (!empty($user) && isset($user[0]->post_id)) {
			$sourceCalId = $res[0]->post_id;
		} 
		// If no access token or token is invalid, refresh it
		if (empty($access_token)) {
			$access_token = $this->GetAccessToken($sourceCalId);
			if (is_wp_error($access_token)) {
				throw new Exception('Failed to get access token: ' . $access_token->get_error_message());
			}
		}
	
		$calendarUrl = 'https://www.googleapis.com/calendar/v3/calendars';
		$calendarData = [
			'summary' => 'Calendar_' . $userId,
			'timeZone' => 'UTC'
		];
	
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $calendarUrl);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $access_token, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($calendarData));
		$response = json_decode(curl_exec($ch), true);
		curl_close($ch);
	
		if (isset($response['id'])) {
			$calendarId = $response['id'];
			update_user_meta($userId, 'user_google_calendar_id', $calendarId);
			return $calendarId;
		} else {
			if (isset($response['error'])) {
				// If token expired, refresh and retry once
				if ($response['error']['code'] == 401) {
					$access_token = $this->GetAccessToken($sourceCalId);
					if (is_wp_error($access_token)) {
						throw new Exception('Failed to refresh access token: ' . $access_token->get_error_message());
					}
					return $this->CreateUserCalendar($userId); 
				} else {
					throw new Exception('Failed to create new calendar for user ' . $userId . ': ' . $response['error']['message']);
				}
			}
		}
	
		throw new Exception('Unknown error occurred while creating new calendar for user ' . $userId);
	}

	public function addBatchEvent($service, $calendarId, $summary, $description, $startDate, $endDate, $daysOfWeek, $startTime, $endTime, $batchId) {
		$timeZone = 'Asia/Kolkata';
		$daysMap = [
			'Mon' => 'MO',
			'Tue' => 'TU',
			'Wed' => 'WE',
			'Thu' => 'TH',
			'Fri' => 'FR',
			'Sat' => 'SA',
			'Sun' => 'SU'
		];
		$rruleDays = implode(',', array_map(function($day) use ($daysMap) {
			return $daysMap[$day];
		}, $daysOfWeek));
	
		$startDateTime = new DateTime("$startDate $startTime", new DateTimeZone($timeZone));
		$endDateTime = new DateTime("$startDate $endTime", new DateTimeZone($timeZone));
		
		// Convert end date to the correct format
		$untilDateStr = "$endDate 23:59:59";
		$untilDate = new DateTime($untilDateStr, new DateTimeZone($timeZone));
		$untilFormatted = $untilDate->format('Ymd\THis\Z');
	
		// Create the event
		$event = new Google_Service_Calendar_Event(array(
			'summary' => $summary,
			'description' => $description,
			'start' => array(
				'dateTime' => $startDateTime->format('Y-m-d\TH:i:s'),
				'timeZone' => $timeZone,
			),
			'end' => array(
				'dateTime' => $endDateTime->format('Y-m-d\TH:i:s'),
				'timeZone' => $timeZone,
			),
			'recurrence' => array(
				'RRULE:FREQ=WEEKLY;BYDAY=' . $rruleDays . ';UNTIL=' . $untilFormatted,
			),
		));
	
		$event = $service->events->insert($calendarId, $event);
		update_post_meta($batchId, 'resource_calendar_events_batch', $event->id);
		// echo 'Recurring event created: ' . json_encode($event) . '<br>';
	}
	public function addBatchInCalendar($userId, $description, $startDate, $endDate, $startTime, $endTime, $daysOfWeek, $batchId){
        $calendar_id = get_user_meta($userId,'user_google_calendar_id', true);
		if (empty($calendar_id)) {
			$calendarId = $this->CreateUserCalendar($userId);
			$calendar_id = $calendarId;
		}
		$accessToken = get_user_meta($userId,'user_google_access_token', true);
	
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
	
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetUsrAccessToken($userId);
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
        $service = new Google_Service_Calendar($client);

		$summary = $description;
		$desc = '';
		$this->addBatchEvent($service, $calendar_id, $summary, $desc, $startDate, $endDate, $daysOfWeek, $startTime, $endTime, $batchId);
	}

	function updateBatchEvent($service, $calendarId, $eventId, $newStartDate = null, $newEndDate = null, $newDaysOfWeek = null, $newStartTime = null, $newEndTime = null) {
		$event = $service->events->get($calendarId, $eventId);
	
		// Update recurrence rule for end date
		if ($newEndDate) {
			$rrule = $event->getRecurrence();
			foreach ($rrule as &$rule) {
				if (strpos($rule, 'UNTIL=') !== false) {
					$rule = preg_replace('/UNTIL=[^;]*/', 'UNTIL=' . date('Ymd\THis\Z', strtotime($newEndDate . ' 23:59:59')), $rule);
				}
			}
			$event->setRecurrence($rrule);
		}
		// Update recurrence rule for days of the week
		if ($newDaysOfWeek) {
			$daysMap = [
				'MO' => 'MO',
				'TU' => 'TU',
				'WE' => 'WE',
				'TH' => 'TH',
				'FR' => 'FR',
				'SA' => 'SA',
				'SU' => 'SU'
			];
			$rruleDays = implode(',', array_map(function($day) use ($daysMap) {
				return $daysMap[$day];
			}, $newDaysOfWeek));
			$rrule = $event->getRecurrence();
			foreach ($rrule as &$rule) {
				if (strpos($rule, 'BYDAY=') !== false) {
					$rule = preg_replace('/BYDAY=[^;]*/', 'BYDAY=' . $rruleDays, $rule);
				}
			}
			$event->setRecurrence($rrule);
		}
		// Update start and end times
		$timeZone = $event->start->timeZone ?: 'UTC';
		if ($newStartDate) {
			$event->start->dateTime = $newStartDate . 'T' . date('H:i:s', strtotime($event->start->dateTime));
			$event->end->dateTime = $newStartDate . 'T' . date('H:i:s', strtotime($event->end->dateTime));
		}
		if ($newStartTime) {
			$event->start->dateTime = date('Y-m-d', strtotime($event->start->dateTime)) . 'T' . $newStartTime;
			$event->start->timeZone = $timeZone;
		}
		if ($newEndTime) {
			$event->end->dateTime = date('Y-m-d', strtotime($event->end->dateTime)) . 'T' . $newEndTime;
			$event->end->timeZone = $timeZone;
		}
		// Update the event
		$updatedEvent = $service->events->update($calendarId, $eventId, $event);
		// echo 'Event updated: ' . $updatedEvent->htmlLink . '<br>';
	}

	public function updateBatchInCalendar($userId, $description, $ClassStartDate, $ClassEndDate, $ClassStartTime, $endTimeClass, $daysofweek, $batchId){
		$calendar_id = get_user_meta($userId,'user_google_calendar_id', true);
		if (empty($calendar_id)) {
			$calendarId = $this->CreateUserCalendar($userId);
			$calendar_id = $calendarId;
		}
		$accessToken = get_user_meta($userId,'user_google_access_token', true);
	
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
	
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetUsrAccessToken($userId);
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
        $service = new Google_Service_Calendar($client);

		$eventId = get_post_meta($batchId, 'resource_calendar_events_batch', true);
	
		$this->updateBatchEvent($service, $calendar_id, $eventId, $ClassStartDate, $ClassEndDate, $daysofweek, $ClassStartTime, $endTimeClass);
	}

	public function addClassInCalendar($userId, $ClassTitle, $eventStartDate, $eventEndDate, $user_timezone, $userAccessToken, $description, $EventID, $ClassSchdulelearner_email) {
		$calendar_id = get_user_meta($userId,'user_google_calendar_id', true);
		if (empty($calendar_id)) {
			$calendarId = $this->CreateUserCalendar($userId);
			$calendar_id = $calendarId;
		}
		$accessToken = get_user_meta($userId,'user_google_access_token', true);
	
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
	
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetUsrAccessToken($userId);
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
		$service = new Google_Service_Calendar($client);
	
		// Create the event
		$event = new Google_Service_Calendar_Event(array(
			'summary' => $ClassTitle,
			'location' => '',
			'description' => $description,
			'start' => array(
				'dateTime' => $eventStartDate,
				'timeZone' => $user_timezone, 
			),
			'end' => array(
				'dateTime' => $eventEndDate,
				'timeZone' => $user_timezone, 
			),
			'attendees' => $ClassSchdulelearner_email,
		));
	
		// Insert the event into the calendar
		$calendarId = $calendar_id;
		try {
			$event = $service->events->insert($calendarId, $event, array('conferenceDataVersion' => 1));
			update_post_meta($EventID, 'resource_calendar_events_class', $event->id);
			// echo 'Event created: ' . $event->htmlLink;
		} catch (Exception $e) {
			throw new Exception('Failed to create event: ' . $e->getMessage());
		}
	}

	public function updateClassInCalendar($userId, $ClassTitle, $eventStartDate, $eventEndDate, $user_timezone, $userAccessToken, $description, $EventID, $ClassSchdulelearner_email) {
		$calendar_id = get_user_meta($userId, 'user_google_calendar_id', true);
		if (empty($calendar_id)) {
			throw new Exception('User calendar ID is missing.');
		}
		$accessToken = get_user_meta($userId, 'user_google_access_token', true);
	
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
	
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetUsrAccessToken($userId);
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
		$service = new Google_Service_Calendar($client);
		try {
			$event = $service->events->get($calendar_id, $EventID);
		} catch (Exception $e) {
			throw new Exception('Failed to fetch event: ' . $e->getMessage());
		}
		$event->setSummary($ClassTitle);
		$event->setDescription($description);
		$event->setStart(new Google_Service_Calendar_EventDateTime(array(
			'dateTime' => $eventStartDate,
			'timeZone' => $user_timezone,
		)));
		$event->setEnd(new Google_Service_Calendar_EventDateTime(array(
			'dateTime' => $eventEndDate,
			'timeZone' => $user_timezone,
		)));
		$event->setAttendees($ClassSchdulelearner_email);
	
		try {
			$updatedEvent = $service->events->update($calendar_id, $event->getId(), $event);
			// echo 'Event updated: ' . $updatedEvent->htmlLink;
		} catch (Exception $e) {
			throw new Exception('Failed to update event: ' . $e->getMessage());
		}
	}

	public function getAvailabilityOfResourceInCalendar($resource_ids, $startdate, $enddate, $starttime, $endtime, $daysofweek) {
		$timeZone = 'Asia/Kolkata';
		// Combine date and time to create DateTime objects
		$startDateTime = new DateTime("$startdate $starttime", new DateTimeZone($timeZone));
		$endDateTime = new DateTime("$enddate $endtime", new DateTimeZone($timeZone));
		// Extend the end time by one day to ensure the end date is included
		$endDateTime->modify('+1 day');
		// Convert DateTime objects to RFC3339 format
		$timeMin = $startDateTime->format(DateTime::RFC3339);
		$timeMax = $endDateTime->format(DateTime::RFC3339);
		$calendars = [];
		$services = [];
		// Fetch calendar IDs and access tokens for each resource (user)
		foreach ($resource_ids as $userId) {
			$calendarId = get_user_meta($userId, 'user_google_calendar_id', true);
			if (empty($calendarId)) {
				return new WP_Error('missing_calendar_id', 'Source or destination calendar ID not found.');
			}
			$accessToken = get_user_meta($userId, 'user_google_access_token', true);
			// Initialize Google Client
			$client = new Google_Client();
			$client->setAccessToken($accessToken);
	
			// Refresh the token if it's expired
			if ($client->isAccessTokenExpired()) {
				$accessTokenResponse = $this->GetAccessToken($userId);
				$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
				$client->setAccessToken($accessToken);
			}
			$calendars[] = $calendarId;
			$services[$calendarId] = new Google_Service_Calendar($client);
		}
		$availableResources = [];
		foreach ($calendars as $index => $calendarId) {
			$service = $services[$calendarId];
	
			// Prepare the freeBusy request body
			$requestBody = new Google_Service_Calendar_FreeBusyRequest();
			$requestBody->setTimeMin($timeMin);
			$requestBody->setTimeMax($timeMax);
			$requestBody->setTimeZone($timeZone);
			$requestBody->setItems([['id' => $calendarId]]);
	
			try {
				// Send the freeBusy query
				$freebusy = $service->freebusy->query($requestBody);
				$calendarsAvailability = $freebusy->getCalendars();
			} catch (Exception $e) {
				continue; // Skip this calendar and move to the next one
			}
			$calendarBusy = $calendarsAvailability[$calendarId]->getBusy();
			$isAvailable = true;
	
			foreach ($calendarBusy as $busy) {
				$busyStart = new DateTime($busy->getStart());
				$busyEnd = new DateTime($busy->getEnd());
	
				if ($this->isTimeOverlap($busyStart, $busyEnd, $starttime, $endtime, $daysofweek)) {
					$isAvailable = false;
					break;
				}
			}
			if ($isAvailable) {
				$availableResources[] = $resource_ids[$index];
			}
		}
		return $availableResources;
	}

	private function isTimeOverlap($busyStart, $busyEnd, $starttime, $endtime, $daysofweek) {
        $busyStartTime = $busyStart->format('H:i');
        $busyEndTime = $busyEnd->format('H:i');
        $busyDayOfWeek = $busyStart->format('l');

        return in_array($busyDayOfWeek, $daysofweek) && !(
            strtotime($endtime) <= strtotime($busyStartTime) || 
            strtotime($starttime) >= strtotime($busyEndTime)
        );
    }

	
	public function updateAndMoveSpecificEvents($sourceUserId, $destUserId, $batchId, $startDate, $endDate) {
		$calId = get_user_meta($sourceUserId, 'user_google_calendar_id', true);
		$newCalendarId = get_user_meta($destUserId, 'user_google_calendar_id', true);
		if (empty($calId) || empty($newCalendarId)) {
			return new WP_Error('missing_calendar_id', 'Source or destination calendar ID not found.');
		}
		$accessToken = get_user_meta($sourceUserId, 'user_google_access_token', true);
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
	
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetAccessToken($sourceUserId); 
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
		$service = new Google_Service_Calendar($client);
		
		$eventId = get_post_meta($batchId, 'resource_calendar_events_batch', true);
		$recurringEventId = $eventId;
		$instances = $this->listRecurringEventInstances($service, $calId, $recurringEventId);
		// Specify the date range for filtering instances
		$start_date = new DateTime($startDate);
		$end_date = new DateTime($endDate);
		$end_date->setTime(23, 59, 59); // Make the end date inclusive
	
		// Filter instances by date range
		$instanceIds = [];
		foreach ($instances as $instance) {
			$instanceStart = new DateTime($instance->getStart()->getDateTime());
	
			// Check if the instance falls within the specified date range
			if ($instanceStart >= $start_date && $instanceStart <= $end_date) {
				$instanceIds[] = $instance->getId();
			}
		}
		// Move the filtered instances
		$this->updateAndMoveSpecificRecurringEventInstances($service, $calId, $newCalendarId, $instanceIds);
	}
	
	public function updateAndMoveSpecificRecurringEventInstances($service, $calId, $newCalendarId, $instanceIds) {
		foreach ($instanceIds as $instanceId) {
			// Get the specific instance
			$instance = $service->events->get($calId, $instanceId);
	
			// Create a standalone event in the new calendar
			$this->createStandaloneEvent($service, $instance, $newCalendarId);
	
			// Delete the event from the original calendar
			$this->deleteEvent($service, $calId, $instanceId);
		}
	}
	
	public function listRecurringEventInstances($service, $calendarId, $recurringEventId) {
		$instances = [];
		$pageToken = null;
		do {
			$optParams = array(
				'pageToken' => $pageToken,
			);
			$events = $service->events->instances($calendarId, $recurringEventId, $optParams);
			foreach ($events->getItems() as $event) {
				$instances[] = $event;
			}
			$pageToken = $events->getNextPageToken();
		} while ($pageToken);
	
		return $instances;
	}
	
	public function createStandaloneEvent($service, $instance, $newCalendarId) {
		// Create a new event object
		$newEvent = new Google_Service_Calendar_Event(array(
			'summary' => $instance->getSummary(),
			'description' => $instance->getDescription(),
			'start' => $instance->getStart(),
			'end' => $instance->getEnd(),
			'timeZone' => $instance->getStart()->getTimeZone()
		));
		// Insert the new event into the target calendar
		$movedEvent = $service->events->insert($newCalendarId, $newEvent);
		return $movedEvent;
	}
	
	public function deleteEvent($service, $calendarId, $eventId) {
		$service->events->delete($calendarId, $eventId);
	}
	

	//move class event from 1 calendar to other
	public function updateAndMoveSingleEvent($sourceUserId, $classId, $destUserId, $summary = null, $description = null, $startDateTime = null, $endDateTime = null, $timeZone = 'Asia/Kolkata') {
		$calendarId = get_user_meta($sourceUserId, 'user_google_calendar_id', true);
		$newCalendarId = get_user_meta($destUserId, 'user_google_calendar_id', true);
		// Check if the calendar ID exists, if not create a new calendar
		if (empty($calendarId) || empty($newCalendarId)) {
			return new WP_Error('missing_calendar_id', 'Source or destination calendar ID not found.');
		}
		// Get the access token
		$accessToken = get_user_meta($sourceUserId, 'user_google_access_token', true);
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
		// Refresh the token if it's expired
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetAccessToken($sourceUserId); 
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
		$service = new Google_Service_Calendar($client);
		// Get the event ID from post meta
		$eventId = get_post_meta($classId, 'resource_calendar_events_class', true);
		if (empty($eventId)) {
			return new WP_Error('event_not_found', 'Event ID not found for the given class ID.');
		}
		// Fetch the event from the source calendar
		try {
			$event = $service->events->get($calendarId, $eventId);
		} catch (Exception $e) {
			return new WP_Error('event_fetch_error', 'Error fetching event: ' . $e->getMessage());
		}
		// Update the event details if provided
		if ($summary) {
			$event->setSummary($summary);
		}
		if ($description) {
			$event->setDescription($description);
		}
		if ($startDateTime && $endDateTime) {
			$start = new Google_Service_Calendar_EventDateTime();
			$start->setDateTime($startDateTime);
			$start->setTimeZone($timeZone);
			$event->setStart($start);
			
			$end = new Google_Service_Calendar_EventDateTime();
			$end->setDateTime($endDateTime);
			$end->setTimeZone($timeZone);
			$event->setEnd($end);
		}
		// Move the event to the new calendar
		try {
			$movedEvent = $service->events->move($calendarId, $eventId, $newCalendarId);
			return 'Event moved to new calendar: ' . $movedEvent->htmlLink;
		} catch (Exception $e) {
			return new WP_Error('event_move_error', 'Error moving event: ' . $e->getMessage());
		}
	}
	

	//move batch from 1 calendar to other
	public function updateAndMoveEvents($sourceUserId, $destUserId, $batchId,$summary, $description, $startDateTime, $endDateTime) {
		$client = new Google_Client();
		$calendarId = get_user_meta($sourceUserId, 'user_google_calendar_id', true);
		$newCalendarId = get_user_meta($destUserId, 'user_google_calendar_id', true);
		if (empty($calendarId) || empty($newCalendarId)) {
			return new WP_Error('missing_calendar_id', 'Source or destination calendar ID not found.');
		}
		$accessToken = get_user_meta($sourceUserId, 'user_google_access_token', true);
		$client = new Google_Client();
		$client->setAccessToken($accessToken);
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetAccessToken($sourceUserId); 
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}
		$service = new Google_Service_Calendar($client);
		$eventId = get_post_meta($batchId, 'resource_calendar_events_batch', true);
		$this->updateAndMoveBatch($sourceUserId, $service, $eventId, $destUserId, $summary, $description, $startDateTime, $endDateTime);
	}
	public function updateAndMoveBatch($calendarId, $service, $eventId, $newCalendarId, $summary = null, $description = null, $startDateTime = null, $endDateTime = null, $timeZone = 'Asia/Kolkata') {
		// Get the event ID from post meta
		try {
			$event = $service->events->get($calendarId, $eventId);
		} catch (Exception $e) {
			return new WP_Error('event_fetch_error', 'Error fetching event: ' . $e->getMessage());
		}
		// Update the event details if provided
		if ($summary) {
			$event->setSummary($summary);
		}
		if ($description) {
			$event->setDescription($description);
		}
		if ($startDateTime && $endDateTime) {
			$start = new Google_Service_Calendar_EventDateTime();
			$start->setDateTime($startDateTime);
			$start->setTimeZone($timeZone);
			$event->setStart($start);
			
			$end = new Google_Service_Calendar_EventDateTime();
			$end->setDateTime($endDateTime);
			$end->setTimeZone($timeZone);
			$event->setEnd($end);
		}
		// Move the event to the new calendar
		try {
			$movedEvent = $service->events->move($calendarId, $eventId, $newCalendarId);
			return 'Event moved to new calendar: ' . $movedEvent->htmlLink;
		} catch (Exception $e) {
			return new WP_Error('event_move_error', 'Error moving event: ' . $e->getMessage());
		}
	}

	// get instructors availability with date and time slots - start
	function getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax) {
		$service = new \Google_Service_Calendar($client);

		// Ensure timeMin and timeMax are DateTime objects and set the correct timezone
		$timeMin = new \DateTime($timeMin);
		$timeMax = new \DateTime($timeMax);
		$timeMin->setTimezone(new \DateTimeZone('Asia/Kolkata'));
		$timeMax->setTimezone(new \DateTimeZone('Asia/Kolkata'));

		// Format the DateTime objects back to RFC3339 strings
		$timeMinFormatted = $timeMin->format(\DateTime::RFC3339);
		$timeMaxFormatted = $timeMax->format(\DateTime::RFC3339);

		$freebusyRequest = new \Google_Service_Calendar_FreeBusyRequest();
		$freebusyRequest->setTimeMin($timeMinFormatted);
		$freebusyRequest->setTimeMax($timeMaxFormatted);
		$freebusyRequest->setTimeZone('Asia/Kolkata');
		$freebusyRequest->setItems([['id' => $calendarId]]);

		$freebusy = $service->freebusy->query($freebusyRequest);

		// Extract busy time slots
		$calendars = $freebusy->getCalendars();
		$busyTimes = [];
		if (isset($calendars[$calendarId])) {
			$busyTimes = $calendars[$calendarId]->getBusy();
		}

		return $busyTimes;
	}

	function getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax) {
		$combinedBusyTimes = [];

		foreach ($calendarIds as $calendarId) {
			$busyTimes = $this->getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax);
			foreach ($busyTimes as $busyTime) {
				$busyStart = new \DateTime($busyTime->getStart());
				$busyEnd = new \DateTime($busyTime->getEnd());
				$combinedBusyTimes[] = [
					'start' => $busyStart,
					'end' => $busyEnd
				];
			}
		}

		// Sort combined busy times by start time
		usort($combinedBusyTimes, function($a, $b) {
			return $a['start'] <=> $b['start'];
		});

		return $combinedBusyTimes;
	}

	function generateTimeSlots($timeMin, $timeMax, $combinedBusyTimes) {
		$slots = [];
		$currentSlotStart = clone $timeMin;

		while ($currentSlotStart < $timeMax) {
			$currentSlotEnd = clone $currentSlotStart;
			$currentSlotEnd->modify('+30 minutes');
			if ($currentSlotEnd > $timeMax) {
				$currentSlotEnd = clone $timeMax;
			}

			$isFree = true;
			foreach ($combinedBusyTimes as $busyTime) {
				$busyStart = $busyTime['start'];
				$busyEnd = $busyTime['end'];

				// Check if the current slot overlaps with any busy time
				if (($busyStart < $currentSlotEnd && $busyEnd > $currentSlotStart)) {
					$isFree = false;
					break; 
				}
			}

			$slots[] = [
				'starttime' => $currentSlotStart->format('H:i'),
				'endtime' => $currentSlotEnd->format('H:i'),
				'status' => $isFree
			];

			// Move to the next slot
			$currentSlotStart = clone $currentSlotEnd;
		}

		return $slots;
	}

	function getAvailableSlotsResource($client, $calendarIds, $startDate, $endDate, $startTime, $endTime, $instructors) {
		global $wpdb;
		$date = new \DateTime($startDate);
		$end = new \DateTime($endDate);
		$end->modify('+1 day'); 

		$availableSlots = [];

		while ($date < $end) {
			$currentDate = $date->format('Y-m-d');
			$dayOfWeek = $date->format('l');
			$timeMin = "$currentDate $startTime";
			$timeMax = "$currentDate $endTime";

			$timeMinDT = new \DateTime($timeMin, new \DateTimeZone('Asia/Kolkata'));
			$timeMaxDT = new \DateTime($timeMax, new \DateTimeZone('Asia/Kolkata'));
			// Combine busy times from all calendars
			$combinedBusyTimes = $this->getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax);
			// Generate slots based on combined busy times
			$slots = $this->generateTimeSlots($timeMinDT, $timeMaxDT, $combinedBusyTimes);

			$availableSlots[] = [
				'date' => $currentDate,
				'day_of_week' => $dayOfWeek,
				'slots' => $slots
			];

			$output = [
				'instructors' => $instructors,
				'available_slots' => $availableSlots
			];

			$date->modify('+1 day');
		}

		return $output;
	}

	public function getAvailabilityOfInstructor($userId, $startDate, $endDate, $startTime, $endTime) {
		$accessToken = $this->get_access_token_from_database();
		$client = new Google_Client();
		global $wpdb;
		$token = $wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key = 'google_access_token_cal'");
		$res = $wpdb->get_results( $token );
		$userID = $res[0]->post_id;
		if ($client->isAccessTokenExpired()) {
			$accessTokenResponse = $this->GetAccessToken($userID);
			$accessToken = is_array($accessTokenResponse) ? $accessTokenResponse : $accessTokenResponse->get_data()['access_token'];
			$client->setAccessToken($accessToken);
		}

		$instructors = [];
		$instructorCalendarIds = [];

		if (is_array($userId)) {
			foreach ($userId as $id) {
				$calendarId = get_user_meta($id, 'user_google_calendar_id', true);
				if ($calendarId) {
					$instructorCalendarIds[] = $calendarId;
					$instructorName = get_user_meta($id, 'yuno_display_name', true);
					$instructors[] = [
						'id' => $id,
						'name' => $instructorName
					];
				}
			}
		} else {
			$calendarId = get_user_meta($userId, 'user_google_calendar_id', true);
			if ($calendarId) {
				$instructorCalendarIds[] = $calendarId;
				$instructorName = get_user_meta($userId, 'yuno_display_name', true);
				$instructors[] = [
					'id' => $userId,
					'name' => $instructorName
				];
			}
		}

		if (empty($instructorCalendarIds)) {
			$availableSlots = [];
			return $availableSlots;
		}

		$availableSlots = $this->getAvailableSlotsResource($client, $instructorCalendarIds, $startDate, $endDate, $startTime, $endTime, $instructors);

		$availableSlotsFormatted = json_encode($availableSlots);

		return $availableSlots;
	}
	// get instructors availability with date and time slots - end

	

}


