<?php

namespace V4;

/**
 *  Instructor Controller
 */


class InstructorController extends Controller
{
    /**
     * Constructor to initialize the InstructorController
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
        $this->loadModel('instructor');
    }


    /**
     * Fetches the working hours for a given instructor resource.
     * If no valid working hours data is found, returns a default 7-day blank structure.
     *
     * @param array $request Request payload containing 'resourceType' and 'resourceId'
     * @return WP_REST_Response JSON-formatted response with working hours data
     */
    public function getResourceWorkingHours($request)
    {
        try {
            $workingHours = $this->instructorModel->getInstructor(
                ['resource' => $request['resourceType'], 'id' => $request['resourceId']],
                ['key' => 'working_hours']
            );

            $isInvalid = !is_array($workingHours)
                || !isset($workingHours['resource'])
                || !isset($workingHours['days'])
                || !is_array($workingHours['days'])
                || empty($workingHours['days'])
                || count(array_filter($workingHours['days'], fn($day) => $day !== 'Error::Key_Not_Found_In_Data')) === 0;

            if ($isInvalid) {
                $emptyWorkingHours = $this->instructorModel->getBlankWorkingHours(['id' => $request['resourceId']]);
                return $this->response->success("GET_SUCCESS", $emptyWorkingHours, ['message' => "Working Hours found"]);
            }

            return $this->response->success("GET_SUCCESS", $workingHours, ['message' => "Working Hours found"]);

        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    /**
     * Retrieves detailed information about an instructor
     *
     * @param array $request Request payload containing 'instructorId'
     * @return WP_REST_Response JSON-formatted response with instructor details
     */
    public function getInstructorDetails($request)
    {
        try {
            $instructorId = (int)$request['instructorId'];
            
            if (empty($instructorId) || !is_numeric($instructorId)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid instructor ID"]);
            }

            $instructorDetails = $this->instructorModel->getInstructor(['id' => $instructorId]);
            
            if (!$instructorDetails) {
                return $this->response->error("GET_FAIL", ['message' => "Instructor details not found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $instructorDetails, ['message' => "Instructor details found"]);
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    public function getResourceAvailability($request)
    {
        try {
            $resource_id = (int)$request['resource_id'];
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];
            $start_time = $request['start_time'];
            $end_time = $request['end_time'];
            $resource = $request['resource'] ?? 'Instructor';
            $isAvailable = $this->instructorModel->getInstructorAvailability(['resource' => $resource, 'resource_id' => $resource_id, 'start_date' => $start_date, 'end_date' => $end_date, 'start_time' => $start_time, 'end_time' => $end_time], ['schema' => 'Availability']);

            if (!$isAvailable) {
                return $this->response->error("GET_FAIL", ['message' => "Resource not available"]);
            }
            
            return $this->response->success("GET_SUCCESS", $isAvailable, ['message' => "Resource is available"] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieves a list of instructors with filtering options
     *
     * @param array $request Request payload containing optional filters
     * @return \WP_REST_Response JSON-formatted response with instructors data
     */
    public function getInstructors($request)
    {
        try {
            error_log('getInstructors method called');
            
            $getVars = $request->get_query_params();
            $viewType = $request->get_param('viewType') ?? 'grid'; // Get viewType, default to grid
            error_log('View Type: ' . $viewType);
            error_log('Query params: ' . print_r($getVars, true));
            
            // Determine limit for filters
            $limit_param = $request->get_param('limit');
            if ($limit_param !== null && is_numeric($limit_param) && (int)$limit_param > 0) {
                $limit_for_filters = (int)$limit_param;
            } else {
                // If limit is not provided, zero, negative, or non-numeric, fetch a large set (effectively "all" for ES max window)
                $limit_for_filters = 10000; 
            }

            // Determine offset for filters
            $offset_param = $request->get_param('offset');
            if ($offset_param !== null && is_numeric($offset_param) && (int)$offset_param >= 0) {
                $offset_for_filters = (int)$offset_param;
            } else {
                $offset_for_filters = 0; // Default to 0 if not provided or invalid
            }

            $user_id = isset($getVars['user_id']) ? (int)$getVars['user_id'] : 0;
            
            $category_ids = [];
            if (isset($getVars['category_ids'])) {
                if (is_array($getVars['category_ids'])) {
                    $category_ids = array_map('intval', $getVars['category_ids']);
                } else if (is_string($getVars['category_ids'])) {
                    if (preg_match('/^\[(.*)\]$/', $getVars['category_ids'], $matches)) {
                        $category_ids = array_map('intval', explode(',', $matches[1]));
                    } else if (strpos($getVars['category_ids'], ',') !== false) {
                        $category_ids = array_map('intval', explode(',', $getVars['category_ids']));
                    } else {
                        $category_ids = [(int)$getVars['category_ids']];
                    }
                }
            }
            error_log('Processed category_ids: ' . print_r($category_ids, true));
            
            $is_featured = isset($getVars['is_featured']) ? filter_var($getVars['is_featured'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            // Ensure boolean conversion for these filters, defaulting to appropriate values or null
            $has_active_batches = isset($getVars['has_active_batches']) ? filter_var($getVars['has_active_batches'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            $has_past_batches = isset($getVars['has_past_batches']) ? filter_var($getVars['has_past_batches'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            $has_active_enrollments = isset($getVars['has_active_enrollments']) ? filter_var($getVars['has_active_enrollments'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $has_past_enrollments = isset($getVars['has_past_enrollments']) ? filter_var($getVars['has_past_enrollments'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $is_disabled = isset($getVars['is_disabled']) ? filter_var($getVars['is_disabled'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $org_id = isset($getVars['org_id']) ? (int)$getVars['org_id'] : 0;
            $has_mapped_courses = isset($getVars['has_mapped_courses']) ? filter_var($getVars['has_mapped_courses'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;

            $filters = [
                'limit' => $limit_for_filters,
                'offset' => $offset_for_filters,
                'viewType' => $viewType,
                'user_id' => $user_id,
                'category_ids' => $category_ids,
                'is_featured' => $is_featured,
                'has_active_batches' => $has_active_batches,
                'has_past_batches' => $has_past_batches, 
                'has_active_enrollments' => $has_active_enrollments,
                'has_past_enrollments' => $has_past_enrollments,
                'is_disabled' => $is_disabled,
                'org_id' => $org_id,
                'has_mapped_courses' => $has_mapped_courses,
                'status' => $getVars['status'] ?? null,
                'vc_status' => $getVars['vc_status'] ?? null,
                'native_language' => $getVars['native_language'] ?? null,
                'avg_rating' => $getVars['avg_rating'] ?? 0,
                'days' => $getVars['days'] ?? '0',
                'is_completed' => $getVars['is_completed'] ?? 'yes',
                'batch_id' => isset($getVars['batch_id']) ? (int)$getVars['batch_id'] : 0,
                'enrollment_id' => isset($getVars['enrollment_id']) ? (int)$getVars['enrollment_id'] : 0,
                'mapped_courses' => isset($getVars['mapped_courses']) ? (is_array($getVars['mapped_courses']) ? array_map('intval', $getVars['mapped_courses']) : [(int)$getVars['mapped_courses']]) : [],
            ];
            
            error_log('Filters passed to model: ' . print_r($filters, true));
            
            $instructors = $this->instructorModel->getInstructors($filters);
            error_log('Instructors result from model: ' . ($instructors ? 'Data found (' . ($instructors['count'] ?? 'N/A') . ')' : 'No data found'));
            
            if (!$instructors || !isset($instructors['status']) || $instructors['status'] === 'FAIL') {
                $codes = error_code_setting();
                error_log('No instructors found or error from model, returning 200 with error message.');
                return new \WP_REST_Response([
                    'code' => $instructors['code'] ?? $codes["GET_FAIL"]["code"],
                    'message' => $instructors['message'] ?? 'No Data Found',
                    'status' => $instructors['status'] ?? $codes["GET_FAIL"]["status"],
                    'count' => $instructors['count'] ?? 0,
                    'data' => $instructors['data'] ?? [] 
                ], 200);
            }
            
            error_log('Returning success response with data from model');
            return new \WP_REST_Response($instructors, 200);
            
        } catch (\Exception $e) {
            error_log('Exception in getInstructors: ' . $e->getMessage());
            $codes = error_code_setting();
            return new \WP_REST_Response([
                'code' => $codes["GET_FAIL"]["code"],
                'message' => $e->getMessage(),
                'status' => $codes["GET_FAIL"]["status"]
            ], 200);
        }
    }

    public function getInstructorVirtualClasserooms($request) {
        try {
            $instructorId = (int)$request['instructorId'];
            $getVars = $request->get_query_params();
            $validation_checks = [
                'instructorId' => 'numeric'
            ];
            
            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['instructorId' => $instructorId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // $userData = $this->userModel->validUser($instructorId);
            // if (!$userData) {
            //     return $this->response->error('USER_FAIL');
            // }
            // GET ES record with payload
            $userData = $this->userModel->getUser($instructorId);
           
            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if ($userData===false || $this->userModel->checkRole($userData['role'],$this->userModel->yn_Instructor)===false) {
                return $this->response->error('ROLE_FAIL');
            }

            if (isset($instructorId)) {
                $query['params'] = [
                    'instructorId' => $instructorId
                ];

                $query['custom'] = [
                    "size" => 0,
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => $instructorId
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "distinct_org_ids" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "org_ids" => [
                                    "composite" => [
                                        //"size" => ELASTIC_RECORDS_COUNT, // Number of results per page
                                        "sources" => [
                                            [
                                                "org_id" => [
                                                    "terms" => [
                                                        "field" => "data.details.org_id"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "aggs" => [
                                        "sample_field" => [
                                            "top_hits" => [
                                                "_source" => [
                                                    "includes" => [
                                                        "data.details.academies",
                                                        "data.details.mapped_instructor_ids",
                                                        "data.details.org_id"
                                                    ]
                                                ],
                                                "size" => 1
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false // Excludes document source in the results
                ];
            }

            $virtualClasses = $this->instructorModel->getInstructorVirtualClasserooms($query);
			
            if (!$virtualClasses) {
                return $this->response->error("GET_FAIL", ['replace'=>'VirtualClasses']);
            }
            return $this->response->success("GET_SUCCESS", $virtualClasses, ['replace'=>'VirtualClasses'] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $request Request payload containing instructor data
     * @return WP_REST_Response JSON-formatted response with creation result
     */
    public function createInstructor($request)
    {
        try {
            $data = json_decode($request->get_body(), true);
            
            // Validate request data
            if (empty($data)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid request data"]);
            }
            
            // Check if we have either user_id or email
            if (empty($data['user_id']) && empty($data['email'])) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Either user_id or email is required"]);
            }
            
            // If creating new user, validate required fields
            if (empty($data['user_id']) && !empty($data['email'])) {
                $requiredFields = ['email', 'first_name', 'last_name', 'phone'];
                foreach ($requiredFields as $field) {
                    if (empty($data[$field])) {
                        return $this->response->error('VALIDATION_FAIL', ['message' => "$field is required for new users"]);
                    }
                }
            }
            
            // Create instructor
            $result = $this->instructorModel->createInstructor($data);
            
            if (is_wp_error($result)) {
                return $this->response->error('POST_INSERT_FAIL', [
                    'message' => $result->get_error_message()
                ]);
            }
            
            return $this->response->success("POST_INSERT", $result, ['message' => "Instructor created successfully"]);
            
        } catch (\Exception $e) {
            return $this->response->error('POST_INSERT_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }
}
