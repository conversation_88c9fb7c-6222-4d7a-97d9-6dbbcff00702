{"took": 7, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 285, "relation": "eq"}, "max_score": 1.0, "hits": [{"_index": "org", "_type": "_doc", "_id": "org-19991", "_score": 1.0, "_source": {"data": {"details": {"record_id": 19991, "organisation_name": "Shivam ORG", "org_url": "https://dev.yunolearning.com/org/shivam-org/", "excerpt": "", "status": true, "industry": "education", "sub_industry": "k12school", "number_of_employees": 0, "logo_image": "N/A", "fav_icon": "N/A", "color_theme": {"font_family": "N/A", "primary_color": "N/A", "background_color": "N/A"}, "category": [{"id": 3964, "name": "<PERSON><PERSON> on Finance", "slug": "anil-lamba-on-finance", "parent_id": 0, "sub_category": []}, {"id": 3089, "name": "Coding for kids", "slug": "coding-for-kids", "parent_id": 0, "sub_category": []}], "category_slug": "3964", "created_at": "2022-12-27 23:03:42", "webhooks": [], "event_date": "2022-12-27 23:03:42", "user_id": [12098], "update_event_type": "org", "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-05-10 12:17:40"}}}, {"_index": "org", "_type": "_doc", "_id": "org-20069", "_score": 1.0, "_source": {"data": {"details": {"record_id": 20069, "organisation_name": "REGRESS ORG", "org_url": "https://dev.yunolearning.com/org/regress-org/", "excerpt": "", "status": true, "industry": "education", "sub_industry": "k12school", "number_of_employees": 0, "logo_image": "N/A", "fav_icon": "N/A", "color_theme": {"font_family": "N/A", "primary_color": "N/A", "background_color": "N/A"}, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": []}], "category_slug": "3060", "created_at": "2023-01-07 00:27:22", "webhooks": [], "event_date": "2023-01-07 00:27:22", "user_id": [12100, 12445], "update_event_type": "org", "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-05-10 12:17:40"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27929", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27929, "update_event_type": "org", "organisation_name": "org for testing 18 june - checkk", "excerpt": "org for testing 18 june - checkk", "org_url": "https://dev.yunolearning.com/org/org-for-testing-18-june-checkk-15/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": "2-5", "logo_image": null, "fav_icon": null, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "user_id": ["13737"], "created_at": "2024-06-18 12:42:15", "description": "Org description of the org.", "category_slug": "microsoft-excel", "color_theme": {"font": null, "background_color": null, "theme_color": {"primary": null}}, "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-18 12:45:04"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27949", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27949, "update_event_type": "org", "organisation_name": "org for testing 18 june - checkk", "excerpt": "org for testing 18 june - checkk", "org_url": "https://dev.yunolearning.com/org/org-for-testing-18-june-checkk-16/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": "2-5", "logo_image": "https://dev.yunolearning.com/wp-content/uploads/2024/06/Screenshot-from-2024-06-06-15-23-33.png", "fav_icon": "https://dev.yunolearning.com/wp-content/uploads/2024/06/Screenshot-from-2024-06-11-16-04-16-1.png", "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "user_id": ["13737"], "created_at": "2024-06-19 00:17:11", "description": "Org description of the org.", "category_slug": "microsoft-excel", "prasar": {"live_classes_included": true, "resources": {"study_material": false, "videos": false, "practice_tests": true}, "prasar_url": "http://prasar.url", "mobile-app": {"app_name": "test-app prasar", "app_short_description": "Test App short Description", "app_long_description": "Test App Long Description", "privacy_policy_url": "http://app/privacy-policy", "video_url": "http://app/video", "app_developer": {"email": "developer@app", "phone": "9999889988", "website": "https://test.com"}}}, "color_theme": {"font": null, "background_color": null, "theme_color": {"primary": null}}, "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-19 00:35:02"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27924", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27924, "update_event_type": "org", "organisation_name": "org for testing 18 june - checkk", "excerpt": "org for testing 18 june - checkk", "org_url": "http://local.yunolearning.com/org/org-for-testing-11-june-2/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": "2-5", "logo_image": "http://local.yunolearning.com/wp-content/uploads/2024/06/Screenshot-from-2024-06-06-15-23-33-1.png", "fav_icon": "http://local.yunolearning.com/wp-content/uploads/2024/06/Screenshot-from-2024-06-11-16-04-16-1.png", "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "user_id": ["13737"], "created_at": "2024-06-11 17:51:19", "description": "Org description of the org.", "category_slug": "microsoft-excel", "prasar": {"resources": {"study_material": false, "videos": false, "practice_tests": true}, "prasar_url": "http://prasar.url", "mobile-app": {"app_name": "test-app prasar", "app_short_description": "Test App short Description", "app_long_description": "Test App Long Description", "privacy_policy_url": "http://app/privacy-policy", "video_url": "http://app/video", "app_developer": {"email": "developer@app", "phone": "9999889988", "website": "https://test.com"}}}, "color_theme": {"font": null, "background_color": null, "theme_color": {"primary": null}}, "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": [27866]}}, "@timestamp": "2024-06-19 14:13:39"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27761", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27761, "organisation_name": "xyz org 5 jun -test4", "org-admin": ["13737"], "event_type": "org", "excerpt": "xyz org 5 jun -test4", "org_url": "http://local.yunolearning.com/org/xyz-org-5-jun-test4-2/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": 1, "logo_image": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-50.jpeg", "fav_icon": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-51.jpeg", "color_theme": {"font": "Roboto Serif", "background_color": "#87676", "theme_color": {"primary": "#00887"}}, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "created_at": "2024-06-05 17:52:08", "description": "Org_description of the org.", "category_slug": "microsoft-excel", "uuid": "66605999f203d3.88845567", "event_date": "2024-06-05 17:57:05", "user_id": [12465, 12463], "update_event_type": "org", "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-05 17:57:05"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27764", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27764, "organisation_name": "xyz org 5 jun -test4", "org-admin": ["13737"], "event_type": "org", "excerpt": "xyz org 5 jun -test4", "org_url": "http://local.yunolearning.com/org/xyz-org-5-jun-test4-3/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": 1, "logo_image": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-52.jpeg", "fav_icon": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-53.jpeg", "color_theme": {"font": "Roboto Serif", "background_color": "#87676", "theme_color": {"primary": "#00887"}}, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "created_at": "2024-06-05 17:52:58", "description": "Org_description of the org.", "category_slug": "microsoft-excel", "uuid": "6660599a69eed4.06930358", "event_date": "2024-06-05 17:57:06", "user_id": [12465, 12463], "update_event_type": "org", "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-05 17:57:06"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27780", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27780, "update_event_type": "org", "organisation_name": "xyz org 5 jun -test444", "org-admin": ["13737"], "excerpt": "xyz org 5 jun -test444", "org_url": "http://local.yunolearning.com/org/xyz-org-5-jun-test444/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": 1, "logo_image": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-64.jpeg", "fav_icon": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-65.jpeg", "color_theme": {"font": "Roboto Serif", "background_color": "#87676", "theme_color": {"primary": "#00887"}}, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "created_at": "2024-06-05 17:57:24", "description": "Org_description of the org.", "category_slug": "microsoft-excel", "user_id": [12465, 12463], "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-05 17:58:11"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27683", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27683, "organisation_name": "xyz org 5 jun", "org-admin": ["13737"], "event_type": "org", "excerpt": "xyz org 5 jun", "org_url": "http://local.yunolearning.com/org/xyz-org-5-jun/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": 27, "logo_image": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-10.jpeg", "fav_icon": "http://local.yunolearning.com/wp-content/uploads/2024/06/download-11.jpeg", "color_theme": {"font": null, "background_color": null, "theme_color": {"primary": null}}, "category": [], "created_at": "2024-06-05 10:41:58", "description": "Org_description of the org.", "category_slug": null, "uuid": "6660165b4d4c68.66441566", "event_date": "2024-06-05 13:10:11", "user_id": [12465, 12463], "update_event_type": "org", "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-05 13:10:11"}}}, {"_index": "org", "_type": "_doc", "_id": "org-27701", "_score": 1.0, "_source": {"data": {"details": {"record_id": 27701, "update_event_type": "org", "organisation_name": "org for testingggg55", "org-admin": ["13737"], "excerpt": "org for testingggg55", "org_url": "https://dev.yunolearning.com/org/org-for-testingggg55/", "event_label": "Org Details", "status": true, "industry": "Industry", "sub_industry": "sub_industry", "number_of_employees": 1, "logo_image": "https://dev.yunolearning.com/wp-content/uploads/2024/06/download-10.jpeg", "fav_icon": "https://dev.yunolearning.com/wp-content/uploads/2024/06/download-11.jpeg", "color_theme": {"font": "Roboto Serif", "background_color": "#87676", "theme_color": {"primary": "#00887"}}, "category": [{"id": 3060, "name": "IELTS", "slug": "ielts", "parent_id": 0, "sub_category": [{"id": 3076, "name": "<PERSON><PERSON>", "slug": "exam-module", "parent_id": 3060, "sub_category": [{"id": 3099, "name": "Reading", "slug": "reading", "parent_id": 3076, "sub_category": []}, {"id": 3100, "name": "Writing", "slug": "writing", "parent_id": 3076, "sub_category": []}]}, {"id": 3064, "name": "Target Band Score", "slug": "cognitive-level", "parent_id": 3060, "sub_category": [{"id": 3073, "name": "5.5", "slug": "5-5", "parent_id": 3064, "sub_category": []}]}]}, {"id": 4324, "name": "Microsoft Excel", "slug": "microsoft-excel", "parent_id": 0, "sub_category": [{"id": 4325, "name": "<PERSON><PERSON><PERSON>", "slug": "beginner-microsoft-excel", "parent_id": 4324, "sub_category": []}]}], "created_at": "2024-06-06 12:56:28", "description": "Org_description of the org.", "category_slug": "microsoft-excel", "user_id": [12465, 12463], "yuno_academy_subscription": {"is_active": false, "plan": {"country_code": "", "plan_label": "", "prasar": false, "max_instructors": 0, "plan_id": 0, "api_access": false}, "academies": ""}}, "@timestamp": "2024-06-06 13:00:03"}}}]}}