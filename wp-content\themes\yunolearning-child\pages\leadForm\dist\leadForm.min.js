const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":this.host()},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let o="";void 0!==t&&(o="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+o},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+o},studentResultsAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+o},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+o+"/"+n+"/"+s+"/"+r},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+o+"/"+n},classAPi:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+o+"/"+n+"/"+s},classesByViewAPi:function(e,t,o,s,r,a,i,u,l,c){let d="",p="",m="";c&&(d="?ver="+n()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==a&&!1!==a&&(m="/"+a);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+o+"/"+s+"/"+r+m+"/"+i+"/"+u+"/"+l+d},groupsAPi:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+o+"/"+n},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,o,n,s){let r="";!1!==s&&(r="?filter="+s);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+o+"/"+n+r},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+s},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+o},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+o+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},enrollmentList:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},enrollmentsList:function(e,t,o,n,s,r,a,i,u,l,c,d,p,m){let g="";g=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+g+"/enrollments/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a+"/"+i+"/"+u+"/"+l+"/"+m+"/"+c+"/"+d},paymentsList:function(e,t,o,s,r,a,i,u,l,c){let d="";c&&(d="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+o+"/"+s+"/"+r+"/"+a+"/"+i+"/"+u+"/"+l+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let o="",n="";void 0!==t&&!1!==t&&(n="/"+t),void 0!==e&&!1!==e&&(o="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+o+n},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+o+"/status/toggle"},changeBatchAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,o){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+o+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+s},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+o+"/"+n},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,o,n,s,r,a,i,u){let l="";void 0!==a&&!1!==a&&(l="/"+a);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+l+"/"+i+"/"+u},mapCoursesAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+o},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},deleteResourceAttachmentAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+o},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},videoListAPI:function(e,t){let o="",n="";void 0!==e&&(o=e),void 0===n&&!1===n||(n=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+o},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,o,n,s){let r="";if(!1!==t)r=t;else{let t="";void 0!==o&&(t=o),r=e+"/"+t+"/"+n+"/"+s}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+r},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+o},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,o,s,r,a,i){let u="",l="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(u="/"+e),void 0!==s&&!1!==s&&(d="/"+s),void 0!==r&&!1!==r&&(p="/"+r),void 0!==a&&!1!==a&&(m="/"+a),void 0!==o&&!1!==o&&(c="/"+o),i&&(l="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/review"+u+"/"+t+c+d+p+m+l},courseListAPI:function(e,t,o,s){let r="",a="",i="";s&&(r="?ver="+n()),void 0!==t&&!1!==t&&(a="/"+t),void 0!==o&&!1!==o&&(i="/"+o);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+a+i+r},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+o},learnerListAPI:function(e,t){let o="";t&&(o="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+o},instructorAvailabilityAPI:function(e,t,o){let s="",r="";o&&(s="?ver="+n()),void 0!==t&&!1!==t&&(r="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+r+s},createUpdateAvailabilityAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+o},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+o},instructorsByCategoryAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+o},capabilitiesAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+o},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+o},signInURLWithState(e){const t=["email","profile"],o=encodeURI(JSON.stringify(e));let n="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",o);n=e.toString()}else n="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+o+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return n},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+s},eventDetailAPI:function(e,t,o,s,r){let a="";r&&(a="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+o+"?uuid="+s+a},profileDetailAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+s},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},resourcesDetailAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+o+"/"+n},videoTestimonialAPI:function(e,t){let o="";t&&(o="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o},createExamResultAPI:function(e,t,o,n){let s="",r="";void 0===n&&!1===n&&(n="v1"),void 0!==t&&!1!==t&&(s="/"+t),void 0!==o&&!1!==o&&(r="/"+o);return this.pickHost()+"/wp-json/yuno/"+n+"/examresult/"+e+s+r},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,o){let n="",s="";void 0!==t&&(n="/"+t),void 0!==o&&(s="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n+s},videotestimonialListAPI:function(e,t,o,n){let s="",r="";void 0!==o&&(s="/"+o),void 0!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+s+r},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,o,n,s,r,a){let i="",u="",l="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==o&&!1!==o&&(c="/"+o),void 0!==n&&!1!==n&&(d="/"+n),void 0!==s&&!1!==s&&(p="/"+s),void 0!==r&&!1!==r&&(i="/"+r),void 0!==a&&!1!==a&&(u="/"+a);return this.pickHost()+"/wp-json/yuno/v1/article"+l+m+c+d+p+i+u},webinarSingleAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+s},webinarListingAPI:function(e,t,o,s,r){let a="";r&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+o+"/"+s+a},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,o,n,s,r,a){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+n},managelearningContentAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+o},learningContentAPI:function(e,t,o,n,s){let r="",a="",i="",u="";void 0!==t&&!1!==t&&(r="/"+t),void 0!==o&&!1!==o&&(a="/"+o),void 0!==n&&!1!==n&&(i="/"+n),void 0!==s&&!1!==s&&(u="/"+s);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+r+a+i+u},learnerInsightsAPI:function(e,t,o,s,r,a,i,u,l){let c="";l&&(c="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+o+"/"+s+"/"+r+"/"+a+"/"+i+"/"+u+c},learnerInsightsClassAPI:function(e,t,o,s){let r="",a="";a=void 0!==o&&!1!==o?"v2":"v1",s&&(r="?ver="+n());return this.pickHost()+"/wp-json/yuno/"+a+"/learner/class/"+e+"/"+t+r},signupFormAPI:function(e,t,o){let s="",r="";o&&(s="?ver="+n()),void 0!==t&&!1!==t&&(r="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+r+s},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,o,s,r){let a="";r&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+o+"/"+s+a},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,o,n){let s="";!1!==n&&(s="?filters="+encodeURI(JSON.stringify(n)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+o+"/"+s},resourcesResultsAPI:function(e,t,o,n,s,r,a){let i="";!1!==r&&(i="?filters="+encodeURI(JSON.stringify(r))),void 0!==a&&!1!==a||(a="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+a+"/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+i},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+s},instructorInsightsAPI:function(e,t,o,s,r,a,i,u){let l="";u&&(l="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+o+"/"+s+"/"+r+"/"+a+"/"+i+l},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+o},dashboardListAPI:function(e,t,o,n,s){let r="",a="",i="",u="";void 0!==o&&!1!==o&&(u="/"+o),void 0!==t&&!1!==t&&(i="/"+t),void 0!==n&&!1!==n&&(r="/"+n),void 0!==s&&!1!==s&&(a="/"+s);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+i+u+r+a},enrollmentDashboardAPI:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+o+"/"+n+"/"+s},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+o},publishedBlogsAPI:function(e,t,o,n,s,r){let a="",i="",u="",l="",c="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(u="/"+t),void 0!==o&&!1!==o&&(l="/"+o),void 0!==n&&!1!==n&&(c="/"+n),void 0!==s&&!1!==s&&(a="/"+s),void 0!==r&&!1!==r&&(i="/"+r);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+u+l+c+a+i},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,o,n,s,r){r=void 0!==r&&!1!==r?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"/"+n:"",s=void 0!==s&&!1!==s?"/"+s:"";return this.pickHost()+"/wp-json/yuno/v1/"+r+"/"+e+t+o+n+s},instructorMyCourses:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+o},instructorCourseBatches:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+o+"/batches/"+n+"/"+s+"/"+r},manageBookmarkAPI:function(e,t,o,n,s,r){void 0!==t&&!1!==t||(t="v1"),o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"/"+n:"",s=void 0!==s&&!1!==s?"/"+s:"",r=void 0!==r&&!1!==r?"?filters="+encodeURI(JSON.stringify(r)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+o+n+s+r},availableCourses:function(e,t,o,n){t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"?params="+encodeURI(JSON.stringify(n)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+o+n},availableBatches:function(e,t,o,n){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"?params="+encodeURI(JSON.stringify(n)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+o},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,o,n,s,r,a){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+o+"/"+n},pastBatchesList:function(e,t,o,n,s,r){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,o,n,s,r,a,i,u,l,c,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a+"/"+i+"/"+u+"/"+l+"/"+c+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+o+"/"+n+"/"+s},instructorVideotestimonial:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,o,n,s,r,a){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+o},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+o},classReviews:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+o+"/"+n+"/"+s},classReviewsByInstructor:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+o+"/"+n+"/"+s},listOfUser:function(e,t,o){let s="";o&&(s="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+s},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,o,n,s,r,a,i,u,l,c){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+o+"/"+n+"/"+s+"/"+r+"/"+a+"/"+i+"/"+u+"/"+l+"/"+c},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+o},courseEconomicsSummary:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+o+"/"+n},csvList:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+o+"/"+n+"/"+s},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,o,n){void 0===n&&(n="v1");return this.pickHost()+"/wp-json/yuno/"+n+"/referrer/user/"+e+"/"+t+"/"+o},referralReports:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+o+"/"+n},orgToken:function(e,t){let o="";return o="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,o},webhooks:function(e,t,o,n,s,r){let a="";return"grid"===e?a=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+o+"/"+n+"/"+s+"/"+r:"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?a=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),a},seo:function(e,t,o,n){let s="";return"status"===e?s=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?s=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+o+"/?search="+n),s},quiz:function(e,t,o,n){let s="";return void 0!==n&&!1!==n||(n=""),"create"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+n:"quizgrid"===e?s=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?s=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?s=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?s=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+o:"delete"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),s},question:function(e,t,o,n,s){let r="";return"questions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+o:"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?r=this.pickHost()+"/wp-json/yuno/v1/question/"+n:"deleteQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+s:"attempt"===e?r=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+n:"attemptQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+s:"questionset"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+s:"questionsetQuestions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+s:"questionsetQuestionsList"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+s:"questionsetQuestionsReorder"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),r},enrollments:function(e,t,o,n){let s="";return"active"===e?s=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+o+"/learners":"extendDate"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),s},blog:function(e,t,o,n){let s="";return"recentSingle"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+o+"/"+n+"?is_list=true&category_id="+t:"categoriesList"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),s},writingTask:function(e,t,o,n,s,r,a,i){let u="";return"type"===e?u=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+o:"create"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+n+"/"+s+"/"+r+"/"+a+"/"+i:"payload"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+n:"delete"===e&&(u=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+n),u},categoryLandingPage:function(e,t){let o="";return"category"===e?o=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(o=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),o},examResults:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+o+"/"+n},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,o,n,s,r,a,i,u,l,c,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${o}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${n}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${s}/${r}/${a}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${s}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${i}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${u}/batch`,courses:`/wp-json/yuno/v3/org/course/${o}/${t}/${n}/${s}/${l}/${c}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${o}/${n}/${s}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,o){let n="";return"steps"===e?n=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+o:"postStep"===e?n=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+o:"updateMobile"===e&&(n=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),n},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,o,n,s,r){let a="";switch(e){case"insights":a=`/wp-json/yuno/v2/users/${t}/learner/${o}/${n}/${s}`;break;case"demoRequests":a=`/wp-json/yuno/v1/demo-requests/${t}/list/${n}/${s}`;break;case"demoRequestsOrg":a=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${o}/${n}/${s}`;break;case"learnerDetailOrg":a=`/wp-json/yuno/v1/demo-requests/${t}/${r}`;break;case"learnerDetail":a=`/wp-json/yuno/v1/demo-requests/${r}`;break;case"instructorLearnerDetail":a=`/wp-json/yuno/v2/instructor/mylearner/${r}`;break;case"orgAdminLearners":a=`/wp-json/yuno/v2/orgadmin/learner/${o}/${n}/${s}`}return this.pickHost()+a},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,o,n,s){let r="";switch(e){case"googleFonts":r=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":r=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":r=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":r=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${o}/${n}`;break;case"orgBatches":r=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${s}`;break;case"org":r=`${this.pickHost()}/wp-json/yuno/v2/org/${n}`;break;case"categories":r=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return r},course:function(e,t,o,n,s,r){let a="";switch(e){case"payload":a=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":a=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":a=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":a=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${o}/${n}/${s}/${r}`;break;case"createBatchOrg":a=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":a=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":a=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return a},learner:function(e,t,o,n,s,r){let a="";switch(e){case"enrolledCourses":a=`/wp-json/yuno/v3/learner/${t}/enrollments/${o}`;break;case"classes":a=`/wp-json/yuno/v4/classes/${o}/${n}/${t}?limit=${s}&offset=${r}`;break;case"filters":a=`/wp-json/yuno/v4/classes/filter/${n}/${t}`}return this.pickHost()+a},classes:function(e,t,o,n,s,r,a,i,u,l,c){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${o}/${n}/${s}/${r}/${a}/${i}/${u}/${l}/${c}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private"}[e]||"")},attendance:function(e,t,o,n,s){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${o}/${n}/${s}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots"}[e]||"")},classInsights:function(e,t,o,n,s){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${o}/${n}/${s}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${o}/${n}/${s}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${o}/${n}/${s}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${o}/${n}/${s}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",activeOrg:"/wp-json/yuno/v3/user/state"}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`,detail:`/place/details/json?place_id=${t.placeID}&key=${t.key}`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")}},o=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var o=indexedDB.open("test");o.onerror=function(){e(!0)},o.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),n=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,o)=>e.find((e=>e[t]===o))||null,heightOfEle:function(e,t){let o=e.offsetHeight;if(t){let t=getComputedStyle(e);return o+=parseInt(t.marginTop)+parseInt(t.marginBottom),o}return o},assignVValidationObj:function(e){const t=window.VeeValidate,o=window.VeeValidateRules,n=t.ValidationProvider,s=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let n in e.messages)t.extend(n,o[n]);t.localize("validationMsg",e),Vue.component("ValidationProvider",n),Vue.component("ValidationObserver",s)},removeObjInArr:function(e,t,o){let n=e.length;for(;n--;)e[n]&&e[n].hasOwnProperty(t)&&arguments.length>2&&e[n][t]===o&&e.splice(n,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),o=0;o<t.length;o++){var n=t[o].split("=");if(n[0]==e)return n[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){o.then((function(t){e(t)}))},setCookie:function(e,t,o){let n=new Date;void 0===o&&(o=30),n.setTime(n.getTime()+24*o*60*60*1e3);let s="expires="+n.toGMTString();document.cookie=e+"="+t+";"+s+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:n,removeValInArr:function(e){let t,o,n=arguments,s=n.length;for(;s>1&&e.length;)for(t=n[--s];-1!==(o=e.indexOf(t));)e.splice(o,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,o){let n=e.match(t);return null!=n&&(!0===o?n[1].replace(/\/$/,""):n[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,o){let n=window.pageYOffset,s=(r=e,window.pageYOffset+document.querySelector(r).getBoundingClientRect().top);var r;targetY=document.body.scrollHeight-s<window.innerHeight?document.body.scrollHeight-window.innerHeight:s,customHeight=void 0!==o?o:74,diff=targetY-n-customHeight;let a="";diff&&window.requestAnimationFrame((function e(o){a||(a=o);let s=o-a,r=Math.min(s/t,1);var i;r=(i=r)<.5?4*i*i*i:(i-1)*(2*i-2)*(2*i-2)+1,window.scrollTo(0,n+diff*r),s<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var o=e.substring(1).split("&"),n=0;n<o.length;n++){var s=o[n].split("=");if(s[0]==t)return s[1]}return!1},cleanTextAndTruncate:function(e,t){let o=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(o=o.replace(/\s\s+/g," "),o.length>t){const e=o.lastIndexOf(" ",t-1);return o.substring(0,e)+"..."}return o}}}(jQuery),YUNOStore=function(e){const t=function(e,t,o,n,s){if(n){if(void 0===t.addToModule||t.addToModule)if(void 0!==s&&s){for(let t=0;t<o.length;t++)e.data.push(o[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=o}else t.moduleTabs?(e.error=o,e.loading=!1):e.error=o;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[]},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{ClassTitle:"",ClassDescription:"",ClassDate:"",ClassTime:"",ClassDuration:"",classSchedule:"",learner:[],RelatedCourses:"",BatchID:"",academy_id:""}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorPay",payment_mode:"generate_link",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:""}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,o){if(o.isError){let n=o.response.response,s=!(void 0===o.pushData||!o.pushData),r=e[o.store],a="";a=void 0!==n&&void 0!==n.data&&void 0!==n.data.message?n.data.message:YUNOCommon.config.errorMsg.common,console.log(a),console.log(o.store),o.tabs?(t(r.tabs[o.tabIndex],o,n,!1,s),r.tabs[o.tabIndex].error=!0,r.tabs[o.tabIndex].errorData=a,o.callback&&o.callbackFunc(r.tabs[o.tabIndex].errorData)):(t(r,o,n,!1,s),r.error=!0,r.errorData=a,o.callback&&o.callbackFunc(r.errorData))}else{let n=o.response.data.data,s=!(void 0===o.pushData||!o.pushData),r=o.response.data,a=e[o.store];o.tabs?(204===r.code&&(a.tabs[o.tabIndex].error=!0,a.tabs[o.tabIndex].errorData=r.message,console.log(r.message),console.log(o.store)),401===r.code&&(a.tabs[o.tabIndex].error=!0,a.tabs[o.tabIndex].errorData=r.message,console.log(r.message),console.log(o.store)),t(a.tabs[o.tabIndex],o,n,!0,s)):(204===r.code&&(a.error=!0,a.errorData=r.message,console.log(r.message),console.log(o.store)),401===r.code&&(a.error=!0,a.errorData=r.message,console.log(r.message),console.log(o.store)),t(a,o,n,!0,s)),o.callback&&o.callbackFunc(o)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,o){if(o.isError){let n=o.response;(void 0===o.overrideData||o.overrideData)&&t(e[o.module],o,n,!1),o.callback&&o.callbackFunc(e[o.module].errorData)}else{let n=o.response.data.data;(void 0===o.overrideData||o.overrideData)&&t(e[o.module],o,n,!0),o.callback&&o.callbackFunc(o)}},reviewsByType(e,o){if(o.isError){let n=o.response;t(e[o.module],o,n,!1)}else{let n=o.response.data.data;o.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[o.module],o,n,!0),o.isTabAdded=!0,setTimeout((()=>{o.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,o){if(o.isError){let n=o.response;t(e[o.module],o,n,!1)}else{let s=o.response.data.data;for(var n=0;n<s.length;n++)s[n].username_email_phone=`${s[n].username_email} (${s[n].phone})`;t(e[o.module],o,s,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let o=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(o,"id",t.classID),0===o.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let o=e[t.parentModule].data[t.classIndex];void 0!==o&&(o.isLoading=!1,o.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const o=localStorage.getItem("userSignUp");null!==o&&"pending"===o&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,o){if(o.isError){let n=o.response;"enrolled"===o.nestedTab?(e[o.module].tabs[o.tabIndex].loading=!1,e[o.module].tabs[o.tabIndex].pageLoading=!1,e[o.module].tabs[o.tabIndex].error=n):t(e[o.module],o,n,!1)}else o.callback&&o.callbackFunc(o,o.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let o=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let n=0;n<o.length;n++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(o[n]);let n=e[t.module].data[t.courseIndex].tabs[t.tabIndex];n.count=t.response.data.count,n.currentCount=n.data.length,n.offset=n.currentCount,n.isLoadMore=!1}else{for(let e=0;e<o.length;e++)o[e].isBatches=!1,o[e].isLoading=!1,o[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=o}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let n=t.response.data.data;if(void 0!==t.nested){let s=n.columns,r=n.rows,a={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&s.push(a);for(var o=0;o<r.length;o++)r[o].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+r[o].id};e[t.nested].tabs[0].data=n,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=n.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const o=function(o){o?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?o(!1):o(!0)},instructorLearners(e,t){const o=function(o){if(o&&void 0!==t.response.data){let o=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=o,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let n={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(o.group_created_time)[0]),group_created_time:o.group_created_time,group_id:""+o.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${o.group_id}`};e[t.module].tabs[t.index].data.unshift(n),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let o=e.instructorLearners.addLearner;o.selectedLearner="",o.payload.group_id="",o.payload.owner_id="",o.payload.user_ids=[],o.deleteUser=[],o.newAddedUser=[],o.payload.add_user_ids=[],o.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const s=e[t.module].tabs[t.index];s.hasData=!0,s.isLoadMore=!1;for(var n=0;n<o.length;n++)o[n].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(o[n].group_created_time)[0]),o[n].scheduleClassURL=`/class-schedule/?groupID=${o[n].group_id}`,s.data.push(o[n]);s.count=t.response.data.count,s.currentCount=s.data.length,s.offset=s.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?o(!1):(o(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},o){let n=t[o.store];n.loading=!0,axios.get(o.apiURL,{headers:void 0!==o.headers?o.headers:""}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)}))},postThirdPartyData({commit:e,state:t},o){let n=t[o.store];n.loading=!0,axios.defaults.timeout=void 0===o.timeout?0:o.timeout,"post"===o.method?axios.post(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"patch"===o.method?axios.patch(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"head"===o.method?axios.head(o.apiURL,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"put"===o.method?axios.put(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):console.log("not defined")},fetchData({commit:e,state:t},o){let n="",s="";s="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},n=void 0!==o.store?o.tabs?t[o.store].tabs[o.tabIndex]:t[o.store]:t[o.module],void 0===o.moduleLoading||o.moduleLoading?n.loading=!0:n.loading=!1,o.moduleTabs&&o.isTabLoader&&(n.loading=!0),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.get(o.apiURL,{headers:s}).then((n=>{o.response=n,o.isError=!1,e(o.module,o),403===(o.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===o.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,t[o.store].loading=!1,t[o.store].success=!0;let s="";s=void 0!==o.store?o.tabs?t[o.store].tabs[o.tabIndex]:t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),403===n.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===o.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),o.response=n,o.isError=!0,e(o.module,o)}))},putData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.put(o.apiURL,o.payload,{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},awsPutData({commit:e,state:t},o){let n={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization="",axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.put(o.apiURL,JSON.stringify(o.payload),{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,o.response=n,o.isError=!0,e(o.module,o)}))},postData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.post(o.apiURL,o.payload,{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},awsPostData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.post(o.apiURL,JSON.stringify(o.payload),{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},deleteData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.delete(o.apiURL,{headers:n,data:o.payload}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,o.response=n,o.isError=!0,e(o.module,o)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}});var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}},$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.ISOLATE_POLYFILLS=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,o){return e==Array.prototype||e==Object.prototype||(e[t]=o.value),e},$jscomp.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var t=0;t<e.length;++t){var o=e[t];if(o&&o.Math==Math)return o}throw Error("Cannot find global object")},$jscomp.global=$jscomp.getGlobal(this),$jscomp.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE,$jscomp.polyfills={},$jscomp.propertyToPolyfillSymbol={},$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(e,t){var o=$jscomp.propertyToPolyfillSymbol[t];return null==o?e[t]:void 0!==(o=e[o])?o:e[t]};$jscomp.polyfill=function(e,t,o,n){t&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(e,t,o,n):$jscomp.polyfillUnisolated(e,t,o,n))},$jscomp.polyfillUnisolated=function(e,t,o,n){for(o=$jscomp.global,e=e.split("."),n=0;n<e.length-1;n++){var s=e[n];if(!(s in o))return;o=o[s]}(t=t(n=o[e=e[e.length-1]]))!=n&&null!=t&&$jscomp.defineProperty(o,e,{configurable:!0,writable:!0,value:t})},$jscomp.polyfillIsolated=function(e,t,o,n){var s=e.split(".");e=1===s.length,n=s[0],n=!e&&n in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var r=0;r<s.length-1;r++){var a=s[r];if(!(a in n))return;n=n[a]}s=s[s.length-1],null!=(t=t(o=$jscomp.IS_SYMBOL_NATIVE&&"es6"===o?n[s]:null))&&(e?$jscomp.defineProperty($jscomp.polyfills,s,{configurable:!0,writable:!0,value:t}):t!==o&&($jscomp.propertyToPolyfillSymbol[s]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(s):$jscomp.POLYFILL_PREFIX+s,s=$jscomp.propertyToPolyfillSymbol[s],$jscomp.defineProperty(n,s,{configurable:!0,writable:!0,value:t})))},$jscomp.initSymbol=function(){},$jscomp.polyfill("Symbol",(function(e){if(e)return e;var t=function(e,t){this.$jscomp$symbol$id_=e,$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:t})};t.prototype.toString=function(){return this.$jscomp$symbol$id_};var o=0,n=function(e){if(this instanceof n)throw new TypeError("Symbol is not a constructor");return new t("jscomp_symbol_"+(e||"")+"_"+o++,e)};return n}),"es6","es3"),$jscomp.initSymbolIterator=function(){},$jscomp.polyfill("Symbol.iterator",(function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),o=0;o<t.length;o++){var n=$jscomp.global[t[o]];"function"==typeof n&&"function"!=typeof n.prototype[e]&&$jscomp.defineProperty(n.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return e}),"es6","es3"),$jscomp.initSymbolAsyncIterator=function(){},$jscomp.iteratorPrototype=function(e){return(e={next:e})[Symbol.iterator]=function(){return this},e},function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e=e||self).VeeValidate={},e.Vue)}(this,(function(e,t){function o(e,t,o,n){return new(o||(o=Promise))((function(s,r){function a(e){try{u(n.next(e))}catch(e){r(e)}}function i(e){try{u(n.throw(e))}catch(e){r(e)}}function u(e){e.done?s(e.value):new o((function(t){t(e.value)})).then(a,i)}u((n=n.apply(e,t||[])).next())}))}function n(e,t){function o(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,s&&(r=2&o[0]?s.return:o[0]?s.throw||((r=s.return)&&r.call(s),0):s.next)&&!(r=r.call(s,o[1])).done)return r;switch(s=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,s=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(r=i.trys,(r=0<r.length&&r[r.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3]))i.label=o[1];else if(6===o[0]&&i.label<r[1])i.label=r[1],r=o;else{if(!(r&&i.label<r[2])){r[2]&&i.ops.pop(),i.trys.pop();continue}i.label=r[2],i.ops.push(o)}}o=t.call(e,i)}catch(e){o=[6,e],s=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}var n,s,r,a,i={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a}function s(){for(var e=0,t=0,o=arguments.length;t<o;t++)e+=arguments[t].length;e=Array(e);var n=0;for(t=0;t<o;t++)for(var s=arguments[t],r=0,a=s.length;r<a;r++,n++)e[n]=s[r];return e}function r(e){return null==e}function a(e,t){if(e instanceof RegExp&&t instanceof RegExp)return a(e.source,t.source)&&a(e.flags,t.flags);if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var o=0;o<e.length;o++)if(!a(e[o],t[o]))return!1;return!0}return G(e)&&G(t)?Object.keys(e).every((function(o){return a(e[o],t[o])}))&&Object.keys(t).every((function(o){return a(e[o],t[o])})):e!=e&&t!=t||e===t}function i(e){return""!==e&&!r(e)}function u(e){return"function"==typeof e}function l(e){return u(e)&&!!e.__locatorRef}function c(e,t){var o=Array.isArray(e)?e:p(e);if(u(o.findIndex))return o.findIndex(t);for(var n=0;n<o.length;n++)if(t(o[n],n))return n;return-1}function d(e,t){return-1!==e.indexOf(t)}function p(e){if(u(Array.from))return Array.from(e);for(var t=[],o=e.length,n=0;n<o;n++)t.push(e[n]);return t}function m(e){return u(Object.values)?Object.values(e):Object.keys(e).map((function(t){return e[t]}))}function g(e,t){return Object.keys(t).forEach((function(o){G(t[o])?(e[o]||(e[o]={}),g(e[o],t[o])):e[o]=t[o]})),e}function h(e){return e}function f(e,t,o){return void 0===t&&(t=0),void 0===o&&(o={cancelled:!1}),0===t?e:function(){for(var s=[],r=0;r<arguments.length;r++)s[r]=arguments[r];clearTimeout(n),n=setTimeout((function(){n=void 0,o.cancelled||e.apply(void 0,s)}),t)};var n}function v(e,t){return e.replace(/{([^}]+)}/g,(function(e,o){return o in t?t[o]:"{"+o+"}"}))}function y(e){var t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?G(e)&&e._$$isNormalized?e:G(e)?Object.keys(e).reduce((function(t,o){var n=!0===e[o]?[]:Array.isArray(e[o])||G(e[o])?e[o]:[e[o]];return!1!==e[o]&&(t[o]=b(o,n)),t}),t):"string"!=typeof e?(console.warn("[vee-validate] rules must be either a string or an object."),t):e.split("|").reduce((function(e,t){var o=[],n=t.split(":")[0];return d(t,":")&&(o=t.split(":").slice(1).join(":").split(",")),n?(e[n]=b(n,o),e):e}),t):t}function b(e,t){var o=K.getRuleDefinition(e);if(!o)return t;var n={};if(!o.params&&!Array.isArray(t))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(t)&&!o.params)return t;if(!o.params||o.params.length<t.length&&Array.isArray(t))var s,r=t.map((function(e,t){var n,r=null===(n=o.params)||void 0===n?void 0:n[t];return s=r||s,r||(r=s),r}));else r=o.params;for(var a=0;a<r.length;a++){var i=r[a],u=i.default;Array.isArray(t)?a in t&&(u=t[a]):i.name in t?u=t[i.name]:1===r.length&&(u=t),i.isTarget&&(u=w(u,i.cast)),"string"==typeof u&&"@"===u[0]&&(u=w(u.slice(1),i.cast)),!l(u)&&i.cast&&(u=i.cast(u)),n[i.name]?(n[i.name]=Array.isArray(n[i.name])?n[i.name]:[n[i.name]],n[i.name].push(u)):n[i.name]=u}return n}function w(e,t){var o=function(o){return o=o[e],t?t(o):o};return o.__locatorRef=e,o}function k(e,t,s){var r,a,i,u,l,c;return void 0===s&&(s={}),o(this,void 0,void 0,(function(){var o,d,p,m,g,h;return n(this,(function(n){switch(n.label){case 0:return o=null===(r=s)||void 0===r?void 0:r.bails,d=null===(a=s)||void 0===a?void 0:a.skipIfEmpty,[4,A({name:(null===(i=s)||void 0===i?void 0:i.name)||"{field}",rules:y(t),bails:null==o||o,skipIfEmpty:null==d||d,forceRequired:!1,crossTable:(null===(u=s)||void 0===u?void 0:u.values)||{},names:(null===(l=s)||void 0===l?void 0:l.names)||{},customMessages:(null===(c=s)||void 0===c?void 0:c.customMessages)||{}},e,s)];case 1:return p=n.sent(),m=[],g={},h={},p.errors.forEach((function(e){var t=e.msg();m.push(t),g[e.rule]=t,h[e.rule]=e.msg})),[2,{valid:p.valid,errors:m,failedRules:g,regenerateMap:h}]}}))}))}function A(e,t,s){var r=void 0!==(s=(void 0===s?{}:s).isInitial)&&s;return o(this,void 0,void 0,(function(){var o,s,a,i,u,l,c,d;return n(this,(function(n){switch(n.label){case 0:return[4,j(e,t)];case 1:if(o=n.sent(),s=o.shouldSkip,a=o.errors,s)return[2,{valid:!a.length,errors:a}];i=Object.keys(e.rules).filter((function(e){return!K.isRequireRule(e)})),u=i.length,l=0,n.label=2;case 2:return l<u?r&&K.isLazy(i[l])?[3,4]:(c=i[l],[4,I(e,t,{name:c,params:e.rules[c]})]):[3,5];case 3:if(!(d=n.sent()).valid&&d.error&&(a.push(d.error),e.bails))return[2,{valid:!1,errors:a}];n.label=4;case 4:return l++,[3,2];case 5:return[2,{valid:!a.length,errors:a}]}}))}))}function j(e,t){return o(this,void 0,void 0,(function(){var o,s,a,i,u,l,c,d,p;return n(this,(function(n){switch(n.label){case 0:var m;o=Object.keys(e.rules).filter(K.isRequireRule),s=o.length,a=[],(m=r(t)||""===t)||(m=Array.isArray(t)&&0===t.length),u=(i=m)&&e.skipIfEmpty,l=!1,c=0,n.label=1;case 1:return c<s?(d=o[c],[4,I(e,t,{name:d,params:e.rules[d]})]):[3,4];case 2:if(p=n.sent(),!G(p))throw Error("Require rules has to return an object (see docs)");if(p.required&&(l=!0),!p.valid&&p.error&&(a.push(p.error),e.bails))return[2,{shouldSkip:!0,errors:a}];n.label=3;case 3:return c++,[3,1];case 4:return i&&!l&&!e.skipIfEmpty||!e.bails&&!u?[2,{shouldSkip:!1,errors:a}]:[2,{shouldSkip:!l&&i,errors:a}]}}))}))}function I(e,t,s){return o(this,void 0,void 0,(function(){var o,r,a,i,u;return n(this,(function(n){switch(n.label){case 0:if(!(o=K.getRuleDefinition(s.name))||!o.validate)throw Error("No such validator '"+s.name+"' exists.");return r=o.castValue?o.castValue(t):t,a=function(e,t){if(Array.isArray(e))return e;var o={};return Object.keys(e).forEach((function(n){var s=e[n];s=l(s)?s(t):s,o[n]=s})),o}(s.params,e.crossTable),[4,o.validate(r,a)];case 1:return"string"==typeof(i=n.sent())?(u=Z(Z({},a||{}),{_field_:e.name,_value_:t,_rule_:s.name}),[2,{valid:!1,error:{rule:s.name,msg:function(){return v(i,u)}}}]):(G(i)||(i={valid:i}),[2,{valid:i.valid,required:i.required,error:i.valid?void 0:S(e,t,o,s.name,a)}])}}))}))}function S(e,t,o,n,s){var r,a=null!=(r=e.customMessages[n])?r:o.message;r=function(e,t,o){if(t=t.params,!t||0>=t.filter((function(e){return e.isTarget})).length)return{};var n={},s=e.rules[o];for(!Array.isArray(s)&&G(s)&&(s=t.map((function(e){return s[e.name]}))),o=0;o<t.length;o++){var r=t[o],a=s[o];l(a)&&(a=a.__locatorRef,n[r.name]=e.names[a]||a,n["_"+r.name+"_"]=e.crossTable[a])}return n}(e,o,n),o=function(e,t,o,n){var s={},r=e.rules[o],a=t.params||[];return r?(Object.keys(r).forEach((function(t,o){var n=r[t];if(!l(n))return{};var i=a[o];if(!i)return{};n=n.__locatorRef,s[i.name]=e.names[n]||n,s["_"+i.name+"_"]=e.crossTable[n]})),{userTargets:s,userMessage:n}):{}}(e,o,n,a),a=o.userTargets;var i=o.userMessage,u=Z(Z(Z(Z({},s||{}),{_field_:e.name,_value_:t,_rule_:n}),r),a);return{msg:function(){var t=i||Q.defaultMessage,o=e.name;return t="function"==typeof t?t(o,u):v(t,Z(Z({},u),{_field_:o}))},rule:n}}function _(){ee.$emit("change:locale")}function $(e){if(e.data){var t=e.data;if("model"in t)return t.model;if(e.data.directives)return function(e,t){var o=Array.isArray(e)?e:p(e),n=c(o,t);return-1===n?void 0:o[n]}(e.data.directives,(function(e){return"model"===e.name}))}}function D(e){var t,o,n,s=$(e);return s?{value:s.value}:(s=(null===(t=P(e))||void 0===t?void 0:t.prop)||"value",null!==(o=e.componentOptions)&&void 0!==o&&o.propsData&&s in e.componentOptions.propsData?{value:e.componentOptions.propsData[s]}:null!==(n=e.data)&&void 0!==n&&n.domProps&&"value"in e.data.domProps?{value:e.data.domProps.value}:void 0)}function L(e){return Array.isArray(e)||void 0===D(e)?function(e){return Array.isArray(e)?e:Array.isArray(e.children)?e.children:e.componentOptions&&Array.isArray(e.componentOptions.children)?e.componentOptions.children:[]}(e).reduce((function(e,t){var o=L(t);return o.length&&e.push.apply(e,o),e}),[]):[e]}function P(e){return e.componentOptions?e.componentOptions.Ctor.options.model:null}function C(e,t,o){r(e[t])?e[t]=[o]:u(e[t])&&e[t].fns?((e=e[t]).fns=Array.isArray(e.fns)?e.fns:[e.fns],d(e.fns,o)||e.fns.push(o)):(u(e[t])&&(e[t]=[e[t]]),Array.isArray(e[t])&&!d(e[t],o)&&e[t].push(o))}function H(e,t,o){e.componentOptions?e.componentOptions&&(e.componentOptions.listeners||(e.componentOptions.listeners={}),C(e.componentOptions.listeners,t,o)):(e.data||(e.data={}),r(e.data.on)&&(e.data.on={}),C(e.data.on,t,o))}function T(e,t){var o;return e.componentOptions?(P(e)||{event:"input"}).event:null!==(o=null==t?void 0:t.modifiers)&&void 0!==o&&o.lazy?"change":oe(e)?"input":"change"}function O(e,t){return e.$scopedSlots.default?e.$scopedSlots.default(t)||[]:e.$slots.default||[]}function R(e){return Z(Z({},e.flags),{errors:e.errors,classes:e.classes,failedRules:e.failedRules,reset:function(){return e.reset()},validate:function(){for(var t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];return e.validate.apply(e,t)},ariaInput:{"aria-invalid":e.flags.invalid?"true":"false","aria-required":e.isRequired?"true":"false","aria-errormessage":"vee_"+e.id},ariaMsg:{id:"vee_"+e.id,"aria-live":e.errors.length?"assertive":"off"}})}function U(e,t){e.initialized||(e.initialValue=t);var o=!!(!e._ignoreImmediate&&e.immediate||e.value!==t&&e.normalizedEvents.length||e._needsValidation||!e.initialized&&void 0===t);if(e._needsValidation=!1,e.value=t,e._ignoreImmediate=!0,o){var n=function(){if(e.immediate||e.flags.validated)return E(e);e.validateSilent()};e.initialized?n():e.$once("hook:mounted",(function(){return n()}))}}function x(e){return(u(e.mode)?e.mode:X[e.mode])(e)}function E(e){var t=e.validateSilent();return e._pendingValidation=t,t.then((function(o){return t===e._pendingValidation&&(e.applyResult(o),e._pendingValidation=void 0),o}))}function M(e){e.$veeOnInput||(e.$veeOnInput=function(t){e.syncValue(t),e.setFlags({dirty:!0,pristine:!1})});var t=e.$veeOnInput;e.$veeOnBlur||(e.$veeOnBlur=function(){e.setFlags({touched:!0,untouched:!1})});var o=e.$veeOnBlur,n=e.$veeHandler,s=x(e);return n&&e.$veeDebounce===e.debounce||(n=f((function(){e.$nextTick((function(){e._pendingReset||E(e),e._pendingReset=!1}))}),s.debounce||e.debounce),e.$veeHandler=n,e.$veeDebounce=e.debounce),{onInput:t,onBlur:o,onValidate:n}}function N(e){var t=e.$_veeObserver.refs;return e.fieldDeps.reduce((function(e,o){return t[o]?(e.values[o]=t[o].value,e.names[o]=t[o].name,e):e}),{names:{},values:{}})}function B(e,t,o){void 0===o&&(o=!0);var n=e.$_veeObserver.refs;if(e._veeWatchers||(e._veeWatchers={}),!n[t]&&o)return e.$once("hook:mounted",(function(){B(e,t,!1)}));!u(e._veeWatchers[t])&&n[t]&&(e._veeWatchers[t]=n[t].$watch("value",(function(){e.flags.validated&&(e._needsValidation=!0,e.validate())})))}function F(e){e.$_veeObserver&&e.$_veeObserver.unobserve(e.id,"observer")}function V(e){e.$_veeObserver&&e.$_veeObserver.observe(e,"observer")}function Y(){return Z(Z({},{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1}),{valid:!0,invalid:!1})}function q(){for(var e=s(m(this.refs),this.observers),t={},o=Y(),n={},r=e.length,a=0;a<r;a++){var i=e[a];Array.isArray(i.errors)?(t[i.id]=i.errors,n[i.id]=Z({id:i.id,name:i.name,failedRules:i.failedRules},i.flags)):(t=Z(Z({},t),i.errors),n=Z(Z({},n),i.fields))}return re.forEach((function(t){var n=t[0];o[n]=e[t[1]]((function(e){return e.flags[n]}))})),{errors:t,flags:o,fields:n}}t=t&&t.hasOwnProperty("default")?t.default:t;var z,Z=function(){return Z=Object.assign||function(e){for(var t,o=1,n=arguments.length;o<n;o++)for(var s in t=arguments[o])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},Z.apply(this,arguments)},G=function(e){return null!==e&&e&&"object"==typeof e&&!Array.isArray(e)},W={},K=function(){function e(){}return e.extend=function(e,t){var o=function(e){var t;return null!==(t=e.params)&&void 0!==t&&t.length&&(e.params=e.params.map((function(e){return"string"==typeof e?{name:e}:e}))),e}(t);W[e]=W[e]?g(W[e],t):Z({lazy:!1,computesRequired:!1},o)},e.isLazy=function(e){var t;return!(null===(t=W[e])||void 0===t||!t.lazy)},e.isRequireRule=function(e){var t;return!(null===(t=W[e])||void 0===t||!t.computesRequired)},e.getRuleDefinition=function(e){return W[e]},e}(),Q=Z({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),J=function(e){Q=Z(Z({},Q),e)},X={aggressive:function(){return{on:["input","blur"]}},eager:function(e){return e.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},ee=new t,te=function(){function e(e,t){this.container={},this.locale=e,this.merge(t)}return e.prototype.resolve=function(e,t,o){return this.format(this.locale,e,t,o)},e.prototype.format=function(e,t,o,n){var s,r,a,i,l,c,d,p;return(o=(null===(a=null===(r=null===(s=this.container[e])||void 0===s?void 0:s.fields)||void 0===r?void 0:r[t])||void 0===a?void 0:a[o])||(null===(l=null===(i=this.container[e])||void 0===i?void 0:i.messages)||void 0===l?void 0:l[o]))||(o="{field} is not valid"),t=null!=(p=null===(d=null===(c=this.container[e])||void 0===c?void 0:c.names)||void 0===d?void 0:d[t])?p:t,u(o)?o(t,n):v(o,Z(Z({},n),{_field_:t}))},e.prototype.merge=function(e){g(this.container,e)},e.prototype.hasRule=function(e){var t,o;return!(null===(o=null===(t=this.container[this.locale])||void 0===t?void 0:t.messages)||void 0===o||!o[e])},e}(),oe=function(e){var t,o=(null===(t=e.data)||void 0===t?void 0:t.attrs)||e.elm;return!("input"!==e.tag||o&&o.type)||"textarea"===e.tag||d("text password search email tel url number".split(" "),null==o?void 0:o.type)},ne=0,se=t.extend({inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver={refs:{},observe:function(e){this.refs[e.id]=e},unobserve:function(e){delete this.refs[e]}}),this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,default:""},name:{type:String,default:null},mode:{type:[String,Function],default:function(){return Q.mode}},rules:{type:[Object,String],default:null},immediate:{type:Boolean,default:!1},bails:{type:Boolean,default:function(){return Q.bails}},skipIfEmpty:{type:Boolean,default:function(){return Q.skipOptional}},debounce:{type:Number,default:0},tag:{type:String,default:"span"},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},customMessages:{type:Object,default:function(){return{}}}},watch:{rules:{deep:!0,handler:function(e,t){this._needsValidation=!a(e,t)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1},failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var e=this;return Object.keys(this.normalizedRules).reduce((function(t,o){var n=function(e){return Array.isArray(e)?e.filter(l):Object.keys(e).filter((function(t){return l(e[t])})).map((function(t){return e[t]}))}(e.normalizedRules[o]).map((function(e){return e.__locatorRef}));return t.push.apply(t,n),n.forEach((function(t){B(e,t)})),t}),[])},normalizedEvents:function(){var e=this;return(x(this).on||[]).map((function(t){return"input"===t?e._inputEventName:t}))},isRequired:function(){var e=Z(Z({},this._resolvedRules),this.normalizedRules);return e=Object.keys(e).some(K.isRequireRule),this.flags.required=!!e,e},classes:function(){return function(e,t){for(var o={},n=Object.keys(t),s=n.length,a=function(s){s=n[s];var a=e&&e[s]||s,i=t[s];if(r(i)||("valid"===s||"invalid"===s)&&!t.validated)return"continue";"string"==typeof a?o[a]=i:Array.isArray(a)&&a.forEach((function(e){o[e]=i}))},i=0;i<s;i++)a(i);return o}(Q.classes,this.flags)},normalizedRules:function(){return y(this.rules)}},created:function(){var e=this,t=function(){if(e.flags.validated){var t=e._regenerateMap;if(t){var o=[],n={};Object.keys(t).forEach((function(e){var s=t[e]();o.push(s),n[e]=s})),e.applyResult({errors:o,failedRules:n,regenerateMap:t})}else e.validate()}};ee.$on("change:locale",t),this.$on("hook:beforeDestroy",(function(){ee.$off("change:locale",t)}))},render:function(e){var t=this;this.registerField();var o=R(this);return L(o=O(this,o)).forEach((function(e){var o,n,s,r,u;if(Q.useConstraintAttrs){var l,c=null===(l=e.data)||void 0===l?void 0:l.attrs;if(d(["input","select","textarea"],e.tag)&&c)if(l={},"required"in c&&!1!==c.required&&K.getRuleDefinition("required")&&(l.required="checkbox"!==c.type||[!0]),oe(e)){c=Z,l=Z({},l);var p=null===(u=e.data)||void 0===u?void 0:u.attrs;u={},p&&("email"===p.type&&K.getRuleDefinition("email")&&(u.email=["multiple"in p]),p.pattern&&K.getRuleDefinition("regex")&&(u.regex=p.pattern),0<=p.maxlength&&K.getRuleDefinition("max")&&(u.max=p.maxlength),0<=p.minlength&&K.getRuleDefinition("min")&&(u.min=p.minlength),"number"===p.type&&(i(p.min)&&K.getRuleDefinition("min_value")&&(u.min_value=Number(p.min)),i(p.max)&&K.getRuleDefinition("max_value")&&(u.max_value=Number(p.max)))),u=y(c(l,u))}else u=y(l);else u={}}else u={};a(t._resolvedRules,u)||(t._needsValidation=!0),d(["input","select","textarea"],e.tag)&&(t.fieldName=(null===(n=null===(o=e.data)||void 0===o?void 0:o.attrs)||void 0===n?void 0:n.name)||(null===(r=null===(s=e.data)||void 0===s?void 0:s.attrs)||void 0===r?void 0:r.id)),t._resolvedRules=u,function(e,t){var o=D(t);e._inputEventName=e._inputEventName||T(t,$(t)),U(e,null==o?void 0:o.value);var n=(o=M(e)).onBlur,s=o.onValidate;H(t,e._inputEventName,o.onInput),H(t,"blur",n),e.normalizedEvents.forEach((function(e){H(t,e,s)})),e.initialized=!0}(t,e)})),this.slim&&1>=o.length?o[0]:e(this.tag,o)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(e){var t=this;Object.keys(e).forEach((function(o){t.flags[o]=e[o]}))},syncValue:function(e){this.value=e=function(e){var t,o;return e&&("undefined"!=typeof Event&&u(Event)&&e instanceof Event||e&&e.srcElement)?"file"===(e=e.target).type&&e.files?p(e.files):null!==(t=e._vModifiers)&&void 0!==t&&t.number?(t=parseFloat(e.value))!=t?e.value:t:null!==(o=e._vModifiers)&&void 0!==o&&o.trim&&"string"==typeof e.value?e.value.trim():e.value:e}(e),this.flags.changed=this.initialValue!==e},reset:function(){var e=this;this.errors=[],this.initialValue=this.value;var t={untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1};t.required=this.isRequired,this.setFlags(t),this.failedRules={},this.validateSilent(),this._pendingValidation=void 0,this._pendingReset=!0,setTimeout((function(){e._pendingReset=!1}),this.debounce)},validate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o(this,void 0,void 0,(function(){return n(this,(function(t){return 0<e.length&&this.syncValue(e[0]),[2,E(this)]}))}))},validateSilent:function(){return o(this,void 0,void 0,(function(){var e,t;return n(this,(function(o){switch(o.label){case 0:return this.setFlags({pending:!0}),e=Z(Z({},this._resolvedRules),this.normalizedRules),Object.defineProperty(e,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,k(this.value,e,Z(Z({name:this.name||this.fieldName},N(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return t=o.sent(),this.setFlags({pending:!1,valid:t.valid,invalid:!t.valid}),[2,t]}}))}))},setErrors:function(e){this.applyResult({errors:e,failedRules:{}})},applyResult:function(e){var t=e.errors,o=e.failedRules;e=e.regenerateMap,this.errors=t,this._regenerateMap=e,this.failedRules=Z({},o||{}),this.setFlags({valid:!t.length,passed:!t.length,invalid:!!t.length,failed:!!t.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var e=function(e){return e.vid?e.vid:e.name?e.name:e.id?e.id:e.fieldName?e.fieldName:"_vee_"+ ++ne}(this),t=this.id;!this.isActive||t===e&&this.$_veeObserver.refs[t]||(t!==e&&this.$_veeObserver.refs[t]===this&&this.$_veeObserver.unobserve(t),this.id=e,this.$_veeObserver.observe(this))}}}),re=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],ae=0,ie=t.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,default:"span"},vid:{type:String,default:function(){return"obs_"+ae++}},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:Y(),fields:{}}},created:function(){var e=this;this.id=this.vid,V(this);var t=f((function(t){var o=t.flags,n=t.fields;e.errors=t.errors,e.flags=o,e.fields=n}),16);this.$watch(q,t)},activated:function(){V(this)},deactivated:function(){F(this)},beforeDestroy:function(){F(this)},render:function(e){var t=O(this,Z(Z({},this.flags),{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=t.length?t[0]:e(this.tag,{on:this.$listeners},t)},methods:{observe:function(e,t){var o;void 0===t&&(t="provider"),"observer"===t?this.observers.push(e):this.refs=Z(Z({},this.refs),((o={})[e.id]=e,o))},unobserve:function(e,t){if(void 0===t&&(t="provider"),"provider"===t)this.refs[e]&&this.$delete(this.refs,e);else{var o=c(this.observers,(function(t){return t.id===e}));-1!==o&&this.observers.splice(o,1)}},validate:function(e){var t=void 0!==(e=(void 0===e?{}:e).silent)&&e;return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return[4,Promise.all(s(m(this.refs).filter((function(e){return!e.disabled})).map((function(e){return e[t?"validateSilent":"validate"]().then((function(e){return e.valid}))})),this.observers.filter((function(e){return!e.disabled})).map((function(e){return e.validate({silent:t})}))))];case 1:return[2,e.sent().every((function(e){return e}))]}}))}))},handleSubmit:function(e){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return[4,this.validate()];case 1:return t.sent()&&e?[2,e()]:[2]}}))}))},reset:function(){return s(m(this.refs),this.observers).forEach((function(e){return e.reset()}))},setErrors:function(e){var t=this;Object.keys(e).forEach((function(o){var n=t.refs[o];n&&(o="string"==typeof(o=e[o]||[])?[o]:o,n.setErrors(o))})),this.observers.forEach((function(t){t.setErrors(e)}))}}});e.ValidationObserver=ie,e.ValidationProvider=se,e.configure=function(e){J(e)},e.extend=function(e,t){if(!u(t)&&!u(t.validate)&&!K.getRuleDefinition(e))throw Error("Extension Error: The validator '"+e+"' must be a function or have a 'validate' method.");"object"==typeof t?K.extend(e,t):K.extend(e,{validate:t})},e.localeChanged=_,e.localize=function(e,t){var o;z||(z=new te("en",{}),J({defaultMessage:function(e,t){return z.resolve(e,null==t?void 0:t._rule_,t||{})}})),"string"==typeof e?(z.locale=e,t&&z.merge(((o={})[e]=t,o)),_()):z.merge(e)},e.normalizeRules=y,e.setInteractionMode=function(e,t){if(J({mode:e}),t){if(!u(t))throw Error("A mode implementation must be a function");X[e]=t}},e.validate=k,e.version="3.2.3",e.withValidation=function(e,t){void 0===t&&(t=h);var o,n="options"in e?e.options:e,s=se.options;s={name:(n.name||"AnonymousHoc")+"WithValidation",props:Z({},s.props),data:s.data,computed:Z({},s.computed),methods:Z({},s.methods),beforeDestroy:s.beforeDestroy,inject:s.inject};var r=(null===(o=null==n?void 0:n.model)||void 0===o?void 0:o.event)||"input";return s.render=function(e){var o;this.registerField();var s=R(this),a=Z({},this.$listeners),i=$(this.$vnode);this._inputEventName=this._inputEventName||T(this.$vnode,i);var u=D(this.$vnode);U(this,null==u?void 0:u.value);var l=(u=M(this)).onBlur,c=u.onValidate;return C(a,r,u.onInput),C(a,"blur",l),this.normalizedEvents.forEach((function(e){C(a,e,c)})),u=(P(this.$vnode)||{prop:"value"}).prop,s=Z(Z(Z({},this.$attrs),((o={})[u]=null==i?void 0:i.value,o)),t(s)),e(n,{attrs:this.$attrs,props:s,on:a},function(e,t){return Object.keys(e).reduce((function(o,n){return e[n].forEach((function(o){o.context||(e[n].context=t,o.data||(o.data={}),o.data.slot=n)})),o.concat(e[n])}),[])}(this.$slots,this.$vnode.context))},s},Object.defineProperty(e,"__esModule",{value:!0})})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).VeeValidateRules={})}(this,(function(e){"use strict";var t={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},o={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},n={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},r=function(e,o){var n=(void 0===o?{}:o).locale,s=void 0===n?"":n;return Array.isArray(e)?e.every((function(e){return r(e,{locale:s})})):s?(t[s]||t.en).test(e):Object.keys(t).some((function(o){return t[o].test(e)}))},a={validate:r,params:[{name:"locale"}]},i=function(e,t){var o=(void 0===t?{}:t).locale,n=void 0===o?"":o;return Array.isArray(e)?e.every((function(e){return i(e,{locale:n})})):n?(s[n]||s.en).test(e):Object.keys(s).some((function(t){return s[t].test(e)}))},u={validate:i,params:[{name:"locale"}]},l=function(e,t){var o=(void 0===t?{}:t).locale,s=void 0===o?"":o;return Array.isArray(e)?e.every((function(e){return l(e,{locale:s})})):s?(n[s]||n.en).test(e):Object.keys(n).some((function(t){return n[t].test(e)}))},c={validate:l,params:[{name:"locale"}]},d=function(e,t){var n=(void 0===t?{}:t).locale,s=void 0===n?"":n;return Array.isArray(e)?e.every((function(e){return d(e,{locale:s})})):s?(o[s]||o.en).test(e):Object.keys(o).some((function(t){return o[t].test(e)}))},p={validate:d,params:[{name:"locale"}]},m=function(e,t){var o=void 0===t?{}:t,n=o.min,s=o.max;return Array.isArray(e)?e.every((function(e){return!!m(e,{min:n,max:s})})):Number(n)<=e&&Number(s)>=e},g={validate:m,params:[{name:"min"},{name:"max"}]},h={validate:function(e,t){var o=t.target;return String(e)===String(o)},params:[{name:"target",isTarget:!0}]},f=function(e,t){var o=t.length;if(Array.isArray(e))return e.every((function(e){return f(e,{length:o})}));var n=String(e);return/^[0-9]*$/.test(n)&&n.length===o},v={validate:f,params:[{name:"length",cast:function(e){return Number(e)}}]},y={validate:function(e,t){var o=t.width,n=t.height,s=[];e=Array.isArray(e)?e:[e];for(var r=0;r<e.length;r++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(e[r].name))return Promise.resolve(!1);s.push(e[r])}return Promise.all(s.map((function(e){return t=e,s=o,r=n,a=window.URL||window.webkitURL,new Promise((function(e){var o=new Image;o.onerror=function(){return e(!1)},o.onload=function(){return e(o.width===s&&o.height===r)},o.src=a.createObjectURL(t)}));var t,s,r,a}))).then((function(e){return e.every((function(e){return e}))}))},params:[{name:"width",cast:function(e){return Number(e)}},{name:"height",cast:function(e){return Number(e)}}]},b={validate:function(e,t){var o=(void 0===t?{}:t).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return o&&!Array.isArray(e)&&(e=String(e).split(",").map((function(e){return e.trim()}))),Array.isArray(e)?e.every((function(e){return n.test(String(e))})):n.test(String(e))},params:[{name:"multiple",default:!1}]};function w(e){return null==e}function k(e){return Array.isArray(e)&&0===e.length}function A(e){return"function"==typeof Array.from?Array.from(e):function(e){for(var t=[],o=e.length,n=0;n<o;n++)t.push(e[n]);return t}(e)}function j(e){return k(e)||-1!==[!1,null,void 0].indexOf(e)||!String(e).trim().length}var I=function(e,t){return Array.isArray(e)?e.every((function(e){return I(e,t)})):A(t).some((function(t){return t==e}))},S={validate:I},_={validate:function(e,t){return!I(e,t)}},$={validate:function(e,t){var o=new RegExp(".("+t.join("|")+")$","i");return Array.isArray(e)?e.every((function(e){return o.test(e.name)})):o.test(e.name)}},D={validate:function(e){var t=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(e)?e.every((function(e){return t.test(e.name)})):t.test(e.name)}},L={validate:function(e){return Array.isArray(e)?e.every((function(e){return/^-?[0-9]+$/.test(String(e))})):/^-?[0-9]+$/.test(String(e))}},P={validate:function(e,t){var o=t.length;return!w(e)&&("number"==typeof e&&(e=String(e)),e.length||(e=A(e)),e.length===o)},params:[{name:"length",cast:function(e){return Number(e)}}]},C=function(e,t){var o=t.length;return w(e)?0<=o:Array.isArray(e)?e.every((function(e){return C(e,{length:o})})):String(e).length<=o},H={validate:C,params:[{name:"length",cast:function(e){return Number(e)}}]},T=function(e,t){var o=t.max;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every((function(e){return T(e,{max:o})})):Number(e)<=o)},O={validate:T,params:[{name:"max",cast:function(e){return Number(e)}}]},R={validate:function(e,t){var o=new RegExp(t.join("|").replace("*",".+")+"$","i");return Array.isArray(e)?e.every((function(e){return o.test(e.type)})):o.test(e.type)}},U=function(e,t){var o=t.length;return!w(e)&&(Array.isArray(e)?e.every((function(e){return U(e,{length:o})})):String(e).length>=o)},x={validate:U,params:[{name:"length",cast:function(e){return Number(e)}}]},E=function(e,t){var o=t.min;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every((function(e){return E(e,{min:o})})):Number(e)>=o)},M={validate:E,params:[{name:"min",cast:function(e){return Number(e)}}]},N=/^[٠١٢٣٤٥٦٧٨٩]+$/,B=/^[0-9]+$/,F={validate:function(e){function t(e){var t=String(e);return B.test(t)||N.test(t)}return Array.isArray(e)?e.every(t):t(e)}},V=function(e,t){var o=t.regex;return Array.isArray(e)?e.every((function(e){return V(e,{regex:o})})):o.test(String(e))},Y={validate:V,params:[{name:"regex",cast:function(e){return"string"==typeof e?new RegExp(e):e}}]},q={validate:function(e,t){var o=(void 0===t?{allowFalse:!0}:t).allowFalse,n={valid:!1,required:!0};return w(e)||k(e)||!1===e&&!o||(n.valid=!!String(e).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},z={validate:function(e,t){var o,n=t.target,s=t.values;return(o=s&&s.length?(Array.isArray(s)||"string"!=typeof s||(s=[s]),s.some((function(e){return e==String(n).trim()}))):!j(n))?{valid:!j(e),required:o}:{valid:!0,required:o}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},Z={validate:function(e,t){var o=t.size;if(isNaN(o))return!1;var n=1024*o;if(!Array.isArray(e))return e.size<=n;for(var s=0;s<e.length;s++)if(e[s].size>n)return!1;return!0},params:[{name:"size",cast:function(e){return Number(e)}}]};e.alpha=a,e.alpha_dash=u,e.alpha_num=c,e.alpha_spaces=p,e.between=g,e.confirmed=h,e.digits=v,e.dimensions=y,e.email=b,e.excluded=_,e.ext=$,e.image=D,e.integer=L,e.is={validate:function(e,t){return e===t.other},params:[{name:"other"}]},e.is_not={validate:function(e,t){return e!==t.other},params:[{name:"other"}]},e.length=P,e.max=H,e.max_value=O,e.mimes=R,e.min=x,e.min_value=M,e.numeric=F,e.oneOf=S,e.regex=Y,e.required=q,e.required_if=z,e.size=Z,Object.defineProperty(e,"__esModule",{value:!0})})),Vue.component("yuno-org-theme",{props:["data","options","resourceloaded"],template:"\n        <span></span>\n    ",data:()=>({}),watch:{resourceloaded(e){e&&this.resourceFetched()}},computed:{...Vuex.mapState(["orgAdmin"]),isNotYunoLearning:()=>!["yunolearning.com"].includes(window.location.hostname)},async created(){this.fetchOrgInfo(this.$props.options.orgID)},mounted(){},methods:{loadGoogleFont(e){var t=document.createElement("link");t.rel="stylesheet",t.href="https://fonts.googleapis.com/css2?family="+encodeURIComponent(e)+"&display=swap",document.head.appendChild(t)},resourceFetched(){const e=this.orgAdmin.data.theme;this.loadGoogleFont(e.font_family),document.documentElement.style.setProperty("--primary-color",e.primary_color),document.documentElement.style.setProperty("--body-bg-color",e.background_color),document.documentElement.style.setProperty("--font-family",e.font_family)},gotOrgInfo(e){const{code:t,data:o}=e.response?.data||{};200===t&&(void 0===this.$props.resourceloaded&&this.resourceFetched(),this.$emit("orgFetched"))},fetchOrgInfo(e){const t={apiURL:YUNOCommon.config.org("info",e),module:"gotData",store:"orgAdmin",callback:!0,callbackFunc:e=>this.gotOrgInfo(e)};console.log(this.isNotYunoLearning),this.isNotYunoLearning&&this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-footer",{props:["isOrg"],template:'\n        <footer class="leadFormFooter">\n            <div class="container">\n                <div class="columns is-mobile is-centered">\n                    <div class="poweredBy">\n                        <p>Powered By</p>\n                        <img\n                            :src="logo"\n                            alt="Yuno Learning"\n                            width="51"\n                            height="24"\n                        >\n                    </div>\n                </div>\n            </div>\n        </footer>\n    ',data(){return{logo:this.$store.state.themeURL+"/assets/images/yuno-logo-grey.svg"}},computed:{...Vuex.mapState(["orgAdmin"])},async created(){},mounted(){},methods:{}}),Vue.component("yuno-header",{props:["data","options","isOrg"],template:'\n    <b-navbar \n        class="brandHeader" \n        :mobile-burger="false"\n    >\n        <template #brand>\n            <b-navbar-item>\n                <template v-if="isOrg">\n                    <template v-if="orgAdmin.loading">\n                        <b-skeleton width="102px" height="48px"></b-skeleton>\n                    </template>\n                    <template v-if="orgAdmin.success && orgAdmin.error === null">\n                        <img\n                            :src="orgAdmin.data.logo_image_url"\n                            :alt="orgAdmin.data.organisation_name"\n                        >    \n                    </template>\n                </template>\n                <template v-else>\n                    <img\n                        src="https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg"\n                        alt="Yuno Learning"\n                    >\n                </template>\n            </b-navbar-item>\n        </template>\n    </b-navbar>\n    ',data:()=>({}),computed:{...Vuex.mapState(["orgAdmin"])},async created(){},mounted(){},methods:{}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 10 numbers required",is:"This field is required",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-lead-form",{props:{type:{type:String,required:!0}},template:'\n    <section class="leadForm">\n        <yuno-page-loader v-if="isUserLoading"></yuno-page-loader>\n        <yuno-org-theme v-if="isOrg" :options="{\n                \'orgID\': orgID\n            }" \n            @orgFetched="onOrgFetched"\n        >\n        </yuno-org-theme>\n        <template v-if="isUserReady">\n            <yuno-header :isOrg="isOrg"></yuno-header>\n            <template v-if="stepsBegin">\n                <yuno-schedule-demo\n                    v-if="bookADemo"\n                    :category="type"\n                    @showThankyou="showThankyou"\n                >\n                </yuno-schedule-demo>\n                <yuno-thank-you \n                    v-if="isThankyou" \n                    :options="{\'category\': type}"\n                    :data="findObjectByKey(categories, \'slug\', type)" \n                >\n                </yuno-thank-you>\n                \n                <yuno-steps \n                    :options="{\'category\': type}" \n                    :data="findObjectByKey(categories, \'slug\', type)" \n                    @manageThankyou="manageThankyou" \n                    @initSteps="initSteps" \n                    v-if="!isThankyou && !bookADemo"\n                >\n                </yuno-steps>\n            </template>\n            <template v-else>\n                <yuno-login-step \n                    :isOrg="isOrg"\n                    :options="{\'category\': type}" \n                    :data="findObjectByKey(categories, \'slug\', type)"\n                >\n                </yuno-login-step>\n            </template>\n            <yuno-footer v-if="isOrg" :isOrg="isOrg"></yuno-footer>\n        </template>\n    </section>\n    \n    ',data:()=>({orgID:"",stepsBegin:!1,isThankyou:!1,bookADemo:!1,isOrg:!1,categories:[{slug:"ielts",label:"IELTS",title:"IELTS Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right IELTS online classes"},{slug:"english-speaking",label:"English Speaking",title:"English Speaking Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right English Speaking online classes"},{slug:"pte",label:"PTE",title:"PTE Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right PTE online classes"},{slug:"duolingo",label:"Duolingo",title:"Duolingo Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right Duolingo online classes"},{slug:"toefl",label:"TOEFL",title:"TOEFL Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right TOEFL online classes"},{slug:"french",label:"French",title:"French Live Online Classes",subTitle:"BOOK A DEMO CLASS",questionTitle:"Let us help you find the right French online classes"}]}),computed:{...Vuex.mapState(["user","userInfo","form","categoryList","orgAdmin"]),isUserLoading:{get(){return this.userInfo.loading}},isUserReady:{get(){let e="";return e=!this.user.isLoggedin||this.userInfo.success,e}}},async created(){this.manageState()},mounted(){this.emitEvents()},methods:{onOrgFetched(){},findObjectByKey:(e,t,o)=>e.find((e=>e[t]===o)),initSteps(){this.fetchSteps(!0)},showThankyou(){this.isThankyou=!0,this.bookADemo=!1},manageThankyou(){YUNOCommon.getQueryParameter("academy_id")?(this.bookADemo=!0,this.isThankyou=!1):(this.isThankyou=!0,this.bookADemo=!1)},emitEvents(){Event.$on("stepsBegins",(()=>{this.stepsBegin=!0}))},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:e=>this.gotUserInfo(e)};this.$store.dispatch("fetchData",e)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo()):this.user.isLoggedin=!1;const e=YUNOCommon.getQueryParameter("stepsBegin");this.user.isLoggedin&&e&&(this.stepsBegin=!0)},gotOrgInfo(e){const{code:t,data:o}=e.response?.data||{}},fetchOrgInfo(e){const t={apiURL:YUNOCommon.config.org("info",e),module:"gotData",store:"orgAdmin",callback:!0,callbackFunc:e=>this.gotOrgInfo(e)};this.$store.dispatch("fetchData",t)},manageUserQueryParams(){const e=new URLSearchParams(window.location.search),t=e.get("user_id"),o=e.get("yuno_token");t&&o&&(isLoggedIn=t,this.$store.state.config.yunoAPIToken="Bearer "+o,this.user.userID=t,this.user.isLoggedin=!0)},manageState(){const e=YUNOCommon.getQueryParameter("org_id");e&&(this.isOrg=!0,this.orgID=e),this.manageUserQueryParams(),this.loginStatus(),this.fetchSteps(!1),this.fetchCategories()},gotSteps(e){const{code:t,data:o}=e.response?.data||{};if(200===t){const e=o.form_fields,t=YUNOCommon.findObjectByKey(e,"name","which_ielts_exam_do_you_want_to_take");"ielts"===this.$props.type&&"academic"===t.selected&&YUNOCommon.removeObjInArr(e,"name","What_do_you_wish_to_pursue"),this.form.data=o}},fetchSteps(e){e&&(this.form.data=[],this.form.success=!1);const t={apiURL:YUNOCommon.config.leadForm("steps",isLoggedIn,this.$props.type),module:"gotData",store:"form",callback:!0,addToModule:!1,callbackFunc:e=>this.gotSteps(e)};this.$store.dispatch("fetchData",t)},fetchCategories(){const e={apiURL:YUNOCommon.config.categoryListAPI(),module:"gotData",store:"categoryList",callback:!1};this.$store.dispatch("fetchData",e)}}}),Vue.component("yuno-login-step",{props:["data","options","isOrg"],template:'\n    \n        <section class="loginStep">\n            <div class="container">\n                <div class="columns is-mobile is-centered">\n                    <div class="column is-three-fifths-desktop is-full-mobile">\n                        <div class="card">\n                            <template v-if="isOrg && orgAdmin.loading">\n                                <b-skeleton height="355px"></b-skeleton>\n                            </template>\n                            <template v-else>\n                                <validation-observer \n                                    tag="div" \n                                    class="observer loginObserver"\n                                    ref="commonSignupObserver" \n                                    v-slot="{ handleSubmit, invalid }">\n                                    <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                        <div class="card-content">\n                                            <div class="content">\n                                                <h1 class="largestTitle">{{ data.title }}</h1>\n                                                <h2 class="largeTitle">{{ data.subTitle }}</h2>\n                                                <div class="columns loginFieldWrapper">\n                                                    <div class="column is-full-mobile alignC">\n                                                        <div class="ratingWrapper">\n                                                            <figure>\n                                                                <img :src="googleIcnURL" alt="google"></img>\n                                                            </figure>\n                                                            <div class="ratingInfo">\n                                                                <div class="stars">\n                                                                    <span v-for="(star, i) in 5" class="material-icons">star_rate</span>\n                                                                </div>\n                                                                <h3 class="smallCaption dark60">\n                                                                    {{ ratingCaption }}\n                                                                    <span class="body1">{{ ratingSubCaption }}</span>\n                                                                </h3>\n                                                            </div>\n                                                        </div>\n                                                        <div class="instrutorList" v-if="false">\n                                                            <div class="list">\n                                                                <span v-for="(item, i) in 7" class="material-icons">\n                                                                    account_circle\n                                                                </span>\n                                                            </div>\n                                                            <div class="numbers">{{ instructorCaption }}</div>\n                                                        </div>\n                                                    </div>\n                                                    <div class="column is-three-fifths-desktop is-full-mobile loginWrapper">\n                                                        <b-field label="Phone Number">\n                                                            <validation-provider \n                                                                :customMessages="{ required: \'Phone number is required\'}" \n                                                                tag="div" \n                                                                :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" \n                                                                v-slot="{ errors, classes }"\n                                                                class="fieldWrapper"\n                                                            >\n                                                                <b-input \n                                                                    placeholder="Enter your phone number" \n                                                                    :class="classes" \n                                                                    inputmode="numeric"\n                                                                    pattern="[0-9]*"\n                                                                    type="text"\n                                                                    v-model="signIn.mobile"\n                                                                >\n                                                                </b-input>\n                                                                <b-button \n                                                                    v-if="user.isLoggedin" \n                                                                    class="updateNumber"\n                                                                >\n                                                                    <span class="material-icons">edit</span>\n                                                                </b-button>\n                                                                <p class="error">{{errors[0]}}</p>\n                                                            </validation-provider>    \n                                                        </b-field>\n                                                        <footer class="card-footer googleWrapper">\n                                                            <b-button v-if="!user.isLoggedin" class="card-footer-item hasGoogle" native-type="submit">\n                                                                <img :src="googleIcnURL" alt="google"></img> Sign up with Google\n                                                            </b-button>\n                                                            <b-button v-else class="card-footer-item primary" native-type="submit" :loading="form.loading" :disabled="form.loading">\n                                                                Next\n                                                            </b-button>\n                                                        </footer>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </form>\n                                </validation-observer>\n                            </template>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    \n    ',data(){return{transitionName:"slide-left",ratingCaption:"Rated 5-star on Google",ratingSubCaption:"10,000 successful students",instructorCaption:"Choose from 216 instructors",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""},org_details:{type:"login",org_id:"",org_url:"",phone:""},login_details:{role:""}}}},computed:{...Vuex.mapState(["user","userInfo","subform2","form","categoryList","orgAdmin"])},async created(){},mounted(){this.preFillForm()},methods:{nextStep(){if(0!==this.form.data.form_fields.length)Event.$emit("stepsBegins");else{const e="/search/?state=%7B%22category%22:%5B"+YUNOCommon.findObjectByKey(this.categoryList.data,"categorySlug",this.$props.options.category).category_id+"%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22items%22:%5B%5D,%22slug%22:%22class_days%22%7D,%7B%22items%22:%5B%5D,%22slug%22:%22class_time%22%7D%5D%7D";setTimeout((()=>{window.location.href=e}),50)}},preFillForm(){if(this.user.isLoggedin&&(this.signIn.mobile=this.userInfo.data.mobile),this.$props.isOrg){const e=YUNOCommon.getQueryParameter("org_id")+"@@@"+this.formatDateTime(new Date);this.signIn.org_details.org_id=this.utf8ToBase64(e)}},mobileUpdated(e,t){this.subform2.isLoading=!1;const o=e?.response?.data;201===o?.code?(this.showNotification(o.message,"is-bottom","is-black"),t&&console.log(o.message)):o?.message&&t&&console.log(o.message)},showNotification(e,t,o=""){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:t,type:o})},updateMobile(e){this.subform2.isLoading=!0;const t={phone:this.signIn.mobile,cta:"book_a_demo",course:!1!==YUNOCommon.getQueryParameter("course_id")?YUNOCommon.getQueryParameter("course_id"):0},o={apiURL:YUNOCommon.config.leadForm("postStep",isLoggedIn,this.$props.options.category),module:"gotData",store:"subform2",payload:t,callback:!0,callbackFunc:t=>this.mobileUpdated(t,e)};this.$store.dispatch("postData",o)},initCommonSignup(){this.user.isLoggedin?(this.updateMobile(),setTimeout((()=>{this.nextStep()}),50)):this.setState()},addQueryParamUsingURLSearchParams(e,t,o){let n=new URL(e);return n.searchParams.set(t,o),n.toString()},setSigninProps(){let e=window.location.origin+window.location.pathname+window.location.search,t=this.addQueryParamUsingURLSearchParams(e,"stepsBegin",!0);localStorage.setItem("userState",t),localStorage.setItem("isQuiz",!0),localStorage.setItem("skipSignUp",!0)},setPayload(){let e=this.signIn;e.categoryURL=this.$props.options.category,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="book_a_demo",e.content.id=""},isFirefoxPrivate(e){null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},formatDateTime:e=>`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}`,utf8ToBase64(e){const t=encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,(function(e,t){return String.fromCharCode("0x"+t)}));return btoa(t)}}}),Vue.component("yuno-schedule-demo",{props:{category:{type:String,required:!0}},template:'\n        <section class="loginStep demoSchedule">\n            <div class="container">\n                <div class="columns is-centered">\n                    <a href="#" @click="preStep" class="pre" :class="[isDatePicked ? \'isShow\' : \'\']"><span class="material-icons">arrow_back_ios</span></a>\n                    <div class="column is-full-mobile" :class="[ isDatePicked ? \'is-10\' : \'is-7\' ]">\n                        <div class="columns is-multiline is-centered hasBorder">\n                            <div class="column step1 is-full-mobile hasRightBorder noBtmPaddingMobile" :class="[isDatePicked ? \'isHideInMobile\' : \'\']">\n                                <div class="block">\n                                    <h2 class="smallCaption dark87">{{ showCategory(category) }}</h2>\n                                </div>\n                                <div class="block">\n                                    <h2 class="smallCaption dark87">Time zone</h2>\n                                    <p class="body1"> {{ getFormattedTimeZone() }}</p>\n                                </div>\n                            </div>\n                            <div class="column step1 is-full-mobile datepickerWrapper" :class="[isDatePicked ? \'isHideInMobile\' : \'\']">\n                                <template v-if="subform.loading">\n                                    <b-skeleton width="100%" height="342px"></b-skeleton>\n                                </template>\n                                <template v-else-if="subform.success && subform.error === null">\n                                    <h2 class="smallCaption dark87">Select a Date & Time</h2>\n                                    <b-datepicker \n                                        v-model="scheduleDate" \n                                        inline \n                                        :min-date="startOfToday()"\n                                        class="yunoDatePicker"\n                                        :date-formatter="formatDate"\n                                        @input="onDatePick"\n                                    >\n                                    </b-datepicker>\n                                </template>\n                                <template v-else-if="subform.success">\n                                    {{ subform.errorData }}\n                                </template>\n                            </div>\n                            <div class="column step2 is-3 is-full-mobile hasLeftBorder" v-if="isDatePicked">\n                                <template v-if="subform.loading">\n                                    <div class="slotsWrapper">\n                                        <div class="slot" v-for="(slot, i) in 1" :key="i">\n                                            <h2 class="largeTitle dark87"><b-skeleton active height="24px" width="100%"></b-skeleton></h2>\n                                            <div class="time">\n                                                <b-skeleton \n                                                    v-for="(time, j) in 3" \n                                                    :key="j"\n                                                    active height="40px" width="100%"\n                                                >\n                                                </b-skeleton>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </template>\n                                <template v-else-if="subform.success && subform.error === null && subform.data.length !== 0">\n                                    <div class="slotsWrapper" v-if="isDateTimePicked">\n                                        <div class="selectedSlot">\n                                            <h2 class="smallCaption dark87">{{ convertDateString(payload.class_date_time) }}</h2>\n                                            <p class="smallCaption">{{ startEndTime }} ({{ timezone }})</p>\n                                            <p class="overline capital">Duration: {{ payload.class_duration + " minutes" }}</p>\n                                            <b-button \n                                                @click="onFormSubmit()" \n                                                :loading="subform3.isLoading"\n                                                :disabled="subform3.isLoading"\n                                                class="primaryCTA"\n                                                expanded\n                                            >\n                                                Confirm\n                                            </b-button>\n                                            <b-button \n                                                @click="onCancel()" \n                                                expanded\n                                            >\n                                                Cancel\n                                            </b-button>\n                                        </div>\n                                    </div>\n                                    <div class="slotsWrapper" v-else>\n                                        <div class="slot" v-for="(slot, i) in subform.data.available_slots.time_slots" :key="i">\n                                            <h2 class="smallCaption dark87">{{ convertDateString(slot.date + \' \' + slot.day_of_week) }}</h2>\n                                            <div class="time">\n                                                <b-button \n                                                    v-for="(time, j) in slot.slots" \n                                                    v-if="time.status" \n                                                    @click="onTimeSelect(time, slot.date)" \n                                                    :key="j">\n                                                    {{ convertTo12HourFormat(time.starttime) }} - {{ convertTo12HourFormat(time.endtime) }}\n                                                </b-button>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </template>\n                                <template v-else-if="subform.success">\n                                    {{ subform.errorData }}\n                                </template>\n                            </div>\n                        </div> \n                    </div>\n                </div>\n            </div>\n        </section>                            \n        \n    ',data:()=>({scheduleDate:new Date,isDateTimePicked:!1,isDatePicked:!1,startEndTime:"",timezone:"",payload:{class_title:"",class_date_time:"",class_duration:"",instructor_id:[],category_id:"",academy_id:"",learners:[]},slotsPayload:{resource_id:[],start_date:"",end_date:"",start_time:"",end_time:"23:59"}}),computed:{...Vuex.mapState(["subform","categoryList","form","subform3"])},async created(){},mounted(){},methods:{showCategory(e){return this.findObjectByKey(this.categoryList.data,"categorySlug",e).category},preStep(){this.isDatePicked=!1,this.onCancel()},convertToISOString(e){const[t]=e.split(" ");return new Date(t).toISOString()},getFormattedTimeZone(){const e=new Date,t=Intl.DateTimeFormat().resolvedOptions().timeZone;return`${new Intl.DateTimeFormat("en-US",{timeZone:t,timeZoneName:"short"}).formatToParts(e).find((e=>"timeZoneName"===e.type)).value} (${new Intl.DateTimeFormat("en-US",{timeZone:t,timeZoneName:"long"}).formatToParts(e).find((e=>"timeZoneName"===e.type)).value})`},onCancel(){this.isDateTimePicked=!1},readableDate(e){},convertDateString(e){let t=e;/^\d{4}-\d{2}-\d{2} \w+/.test(e)&&(t=this.convertToISOString(e));const o=new Date(t);if(isNaN(o.getTime()))return e;const n=o.toLocaleString("en-US",{weekday:"long"}),s=o.toLocaleString("en-US",{month:"long"}),r=o.getDate();let a="th";return r%10==1&&11!==r?a="st":r%10==2&&12!==r?a="nd":r%10==3&&13!==r&&(a="rd"),`${n}, ${s} ${r}${a} ${o.getFullYear()}`},updateDateTime(e,t){let o,n;const s=t.split(" ");if(2===s.length){const[e,t]=s;[o,n]=e.split(":").map(Number),"PM"===t.toUpperCase()&&o<12?o+=12:"AM"===t.toUpperCase()&&12===o&&(o=0)}else[o,n]=t.split(":").map(Number);const r=new Date(e);return r.setHours(o,n,0,0),r.toString()},calculateDuration(e,t){const o=e=>{let t,o;const n=e.split(" ");if(2===n.length){const[e,s]=n;[t,o]=e.split(":").map(Number),"PM"===s.toUpperCase()&&t<12?t+=12:"AM"===s.toUpperCase()&&12===t&&(t=0)}else[t,o]=e.split(":").map(Number);return 60*t+o},n=o(e);let s=o(t)-n;return s<0&&(s+=1440),s},findObjectByKey:(e,t,o)=>e.find((e=>e[t]===o)),onTimeSelect(e,t){this.startEndTime=`${this.convertTo12HourFormat(e.starttime)} - ${this.convertTo12HourFormat(e.endtime)}`,this.payload.class_date_time=this.updateDateTime(t,e.starttime),this.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone,e.starttime&&e.endtime&&(this.payload.class_duration=this.calculateDuration(e.starttime,e.endtime)),this.subform.data&&this.subform.data.available_slots&&Array.isArray(this.subform.data.available_slots.instructors)&&(this.payload.instructor_id=this.subform.data.available_slots.instructors.map((e=>e.id)));const o=this.findObjectByKey(this.categoryList.data,"categorySlug",this.category);o&&(this.payload.category_id=o.category_id),this.payload.academy_id=YUNOCommon.getQueryParameter("academy_id")||0,this.payload.learners.push(isLoggedIn),this.isDateTimePicked=!0},formPosted(e,t){this.subform3.loading=!1,this.subform3.isLoading=!1;const o=e?.response?.data;t&&console.log(o),201===o?.code?(o.data&&o.data.data&&(this.subform3.data=o.data.data),this.$emit("showThankyou"),t&&console.log(o.message)):400===o?.code?this.$buefy.toast.open({duration:3e3,message:o.message,position:"is-bottom",type:"is-danger"}):o?.message&&(t&&console.log(o.message),this.$buefy.toast.open({duration:3e3,message:o.message,position:"is-bottom",type:"is-danger"}))},onFormSubmit(e){this.subform3.loading=!0,this.subform3.isLoading=!0;const t={apiURL:YUNOCommon.config.user("classSchedule",!1),module:"gotData",store:"subform3",payload:this.payload,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:t=>this.formPosted(t,e)};this.$store.dispatch("postData",t)},formatDateForPayload(e){const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},setCurrentTime(){const e=new Date;return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},onDatePick(e){this.isDatePicked=!0;const t=JSON.parse(JSON.stringify(this.subform));t.data&&t.data.available_slots&&Array.isArray(t.data.available_slots.instructors)&&(this.slotsPayload.resource_id=t.data.available_slots.instructors.map((e=>e.id))),this.slotsPayload.start_date=this.formatDateForPayload(e),this.slotsPayload.end_date=this.formatDateForPayload(e),this.slotsPayload.start_time=this.setCurrentTime(),this.fetchSlots(!0)},gotSlots(e,t){this.subform.loading=!1,this.subform.isLoading=!1;const o=e?.response?.data;201===o?.code?(o.data&&o.data.data&&(this.subform.data=o.data.data),t&&console.log(o.message)):o?.message&&t&&console.log(o.message)},fetchSlots(e){this.subform.loading=!0,this.subform.isLoading=!0,this.subform.success=!1;const t={apiURL:YUNOCommon.config.user("slots",!1),module:"gotData",store:"subform",payload:this.slotsPayload,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:t=>this.gotSlots(t,e)};this.$store.dispatch("postData",t)},formatDate(e){const t=e.getDate(),o=this.getDaySuffix(t),n=e.toLocaleDateString("en-GB",{weekday:"long",month:"long"}),[s,r]=n.split(" ");return`${s} ${t}${o} ${r}`},getDaySuffix(e){if(e>3&&e<21)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},startOfToday(){const e=new Date;return e.setHours(0,0,0,0),e},convertTo12HourFormat(e){const[t,o]=e.split(":"),n=parseInt(t,10);return`${n%12||12}:${o} ${n>=12?"PM":"AM"}`}}}),Vue.component("yuno-step",{props:["data","options"],template:'\n    <div class="card">\n        <validation-observer \n            tag="div" \n            class="observer questionsWrapper"\n            ref="commonSignupObserver" \n            v-slot="{ handleSubmit, invalid }">\n            <form id="commonSignupForm" @submit.prevent="handleSubmit(initSteps)">\n                <div class="card-content">\n                    <a href="#" v-if="options.currentStep > 0" @click="preStep" class="pre"><span class="material-icons">arrow_back_ios</span></a>\n                    <template v-if="data.type === \'agreeTerms\'">\n                        <validation-provider \n                            :rules="{required:true, is:payload.selected === false}"\n                            class="agreeTerms"\n                            tag="div"\n                            v-slot="{ errors, classes }">\n                            <b-field>\n                                <b-checkbox \n                                    v-model="payload.selected"\n                                    :true-value="true"\n                                    :false-value="false"\n                                    :class="classes"\n                                >\n                                    {{ data.label }}\n                                </b-checkbox>\n                            </b-field>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </template>\n                    <template v-else>\n                        <validation-provider \n                            :customMessages="data.message"\n                            :rules="data.validation" \n                            v-slot="{ errors, classes }">\n                            <b-field class="optionSelector" :class="[data.name]">\n                                <template #label>\n                                    {{ data.label }}\n                                    <small>{{ data.helper_label }}</small>    \n                                </template>\n                                <b-radio-button \n                                    v-for="(option, j) in data.items"\n                                    :key="j"\n                                    v-model="payload.selected"\n                                    :native-value="option.slug"\n                                    @input="onOptionSelect"\n                                    :class="classes"\n                                    type="is-success is-light is-outlined"\n                                >\n                                    <span>{{ option.label }}</span>\n                                </b-radio-button>\n                            </b-field>\n                        </validation-provider>\n                    </template>\n                </div>\n                <footer class="card-footer" v-if="manageCTAVisibility(data)">\n                    <b-button class="card-footer-item primary" @click="nexStep">\n                        Next\n                    </b-button>\n                </footer>\n            </form>\n        </validation-observer>\n    </div>\n    ',data:()=>({payload:{name:"",selected:"",course:!1!==YUNOCommon.getQueryParameter("course_id")?YUNOCommon.getQueryParameter("course_id"):0,cta:"book_a_demo",academy_id:!1!==YUNOCommon.getQueryParameter("academy_id")?YUNOCommon.getQueryParameter("academy_id"):0}}),computed:{...Vuex.mapState(["subform","categoryList","form"])},async created(){},mounted(){this.preFillStep()},methods:{nexStep(){this.$props.options.currentStep+1===this.form.data.form_fields.length?(this.$emit("manageThankyou"),this.initSteps()):Event.$emit("nextStep")},manageCTAVisibility(e){let t="";return t=""!==e.selected,t},preFillStep(){const e=this.$props.data.selected;this.$props.data.step_completed&&(this.payload.selected=e)},preStep(){Event.$emit("preStep")},onOptionSelect(e){this.resetSubform(),this.initSteps()},resetSubform(){this.subform.error=null,this.subform.errorData=[],this.subform.success=!1},initSteps(){""!==this.payload.selected&&(this.payload.name=this.$props.data.name,this.onFormSubmit(),"which_ielts_exam_do_you_want_to_take"===this.payload.name&&"academic"===this.payload.selected&&YUNOCommon.removeObjInArr(this.form.data.form_fields,"name","What_do_you_wish_to_pursue")),Event.$emit("nextStep")},formPosted(e,t){this.subform.loading=!1,this.subform.isLoading=!1;const o=e?.response?.data;201===o?.code?(o.data&&o.data.data&&(this.subform.data=o.data.data),t&&console.log(o.message)):o?.message&&t&&console.log(o.message)},onFormSubmit(e){this.subform.loading=!0,this.subform.isLoading=!0;this.$props.options.currentStep+1===this.form.data.form_fields.length&&this.$emit("manageThankyou");const t={apiURL:YUNOCommon.config.leadForm("postStep",isLoggedIn,this.$props.options.category),module:"gotData",store:"subform",payload:this.payload,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:t=>this.formPosted(t,e)};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-steps",{props:["data","options"],template:'\n    <div>\n        <template v-if="form.loading">\n            <transition :name="transitionName" mode="out-in">\n                <section class="loginStep">\n                    <div class="container">\n                        <div class="columns is-mobile is-centered">\n                            <div class="column is-half-desktop is-full-mobile">\n                                <div class="card">\n                                    <div class="questionsWrapper">\n                                        <div class="card-content">\n                                            <div class="content">\n                                                <b-skeleton size="is-large" :active="true" :count="2"></b-skeleton>\n                                            </div>\n                                        </div>\n                                        <footer class="card-footer">\n                                            <a class="card-footer-item">\n                                                <b-skeleton size="is-large" :active="true"></b-skeleton>\n                                            </a>\n                                        </footer>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n            </transition>\n        </template>\n        <template v-if="form.success">\n            <template v-if="form.error">\n                error\n            </template>\n            <template v-else>\n                <div class="loginStep noBtmGap hideInMobile">\n                    <h2 class="largerSubheading">{{ data.questionTitle }}</h2>\n                </div>\n                <transition :name="transitionName" mode="out-in">\n                    <div class="step-container" v-if="currentStep === index" v-for="(question, index) in form.data.form_fields" :key="question.id">\n                        <section class="loginStep noTopGap">\n                            <div class="container">\n                                <div class="columns is-mobile is-centered">\n                                    <div class="column is-half-desktop is-full-mobile">\n                                        <yuno-step :data="question" :options="{\'category\': options.category, \'currentStep\': currentStep}" @manageThankyou="manageThankyou" @initSteps="initSteps"></yuno-step>\n                                    </div>\n                                </div>\n                            </div>\n                        </section>\n                    </div>\n                </transition>\n            </template>\n        </template>\n    </div>\n    ',data:()=>({currentStep:0,transitionName:"slide-left"}),computed:{...Vuex.mapState(["form"])},async created(){this.emitEvents()},mounted(){},methods:{initSteps(){this.$emit("initSteps")},manageThankyou(){this.$emit("manageThankyou")},nextStep(){this.transitionName="slide-left",this.currentStep<this.form.data.form_fields.length-1&&this.currentStep++},preStep(){this.transitionName="slide-right",this.currentStep>0&&this.currentStep--},emitEvents(){Event.$on("nextStep",(()=>{this.nextStep()})),Event.$on("preStep",(()=>{this.preStep(),this.initSteps()}))}}}),Vue.component("yuno-thank-you",{props:["data","options"],template:'\n    <section class="loginStep topGap">\n        <div class="container">\n            <div class="columns is-mobile is-centered">\n                <div class="column is-three-fifths-desktop is-full-mobile">\n                    <div class="card thankyou">\n                        <span class="material-icons-outlined">thumb_up</span>\n                        <h3 class="largerTitle">{{ title }}</h3>\n                        <h4 class="smallerCaption dark87">{{ "Dear " + userInfo.data.yuno_display_name }}</h4>\n                        <p class="body1">{{ description }}</p>\n                        <b-button class="primary" tag="a" :href="ctaURL" @click="manageRedirection($event)">\n                            {{ manageCTALabel(\'See all \' + data.label + \' Classes\') }}\n                        </b-button>\n                    </div>\n                </div>\n            </div>    \n        </div>\n    </section>\n    ',data:()=>({title:"Thank You for Your Responses!",description:"Thank you for taking the time to submit your answers! Your thoughtful responses are invaluable to us, and we appreciate the effort you put into providing us with this information.",cta:"See all IELTS Classes",ctaURL:""}),computed:{...Vuex.mapState(["subform","categoryList","form","userInfo","categoryList"])},async created(){},mounted(){this.manageCTA()},methods:{manageCTALabel(e){const t=new URLSearchParams(window.location.search).get("cta");return t||e},manageRedirection(e){const t=new URLSearchParams(window.location.search),o=t.get("user_id"),n=t.get("yuno_token");if(o&&n){e.preventDefault();const o=t.get("redirect_url");window.location.href=o}},manageCTA(){const e=YUNOCommon.findObjectByKey(this.categoryList.data,"categorySlug",this.$props.options.category);this.ctaURL="/search/?state=%7B%22limit%22:20,%22offset%22:0,%22personalization%22:%22all%22,%22category%22:%5B"+e.category_id+"%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22selected%22:%5B%5D,%22slug%22:%22class_days%22%7D,%7B%22selected%22:%5B%5D,%22slug%22:%22class_time%22%7D%5D,%22instructor_id%22:0,%22price_per_hour%22:10000,%22total_duration%22:24%7D"}}});