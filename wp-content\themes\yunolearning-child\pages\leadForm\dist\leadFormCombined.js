const YUNOCommon = (function ($) {

    const config = {
        errorMsg: {
            common: "An error seems to have occurred. Please try again. ",
            notMapped: "The selected course doesn't mapped with any instructor. Please select other course.",
            enrollmentError: "It seems something went wrong with our servers. Our team has been notified. Please try again later.",
            sesstionExpired: "Your session has been expired. Please login again to resume your session. "
        },
        awsHost: function () {
            let isHost = window.location.hostname

            if (isHost === "localhost") {
                return "https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev"
            };

            if (isHost === "www.yunolearning.com") {
                return "https://api.yunolearning.com"
            };

            if (isHost === "stage.yunolearning.com" || isHost === "dev.yunolearning.com") {
                return "https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev"
            };

            if (isHost === "webcache.googleusercontent.com") {
                return "https://api.yunolearning.com"
            };
        },
        addVerion: function (hasQuery) {
            let version = 1,
                versionStr = "";

            if (hasQuery) {
                versionStr = "?buildVersion=" + version + ""
            } else {
                versionStr = "&buildVersion=" + version + ""
            }

            return versionStr;
        },
        pickHost: function () {
            if (this.host() === "http://localhost") {
                return "https://dev.yunolearning.com"
            } else if (this.host() === "http://*************") {
                return "https://dev.yunolearning.com"
            }  else if (this.host() === "https://www.yunolearning.com") {
                return "https://www.yunolearning.com"
            } else if (this.host() === "https://webcache.googleusercontent.com") {
                return "https://www.yunolearning.com"
            }
             else {
                return this.host();
            }
        },
        host: function () {
            const getHost = window.location.protocol + '//' + window.location.hostname;
            return getHost;
        },
        footerAPI: function () {
            // const createURL = this.pickHost() + "/wp-json/yuno/v1/misc/footer/info";
            const createURL = this.pickHost() + "/wp-json/yuno/v1/footer";

            return createURL;
        },    
        latesBlogAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/latest/blogs";

            return createURL;
        },
        courseIELTSAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/ielts/short-detail";

            return createURL;
        },
        allCoursesIELTSAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/ielts/detail";

            return createURL;
        },
        headerMenuAPI: function (userRole, categories) {
            let hasCategories = "";

            if (categories !== undefined) {
                hasCategories = "?category=" + categories + "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/menu/" + userRole + "" + hasCategories + "";

            return createURL;
        },
        headerMenuAPIV2: function (userID, userRole) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/menu/"+ userID +"/" + userRole + "";
            return createURL;
        },
        userRoleAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/" + userID + "/role";

            return createURL;
        },
        userProfileAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/" + userID + "/profile" + hasCache + "";

            return createURL;
        },
        studentResultsAPI: function (type, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/results/" + type + "/" + limit + "/" + offset + "";

            return createURL;
        },
        faqAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/faq/" + type + "";

            return createURL;
        },
        courseAPI: function (courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/getdetail/" + courseID + "/" + userID + "";

            return createURL;
        },
        scheduleAPI: function (courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/" + courseID + "/schedule";

            return createURL;
        },
        instructorAPI: function (ownerID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/" + ownerID + "/getUserDetail";

            return createURL;
        },
        updateInstructorDetailAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateUserDetail";

            return createURL;
        },
        instructorCoursesAPI: function (instructorId, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/instructor/" + instructorId + "/" + userID + "";

            return createURL;
        },
        instructorBatchAPI: function (instructorId, courseID, userID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + instructorId + "/instructor/" + courseID + "/" + userID + "/" + state + "/" + limit + "/" + offset + "";

            return createURL;
        },
        instructorNonBatchesAPI: function (instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructorSingleProductDetail?instructorID=" + instructorId + "";

            return createURL;
        },
        categoriesAPi: function (query) {
            if (query === undefined) {
                query = "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/category"+ query +"";

            return createURL;
        },
        featuredCoursesAPi: function (instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/featuredCourses";

            return createURL;
        },
        featuredInstructorAPi: function (channel) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/featuredInstructor/" + channel + "";

            return createURL;
        },
        batchAPi: function (isAll, id, userID, state) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/" + isAll + "/" + id + "/" + userID + "/" + state + "";

            return createURL;
        },
        classAPi: function (type, instructorId, loggedinUser, criteria, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/classes/" + type + "/" + instructorId + "/" + loggedinUser + "/" + criteria + "/" + limit + "/" + offset + "";

            return createURL;
        },
        signUpAPi: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/" + userID + "/" + type + "";

            return createURL;
        },
        signUpV2APi: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/" + userID + "/" + type + "";

            return createURL;
        },
        isUserSignUpAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/signup/" + userID + "";

            return createURL;
        },
        loginAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/loginWithGoogle";

            return createURL;
        },
        createPaymentAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/payment";

            return createURL;
        },
        updatePaymentAPi: function (batchID, courseID, userID, receiptID, dealID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/" + batchID + "/" + courseID + "/" + userID + "/" + receiptID + "/" + dealID + "/payment";

            return createURL;
        },
        myLearnersAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/schedule/mylearners/mygroups/" + userID + "";

            return createURL;
        },
        classTitleAPi: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/class-schedule/titles/instructor/" + instructorID + "";

            return createURL;
        },
        addClassTitleAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/class/title";

            return createURL;
        },
        createClassAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/create/class/" + userID + "";

            return createURL;
        },
        updateClassAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/update/class/" + userID + "";

            return createURL;
        },
        classesAPi: function (role, userID, type, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/classes/" + role + "/" + userID + "/" + type + "/" + limit + "/" + offset + "";

            return createURL;
        },
        classesByViewAPi: function (view, currentUserID, userID, filterVal, courseID, attendance, state, limit, offset, isCache) {
            let hasCache = "",
                hasCurrentUserID = "",
                hasAttendance = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            if (currentUserID !== undefined && currentUserID !== false) {
                hasCurrentUserID = "/" + currentUserID + ""
            }

            if (attendance !== undefined && attendance !== false) {
                hasAttendance = "/" + attendance + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/classes/" + view + "" + hasCurrentUserID + "/" + userID + "/" + filterVal + "/" + courseID + hasAttendance + "/" + state + "/" + limit + "/" + offset + "" + hasCache + "";

            return createURL;
        },
        groupsAPi: function (role, userID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get/groups/" + role + "/" + userID + "/" + limit + "/" + offset + "";

            return createURL;
        },
        addLearnersToGroupAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateUsersInGroup";

            return createURL;
        },
        updateGroupTitleAPI: function (userID, groupID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateGroup";

            return createURL;
        },
        createGroupsAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/group";

            return createURL;
        },
        crmContacts: function () {
            const createURL = this.awsHost() + "/getCRMContacts";

            return createURL;
        },
        instructorLearnersAPI: function (role, userID, limit, offset, filters) {
            let hasFilter = "";

            if (filters !== false) {
                hasFilter = "?filter=" + filters + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/mylearners/" + role + "/" + userID + "/" + limit + "/" + offset + "" + hasFilter + "";

            return createURL;
        },
        instructorBatchesAPI: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/batches/" + userID + "/" + type + "";

            return createURL;
        },
        learnerCoursesAPI: function(userID, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            };
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/"+ userID +"/courses/"+ type +""+ hasCache +"";

            return createURL;
        },
        enHeroCardsAPI: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/english-speaking/detail";

            return createURL;
        },
        classDetailAPI: function (classID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/classDetail/" + classID + "/" + userID + "";

            return createURL;
        },
        classLearnerAPI: function (classID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/classLearnerDetail/" + classID + "/"+ limit +"/"+ offset +"";

            return createURL;
        },
        demoClassEnrollAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enroll/demo/class";

            return createURL;
        },
        editClassAPI: function (classID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/class/detail/" + classID + "/instructor/" + userID + "";

            return createURL;
        },
        allCoursesAPI: function (filter) {
            let hasFilter = "";

            if (filter !== undefined && filter !== false) {
                hasFilter = "/" + filter;
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/course/list"+ hasFilter +"";

            return createURL;
        },
        allBatchesAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/admin/batches/all";

            return createURL;
        },
        enrollmentStatusAPI: function (batchID, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/batch/" + batchID + "/" + courseID + "/" + userID + "/status";

            return createURL;
        },
        generatePaymentLinkAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/payment/link";

            return createURL;
        },
        reviewsAPI: function (type, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/reviews/" + type + "/" + userID + "";

            return createURL;
        },
        paymentList: function (filter, userID, type, typeVal, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/payments/" + filter + "/" + userID + "/" + type + "/" + typeVal + "/" + limit + "/" + offset + "";

            return createURL;
        },
        enrollmentList: function (filter, userID, type, typeVal, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/" + filter + "/" + userID + "/" + type + "/" + typeVal + "/" + limit + "/" + offset + "";

            return createURL;
        },
        enrollmentsList: function (role, userID, view, instructorID, orgAdminID, referral, learnerID, eStatus, counsellorID, pStatus, limit, offset, apiVersion, courseID) {
            let hasAPIVersion = "";

            if (apiVersion !== undefined && apiVersion !== false) {
                hasAPIVersion = apiVersion;
            } else {
                hasAPIVersion = "v1"
            }


            const createURL = this.pickHost() + "/wp-json/yuno/"+ hasAPIVersion +"/enrollments/" + role + "/" + userID + "/" + view + "/" + instructorID + "/" + orgAdminID + "/" + referral + "/" + learnerID + "/" + eStatus + "/" + counsellorID + "/" + pStatus + "/" + courseID + "/" + limit + "/" + offset + "";

            return createURL;
        },
        paymentsList: function (role, userID, view, days, pStatus, counsellorID, instructorID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/payment/" + role + "/" + userID + "/" + view + "/" + days + "/" + pStatus + "/" + counsellorID + "/" + instructorID + "/" + limit + "/" + offset + "" + hasCache + "";

            return createURL;
        },
        updatePaymentLinkAPI: function () {
            const createURL = this.awsHost() + "/payments/updatelink";

            return createURL;
        },
        updateLinkAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateLink/payment";

            return createURL;
        },
        instructorListAPI: function (userID, type) {
            let hasUserID = "",
                hasType = "";

            if (type !== undefined && type !== false) {
                hasType = "/" + type;
            }

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/list" + hasUserID + ""+ hasType +"";

            return createURL;
        },
        reviewsByTypeAPI: function (channelID, instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/reviews/" + channelID + "/" + instructorId + "";

            return createURL;
        },
        batchToggleAPI: function (batchid, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/" + batchid + "/" + courseID + "/" + userID + "/status/toggle";

            return createURL;
        },
        changeBatchAPI: function (batchid, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/changeBatches";

            return createURL;
        },
        blogListAPI: function (limit, offset) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts/?per_page=" + limit + "&offset=" + offset + "&_embed";

            return createURL;
        },
        pageAPI: function (postID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/pages/" + postID + "?_embed";

            return createURL;
        },
        blogAPI: function (postID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts/" + postID + "?_embed";

            return createURL;
        },
        postCategoriesAPI: function (limit) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/categories/?per_page=" + limit + "";

            return createURL;
        },
        courseBatchesAPI: function (courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/all/" + courseID + "/0/upcomingOngoing";

            return createURL;
        },
        blogsByCategoryAPI: function (categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts?categories=" + categoryID + "&per_page=" + limit + "&offset=" + offset + "&_embed";

            return createURL;
        },
        blogCategoryAPI: function (categoryID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/categories/" + categoryID + "";

            return createURL;
        },
        settingsAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/notificationsettings/" + userID + "";

            return createURL;
        },
        updateSettingsAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/notificationsettings";

            return createURL;
        },
        addressAPI: function (userID, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/" + userID + "/address/" + type + "" + hasCache + "";

            return createURL;
        },
        updateAddressAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/user/address";

            return createURL;
        },
        listOfCounsellorsAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/counselor/list";

            return createURL;
        },
        googleContactsAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/getGoogleContacts/" + userID + "";

            return createURL;
        },
        meetingAPI: function (meetingID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/meeting/" + meetingID + "/" + state + "/"+ limit +"/"+ offset +"";

            return createURL;
        },
        participantsAPI: function (participantID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/zoom/participants/" + participantID + "";

            return createURL;
        },
        batchesGrid: function (state, userID, instructorID, courseID, classTime, category, groupType, limit, offset) {
            let hasgroupType = "";

            if (groupType !== undefined && groupType !== false) {
                hasgroupType = "/" + groupType + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + state + "/" + userID + "/" + instructorID + "/" + courseID + "/" + classTime + "/" + category + ""+ hasgroupType +"/" + limit + "/" + offset + "";

            return createURL;
        },
        mapCoursesAPI: function (state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/courses/" + state + "/" + limit + "/" + offset + "";

            return createURL;
        },
        updateInstructorCoursesAPI: function (instructorID, courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/instructor/" + instructorID + "/course/" + courseID + "";

            return createURL;
        },
        relatedCoursesAPI: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/" + instructorID + "/courses";

            return createURL;
        },
        categoryListAPI: function (filters) {
            let hasFilters = "";
    
            if (filters !== undefined) {
                hasFilters = "?filter=" + filters + "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/category/signup"+ hasFilters +"";

            return createURL;
        },
        categoryTaxonomyAPI: function (userID) {
            let hasUserID = "";

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/taxonomy/course_category" + hasUserID + "";

            return createURL;
        },
        createEBookAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/ebook/create";

            return createURL;
        },
        eBookListAPI: function (ebookID, userID, view, category, limit, offset) {

            const createURL = this.pickHost() + "/wp-json/yuno/v1/ebook/" + ebookID + "/" + userID + "/" + view + "/" + category + "/" + limit + "/" + offset + "";

            return createURL;
        },
        deleteResourceAttachmentAPI: function (resourceType, resourceID, extension) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + resourceType + "/" + resourceID + "/attachment/delete/" + extension + "";

            return createURL;
        },
        resourceEmailAPI: function (resourceType) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + resourceType + "/send/email";

            return createURL;
        },
        createDocAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/document/create";

            return createURL;
        },
        docListAPI: function (docID, userID, view, category, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/document/" + docID + "/" + userID + "/" + view + "/" + category + "/" + limit + "/" + offset + "";

            return createURL;
        },
        videoListAPI: function (videoID, query) {
            let hasVideoID = "",
                hasQuery = "";

            if (videoID !== undefined) {
                hasVideoID = videoID;
            }

            if (hasQuery !== undefined || hasQuery !== false) {
                hasQuery = query;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videos/" + hasVideoID + "";

            return createURL;
        },
        videoSearchAPI: function (query) {
            let hasQuery = "";

            if (hasQuery !== undefined || hasQuery !== false) {
                hasQuery = query;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videos/" + hasQuery + "";

            return createURL;
        },
        videoListByViewAPI: function (view, videoID, category, limit, offset) {
            let params = "";

            if (videoID !== false) {
                params = videoID;
            } else {
                let hasCategory = "";

                if (category !== undefined) {
                    hasCategory = category;
                }

                params = "" + view + "/" + hasCategory + "/" + limit + "/" + offset + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/video/get/" + params + "";

            return createURL;
        },
        createVideoAPI: function (isUpdate) {
            let params = "";

            if (isUpdate) {
                params = "update";
            } else {
                params = "create";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/video/" + params + "";

            return createURL;
        },
        userInfoAPI: function (userID, noCache) {
            let hasNoCache = "";

            if (noCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v3/user/info/" + userID + "" + hasNoCache + "";

            return createURL;
        },
        vcSettingsAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/admin/vc/settings/" + type + "";

            return createURL;
        },
        reviewAPI: function (type, userID, filter, limit, offset, sort, isCache) {
            let hasType = "",
                hasNoCache = "",
                hasFilter = "",
                hasLimit = "",
                hasOffset = "",
                hasSort = "";

            if (type !== undefined && type !== false) {
                hasType = "/" + type;
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/" + limit;
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/" + offset;
            }

            if (sort !== undefined && sort !== false) {
                hasSort = "/" + sort;
            }

            if (filter !== undefined && filter !== false) {
                hasFilter = "/" + filter;
            };

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/review" + hasType + "/" + userID + "" + hasFilter + "" + hasLimit + "" + hasOffset + "" + hasSort + "" + hasNoCache + "";

            return createURL;
        },
        courseListAPI: function (type, courseID, userID, isCache) {
            let hasNoCache = "",
                hasCourseID = "",
                hasUserID = ""; // user id or course id

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            if (courseID !== undefined && courseID !== false) {
                hasCourseID = "/" + courseID + ""
            }

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/" + type + "/detail/list" + hasCourseID + ""+ hasUserID +"" + hasNoCache + "";

            return createURL;
        },
        countriesListAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/countries";

            return createURL;
        },
        stateListAPI: function (countryID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/state/country/" + countryID + "";

            return createURL;
        },
        cityListAPI: function (stateID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/city/state/" + stateID + "";

            return createURL;
        },
        languageListAPI: function (stateID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/languages";

            return createURL;
        },
        listOfMappedInstructorAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/course/batch";

            return createURL;
        },
        batchCreateUpdateAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + type + "/batch";

            return createURL;
        },
        batchDetailAPI: function (batchID, isCache) {
            let hasNoCache = "";

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + batchID + "" + hasNoCache + "";

            return createURL;
        },
        learnerListAPI: function (search, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/list/" + search + "" + hasCache + "";

            return createURL;
        },
        instructorAvailabilityAPI: function (userID, clock, isCache) {
            let hasCache = "",
                hasClock = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            if (clock !== undefined && clock !== false) {
                hasClock = "/"+ clock +"";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/" + userID + ""+ hasClock +"" + hasCache + "";

            return createURL;
        },
        createUpdateAvailabilityAPI: function (requestType, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/" + requestType + "" + hasCache + "";

            return createURL;
        },
        timeSlotsAPI: function (isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }


            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/slots" + hasCache + "";

            return createURL;
        },
        availabilityGridAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }


            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/days/" + userID + "" + hasCache + "";

            return createURL;
        },
        instructorsByCategoryAPI: function (categoryID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/category/" + categoryID + "" + hasCache + "";

            return createURL;
        },
        capabilitiesAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/forte/" + userID + "" + hasCache + "";

            return createURL;
        },
        paymentLinkUpdateAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/payment/link";

            return createURL;
        },
        getInviteURLAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/invitation/link/" + userID + "";

            return createURL;
        },
        invitedByUserAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/" + userID + "" + hasCache + "";

            return createURL;
        },
        signInURLWithState(data) {
            const scopes = [
                "email",
                "profile"
            ];

            const state = encodeURI(JSON.stringify(data));
            let loginURL = "";

            if (yunoCognitoLoginURL !== undefined) {
                const urlObj = new URL(yunoCognitoLoginURL);
                urlObj.searchParams.set("state", state); 
                const newUrl = urlObj.toString();

                loginURL = newUrl
            } else {
                loginURL = "https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri=" + gRU + "&client_id=" + gCID + "&state=" + state + "&scope=" + scopes.join("%20") + "&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";
            }

            let url = loginURL;

            return url;
        },
        updateUserCategoryAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/insert/category";

            return createURL;
        },
        learnerHistoryAPI: function(date, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/history/"+ date +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        eventDetailAPI: function(eventType, learnerID, classID, uuid, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/history/detail/"+ eventType +"/"+ learnerID +"/"+ classID +"?uuid="+ uuid +""+ hasCache +"";

            return createURL;
        },
        profileDetailAPI: function(role, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/profile/"+ role +"/"+ userID +""+ hasCache +"";
            
            return createURL;
        },
        apiTokenExpiry: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/expire/time";

            return createURL;
        },
        apiTokenRefresh: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/google/refresh/token";

            return createURL;
        },
        staticPageAPI: function (page) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/page/" + page + "";

            return createURL; 
        },
        resourcesListingAPI: function (category, type, userID, viewType, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/resources/" + category + "/" + type + "/" + userID + "/" + viewType + "/" + limit + "/" + offset + "";

            return createURL;
        },
        resourcesDetailAPI: function (category, type, resourceID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/resources/" + category + "/" + type + "/" + resourceID + "/" + userID + "";

            return createURL;
        },
        videoTestimonialAPI: function(search, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ search +""+ hasCache +"";

            return createURL;
        },
        createExamResultAPI: function(method, limit, offset, version) {
            let hasLimit = "",
                hasOffset = "";

            if (version === undefined && version === false ) {
                version = "v1"
            } else {
                version = version
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/examresult/"+ method +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        deleteExamResultAPI: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/examresult/delete/"+ id +"";
            
            return createURL;
        },
        manageVideotestimonialAPI: function(method, limit, offset) {
            let hasLimit = "", 
                hasOffset = "";

            if (limit !== undefined) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ method +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        videotestimonialListAPI: function(criteria, view, limit, offset) {
            let hasLimit = "",
                hasOffset = "";

            if (limit !== undefined) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ criteria +"/"+ view +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        deleteVideotestimonialAPI: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/delete/"+ id +"";
            
            return createURL;
        },
        manageArticleAPI: function(method, resourceID, userID, view, category, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasMethod = "",
                hasUserID = "",
                hasView = "",
                hasCategory = "",
                hasResourceID = "";

            if (method !== undefined && method !== false) {
                hasMethod = "/"+ method +"";
            }

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            if (userID !== undefined && userID !== false) { // user-id or resource id
                hasUserID = "/"+ userID +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/article"+ hasMethod +""+ hasResourceID +""+ hasUserID +""+ hasView +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        webinarSingleAPI: function(classID, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ classID +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        webinarListingAPI: function(view, userID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ view +"/"+ userID +"/"+ limit +"/"+ offset +""+ hasCache +"";

            return createURL;
        },
        deleteWebinarAPI: function(id, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/delete/class/"+ id +"/"+ instructorID +"";
            
            return createURL;
        },
        webinarEnrollmentAPI: function(id, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/enrollment";
            
            return createURL;
        },
        webinarInsightsAPI: function(view, adminID, instructorID, categoryID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ view +"/"+ adminID +"/"+ instructorID +"/"+ categoryID +"/"+ state +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        notificationListAPI: function(userID, channel) {
            if (channel !== undefined) {
                channel = channel;
            } else {
                channel = "channel";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ channel +"/"+ userID +"";
            
            return createURL;
        },
        notificationUpdateAPI: function(channel) {
            if (channel !== undefined) {
                channel = channel;
            } else {
                channel = "channel";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ channel +"/update";
            
            return createURL;
        },
        manageNotificationAPI: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ type +"";
            
            return createURL;
        },
        searchResourceAPI: function(userID, categoryID, hasWebiner, search) {
            // const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID  +"/"+ categoryID +"/"+ hasWebiner +"?search="+ search  +"";
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+ userID +"?search="+ search  +"";
            
            return createURL;
        },
        managelearningContentAPI: function(type, resourceID) {
            let hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ type +""+ hasResourceID +"";
            
            return createURL;
        },
        learningContentAPI: function(userID, resourceID, category, limit, offset) {
            let hasResourceID = "", // Resource ID or view
                hasCategory = "", // Category or update/landing
                hasLimit = "",
                hasOffset = ""; 

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }
            
            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID +""+ hasResourceID +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        learnerInsightsAPI: function(role, userID, viewType, instructorID, days, learnerID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/"+ role +"/"+ userID +"/"+ viewType +"/"+ instructorID +"/"+ days +"/"+ learnerID +"/"+ limit +"/"+ offset +""+ hasCache +"";
            
            return createURL;
        },
        learnerInsightsClassAPI: function(classID, learnerID, isV2, isCache) {
            let hasCache = "",
                hasVersion = "";

            if (isV2 !== undefined && isV2 !== false) {
                hasVersion = "v2";
            } else {
                hasVersion = "v1";
            };

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ hasVersion +"/learner/class/"+ classID +"/"+ learnerID +""+ hasCache +"";
            
            return createURL;
        },
        signupFormAPI: function(type, filters, isCache) {
            let hasCache = "",
                hasFilters = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            };

            if (filters !== undefined && filters !== false) {
                hasFilters = "/?state="+ filters +"";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/signup/form/"+ type +""+ hasFilters +""+ hasCache +"";

            return createURL;
        },
        resourceTitleAPI: function(search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/resources/?search="+ search  +"&item="+ type +"";
            
            return createURL;
        },
        resourceDraftsAPI: function(userID, view, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/drafts/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +""+ hasCache +"";

            return createURL;
        },
        resourceDraftsDeleteAPI: function(resourceID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/drafts/delete/"+ resourceID +"";

            return createURL;
        },
        demoRequestAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/demo/class/enroll/request";
            
            return createURL;
        },
        instructorProfileAPI: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/"+ userID +"/instructor/profile";
            
            return createURL;
        },
        subjectsListAPI: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/subjects/list";
            
            return createURL;
        },
        campaignAudienceAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/campaign/audience";
            
            return createURL;
        },
        createCampaignAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/campaign";
            
            return createURL;
        },
        coursesFiltersAPI: function(categoryID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/"+ categoryID +"/"+ type +"";
            
            return createURL;
        },
        coursesResultsAPI: function(categorySlug, limit, offset, filters) {
            let hasFilter = "";

            if (filters !== false) {
                hasFilter = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/courses/web/"+ categorySlug +"/detail/"+ limit +"/"+ offset +"/"+ hasFilter +"";
            
            return createURL;
        },
        resourcesResultsAPI: function(categorySlug, userID, view, limit, offset, filters, deviceType) {
            let hasFilter = "";
            if (filters !== false) {
                hasFilter = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            }

            if (deviceType === undefined || deviceType === false) {
                deviceType = "web";
            } else {
                deviceType = deviceType;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/resources/"+ deviceType +"/"+ categorySlug +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"/"+ hasFilter +"";
            
            return createURL;
        },
        instructorStatsAPI: function(instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/getinstructorstats/"+ instructorID +"/attendance";
            
            return createURL;
        },
        instructorCoursesV2API: function (instructorID, userID, isCache) {

            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/courses/category/"+ instructorID +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        instructorInsightsAPI: function(userID, type, days, status, view, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/"+ userID +"/"+ type +"/"+ days +"/"+ status +"/"+ view +"/"+ limit +"/"+ offset +""+ hasCache +"";
            
            return createURL;
        },
        enableDisableInstructorAPI: function(userID, status) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/"+ userID +"/"+ status +"";
            
            return createURL;
        },
        vcPermissionAPI: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/vc/settings/"+ type +"";
            
            return createURL;
        },
        instructorProfileInsightsAPI: function(version, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/users/"+ instructorID +"/profile";
            
            return createURL;
        },
        piiAPI: function(version, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/users/pii/"+ type +"";
            
            return createURL;
        },
        mappedCoursesAPI: function(instructorID, groupType) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mapped/course/"+ instructorID +"/"+ groupType +"";

            return createURL;
        },
        makeFeaturedAPI: function(instructorID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/featured/"+ instructorID +"/"+ type +"";
            
            return createURL;
        },
        dashboardLearnersAPI: function(search) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/user/enrollment?search="+ search +"";
            
            return createURL;
        },
        manageDashboardAPI: function(type, recordID) {
            let hasRecordID = "";

            if (recordID !== undefined && recordID !== false) {
                hasRecordID = "/"+ recordID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/"+ type +"/report"+ hasRecordID +"";
            
            return createURL;
        },
        dashboardListAPI: function(record, view, role, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasView = "",
                hasRole = "";

            if (role !== undefined && role !== false) {
                hasRole = "/"+ role +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/report/"+ record +""+ hasView +""+ hasRole +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        enrollmentDashboardAPI: function(reportID, view, role, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/enrollment/report/"+ reportID +"/"+ view +"/"+ role +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        usersListAPI: function(role) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/list/"+ role +"";
            
            return createURL;
        },
        enrollmentClassDetailAPI: function(learnerID, batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+ learnerID +"/"+ batchID +"";
            
            return createURL;
        },
        vimeoVideoAPI: function(videoID) {
            const createURL = "https://api.vimeo.com/videos/"+ videoID +"";
            
            return createURL;
        },
        batchLearnersAPI: function(batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batch/"+ batchID +"/learners";
            
            return createURL;
        },
        courseBatchLearners: function(batchID, courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batch/"+ batchID +"/"+ courseID +"/learners";
            
            return createURL;
        },
        blogCategoriesAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/taxonomy/blog_category";

            return createURL;
        },
        manageBlogAPI: function(type, resourceID) {
            let hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/blog/"+ type +""+ hasResourceID +"";

            return createURL;
        },
        publishedBlogsAPI: function(resourceID, userID, view, category, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasMethod = "",
                hasUserID = "",
                hasView = "",
                hasCategory = "",
                hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            if (userID !== undefined && userID !== false) { // user-id or resource id
                hasUserID = "/"+ userID +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) { 
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/blog"+ hasResourceID +""+ hasUserID +""+ hasView +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        categoriesListAPI: function(criteria) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/"+ criteria +"";
            
            return createURL;
        },
        vimeoUploadVideoAPI: function() {
            const createURL = "https://api.vimeo.com/me/videos";
            
            return createURL;
        },
        vimeoVideoPrivacyAPI: function(videoID, domain) {
            const createURL = "https://api.vimeo.com/videos/"+ videoID +"/privacy/domains/"+ domain +"";
            
            return createURL;
        },
        manageVideoClippingAPI: function(criteria, instructorID,  view, limit, offset, isList) {
            if (isList !== undefined && isList !== false) {
                isList = "clippings"
            } else {
                isList = "clipping"
            }

            if (instructorID !== undefined && instructorID !== false) {
                instructorID = "/" + instructorID;
            } else {
                instructorID = "";
            }

            // @view: We can also other value

            if (view !== undefined && view !== false) {
                view = "/" + view;
            } else {
                view = "";
            }

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit;
            } else {
                limit = "";
            }

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset;
            } else {
                offset = "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ isList +"/"+ criteria +""+ instructorID +""+ view +""+ limit +""+ offset +"";
            
            return createURL;
        },
        instructorMyCourses: function(instructorID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mycourses/instructor/"+ instructorID +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        instructorCourseBatches: function(courseID, role, userID, criteria, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mycourses/"+ courseID +"/"+ role +"/"+ userID  +"/batches/"+ criteria +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        manageBookmarkAPI: function(type, version, userID, limit, offset, filters) {
            if (version !== undefined && version !== false) {
                version = version
            } else {
                version = "v1"
            }

            if (userID !== undefined && userID !== false) {
                userID = "/" + userID
            } else {
                userID = ""
            };

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/bookmark/"+ type +""+ userID +""+ limit +""+ offset +""+ filters +"";
            
            return createURL;
        },
        availableCourses: function(personalization, limit, offset, filters) {
            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?params=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/available-courses/"+ personalization +""+ limit +""+ offset +""+ filters +"";
            
            return createURL;
        },
        availableBatches: function(courseID, limit, offset, filters) {
            if (courseID !== undefined && courseID !== false) {
                courseID = "/" + courseID
            } else {
                courseID = ""
            };

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?params=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/upcomingOngoing"+ courseID  +""+ limit +""+ offset +"";
            
            return createURL;
        },
        courseEnrollmentStatus: function(courseID, userID) {
            if (courseID !== undefined && courseID !== false) {
                courseID = "/" + courseID
            } else {
                courseID = ""
            };

            if (userID !== undefined && userID !== false) {
                userID = "/" + userID
            } else {
                userID = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/enrollment-status"+ courseID +""+ userID +"";
            
            return createURL;
        },
        courseOneToOne: function(courseID, personalization, day, time, duration, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/"+ courseID +"/"+ personalization +"/"+ day +"/"+ time +"/"+ duration +"/"+ limit +"/"+ offset +"";
            
            return createURL; 
        },
        cloudinaryImageUpload: function(unsigned) {
            if (unsigned) {
                return {
                    upload_URL: "https://api.cloudinary.com/v1_1/harman-singh/upload",
                    upload_preset: "jg32bezo"
                }
            } else {
                return false
            }
        },
        imageUpload: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/image/upload/";
            
            return createURL;
        },
        categorySearch: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/search";
            
            return createURL;
        },
        categoryResources: function(filters) {
            if (filters !== undefined && filters !== false) {
                filters = "?ids=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/resources/"+ filters +"";
            
            return createURL;
        },
        coursesList: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/courses/all";
            
            return createURL;
        },
        upcomingOngoingBatchesList: function(courseID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+ courseID +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        pastBatchesList: function(courseID, personalisation, userID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/past/"+ courseID +"/"+ personalisation +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        checkout: function(courseID, batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/checkout/"+ courseID + "/"+ batchID +"";
            
            return createURL;
        },
        instructorInsights: function(type, userID, days, status, view, vcStatus, courseID, categoryID, featured, nativeLanguage, avgRating, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/"+ type +"/"+ userID +"/"+ days +"/"+ status +"/"+ view +"/"+ vcStatus +"/"+ courseID +"/"+ categoryID +"/"+ featured +"/"+ nativeLanguage +"/"+ avgRating +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        updateNativelanguage: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/nativelanguage/"+ userID +"";
            
            return createURL;
        },
        endBatch: function(batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/endbatch/"+ batchID +"";
            
            return createURL;
        },
        collections: function(userID, view, categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID +"/"+ view +"/"+ categoryID +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        instructorVideotestimonial: function(userID, view, categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor";
            
            return createURL;
        },
        courses: function(userID, courseID, categoryID, format, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/"+ userID +"/"+ courseID +"/"+ categoryID +"/"+ format +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        activityList: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/activity";
            
            return createURL;
        },
        subCategoriyList: function(category) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/"+ category +"/subcategories";
            
            return createURL;
        },
        courseSchedule: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/schedule/"+ type +"";
            
            return createURL;
        },
        courseScheduleForm: function(courseID, scheduleID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/schedule/"+ courseID +"/"+ scheduleID +"";
            
            return createURL;
        },
        createCSV: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/export/csv";
            
            return createURL;
        },
        downloadCSV: function(userID, role) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/csv/"+ userID +"/"+ role +"";
            
            return createURL;
        },
        courseDetail: function(courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/detail/"+ courseID +"/"+ userID +"";
            
            return createURL;
        },
        reviewIssues: function(id, role, taxonomyID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/items/"+ id +"/issue/"+ role +"/"+ taxonomyID +"";
            
            return createURL;
        },
        reviewPost: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/post";

            return createURL;
        },
        formReview: function(userID, classID, feedbackTypeID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/get/"+ userID +"/"+ classID +"/"+ feedbackTypeID +"";

            return createURL;
        },
        classReviews: function(type, classID, feedbackTypeID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/average/"+ type +"/"+ classID +"/"+ feedbackTypeID +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        classReviewsByInstructor: function(type, instructorID, feedbackTypeID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/classreviews/instructor/"+ type +"/"+ instructorID +"/"+ feedbackTypeID +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        listOfUser: function (search, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ type +"/list/" + search + "" + hasCache + "";

            return createURL;
        },
        learnerActivity: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/get-feedback/"+ userID +"";

            return createURL;
        },
        recentLearnerClass: function(userID, itemID) {

            const createURL = this.pickHost() + "/wp-json/yuno/v2/get-feedback-information/"+ userID +"/"+ itemID +"";

            return createURL;
        },
        enrollmentListByType: function(type, userID, view, orgAdminID, referrerID, enrollmentStarts, enrollmentEnds, instructorID, attendance, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ type +"/enrollments/"+ userID +"/"+ view +"/"+ orgAdminID +"/"+ referrerID +"/"+ enrollmentStarts +"/"+ enrollmentEnds +"/"+ instructorID +"/"+ attendance +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        courseEconomics: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ type +"";

            return createURL;
        },
        courseEconomicsForm: function(courseID, formID, personalisation) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ courseID +"/"+ formID +"/"+ personalisation +"";
            return createURL;
        },
        courseEconomicsSummary: function(courseID, formID, batchSize, personalisation) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ courseID +"/"+ formID +"/"+ batchSize +"/"+ personalisation +"";

            return createURL;
        },
        csvList: function(userID, role, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/csv/"+ userID +"/"+ role +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        orgList: function (search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/org/" + search + "";

            return createURL;
        },
        referrerDetails: function (search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/details";

            return createURL;
        },
        referrerID: function (userID, buyerID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/"+ userID +"/"+ buyerID +"";

            return createURL;
        },
        mappedInstructors: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/course/"+ instructorID +"";

            return createURL;
        },
        generateRefferralURL: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get-referral-url?params="+ encodeURI(JSON.stringify(params)) +"";

            return createURL;
        },
        generateRefferralCode: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/code/generate";

            return createURL;
        },
        referrerURL: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/url";

            return createURL;
        },
        referralDetail: function (batchID, courseID, userID, version) {
            if (version === undefined) {
                version = "v1"
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/referrer/user/"+ batchID +"/"+ courseID +"/"+ userID +"";

            return createURL;
        },
        referralReports: function (userID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/report/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        orgToken: function (userID, type) {
            let createURL = "";

            if (type === "POST") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/create/token";
            } else if (type === "PUT") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/update/token";
            } else {
                createURL = this.pickHost() + "/wp-json/yuno/v1/token/"+ userID +"";
            }

            return createURL;
        },
        webhooks: function (type, webHookID, orgID, view, limit, offset) {
            let createURL = "";

            if (type === "grid") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/"+ webHookID +"/"+ orgID +"/"+ view +"/"+ limit +"/"+ offset +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/create";
            } else if (type === "events") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/events";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/update";
            }

            return createURL;
        },
        seo: function (type, pageID, searchCriteria, searchQuery) {
            let createURL = "";

            if (type === "status") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/status/"+ pageID +"";
            } else if (type === "markNoIndex") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/mark-no-index";
            } else if (type === "pageSearch") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/search-get/"+ searchCriteria +"/?search="+ searchQuery +"";
            };

            return createURL;
        },
        quiz: function (type, quizID, userID, query) {
            let createURL = "";

            if (query!== undefined && query !== false) {
                query = query
            } else {
                query = ""
            }

            if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz";
            } else if (type === "edit") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/"+ quizID +"/"+ query +"";
            } else if (type === "quizgrid") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quizzes";
            } else if (type === "quizgridV2") {
                createURL = this.pickHost() + "/wp-json/yuno/v3/category/practice";
            } else if (type === "attempt") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/attempt/";
            } else if (type === "review") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/attempt/answers/"+ quizID +"/"+ userID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/"+ quizID +"";
            } else if (type === "quizReorder") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/reorder";
            };

            return createURL;
        },
        question: function (type, category, questionType, questionID, questionSetID) {
            let createURL = "";

            if (type === "questions") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionbank/"+ category +"/"+ questionType +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question";
            } else if (type === "single") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question/"+ questionID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question/"+ questionID +"";
            } else if (type === "deleteQuestionSet") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset/"+ questionSetID +"";
            } else if (type === "attempt") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question-attempt/"+ questionID +"";
            } else if (type === "attemptQuestionSet") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-attempt/"+ questionSetID +"";
            } else if (type === "questionset") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset";
            } else if (type === "questionsetGET") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset/"+ questionSetID +"";
            } else if (type === "questionsetQuestions") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions";
            } else if (type === "questionsetQuestionsGET") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/"+ questionSetID +"";
            } else if (type === "questionsetQuestionsList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/reorder/"+ questionSetID +"";
            } else if (type === "questionsetQuestionsReorder") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/reorder";
            };

            return createURL;
        },
        enrollments: function (type, batchID, courseID, query) {
            let createURL = "";

            if (type === "active") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/batch/"+ batchID +"/"+ courseID +"/learners";
            } else if (type === "extendDate") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/update";
            }

            return createURL; 
        },
        blog: function (type, categoryID, limit, offset) {
            let createURL = "";

            if (type === "recentSingle") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+ categoryID +"";
            } else if (type === "recentList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/recent/"+ limit +"/"+ offset +"?is_list=true&category_id="+ categoryID +"";
            } else if (type === "categoriesList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/categories";
            } else if (type === "detail") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/"+ categoryID +"";
            };
            
            return createURL;
        },
        writingTask: function (type, examType, taskType, dataID, userID, view, limit, offset) {
            let createURL = "";

            if (type === "type") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/writingtask/type/"+ examType +"/"+ taskType +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/create";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/update";
            } else if (type === "singleRecord") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/"+ dataID +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";
            } else if (type === "payload") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/"+ dataID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/writingtask/delete/"+ dataID +"";
            };

            

            return createURL;
        },
        categoryLandingPage: function (type, categoryID) {
            let createURL = "";

            if (type === "category") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/category/"+ categoryID +"";
            } else if (type === "practiceTests") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/category/practice/";
            };

            return createURL;
        },
        examResults: function (category, view, limit, offset) {
            let createURL = this.pickHost() + "/wp-json/yuno/v2/examresult/"+ category +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        mainNav: function (userID, role) {
            let createURL = this.pickHost() + "/wp-json/yuno/v3/menu/"+ userID +"/"+ role +"";

            return createURL;
        },
        org: function (type, id, userID, courseEconomicsType, courseID, courseEconomicsID, personalization, courseScheduleType, createBatchType, categoryID, instructorFormat, view, limit, offset) {
            const baseHost = this.pickHost();
            const paths = {
                info: `/wp-json/yuno/v2/org/${id}`,
                singleLearner: `/wp-json/yuno/v1/org/user/${id}/${userID}`,
                industries: "/wp-json/yuno/v1/org/industries/details",
                detailsUpdate: "/wp-json/yuno/v1/org/update",
                create: "/wp-json/yuno/v1/org/create",
                settings: `/wp-json/yuno/v2/org/settings/${id}`,
                createCourseEconomics: `/wp-json/yuno/v3/org/course/economics/${courseEconomicsType}`,
                courseEconomicsPersonalization: `/wp-json/yuno/v3/org/course/economics/${courseID}/${courseEconomicsID}/${personalization}`,
                courseDetailForm: `/wp-json/yuno/v3/org/course/${courseID}`,
                courseDetailUpdate: `/wp-json/yuno/v3/org/course`,
                orgAcademies: `/wp-json/yuno/v3/org/academies/${id}`,
                courseSchedule: `/wp-json/yuno/v3/org/course/schedule/${courseScheduleType}`,
                batchesUpcomingOngoing: `/wp-json/yuno/v3/org/batches/upcomingOngoing`,
                createBatch: `/wp-json/yuno/v3/org/${createBatchType}/batch`,
                courses: `/wp-json/yuno/v3/org/course/${userID}/${id}/${courseEconomicsType}/${courseID}/${categoryID}/${instructorFormat}/${view}/${limit}/${offset}`,
                batchesPast: `/wp-json/yuno/v3/org/batches/past/${id}/${userID}/${courseEconomicsType}/${courseID}/${view}/${limit}/${offset}`,
                enrollments: `/wp-json/yuno/v3/org/academy/enrollments`,
                academyDetails: `/wp-json/yuno/v1/academy/${id}`,
                academyInstructors: `/wp-json/yuno/v1/org/user/instructors/${id}`,
                createUpdateAcademy: `/wp-json/yuno/v1/academy`,
                getAcademy: `/wp-json/yuno/v1/academy/${id}`,
            };

            return baseHost + (paths[type] || "");
        },
        leadForm: function (type, userID, categorySlug) {
            let createURL = "";

            if (type === "steps") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/signup/form/"+ userID +"/"+ categorySlug +"";
            } else if (type === "postStep") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/signup/form/update/"+ userID +"/"+ categorySlug +"";
            } else if (type === "updateMobile") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/admin/user/phone/update ";
            };

            return createURL;
        },
        availableCoursesV2: function (type) {
            let createURL = "";

            if (type === "listing") {
                createURL = this.pickHost() + "/wp-json/yuno/v3/available-courses/"; // this is url
            }

            return createURL;
        },
        activeCategory: function (type) {
            let createURL = "";

            if (type === "set") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/user/add/category/";
            }

            return createURL;
        },
        learners: function (type, role, view, limit, offset, learnerID) {
            let endpoint = "";
        
            switch (type) {
                case "insights":
                    endpoint = `/wp-json/yuno/v2/users/${role}/learner/${view}/${limit}/${offset}`;
                    break;
                case "demoRequests":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${role}/list/${limit}/${offset}`;
                    break;
                case "demoRequestsOrg":
                    endpoint = `/wp-json/yuno/v1/demo-requests/org-admin/${role}/${view}/${limit}/${offset}`;
                    break;
                case "learnerDetailOrg":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${role}/${learnerID}`;
                    break;
                case "learnerDetail":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${learnerID}`;
                    break;
                case "instructorLearnerDetail":
                    endpoint = `/wp-json/yuno/v2/instructor/mylearner/${learnerID}`;
                    break;
                case "orgAdminLearners":
                    endpoint = `/wp-json/yuno/v2/orgadmin/learner/${view}/${limit}/${offset}`;
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        deleteUser: function (type) {
            let createURL = "";

            if (type === "requested") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/user/add/delete/requests";
            }
            
            return createURL;
        },
        generic: function (type, params, role, orgID, academyID) {
            let createURL = "";

            switch (type) {
                case "googleFonts":
                    createURL = `https://www.googleapis.com/webfonts/v1/webfonts/?${params}`;
                    break;

                case "courseSuggestions":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${params}`;
                    break;

                case "contentSearch":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${params}`;
                    break;
                case "userSearch":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${params}/${role}/${orgID}`;
                    break;

                case "orgBatches":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${academyID}`;
                    break;

                case "org":
                    createURL = `${this.pickHost()}/wp-json/yuno/v2/org/${orgID}`;
                    break;

                case "categories":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/all/category/signup`;
                    break;

                default:
                    break;
            }

            return createURL;
        },
        course: function (type, courseID, userID, view, limit, offset) {
            let createURL = "";

            switch (type) {
                case "payload":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/${courseID}`;
                    break;

                case "updateCourse":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course`;
                    break;

                case "mapInstructor":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;
                    break;

                case "invitedInstructors":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${courseID}/${userID}/${view}/${limit}/${offset}`;
                    break;

                case "createBatchOrg":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;
                    break;

                case "updateBatchOrg":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;
                    break;

                case "mapCourses":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`;
                    break;
                    
                default:
                    break;
            }

            return createURL;
        },
        learner: function (type, learnerID, courseType , role , limit , offset) {
            let endpoint = "";
            switch (type) {
                case "enrolledCourses":
                    endpoint = `/wp-json/yuno/v3/learner/${learnerID}/enrollments/${courseType}`;
                    break;
                case "classes":
                    endpoint = `/wp-json/yuno/v4/classes/${courseType}/${role}/${learnerID}?limit=${limit}&offset=${offset}`;
                    break;
                case "filters":
                    endpoint = `/wp-json/yuno/v4/classes/filter/${role}/${learnerID}`;
                    break;
                default:
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        classes: function (type, view, currentLoggedInUserID, instructorID, demoOrPrivate, courseID, attendancePercentage, classType, limit, offset, grandTotal) {
            let endpoint = "";
        
            switch (type) {
                case "allClasses":
                    endpoint = `/wp-json/yuno/v3/classes/${view}/${currentLoggedInUserID}/${instructorID}/${demoOrPrivate}/${courseID}/${attendancePercentage}/${classType}/${limit}/${offset}/${grandTotal}`;
                    break;
                default:
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        instructor: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                learners: `/wp-json/yuno/v1/instructor/mylearners/${props.instructorID}/${props.limit}/${props.offset}`,
                learnerDetail: `/wp-json/yuno/v2/instructor/mylearner/${props.learnerID}`,
                learnersV2: `/wp-json/yuno/v2/${props.role}/mylearners/${props.instructorID}/${props.view}/${props.limit}/${props.offset}`,
                schedulePrivate: `/wp-json/yuno/v4/class/schedule-private`
            };

            return baseHost + (paths[type] || "");
        },
        attendance: function (type, role, view, limit, offset,) {
            const baseHost = this.pickHost();
            const paths = {
                learners: `/wp-json/yuno/v1/attendance/${role}/${view}/${limit}/${offset}`
            };

            return baseHost + (paths[type] || "");
        },
        user: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                region: `/wp-json/yuno/v3/user/region/${props.loggedinUserID}`,
                languages: `/wp-json/yuno/v3/user/languages`,
                countries: `/wp-json/yuno/v1/countries`,
                timezones: `/wp-json/yuno/v3/user/timezones`,
                currencies: `/wp-json/yuno/v3/user/currencies`,
                virtualClassRoom: `/wp-json/yuno/v3/virtual-classroom/${props.loggedinUserID}`,
                virtualClassRoomV4: `/wp-json/yuno/v4/settings/virtual-classrooms/${props.loggedinUserID}`,
                vcDisconnect: `/wp-json/yuno/v3/virtual-classroom`,
                classLaunchStatus: `/wp-json/yuno/v2/class/updateLaunchStatus/${props.classID}`,
                classSchedule: `/wp-json/yuno/v4/classes/demo`,
                slots: `/wp-json/yuno/v2/instructor/freebusy/slots`,
            };

            return baseHost + (paths[type] || "");
        },
        classInsights: function (type, currentLoggedInUserID, view, limit, offset) {
            const baseHost = this.pickHost();
            const paths = {
                yunoAdminPast: `/wp-json/yuno/v3/classes/past/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoAdminOngoingUpcoming: `/wp-json/yuno/v3/classes/ongoingUpcoming/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoOrgPast: `/wp-json/yuno/v3/org/classes/past/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoOrgOngoingUpcoming: `/wp-json/yuno/v3/org/classes/ongoingUpcoming/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
            };

            return baseHost + (paths[type] || "");
        },
        resource: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                batches: `/wp-json/yuno/v2/batches/upcomingOngoing/${props.role}/${props.userID}/${props.limit}/${props.offset}`,
                batchLearners: `/wp-json/yuno/v1/batch/${props.batchID}/${props.courseID}/learners`,
                sendResource: `/wp-json/yuno/v1/resources/send/resource`
            };

            return baseHost + (paths[type] || "");
        },
        academy: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                academies: `/wp-json/yuno/v3/org/academies`,
                activeOrg: `/wp-json/yuno/v3/user/state`,
                
            };
            return baseHost + (paths[type] || "");
        },
        googleMapLocation: function (type, props) {
            const baseHost = "https://maps.googleapis.com/maps/api";
            const paths = {
                geoLocation: `/geocode/json?latlng=${props.latitude},${props.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${props.key}`,
                detail: `/place/details/json?place_id=${props.placeID}&key=${props.key}`,
            };
            return baseHost + (paths[type] || "");
        },
        header: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                menu: `/wp-json/yuno/v4/menus/${props.userID}/${props.orgID}`,
            };
            return baseHost + (paths[type] || "");
        },
    };

    const findObjectByKey = (array, key, value) => {
        return array.find(obj => obj[key] === value) || null;
    };

    const assignVValidationObj = function (customMsg) {
        const VeeValidate = window.VeeValidate,
            VeeValidateRules = window.VeeValidateRules,
            ValidationProvider = VeeValidate.ValidationProvider,
            ValidationObserver = VeeValidate.ValidationObserver;

        VeeValidate.extend('minLength', {
            validate(value, { length }) {
                return value.length >= length;
            },
            params: ['length'],
            message: 'At least {length} items must be selected'
        });

        VeeValidate.extend('maxLength', {
            validate(value, { length }) {
                return value.length <= length;
            },
            params: ['length'],
            message: 'No more than {length} items must be selected'
        });

        VeeValidate.extend('isSelected', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Custom title is not allowed'
        });

        VeeValidate.extend('isSelectedFromList', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Please select the user from list'
        });

        VeeValidate.extend('isBatchSelected', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Please select the batch from list'
        });

        VeeValidate.extend('notAllowed', {
            validate(value, { number }) {
                const regExp = /^0[0-9].*$/;

                return regExp.test(value) === false;
            },
            params: ['number'],
            message: `Phone number can't start with {number}`
        });

        VeeValidate.extend('greaterThen', {
            validate(value, { number }) {
                if (value > number) {
                    return true;
                } else {
                    return false;
                }
            },
            params: ['number'],
            message: `Value should be greater then {number}`
        });

        VeeValidate.extend('isOverlapping', {
            validate(value) {
                if (value) {
                    return false;
                } else {
                    return true;
                }
            },
            
            message: `Time overlap with another set of time`
        });

        VeeValidate.extend('isEndTime', {
            validate(value) {
                if (value) {
                    return false;
                } else {
                    return true;
                }
            },
            
            message: `Choose an end time later than the start time.`
        });

        VeeValidate.extend('selectLearner', {
            validate(value, { number }) {
                return number !== 0;
            },
            params: ['number'],
            message: `Please add at least 1 learner from list`
        });

        VeeValidate.extend('isEmpty', {
            validate(value, { getValue }) {
                if (getValue === "") {
                    return false
                } else {
                    return true
                }
            },
            params: ['getValue'],
            message: "Field should not be blank"
        });

        VeeValidate.extend('isNotBlank', {
            validate(value, { getValue }) {
                if (getValue === null) {
                    return false
                } else {
                    return true
                }
            },
            params: ['getValue'],
            message: `Please select the learner from list`
        });

        VeeValidate.extend('url', {
            validate (value, {getValue}) {
                const regExp = /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;

                if (regExp.test(value)) {
                    return true
                } else {
                    return false
                }
            },
            params: ['getValue'],
            message: `Please enter valid URL`
        });

        VeeValidate.extend('httpsURL', {
            validate (value, {getValue}) {
                const regExp = /^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/;

                if (regExp.test(value)) {
                    return true
                } else {
                    return false
                }
            },
            params: ['getValue'],
            message: `Please make sure URL should start with "https" and should be valid`
        });

        VeeValidate.extend('email', {
            validate (value) {
            const regExp = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (regExp.test(value)) {
                return true;
            } else {
                return false;
            }
            },
            message: 'Please enter a valid email address'
        });

        VeeValidate.extend('hasCurlyBrackets', {
            validate(value) {
                const regExp = /\{.+?\}/; 
                return regExp.test(value);
            },
            message: 'String must have curly brackets with content inside'
        });

        for (let key in customMsg.messages) {
            VeeValidate.extend(key, VeeValidateRules[key]);
        };

        VeeValidate.localize('validationMsg', customMsg);
        Vue.component('ValidationProvider', ValidationProvider);
        Vue.component('ValidationObserver', ValidationObserver);
    };

    const heightOfEle = function (el, includeMargin) {
        let height = el.offsetHeight;

        if (includeMargin) {
            let style = getComputedStyle(el);

            height += parseInt(style.marginTop) + parseInt(style.marginBottom);
            return height
        } else {
            return height;
        };
    };

    const removeValInArr = function (arr) {
        let what, a = arguments, L = a.length, ax;
        while (L > 1 && arr.length) {
            what = a[--L];
            while ((ax = arr.indexOf(what)) !== -1) {
                arr.splice(ax, 1);
            }
        }
        return arr;
    };

    const removeObjInArr = function (arr, attr, value) {
        let i = arr.length;

        while (i--) {
            if (arr[i] &&
                arr[i].hasOwnProperty(attr) &&
                (arguments.length > 2 && arr[i][attr] === value)) {

                arr.splice(i, 1);

            }
        }
        return arr;
    };

    const formatDate = function (d) {
        var date = new Date(d);

        if (isNaN(date.getTime())) {
            return d;
        } else {
            var month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

            day = date.getDate();

            if (day < 10) {
                day = "0" + day;
            }

            return month[date.getMonth()] + " " + day + " " + date.getFullYear();
        };
    };

    const timeConvert = function (time) {
        time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];

        if (time.length > 1) {
            time = time.slice(1);
            time[5] = +time[0] < 12 ? 'AM' : 'PM';
            time[0] = +time[0] % 12 || 12;
        }
        return time.join('');
    };

    const dateTimeToArray = function (str) {
        const splitarray = new Array(),
            toArray = str.split(" ");

        return toArray;
    };

    const getQueryParameter = function (queryKey) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == queryKey) { return pair[1]; }
        }
        return (false);
    };

    const queryParameterNonWindow = function (url, queryKey) {
        var query = url.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == queryKey) { return pair[1]; }
        }
        return (false);
    };

    const countriesData = function () {
        return [{ name: 'Afghanistan', code: 'AF' }, { name: 'Åland Islands', code: 'AX' }, { name: 'Albania', code: 'AL' }, { name: 'Algeria', code: 'DZ' }, { name: 'American Samoa', code: 'AS' }, { name: 'AndorrA', code: 'AD' }, { name: 'Angola', code: 'AO' }, { name: 'Anguilla', code: 'AI' }, { name: 'Antarctica', code: 'AQ' }, { name: 'Antigua and Barbuda', code: 'AG' }, { name: 'Argentina', code: 'AR' }, { name: 'Armenia', code: 'AM' }, { name: 'Aruba', code: 'AW' }, { name: 'Australia', code: 'AU' }, { name: 'Austria', code: 'AT' }, { name: 'Azerbaijan', code: 'AZ' }, { name: 'Bahamas', code: 'BS' }, { name: 'Bahrain', code: 'BH' }, { name: 'Bangladesh', code: 'BD' }, { name: 'Barbados', code: 'BB' }, { name: 'Belarus', code: 'BY' }, { name: 'Belgium', code: 'BE' }, { name: 'Belize', code: 'BZ' }, { name: 'Benin', code: 'BJ' }, { name: 'Bermuda', code: 'BM' }, { name: 'Bhutan', code: 'BT' }, { name: 'Bolivia', code: 'BO' }, { name: 'Bosnia and Herzegovina', code: 'BA' }, { name: 'Botswana', code: 'BW' }, { name: 'Bouvet Island', code: 'BV' }, { name: 'Brazil', code: 'BR' }, { name: 'British Indian Ocean Territory', code: 'IO' }, { name: 'Brunei Darussalam', code: 'BN' }, { name: 'Bulgaria', code: 'BG' }, { name: 'Burkina Faso', code: 'BF' }, { name: 'Burundi', code: 'BI' }, { name: 'Cambodia', code: 'KH' }, { name: 'Cameroon', code: 'CM' }, { name: 'Canada', code: 'CA' }, { name: 'Cape Verde', code: 'CV' }, { name: 'Cayman Islands', code: 'KY' }, { name: 'Central African Republic', code: 'CF' }, { name: 'Chad', code: 'TD' }, { name: 'Chile', code: 'CL' }, { name: 'China', code: 'CN' }, { name: 'Christmas Island', code: 'CX' }, { name: 'Cocos (Keeling) Islands', code: 'CC' }, { name: 'Colombia', code: 'CO' }, { name: 'Comoros', code: 'KM' }, { name: 'Congo', code: 'CG' }, { name: 'Congo, The Democratic Republic of the', code: 'CD' }, { name: 'Cook Islands', code: 'CK' }, { name: 'Costa Rica', code: 'CR' }, { name: 'Cote D\'Ivoire', code: 'CI' }, { name: 'Croatia', code: 'HR' }, { name: 'Cuba', code: 'CU' }, { name: 'Cyprus', code: 'CY' }, { name: 'Czech Republic', code: 'CZ' }, { name: 'Denmark', code: 'DK' }, { name: 'Djibouti', code: 'DJ' }, { name: 'Dominica', code: 'DM' }, { name: 'Dominican Republic', code: 'DO' }, { name: 'Ecuador', code: 'EC' }, { name: 'Egypt', code: 'EG' }, { name: 'El Salvador', code: 'SV' }, { name: 'Equatorial Guinea', code: 'GQ' }, { name: 'Eritrea', code: 'ER' }, { name: 'Estonia', code: 'EE' }, { name: 'Ethiopia', code: 'ET' }, { name: 'Falkland Islands (Malvinas)', code: 'FK' }, { name: 'Faroe Islands', code: 'FO' }, { name: 'Fiji', code: 'FJ' }, { name: 'Finland', code: 'FI' }, { name: 'France', code: 'FR' }, { name: 'French Guiana', code: 'GF' }, { name: 'French Polynesia', code: 'PF' }, { name: 'French Southern Territories', code: 'TF' }, { name: 'Gabon', code: 'GA' }, { name: 'Gambia', code: 'GM' }, { name: 'Georgia', code: 'GE' }, { name: 'Germany', code: 'DE' }, { name: 'Ghana', code: 'GH' }, { name: 'Gibraltar', code: 'GI' }, { name: 'Greece', code: 'GR' }, { name: 'Greenland', code: 'GL' }, { name: 'Grenada', code: 'GD' }, { name: 'Guadeloupe', code: 'GP' }, { name: 'Guam', code: 'GU' }, { name: 'Guatemala', code: 'GT' }, { name: 'Guernsey', code: 'GG' }, { name: 'Guinea', code: 'GN' }, { name: 'Guinea-Bissau', code: 'GW' }, { name: 'Guyana', code: 'GY' }, { name: 'Haiti', code: 'HT' }, { name: 'Heard Island and Mcdonald Islands', code: 'HM' }, { name: 'Holy See (Vatican City State)', code: 'VA' }, { name: 'Honduras', code: 'HN' }, { name: 'Hong Kong', code: 'HK' }, { name: 'Hungary', code: 'HU' }, { name: 'Iceland', code: 'IS' }, { name: 'India', code: 'IN' }, { name: 'Indonesia', code: 'ID' }, { name: 'Iran, Islamic Republic Of', code: 'IR' }, { name: 'Iraq', code: 'IQ' }, { name: 'Ireland', code: 'IE' }, { name: 'Isle of Man', code: 'IM' }, { name: 'Israel', code: 'IL' }, { name: 'Italy', code: 'IT' }, { name: 'Jamaica', code: 'JM' }, { name: 'Japan', code: 'JP' }, { name: 'Jersey', code: 'JE' }, { name: 'Jordan', code: 'JO' }, { name: 'Kazakhstan', code: 'KZ' }, { name: 'Kenya', code: 'KE' }, { name: 'Kiribati', code: 'KI' }, { name: 'Korea, Democratic People\'S Republic of', code: 'KP' }, { name: 'Korea, Republic of', code: 'KR' }, { name: 'Kuwait', code: 'KW' }, { name: 'Kyrgyzstan', code: 'KG' }, { name: 'Lao People\'S Democratic Republic', code: 'LA' }, { name: 'Latvia', code: 'LV' }, { name: 'Lebanon', code: 'LB' }, { name: 'Lesotho', code: 'LS' }, { name: 'Liberia', code: 'LR' }, { name: 'Libyan Arab Jamahiriya', code: 'LY' }, { name: 'Liechtenstein', code: 'LI' }, { name: 'Lithuania', code: 'LT' }, { name: 'Luxembourg', code: 'LU' }, { name: 'Macao', code: 'MO' }, { name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK' }, { name: 'Madagascar', code: 'MG' }, { name: 'Malawi', code: 'MW' }, { name: 'Malaysia', code: 'MY' }, { name: 'Maldives', code: 'MV' }, { name: 'Mali', code: 'ML' }, { name: 'Malta', code: 'MT' }, { name: 'Marshall Islands', code: 'MH' }, { name: 'Martinique', code: 'MQ' }, { name: 'Mauritania', code: 'MR' }, { name: 'Mauritius', code: 'MU' }, { name: 'Mayotte', code: 'YT' }, { name: 'Mexico', code: 'MX' }, { name: 'Micronesia, Federated States of', code: 'FM' }, { name: 'Moldova, Republic of', code: 'MD' }, { name: 'Monaco', code: 'MC' }, { name: 'Mongolia', code: 'MN' }, { name: 'Montserrat', code: 'MS' }, { name: 'Morocco', code: 'MA' }, { name: 'Mozambique', code: 'MZ' }, { name: 'Myanmar', code: 'MM' }, { name: 'Namibia', code: 'NA' }, { name: 'Nauru', code: 'NR' }, { name: 'Nepal', code: 'NP' }, { name: 'Netherlands', code: 'NL' }, { name: 'Netherlands Antilles', code: 'AN' }, { name: 'New Caledonia', code: 'NC' }, { name: 'New Zealand', code: 'NZ' }, { name: 'Nicaragua', code: 'NI' }, { name: 'Niger', code: 'NE' }, { name: 'Nigeria', code: 'NG' }, { name: 'Niue', code: 'NU' }, { name: 'Norfolk Island', code: 'NF' }, { name: 'Northern Mariana Islands', code: 'MP' }, { name: 'Norway', code: 'NO' }, { name: 'Oman', code: 'OM' }, { name: 'Pakistan', code: 'PK' }, { name: 'Palau', code: 'PW' }, { name: 'Palestinian Territory, Occupied', code: 'PS' }, { name: 'Panama', code: 'PA' }, { name: 'Papua New Guinea', code: 'PG' }, { name: 'Paraguay', code: 'PY' }, { name: 'Peru', code: 'PE' }, { name: 'Philippines', code: 'PH' }, { name: 'Pitcairn', code: 'PN' }, { name: 'Poland', code: 'PL' }, { name: 'Portugal', code: 'PT' }, { name: 'Puerto Rico', code: 'PR' }, { name: 'Qatar', code: 'QA' }, { name: 'Reunion', code: 'RE' }, { name: 'Romania', code: 'RO' }, { name: 'Russian Federation', code: 'RU' }, { name: 'RWANDA', code: 'RW' }, { name: 'Saint Helena', code: 'SH' }, { name: 'Saint Kitts and Nevis', code: 'KN' }, { name: 'Saint Lucia', code: 'LC' }, { name: 'Saint Pierre and Miquelon', code: 'PM' }, { name: 'Saint Vincent and the Grenadines', code: 'VC' }, { name: 'Samoa', code: 'WS' }, { name: 'San Marino', code: 'SM' }, { name: 'Sao Tome and Principe', code: 'ST' }, { name: 'Saudi Arabia', code: 'SA' }, { name: 'Senegal', code: 'SN' }, { name: 'Serbia and Montenegro', code: 'CS' }, { name: 'Seychelles', code: 'SC' }, { name: 'Sierra Leone', code: 'SL' }, { name: 'Singapore', code: 'SG' }, { name: 'Slovakia', code: 'SK' }, { name: 'Slovenia', code: 'SI' }, { name: 'Solomon Islands', code: 'SB' }, { name: 'Somalia', code: 'SO' }, { name: 'South Africa', code: 'ZA' }, { name: 'South Georgia and the South Sandwich Islands', code: 'GS' }, { name: 'Spain', code: 'ES' }, { name: 'Sri Lanka', code: 'LK' }, { name: 'Sudan', code: 'SD' }, { name: 'Suriname', code: 'SR' }, { name: 'Svalbard and Jan Mayen', code: 'SJ' }, { name: 'Swaziland', code: 'SZ' }, { name: 'Sweden', code: 'SE' }, { name: 'Switzerland', code: 'CH' }, { name: 'Syrian Arab Republic', code: 'SY' }, { name: 'Taiwan, Province of China', code: 'TW' }, { name: 'Tajikistan', code: 'TJ' }, { name: 'Tanzania, United Republic of', code: 'TZ' }, { name: 'Thailand', code: 'TH' }, { name: 'Timor-Leste', code: 'TL' }, { name: 'Togo', code: 'TG' }, { name: 'Tokelau', code: 'TK' }, { name: 'Tonga', code: 'TO' }, { name: 'Trinidad and Tobago', code: 'TT' }, { name: 'Tunisia', code: 'TN' }, { name: 'Turkey', code: 'TR' }, { name: 'Turkmenistan', code: 'TM' }, { name: 'Turks and Caicos Islands', code: 'TC' }, { name: 'Tuvalu', code: 'TV' }, { name: 'Uganda', code: 'UG' }, { name: 'Ukraine', code: 'UA' }, { name: 'United Arab Emirates', code: 'AE' }, { name: 'United Kingdom', code: 'GB' }, { name: 'United States', code: 'US' }, { name: 'United States Minor Outlying Islands', code: 'UM' }, { name: 'Uruguay', code: 'UY' }, { name: 'Uzbekistan', code: 'UZ' }, { name: 'Vanuatu', code: 'VU' }, { name: 'Venezuela', code: 'VE' }, { name: 'Viet Nam', code: 'VN' }, { name: 'Virgin Islands, British', code: 'VG' }, { name: 'Virgin Islands, U.S.', code: 'VI' }, { name: 'Wallis and Futuna', code: 'WF' }, { name: 'Western Sahara', code: 'EH' }, { name: 'Yemen', code: 'YE' }, { name: 'Zambia', code: 'ZM' }, { name: 'Zimbabwe', code: 'ZW' }]
    };

    const PrivateWindow = new Promise(function (resolve, reject) {
        try {
            if (navigator.userAgent.includes("Firefox")) {
                //Firefox
                var db = indexedDB.open("test");
                db.onerror = function () { resolve(true); };
                db.onsuccess = function () { resolve(false); };
            } else {
                resolve(null);
            }
        }
        catch (err) {
            console.log(err);
            resolve(null);
        }
    });

    const isPrivateWindow = function (callback) {
        PrivateWindow.then(function (is_private) {
            callback(is_private);
        });
    };

    const setCookie = function (cname, cvalue, exdays) {
        let d = new Date();

        exdays === undefined ? exdays = 30 : exdays;
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));

        let expires = "expires=" + d.toGMTString();

        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    };

    const deleteCookie = function (name) {
        document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    };

    const getCookie = function (name) {
        let value = "; " + document.cookie,
            parts = value.split("; " + name + "=");

        if (parts.length == 2) return parts.pop().split(";").shift();
    };

    const timestamp = function () {
        const currentTime = performance.now()

        return currentTime;
    };

    const hasInArray = function (arr, obj) {
        return (arr.indexOf(obj) != -1);
    };

    const getFromString = function (str, regexp, isLastChar) {
        let arr = str.match(regexp);

        if (arr != null) { // Did it match?
            return isLastChar === true ? arr[1].replace(/\/$/, "") : arr[1];
        } else {
            return false;
        }
    };

    const encodeObj = function (data) {
        const state = encodeURI(JSON.stringify(data));

        return state;
    };

    const detectQueryString = function() {
        const currentQueryString = window.location.search;

        if (currentQueryString) {
            return currentQueryString;
        } else {
          return false;
        }
    };

    const scrollToElement = function (element, duration, headerHeight) {
        const getElementY = function (query) {
            return window.pageYOffset + document.querySelector(query).getBoundingClientRect().top
        };

        let startingY = window.pageYOffset,
            elementY = getElementY(element);
            targetY = document.body.scrollHeight - elementY < window.innerHeight ? document.body.scrollHeight - window.innerHeight : elementY,
            customHeight = headerHeight !== undefined ? headerHeight : 74,
            diff = targetY - startingY - customHeight;

        // Easing function: easeInOutCubic
        let easing = function (t) { return t < .5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1 },
            start = "";

        if (!diff) return

        // Bootstrap our animation - it will get called right before next frame shall be rendered.
        window.requestAnimationFrame(function step(timestamp) {
            if (!start) start = timestamp
            // Elapsed miliseconds since start of scrolling.
            let time = timestamp - start
            // Get percent of completion in range [0, 1].
            let percent = Math.min(time / duration, 1)
            // Apply the easing.
            percent = easing(percent)

            window.scrollTo(0, startingY + diff * percent)

            // Proceed with animation as long as we wanted it to.
            if (time < duration) {
                window.requestAnimationFrame(step)
            }
        })
    };

    const removeTagsFromString = function(str) {
        return str.replace(/(<([^>]+)>)/ig, "") 
    };

    const cleanTextAndTruncate = function(htmlString, maxLength) {
        // Remove tags by replacing them with a space and then trim the result
        let textWithoutTags = htmlString.replace(/<\/[^>]+>/gi, ' ').replace(/<[^>]+>/gi, '').trim();
        
        // Replace multiple spaces with a single space
        textWithoutTags = textWithoutTags.replace(/\s\s+/g, ' ');
      
        // Truncate to the maxLength if necessary and add ellipsis
        if (textWithoutTags.length > maxLength) {
          // Ensure we don't break in the middle of a word
          // Find the last space before the maxLength
          const lastSpaceIndex = textWithoutTags.lastIndexOf(' ', maxLength - 1);
          // Use the lastSpaceIndex to truncate the string, and add an ellipsis
          return textWithoutTags.substring(0, lastSpaceIndex) + '...';
        }
      
        // Return the text if it's less than the maxLength
        return textWithoutTags;
    } 

    const findInArray = function(array, value) {
        const found = array.find(element => element === value);

        if (found !== undefined) {
            return true
        } else {
            return false
        }
    };

    return {
        config: config,
        findObjectByKey: findObjectByKey,
        heightOfEle: heightOfEle,
        assignVValidationObj: assignVValidationObj,
        removeObjInArr: removeObjInArr,
        formatDate: formatDate,
        dateTimeToArray: dateTimeToArray,
        timeConvert: timeConvert,
        getQueryParameter: getQueryParameter,
        countriesData: countriesData,
        isPrivateWindow: isPrivateWindow,
        setCookie: setCookie,
        deleteCookie: deleteCookie,
        getCookie: getCookie,
        timestamp: timestamp,
        removeValInArr: removeValInArr,
        hasInArray: hasInArray,
        getFromString: getFromString,
        encodeObj: encodeObj,
        detectQueryString: detectQueryString,
        scrollToElement: scrollToElement,
        removeTagsFromString: removeTagsFromString,
        findInArray: findInArray,
        queryParameterNonWindow: queryParameterNonWindow,
        cleanTextAndTruncate: cleanTextAndTruncate
    };
})(jQuery);

const YUNOStore = (function($) {

    const updateModule = function(moduleObj, options, data, isSuccess, isPush) {

        if (isSuccess) {
            if (options.addToModule === undefined || options.addToModule) {

                if (isPush !== undefined && isPush) {
                    for (let i = 0; i < data.length; i++) {
                        moduleObj.data.push(data[i]);
                    };

                    if (options.hasLoadmore) {
                        moduleObj.count = options.response.data.count;
                        moduleObj.currentCount = moduleObj.data.length;
                        moduleObj.offset = moduleObj.currentCount;
                    };
    
                } else {
                    moduleObj.data = data;
                }
            }
        } else {
            if (options.moduleTabs) {
                moduleObj.error = data;
                moduleObj.loading = false;
            } else {
                moduleObj.error = data;
            }


        };

        moduleObj.success = true;
        moduleObj.loading = false;
    };                                                                                                                                                                                          

    /*
        @method: manageLoading()
        @description: Every batch object has a property call "isLoading". 
            When user initiate payment on particular batch"isLoading" state is being update with this method.
    */
    const manageLoading = function(options, forInstructor, courseBatches) {
        if (options.pageType === "instructorBatch") {
            if (forInstructor !== undefined) forInstructor.isLoading = false;
        } else {
            let currentBatch = courseBatches.filter(function(el) {
                return el.type === options.filterType;
            });

            currentBatch[0].data[options.batchIndex].isLoading = false;
        }
    };

    const init = function() {
        const store = new Vuex.Store({
            state: {
                pageLoader: false,
                loader: {
                    overlay: false,
                    isActive: false
                },
                themeURL: themeURL,
                homeURL: homePage,
                config: {
                    signInURL: "",
                    yunoAPIToken: typeof yunoAPIToken !== 'undefined' ? yunoAPIToken : "",
                    unauthorizedModal: false,
                    vimeoToken: "Bearer 4c4b3e1ac1851a23047dd7a338f5caee",
                    vimeoVideoToken: "Bearer 878869c3fe96f7ec679b9455c539ee77",
                    vimeoVideoEditToken: "Bearer 4c4b3e1ac1851a23047dd7a338f5caee",
                    googleAPIKey: "AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",
                    googleMapAPIKey: "AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",
                    googleMapLocationAPIKey: "AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI",
                },
                user: {
                    isLoggedin: false,
                    userID: isLoggedIn
                },
                userRole: {
                    loading: false,
                    error: null,
                    success: false,
                    data: [],
                    response: []
                },
                userProfile: {
                    loading: false,
                    error: null,
                    success: false,
                    data: []
                },
                header: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                footer: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                blogList: {
                    loading: false,
                    error: null,
                    success: false,
                    data: []
                },
                courseListIELTS: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                allCourseListIELTS: {
                    title: "All our courses are delivered by expert IELTS trainers",
                    demoPageURL: "/demo-classes",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: {
                        single: [],
                        multiple: []
                    }
                },
                resultsIELTS: {
                    title: "Our students' results",
                    isLoadMore: false,
                    count: "",
                    currentCount: "",
                    limit: 8,
                    offset: 0,
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                faqIELTS: {
                    title: "FAQs",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                recordedClasses: {
                    title: "Checkout actual classes that we recorded",
                    videos: [
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",
                            caption: "Subject Verb Agreement Class - Learn IELTS - Yuno Learning"
                        },
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",
                            caption: "IELTS Writing Task 2 Tips"
                        },
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",
                            caption: "How to Paraphrase - Learn IELTS - Yuno Learning"
                        }
                    ]
                },
                course: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0
                },
                courseV2: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0
                },
                schedule: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                courseBatches: {
                    title: "",
                    coursesCount: "",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 4,
                    offset: 0
                },
                courseBatchesFilters: {
                    currentCourse: "",
                    tabs: [
                        {
                            title: "Any Time",
                            type: "anytime",
                            isActive: true,
                            data: []
                        },
                        {
                            title: "Morning",
                            type: "morning",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Afternoon",
                            type: "afternoon",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Evening",
                            type: "evening",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Night",
                            type: "night",
                            isActive: false,
                            data: []
                        }
                    ]
                },
                courseTabs: [],
                instructor: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    tabs: []
                },
                learnerCourses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: [
                        {
                            title: "My Courses",
                            tab: "Upcoming and Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "batchCard",
                            errorMsg: "You have not enrolled any course yet",
                            isExploreCTA: true,
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            manageState: true

                        },
                        {
                            title: "My Courses",
                            tab: "Past",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "batchCard",
                            errorMsg: "You do not have any past course yet",
                            isExploreCTA: true,
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            manageState: true
                        }
                    ]
                },
                instructorCourses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                instructorBasicDetails: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    isLoading: false,
                    isUpdateDetail: false,
                    fluentInSelected: [],
                    understandSelected: [],
                    payload: {
                        user_id: "",
                        flat_house_number: "",
                        street: "",
                        landmark: "",
                        pin_code: "",
                        country: "",
                        state: "",
                        city: "",
                        experience: "",
                        fluent_in: [],
                        understand: [],
                        is_about: false
                    }
                },
                instructorAbout: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    isLoading: false,
                    isAbout: false,
                    payload: {
                        user_id: "",
                        is_about: true,
                        about: ""
                    }
                },
                instructorDemoClasses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 2,
                    offset: 0,
                    isLoadMore: false,
                    data: []
                },
                instructorMyCourses: {
                    title: "My Courses",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                homeCategories: {
                    title: "Top Courses on Yuno",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                homeCarouselList: {
                    title: "Featured Courses",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorslList: {
                    title: "Meet Our Expert Instructors",
                    description: "",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                whyLearn: {
                    title: "Why Choose Yuno for Your Learning Journey",
                    list: [
                        {
                            title: "Online classes with personalized attention",
                            description: "All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",
                            icon: "portrait",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Instructors who care for your success",
                            description: "Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",
                            icon: "emoji_events",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Best instructors but affordable pricing",
                            description: "All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",
                            icon: "account_balance_wallet",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Really smooth experience of technology",
                            description: "We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",
                            icon: "code",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Your counsellor is just a call away",
                            description: "You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",
                            icon: "call",
                            iconType: "material-icons"
                        }
                    ]
                },
                homeHero: {
                    list: [
                        {
                            title: "Coding classes don’t have to cost so much. Try Yuno’s affordable classes",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/coding-for-kids",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-kids.jpg"
                        },
                        {
                            title: "Fluency builds confidence among children. Try our English speaking classes",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/english-speaking",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-English-Speaking.jpg"
                        },
                        {
                            title: "Communicate. Succeed. English speaking classes for working professionals",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/english-speaking",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-professional.jpg"
                        }
                    ]
                    
                },
                chooseType: {
                    title: "English Speaking Online Classes",
                    subTitle: "Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",
                    data: [],
                    loading: false,
                    error: null,
                    success: false,
                    list: [
                        {
                            title: "Working Professionals",
                            type: "professionals",
                            age: "22+",
                            description: "We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                },
                                {
                                    monthly: "2900",
                                    perClass: "242",
                                    level: "Advanced"
                                }
                            ]
                        },
                        {
                            title: "Students",
                            type: "students",
                            age: "15 - 22",
                            description: "Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        },
                        {
                            title: "Kids",
                            type: "kids",
                            age: "8 to 14",
                            description: "There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        },
                        {
                            title: "Homemakers",
                            type: "homemakers",
                            age: "25 to 55",
                            description: "You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        }
                    ]                    
                },
                meetInstructor: {
                    title: "Instructors who really care",
                    description: "You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                featuredTestimonials: {
                    title: "So immersive that you’d want more classes",
                    description: "We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",
                    footer: {
                        title: "Affordable pricing",
                        description: "So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."
                    }
                },
                spokenEnglishContentBlock: {
                    title: "You don’t only get to practise, but you acquire English language skills for life",
                    description: "The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",
                    img: ""+ themeURL +"/assets/images/languages.svg"
                },
                kidsHero: {
                    description: "I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",
                    img: ""+ themeURL +"/assets/images/codingForKids.svg",
                    author: {
                        name: "Elon Musk",
                        img: ""+ themeURL +"/assets/images/Elon-Musk.jpg",
                        about: "Elon Musk, Founder of Tesla and SpaceX"
                    }
                },
                kidsOfferBanner: {
                    title: "Introducing programming foundation course for 7 - 16 years old",
                    img: ""+ themeURL +"/assets/images/offerPrice.svg",
                    productURL: "/course/learn-to-code-with-mit-scratch",
                    list: [
                        "We make learning to code fun for your child",
                        "24 hours of live classes with homework assignments",
                        "3 classes per week",
                        "Rs. 349 per class",
                        "All classes by expert computer science instructors",
                        "Industry standard curriculum designed by MIT, USA",
                        "Certificate on successful completion"
                    ]
                },
                kidsTestimonials: {
                    title: "What parents have to say"
                },
                kidsHighlights: {
                    title: "Highlights",
                    call: "Call us at <span>+91 62390 91798<\/span>",
                    list: [
                        {
                            type: "liveClass",
                            name: "Live Class",
                            label: "21 hours of live classes"
                        },
                        {
                            type: "oneToOne",
                            name: "One to One",
                            label: "one-to-one doubt clearing sessions"
                        },
                        {
                            type: "price",
                            name: "Price",
                            label: "&#8377;349 per class"
                        },
                        {
                            type: "game",
                            name: "Game",
                            label: "Game development by students"
                        },
                        {
                            type: "programming",
                            name: "Programming",
                            label: "Programming fundamentals"
                        },
                        {
                            type: "flexible",
                            name: "Flexible",
                            label: "Flexible timings with multiple batches"
                        },
                        {
                            type: "certificate",
                            name: "Certificate",
                            label: "Certificate of completion"
                        },
                        {
                            type: "demo",
                            name: "Demo",
                            label: "Free Demo Class"
                        },
                        {
                            type: "cv",
                            name: "CV",
                            label: "Curriculum by MIT, USA"
                        }
                    ]
                },
                gamesShowcase: {
                    title: "Game Developed by Students",
                    list: [
                        {
                            title: "Pop The Balloon Game",
                            img: ""+ themeURL +"/assets/images/PopUPthebaloon.png",
                            url: "https://scratch.mit.edu/projects/419275974/"
                        },
                        {
                            title: "Apple Catcher",
                            img: ""+ themeURL +"/assets/images/Applecatcher.png",
                            url: "https://scratch.mit.edu/projects/423139061/"
                        },
                        {
                            title: "Killing Zombies",
                            img: ""+ themeURL +"/assets/images/Kill-Zombies.png",
                            url: "https://scratch.mit.edu/projects/425774405/"
                        },
                        {
                            title: "Pac-man",
                            img: ""+ themeURL +"/assets/images/PACMAN.png",
                            url: "https://scratch.mit.edu/projects/429660245/"
                        }
                    ]
                },
                ieltsReviews: {
                    title: "What our students say",
                    description: "",
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                ourInstructors: {
                    title: "Our Instructors",
                    description: "You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                curriculum: {
                    title: "Best-in-class curriculum by MIT, USA",
                    description: "The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",
                    link: {
                        label: "See curriculum",
                        url: "/course/learn-to-code-with-mit-scratch"
                    },
                    img: ""+ themeURL +"/assets/images/scratch.svg",
                    author: {
                        name: "Mitchel Resnik",
                        about: "PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",
                        img: ""+ themeURL +"/assets/images/MitchellResnickThumb.jpg",
                        link: {
                            label: "Watch Mitchel Resnik’s Video on TED",
                            url: "https://www.youtube.com/watch?v=Ok6LbV6bqaE"
                        }
                    }
                },
                signUpForm: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                loginWithGoogle: {
                    isLoading: false,
                    data: [],
                    payload: {
                        State: loginState    
                    }
                },
                isUserSignUp: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                enrollment: {
                    isLoading: false,
                    isCourseEnrolled: false,
                    data: [],
                    error: null,
                    errorData: [],
                    success: {
                        username: "",
                        productTitle: "",
                        amount: "",
                        message: "You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"
                    },
                    payload: {
                        id: "",
                        receipt: "",
                        user_id: "",
                        course_id: "",
                        batch_id: "",
                        batch_name: "",
                        batch_end_date: "",
                        payment_gateway: "razorpay",
                        payment_mode: "",
                        amount: "",
                        amount_due: "",
                        total_instalments: 0,
                        instalment_amount: 0,
                        duration: "",
                        status: "",
                        description: "",
                        self_notes: "",
                        currency: "INR",
                        counselor_id: 0,
                        short_url: "",
                        zoho_product_id: ""
                    }
                },
                paymentDismiss: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classSchedule: {
                    isLoading: false,
                    modal: false,
                    successModal: false,
                    data: [],
                    error: null,
                    errorData: [],
                    currentLearner: "",
                    date: typeof moment !== 'undefined' ? new Date(moment(new Date).add(5, 'minutes').utc()) : "",
                    time: typeof moment !== 'undefined' ? new Date(moment(new Date).add(5, 'minutes').utc()) : "",
                    learnerSelected: "",
                    relatedCourses: "",
                    payload: {
                        ClassTitle: "",
                        ClassDescription: "",
                        ClassDate: "",
                        ClassTime: "",
                        ClassDuration: "",
                        classSchedule: "",
                        learner: [],
                        RelatedCourses: "",
                        BatchID: "",
                        academy_id: "",
                    }
                },
                classEdit: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                classDelete: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                classTitle: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                addClassTitle: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: {
                        instructor_id: "",
                        title: ""
                    }
                },
                myLearners: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learner: {
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Schedule",
                            tab: "Upcoming & Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any upcoming and ongoing class",
                            isExploreCTA: false,
                            emptyStateCTA: {
                                ctaLabel: "See past classes & recordings",
                                tab: "yunoPast"
                            },
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true
                        },
                        {
                            title: "My Schedule",
                            tab: "Past Classes & Recordings",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any past class",
                            isExploreCTA: false,
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true,
                            emptyStateCTA: false
                        }
                    ]
                },
                instructorHome: {
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Schedule",
                            tab: "Upcoming & Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            createAction: "Schedule New Class",
                            createURL: "/class-schedule",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any upcoming and ongoing class",
                            isExploreCTA: false,
                            manageState: true,
                            emptyStateCTA: false,
                            count: "",
                            currentCount: "",
                            isLoadMore: false,
                            limit: 20,
                            offset: 0,
                            hasLoadMore: true
                        },
                        {
                            title: "My Schedule",
                            tab: "Past Classes & Recordings",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            createAction: "Schedule New Class",
                            createURL: "/class-schedule",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any past class",
                            isExploreCTA: false,
                            manageState: true,
                            emptyStateCTA: false,
                            count: "",
                            currentCount: "",
                            isLoadMore: false,
                            limit: 20,
                            offset: 0,
                            hasLoadMore: true
                        }
                    ]
                },
                instructorLearners: {
                    scheduleClass: {
                        modal: false,
                        data: []
                    },
                    updateTitle: {
                        modal: false,
                        modalData: null,
                        isLoading: false,
                        payload: {
                            title: "",
                            group_id: ""
                        }
                    },
                    addLearner: {
                        modal: false,
                        modalData: null,
                        isLoading: false,
                        selectedLearner: "",
                        deleteUser: [],
                        newAddedUser: [],
                        payload: {
                            add_user_ids: [],
                            delete_user_ids: [],
                            group_id: ""
                        }
                    },
                    newGroupModal: {
                        modal: false,
                        learners: [],
                        selectedLearner: [],
                        isLoading: false,
                        data: [],
                        payload: {
                            title: "",
                            owner_id: "",
                            role: "",
                            access: "rw",
                            user_ids: []
                        }
                    },
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Learners",
                            tab: "All",
                            url: "yunoAllLearners",
                            isActive: false,
                            hasData: false,
                            type: "tableGrid-2",
                            defaultSort: "name",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don't have any learner",
                            defaultFilters: [
                                {
                                    type: "viewBy",
                                    val: "all"
                                }
                            ],
                            appliedFilters: [],
                            filters: [
                                {
                                    selected: "By Learner Type",
                                    default: "By Learner Type",
                                    type: "viewBy",
                                    module: "dropdown",
                                    isActive: false,
                                    items: [
                                        {
                                            label: "All",
                                            val: "all",
                                            default: "all"
                                        },
                                        {
                                            label: "My Contacts",
                                            val: "myContacts",
                                            default: "all"
                                        },
                                        {
                                            label: "My Referrals",
                                            val: "myReferrals",
                                            default: "all"
                                        }
                                    ]
                                },
                            ],
                            tableOptions: {
                                isFluid: true,
                                pageLoading: false,
                                apiPaginated: true,
                                totalResult: "",
                                perPage: 20,
                                currentPage: 1,
                                limit: 100,
                                offset: 0
                            },
                            manageState: true
                        },
                        {
                            title: "My Groups",
                            tab: "Groups",
                            url: "yunoGroups",
                            isActive: false,
                            hasData: false,
                            type: "groupCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don't have any group",
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true
                        }
                    ]
                },
                allLearners: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classDetail: {
                    classType: "",
                    data: [],
                    tabs: [],
                    loading: false,
                    error: null,
                    errorMsg: "",
                    errorData: [],
                    success: false
                },
                demoClassEnroll: {
                    isLoading: false,
                    modal: false,
                    successModal: false,
                    error: null,
                    errorData: [],
                    data: [],
                    payload: {
                        class_id: "",
                        instructor_id: "",
                        user_id: "",
                        start_date: "",
                        end_date: "",
                        class_title: "",
                        class_description: ""
                    }
                },
                paymentLink: {
                    isLoading: false,
                    data: [],
                    successModal: false,
                    form: {
                        amount: "",
                        selectedUser: "",
                        user: "",
                        selectedBatch: "",
                        batchID: "",
                        batch: "",
                        courseID: "",
                        course: "",
                        paymentType: "",
                        noteForSelf: "",
                        isInstallment: false,
                        installments: "",
                        isNextSlide: false,
                        howManyInstallments: ["2","3"]
                    },
                    payload: {
                        customer_name: "",
                        customer_email: "",
                        customer_contact: "",
                        type: "link",
                        view_less: 1,
                        amount: "",
                        currency: "INR",
                        payment_description: "",
                        receipt: "",
                        partial_payment: 0
                    }
                },
                crmContacts: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allCourses: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allBatches: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                enrollmentStatus: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createPayment: {
                    data: [],
                    payload: {
                        id: "",
                        receipt: "",
                        Order_id: "",
                        customer_id: "",
                        user_id: "",
                        course_id: "",
                        batch_id: "",
                        batch_name: "",
                        batch_end_date: "",
                        payment_gateway: "razorPay",
                        payment_mode: "generate_link",
                        amount: "",
                        amount_due: "",
                        total_instalments: "",
                        instalment_amount: "",
                        duration: "",
                        status: "",
                        description: "",
                        self_notes: "",
                        currency: "INR",
                        entity: "invoice",
                        counselor_id: "",
                        short_url: "",
                        org_id: "",
                        org_user_id: "",
                        org_user_phone: "",
                        org_user_name: "",
                        org_user_email: ""   
                    }
                },
                reviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                updatePaymentLink: {
                    data: [],
                    successModal: false,
                    payload: {
                        id: ""
                    }
                },
                updateLink: {
                    data: [],
                    error: null,
                    errorData: [],
                    loading: false,
                    success: false,
                    successModal: false,
                    payload: {
                        receipt_id: "",
                        razerpay_invoice_id: "",
                        short_url: ""
                    }
                },
                instructorList: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                profileReviews: {
                    title: "Reviews"
                },
                reviewsByType: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                demoClasses: {
                    loading: false,
                    error: null,
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0,
                    isLoadMore: false,
                    data: [],
                    filters: [
                        {
                            title: "Any Time",
                            type: "anytime",
                            isActive: true,
                            data: []
                        },
                        {
                            title: "Morning",
                            type: "morning",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Afternoon",
                            type: "afternoon",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Evening",
                            type: "evening",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Night",
                            type: "night",
                            isActive: false,
                            data: []
                        }
                    ]
                },
                manageEnroll: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                changeBatch: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                blogs: {
                    data: [],
                    headers: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0,
                    isLoadMore: false
                },
                blogDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                postCategories: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                anilLambaHero: {
                    list: [
                        {
                            title: "Anil Lamba on finance",
                            img: ""+ themeURL +"/assets/images/hero-anilLamba.png"
                        }
                    ]
                    
                },
                settings: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                notifications: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                counsellorList: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    success: false
                },
                pageDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                googleContacts: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                participants: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: {
                        login_email: "",
                        first_name: "",
                        last_name: "",
                        user_id: ""
                    }
                },
                mapCourses: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                relatedCourses: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                categoryList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    selected: ""
                },
                categoryTaxonomy: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createEBook: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                publishedEBooks: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                deleteEBookAttachment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                eBookEmail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createResource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                publishedResources: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    manageState: false,
                    defaultFilters: [],
                    appliedFilters: [],
                    filters: []
                },
                deleteResourceAttachment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                resourceEmail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                videoList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                userInfo: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                paymentLinkList: {
                    data: [],
                    changeBatch: "",
                    paymentDetail: "",
                    createPayment: "",
                    generateLink: "",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                instructorInsights: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                instructorStats: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                goalsAchieved: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerRating: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                reviewVariations: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                completedEnrollments: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classDelivered: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                activeEnrollment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                qtRating: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                topIssuesCited: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorReviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                ratingBreakdown: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                countries: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                states: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                cities: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                languages: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                mappedInstructor: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                batchCreateUpdate: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                batchDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                timeSlots: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorAvailability: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorAvailabilityGrid: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorsByCategory: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                capabilities: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                cancelPaymentLink: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                inviteLink: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                invitedByUser: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                updateUserCategory: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                apiTokenExpiryTime: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                apiTokenRefresh: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerProfileHeader: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                staticPage: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerProfile: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                resources: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                resource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 15,
                    offset: 0,
                },
                learnerInsightsClass: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0,
                },
                videoTestimonials: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                ieltsResults: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 10,
                    offset: 0,
                },
                deleteResource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                eventDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                moduleWithoutTab: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    limit: 20,
                    offset: 0,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                },
                moduleWithLoadMore: {
                    data: [],
                    other: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 3,
                    offset: 0,
                },
                signupLastStep: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                subjectsList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                filters: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    filters: null
                },
                filterResult: {
                    data: [],
                    additional: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    currentPage: 1,
                    isSidebar: false,
                    limit: 20,
                    offset: 0,
                    payload: [],
                    modal: {
                        isActive: false,
                        data: []
                    },
                    tabs: [],
                    refreshTable: false
                },
                enrollmentV2: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allReviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                enableDisableInstructor: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                module: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                drawer: {
                    data: [],
                    isActive: false,
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                form: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: [],
                    fields: []
                },
                subform: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                subform2: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                subform3: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                orgAdmin: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                referralCode: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                generateCode: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                searchSuggestions: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                chooseAccountTypeModal: {
                    modal: false,
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                tabs: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
            },
            mutations: {
                gotData(state, options) {
                    if (options.isError) {
                        let getData = options.response.response,
                            isPush = options.pushData !== undefined && options.pushData ? true : false,
                            module = state[options.store],
                            errorMsg = "";

                        if (getData !== undefined) {
                            errorMsg = getData.data !== undefined && getData.data.message !== undefined ? getData.data.message : YUNOCommon.config.errorMsg.common;
                        } else {
                            errorMsg = YUNOCommon.config.errorMsg.common;
                        }
                        
                        console.log(errorMsg)
                        console.log(options.store)

                        if (options.tabs) {
                            updateModule(module.tabs[options.tabIndex], options, getData, false, isPush);
                            module.tabs[options.tabIndex].error = true;
                            module.tabs[options.tabIndex].errorData = errorMsg;
                            if (options.callback) { options.callbackFunc(module.tabs[options.tabIndex].errorData)};
                        } else {
                            updateModule(module, options, getData, false, isPush);
                            module.error = true;
                            module.errorData = errorMsg;
                            if (options.callback) { options.callbackFunc(module.errorData)};
                        }
                    } else {
                        let getData = options.response.data.data,
                            isPush = options.pushData !== undefined && options.pushData ? true : false,
                            hasError = options.response.data,
                            module = state[options.store];

                        if (options.tabs) {
                            if (hasError.code === 204) {
                                module.tabs[options.tabIndex].error = true;
                                module.tabs[options.tabIndex].errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };

                            if (hasError.code === 401) {
                                module.tabs[options.tabIndex].error = true;
                                module.tabs[options.tabIndex].errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };

                            updateModule(module.tabs[options.tabIndex], options, getData, true, isPush);
                            
                        } else {
                            if (hasError.code === 204) {
                                module.error = true;
                                module.errorData = hasError.message;
                                console.log(hasError.message)
                                console.log(options.store)
                            }

                            if (hasError.code === 401) {
                                module.error = true;
                                module.errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };
                                
                            updateModule(module, options, getData, true, isPush);
                        }
                        
                        if (options.callback) { options.callbackFunc(options)};
                    }
                },
                thirdParty(state, options) {
                    module = state[options.store];

                    if (module.error) {
                        options.callbackFunc(module)
                    } else {
                        options.callbackFunc(options)
                    }
                },
                mapCourses(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        if (options.overrideData === undefined || options.overrideData) {
                            updateModule(state[options.module], options, getData, false);
                        }
                        
                        if (options.callback) { options.callbackFunc(state[options.module].errorData)};
                    } else {
                        let getData = options.response.data.data;

                        if (options.overrideData === undefined || options.overrideData) {
                            updateModule(state[options.module], options, getData, true);    
                        }
                        
                        if (options.callback) { options.callbackFunc(options)};
                    }
                },
                reviewsByType(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        updateModule(state[options.module], options, getData, false);
                    } else {
                        let getData = options.response.data.data;

                        if (!options.isTabAdded) {
                            state.instructor.data.tabs.push(
                                {
                                    tab: "Reviews",
                                    url: "yunoFeaturedTestimonials"
                                }
                            );    
                        }

                        updateModule(state[options.module], options, getData, true);

                        options.isTabAdded = true;

                        setTimeout(() => { 
                            options.componentInstance.$refs.testimonialWrapper.initSlider();
                        }, 30);
                    }
                },
                crmContacts(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        updateModule(state[options.module], options, getData, false);
                    } else {
                        let getData = options.response.data.data;

                        for (var i = 0; i < getData.length; i++) {
                            getData[i].username_email_phone = `${getData[i].username_email} (${getData[i].phone})`;
                        };

                        updateModule(state[options.module], options, getData, true);
                    }
                },
                classDelete(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `${YUNOCommon.config.errorMsg.common}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        })
                    } else {
                        state.loader.isActive = false;
                        state.loader.overlay = false;

                        if (options.classID !== undefined) {
                            let getClasses = state.instructorHome.tabs[0].data;

                            YUNOCommon.removeObjInArr(getClasses, "id", options.classID); 

                            if (getClasses.length === 0) {
                                state.instructorHome.tabs[0].error = true;
                            };

                            options.componentInstance.$buefy.toast.open({
                                duration: 5000,
                                message: `Class successfully deleted`,
                                position: 'is-bottom'
                            });
                        } else {
                            options.componentInstance.$buefy.dialog.alert({
                                title: 'Delete',
                                message: 'Class successfully deleted',
                                confirmText: 'Ok',
                                type: 'is-danger',
                                onConfirm: () => window.location.href = YUNOCommon.config.host() + "/instructor"
                            });
                        };

                        state[options.module].data = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        
                    }
                },
                demoClassEnroll(state, options) {
                    if (options.isError) {
                        state[options.module].isLoading = false;

                        if (options.classIndex !== undefined) {
                            state.loader.isActive = false;
                            state.loader.overlay = false;
                        }

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `${YUNOCommon.config.errorMsg.common}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        })
                        
                    } else {
                        localStorage.removeItem('demoClassState');
                        state[options.module].isLoading = false;
                        state[options.module].data = options.response.data.data;

                        //Instructor Profile
                        if (options.classIndex !== undefined) {
                            let getModule = state[options.parentModule].data[options.classIndex];

                            if (getModule !== undefined) {
                                getModule.isLoading = false    
                                getModule.is_enrolled = true;
                            }

                            state.loader.isActive = false;
                            state.loader.overlay = false;
                        } else {
                            state[options.parentModule].data.is_enrolled = true;
                        }

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `You have successfully enrolled`,
                            position: 'is-bottom'
                        })

                        const isUserSignUp = localStorage.getItem('userSignUp');

                        if (isUserSignUp !== null && isUserSignUp === "pending") {
                            localStorage.setItem('oldUserState', "/learner/");
                            localStorage.setItem('userState', "/sign-up");
                            window.location.href = YUNOCommon.config.host() + "/sign-up";    
                        }
                    }
                },
                userRole(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;    
                    } else {
                        state[options.module].data = options.response.data.data;
                        state[options.module].response = options.response.data;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        if (options.componentInstance !== "") {
                            options.componentInstance.getUserRole(options.response.data.data);   
                        }
                    }
                },
                userProfile(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;
                    } else {
                        state[options.module].data = options.response.data.data;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        if (options.instance !== "" && typeof options.instance.gotUserProfile !== 'undefined') {
                            options.instance.gotUserProfile(options.response.data.data);   
                        }
                    }
                },
                classDetail(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        if (options.nestedTab === "enrolled") {
                            state[options.module].tabs[options.tabIndex].loading = false;
                            state[options.module].tabs[options.tabIndex].pageLoading = false;
                            state[options.module].tabs[options.tabIndex].error = getData;
                        } else {
                            updateModule(state[options.module], options, getData, false);
                        }
                        
                    } else {
                        if (options.callback) { 
                            options.callbackFunc(options, options.response)
                        };
                    }
                },
                instructorMyCourses(state, options) {
                    if (options.isError) {
                        if (options.batches) {
                            let getTab = state[options.module].data[options.courseIndex].tabs[options.tabIndex];

                            getTab.error = options.response
                        } else {
                            state[options.module].error = options.response;    
                        }
                        
                        state[options.module].success = true;
                        state[options.module].loading = false; 
                    } else {
                        let getData = options.response.data.data;

                        if (options.batches) {
                            state[options.module].data[options.courseIndex].isBatches = true;

                            for (let i = 0; i < getData.length; i++) {
                                state[options.module].data[options.courseIndex].tabs[options.tabIndex].data.push(getData[i])
                            };

                            let getTab = state[options.module].data[options.courseIndex].tabs[options.tabIndex];
                            
                            getTab.count = options.response.data.count;
                            getTab.currentCount = getTab.data.length;
                            getTab.offset = getTab.currentCount;
                            getTab.isLoadMore = false;
                        } else {
                            for (let i = 0; i < getData.length; i++) {
                                getData[i].isBatches = false;
                                getData[i].isLoading = false;

                                getData[i].tabs = [
                                    {
                                        tab: "Upcoming & Ongoing",
                                        isActive: true,
                                        type: "upcomingOngoing",
                                        data: [],
                                        isLoadMore: false,
                                        count: "",
                                        currentCount: "",
                                        limit: 4,
                                        offset: 0,
                                        error: null 
                                    },
                                    {
                                        tab: "Past",
                                        isActive: false,
                                        type: "past",
                                        data: [],
                                        isLoadMore: false,
                                        count: "",
                                        currentCount: "",
                                        limit: 4,
                                        offset: 0,
                                        error: null
                                    }
                                ]
                            };
                            state[options.module].data = getData;
                        }

                        state[options.module].success = true;
                        state[options.module].loading = false;
                    }
                },
                allLearners(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false; 

                        if (options.nested !== undefined) {
                            state[options.nested].tabs[0].error = true;
                        };
                    } else {
                        let getData = options.response.data.data;

                        if (options.nested !== undefined) {
                            let getColumns = getData.columns,
                                getRows = getData.rows,
                                newCol = {
                                    field: "actions",
                                    label: "Actions",
                                    sortable: false
                                };
                            
                            if (options.userRole === "Instructor") {
                                getColumns.push(newCol);    
                            }

                            for (var i = 0; i < getRows.length; i++) {
                                getRows[i].scheduleClass = {
                                    active: true,
                                    url: "/class-schedule/?learnerID="+ getRows[i].id +""
                                };
                            }

                            state[options.nested].tabs[0].data = getData;
                            state[options.nested].tabs[0].totalResult = options.response.data.count
                            state[options.nested].tabs[0].pageLoading = false;
                        };

                        state[options.module].data = getData.rows;
                        state[options.module].success = true;
                        state[options.module].loading = false;
                    }
                },
                instructorHome(state, options) {
                    const updateData = function(isSuccess) {

                        if (isSuccess) {
                            state[options.module].tabs[options.index].hasData = true;
                            state[options.module].tabs[options.index].data = options.response.data.data;    
                        } else {
                            state[options.module].tabs[options.index].hasData = false;
                            state[options.module].tabs[options.index].data = options.response;
                            state[options.module].tabs[options.index].error = true;
                        }

                        state[options.module].success = true;
                        state[options.module].loading = false;
                        state[options.module].tabs[options.index].success = true;
                        state[options.module].tabs[options.index].loading = false;
                    }

                    if (options.isError) {
                        updateData(false);
                    } else {
                        updateData(true);
                    }
                },
                instructorLearners(state, options) {
                    const updateData = function(isSuccess) {
                        if (isSuccess && options.response.data !== undefined) {
                            let getData = options.response.data.data;

                            if (options.form !== undefined) {
                                state[options.module][options.form].data = getData;
                                state[options.module][options.form].modal = false;

                                if (options.form === "newGroupModal") {
                                    let groupObj = {
                                        date: YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(getData.group_created_time)[0]),
                                        group_created_time: getData.group_created_time,
                                        group_id: ""+getData.group_id+"",
                                        group_name: options.payload.title,
                                        total_users: 0,
                                        user: options.learners,
                                        scheduleClassURL: `/class-schedule/?groupID=${getData.group_id}`
                                    };

                                    state[options.module].tabs[options.index].data.unshift(groupObj);

                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Group successfully created`,
                                        position: 'is-bottom'
                                    });
                                };

                                if (options.form === "addLearner") {

                                    let getGroups = state[options.module].tabs[options.index].data.filter(function (el) {
                                        return el.group_id === options.group.group_id;
                                    });

                                    getGroups[0].user = options.group.user;

                                    let getModalObj = state.instructorLearners.addLearner;

                                    //Resetting data
                                    getModalObj.selectedLearner = "";
                                    getModalObj.payload.group_id = "";
                                    getModalObj.payload.owner_id = "";
                                    getModalObj.payload.user_ids = [];
                                    getModalObj.deleteUser = [];
                                    getModalObj.newAddedUser = [];
                                    getModalObj.payload.add_user_ids = [];
                                    getModalObj.payload.delete_user_ids = [];

                                    options.componentInstance.learnerAdded = true;
                                    options.componentInstance.group = null;
                                    options.componentInstance.learnersList = [];

                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Learners added successfully`,
                                        position: 'is-bottom'
                                    });   
                                }

                                if (options.form === "updateTitle") {
                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Group title updated successfully`,
                                        position: 'is-bottom'
                                    });   
                                }

                            } else {
                                const groupTab = state[options.module].tabs[options.index];

                                groupTab.hasData = true;
                                groupTab.isLoadMore = false;

                                for (var i = 0; i < getData.length; i++) {
                                    getData[i].date = YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(getData[i].group_created_time)[0]);
                                    getData[i].scheduleClassURL = `/class-schedule/?groupID=${getData[i].group_id}`;
                                    groupTab.data.push(getData[i])
                                };

                                groupTab.count = options.response.data.count;
                                groupTab.currentCount = groupTab.data.length;
                                groupTab.offset = groupTab.currentCount;
                            }

                        } else {
                            if (options.form !== undefined) {
                                state[options.module][options.form].modal = false;

                                options.componentInstance.$buefy.toast.open({
                                    duration: 5000,
                                    message: `${options.response.response.data.message}`,
                                    position: 'is-bottom',
                                    type: 'is-danger'
                                });
                            } else {
                                state[options.module].tabs[options.index].hasData = false;
                                state[options.module].tabs[options.index].data = options.response;
                                state[options.module].tabs[options.index].error = true;
                                state[options.module].tabs[options.index].errorData = options.response;
                            }
                        }

                        if (options.form !== undefined) {
                            state[options.module][options.form].isLoading = false
                        } else {
                            state[options.module].success = true;
                            state[options.module].loading = false;
                            state[options.module].tabs[options.index].success = true;
                            state[options.module].tabs[options.index].loading = false;    
                        }
                    };

                    if (options.isError) {
                        updateData(false);
                    } else {
                        updateData(true);

                        if (options.response.data.code === 204) {
                            state[options.module].tabs[options.index].error = true;
                            state[options.module].tabs[options.index].errorData = options.response.data.message;

                            console.log(options.response.data.message)
                        };
                    }
                }
            },
            actions: {
                fetchThirdPartyData({commit, state}, options) {
                    let module = state[options.store];

                    module.loading = true;

                    axios.get(options.apiURL, {
                        headers: options.headers !== undefined ? options.headers : "",
                    }).then(response => {
                        module.loading = false;
                        options.response = response;
                        module.error = null;
                        commit(options.module, options);
                    }).catch(e => {
                        module.loading = false;
                        module.errorData = e;
                        options.response = e;
                        module.error = true;
                        commit(options.module, options); 
                    })
                },
                postThirdPartyData({commit, state}, options) {
                    let module = state[options.store];
                    module.loading = true;

                    axios.defaults.timeout = options.timeout === undefined ? 0 : options.timeout; 

                    if (options.method === "post") {
                        axios.post(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "patch") {
                        axios.patch(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "head") {
                        axios.head(options.apiURL, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "put") {
                        axios.put(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else {
                        console.log("not defined")
                    }

                    
                },
                fetchData({commit, state}, options) {
                    let module = "",
                        getHeaders = "";

                    if (isLoggedIn !== "0") {
                        getHeaders = {
                            'authorization': state.config.yunoAPIToken
                        }
                    } else {
                        getHeaders = {
                            'authorization': ""
                        }    
                    };

                    if (options.store !== undefined) {
                        if (options.tabs) {
                            module = state[options.store].tabs[options.tabIndex];
                        } else {
                            module = state[options.store];
                        }
                    } else {
                        module = state[options.module];
                    }

                    if (options.moduleLoading === undefined || options.moduleLoading) {
                        module.loading = true;
                    } else {
                        module.loading = false;
                    }

                    if (options.moduleTabs && options.isTabLoader) {
                        module.loading = true;
                    };

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.get(options.apiURL, {
                        headers: getHeaders,
                    }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);

                            let hasErrorCode = options.response?.data?.data?.status ?? '';

                            if (hasErrorCode === 403) {
                                if (!state.config.unauthorizedModal) {
                                    if (options.store === "userInfo") {
                                        state.header.success = true
                                        state.footer.success = true
                                        state.capabilities.success = true
                                    }
                                    state.config.unauthorizedModal = true
                                }
                            }
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;
                            state[options.store].loading = false;
                            state[options.store].success = true;

                            let module = "";

                            if (options.store !== undefined) {
                                if (options.tabs) {
                                    module = state[options.store].tabs[options.tabIndex];
                                } else {
                                    module = state[options.store];
                                }
                            } else {
                                module = state[options.module];
                            }
                            
                            if (module.errorData !== undefined && e.response !== undefined) {
                                module.errorData = e.response
                            }
                            
                            let hasErrorCode = e.response.data.data.status;

                            if (hasErrorCode === 403) {
                                if (!state.config.unauthorizedModal) {
                                    if (options.store === "userInfo") {
                                        state.header.success = true;
                                        state.footer.success = true;
                                        state.capabilities.success = true;
                                    };
                                    state.config.unauthorizedModal = true;        
                                };
                            };

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                putData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) {
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }

                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.put(options.apiURL, options.payload, {
                        headers: getHeaders
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                awsPutData({commit, state}, options) {
                    let getHeaders = {
                        'accept': 'application/json',
                        'content-type': 'application/json',
                        'authorization': ""
                    };
                    
                    if (isLoggedIn !== "0") {
                        getHeaders.authorization = state.config.yunoAPIToken
                    } else {
                        getHeaders.authorization = "";
                    };

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.put(options.apiURL, JSON.stringify(options.payload), {
                        headers: getHeaders
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;
                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                postData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) { // Add authorization key value pair in case headers is defined
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }
                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json',
                            // 'authorization': ""
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.post(options.apiURL, options.payload, {
                        headers: getHeaders,
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                awsPostData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) {
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }
                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.post(options.apiURL, JSON.stringify(options.payload), {
                        headers: getHeaders,
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }
                            
                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                deleteData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) { // Add authorization key value pair in case headers is defined
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }

                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.delete(options.apiURL, {
                        headers: getHeaders,
                        data: options.payload
                    }).then(response => {
                        options.response = response;
                        options.isError = false;
                        commit(options.module, options);
                    })
                    .catch(e => {
                        console.log(options.store)
                        console.log(e);
                        state[options.store].error = true;
                        options.response = e;
                        options.isError = true;
                        commit(options.module, options);
                    });
                }
            },
            getters: {
                getSignInURL() {
                    const scopes = [
                        "email",
                        "profile"
                    ];
                    
                    // Old google auth URL
                    // let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state=stateUpdate&scope="+ scopes.join("%20") +"&approval_prompt=auto&include_granted_scopes=true"
                    
                    // New google auth URL
                    let url = "https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+ gRU +"&client_id="+ gCID +"&scope="+ scopes.join("%20") +"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow"
                    
                    return url;
                },
                googleMeet() {
                    const scopes = [
                        "email",
                        "profile",
                        "https://www.googleapis.com/auth/calendar",
                        "https://www.googleapis.com/auth/calendar.events",
                        "https://www.googleapis.com/auth/admin.reports.audit.readonly",
                        "https://www.googleapis.com/auth/drive.readonly"
                    ];

                    const stateObj = {
                            googleMeet: true
                        },
                        state = encodeURI(JSON.stringify(stateObj));

                    // Old URL
                    // let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&prompt=consent&include_granted_scopes=true"

                    // New URL
                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&approval_prompt=force&flowName=GeneralOAuthFlow"
                    
                    return url;
                },
                googleContacts() {
                    const scopes = [
                        "email",
                        "profile",
                        "https://www.googleapis.com/auth/contacts.readonly",
                        "https://www.googleapis.com/auth/contacts.other.readonly"
                    ];

                    const stateObj = "stateUpdate",
                    state = encodeURI(JSON.stringify(stateObj));

                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&approval_prompt=force&flowName=GeneralOAuthFlow";
                    
                    return url;
                },
                getSwitchAccountURL(state) {
                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow";
                    
                    return url;
                }
            }
        });

        return store;
    };

    return {
        init: init
    };

})(jQuery);
const YUNOPageLoader = (function($) {
    
    const loader = function() {
        Vue.component('yuno-page-loader', {
            template: `
                <div class="yunoPageLoader">
                    <div class="yunoSpinner"></div>
                </div>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                
            }
        });
    };

    return {
        loader: loader
    };
})(jQuery);




const YUNOLoader = (function($) {
    
    const loader = function() {
        Vue.component('yuno-loader', {
            template: `
                <div 
                    :class="{'withOverlay': isOverlay, 'isActive': loader.isActive}"
                    class="yunoLoader">
                    <div class="yunoSpinner"></div>
                </div>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                ...Vuex.mapState([
                    'loader'
                    ]),
                isOverlay: {
                    get() {
                        let getOpt = this.loader;

                        if (getOpt.overlay) {
                            document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled");
                            document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled");
                        } else {
                            document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled");
                            document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled");
                        }

                        return this.loader.overlay
                    }
                }
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                
            }
        });
    };

    return {
        loader: loader
    };
})(jQuery);




/*
 MIT
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
*****************************************************************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(m){var p=0;return function(){return p<m.length?{done:!1,value:m[p++]}:{done:!0}}};$jscomp.arrayIterator=function(m){return{next:$jscomp.arrayIteratorImpl(m)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(m,p,t){if(m==Array.prototype||m==Object.prototype)return m;m[p]=t.value;return m};$jscomp.getGlobal=function(m){m=["object"==typeof globalThis&&globalThis,m,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var p=0;p<m.length;++p){var t=m[p];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(m,p){var t=$jscomp.propertyToPolyfillSymbol[p];if(null==t)return m[p];t=m[t];return void 0!==t?t:m[p]};
$jscomp.polyfill=function(m,p,t,u){p&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(m,p,t,u):$jscomp.polyfillUnisolated(m,p,t,u))};$jscomp.polyfillUnisolated=function(m,p,t,u){t=$jscomp.global;m=m.split(".");for(u=0;u<m.length-1;u++){var v=m[u];if(!(v in t))return;t=t[v]}m=m[m.length-1];u=t[m];p=p(u);p!=u&&null!=p&&$jscomp.defineProperty(t,m,{configurable:!0,writable:!0,value:p})};
$jscomp.polyfillIsolated=function(m,p,t,u){var v=m.split(".");m=1===v.length;u=v[0];u=!m&&u in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var z=0;z<v.length-1;z++){var A=v[z];if(!(A in u))return;u=u[A]}v=v[v.length-1];t=$jscomp.IS_SYMBOL_NATIVE&&"es6"===t?u[v]:null;p=p(t);null!=p&&(m?$jscomp.defineProperty($jscomp.polyfills,v,{configurable:!0,writable:!0,value:p}):p!==t&&($jscomp.propertyToPolyfillSymbol[v]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(v):$jscomp.POLYFILL_PREFIX+v,v=
$jscomp.propertyToPolyfillSymbol[v],$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:p})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(m){if(m)return m;var p=function(v,z){this.$jscomp$symbol$id_=v;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:z})};p.prototype.toString=function(){return this.$jscomp$symbol$id_};var t=0,u=function(v){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return new p("jscomp_symbol_"+(v||"")+"_"+t++,v)};return u},"es6","es3");$jscomp.initSymbolIterator=function(){};
$jscomp.polyfill("Symbol.iterator",function(m){if(m)return m;m=Symbol("Symbol.iterator");for(var p="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<p.length;t++){var u=$jscomp.global[p[t]];"function"===typeof u&&"function"!=typeof u.prototype[m]&&$jscomp.defineProperty(u.prototype,m,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return m},"es6",
"es3");$jscomp.initSymbolAsyncIterator=function(){};$jscomp.iteratorPrototype=function(m){m={next:m};m[Symbol.iterator]=function(){return this};return m};
(function(m,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("vue")):"function"===typeof define&&define.amd?define(["exports","vue"],p):(m=m||self,p(m.VeeValidate={},m.Vue))})(this,function(m,p){function t(a,b,c,d){return new (c||(c=Promise))(function(e,k){function h(g){try{l(d.next(g))}catch(n){k(n)}}function f(g){try{l(d["throw"](g))}catch(n){k(n)}}function l(g){g.done?e(g.value):(new c(function(n){n(g.value)})).then(h,f)}l((d=d.apply(a,b||[])).next())})}function u(a,
b){function c(g){return function(n){return d([g,n])}}function d(g){if(k)throw new TypeError("Generator is already executing.");for(;e;)try{if(k=1,h&&(f=g[0]&2?h["return"]:g[0]?h["throw"]||((f=h["return"])&&f.call(h),0):h.next)&&!(f=f.call(h,g[1])).done)return f;if(h=0,f)g=[g[0]&2,f.value];switch(g[0]){case 0:case 1:f=g;break;case 4:return e.label++,{value:g[1],done:!1};case 5:e.label++;h=g[1];g=[0];continue;case 7:g=e.ops.pop();e.trys.pop();continue;default:if(!(f=e.trys,f=0<f.length&&f[f.length-
1])&&(6===g[0]||2===g[0])){e=0;continue}if(3===g[0]&&(!f||g[1]>f[0]&&g[1]<f[3]))e.label=g[1];else if(6===g[0]&&e.label<f[1])e.label=f[1],f=g;else if(f&&e.label<f[2])e.label=f[2],e.ops.push(g);else{f[2]&&e.ops.pop();e.trys.pop();continue}}g=b.call(a,e)}catch(n){g=[6,n],h=0}finally{k=f=0}if(g[0]&5)throw g[1];return{value:g[0]?g[1]:void 0,done:!0}}var e={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},k,h,f,l;return l={next:c(0),"throw":c(1),"return":c(2)},"function"===typeof Symbol&&
(l[Symbol.iterator]=function(){return this}),l}function v(){for(var a=0,b=0,c=arguments.length;b<c;b++)a+=arguments[b].length;a=Array(a);var d=0;for(b=0;b<c;b++)for(var e=arguments[b],k=0,h=e.length;k<h;k++,d++)a[d]=e[k];return a}function z(a){return null===a||void 0===a}function A(a,b){if(a instanceof RegExp&&b instanceof RegExp)return A(a.source,b.source)&&A(a.flags,b.flags);if(Array.isArray(a)&&Array.isArray(b)){if(a.length!==b.length)return!1;for(var c=0;c<a.length;c++)if(!A(a[c],b[c]))return!1;
return!0}return B(a)&&B(b)?Object.keys(a).every(function(d){return A(a[d],b[d])})&&Object.keys(b).every(function(d){return A(a[d],b[d])}):a!==a&&b!==b?!0:a===b}function Z(a){return""===a?!1:!z(a)}function y(a){return"function"===typeof a}function E(a){return y(a)&&!!a.__locatorRef}function aa(a,b){var c=Array.isArray(a)?a:M(a);if(y(c.findIndex))return c.findIndex(b);for(var d=0;d<c.length;d++)if(b(c[d],d))return d;return-1}function za(a,b){var c=Array.isArray(a)?a:M(a),d=aa(c,b);return-1===d?void 0:
c[d]}function F(a,b){return-1!==a.indexOf(b)}function M(a){if(y(Array.from))return Array.from(a);for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function N(a){return y(Object.values)?Object.values(a):Object.keys(a).map(function(b){return a[b]})}function O(a,b){Object.keys(b).forEach(function(c){B(b[c])?(a[c]||(a[c]={}),O(a[c],b[c])):a[c]=b[c]});return a}function P(){return{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,
failed:!1}}function Aa(a){return a}function ba(a,b,c){void 0===b&&(b=0);void 0===c&&(c={cancelled:!1});if(0===b)return a;var d;return function(){for(var e=[],k=0;k<arguments.length;k++)e[k]=arguments[k];clearTimeout(d);d=setTimeout(function(){d=void 0;c.cancelled||a.apply(void 0,e)},b)}}function Q(a,b){return a.replace(/{([^}]+)}/g,function(c,d){return d in b?b[d]:"{"+d+"}"})}function Ba(a){var b;if(null===(b=a.params)||void 0===b?0:b.length)a.params=a.params.map(function(c){return"string"===typeof c?
{name:c}:c});return a}function I(a){var b={};Object.defineProperty(b,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1});return a?B(a)&&a._$$isNormalized?a:B(a)?Object.keys(a).reduce(function(c,d){var e=!0===a[d]?[]:Array.isArray(a[d])?a[d]:B(a[d])?a[d]:[a[d]];!1!==a[d]&&(c[d]=ca(d,e));return c},b):"string"!==typeof a?(console.warn("[vee-validate] rules must be either a string or an object."),b):a.split("|").reduce(function(c,d){var e=[],k=d.split(":")[0];F(d,":")&&(e=d.split(":").slice(1).join(":").split(","));
if(!k)return c;c[k]=ca(k,e);return c},b):b}function ca(a,b){var c=x.getRuleDefinition(a);if(!c)return b;var d={};if(!c.params&&!Array.isArray(b))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(b)&&!c.params)return b;if(!c.params||c.params.length<b.length&&Array.isArray(b)){var e;var k=b.map(function(g,n){var q,w=null===(q=c.params)||void 0===q?void 0:q[n];e=w||e;w||(w=e);return w})}else k=c.params;for(var h=0;h<k.length;h++){var f=k[h],l=f["default"];
Array.isArray(b)?h in b&&(l=b[h]):f.name in b?l=b[f.name]:1===k.length&&(l=b);f.isTarget&&(l=da(l,f.cast));"string"===typeof l&&"@"===l[0]&&(l=da(l.slice(1),f.cast));!E(l)&&f.cast&&(l=f.cast(l));d[f.name]?(d[f.name]=Array.isArray(d[f.name])?d[f.name]:[d[f.name]],d[f.name].push(l)):d[f.name]=l}return d}function da(a,b){var c=function(d){d=d[a];return b?b(d):d};c.__locatorRef=a;return c}function Ca(a){return Array.isArray(a)?a.filter(E):Object.keys(a).filter(function(b){return E(a[b])}).map(function(b){return a[b]})}
function ea(a,b,c){void 0===c&&(c={});var d,e,k,h,f,l;return t(this,void 0,void 0,function(){var g,n,q,w,D,J,R;return u(this,function(fa){switch(fa.label){case 0:return g=null===(d=c)||void 0===d?void 0:d.bails,n=null===(e=c)||void 0===e?void 0:e.skipIfEmpty,q={name:(null===(k=c)||void 0===k?void 0:k.name)||"{field}",rules:I(b),bails:null!==g&&void 0!==g?g:!0,skipIfEmpty:null!==n&&void 0!==n?n:!0,forceRequired:!1,crossTable:(null===(h=c)||void 0===h?void 0:h.values)||{},names:(null===(f=c)||void 0===
f?void 0:f.names)||{},customMessages:(null===(l=c)||void 0===l?void 0:l.customMessages)||{}},[4,Da(q,a,c)];case 1:return w=fa.sent(),D=[],J={},R={},w.errors.forEach(function(L){var ha=L.msg();D.push(ha);J[L.rule]=ha;R[L.rule]=L.msg}),[2,{valid:w.valid,errors:D,failedRules:J,regenerateMap:R}]}})})}function Da(a,b,c){c=(void 0===c?{}:c).isInitial;var d=void 0===c?!1:c;return t(this,void 0,void 0,function(){var e,k,h,f,l,g,n,q;return u(this,function(w){switch(w.label){case 0:return[4,Ea(a,b)];case 1:e=
w.sent();k=e.shouldSkip;h=e.errors;if(k)return[2,{valid:!h.length,errors:h}];f=Object.keys(a.rules).filter(function(D){return!x.isRequireRule(D)});l=f.length;g=0;w.label=2;case 2:if(!(g<l))return[3,5];if(d&&x.isLazy(f[g]))return[3,4];n=f[g];return[4,ia(a,b,{name:n,params:a.rules[n]})];case 3:q=w.sent();if(!q.valid&&q.error&&(h.push(q.error),a.bails))return[2,{valid:!1,errors:h}];w.label=4;case 4:return g++,[3,2];case 5:return[2,{valid:!h.length,errors:h}]}})})}function Ea(a,b){return t(this,void 0,
void 0,function(){var c,d,e,k,h,f,l,g,n;return u(this,function(q){switch(q.label){case 0:c=Object.keys(a.rules).filter(x.isRequireRule);d=c.length;e=[];var w;(w=z(b)||""===b)||(w=Array.isArray(b)&&0===b.length);h=(k=w)&&a.skipIfEmpty;f=!1;l=0;q.label=1;case 1:if(!(l<d))return[3,4];g=c[l];return[4,ia(a,b,{name:g,params:a.rules[g]})];case 2:n=q.sent();if(!B(n))throw Error("Require rules has to return an object (see docs)");n.required&&(f=!0);if(!n.valid&&n.error&&(e.push(n.error),a.bails))return[2,
{shouldSkip:!0,errors:e}];q.label=3;case 3:return l++,[3,1];case 4:return k&&!f&&!a.skipIfEmpty||!a.bails&&!h?[2,{shouldSkip:!1,errors:e}]:[2,{shouldSkip:!f&&k,errors:e}]}})})}function ia(a,b,c){return t(this,void 0,void 0,function(){var d,e,k,h,f;return u(this,function(l){switch(l.label){case 0:d=x.getRuleDefinition(c.name);if(!d||!d.validate)throw Error("No such validator '"+c.name+"' exists.");e=d.castValue?d.castValue(b):b;k=Fa(c.params,a.crossTable);return[4,d.validate(e,k)];case 1:h=l.sent();
if("string"===typeof h)return f=r(r({},k||{}),{_field_:a.name,_value_:b,_rule_:c.name}),[2,{valid:!1,error:{rule:c.name,msg:function(){return Q(h,f)}}}];B(h)||(h={valid:h});return[2,{valid:h.valid,required:h.required,error:h.valid?void 0:Ga(a,b,d,c.name,k)}]}})})}function Ga(a,b,c,d,e){var k,h=(k=a.customMessages[d],null!==k&&void 0!==k?k:c.message);k=Ha(a,c,d);c=Ia(a,c,d,h);h=c.userTargets;var f=c.userMessage,l=r(r(r(r({},e||{}),{_field_:a.name,_value_:b,_rule_:d}),k),h);return{msg:function(){var g=
f||C.defaultMessage;var n=a.name;g="function"===typeof g?g(n,l):Q(g,r(r({},l),{_field_:n}));return g},rule:d}}function Ha(a,b,c){b=b.params;if(!b||0>=b.filter(function(f){return f.isTarget}).length)return{};var d={},e=a.rules[c];!Array.isArray(e)&&B(e)&&(e=b.map(function(f){return e[f.name]}));for(c=0;c<b.length;c++){var k=b[c],h=e[c];E(h)&&(h=h.__locatorRef,d[k.name]=a.names[h]||h,d["_"+k.name+"_"]=a.crossTable[h])}return d}function Ia(a,b,c,d){var e={},k=a.rules[c],h=b.params||[];if(!k)return{};
Object.keys(k).forEach(function(f,l){var g=k[f];if(!E(g))return{};var n=h[l];if(!n)return{};g=g.__locatorRef;e[n.name]=a.names[g]||g;e["_"+n.name+"_"]=a.crossTable[g]});return{userTargets:e,userMessage:d}}function Fa(a,b){if(Array.isArray(a))return a;var c={};Object.keys(a).forEach(function(d){var e=a[d];e=E(e)?e(b):e;c[d]=e});return c}function ja(){S.$emit("change:locale")}function Ja(a){var b,c;if(!a||!("undefined"!==typeof Event&&y(Event)&&a instanceof Event||a&&a.srcElement))return a;a=a.target;
return"file"===a.type&&a.files?M(a.files):(null===(b=a._vModifiers)||void 0===b?0:b.number)?(b=parseFloat(a.value),b!==b?a.value:b):(null===(c=a._vModifiers)||void 0===c?0:c.trim)?"string"===typeof a.value?a.value.trim():a.value:a.value}function T(a){if(a.data){var b=a.data;if("model"in b)return b.model;if(a.data.directives)return za(a.data.directives,function(c){return"model"===c.name})}}function U(a){var b,c,d,e=T(a);if(e)return{value:e.value};e=(null===(b=V(a))||void 0===b?void 0:b.prop)||"value";
if((null===(c=a.componentOptions)||void 0===c?0:c.propsData)&&e in a.componentOptions.propsData)return{value:a.componentOptions.propsData[e]};if((null===(d=a.data)||void 0===d?0:d.domProps)&&"value"in a.data.domProps)return{value:a.data.domProps.value}}function Ka(a){return Array.isArray(a)?a:Array.isArray(a.children)?a.children:a.componentOptions&&Array.isArray(a.componentOptions.children)?a.componentOptions.children:[]}function ka(a){return Array.isArray(a)||void 0===U(a)?Ka(a).reduce(function(b,
c){var d=ka(c);d.length&&b.push.apply(b,d);return b},[]):[a]}function V(a){return a.componentOptions?a.componentOptions.Ctor.options.model:null}function K(a,b,c){z(a[b])?a[b]=[c]:y(a[b])&&a[b].fns?(a=a[b],a.fns=Array.isArray(a.fns)?a.fns:[a.fns],F(a.fns,c)||a.fns.push(c)):(y(a[b])&&(a[b]=[a[b]]),Array.isArray(a[b])&&!F(a[b],c)&&a[b].push(c))}function W(a,b,c){a.componentOptions?a.componentOptions&&(a.componentOptions.listeners||(a.componentOptions.listeners={}),K(a.componentOptions.listeners,b,c)):
(a.data||(a.data={}),z(a.data.on)&&(a.data.on={}),K(a.data.on,b,c))}function la(a,b){var c;return a.componentOptions?(V(a)||{event:"input"}).event:(null===(c=null===b||void 0===b?void 0:b.modifiers)||void 0===c?0:c.lazy)?"change":ma(a)?"input":"change"}function La(a,b){return Object.keys(a).reduce(function(c,d){a[d].forEach(function(e){e.context||(a[d].context=b,e.data||(e.data={}),e.data.slot=d)});return c.concat(a[d])},[])}function na(a,b){return a.$scopedSlots["default"]?a.$scopedSlots["default"](b)||
[]:a.$slots["default"]||[]}function oa(a){return r(r({},a.flags),{errors:a.errors,classes:a.classes,failedRules:a.failedRules,reset:function(){return a.reset()},validate:function(){for(var b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return a.validate.apply(a,b)},ariaInput:{"aria-invalid":a.flags.invalid?"true":"false","aria-required":a.isRequired?"true":"false","aria-errormessage":"vee_"+a.id},ariaMsg:{id:"vee_"+a.id,"aria-live":a.errors.length?"assertive":"off"}})}function pa(a,b){a.initialized||
(a.initialValue=b);var c=!a._ignoreImmediate&&a.immediate||a.value!==b&&a.normalizedEvents.length||a._needsValidation||!a.initialized&&void 0===b?!0:!1;a._needsValidation=!1;a.value=b;a._ignoreImmediate=!0;if(c){var d=function(){if(a.immediate||a.flags.validated)return X(a);a.validateSilent()};a.initialized?d():a.$once("hook:mounted",function(){return d()})}}function qa(a){return(y(a.mode)?a.mode:ra[a.mode])(a)}function X(a){var b=a.validateSilent();a._pendingValidation=b;return b.then(function(c){b===
a._pendingValidation&&(a.applyResult(c),a._pendingValidation=void 0);return c})}function sa(a){a.$veeOnInput||(a.$veeOnInput=function(k){a.syncValue(k);a.setFlags({dirty:!0,pristine:!1})});var b=a.$veeOnInput;a.$veeOnBlur||(a.$veeOnBlur=function(){a.setFlags({touched:!0,untouched:!1})});var c=a.$veeOnBlur,d=a.$veeHandler,e=qa(a);d&&a.$veeDebounce===a.debounce||(d=ba(function(){a.$nextTick(function(){a._pendingReset||X(a);a._pendingReset=!1})},e.debounce||a.debounce),a.$veeHandler=d,a.$veeDebounce=
a.debounce);return{onInput:b,onBlur:c,onValidate:d}}function Ma(a,b){var c=U(b);a._inputEventName=a._inputEventName||la(b,T(b));pa(a,null===c||void 0===c?void 0:c.value);c=sa(a);var d=c.onBlur,e=c.onValidate;W(b,a._inputEventName,c.onInput);W(b,"blur",d);a.normalizedEvents.forEach(function(k){W(b,k,e)});a.initialized=!0}function Na(a,b){for(var c={},d=Object.keys(b),e=d.length,k=function(f){f=d[f];var l=a&&a[f]||f,g=b[f];if(z(g)||("valid"===f||"invalid"===f)&&!b.validated)return"continue";"string"===
typeof l?c[l]=g:Array.isArray(l)&&l.forEach(function(n){c[n]=g})},h=0;h<e;h++)k(h);return c}function Oa(a){var b=a.$_veeObserver.refs;return a.fieldDeps.reduce(function(c,d){if(!b[d])return c;c.values[d]=b[d].value;c.names[d]=b[d].name;return c},{names:{},values:{}})}function Pa(a){if(a.vid)return a.vid;if(a.name)return a.name;if(a.id)return a.id;if(a.fieldName)return a.fieldName;ta++;return"_vee_"+ta}function Qa(){return{refs:{},observe:function(a){this.refs[a.id]=a},unobserve:function(a){delete this.refs[a]}}}
function ua(a,b,c){void 0===c&&(c=!0);var d=a.$_veeObserver.refs;a._veeWatchers||(a._veeWatchers={});if(!d[b]&&c)return a.$once("hook:mounted",function(){ua(a,b,!1)});!y(a._veeWatchers[b])&&d[b]&&(a._veeWatchers[b]=d[b].$watch("value",function(){a.flags.validated&&(a._needsValidation=!0,a.validate())}))}function va(a){a.$_veeObserver&&a.$_veeObserver.unobserve(a.id,"observer")}function wa(a){a.$_veeObserver&&a.$_veeObserver.observe(a,"observer")}function xa(){return r(r({},P()),{valid:!0,invalid:!1})}
function Ra(){for(var a=v(N(this.refs),this.observers),b={},c=xa(),d={},e=a.length,k=0;k<e;k++){var h=a[k];Array.isArray(h.errors)?(b[h.id]=h.errors,d[h.id]=r({id:h.id,name:h.name,failedRules:h.failedRules},h.flags)):(b=r(r({},b),h.errors),d=r(r({},d),h.fields))}Sa.forEach(function(f){var l=f[0];c[l]=a[f[1]](function(g){return g.flags[l]})});return{errors:b,flags:c,fields:d}}p=p&&p.hasOwnProperty("default")?p["default"]:p;var r=function(){r=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<
d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return r.apply(this,arguments)},B=function(a){return null!==a&&a&&"object"===typeof a&&!Array.isArray(a)},G={},x=function(){function a(){}a.extend=function(b,c){var d=Ba(c);G[b]=G[b]?O(G[b],c):r({lazy:!1,computesRequired:!1},d)};a.isLazy=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.lazy)};a.isRequireRule=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.computesRequired)};a.getRuleDefinition=
function(b){return G[b]};return a}(),C=r({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Y=function(a){C=r(r({},C),a)},ra={aggressive:function(){return{on:["input","blur"]}},eager:function(a){return a.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},
S=new p,Ta=function(){function a(b,c){this.container={};this.locale=b;this.merge(c)}a.prototype.resolve=function(b,c,d){return this.format(this.locale,b,c,d)};a.prototype.format=function(b,c,d,e){var k,h,f,l,g,n,q,w;(d=(null===(f=null===(h=null===(k=this.container[b])||void 0===k?void 0:k.fields)||void 0===h?void 0:h[c])||void 0===f?void 0:f[d])||(null===(g=null===(l=this.container[b])||void 0===l?void 0:l.messages)||void 0===g?void 0:g[d]))||(d="{field} is not valid");c=(w=null===(q=null===(n=this.container[b])||
void 0===n?void 0:n.names)||void 0===q?void 0:q[c],null!==w&&void 0!==w?w:c);return y(d)?d(c,e):Q(d,r(r({},e),{_field_:c}))};a.prototype.merge=function(b){O(this.container,b)};a.prototype.hasRule=function(b){var c,d;return!(null===(d=null===(c=this.container[this.locale])||void 0===c?void 0:c.messages)||void 0===d||!d[b])};return a}(),H,ma=function(a){var b,c=(null===(b=a.data)||void 0===b?void 0:b.attrs)||a.elm;return("input"!==a.tag||c&&c.type)&&"textarea"!==a.tag?F("text password search email tel url number".split(" "),
null===c||void 0===c?void 0:c.type):!0},ta=0,ya=p.extend({inject:{$_veeObserver:{from:"$_veeObserver","default":function(){this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver=Qa());return this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,"default":""},name:{type:String,"default":null},mode:{type:[String,Function],"default":function(){return C.mode}},rules:{type:[Object,String],"default":null},immediate:{type:Boolean,"default":!1},bails:{type:Boolean,"default":function(){return C.bails}},
skipIfEmpty:{type:Boolean,"default":function(){return C.skipOptional}},debounce:{type:Number,"default":0},tag:{type:String,"default":"span"},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1},customMessages:{type:Object,"default":function(){return{}}}},watch:{rules:{deep:!0,handler:function(a,b){this._needsValidation=!A(a,b)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:P(),failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var a=
this;return Object.keys(this.normalizedRules).reduce(function(b,c){var d=Ca(a.normalizedRules[c]).map(function(e){return e.__locatorRef});b.push.apply(b,d);d.forEach(function(e){ua(a,e)});return b},[])},normalizedEvents:function(){var a=this;return(qa(this).on||[]).map(function(b){return"input"===b?a._inputEventName:b})},isRequired:function(){var a=r(r({},this._resolvedRules),this.normalizedRules);a=Object.keys(a).some(x.isRequireRule);this.flags.required=!!a;return a},classes:function(){return Na(C.classes,
this.flags)},normalizedRules:function(){return I(this.rules)}},created:function(){var a=this,b=function(){if(a.flags.validated){var c=a._regenerateMap;if(c){var d=[],e={};Object.keys(c).forEach(function(k){var h=c[k]();d.push(h);e[k]=h});a.applyResult({errors:d,failedRules:e,regenerateMap:c})}else a.validate()}};S.$on("change:locale",b);this.$on("hook:beforeDestroy",function(){S.$off("change:locale",b)})},render:function(a){var b=this;this.registerField();var c=oa(this);c=na(this,c);ka(c).forEach(function(d){var e,
k,h,f,l;if(C.useConstraintAttrs){var g,n=null===(g=d.data)||void 0===g?void 0:g.attrs;if(F(["input","select","textarea"],d.tag)&&n)if(g={},"required"in n&&!1!==n.required&&x.getRuleDefinition("required")&&(g.required="checkbox"===n.type?[!0]:!0),ma(d)){n=r;g=r({},g);var q=null===(l=d.data)||void 0===l?void 0:l.attrs;l={};q&&("email"===q.type&&x.getRuleDefinition("email")&&(l.email=["multiple"in q]),q.pattern&&x.getRuleDefinition("regex")&&(l.regex=q.pattern),0<=q.maxlength&&x.getRuleDefinition("max")&&
(l.max=q.maxlength),0<=q.minlength&&x.getRuleDefinition("min")&&(l.min=q.minlength),"number"===q.type&&(Z(q.min)&&x.getRuleDefinition("min_value")&&(l.min_value=Number(q.min)),Z(q.max)&&x.getRuleDefinition("max_value")&&(l.max_value=Number(q.max))));l=I(n(g,l))}else l=I(g);else l={}}else l={};A(b._resolvedRules,l)||(b._needsValidation=!0);F(["input","select","textarea"],d.tag)&&(b.fieldName=(null===(k=null===(e=d.data)||void 0===e?void 0:e.attrs)||void 0===k?void 0:k.name)||(null===(f=null===(h=d.data)||
void 0===h?void 0:h.attrs)||void 0===f?void 0:f.id));b._resolvedRules=l;Ma(b,d)});return this.slim&&1>=c.length?c[0]:a(this.tag,c)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(a){var b=this;Object.keys(a).forEach(function(c){b.flags[c]=a[c]})},syncValue:function(a){this.value=a=Ja(a);this.flags.changed=this.initialValue!==a},reset:function(){var a=this;this.errors=[];this.initialValue=
this.value;var b=P();b.required=this.isRequired;this.setFlags(b);this.failedRules={};this.validateSilent();this._pendingValidation=void 0;this._pendingReset=!0;setTimeout(function(){a._pendingReset=!1},this.debounce)},validate:function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return t(this,void 0,void 0,function(){return u(this,function(c){0<a.length&&this.syncValue(a[0]);return[2,X(this)]})})},validateSilent:function(){return t(this,void 0,void 0,function(){var a,b;return u(this,
function(c){switch(c.label){case 0:return this.setFlags({pending:!0}),a=r(r({},this._resolvedRules),this.normalizedRules),Object.defineProperty(a,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,ea(this.value,a,r(r({name:this.name||this.fieldName},Oa(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return b=c.sent(),this.setFlags({pending:!1,valid:b.valid,invalid:!b.valid}),[2,b]}})})},setErrors:function(a){this.applyResult({errors:a,
failedRules:{}})},applyResult:function(a){var b=a.errors,c=a.failedRules;a=a.regenerateMap;this.errors=b;this._regenerateMap=a;this.failedRules=r({},c||{});this.setFlags({valid:!b.length,passed:!b.length,invalid:!!b.length,failed:!!b.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var a=Pa(this),b=this.id;!this.isActive||b===a&&this.$_veeObserver.refs[b]||(b!==a&&this.$_veeObserver.refs[b]===this&&this.$_veeObserver.unobserve(b),this.id=a,this.$_veeObserver.observe(this))}}}),
Sa=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],Ua=0,Va=p.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver","default":function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,"default":"span"},vid:{type:String,
"default":function(){return"obs_"+Ua++}},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:xa(),fields:{}}},created:function(){var a=this;this.id=this.vid;wa(this);var b=ba(function(c){var d=c.flags,e=c.fields;a.errors=c.errors;a.flags=d;a.fields=e},16);this.$watch(Ra,b)},activated:function(){wa(this)},deactivated:function(){va(this)},beforeDestroy:function(){va(this)},render:function(a){var b=na(this,r(r({},this.flags),
{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=b.length?b[0]:a(this.tag,{on:this.$listeners},b)},methods:{observe:function(a,b){var c;void 0===b&&(b="provider");"observer"===b?this.observers.push(a):this.refs=r(r({},this.refs),(c={},c[a.id]=a,c))},unobserve:function(a,b){void 0===b&&(b="provider");if("provider"===b)this.refs[a]&&this.$delete(this.refs,a);else{var c=aa(this.observers,function(d){return d.id===
a});-1!==c&&this.observers.splice(c,1)}},validate:function(a){a=(void 0===a?{}:a).silent;var b=void 0===a?!1:a;return t(this,void 0,void 0,function(){var c;return u(this,function(d){switch(d.label){case 0:return[4,Promise.all(v(N(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[b?"validateSilent":"validate"]().then(function(k){return k.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:b})})))];case 1:return c=d.sent(),
[2,c.every(function(e){return e})]}})})},handleSubmit:function(a){return t(this,void 0,void 0,function(){var b;return u(this,function(c){switch(c.label){case 0:return[4,this.validate()];case 1:return(b=c.sent())&&a?[2,a()]:[2]}})})},reset:function(){return v(N(this.refs),this.observers).forEach(function(a){return a.reset()})},setErrors:function(a){var b=this;Object.keys(a).forEach(function(c){var d=b.refs[c];d&&(c=a[c]||[],c="string"===typeof c?[c]:c,d.setErrors(c))});this.observers.forEach(function(c){c.setErrors(a)})}}});
m.ValidationObserver=Va;m.ValidationProvider=ya;m.configure=function(a){Y(a)};m.extend=function(a,b){if(!y(b)&&!y(b.validate)&&!x.getRuleDefinition(a))throw Error("Extension Error: The validator '"+a+"' must be a function or have a 'validate' method.");"object"===typeof b?x.extend(a,b):x.extend(a,{validate:b})};m.localeChanged=ja;m.localize=function(a,b){var c;H||(H=new Ta("en",{}),Y({defaultMessage:function(d,e){return H.resolve(d,null===e||void 0===e?void 0:e._rule_,e||{})}}));"string"===typeof a?
(H.locale=a,b&&H.merge((c={},c[a]=b,c)),ja()):H.merge(a)};m.normalizeRules=I;m.setInteractionMode=function(a,b){Y({mode:a});if(b){if(!y(b))throw Error("A mode implementation must be a function");ra[a]=b}};m.validate=ea;m.version="3.2.3";m.withValidation=function(a,b){void 0===b&&(b=Aa);var c,d="options"in a?a.options:a,e=ya.options;e={name:(d.name||"AnonymousHoc")+"WithValidation",props:r({},e.props),data:e.data,computed:r({},e.computed),methods:r({},e.methods),beforeDestroy:e.beforeDestroy,inject:e.inject};
var k=(null===(c=null===d||void 0===d?void 0:d.model)||void 0===c?void 0:c.event)||"input";e.render=function(h){var f;this.registerField();var l=oa(this),g=r({},this.$listeners),n=T(this.$vnode);this._inputEventName=this._inputEventName||la(this.$vnode,n);var q=U(this.$vnode);pa(this,null===q||void 0===q?void 0:q.value);q=sa(this);var w=q.onBlur,D=q.onValidate;K(g,k,q.onInput);K(g,"blur",w);this.normalizedEvents.forEach(function(J){K(g,J,D)});q=(V(this.$vnode)||{prop:"value"}).prop;l=r(r(r({},this.$attrs),
(f={},f[q]=null===n||void 0===n?void 0:n.value,f)),b(l));return h(d,{attrs:this.$attrs,props:l,on:g},La(this.$slots,this.$vnode.context))};return e};Object.defineProperty(m,"__esModule",{value:!0})});



!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r=r||self).VeeValidateRules={})}(this,function(r){"use strict";var i={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},u={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},o=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return o(r,{locale:n})}):n?(i[n]||i.en).test(e):Object.keys(i).some(function(r){return i[r].test(e)})},e={validate:o,params:[{name:"locale"}]},l=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return l(r,{locale:n})}):n?(s[n]||s.en).test(e):Object.keys(s).some(function(r){return s[r].test(e)})},t={validate:l,params:[{name:"locale"}]},c=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return c(r,{locale:n})}):n?(u[n]||u.en).test(e):Object.keys(u).some(function(r){return u[r].test(e)})},n={validate:c,params:[{name:"locale"}]},A=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return A(r,{locale:n})}):n?(a[n]||a.en).test(e):Object.keys(a).some(function(r){return a[r].test(e)})},f={validate:A,params:[{name:"locale"}]},m=function(r,e){var t=void 0===e?{}:e,n=t.min,i=t.max;return Array.isArray(r)?r.every(function(r){return!!m(r,{min:n,max:i})}):Number(n)<=r&&Number(i)>=r},v={validate:m,params:[{name:"min"},{name:"max"}]},$={validate:function(r,e){var t=e.target;return String(r)===String(t)},params:[{name:"target",isTarget:!0}]},d=function(r,e){var t=e.length;if(Array.isArray(r))return r.every(function(r){return d(r,{length:t})});var n=String(r);return/^[0-9]*$/.test(n)&&n.length===t},y={validate:d,params:[{name:"length",cast:function(r){return Number(r)}}]},g={validate:function(r,e){var u=e.width,s=e.height,t=[];r=Array.isArray(r)?r:[r];for(var n=0;n<r.length;n++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(r[n].name))return Promise.resolve(!1);t.push(r[n])}return Promise.all(t.map(function(r){return t=r,n=u,i=s,a=window.URL||window.webkitURL,new Promise(function(r){var e=new Image;e.onerror=function(){return r(!1)},e.onload=function(){return r(e.width===n&&e.height===i)},e.src=a.createObjectURL(t)});var t,n,i,a})).then(function(r){return r.every(function(r){return r})})},params:[{name:"width",cast:function(r){return Number(r)}},{name:"height",cast:function(r){return Number(r)}}]},Z={validate:function(r,e){var t=(void 0===e?{}:e).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return t&&!Array.isArray(r)&&(r=String(r).split(",").map(function(r){return r.trim()})),Array.isArray(r)?r.every(function(r){return n.test(String(r))}):n.test(String(r))},params:[{name:"multiple",default:!1}]};function p(r){return null==r}function h(r){return Array.isArray(r)&&0===r.length}function x(r){return"function"==typeof Array.from?Array.from(r):function(r){for(var e=[],t=r.length,n=0;n<t;n++)e.push(r[n]);return e}(r)}function _(r){return h(r)||-1!==[!1,null,void 0].indexOf(r)||!String(r).trim().length}var b=function(e,t){return Array.isArray(e)?e.every(function(r){return b(r,t)}):x(t).some(function(r){return r==e})},w={validate:b},S={validate:function(r,e){return!b(r,e)}},N={validate:function(r,e){var t=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.name)}):t.test(r.name)}},j={validate:function(r){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(r)?r.every(function(r){return e.test(r.name)}):e.test(r.name)}},k={validate:function(r){return Array.isArray(r)?r.every(function(r){return/^-?[0-9]+$/.test(String(r))}):/^-?[0-9]+$/.test(String(r))}},z={validate:function(r,e){return r===e.other},params:[{name:"other"}]},F={validate:function(r,e){return r!==e.other},params:[{name:"other"}]},R={validate:function(r,e){var t=e.length;return!p(r)&&("number"==typeof r&&(r=String(r)),r.length||(r=x(r)),r.length===t)},params:[{name:"length",cast:function(r){return Number(r)}}]},O=function(r,e){var t=e.length;return p(r)?0<=t:Array.isArray(r)?r.every(function(r){return O(r,{length:t})}):String(r).length<=t},q={validate:O,params:[{name:"length",cast:function(r){return Number(r)}}]},C=function(r,e){var t=e.max;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return C(r,{max:t})}):Number(r)<=t)},P={validate:C,params:[{name:"max",cast:function(r){return Number(r)}}]},E={validate:function(r,e){var t=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.type)}):t.test(r.type)}},L=function(r,e){var t=e.length;return!p(r)&&(Array.isArray(r)?r.every(function(r){return L(r,{length:t})}):String(r).length>=t)},U={validate:L,params:[{name:"length",cast:function(r){return Number(r)}}]},T=function(r,e){var t=e.min;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return T(r,{min:t})}):Number(r)>=t)},V={validate:T,params:[{name:"min",cast:function(r){return Number(r)}}]},I=/^[٠١٢٣٤٥٦٧٨٩]+$/,M=/^[0-9]+$/,B={validate:function(r){function e(r){var e=String(r);return M.test(e)||I.test(e)}return Array.isArray(r)?r.every(e):e(r)}},D=function(r,e){var t=e.regex;return Array.isArray(r)?r.every(function(r){return D(r,{regex:t})}):t.test(String(r))},G={validate:D,params:[{name:"regex",cast:function(r){return"string"==typeof r?new RegExp(r):r}}]},H={validate:function(r,e){var t=(void 0===e?{allowFalse:!0}:e).allowFalse,n={valid:!1,required:!0};return p(r)||h(r)||!1===r&&!t||(n.valid=!!String(r).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},J={validate:function(r,e){var t,n=e.target,i=e.values;return(t=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(r){return r==String(n).trim()})):!_(n))?{valid:!_(r),required:t}:{valid:!0,required:t}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},K={validate:function(r,e){var t=e.size;if(isNaN(t))return!1;var n=1024*t;if(!Array.isArray(r))return r.size<=n;for(var i=0;i<r.length;i++)if(r[i].size>n)return!1;return!0},params:[{name:"size",cast:function(r){return Number(r)}}]};r.alpha=e,r.alpha_dash=t,r.alpha_num=n,r.alpha_spaces=f,r.between=v,r.confirmed=$,r.digits=y,r.dimensions=g,r.email=Z,r.excluded=S,r.ext=N,r.image=j,r.integer=k,r.is=z,r.is_not=F,r.length=R,r.max=q,r.max_value=P,r.mimes=E,r.min=U,r.min_value=V,r.numeric=B,r.oneOf=w,r.regex=G,r.required=H,r.required_if=J,r.size=K,Object.defineProperty(r,"__esModule",{value:!0})});

Vue.component('yuno-org-theme', {
    props: ["data", "options", "resourceloaded"],
    template: `
        <span></span>
    `,
    data() {
        return {
            
        }
    },
    watch: {
        resourceloaded(data) {
            if (data) {
                this.resourceFetched();
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin',
        ]),
        isNotYunoLearning() {
            const allowedHostnames = ['yunolearning.com'];
            return !allowedHostnames.includes(window.location.hostname);
        }
    },
    async created() {
        this.fetchOrgInfo(this.$props.options.orgID);
    },
    mounted() {
        
    },
    methods: {
        loadGoogleFont(fontName) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=' + encodeURIComponent(fontName) + '&display=swap';
            document.head.appendChild(link);
        },
        resourceFetched() {
            const theme = this.orgAdmin.data.theme;

            this.loadGoogleFont(theme.font_family);
            document.documentElement.style.setProperty('--primary-color', theme.primary_color);
            document.documentElement.style.setProperty('--body-bg-color', theme.background_color);
            document.documentElement.style.setProperty('--font-family', theme.font_family);
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                if (this.$props.resourceloaded === undefined) {
                    this.resourceFetched();
                }

                this.$emit("orgFetched");
            };
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

            console.log(this.isNotYunoLearning)

            if (this.isNotYunoLearning) {
                // Dispatch the API call
                this.$store.dispatch('fetchData', options);     
            }
        },
    }
});
Vue.component('yuno-footer', {
    props: ["isOrg"],
    template: `
        <footer class="leadFormFooter">
            <div class="container">
                <div class="columns is-mobile is-centered">
                    <div class="poweredBy">
                        <p>Powered By</p>
                        <img
                            :src="logo"
                            alt="Yuno Learning"
                            width="51"
                            height="24"
                        >
                    </div>
                </div>
            </div>
        </footer>
    `,
    data() {
        return {
            logo: this.$store.state.themeURL + "/assets/images/yuno-logo-grey.svg",
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});
Vue.component('yuno-header', {
    props: ["data", "options", "isOrg"],
    template: `
    <b-navbar 
        class="brandHeader" 
        :mobile-burger="false"
    >
        <template #brand>
            <b-navbar-item>
                <template v-if="isOrg">
                    <template v-if="orgAdmin.loading">
                        <b-skeleton width="102px" height="48px"></b-skeleton>
                    </template>
                    <template v-if="orgAdmin.success && orgAdmin.error === null">
                        <img
                            :src="orgAdmin.data.logo_image_url"
                            :alt="orgAdmin.data.organisation_name"
                        >    
                    </template>
                </template>
                <template v-else>
                    <img
                        src="https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg"
                        alt="Yuno Learning"
                    >
                </template>
            </b-navbar-item>
        </template>
    </b-navbar>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});
window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 10 numbers required",
        "is": "This field is required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-lead-form', {
    props: {
        type: {
            type: String,
            required: true,
        },
    },
    template: `
    <section class="leadForm">
        <yuno-page-loader v-if="isUserLoading"></yuno-page-loader>
        <yuno-org-theme v-if="isOrg" :options="{
                'orgID': orgID
            }" 
            @orgFetched="onOrgFetched"
        >
        </yuno-org-theme>
        <template v-if="isUserReady">
            <yuno-header :isOrg="isOrg"></yuno-header>
            <template v-if="stepsBegin">
                <yuno-schedule-demo
                    v-if="bookADemo"
                    :category="type"
                    @showThankyou="showThankyou"
                >
                </yuno-schedule-demo>
                <yuno-thank-you 
                    v-if="isThankyou" 
                    :options="{'category': type}"
                    :data="findObjectByKey(categories, 'slug', type)" 
                >
                </yuno-thank-you>
                
                <yuno-steps 
                    :options="{'category': type}" 
                    :data="findObjectByKey(categories, 'slug', type)" 
                    @manageThankyou="manageThankyou" 
                    @initSteps="initSteps" 
                    v-if="!isThankyou && !bookADemo"
                >
                </yuno-steps>
            </template>
            <template v-else>
                <yuno-login-step 
                    :isOrg="isOrg"
                    :options="{'category': type}" 
                    :data="findObjectByKey(categories, 'slug', type)"
                >
                </yuno-login-step>
            </template>
            <yuno-footer v-if="isOrg" :isOrg="isOrg"></yuno-footer>
        </template>
    </section>
    
    `,
    data() {
        return {
            orgID: "",
            stepsBegin: false,
            isThankyou: false,
            bookADemo: false,
            isOrg: false,
            categories: [
                {
                    slug: "ielts",
                    label: "IELTS",
                    title: "IELTS Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right IELTS online classes"
                },
                {
                    slug: "english-speaking",
                    label: "English Speaking",
                    title: "English Speaking Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right English Speaking online classes"
                },
                {
                    slug: "pte",
                    label: "PTE",
                    title: "PTE Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right PTE online classes"
                },
                {
                    slug: "duolingo",
                    label: "Duolingo",
                    title: "Duolingo Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right Duolingo online classes"
                },
                {
                    slug: "toefl",
                    label: "TOEFL",
                    title: "TOEFL Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right TOEFL online classes"
                },
                {
                    slug: "french",
                    label: "French",
                    title: "French Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right French online classes"
                },
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'form',
            'categoryList',
            'orgAdmin'
        ]),
        isUserLoading: {
            get() {
                const module =  this.userInfo.loading 

                return module
            }
        },
        isUserReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module = this.userInfo.success
                } else {
                    module = true 
                }

                return module
            }
        },
    },
    async created() {
        this.manageState();
    },
    mounted() {
        this.emitEvents();
    },
    methods: {
        onOrgFetched() {

        },
        findObjectByKey(array, key, value) {
            return array.find(item => item[key] === value);
        },
        initSteps() {
            this.fetchSteps(true);
        },
        showThankyou() {
            this.isThankyou = true;
            this.bookADemo = false;
        },
        manageThankyou() {
            const hasAcademyID = YUNOCommon.getQueryParameter("academy_id");

            if (hasAcademyID) {
                this.bookADemo = true;    
                this.isThankyou = false;
            } else {
                this.isThankyou = true;
                this.bookADemo = false;    
            }
        },
        emitEvents() {
            Event.$on('stepsBegins', () => {
                this.stepsBegin = true;
            });
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchUserInfo() {
            const options = { 
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: (options) => this.gotUserInfo(options)
            };

            this.$store.dispatch('fetchData', options); 
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                this.fetchUserInfo();
            } else {
                this.user.isLoggedin = false;
            }

            const stepsBegin = YUNOCommon.getQueryParameter("stepsBegin")

            if (this.user.isLoggedin && stepsBegin) {
                this.stepsBegin = true;
            }
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {};
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

             // Dispatch the API call
            this.$store.dispatch('fetchData', options); 
        },
        manageUserQueryParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const userid = urlParams.get('user_id');
            const token = urlParams.get('yuno_token');

            if (userid && token) {
                isLoggedIn = userid;
                this.$store.state.config.yunoAPIToken = "Bearer " + token;
                this.user.userID = userid;
                this.user.isLoggedin = true;
            }
        },  
        manageState() {
            const orgID = YUNOCommon.getQueryParameter("org_id");

            if (orgID) {
                this.isOrg = true;
                this.orgID = orgID;
                // this.fetchOrgInfo(orgID);
            };

            this.manageUserQueryParams();
            this.loginStatus();
            this.fetchSteps(false);
            this.fetchCategories();
        },
        gotSteps(options) {
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                const steps = data.form_fields,
                    examType = YUNOCommon.findObjectByKey(steps, "name", "which_ielts_exam_do_you_want_to_take");

                if (this.$props.type === "ielts" && examType.selected === "academic") {
                    YUNOCommon.removeObjInArr(steps, "name", "What_do_you_wish_to_pursue"); 
                };

                this.form.data = data;
            }
        },
        fetchSteps(resetModule) {
            if (resetModule) {
                this.form.data = [];
                this.form.success = false;
            };

            const options = { 
                apiURL: YUNOCommon.config.leadForm("steps", isLoggedIn, this.$props.type),
                module: "gotData",
                store: "form",
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotSteps(options)
            };

            this.$store.dispatch('fetchData', options); 
        },
        fetchCategories() {
            const options = { 
                apiURL: YUNOCommon.config.categoryListAPI(),
                module: "gotData",
                store: "categoryList",
                callback: false
            };

            this.$store.dispatch('fetchData', options); 
        },
    }
});
Vue.component('yuno-login-step', {
    props: ["data", "options", "isOrg"],
    template: `
    
        <section class="loginStep">
            <div class="container">
                <div class="columns is-mobile is-centered">
                    <div class="column is-three-fifths-desktop is-full-mobile">
                        <div class="card">
                            <template v-if="isOrg && orgAdmin.loading">
                                <b-skeleton height="355px"></b-skeleton>
                            </template>
                            <template v-else>
                                <validation-observer 
                                    tag="div" 
                                    class="observer loginObserver"
                                    ref="commonSignupObserver" 
                                    v-slot="{ handleSubmit, invalid }">
                                    <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">
                                        <div class="card-content">
                                            <div class="content">
                                                <h1 class="largestTitle">{{ data.title }}</h1>
                                                <h2 class="largeTitle">{{ data.subTitle }}</h2>
                                                <div class="columns loginFieldWrapper">
                                                    <div class="column is-full-mobile alignC">
                                                        <div class="ratingWrapper">
                                                            <figure>
                                                                <img :src="googleIcnURL" alt="google"></img>
                                                            </figure>
                                                            <div class="ratingInfo">
                                                                <div class="stars">
                                                                    <span v-for="(star, i) in 5" class="material-icons">star_rate</span>
                                                                </div>
                                                                <h3 class="smallCaption dark60">
                                                                    {{ ratingCaption }}
                                                                    <span class="body1">{{ ratingSubCaption }}</span>
                                                                </h3>
                                                            </div>
                                                        </div>
                                                        <div class="instrutorList" v-if="false">
                                                            <div class="list">
                                                                <span v-for="(item, i) in 7" class="material-icons">
                                                                    account_circle
                                                                </span>
                                                            </div>
                                                            <div class="numbers">{{ instructorCaption }}</div>
                                                        </div>
                                                    </div>
                                                    <div class="column is-three-fifths-desktop is-full-mobile loginWrapper">
                                                        <b-field label="Phone Number">
                                                            <validation-provider 
                                                                :customMessages="{ required: 'Phone number is required'}" 
                                                                tag="div" 
                                                                :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" 
                                                                v-slot="{ errors, classes }"
                                                                class="fieldWrapper"
                                                            >
                                                                <b-input 
                                                                    placeholder="Enter your phone number" 
                                                                    :class="classes" 
                                                                    inputmode="numeric"
                                                                    pattern="[0-9]*"
                                                                    type="text"
                                                                    v-model="signIn.mobile"
                                                                >
                                                                </b-input>
                                                                <b-button 
                                                                    v-if="user.isLoggedin" 
                                                                    class="updateNumber"
                                                                >
                                                                    <span class="material-icons">edit</span>
                                                                </b-button>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>    
                                                        </b-field>
                                                        <footer class="card-footer googleWrapper">
                                                            <b-button v-if="!user.isLoggedin" class="card-footer-item hasGoogle" native-type="submit">
                                                                <img :src="googleIcnURL" alt="google"></img> Sign up with Google
                                                            </b-button>
                                                            <b-button v-else class="card-footer-item primary" native-type="submit" :loading="form.loading" :disabled="form.loading">
                                                                Next
                                                            </b-button>
                                                        </footer>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </validation-observer>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    
    `,
    data() {
        return {
            transitionName: 'slide-left',
            ratingCaption: "Rated 5-star on Google",
            ratingSubCaption: "10,000 successful students",
            instructorCaption: "Choose from 216 instructors",
            googleIcnURL: this.$store.state.themeURL + "/assets/images/google.svg",
            signIn: {
                mobile: "",
                categoryURL: "",
                productCode: "",
                leadStatus: "",
                variant: "",
                utmSource: "",
                utmCampaign: "",
                utmMedium: "",
                adGroupID: "",
                adContent: "",
                utmTerm: "",
                gclid: "",
                content: {
                    type: "",
                    id: ""
                },
                landing_page: {
                    url: "",
                    title: ""
                },
                org_details: {
                    type: "login",
                    org_id: "",
                    org_url: "",
                    phone: ""
                },
                login_details: {
                    role: ""
                }
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'subform2',
            'form',
            'categoryList',
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    mounted() {
        this.preFillForm()
    },
    methods: {
        nextStep() {
            if (this.form.data.form_fields.length !== 0) {
                Event.$emit('stepsBegins');    
            } else {
                const category = YUNOCommon.findObjectByKey(this.categoryList.data, "categorySlug", this.$props.options.category),
                    courseSearchURL = "/search/?state=%7B%22category%22:%5B"+ category.category_id +"%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22items%22:%5B%5D,%22slug%22:%22class_days%22%7D,%7B%22items%22:%5B%5D,%22slug%22:%22class_time%22%7D%5D%7D";

                setTimeout(() => {
                    window.location.href = courseSearchURL;    
                }, 50);
            };
        },
        preFillForm() {
            if (this.user.isLoggedin) {
                this.signIn.mobile = this.userInfo.data.mobile;
            }

            if (this.$props.isOrg) {
                const orgID = YUNOCommon.getQueryParameter("org_id");
                const currentDateTime = this.formatDateTime(new Date());
                const orgId = "" + orgID + "@@@" + currentDateTime + "";
                this.signIn.org_details.org_id = this.utf8ToBase64(orgId);
            };
        },
        mobileUpdated(options, debug) {
            this.subform2.isLoading = false;

            const response = options?.response?.data;

            if (response?.code === 201) {
                this.showNotification(response.message, 'is-bottom', 'is-black');
                debug ? console.log(response.message) : "";
                
            } else if (response?.message) { 
                debug ? console.log(response.message) : "";
            }
        },
        showNotification(message, position, type = '') { 
            this.$buefy.toast.open({
                duration: 5000, // Notification duration in milliseconds
                message: `${message}`, // Notification message
                position: position, // Position of the notification
                type: type // Type of the notification (e.g., 'is-danger' for errors)
            });
        },
        updateMobile(debug) {
            this.subform2.isLoading = true;

            const payload = {
                "phone": this.signIn.mobile,
                "cta": "book_a_demo",
                "course": YUNOCommon.getQueryParameter("course_id") !== false ? YUNOCommon.getQueryParameter("course_id") : 0
            }

            const options = {
                apiURL: YUNOCommon.config.leadForm("postStep", isLoggedIn, this.$props.options.category), 
                module: "gotData", 
                store: "subform2", 
                payload: payload, 
                callback: true, 
                callbackFunc: (options) => this.mobileUpdated(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        initCommonSignup() {
            if (this.user.isLoggedin) {
                this.updateMobile();
                setTimeout(() => {
                    this.nextStep();    
                }, 50);
                
            } else {
                this.setState();
            }
        },
        addQueryParamUsingURLSearchParams(url, key, value) {
            let urlObj = new URL(url);
            // Append or overwrite the query parameter
            urlObj.searchParams.set(key, value);
            // Return the updated URL string
            return urlObj.toString();
        },
        setSigninProps() {
            let currentURL = window.location.origin + window.location.pathname + window.location.search,
                updatedURL = this.addQueryParamUsingURLSearchParams(currentURL, "stepsBegin", true);

            localStorage.setItem('userState', updatedURL);
            localStorage.setItem("isQuiz", true);
            localStorage.setItem('skipSignUp', true);
        },
        setPayload() {
            let payload = this.signIn;

            payload.categoryURL = this.$props.options.category;
            payload.landing_page.url = window.location.origin + window.location.pathname;
            payload.landing_page.title = document.title;
            payload.productCode = "";
            payload.leadStatus = "";
            payload.utmSource = YUNOCommon.getQueryParameter("utm_source");
            payload.utmCampaign = YUNOCommon.getQueryParameter("utm_campaign");
            payload.utmMedium = YUNOCommon.getQueryParameter("utm_medium");
            payload.adGroupID = YUNOCommon.getQueryParameter("adgroupid");
            payload.adContent = YUNOCommon.getQueryParameter("ad_content");
            payload.utmTerm = YUNOCommon.getQueryParameter("utm_term");
            payload.gclid = YUNOCommon.getQueryParameter("gclid");
            payload.content.type = "book_a_demo";
            payload.content.id = "";
        },
        isFirefoxPrivate(is_private) {
            if (is_private !== null && is_private) {
                setTimeout(() => {  
                    this.setSigninProps();
                    this.setPayload();
                    window.location.href = YUNOCommon.config.signInURLWithState(this.signIn)
                }, 5000); 
            } else {
                this.setSigninProps();
                this.setPayload();
                setTimeout(() => { window.location.href = YUNOCommon.config.signInURLWithState(this.signIn) }, 50); 
            }
        },
        setState() {
            YUNOCommon.isPrivateWindow(this.isFirefoxPrivate);
        },
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
        
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        utf8ToBase64(str) {
            // First, encode the string as UTF-8
            const utf8EncodedString = encodeURIComponent(str);
        
            // Then, convert each character to a binary representation
            const binaryString = utf8EncodedString.replace(/%([0-9A-F]{2})/g, function(match, p1) {
                return String.fromCharCode('0x' + p1);
            });
        
            // Finally, convert to Base64
            return btoa(binaryString);
        },
    }
});
Vue.component('yuno-schedule-demo', {
    props: {
        category: {
            type: String,
            required: true
        }
    },
    template: `
        <section class="loginStep demoSchedule">
            <div class="container">
                <div class="columns is-centered">
                    <a href="#" @click="preStep" class="pre" :class="[isDatePicked ? 'isShow' : '']"><span class="material-icons">arrow_back_ios</span></a>
                    <div class="column is-full-mobile" :class="[ isDatePicked ? 'is-10' : 'is-7' ]">
                        <div class="columns is-multiline is-centered hasBorder">
                            <div class="column step1 is-full-mobile hasRightBorder noBtmPaddingMobile" :class="[isDatePicked ? 'isHideInMobile' : '']">
                                <div class="block">
                                    <h2 class="smallCaption dark87">{{ showCategory(category) }}</h2>
                                </div>
                                <div class="block">
                                    <h2 class="smallCaption dark87">Time zone</h2>
                                    <p class="body1"> {{ getFormattedTimeZone() }}</p>
                                </div>
                            </div>
                            <div class="column step1 is-full-mobile datepickerWrapper" :class="[isDatePicked ? 'isHideInMobile' : '']">
                                <template v-if="subform.loading">
                                    <b-skeleton width="100%" height="342px"></b-skeleton>
                                </template>
                                <template v-else-if="subform.success && subform.error === null">
                                    <h2 class="smallCaption dark87">Select a Date & Time</h2>
                                    <b-datepicker 
                                        v-model="scheduleDate" 
                                        inline 
                                        :min-date="startOfToday()"
                                        class="yunoDatePicker"
                                        :date-formatter="formatDate"
                                        @input="onDatePick"
                                    >
                                    </b-datepicker>
                                </template>
                                <template v-else-if="subform.success">
                                    {{ subform.errorData }}
                                </template>
                            </div>
                            <div class="column step2 is-3 is-full-mobile hasLeftBorder" v-if="isDatePicked">
                                <template v-if="subform.loading">
                                    <div class="slotsWrapper">
                                        <div class="slot" v-for="(slot, i) in 1" :key="i">
                                            <h2 class="largeTitle dark87"><b-skeleton active height="24px" width="100%"></b-skeleton></h2>
                                            <div class="time">
                                                <b-skeleton 
                                                    v-for="(time, j) in 3" 
                                                    :key="j"
                                                    active height="40px" width="100%"
                                                >
                                                </b-skeleton>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="subform.success && subform.error === null && subform.data.length !== 0">
                                    <div class="slotsWrapper" v-if="isDateTimePicked">
                                        <div class="selectedSlot">
                                            <h2 class="smallCaption dark87">{{ convertDateString(payload.class_date_time) }}</h2>
                                            <p class="smallCaption">{{ startEndTime }} ({{ timezone }})</p>
                                            <p class="overline capital">Duration: {{ payload.class_duration + " minutes" }}</p>
                                            <b-button 
                                                @click="onFormSubmit()" 
                                                :loading="subform3.isLoading"
                                                :disabled="subform3.isLoading"
                                                class="primaryCTA"
                                                expanded
                                            >
                                                Confirm
                                            </b-button>
                                            <b-button 
                                                @click="onCancel()" 
                                                expanded
                                            >
                                                Cancel
                                            </b-button>
                                        </div>
                                    </div>
                                    <div class="slotsWrapper" v-else>
                                        <div class="slot" v-for="(slot, i) in subform.data.available_slots.time_slots" :key="i">
                                            <h2 class="smallCaption dark87">{{ convertDateString(slot.date + ' ' + slot.day_of_week) }}</h2>
                                            <div class="time">
                                                <b-button 
                                                    v-for="(time, j) in slot.slots" 
                                                    v-if="time.status" 
                                                    @click="onTimeSelect(time, slot.date)" 
                                                    :key="j">
                                                    {{ convertTo12HourFormat(time.starttime) }} - {{ convertTo12HourFormat(time.endtime) }}
                                                </b-button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="subform.success">
                                    {{ subform.errorData }}
                                </template>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </section>                            
        
    `,
    data() {
        return {
            scheduleDate: new Date(),
            isDateTimePicked: false,
            isDatePicked: false,
            startEndTime: "",
            timezone: "",
            payload: {
                class_title: "",
                class_date_time: "",
                class_duration: "",
                instructor_id: [],
                category_id: "",
                academy_id:"",
                learners: []
            },
            slotsPayload: {
                resource_id: [],
                start_date: "",
                end_date: "",
                start_time: "",
                end_time: "23:59",
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'subform',
            'categoryList',
            'form',
            'subform3'
        ]),
    },
    async created() {

    },
    mounted() {

    },
    methods: {
        showCategory(category) {
            return this.findObjectByKey(this.categoryList.data, 'categorySlug', category).category;
        },
        preStep() {
            this.isDatePicked = false;
            this.onCancel();
        },
        convertToISOString(dateStr) {
            // Expecting a string like "2025-03-14 Friday"
            // Split the string and take the first part
            const [datePart] = dateStr.split(' ');
            const dateObj = new Date(datePart);
            return dateObj.toISOString();
        },
        getFormattedTimeZone() {
            const dt = new Date();
            const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Get the short abbreviation, e.g., "EST"
            const shortParts = new Intl.DateTimeFormat('en-US', { timeZone, timeZoneName: 'short' }).formatToParts(dt);
            const shortName = shortParts.find(part => part.type === 'timeZoneName').value;

            // Get the full time zone name, e.g., "Eastern Standard Time"
            const longParts = new Intl.DateTimeFormat('en-US', { timeZone, timeZoneName: 'long' }).formatToParts(dt);
            const longName = longParts.find(part => part.type === 'timeZoneName').value;

            return `${shortName} (${longName})`;
        },
        onCancel() {
            this.isDateTimePicked = false;
        },
        readableDate(date) {

        },
        // Converts a string "YYYY-MM-DD DayName" to "Weekday, Month DaySuffix Year"
        convertDateString(dateStr) {
            let parsedDateStr = dateStr;
            // If the dateStr matches "YYYY-MM-DD DayName" then convert it to an ISO string.
            if (/^\d{4}-\d{2}-\d{2} \w+/.test(dateStr)) {
                parsedDateStr = this.convertToISOString(dateStr);
            }
            const dateObj = new Date(parsedDateStr);

            if (isNaN(dateObj.getTime())) {
                // Return the input string if date is invalid
                return dateStr;
            }
            // Retrieve weekday, month, day, and year
            const weekday = dateObj.toLocaleString('en-US', { weekday: 'long' });
            const month = dateObj.toLocaleString('en-US', { month: 'long' });
            const day = dateObj.getDate();
            const year = dateObj.getFullYear();

            // Determine the appropriate day suffix
            let suffix = 'th';
            if (day % 10 === 1 && day !== 11) {
                suffix = 'st';
            } else if (day % 10 === 2 && day !== 12) {
                suffix = 'nd';
            } else if (day % 10 === 3 && day !== 13) {
                suffix = 'rd';
            }
            return `${weekday}, ${month} ${day}${suffix} ${year}`;
        },
        // Updates the scheduleDate with the provided time string
        updateDateTime(scheduleDate, timeStr) {
            let hours, minutes;
            const parts = timeStr.split(' ');
            if (parts.length === 2) {
                // Format: "hh:mm AM/PM"
                const [timePart, period] = parts;
                [hours, minutes] = timePart.split(':').map(Number);
                if (period.toUpperCase() === 'PM' && hours < 12) {
                    hours += 12;
                } else if (period.toUpperCase() === 'AM' && hours === 12) {
                    hours = 0;
                }
            } else {
                // Format: "HH:mm"
                [hours, minutes] = timeStr.split(':').map(Number);
            }
            const updatedDate = new Date(scheduleDate);
            updatedDate.setHours(hours, minutes, 0, 0);
            return updatedDate.toString();
        },
        // Calculate duration between start and end time strings (either 12/24-hour format)
        calculateDuration(startTimeStr, endTimeStr) {
            const parseTime = (timeStr) => {
                let hrs, mins;
                const parts = timeStr.split(' ');
                if (parts.length === 2) {
                    const [timePart, period] = parts;
                    [hrs, mins] = timePart.split(':').map(Number);
                    if (period.toUpperCase() === 'PM' && hrs < 12) {
                        hrs += 12;
                    } else if (period.toUpperCase() === 'AM' && hrs === 12) {
                        hrs = 0;
                    }
                } else {
                    [hrs, mins] = timeStr.split(':').map(Number);
                }
                return hrs * 60 + mins;
            };

            const startMinutes = parseTime(startTimeStr);
            const endMinutes = parseTime(endTimeStr);
            let diff = endMinutes - startMinutes;
            if (diff < 0) diff += 24 * 60; // Adjust for overnight time differences
            return diff;
        },
        // Returns the first object in 'array' where item[key] equals 'value'
        findObjectByKey(array, key, value) {
            return array.find(item => item[key] === value);
        },
        // Handles the selection of a time slot
        onTimeSelect(time, date) {
            // Update class_date_time using the selected start time
            this.startEndTime = `${this.convertTo12HourFormat(time.starttime)} - ${this.convertTo12HourFormat(time.endtime)}`;
            this.payload.class_date_time = this.updateDateTime(date, time.starttime);
            this.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Calculate and set class duration if both start and end times are provided
            if (time.starttime && time.endtime) {
                this.payload.class_duration = this.calculateDuration(time.starttime, time.endtime);
            }

            // Get instructors from available_slots and update payload.instructor_id, if available
            if (
                this.subform.data &&
                this.subform.data.available_slots &&
                Array.isArray(this.subform.data.available_slots.instructors)
            ) {
                this.payload.instructor_id = this.subform.data.available_slots.instructors.map(instructor => instructor.id);
            }

            // Update payload with selected category, academy and learner info
            const categoryObj = this.findObjectByKey(this.categoryList.data, 'categorySlug', this.category);
            if (categoryObj) {
                this.payload.category_id = categoryObj.category_id;
            }
            this.payload.academy_id = YUNOCommon.getQueryParameter("academy_id") || 0;
            this.payload.learners.push(isLoggedIn);
            this.isDateTimePicked = true;
            // this.onFormSubmit(true);
        },
        formPosted(options, debug) {
            this.subform3.loading = false;
            this.subform3.isLoading = false;

            const response = options?.response?.data;

            debug ? console.log(response) : "";

            if (response?.code === 201) {

                if (response.data && response.data.data) {
                    this.subform3.data = response.data.data;
                }

                this.$emit("showThankyou")

                debug ? console.log(response.message) : "";
            } else if (response?.code === 400) {
                this.$buefy.toast.open({
                    duration: 3000,
                    message: response.message,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            } else if (response?.message) {
                debug ? console.log(response.message) : "";

                this.$buefy.toast.open({
                    duration: 3000,
                    message: response.message,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        onFormSubmit(debug) {
            this.subform3.loading = true;
            this.subform3.isLoading = true;

            const options = {
                apiURL: YUNOCommon.config.user("classSchedule", false),
                module: "gotData",
                store: "subform3",
                payload: this.payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.formPosted(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        formatDateForPayload(date) {
            const dateObj = new Date(date);
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        setCurrentTime() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        // Sets isDatePicked flag on date selection
        onDatePick(date) {
            this.isDatePicked = true;
            const subformData = JSON.parse(JSON.stringify(this.subform));

            if (
                subformData.data &&
                subformData.data.available_slots &&
                Array.isArray(subformData.data.available_slots.instructors)
            ) {
                this.slotsPayload.resource_id = subformData.data.available_slots.instructors.map(instructor => instructor.id);
            }

            this.slotsPayload.start_date = this.formatDateForPayload(date);
            this.slotsPayload.end_date = this.formatDateForPayload(date);
            this.slotsPayload.start_time = this.setCurrentTime();
            this.fetchSlots(true);
        },
        gotSlots(options, debug) {
            this.subform.loading = false;
            this.subform.isLoading = false;

            const response = options?.response?.data;

            if (response?.code === 201) {
                if (response.data && response.data.data) {
                    this.subform.data = response.data.data;
                }
                debug ? console.log(response.message) : "";
            } else if (response?.message) {
                debug ? console.log(response.message) : "";
            }
        },
        fetchSlots(debug) {
            this.subform.loading = true;
            this.subform.isLoading = true;
            this.subform.success = false;

            const options = {
                apiURL: YUNOCommon.config.user("slots", false),
                module: "gotData",
                store: "subform",
                payload: this.slotsPayload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.gotSlots(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        // Formats a given date into "Weekday DaySuffix Month"
        formatDate(date) {
            const options = { weekday: 'long', month: 'long' };
            const day = date.getDate();
            const suffix = this.getDaySuffix(day);
            const formattedDate = date.toLocaleDateString('en-GB', options);
            const [weekday, month] = formattedDate.split(' ');
            return `${weekday} ${day}${suffix} ${month}`;
        },
        // Returns the appropriate suffix for a given day
        getDaySuffix(day) {
            if (day > 3 && day < 21) return 'th';
            switch (day % 10) {
                case 1: return 'st';
                case 2: return 'nd';
                case 3: return 'rd';
                default: return 'th';
            }
        },
        // Returns a Date object representing the start of today
        startOfToday() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return today;
        },
        // Converts a 24-hour time string ("HH:mm") to 12-hour formatted string ("h:mm AM/PM")
        convertTo12HourFormat(time) {
            const [hoursStr, minutes] = time.split(':');
            const hours = parseInt(hoursStr, 10);
            const period = hours >= 12 ? 'PM' : 'AM';
            const adjustedHours = hours % 12 || 12;
            return `${adjustedHours}:${minutes} ${period}`;
        }
    }
});
Vue.component('yuno-step', {
    props: ["data", "options"],
    template: `
    <div class="card">
        <validation-observer 
            tag="div" 
            class="observer questionsWrapper"
            ref="commonSignupObserver" 
            v-slot="{ handleSubmit, invalid }">
            <form id="commonSignupForm" @submit.prevent="handleSubmit(initSteps)">
                <div class="card-content">
                    <a href="#" v-if="options.currentStep > 0" @click="preStep" class="pre"><span class="material-icons">arrow_back_ios</span></a>
                    <template v-if="data.type === 'agreeTerms'">
                        <validation-provider 
                            :rules="{required:true, is:payload.selected === false}"
                            class="agreeTerms"
                            tag="div"
                            v-slot="{ errors, classes }">
                            <b-field>
                                <b-checkbox 
                                    v-model="payload.selected"
                                    :true-value="true"
                                    :false-value="false"
                                    :class="classes"
                                >
                                    {{ data.label }}
                                </b-checkbox>
                            </b-field>
                            <p class="error">{{errors[0]}}</p>
                        </validation-provider>
                    </template>
                    <template v-else>
                        <validation-provider 
                            :customMessages="data.message"
                            :rules="data.validation" 
                            v-slot="{ errors, classes }">
                            <b-field class="optionSelector" :class="[data.name]">
                                <template #label>
                                    {{ data.label }}
                                    <small>{{ data.helper_label }}</small>    
                                </template>
                                <b-radio-button 
                                    v-for="(option, j) in data.items"
                                    :key="j"
                                    v-model="payload.selected"
                                    :native-value="option.slug"
                                    @input="onOptionSelect"
                                    :class="classes"
                                    type="is-success is-light is-outlined"
                                >
                                    <span>{{ option.label }}</span>
                                </b-radio-button>
                            </b-field>
                        </validation-provider>
                    </template>
                </div>
                <footer class="card-footer" v-if="manageCTAVisibility(data)">
                    <b-button class="card-footer-item primary" @click="nexStep">
                        Next
                    </b-button>
                </footer>
            </form>
        </validation-observer>
    </div>
    `,
    data() {
        return {
            payload: {
                name: "",
                selected: "",
                course: YUNOCommon.getQueryParameter("course_id") !== false ? YUNOCommon.getQueryParameter("course_id") : 0,
                cta: "book_a_demo",
                academy_id: YUNOCommon.getQueryParameter("academy_id") !== false ? YUNOCommon.getQueryParameter("academy_id") : 0,
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'subform',
            'categoryList',
            'form'
        ]),
    },
    async created() {
        
    },
    mounted() {
        this.preFillStep()
    },
    methods: {
        nexStep() {
            const currentStep = this.$props.options.currentStep + 1;

            if (currentStep === this.form.data.form_fields.length) {
                this.$emit("manageThankyou")
                this.initSteps();
            } else {
                Event.$emit('nextStep');
            };
        },
        manageCTAVisibility(data) {
            let setVisibility = "";

            if (data.selected !== "") {
                setVisibility = true
            } else {
                setVisibility = false
            }

            return setVisibility;
        },
        preFillStep() {
            const selected = this.$props.data.selected,
                stepCompleted = this.$props.data.step_completed;

            if (stepCompleted) {
                this.payload.selected = selected;
            }
        },
        preStep() {
            Event.$emit('preStep');
        },
        onOptionSelect(e) {
            this.resetSubform();
            this.initSteps();
        },
        resetSubform() {
            this.subform.error = null;
            this.subform.errorData = []
            this.subform.success = false
        },
        initSteps() {
            if (this.payload.selected !== "") {
                this.payload.name = this.$props.data.name;
                this.onFormSubmit();    

                if (this.payload.name === "which_ielts_exam_do_you_want_to_take" && this.payload.selected === "academic") {
                    YUNOCommon.removeObjInArr(this.form.data.form_fields, "name", "What_do_you_wish_to_pursue"); 
                };
            }

            Event.$emit('nextStep');
        },
        formPosted(options, debug) {
            this.subform.loading = false;
            this.subform.isLoading = false;

            const response = options?.response?.data;

            if (response?.code === 201) {
                
                if (response.data && response.data.data) {
                    this.subform.data = response.data.data;
                }
                debug ? console.log(response.message) : "";
            } else if (response?.message) { 
                debug ? console.log(response.message) : "";
            }
        },
        onFormSubmit(debug) {
            this.subform.loading = true;
            this.subform.isLoading = true;
            const currentStep = this.$props.options.currentStep + 1;
            

            if (currentStep === this.form.data.form_fields.length) {
                this.$emit("manageThankyou")
            };

            const options = {
                apiURL: YUNOCommon.config.leadForm("postStep", isLoggedIn, this.$props.options.category), 
                module: "gotData", 
                store: "subform", 
                payload: this.payload, 
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true, 
                callbackFunc: (options) => this.formPosted(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
    }
});
Vue.component('yuno-steps', {
    props: ["data", "options"],
    template: `
    <div>
        <template v-if="form.loading">
            <transition :name="transitionName" mode="out-in">
                <section class="loginStep">
                    <div class="container">
                        <div class="columns is-mobile is-centered">
                            <div class="column is-half-desktop is-full-mobile">
                                <div class="card">
                                    <div class="questionsWrapper">
                                        <div class="card-content">
                                            <div class="content">
                                                <b-skeleton size="is-large" :active="true" :count="2"></b-skeleton>
                                            </div>
                                        </div>
                                        <footer class="card-footer">
                                            <a class="card-footer-item">
                                                <b-skeleton size="is-large" :active="true"></b-skeleton>
                                            </a>
                                        </footer>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </transition>
        </template>
        <template v-if="form.success">
            <template v-if="form.error">
                error
            </template>
            <template v-else>
                <div class="loginStep noBtmGap hideInMobile">
                    <h2 class="largerSubheading">{{ data.questionTitle }}</h2>
                </div>
                <transition :name="transitionName" mode="out-in">
                    <div class="step-container" v-if="currentStep === index" v-for="(question, index) in form.data.form_fields" :key="question.id">
                        <section class="loginStep noTopGap">
                            <div class="container">
                                <div class="columns is-mobile is-centered">
                                    <div class="column is-half-desktop is-full-mobile">
                                        <yuno-step :data="question" :options="{'category': options.category, 'currentStep': currentStep}" @manageThankyou="manageThankyou" @initSteps="initSteps"></yuno-step>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </transition>
            </template>
        </template>
    </div>
    `,
    data() {
        return {
            currentStep: 0,
            transitionName: 'slide-left',
        }
    },
    computed: {
        ...Vuex.mapState([
            'form'
        ]),
    },
    async created() {
        this.emitEvents();
    },
    mounted() {
        
    },
    methods: {
        initSteps() {
            this.$emit('initSteps');
        },
        manageThankyou() {
            this.$emit("manageThankyou")
        },
        nextStep() {
            this.transitionName = 'slide-left';

            if (this.currentStep < this.form.data.form_fields.length - 1) {
              this.currentStep++;
            }
        },
        preStep() {
            this.transitionName = 'slide-right';

            if (this.currentStep > 0) {
                this.currentStep--;
            };
        },
        emitEvents() {
            Event.$on('nextStep', () => {
                this.nextStep();
            });

            Event.$on('preStep', () => {
                this.preStep();
                this.initSteps();
            });
        }
    }
});
Vue.component('yuno-thank-you', {
    props: ["data", "options"],
    template: `
    <section class="loginStep topGap">
        <div class="container">
            <div class="columns is-mobile is-centered">
                <div class="column is-three-fifths-desktop is-full-mobile">
                    <div class="card thankyou">
                        <span class="material-icons-outlined">thumb_up</span>
                        <h3 class="largerTitle">{{ title }}</h3>
                        <h4 class="smallerCaption dark87">{{ "Dear " + userInfo.data.yuno_display_name }}</h4>
                        <p class="body1">{{ description }}</p>
                        <b-button class="primary" tag="a" :href="ctaURL" @click="manageRedirection($event)">
                            {{ manageCTALabel('See all ' + data.label + ' Classes') }}
                        </b-button>
                    </div>
                </div>
            </div>    
        </div>
    </section>
    `,
    data() {
        return {
            title: "Thank You for Your Responses!",
            description: "Thank you for taking the time to submit your answers! Your thoughtful responses are invaluable to us, and we appreciate the effort you put into providing us with this information.",
            cta: "See all IELTS Classes",
            ctaURL: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'subform',
            'categoryList',
            'form',
            'userInfo',
            'categoryList'
        ]),
    },
    async created() {
        
    },
    mounted() {
        this.manageCTA()
    },
    methods: {
        manageCTALabel(str) {
            const urlParams = new URLSearchParams(window.location.search);
            const ctaLabel = urlParams.get('cta');

            if (ctaLabel) {
                return ctaLabel;
            } else {
                return str;
            }
        },
        manageRedirection(e) {
            const urlParams = new URLSearchParams(window.location.search);
            const userid = urlParams.get('user_id');
            const token = urlParams.get('yuno_token');

            if (userid && token) {
                e.preventDefault();
                
                const redirectUrl = urlParams.get('redirect_url');
                window.location.href = redirectUrl;
            }
        },
        manageCTA() {
            const category = YUNOCommon.findObjectByKey(this.categoryList.data, "categorySlug", this.$props.options.category);
            this.ctaURL = "/search/?state=%7B%22limit%22:20,%22offset%22:0,%22personalization%22:%22all%22,%22category%22:%5B"+ category.category_id +"%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22selected%22:%5B%5D,%22slug%22:%22class_days%22%7D,%7B%22selected%22:%5B%5D,%22slug%22:%22class_time%22%7D%5D,%22instructor_id%22:0,%22price_per_hour%22:10000,%22total_duration%22:24%7D";
        }
    }
});