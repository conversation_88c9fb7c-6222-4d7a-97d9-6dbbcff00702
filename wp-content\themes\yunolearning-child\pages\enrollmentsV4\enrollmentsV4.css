#app .dark87, #app .mainHeader .gridInfo .note span, #app .mainHeader .gridInfo .actions, #app .mainHeader .gridInfo .actions li a {
  color: rgba(0, 0, 0, 0.87);
}

#app .dark60, #app .mainHeader .gridInfo .note {
  color: rgba(0, 0, 0, 0.6);
}

#app .dark38 {
  color: rgba(0, 0, 0, 0.38);
}

#app .pageGrid .mainBody {
  position: relative;
  z-index: 7;
}

#app .mainHeader {
  margin: 15px 0;
  background-color: #FFF;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
}

@media (max-width: 767px) {
  #app .mainHeader {
    margin: 30px 0 15px 0;
    padding-left: 18px;
  }
}

#app .mainHeader .block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0;
}

#app .mainHeader .pageTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .mainHeader .pageTitle a {
  position: relative;
  top: 4px;
  font-size: 18px;
  margin-left: 5px;
}

#app .mainHeader .pageTitle a:hover {
  text-decoration: none;
}

#app .mainHeader .gridInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-top: 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .mainHeader .gridInfo .note {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .mainHeader .gridInfo .note span {
  font-weight: 500;
}

#app .mainHeader .gridInfo .actions {
  text-decoration: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -5px;
}

#app .mainHeader .gridInfo .actions li {
  padding: 0 5px;
}
/*# sourceMappingURL=enrollmentsV4.css.map */