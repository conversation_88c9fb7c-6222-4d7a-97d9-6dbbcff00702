<?php
return [
    'id' => 'integer', // id of academy Example: [29874],
    'name' => 'string', // Name of the academy Example: ['shavys english hut'],
    'logo_url' => 'Refer#Image', // Logo image url of the Academy Example: ['https://dev.yunolearning.com/wp-content/uploads/2024/09/shavy_jain_logo_-_khushpreet_kaur__1_.png'],
    'fav_icon_url' => 'Refer#Image', // Fav icon URL of the Academy,
    'banner_image_url' => 'Refer#Image', // favicon image url of academy,
    'short_description' => 'string', // excerpt of academy Example: ['Shavy’s English Hut is a leading institute specializing in activity-based learning methods for English language development. We offer engaging, interactive classes tailored for both young children and adults, providing a fresh and fun approach to mastering English.'],
    'long_description' => 'string', // description of academy Example: ["Founded in 2016 by <PERSON><PERSON><PERSON>, a CELTA-certified trainer and content creator, <PERSON><PERSON><PERSON>’s English Hut is known for its innovative, activity-based learning methods, combining storytelling, puppetry, and interactive techniques to create memorable learning experiences for all ages. We are a certified Jolly Phonics and Jolly Grammar Institute, catering to students across India through our online platform. Beyond conventional learning, we focus on helping professionals and businesses excel in communication skills through engaging corporate training sessions. Whether it's delivering impactful pitches, crafting effective business emails, or improving meeting communication, our goal is to enhance confidence and clarity in English communication."],
    'active_learners' => 'integer',  // Active learners of academy Example: [4]
    'past_learners' =>  'integer', // past active learners of academy Example: [5]
    'org' => 'Refer#Organization_Minimal', // The organization this academy belongs to,
    'category' => [
        'Refer#Category_Minimal'
    ], // The different subject categories that the academy teaches
    'courses' => [
        'Refer#Course_Minimal'
    ], // Courses of the academy Example: [2]
    'instructors' => [
        'Refer#User_Minimal'
    ], // Sum of all the instructors who can teach all the courses offered by the academy. An instructor who can teach a course is "mapped" to the course Example: [3]
    'created_time' => 'Refer#Date_Time'  // Date and time when the academy was created Example: ['2024-09-10 10:10:10']  
];
