<?php
/**
 * Attendance Controller
 */
class AttendanceController extends WP_REST_Controller
{
    
    function __construct()
    {
        $this->namespace = 'yuno/v1';
        $this->resource_name_1 = '/attendance/(?P<role>[a-zA-Z0-9-]+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
    }

    public function register_routes() {     
        
        register_rest_route($this->namespace, $this->resource_name_1, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'get_attendance_insights'),
				'permission_callback' => array($this, 'check_access_permissions_check'),
				'args'                => array(),
			),
		));
    }

    /**
     * Token authorization check
     * This function common for all post login apis  
     */
    public function check_access_permissions_check(WP_REST_Request $request) {
        return true;
    	$authToken = $request->get_header('authorization');
		if (empty($authToken)) { return false; }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) { return true; } else { return false; }

        }
        $codes=error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true){
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData); 
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData); 
        }
    }
    
    /**
     * Get Attendance Insights
     */

     public static function mustQueries_attendance($data)
    {
        
        $mustQueries = [];
        // Add dynamic match queries for basic details filters
        if (isset($data['details_filters']) && is_array($data['details_filters'])) {
        foreach ($data['details_filters'] as $key => $value) {
            if ($value != "") {
            if ($value === "false") {
                // Match records where the field is false or doesn't exist
                $mustQueries[] = [
                "bool" => [
                    "should" => [
                    ["match" => ["data.details.$key" => false]],
                    ["bool" => ["must_not" => ["exists" => ["field" => "data.details.$key"]]]]
                    ]
                ]
                ];
            }elseif ($value === "true") {
                // Match records where the field is true
                $mustQueries[] = [
                "match" => [
                    "data.details.$key" => true
                ]
                ];
            } else {
                $mustQueries[] = [
                "match" => [
                    "data.details.$key" => $value
                ]
                ];
            }
            }
        }
        }

        if (isset($data['cat_filters']) && is_array($data['cat_filters'])) {
        foreach ($data['cat_filters'] as $key => $value) {
            if ($value != "") {
            $mustQueries[] = [
                "bool" => [
                    "must" => [
                        [
                            "match" => [
                                "data.details.{$key}" => $value
                            ]
                        ]
                    ]
                ]
            ];
            }
        }
        }

        if (isset($data['instructor_role'])){
            $instructor_id = $data['instructor_role']['instructor_id'];
            $mustQueries[] = [
                "bool" => [
                    "must" => [
                        [
                            "term" => [
                                "data.details.instructor_id" => $instructor_id
                            ]
                        ]
                    ]
                ]
            ];
        }

        if (isset($data['orgadmin_role'])){
            $orgs = $data['orgadmin_role']['org_ids'];
            $mustQueries[] = [
                "bool" => [
                    "must" => [
                        [
                            "terms" => [
                                "data.details.org_admin.id" => $orgs 
                            ]
                        ]
                    ]
                ]
            ];
        }

        if (isset($data['counselor_role'])){
            $counselor_id = $data['counselor_role']['counselor_id'];
            $mustQueries[] = [
                "bool" => [
                    "must" => [
                        [
                            "term" => [
                                "data.details.counselor_id" => $counselor_id
                            ]
                        ]
                    ]
                ]
            ];
        }


        if (isset($data['search_filters']) && is_array($data['search_filters'])) {
            foreach ($data['search_filters'] as $key => $value) {
                $keyname = '';
                $searchValue = '';
        
                if ($key == "course" && isset($value['id'])) {
                    $keyname = "course_id";
                    $searchValue = $value['id'];
                } elseif ($key == "instructor" && isset($value['user_id'])) {
                    $keyname = "instructor_id";
                    $searchValue = $value['user_id'];
                } elseif ($key == "counselor" && isset($value['user_id'])) {
                    $keyname = "counselor_id";
                    $searchValue = $value['user_id'];
                }
        
                if ($keyname != '' && $searchValue != '') {
                    $mustQueries[] = [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.{$keyname}" => $searchValue
                                    ]
                                ]
                            ]
                        ]
                    ];
                }
            }
        }

        if (isset($data['course_filter']['course_name']) && $data['course_filter']['course_name'] === true) {
            $mustQueries[] = [
                "bool" => [
                    "must_not" => [
                        "match" => ["data.details.course_name" => false]
                    ]
                ]
            ];
        }

        return $mustQueries;
    }

    private function getAttendancePercentageRange($value)
    {
        $ranges = [
            "25 percent" => ["lt" => 25.0],
            "33 percent" => ["lt" => 33.0],
            "40 percent" => ["lt" => 40.0],
            "50 percent" => ["lt" => 50.0],
            "66 percent" => ["lt" => 66.0],
            "75 percent" => ["lt" => 75.0],
            "80 percent" => ["lt" => 80.0],
            "85 percent" => ["lt" => 85.0],
            "90 percent" => ["lt" => 90.0],
            "95 percent" => ["lt" => 95.0],
            "100 percent" => ["lt" => 100.0]
        ];

        return $ranges[$value] ?? [];
    }


    public function rangeQueries_attendance($data)
    {

        $rangeFilters = [];

        if (isset($data['case'])) {
            if ($data['case'] == "get_attendance_insights") {
                $to_sort = "event_date";
            }
        } else {
            $to_sort = "basic_details";
        }
        if (isset($data['event_date']) && !empty($data['event_date'])) {
            foreach ($data['event_date'] as $field => $range) {
                if ($range != "") {
                    $rangeFilters[] = [
                        "range" => [
                            "data.details.$to_sort" => [
                                "gte" => $range,
                            ]
                        ]
                    ];
                }
            }
        }

        // if (isset($data['attendance_filter']) && !empty($data['attendance_filter'])) {
        //     $attendancePercentageRange = $this->getAttendancePercentageRange($data['attendance_filter']);
        //     if (!empty($attendancePercentageRange)) {
        //         $rangeFilters[] = [
        //             "range" => [
        //                 "data.details.attendance_percentage" => $attendancePercentageRange
        //             ]
        //         ];
        //     }
        // }
        if (isset($data['attendance_filter']) && !empty($data['attendance_filter'])) {
            $maxPercentage = intval($data['attendance_filter']); 
        
            if ($maxPercentage > 0) {
                $scriptQuery = [
                    "script" => [
                        "script" => [
                            "source" => "
                                int enrolledCount = doc['data.details.enrolled_classes_collection'].size() > 0 ? doc['data.details.enrolled_classes_collection'].size() : 0;
                                int attendedCount = doc['data.details.attended_classes_collection'].size() > 0 ? doc['data.details.attended_classes_collection'].size() : 0;
                                if (enrolledCount > 0) {
                                    double percentage = (attendedCount / (double) enrolledCount) * 100;
                                    return percentage <= params.max_percentage;
                                } else {
                                    return false;
                                }
                            ",
                            "params" => [
                                "max_percentage" => $maxPercentage
                            ]
                        ]
                    ]
                ];
        
                $rangeFilters[] = $scriptQuery;
            }
        }

        if (isset($data['ranges']) && is_array($data['ranges'])) {
            foreach ($data['ranges'] as $field => $range) {
              if ($range != "" && $field !== 'grid_date') {
                $rangeFilters[] = [
                  "range" => [
                    "data.details.$to_sort" => [
                      "gte" => $range,
                    ]
                  ]
                ];
              }
            }
          }

        return $rangeFilters;
    }

    function getClassTimes($class_ids, $data) {
        $index_name = "enrolledclassevent";
        $date_ranges = [
            "24-hours" => "-1 day",
            "7-days" => "-7 days",
            "30-days" => "-30 days",
            "90-days" => "-90 days"
        ];
    
        $rangeFilters = [];
        if (isset($data['ranges']) && is_array($data['ranges'])) {
            foreach ($data['ranges'] as $field => $range) {
              if ($range != "") {
                $rangeFilters[] = [
                  "range" => [
                    "data.details.$field" => [
                      "gte" => $range,
                    ]
                  ]
                ];
              }
            }
          }

          if (isset($data['root_filters']) && is_array($data['root_filters'])) {
            foreach ($data['root_filters'] as $key => $value) {
                if ($key == "class_time" && $value !== "all") {
                    $time_ranges = [
                        "morning" => ["gte" => "06:00:00", "lt" => "12:00:00"],
                        "afternoon" => ["gte" => "12:00:00", "lt" => "16:00:00"],
                        "evening" => ["gte" => "16:00:00", "lt" => "20:00:00"],
                        "night" => ["gte" => "20:00:00", "lt" => "23:59:59"]
                    ];
        
                    if (isset($time_ranges[$value])) {
                        $time_range = $time_ranges[$value];
                        $rangeFilters[] = [
                            "script" => [
                                "script" => [
                                    "source" => "
                                        def eventTime = doc['data.details.event_date'].value.toOffsetDateTime().toLocalTime();
                                        def startTime = LocalTime.parse(params.gte);
                                        def endTime = LocalTime.parse(params.lt);
                                        return !eventTime.isBefore(startTime) && eventTime.isBefore(endTime);
                                    ",
                                    "lang" => "painless",
                                    "params" => [
                                        "gte" => $time_range['gte'],
                                        "lt" => $time_range['lt']
                                    ]
                                ]
                            ]
                        ];
                    }
                }
            }
        }
    
        $query = [
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            "terms" => [
                                "data.details.id" => $class_ids
                            ]
                        ]
                    ],
                    "filter" => $rangeFilters
                ]
            ],
            "_source" => ["data.details.time", "data.details.id"]
        ];

        // print_r(json_encode($query));exit;
    
        $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . ELASTIC_RECORDS_COUNT;
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => $params,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION =>CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $results = json_decode($response, true);
        curl_close($curl);
    
        $class_times = [];
        foreach ($results['hits']['hits'] as $hit) {
            $class_id = $hit['_source']['data']['details']['id'];
            $class_time = $hit['_source']['data']['details']['time'];
            $class_time_24 = date("H:i", strtotime($class_time));
            if ($class_time_24 < "12:00") {
                $time_slot = "morning";
            } elseif ($class_time_24 < "14:00") {
                $time_slot = "afternoon";
            } elseif ($class_time_24 < "20:00") {
                $time_slot = "evening";
            } else {
                $time_slot = "night";
            }
            $class_times[$class_id] = $time_slot;
        }
    // print_r(json_encode($results));exit;
        return $class_times;
    }
    

    // public function get_private_classes_all($data)
    // {
    //     $codes = error_code_setting();
    //     $mustQueries = $this->mustQueries_attendance($data);
    //     $rangeQueries = $this->rangeQueries_attendance($data);

    //     if (isset($data['ranges']['grid_date']) && !empty($data['ranges']['grid_date'])) {
    //         $grid_date = $data['ranges']['grid_date'];
    //     } else {
    //         $grid_date = date("Y-m-d H:i:s", strtotime("-4 months"));
    //     }

    //     $time_ranges = [
    //         "morning" => ["gte" => "06:00:00", "lt" => "12:00:00"],
    //         "afternoon" => ["gte" => "12:00:00", "lt" => "16:00:00"],
    //         "evening" => ["gte" => "16:00:00", "lt" => "20:00:00"],
    //         "night" => ["gte" => "20:00:00", "lt" => "23:59:59"]
    //     ];
        
    //     $class_time_filter = [];
    //     if (isset($data['root_filters']['class_time']) && $data['root_filters']['class_time'] !== "all") {
    //         $class_time = $data['root_filters']['class_time'];
    //         if (isset($data['root_filters']['class_time']) && $data['root_filters']['class_time'] !== "all") {
    //             $class_time = $data['root_filters']['class_time'];
                
    //             if (isset($time_ranges[$class_time])) {
    //                 $time_range = $time_ranges[$class_time];
                    
    //                 $class_time_filter[] = [
    //                     "script" => [
    //                         "script" => [
    //                             "source" => "
    //                                 def eventTime = doc['data.details.event_date'].value.toOffsetDateTime().toLocalTime();
    //                                 def startTime = LocalTime.parse(params.gte);
    //                                 def endTime = LocalTime.parse(params.lt);
    //                                 return !eventTime.isBefore(startTime) && eventTime.isBefore(endTime);
    //                             ",
    //                             "lang" => "painless",
    //                             "params" => [
    //                                 "gte" => $time_range['gte'],  
    //                                 "lt"  => $time_range['lt']    
    //                             ]
    //                         ]
    //                     ]
    //                 ];
    //             }
            
                
    //         }
    //     }
    //     if (isset($data['ranges']['grid_date']) && !empty($data['ranges']['grid_date'])) {
    //         $grid_date = $data['ranges']['grid_date'];

    //         $class_time_filter[] = [
    //             "range" => [
    //                 "data.details.event_date" => [
    //                     "gte" => $grid_date    
    //                 ]
    //             ]
    //         ];
    //     }
        
    //     if (!empty($data['sorting'])) {
    //         $curlPost = [
    //             "query" => [
    //                 "bool" => [
    //                     "must" => $mustQueries,
    //                     "filter" => [
    //                         "bool" => [
    //                             "must" => array_merge($rangeQueries, $class_time_filter)  
    //                         ]
    //                     ]
    //                 ]
    //             ],
    //             "aggs" => [
    //                 "total_learners" => [
    //                     "cardinality" => [
    //                         "field" => "data.details.user_id"
    //                     ]
    //                 ],
    //                 "average_attendance_class_percentage" => [
    //                     "avg" => [
    //                         "script" => [
    //                             "source" => "
    //                                 if (doc['data.details.enrolled_classes_counter.keyword'].size() > 0 && doc['data.details.attended_classes_counter.keyword'].size() > 0) {
    //                                     def enrolled = Double.parseDouble(doc['data.details.enrolled_classes_counter.keyword'][0]);
    //                                     def attended = Double.parseDouble(doc['data.details.attended_classes_counter.keyword'][0]);
    //                                     return (enrolled > 0) ? (attended / enrolled) * 100 : 0;
    //                                 } else {
    //                                     return 0;
    //                                 }
    //                             "
    //                         ]
    //                     ]
    //                 ],
    //                 "average_attendance_duration_percentage" => [
    //                     "avg" => [
    //                         "script" => [
    //                             "source" => "
    //                                 if (doc['data.details.total_actual_time_in_classes_.keyword'].size() > 0 && doc['data.details.total_time_in_classes_.keyword'].size() > 0) {
    //                                     def actual_time = Double.parseDouble(doc['data.details.total_actual_time_in_classes_.keyword'][0]);
    //                                     def total_time = Double.parseDouble(doc['data.details.total_time_in_classes_.keyword'][0]);
    //                                     return (actual_time > 0) ? (total_time / actual_time) * 100 : 0;
    //                                 } else {
    //                                     return 0;
    //                                 }
    //                             "
    //                         ]
    //                     ]
    //                 ]
    //                         ],
    //             "sort" => [
    //                 [
    //                     "_script" => [
    //                         "type" => "string",
    //                         "script" => [
    //                             "source" => "if (params['_source']['data']['details'] != null && params['_source']['data']['details']['" . $data['sorting']['column'] . "'] != null) { params['_source']['data']['details']['" . $data['sorting']['column'] . "'] } else { '1970-01-01T00:00:00Z' }"
    //                         ],
    //                         "order" => $data['sorting']['type']
    //                     ]
    //                 ]
    //             ]
    //         ];
    //     } else {
    //         $curlPost = [
    //             "query" => [
    //                 "bool" => [
    //                     "must" => $mustQueries,
    //                     "filter" => [
    //                         "bool" => [
    //                             "must" => array_merge($rangeQueries, $class_time_filter)  
    //                         ]
    //                     ]
    //                 ]
    //             ]
    //         ];
    //     }

    //     $jsonQuery = json_encode($curlPost, JSON_PRETTY_PRINT);

    //     $index_name = $data['index'] ?? 'batchenrollmentevent';

    //     $method = "GET";
    //     $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
    //     $curl = curl_init();
    //     curl_setopt_array(
    //         $curl,
    //         array(
    //             CURLOPT_PORT => ELASTIC_SEARCH_PORT,
    //             CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search?size=" . ELASTIC_RECORDS_COUNT,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_ENCODING => "",
    //             CURLOPT_MAXREDIRS => 10,
    //             CURLOPT_TIMEOUT => 30,
    //             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //             CURLOPT_CUSTOMREQUEST => $method,
    //             CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
    //             CURLOPT_HTTPHEADER => array(
    //                 "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
    //                 "cache-control: no-cache",
    //                 "content-type: application/json"
    //             ),
    //         )
    //     );
    //     $response = curl_exec($curl);
    //     $results = json_decode($response);

    //     $count = $results->hits->total->value ?? $results->hits->total ?? 0;

    //     $resp = [
    //         'code' => $count > 0 ? $codes["POST_INSERT"]["code"] : $codes["POST_INSERT_FAIL"]["code"],
    //         'message' => $count > 0 ? 'Results found' : 'No results found',
    //         'data' => ['status' => $count > 0 ? $codes["POST_INSERT"]["code"] : $codes["POST_INSERT_FAIL"]["code"]]
    //     ];

    //     $total_attended_classes = 0;
    //     $total_enrolled_classes = 0;
    //     $total_time_in_classes = 0;
    //     $total_actual_time_in_classes = 0;
    //     $unique_users = [];
    //     $learner_attended_classes = 0;
    //     $learner_enrolled_classes = 0;

    //     $class_ids = [];

    //     if ($data['view'] == "grid-view") {
    //         $grid_view = [];
    //         foreach ($results->hits->hits as $res) {
    //             $root = $res->_source->data->details;
    //             $user_id = $root->user_id;

    //             if (!isset($unique_users[$user_id])) {
    //                 $actions = [];
    //                 foreach ($data['actions'] as $action) {
    //                     $row1 = [
    //                         'label' => ucwords(str_replace('_', ' ', $action)),
    //                         'is_active' => true,
    //                         'slug' => $action,
    //                         'url' => "/learner-profile?learner={$root->user_id}",
    //                         'link_target' => "_blank",
    //                         'active_class' => "material-icons-outlined",
    //                         'icon' => [
    //                             'type' => "mdl",
    //                             'class' => "material-icons-outlined",
    //                             'hover' => "material-icons",
    //                             'value' => "timeline"
    //                         ]
    //                     ];
    //                     $actions[] = $row1;
    //                 }

    //                 $unique_users[$user_id] = [
    //                     'learner' => [
    //                         'name' => $root->name,
    //                         'image' => $root->image,
    //                         'user_id' => $root->user_id
    //                     ],
    //                     'org' => $root->org_admin->name ?? '',
    //                     'enrollment_status' => $root->enrollment_status,
    //                     'last_class' => '',
    //                     'recent_classes' => [],
    //                     // 'actions' => $actions,
    //                     'total_attended_classes' => 0,
    //                     'total_enrolled_classes' => 0,
    //                     'total_time_in_classes' => 0,
    //                     'total_actual_time_in_classes' => 0
    //                 ];
    //             }

    //             $attended_classes_counter = $root->attended_classes_counter ?? 0;
    //             $enrolled_classes_counter = $root->enrolled_classes_counter ?? 0;
    //             $total_time_in_classes_ = $root->total_time_in_classes_ ?? 0;
    //             $total_actual_time_in_classes_ = $root->total_actual_time_in_classes_ ?? 0;

    //             // Collect enrolled class IDs
    //             if (!empty($root->enrolled_classes_collection)) {
    //                 // $class_ids = array_merge($class_ids, $root->enrolled_classes_collection);
    //                 $class_ids = array_unique(array_merge($class_ids, $root->enrolled_classes_collection));
    //             }

    //             $attendance_class = $enrolled_classes_counter > 0 ? ($attended_classes_counter / $enrolled_classes_counter) * 100 : 0;
    //             $attendance_duration = $total_actual_time_in_classes_ > 0 ? ($total_time_in_classes_ / $total_actual_time_in_classes_) * 100 : 0;

    //             // Aggregate recent classes from all objects
    //             $enrolled_classes = $root->enrolled_classes_collection ?? [];
    //             $attended_classes_collection = $root->attended_classes_collection ?? [];

    //             foreach ($enrolled_classes as $class_id) {
    //                 $recent_class = [
    //                     'attended' => in_array($class_id, $attended_classes_collection) ? 'Yes' : 'No',
    //                     'course_title' => $root->course_name,
    //                     'batch_id' => $root->batch_id,
    //                     'batch_name' => $root->batch_name,
    //                     'class_id' => $class_id,
    //                     'user_id' => $root->user_id,
    //                     'attendance_class' => round($attendance_class, 2) . '%',
    //                     'attendance_duration' => round($attendance_duration, 2) . '%',
    //                     'instructor' => [
    //                         'id' => $root->instructor_id,
    //                         'image' => $root->instructor_image,
    //                         'name' => $root->instructor_name
    //                     ],
    //                     'category' => $root->category
    //                 ];
    //                 $unique_users[$user_id]['recent_classes'][] = $recent_class;
    //             }

    //             $unique_users[$user_id]['total_attended_classes'] += $attended_classes_counter;
    //             $unique_users[$user_id]['total_enrolled_classes'] += $enrolled_classes_counter;
    //             $unique_users[$user_id]['total_time_in_classes'] += (int) $total_time_in_classes_;
    //             $unique_users[$user_id]['total_actual_time_in_classes'] += (int) $total_actual_time_in_classes_;

    //             $unique_users[$user_id]['last_class'] = [
    //                 'date' => $root->event_date,
    //                 'attended' => $attended_classes_counter > 0 ? 'Yes' : 'No'
    //             ];

    //             $total_attended_classes += $attended_classes_counter;
    //             $total_enrolled_classes += $enrolled_classes_counter;
    //             $total_time_in_classes += (int) $total_time_in_classes_;
    //             $total_actual_time_in_classes += (int) $total_actual_time_in_classes_;

    //             $learner_attended_classes += $attended_classes_counter;
    //             $learner_enrolled_classes += $enrolled_classes_counter;
    //         }

    //         // Fetch class times for all collected class IDs
    //         $class_times = [];
    //         if (!empty($class_ids)) {
    //             $class_times = $this->getClassTimes($class_ids, $data);
    //         }

    //         // Sort recent classes in descending order by class_id
    //         foreach ($unique_users as &$user) {
    //             // Filter recent classes to include only those returned by getClassTimes
    //             $user['recent_classes'] = array_filter($user['recent_classes'], function ($recent_class) use ($class_times) {
    //                 return isset($class_times[$recent_class['class_id']]);
    //             });

    //             usort($user['recent_classes'], function($a, $b) {
    //                 return $b['class_id'] - $a['class_id'];
    //             });

    //             // Limit recent classes to top 10
    //             if (count($user['recent_classes']) > 10) {
    //                 $user['recent_classes'] = array_slice($user['recent_classes'], 0, 10);
    //             }

    //             // Add time slot information to recent classes
    //             foreach ($user['recent_classes'] as &$recent_class) {
    //                 $class_id = $recent_class['class_id'];
    //                 $class_time = $class_times[$class_id];

    //                 $recent_class['time_slot'] = $class_time;
    //             }

    //             $user['attendance_percentage_class_count'] = $user['total_enrolled_classes'] > 0 ? round(($user['total_attended_classes'] / $user['total_enrolled_classes']) * 100, 2)  : '0';
    //             $user['attendance_percentage_class_duration'] = $user['total_actual_time_in_classes'] > 0 ? round(($user['total_time_in_classes'] / $user['total_actual_time_in_classes']) * 100, 2)  : '0';

    //             $actions = []; 
    //             foreach ($data['actions'] as $action) {
    //                 $actions[] = [
    //                     'label' => ucwords(str_replace('_', ' ', $action)),
    //                     'is_active' => true,
    //                     'slug' => $action,
    //                     'url' => "/learner-profile?learner={$user['learner']['user_id']}",
    //                     'link_target' => "_blank",
    //                     'active_class' => "material-icons-outlined",
    //                     'icon' => [
    //                         'type' => "mdl",
    //                         'class' => "material-icons-outlined",
    //                         'hover' => "material-icons",
    //                         'value' => "timeline"
    //                     ]
    //                 ];
    //             }
    //             $user['actions'] = $actions;

    //             unset($user['total_attended_classes']);
    //             unset($user['total_enrolled_classes']);
    //             unset($user['total_time_in_classes']);
    //             unset($user['total_actual_time_in_classes']);
    //         }

    //         usort($unique_users, function($a, $b) {
    //             return strtotime($b['last_class']['date']) - strtotime($a['last_class']['date']);
    //         });

    //         $grid_view['rows'] = array_values($unique_users);
    //         $sortable = ["learner", "last_class"];
    //         foreach ($grid_view['rows'][0] as $key => $value) {
    //             $row = [
    //                 'field' => $key,
    //                 'label' => ucwords(str_replace('_', ' ', $key)),
    //                 'sortable' => in_array($key, $sortable),
    //                 'is_active' => true
    //             ];
    //             $grid_view['columns'][] = $row;
    //         }

    //         $users_count = count($unique_users);
    //         if ($data['limit'] && $data['limit'] < $users_count) {
    //             $unique_users = array_slice($unique_users, 0, $data['limit']);
    //         }

    //         $learners_count = count($grid_view['rows']);
    //         $attendance_percentage_class_count_total = $learner_enrolled_classes > 0 ? ($learner_attended_classes / $learner_enrolled_classes) * 100 : 0;
    //         $attendance_percentage_class_duration_total = $total_actual_time_in_classes > 0 ? ($total_time_in_classes / $total_actual_time_in_classes) * 100 : 0;

    //         $resp['data']['count'] = $learners_count;
    //         $resp['data']['attendance_percentage_class_count'] = round($attendance_percentage_class_count_total, 2) . '%';
    //         $resp['data']['attendance_percentage_class_duration'] = round($attendance_percentage_class_duration_total, 2) . '%';
    //         $resp['data']['grid_view'] = $grid_view;
    //     }

    //     else {
    //         $list_view = [];
    //         $resp['data']['list_view'] = $list_view;
    //     }
    //     $filter_data = [];
    //     foreach ($data['filters'] as $key) {
    //     $row = [];
    //     $row['filter'] = $key;
    //     $row['is_active'] = true;
    //     $row['selected'] = $data[$key];
    //     $row['loading'] = false;
    //     $row['success'] = false;
    //     $row['current'] = "";

    //     $row['placeholder'] = ucwords(str_replace('_', ' ', $key));

    //     if ($data['filter'][$key]) {
    //         foreach ($data['filter'][$key] as $val) {
    //             if($key === "attendance_percentage"){
    //                 if($val === "25%"){
    //                   $label = "Lower than 25%";
    //                 }elseif($val === "33%"){
    //                   $label = "Lower than 33%";
    //                 }elseif($val === "40%"){
    //                   $label = "Lower than 40%";
    //                 }elseif($val === "50%"){
    //                   $label = "Lower than 50%";
    //                 }elseif($val === "66%"){
    //                   $label = "Lower than 66%";
    //                 }elseif($val === "75%"){
    //                     $label = "Lower than 75%";
    //                 }elseif($val === "80%"){
    //                     $label = "Lower than 80%";
    //                 }elseif($val === "85%"){
    //                     $label = "Lower than 85%";
    //                 }elseif($val === "90%"){
    //                     $label = "Lower than 90%";
    //                 }elseif($val === "95%"){
    //                     $label = "Lower than 95%";
    //                 }elseif($val === "100%"){
    //                     $label = "Lower than 100%";
    //                 }
    //                 elseif($val === "all"){
    //                   $label = "All";
    //                 }

    //                 $row['type'] = "dropdown";
    //                 $items = [];
    //                 $items['slug'] = $val ;
    //                 $items['label'] = $label;
    //                 $items['filter'] = $key;
    //                 $row['items'][] = $items;

    //               }elseif($key === "class_time"){
    //                 if($val === "morning"){
    //                   $label = "6am - 12pm (Morning)";
    //                 }elseif($val === "afternoon"){
    //                   $label = "12pm - 2pm (Afternoon)";
    //                 }elseif($val === "evening"){
    //                   $label = "2pm - 8pm (Evening)";
    //                 }elseif($val === "night"){
    //                   $label = "8pm - 12pm (Night)";
    //                 }elseif($val === "all"){
    //                   $label = "All";
    //                 }
    //                 $row['type'] = "dropdown";
    //                 $items = [];
    //                 $items['slug'] = $val ;
    //                 $items['label'] = $label;
    //                 $items['filter'] = $key;
    //                 $row['items'][] = $items;

    //               }elseif($key === "classes_happened"){
    //                 if($val === "24-hours"){
    //                     $label = "Only in the last 24 hours";
    //                 }elseif($val === "7-days"){
    //                     $label = "Only in the last 7 days";
    //                 }elseif($val === "30-days"){
    //                     $label = "Only in the last 30 days";
    //                 }elseif($val === "90-days"){
    //                     $label = "Only in the last 90 days";
    //                 }
    //                 elseif($val === "all"){
    //                   $label = "All";
    //                 }
                    
    //                 $row['type'] = "dropdown";
    //                 $items = [];
    //                 $items['slug'] = $val ;
    //                 $items['label'] = $label;
    //                 $items['filter'] = $key;
    //                 $row['items'][] = $items;
    //               }
    //               elseif($key === "category"){
                   
    //                   $taxonomy = 'course_category'; 
    //                   $term_slug = $val; 
    //                   $term = get_term_by('slug', $term_slug, $taxonomy);
                
    //                  if($term){
    //                   $term_name = $term->name;
    //                   $term_id = $term->term_id;
    //                  }else{
    //                   $term_name = "All";
    //                   $term_id = "";
    //                  }
                    
    //                  $row['type'] = "dropdown";
    //                  $items = [];
    //                  $items['slug'] = $val;
    //                  $items['label'] = $term_name;
    //                  $items['filter'] = $key;
    //                  $row['items'][] = $items;
      
    //               }
    //               else{
    //                 $row['type'] = "dropdown";
    //                 $items = [];
    //                 $items['slug'] = $val;
    //                 $items['label'] = ucfirst(str_replace(['-', '_'], ' ', $val));
    //                 $items['filter'] = $key;
    //                 $row['items'][] = $items;
    //               }
    //         }
    //     } else {
    //         $row['type'] = "autocomplete_search";
    //         $row['items'] = [];
    //     }
    //     $filter_data[] = $row;
    //     }
    //     $resp['data']['filters'] = $filter_data;

    //     return $resp;
    // }
    public function getClassInfo($class_id, $user_id)
    {
        $index_name = "privateclass";
        // $query = [
        //     "query" => [
        //         "bool" => [
        //             "must" => [
        //                 ["term" => ["data.details.class_id" => $class_id]],
        //                 ["term" => ["data.details.user_id" => $user_id]]
        //             ]
        //         ]
        //     ]
        // ];
        $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_doc/privateclass-".$class_id;
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => $params,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                //CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                    "content-type: application/json"
                ),
            )
        );
        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);
        if ($error) {
            return [
                'learner_class_datetime' => ''
            ];
        }
        $responseData = json_decode($response, true);
        $classData = $responseData['_source']['data']['details'];
        return [
            'learner_class_datetime' => $classData['class_actual_date_time'] ?? "",
        ];
    }

    private function filter_learners_by_class_time($learners, $data) {
        $all_class_ids = [];
    
        foreach ($learners as $learner) {
            $attended_classes = $learner->_source->data->details->attended_classes_collection ?? [];
            foreach ($attended_classes as $cid) {
                $all_class_ids[] = $cid;
            }
        }
    
        $all_class_ids = array_values(array_unique($all_class_ids));
        if (empty($all_class_ids)) return [];
    
        $time_filters = [];
    
        if (!empty($data['ranges']['grid_date'])) {
            $time_filters[] = [
                "range" => [
                    "data.details.class_date_time" => [
                        "gte" => $data['ranges']['grid_date']
                    ]
                ]
            ];
        }
    
        if (!empty($data['root_filters']['class_time']) && $data['root_filters']['class_time'] !== 'all') {
            $slot = $data['root_filters']['class_time'];
            $time_ranges = [
                "morning" => ["gte" => "06:00:00", "lt" => "12:00:00"],
                "afternoon" => ["gte" => "12:00:00", "lt" => "16:00:00"],
                "evening" => ["gte" => "16:00:00", "lt" => "20:00:00"],
                "night" => ["gte" => "20:00:00", "lt" => "23:59:59"]
            ];
    
            if (isset($time_ranges[$slot])) {
                $tr = $time_ranges[$slot];
    
                $time_filters[] = [
                    "script" => [
                        "script" => [
                            "source" => "
                                if (!doc.containsKey('data.details.class_date_time') || doc['data.details.class_date_time'].empty) {
                                    return false;
                                }
                                def eventTime = doc['data.details.class_date_time'].value.toOffsetDateTime().toLocalTime();
                                def startTime = LocalTime.parse(params.gte);
                                def endTime = LocalTime.parse(params.lt);
                                return !eventTime.isBefore(startTime) && eventTime.isBefore(endTime);
                            ",
                            "lang" => "painless",
                            "params" => [
                                "gte" => $tr['gte'],
                                "lt" => $tr['lt']
                            ]
                        ]
                    ]
                ];
            }
        }
    
        $query = [
            "query" => [
                "bool" => [
                    "must" => [
                        ["terms" => ["data.details.record_id" => $all_class_ids]]
                    ],
                    "filter" => $time_filters
                ]
            ],
            "_source" => ["data.details.record_id"]
        ];
    
        $url = ELASTIC_SEARCH_END_URL . "/privateclass/_search?size=10000";
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($query),
            CURLOPT_HTTPHEADER => [
                "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "Cache-Control: no-cache",
                "Content-Type: application/json"
            ],
        ]);
    
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response, true);
    
        $valid_classes = array_map(function ($hit) {
            return $hit['_source']['data']['details']['record_id'];
        }, $results['hits']['hits'] ?? []);
    
        $valid_classes = array_flip($valid_classes);
        $filtered_learners = [];
    
        foreach ($learners as $learner) {
            $attended = $learner->_source->data->details->attended_classes_collection ?? [];
            if (!empty(array_intersect($attended, array_keys($valid_classes)))) {
                $filtered_learners[] = $learner;
            }
        }
    
        return $filtered_learners;
    } 

    public function get_private_classes_all($data) {
        $codes = error_code_setting();
        $mustQueries = $this->mustQueries_attendance($data);
        $rangeQueries = $this->rangeQueries_attendance($data);
    
        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $mustQueries,
                    "filter" => [
                        "bool" => [
                            "must" => $rangeQueries
                        ]
                    ]
                ]
            ],
            "aggs" => [
                "total_learners" => [
                    "cardinality" => [
                        "field" => "data.details.user_id"
                    ]
                ],
                "average_attendance_class_percentage" => [
                    "avg" => [
                        "script" => [
                            "source" => "if (doc['data.details.enrolled_classes_counter.keyword'].size() > 0 && doc['data.details.attended_classes_counter.keyword'].size() > 0 && !doc['data.details.enrolled_classes_counter.keyword'][0].isEmpty() && !doc['data.details.attended_classes_counter.keyword'][0].isEmpty()) { def enrolled = Double.parseDouble(doc['data.details.enrolled_classes_counter.keyword'][0]); def attended = Double.parseDouble(doc['data.details.attended_classes_counter.keyword'][0]); return (enrolled > 0) ? (attended / enrolled) * 100 : 0; } else { return 0; }"
                        ]
                    ]
                ],
                "average_attendance_duration_percentage" => [
                    "avg" => [
                        "script" => [
                            "source" => "if (doc['data.details.total_actual_time_in_classes_.keyword'].size() > 0 && doc['data.details.total_time_in_classes_.keyword'].size() > 0 && !doc['data.details.total_actual_time_in_classes_.keyword'][0].isEmpty() && !doc['data.details.total_time_in_classes_.keyword'][0].isEmpty()) { def actual_time = Double.parseDouble(doc['data.details.total_actual_time_in_classes_.keyword'][0]); def total_time = Double.parseDouble(doc['data.details.total_time_in_classes_.keyword'][0]); return (actual_time > 0) ? (total_time / actual_time) * 100 : 0; } else { return 0; }"
                        ]
                    ]
                ]
            ],
            "sort" => [
                [
                    "data.details.event_date" => "desc"
                ]
            ]
        ];
    
        $index_name = $data['index'] ?? 'batchenrollmentevent';
        $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
    
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => $params,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ],
        ]);
    
        $response = curl_exec($curl);
        $results = json_decode($response);
        curl_close($curl);
    
        $raw_learners = $results->hits->hits ?? [];
    
        $learner_count = $results->aggregations->total_learners->value ?? 0;
    
        if (
            (!empty($data['ranges']['grid_date']) || !empty($data['root_filters']['class_time']))
            && !empty($raw_learners)
        ) {
            $raw_learners = $this->filter_learners_by_class_time($raw_learners, $data);

            if(empty($raw_learners)){
                $learner_count = 0;
            }

        }
    
        $attendance_percentage_class_count_total = round($results->aggregations->average_attendance_class_percentage->value ?? 0, 2);
        $attendance_percentage_class_duration_total = round($results->aggregations->average_attendance_duration_percentage->value ?? 0, 2);
    
        $resp = [
            'code' => $learner_count > 0 ? $codes["POST_INSERT"]["code"] : $codes["POST_INSERT_FAIL"]["code"],
            'message' => $learner_count > 0 ? 'Results found' : 'No results found',
            'data' => [
                'status' => $learner_count > 0 ? $codes["POST_INSERT"]["code"] : $codes["POST_INSERT_FAIL"]["code"],
                'count' => $learner_count,
                'attendance_percentage_class_count' => $attendance_percentage_class_count_total . '%',
                'attendance_percentage_class_duration' => $attendance_percentage_class_duration_total . '%'
            ]
        ];
    
        if ($data['view'] == "grid-view") {
            $grid_view = [];
            $class_ids = [];
    
            foreach ($raw_learners as $res) {
                $root = $res->_source->data->details;
                $user_id = $root->user_id;
                $actions = [];
    
                foreach ($data['actions'] as $action) {
                    $actions[] = [
                        'label' => ucwords(str_replace('_', ' ', $action)),
                        'is_active' => true,
                        'slug' => $action,
                        'url' => "/learner-profile?learner={$user_id}",
                        'link_target' => "_blank",
                        'active_class' => "material-icons-outlined",
                        'icon' => [
                            'type' => "mdl",
                            'class' => "material-icons-outlined",
                            'hover' => "material-icons",
                            'value' => "timeline"
                        ]
                    ];
                }
    
                $user_data = [
                    'learner' => [
                        'name' => $root->name,
                        'image' => $root->image,
                        'user_id' => $root->user_id
                    ],
                    'org' => $root->org_admin->name ?? '',
                    'enrollment_status' => $root->enrollment_status,
                    'last_class' => '',
                    'recent_classes' => []
                ];
    
                $enrolled_classes = $root->enrolled_classes_collection ?? [];
                $attended_classes = $root->attended_classes_collection ?? [];
                $highest_class_id = !empty($enrolled_classes) ? max($enrolled_classes) : "";
                $highest_class = ['attended' => 'No', 'date' => ""];
    
                if (in_array($highest_class_id, $attended_classes)) {
                    $learner_class_datetime = $this->getClassInfo($highest_class_id, $root->user_id);
                    $highest_class = ['attended' => 'Yes', 'date' => $learner_class_datetime['learner_class_datetime']];
                }
    
                $user_data['last_class'] = $highest_class;
    
                foreach ($enrolled_classes as $class_id) {
                    $attended = in_array($class_id, $attended_classes) ? 'Yes' : 'No';
                    $user_data['recent_classes'][] = [
                        'attended' => $attended,
                        'course_title' => $root->course_name,
                        'batch_id' => $root->batch_id,
                        'batch_name' => $root->batch_name,
                        'class_id' => $class_id,
                        'user_id' => $root->user_id,
                        'instructor' => [
                            'id' => $root->instructor_id,
                            'image' => $root->instructor_image,
                            'name' => $root->instructor_name
                        ]
                    ];
                    $class_ids[] = $class_id;
                }
    
                $user_data['total_recent_classes_count'] = count($user_data['recent_classes']);
                $countEnrolled = count($enrolled_classes);
                $countAttended = count($attended_classes);
    
                $totalTimeInClass = $root->total_time_in_classes_;
                $actualTimeInClass = $root->total_actual_time_in_classes_;
    
                $user_data['attendance_percentage_class_count'] = $countEnrolled > 0 ? round(($countAttended / $countEnrolled) * 100, 2) : '0';
                $user_data['attendance_percentage_class_duration'] = $actualTimeInClass > 0 ? round(($totalTimeInClass / $actualTimeInClass) * 100, 2) : '0';
                $user_data['actions'] = $actions;
    
                $grid_view['rows'][] = $user_data;
            }
    
            $class_times = [];
            if (!empty($class_ids)) {
                $class_times = $this->getClassTimes($class_ids, $data);
            }
    
            foreach ($grid_view['rows'] as &$user) {
                if (count($user['recent_classes']) > 10) {
                    $user['recent_classes'] = array_slice($user['recent_classes'], -10);
                }
            }
    
            $sortable = ["learner", "last_class"];
            foreach ($grid_view['rows'][0] as $key => $value) {
                if ($key === 'total_recent_classes_count') continue;
                $grid_view['columns'][] = [
                    'field' => $key,
                    'label' => ucwords(str_replace('_', ' ', $key)),
                    'sortable' => in_array($key, $sortable),
                    'is_active' => true
                ];
            }
    
            $resp['data']['grid_view'] = $grid_view;
        } else {
            $resp['data']['list_view'] = [];
        }
    
        $filter_data = [];
        foreach ($data['filters'] as $key) {
            $row = [
                'filter' => $key,
                'is_active' => true,
                'selected' => $data[$key],
                'loading' => false,
                'success' => false,
                'current' => "",
                'placeholder' => ucwords(str_replace('_', ' ', $key)),
            ];
    
            if (in_array($key, ["course", "instructor", "counselor"])) {
                $row['type'] = "autocomplete_search";
                $row['items'] = [];
            } else {
                $row['type'] = "dropdown";
                $items = [];
                foreach ($data['filter'][$key] as $val) {
                    $label = ucfirst($val);
                    if ($key === "attendance_percentage") {
                        $label = "Lower than " . $val;
                    } elseif ($key === "class_time") {
                        $label = ucwords(str_replace("_", " ", $val));
                    }
                    $items[] = [
                        'slug' => $val,
                        'label' => $label,
                        'filter' => $key
                    ];
                }
                $row['items'] = $items;
            }
    
            $filter_data[] = $row;
        }
    
        $resp['data']['filters'] = $filter_data;
    
        return $resp;
    }



    public function get_attendance_insights($request)
    {
        $codes = error_code_setting();
        $role = $request['role']; 
        $typeCollectionArray = array("learner", "yuno-admin", "instructor", "org-admin", "counselor");
    
        if (!in_array($role, $typeCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the user role', array('status' => $codes["GET_FAIL"]["status"]));
        }
        $data = $request->get_params();   
        $data['case'] = "get_attendance_insights";
        $filters = ["attendance_percentage", "category", "course", "enrollment_status", "instructor", "counselor", "class_time", "classes_happened"];
        $attendance_per = ["all", "25%", "33%", "40%", "50%", "66%", "75%", "80%", "85%", "90%", "95%", "100%"];
        $categories = ["all", "ielts", "pte", "english-speaking", "vedic-maths", "anil-lamba-on-finance", "coding-for-kids"];
        $enrollment_stat = ["all", "active", "inactive"];
        $class_time = ["all", "morning", "afternoon", "evening", "night"];
        $classes_events = ["classes_happened"];
        $happened = ["all", "24-hours", "7-days", "30-days", "90-days"];
        $actions = ["user_history"];
        $attendance_percent = ['attendance_percentage'];
        $enroll_status = ['enrollment_status'];
        $root_filters_keys = ["class_time"];

        $cat_filters_keys = ["category"];
        $search_fields = ["course", "instructor", "counselor"];

        if (isset($data['org_id']) && !empty($data['org_id'])) {
            $filters[] = "academy";

            if (isset($data['academy_id']) && !empty($data['academy_id'])) {

                $data['filter']['academy'] = (array)$data['academy_id'];
                $data['cat_filters_academy']['academy'] = (array)$data['academy_id'];
            } else {
                $data['filter']['academy'] = get_post_meta($data['org_id'], 'academies', true);
                $data['cat_filters_academy']['academy'] = get_post_meta($data['org_id'], 'academies', true);
            }
        }

        $logged_in_user_id = CURRENT_LOGGED_IN_USER_ID;
        if($role == 'instructor'){
            $data['instructor_role']["instructor_id"] = $logged_in_user_id;
        }elseif($role == 'org-admin'){
            $orgs = get_user_meta($logged_in_user_id, 'organisation', true);
            $data['orgadmin_role']['org_ids'] = $orgs;
            $org_id = (int) $request['org'];
            if($org_id){
                if (!empty($orgs) && in_array($org_id, $orgs)) {
                    $data['orgadmin_role']['org_ids'] = (array)$org_id;
                } else {
                    return new WP_Error($codes["GET_FAIL"]["code"], 'You are not part of this organisation', array('status' => $codes["GET_FAIL"]["status"]));
                }
            }
        }elseif($role == 'counselor'){
            $data['counselor_role']['counselor_id'] = $logged_in_user_id;
        }

        $data['filters'] = $filters;
        $data['actions'] = $actions;
        $data['filter']['category'] = $categories;
        $data['filter']['enrollment_status'] = $enrollment_stat;
        $data['filter']['class_time'] = $class_time;
        $data['filter']['classes_happened'] = $happened;
        $data['filter']['attendance_percentage'] = $attendance_per;
        $data['course_filter']['course_name'] = true;
        
        foreach ($data as $key => $value) {
            if (!is_array($value) && strtolower($value) == "all") {
				$value = "";
				continue;
			}
            
            if (in_array($key, $classes_events)) {
                if ($value == "24-hours") {
					$value = "1-days";
				}
                if ($value != "" && $value !== "all") {
                    $val = str_replace('-days', ' days', $value);
                    $val = date("Y-m-d H:i:s", strtotime("-{$val}", strtotime(date("Y-m-d H:i:s"))));
                } else {
                    $val = "";
                }
                if ($key == "classes_happened") {
					$data['ranges']["grid_date"] = $val;
				}
                
            }
            elseif (in_array($key, $attendance_percent)) {
                
                if ($value != "" && $value !== "all") {
                    $val = str_replace('%', ' percent', $value);
                    
                } else {
                    $val = "";
                }
                if ($key == "attendance_percentage") {
                    $data["attendance_filter"] = $val;
                }
            }
            elseif (in_array($key, $enroll_status)){
                if ($key == "enrollment_status") {
                    $data["details_filters"][$key] = $value;
                }
            }
             elseif (in_array($key, $cat_filters_keys)) {
                $data['cat_filters'][$key] = $value;
            }
            elseif (in_array($key, $search_fields)) {
                $data['search_filters'][$key] = $value;
            }
            if (in_array($key, $root_filters_keys)) {
				$data['root_filters'][$key] = $value;
			}
        }
        $private_classes = $this->get_private_classes_all($data);
        // echo json_encode($private_classes);
        return new WP_REST_Response($private_classes, $codes["GET_SUCCESS"]["code"]);

    }
   
}
?>