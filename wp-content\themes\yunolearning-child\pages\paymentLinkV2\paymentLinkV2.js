window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minium 10 numbers required",
        "max": "Maxium 15 numbers required",
        "is": "This field is required",
        "required_if": ""
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-payment-link-v2', {
    template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
        >
            <template v-slot:main>
                <b-loading :is-full-page="true" :active="isInProcess" :can-cancel="false"></b-loading>
                <section id="paymentLink" class="container-fluid formSection paymentLink">
                    <template v-if="isFormLoading">
                        <div class="container hasTopGap">
                            <figure class="infiniteSpinner">
                                <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                            </figure>
                        </div>
                    </template>
                    <template v-if="isFormReady">
                        <h1 class="sectionTitle">Enroll Learner</h1> 
                        <validation-observer ref="paymentLinkObserver" v-slot="{handleSubmit, invalid}">
                            <form id="paymentLinkForm" @submit.prevent="handleSubmit(initForm)">
                                <div class="row">
                                    <div class="col-12 col-md-7">
                                        <div class="formWrapper">
                                            <div id="previousFields" :class="{'noBtmGap': paymentLink.form.isInstallment}">
                                                <b-field label="Learner">
                                                    <validation-provider 
                                                        tag="div"
                                                        :customMessages="{ isNotBlank: 'Please select the learner from list' }"
                                                        :rules="{required:true, isNotBlank:learner.selected}" 
                                                        v-slot="{ errors, classes }">
                                                        <b-autocomplete
                                                            :class="classes"
                                                            v-model="learner.current"
                                                            :data="learner.data"
                                                            autocomplete="paymentLinkLearner"
                                                            :loading="learner.isLoading"
                                                            placeholder="Search by name, email or phone"
                                                            :field="userRole.data === 'org-admin' ? 'name' : 'name_email'"
                                                            @typing="getLearners"
                                                            @select="onLearnerSelect($event)"
                                                            :clearable="true">
                                                            <template slot="empty">No results for {{learner.current}}</template>
                                                        </b-autocomplete>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="Academy" v-if="userRole.data === 'org-admin'">
                                                    <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <template v-if="subform2.loading">
                                                            <b-skeleton height="40px"></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <b-select
                                                                :class="classes"
                                                                v-model="academy"
                                                                placeholder="Select Academy"
                                                                :loading="subform2.loading"
                                                                @input="dropdownChange($event)"
                                                            >
                                                                <option
                                                                    v-for="(option, j) in subform2.data"
                                                                    :key="j"
                                                                    :value="option.value"
                                                                >
                                                                    {{ option.label }}
                                                                </option>
                                                            </b-select>
                                                        </template>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="Course & Batch">
                                                    <validation-provider 
                                                        tag="div"
                                                        :customMessages="{ isNotBlank: 'Please select the course from list' }"
                                                        :rules="{required:true, isNotBlank:course.selected}" 
                                                        v-slot="{ errors, classes }">
                                                        <template v-if="allCourses.loading">
                                                            <b-skeleton height="40px"></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <b-autocomplete
                                                                :class="classes"
                                                                v-model="course.current"
                                                                :data="filterCourse"
                                                                autocomplete="paymentLinkCourse"
                                                                placeholder="Search course"
                                                                field="search"
                                                                @select="onCourseSelect($event)"
                                                                :clearable="true">
                                                                <template slot="empty">No results for {{learner.current}}</template>
                                                                <template slot-scope="props">
                                                                    <div class="courseList">
                                                                        <figure class="img">
                                                                            <img v-if="props.option.image !== ''" width="40" height="40" :src="props.option.image" :alt="props.option.label">
                                                                            <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                                                        </figure>
                                                                        <div class="courseContent">
                                                                            <h4 class="courseTitle" v-html="props.option.course_title"></h4>
                                                                            <div class="groupContent">
                                                                                <span class="instructorName" v-if="props.option.name">{{props.option.name}},</span>
                                                                                <span class="dateTime">{{props.option.date}}</span>
                                                                                <span class="batchLabel" v-if="props.option.batch_label">({{props.option.batch_label}})</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </b-autocomplete>
                                                        </template>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="Course" v-if="false">
                                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <b-select 
                                                            :class="classes"
                                                            @input="onCourseChange($event)"
                                                            v-model="paymentLink.form.courseID"
                                                            placeholder="Select">
                                                            <option value="">Select</option>
                                                            <option
                                                                v-for="(course, courseIndex) in allCourses.data"
                                                                :value="course.post_id"
                                                                :key="courseIndex">
                                                                {{ course.product_code }}
                                                            </option>
                                                        </b-select>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="Batch" v-if="false">
                                                    <validation-provider tag="div" class="paymentLinkBatch" :rules="{required:true, isBatchSelected:filteredBatchObj.length}" v-slot="{ errors, classes }">
                                                        <b-autocomplete
                                                            :class="classes"
                                                            v-model="paymentLink.form.selectedBatch"
                                                            :data="filteredBatchObj"
                                                            placeholder="Search by name"
                                                            field="term_name"
                                                            @select="onBatchSelect($event)"
                                                            :clearable="true">
                                                        </b-autocomplete>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <div class="radioList groupElement">
                                                    <p class="fieldLabel">Payment type</p>
                                                    <small class="helper">Choose one option</small>
                                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <div class="field colorGrey">
                                                            <b-radio 
                                                                v-model="paymentLink.form.paymentType"
                                                                native-value="One time payment"
                                                                name="paymentType"
                                                                @input="isInstallments(paymentLink.form.paymentType)">
                                                                One time payment
                                                            </b-radio>
                                                        </div>
                                                        <div class="field colorGrey">
                                                            <b-radio 
                                                                v-model="paymentLink.form.paymentType"
                                                                native-value="Payment in installments"
                                                                name="paymentType"
                                                                @input="isInstallments(paymentLink.form.paymentType)">
                                                                Payment in installments
                                                            </b-radio>
                                                        </div>
                                                        <div class="field colorGrey">
                                                            <b-radio 
                                                                v-model="paymentLink.form.paymentType"
                                                                native-value="Offline payment"
                                                                name="paymentType"
                                                                @input="isInstallments(paymentLink.form.paymentType)">
                                                                Enroll without payment through Yuno
                                                            </b-radio>
                                                        </div>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </div>
                                                <div class="referralCode" v-if="userRole.data !== 'org-admin' && referral.payment_method !== ''">
                                                    <b-field label="Referral code">
                                                        <validation-provider 
                                                            :rules="{required: false, isEmpty: referral.referral_code}" v-slot="{ errors, classes }">
                                                            <b-input v-model="referral.referral_code"></b-input>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-button
                                                        :loading="referralLoading ? true : false"
                                                        :disabled="referral.referral_code === '' ? true : false || referralLoading ? true : false" 
                                                        @click="initReferral"
                                                        class="yunoPrimaryCTA wired">
                                                        <template v-if="hasReferralCode">
                                                            Remove Code
                                                        </template>
                                                        <template v-else>
                                                            Apply
                                                        </template>
                                                    </b-button>
                                                </div>
                                                <b-field label="Org" v-if="userRole.data === 'yuno-admin' && createPayment.payload.payment_mode === 'offline' || userRole.data === 'Counselor' && createPayment.payload.payment_mode === 'offline'">
                                                    <template v-if="orgAdmin.isLoading">
                                                        <div class="smallLoader withField"></div>
                                                    </template>
                                                    <template v-if="orgAdmin.success">
                                                        <validation-provider :rules="{required:false}" v-slot="{ errors, classes }">
                                                            <b-select 
                                                                :class="classes"
                                                                v-model="createPayment.payload.org_id"
                                                                placeholder="Select"
                                                                @input="resumeWatcher"
                                                            >
                                                                <option value="">Select</option>
                                                                <option
                                                                    v-for="(item, itemIndex) in orgAdmin.data"
                                                                    :value="item.id"
                                                                    :key="itemIndex">
                                                                    {{ item.organisation_name }}
                                                                </option>
                                                            </b-select>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </template>
                                                </b-field>
                                                <template v-if="updateLink.success && createPayment.payload.org_id !== ''">
                                                    <b-field label="Org User Email">
                                                        <validation-provider 
                                                            :rules="{required: false, email: true}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_user_email"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org User ID">
                                                        <validation-provider 
                                                            :rules="{required: false, numeric: true}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_user_id"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org User Phone">
                                                        <validation-provider 
                                                            :rules="{required: false, numeric: true}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_user_phone"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org Username">
                                                        <validation-provider 
                                                            :rules="{required: false}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_user_name"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org CRM ID">
                                                        <validation-provider 
                                                            :rules="{required: false}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_crm_id"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org Cohort">
                                                        <validation-provider 
                                                            :rules="{required: false}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_cohort"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org Programs">
                                                        <validation-provider 
                                                            :rules="{required: false}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_programs"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Org Business Unit">
                                                        <validation-provider 
                                                            :rules="{required: false}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="createPayment.payload.org_business_unit"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <div class="groupElement">
                                                        <p class="fieldLabel">Org Parents</p>
                                                        <b-field label="Name">
                                                            <validation-provider 
                                                                :rules="{required: false}" v-slot="{ errors, classes }">
                                                                <b-input :class="classes" v-model="org_parent.name"></b-input>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>
                                                        </b-field>
                                                        <b-field label="Email">
                                                            <validation-provider 
                                                                :rules="{required: false}" v-slot="{ errors, classes }">
                                                                <b-input :class="classes" v-model="org_parent.email"></b-input>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>
                                                        </b-field>
                                                        <b-field label="Phone">
                                                            <validation-provider 
                                                                :rules="{required: false}" v-slot="{ errors, classes }">
                                                                <b-input :class="classes" v-model="org_parent.phone"></b-input>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>
                                                        </b-field>
                                                    </div>
                                                </template>
                                                <b-field label="Counsellor" v-if="userRole.data === 'yuno-admin' && counsellor.isActive">
                                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <b-select 
                                                            :class="classes"
                                                            v-model="createPayment.payload.counselor_id"
                                                            placeholder="Select">
                                                            <option value="">Select</option>
                                                            <option
                                                                v-for="(item, itemIndex) in counsellorList.data"
                                                                :value="item.id"
                                                                :key="itemIndex">
                                                                {{ item.name_email }}
                                                            </option>
                                                        </b-select>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="How did the learner pay?" v-if="paymentMethod.isActive">
                                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <b-select 
                                                            :class="classes"
                                                            v-model="createPayment.payload.payment_gateway"
                                                            placeholder="Select">
                                                            <option value="">Select</option>
                                                            <option
                                                                v-for="(item, itemIndex) in paymentMethod.list"
                                                                :value="item.slug"
                                                                :key="itemIndex">
                                                                {{ item.label }}
                                                            </option>
                                                        </b-select>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <div class="inlineFields">
                                                    <b-field label="Currency">
                                                        <b-select 
                                                            v-model="paymentLink.payload.currency">
                                                            <option value="INR">INR</option>
                                                        </b-select>
                                                    </b-field>
                                                    <b-field label="Total Amount">
                                                        <validation-provider :rules="{required:true, numeric:true, greaterThen:0}" v-slot="{ errors, classes }">
                                                            <b-input :class="classes" v-model="paymentLink.form.amount"></b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field v-if="paymentLink.form.isInstallment" label="Number of instalments">
                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-select 
                                                                :class="classes"
                                                                v-model="createPayment.payload.total_instalments"
                                                                placeholder="Select">
                                                                <option value="" selected>Select</option>
                                                                <option
                                                                    v-for="(installment, installmentIndex) in paymentLink.form.howManyInstallments"
                                                                    :key="installmentIndex"
                                                                    :value="installment">
                                                                    {{installment}}
                                                                </option>
                                                            </b-select>  
                                                            <p class="error">{{errors[0]}}</p>  
                                                        </validation-provider>
                                                    </b-field>
                                                </div>
                                                <b-field label="Instalment 1" v-if="paymentLink.form.isInstallment">
                                                    <validation-provider :rules="{required:paymentLink.form.isNextSlide, numeric:true, greaterThen:0}" v-slot="{ errors, classes }">
                                                        <b-input :class="classes" v-model="createPayment.payload.instalment_amount"></b-input>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                                <b-field label="Note for customer">
                                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                        <b-input 
                                                            :class="classes"
                                                            v-model="paymentLink.payload.payment_description"
                                                            type="textarea">
                                                        </b-input>
                                                        <p class="error">{{errors[0]}}</p>
                                                    </validation-provider>
                                                </b-field>
                                            </div>
                                            <div id="nextFields" v-if="paymentLink.form.isInstallment">
                                                <b-field label="Note for self">
                                                    <b-input 
                                                        v-model="createPayment.payload.self_notes"
                                                        type="textarea">
                                                    </b-input>
                                                </b-field>
                                            </div>
                                        </div>
                                        <div class="ctaWrapper">
                                            <b-button
                                                :loading="paymentLink.isLoading === true ? true : false"
                                                :disabled="paymentLink.isLoading === true ? true : false" 
                                                native-type="submit"
                                                class="yunoSecondaryCTA">
                                                    Enroll
                                            </b-button>
                                        </div>
                                    </div>
                                </div>
                            </form>    
                        </validation-observer>
                    </template>
                </section>
                <b-modal 
                    :active.sync="paymentLink.successModal" 
                    :width="450" 
                    :can-cancel="false" 
                    :on-cancel="onModalClose"
                    class="yunoModal">
                        <div class="modalHeader">
                            <h2 v-if="isOfflinePayment" class="modalTitle">Payment</h2>
                            <h2 v-else class="modalTitle">Payment link</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper">
                                <template v-if="isOfflinePayment">
                                    <p>Offline payment successfully done</p>
                                </template>
                                <template v-else>
                                    <ul class="classFields">
                                        <li>
                                            <div class="clipboard">
                                                <b-input id="yunoPayLink" :value="createPayment.data.payment_link" readonly></b-input>
                                                <i @click="copyToClipboard('yunoPayLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
                                            </div>
                                        </li>
                                    </ul>
                                </template>
                            </div>
                        </div>
                        <div class="modalFooter">
                            <b-button 
                                @click="closeModal()"
                                class="yunoSecondaryCTA">
                                Close
                            </b-button>
                        </div>
                </b-modal>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            isMiniSidebar: false,
            isInProcess: false,
            authorizedRoles: [
                "yuno-admin",
                "Counselor",
                "yuno-category-admin",
                "org-admin"
            ],
            learner: {
                data: [],
                selected: null,
                current: "",
                isLoading: false
            },
            course: {
                selected: null,
                current: ""
            },
            isOfflinePayment: false,
            counsellor: {
                isActive: false
            },
            paymentMethod: {
                isActive: false,
                list: [
                    { label: "Offline", slug: "offline" },
                    { label: "Payment Link", slug: "payment_link" },
                    { label: "Touchless", slug: "touchless" },
                    { label: "Razorpay", slug: "razorpay" },
                    { label: "Cheque", slug: "cheque" },
                    { label: "Google Pay", slug: "google_pay" },
                    { label: "PayTM", slug: "payTM" },
                    { label: "Did not pay", slug: "did_not_pay" },
                    { label: "Bank Transfer/UPI", slug: "bank_transfer_upi" },
                    { label: "Cash", slug: "cash" },
                    { label: "Other", slug: "other" }
                ]
            },
            referral: {
                batch_id: "",
                course_id: "",
                payment_method: "",
                referral_code: "",
                user_id: "",
                action: "create"
            },
            referralLoading: false,
            hasReferralCode: false,
            isWatcherActive: true,
            academy: null,
            payload: {
                user_id: "",
                course_id: "",
                batch_id: "",
                payment_gateway: "",
                payment_mode: "",
                amount: "",
                amount_due: "",
                total_instalments: "",
                instalment_amount: "",
                self_notes: "",
                currency_code: "",
                counselor_id: "",
                org_id: "",
                org_user_id: "",
                org_user_phone: "",
                org_user_name: "",
                org_user_email: "",
                org_crm_id: "",
                org_cohort: "",
                org_programs: "",
                org_parents: [],
                org_business_unit: "",
                referral_code_status: "",
                amount_paid: "",
                enrolled_by: "",
                payment_description: ""
            },
            org_parent: {
                name: "",
                email: "",
                phone: ""
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'paymentLink',
            'crmContacts',
            'allCourses',
            'allBatches',
            'enrollmentStatus',
            'createPayment',
            'loader',
            'counsellorList',
            'capabilities',
            'orgAdmin',
            'form',
            'updateLink',
            'subform2'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        isFormLoading() {
            return this.allCourses.loading || this.capabilities.loading;
        },
        isFormReady() {
            const isReady = this.allCourses.success && this.capabilities.success;
            if (isReady) {
                this.isPrefillForm();
            }
            return isReady;
        },
        filteredUserObj() {
            return this.crmContacts.data.filter(option => 
                option.username_email_phone.toString().toLowerCase()
                    .includes(this.paymentLink.form.selectedUser.toLowerCase())
            );
        },
        filterCourse() {
            return this.allCourses.data.filter(option => 
                option.search.toString().toLowerCase()
                    .includes(this.course.current.toLowerCase())
            );
        },
        filteredBatchObj() {
            return this.allBatches.data.filter(option => 
                option.term_name.toString().toLowerCase()
                    .includes(this.paymentLink.form.selectedBatch.toLowerCase())
            );
        }
    },
    watch: {
        'createPayment.payload.org_id': {
            handler(newValue, oldValue) {
                if (this.isWatcherActive && newValue !== oldValue) {
                    this.initOrgDetails();
                }
            },
            deep: true
        }
    },
    created() {
        this.emitEvents();
        this.fetchFooter();
    },
    mounted() {
        
    },
    methods: {
        dropdownChange(e) {
            this.fetchAllCourses();
        },
        manageFieldByRole(role) {
            return role === "org-admin" && this.academy !== "";
        },
        gotAcademies(options) {
            if (options.response?.status === 200 && options.response?.data) {
                this.academy = this.academy ?? this.subform2.data[0].value;
                this.fetchAllCourses();
            }
        },
        fetchAcademies() {
            this.fetchData(YUNOCommon.config.org("orgAcademies", this.activeOrg()), this.gotAcademies);
        },
        dispatchData(action, options) {
            this.$store.dispatch(action, options);
        },
        fetchData(apiURL, callbackFunc) {
            const options = { 
                apiURL,
                module: "gotData",
                store: "subform2",
                callback: true,
                callbackFunc: (options) => callbackFunc(options)
            };
            this.dispatchData('fetchData', options); 
        },
        pauseWatcher() {
            this.isWatcherActive = false;
        },
        resumeWatcher() {
            this.isWatcherActive = true;
        },
        enableLoader(isActive) {
            this.isInProcess = isActive;
        },
        gotOrgUserDetails(options) {
            this.enableLoader(false);
            if (options.response?.data?.code === 200) {
                const { email, user_id, phone, name } = options.response.data.data;
                this.createPayment.payload.org_user_email = email;
                this.createPayment.payload.org_user_id = user_id;
                this.createPayment.payload.org_user_phone = phone;
                this.createPayment.payload.org_user_name = name;
            }
        },
        fetchOrgUserDetails(learnerID) {
            const options = {
                apiURL: YUNOCommon.config.org("singleLearner", this.createPayment.payload.org_id, learnerID),
                module: "gotData",
                store: "updateLink",
                callback: true,
                callbackFunc: (options) => this.gotOrgUserDetails(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        initOrgDetails() {
            if (this.learner.selected) {
                this.enableLoader(true);
                this.fetchOrgUserDetails(this.learner.selected.id);
            } else {
                this.$buefy.toast.open('Please select learner to update the Org learner details');
            }
        },
        updateAmount(isApplied) {
            const { unitPrice, subscription } = this.paymentLink.form.course;
            this.paymentLink.form.amount = isApplied 
                ? this.form.data.discounted_price
                : (unitPrice === 0 && subscription.length !== 0) 
                    ? subscription[0].value 
                    : unitPrice;
        },
        formPosted(options) {
            this.referralLoading = false;
            const response = options.response?.data;
            
            if (response?.code === 201) {
                this.form.data = response.data;
                this.hasReferralCode = this.referral.action === "create";
                this.referral.action = this.hasReferralCode ? "remove" : "create";
                if (!this.hasReferralCode) {
                    this.referral.referral_code = "";
                }
                this.updateAmount(this.hasReferralCode);
                
                this.$buefy.toast.open({
                    duration: 5000,
                    message: response.message,
                    position: 'is-bottom'
                });
            } else {
                this.$buefy.toast.open({
                    duration: 5000,
                    message: response?.message,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        initReferral() {
            this.referralLoading = true;
            if (this.hasReferralCode) {
                this.referral.referral_code = "";
            }
            
            const options = {
                apiURL: YUNOCommon.config.referrerDetails(),
                module: "gotData",
                store: "form",
                payload: this.referral,
                callback: true,
                callbackFunc: (options) => this.formPosted(options)
            };
            this.$store.dispatch('postData', options);
        },
        onUserInfo(data) {
            if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
                this.fetchCapabilities(false);
                this.fetchOrgAdminList("all");
            }
        },
        onMini(data) {
            this.isMiniSidebar = data;
        },
        onMenuLoaded() {
            
        },
        emitEvents() {
            Event.$on('checkLoggedInState', (status) => {
                if (status) {
                    Event.$on('gotUserRole', (role, userData) => {
                        Event.$on('gotUserMenu', () => {
                            this.fetchCapabilities(false);
                        }); 
                    });
                }
            });
        },
        roleSpecificAPI(role) {
            const capabilities = this.capabilities.data;
            if (capabilities.isGovernLearners) {
                this.fetchAllCourses();
            }
            if (this.userRole.data === "org-admin") {
                this.allCourses.loading = true;
                this.fetchAcademies();
            }
        },
        gotCapabilities(options, noAPICall) {
            if (noAPICall) {
                this.capabilities.loading = false;
                this.capabilities.success = true;
                
                const capabilities = {
                    isClassesWisdom: true,
                    isEntranceWisdom: true,
                    isGangsWisdom: true,
                    isGovernClasses: true,
                    isGovernLearners: true,
                    isGovernGangs: true,
                    isSysopWisdom: true,
                    isPagoWisdom: true
                };
                
                if (this.userRole.data === "Counselor") {
                    delete capabilities.isClassesWisdom;
                }
                
                this.capabilities.data = capabilities;
                this.roleSpecificAPI();
            } else if (options.response?.data?.code === 200) {
                this.capabilities.data = options.response.data.data;
                this.roleSpecificAPI();
            }
        },
        fetchCapabilities(noAPICall) {
            if (noAPICall) {
                this.gotCapabilities(false, noAPICall);
            } else {
                const options = {
                    apiURL: YUNOCommon.config.capabilitiesAPI(isLoggedIn, false),
                    module: "gotData",
                    store: "capabilities",
                    addToModule: "false",
                    callback: true,
                    callbackFunc: (options) => this.gotCapabilities(options)
                };
                this.$store.dispatch('fetchData', options);
            }
        },
        isPrefillForm() {
            const getID = YUNOCommon.getQueryParameter("courseID");
            if (getID) {
                const courseBatch = getID.split("-");
                this.initPrefillForm(courseBatch);
            }
        },
        initPrefillForm(courseBatch) {
            const dataObj = this.allCourses.data.filter(el => el.course_id === courseBatch[0]);
            const getCourseObj = YUNOCommon.findObjectByKey(dataObj, "batch_id", courseBatch[1]);
            this.course.current = getCourseObj.search;
            this.course.selected = getCourseObj;
            this.onCourseSelect(getCourseObj);
        },
        onCourseSelect($event) {
            const form = this.paymentLink.form;
            if (!$event) {
                this.course.selected = null;
                form.course = "";
                form.courseID = "";
                form.batch = "";
                form.batchID = "";
                form.amount = "";
                this.referral.batch_id = "";
                this.referral.course_id = "";
                return;
            }
            
            this.course.selected = $event;
            form.course = $event;
            form.courseID = $event.course_id;
            form.batch = $event;
            form.batchID = $event.batch_id;
            form.amount = $event.batch_cost === 0 && $event.subscription.length !== 0 
                ? $event.subscription[0].value 
                : $event.batch_cost;
            
            this.referral.batch_id = $event.batch_id;
            this.referral.course_id = $event.course_id;
            
            if (this.userInfo.data.role === "yuno-admin") {
                this.paymentLink.payload.org_id = $event.org_id;
            }
        },
        gotLeaners(options) {
            this.learner.isLoading = false;
            if (options.response?.data?.code === 200) {
                this.learner.data = this.crmContacts.data;
            } else {
                this.learner.data = [];
                this.crmContacts.data = [];
            }
        },
        fetchLeaners(search) {
            const options = {
                apiURL: this.getLeanersURL(search),
                module: "gotData",
                store: "crmContacts",
                callback: true,
                callbackFunc: (options) => this.gotLeaners(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        getLeanersURL(search) {
            const role = this.userInfo.data.role;
            const searchParam = `?search=${search}`;
            
            const urlMapping = {
                "org-admin": YUNOCommon.config.generic("userSearch", search, "learner", this.activeOrg()),
                "yuno-admin": YUNOCommon.config.learnerListAPI(searchParam),
                "Counselor": YUNOCommon.config.learnerListAPI(searchParam),
                "yuno-category-admin": YUNOCommon.config.learnerListAPI(searchParam)
            };
            
            return urlMapping[role] || null;
        },
        getLearners: _.debounce(function (name) {
            if (name.length > 2) {
                this.learner.isLoading = true;
                this.fetchLeaners(name);
            } else {
                this.learner.data = [];
            }
        }, 500),
        onLearnerSelect($event) {
            if (!$event) {
                this.learner.selected = null;
                this.paymentLink.form.user = "";
                this.referral.user_id = "";
                return;
            }
            
            this.learner.selected = $event;
            this.paymentLink.form.user = $event;
            this.referral.user_id = this.userRole.data === "org-admin" ? $event.user_id : $event.id;
        },
        gotCounsellors(options) {
            this.enableLoader(false);
            
            if (options.response?.data?.code === 200) {
                this.counsellor.isActive = true;
                this.paymentMethod.isActive = true;
            }
        },
        fetchCounsellorList() {
            this.enableLoader(true);
            const options = {
                apiURL: YUNOCommon.config.listOfCounsellorsAPI(),
                module: "gotData",
                store: "counsellorList",
                callback: true,
                callbackFunc: (options) => this.gotCounsellors(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        isInstallments(userVal) {
            const onlinePayment = (instance) => {
                instance.paymentLink.form.isInstallment = false;
                instance.paymentLink.form.isNextSlide = false;
                instance.createPayment.payload.total_instalments = "";
                instance.createPayment.payload.instalment_amount = "";
                instance.createPayment.payload.self_notes = "";
                instance.createPayment.payload.counselor_id = "";
            };
            
            if (userVal === "Payment in installments") {
                this.paymentLink.form.isInstallment = true;
                this.paymentLink.form.isNextSlide = true;
                this.counsellor.isActive = false;
                this.paymentMethod.isActive = false;
                this.createPayment.payload.payment_gateway = "generate_link";
                this.createPayment.payload.payment_mode = "online";
                this.createPayment.payload.org_id = "";
                this.isOfflinePayment = false;
                this.referral.payment_method = "generate_link";
            } else if (userVal === "Offline payment") {
                onlinePayment(this);
                this.createPayment.payload.payment_gateway = "offline";
                this.createPayment.payload.payment_mode = "offline";
                this.paymentMethod.isActive = true;
                this.isOfflinePayment = true;
                
                if (this.userRole.data === 'yuno-admin') {
                    if (this.counsellorList.data.length === 0) {
                        this.fetchCounsellorList();
                    } else {
                        this.counsellor.isActive = true;
                    }
                }
                
                this.referral.payment_method = "offline";
            } else {
                onlinePayment(this);
                this.counsellor.isActive = false;
                this.paymentMethod.isActive = false;
                this.createPayment.payload.payment_gateway = "generate_link";
                this.createPayment.payload.payment_mode = "online";
                this.createPayment.payload.org_id = "";
                this.isOfflinePayment = false;
                this.referral.payment_method = "generate_link";
            }
            
            if (this.referral.referral_code !== "") {
                this.initReferral();
            }
        },
        initForm() {
            this.paymentLink.isLoading = true;
            const { payload, form } = this.paymentLink;
            
            payload.amount = form.isInstallment ? this.createPayment.payload.instalment_amount : form.amount;
            payload.customer_contact = form.user.phone;
            payload.customer_email = form.user.email;
            payload.customer_name = form.user.name;
            payload.receipt = `receipt-${Math.floor(performance.now())}`;
            
            if (this.userInfo.data.role === "org-admin") {
                payload.org_id = this.activeOrg();
            }

            this.initPayment();
            
            // this.fetchEnrollmentStatus(payload);
        },
        createPaymentDone(options) {
            if (options.isError) {
                this.$buefy.toast.open({
                    duration: 5000,
                    message: YUNOCommon.config.errorMsg.common,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            } else {
                const getData = options.response.data;
                if (getData.status === "SUCCESS") {
                    this.paymentSuccess();
                } else {
                    this.paymentLink.isLoading = false;
                    this.$buefy.toast.open({
                        duration: 5000,
                        message: getData.message,
                        position: 'is-bottom',
                        type: 'is-danger'
                    });
                }

                this.createPayment.data = options.response.data.data;
            }
        },
        gotOrgAdminList(options) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchOrgAdminList(search) {
            const searchParam = `?search=${search}`;
            const options = {
                apiURL: YUNOCommon.config.orgList(searchParam),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgAdminList(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        setPaymentPayload(data) {
            const payload = this.createPayment.payload;
            const paymentLinkForm = this.paymentLink.form;
            const userID = this.userRole.data === "org-admin" ? paymentLinkForm.user.user_id : paymentLinkForm.user.id;
            
            // Common items
            payload.id = data?.id ?? "";
            payload.receipt = data?.receipt ?? "";
            payload.Order_id = data?.order_id ?? "";
            payload.customer_id = data?.customer_id ?? "";
            payload.user_id = userID;
            payload.course_id = paymentLinkForm.courseID;
            payload.batch_id = paymentLinkForm.batchID;
            payload.batch_name = paymentLinkForm.batch.term_name;
            payload.batch_end_date = paymentLinkForm.batch.end_date;
            payload.amount = paymentLinkForm.amount;
            payload.total_instalments = paymentLinkForm.isInstallment ? this.createPayment.payload.total_instalments : 0;
            payload.instalment_amount = paymentLinkForm.isInstallment ? this.createPayment.payload.instalment_amount : 0;
            payload.duration = `${paymentLinkForm.course.duration_weeks} weeks`;
            payload.status = data?.status ?? "";
            payload.short_url = data?.short_url ?? "";
            payload.referral_code_status = this.hasReferralCode ? "applied" : "not_applied";
            
            if (this.isOfflinePayment) {
                const commonPayload = (instance, role) => {
                    payload.amount_due = "0";
                    payload.description = instance.paymentLink.payload.payment_description;
                    payload.amount_paid = paymentLinkForm.amount;
                    
                    if (role === "yuno-admin") {
                        payload.counselor_id = payload.counselor_id;
                    } else if (role === "Counselor") {
                        payload.counselor_id = isLoggedIn;
                    }
                };
                
                switch (this.userRole.data) {
                    case "yuno-admin":
                        commonPayload(this, "yuno-admin");
                        break;
                    case "Counselor":
                        commonPayload(this, "Counselor");
                        break;
                    case "org-admin":
                        commonPayload(this, "org-admin");
                        this.pauseWatcher();
                        payload.org_id = this.activeOrg();
                        break;
                }
            } else {
                payload.amount_due = paymentLinkForm.amount;
                payload.description = data?.description ?? "";
                payload.counselor_id = isLoggedIn;
                
                if (this.userRole.data === 'org-admin') {
                    this.pauseWatcher();
                    payload.org_id = this.activeOrg();
                    payload.counselor_id = "";
                } else {
                    this.pauseWatcher();
                    payload.counselor_id = isLoggedIn;
                    payload.org_id = this.course.selected.org_id;
                }
            }
        },
        initPayment(data) {
            this.setPaymentPayload(data);
            this.setV4Payload();

            const options = {
                apiURL: YUNOCommon.config.enrollmentsV4("createLink", false),
                module: "gotData",
                store: "createPayment",
                payload: this.payload,
                callback: true,
                callbackFunc: (options) => this.createPaymentDone(options)
            };
            this.$store.dispatch('postData', options);
        },
        setV4Payload() {
            const payload = this.payload;
            const createPaymentPayload = this.createPayment.payload;
            
            payload.user_id = Number(createPaymentPayload.user_id);
            payload.course_id = Number(createPaymentPayload.course_id);
            payload.batch_id = Number(createPaymentPayload.batch_id);
            payload.payment_gateway = createPaymentPayload.payment_gateway;
            payload.payment_mode = createPaymentPayload.payment_mode;
            payload.amount = Number(createPaymentPayload.amount);
            payload.amount_due = createPaymentPayload.amount_due;
            payload.total_instalments = createPaymentPayload.total_instalments;
            payload.instalment_amount = createPaymentPayload.instalment_amount;
            payload.self_notes = createPaymentPayload.self_notes;
            payload.currency_code = createPaymentPayload.currency;
            payload.counselor_id = createPaymentPayload.counselor_id;
            payload.org_id = createPaymentPayload.org_id;
            payload.org_user_id = createPaymentPayload.org_user_id;
            payload.org_user_phone = createPaymentPayload.org_user_phone;
            payload.org_user_name = createPaymentPayload.org_user_name;
            payload.org_user_email = createPaymentPayload.org_user_email;
            payload.org_crm_id = createPaymentPayload.org_crm_id;
            payload.org_cohort = createPaymentPayload.org_cohort;
            payload.org_programs = createPaymentPayload.org_programs;
            payload.org_parents.push(this.org_parent);
            payload.org_business_unit = createPaymentPayload.org_business_unit;
            payload.referral_code_status = createPaymentPayload.referral_code_status;
            payload.amount_paid = createPaymentPayload.amount;
            payload.enrolled_by = Number(isLoggedIn);
            payload.payment_description = this.paymentLink.payload.payment_description
            
        },
        paymentLinkDone(options) {
            if (options.isError) {
                this.$buefy.toast.open({
                    duration: 5000,
                    message: YUNOCommon.config.errorMsg.common,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            } else {
                this.initPayment(options.response.data);
            }
        },
        submitForm(payload) {
            const options = {
                apiURL: YUNOCommon.config.generatePaymentLinkAPI(),
                module: "gotData",
                store: "paymentLink",
                payload,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                callback: true,
                callbackFunc: (options) => this.paymentLinkDone(options)
            };
            this.$store.dispatch('postData', options);
        },
        gotEnrollmentStatus(options) {
            if (options.response?.data?.code === 200) {
                const getData = this.enrollmentStatus.data;
                if (getData.status === "INACTIVE") {
                    if (this.isOfflinePayment) {
                        this.initPayment();
                    } else {
                        this.submitForm(options.payload);
                    }
                } else {
                    this.paymentLink.isLoading = false;
                    this.$buefy.toast.open({
                        duration: 5000,
                        message: options.response.data.message,
                        position: 'is-bottom'
                    });
                }
            }
        },
        fetchEnrollmentStatus(payload) {
            const { form } = this.paymentLink;
            const userID = this.userRole.data === "org-admin" ? form.user.user_id : form.user.id;
            
            const options = {
                apiURL: YUNOCommon.config.enrollmentStatusAPI(form.batchID, form.courseID, userID),
                module: "gotData",
                store: "enrollmentStatus",
                payload,
                callback: true,
                callbackFunc: (options) => this.gotEnrollmentStatus(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        clearForm() {
            const { paymentLink, createPayment } = this;
            
            this.learner.current = "";
            this.counsellor.current = "";
            this.counsellor.selected = null;
            this.course.current = "";
            this.course.selected = null;
            this.counsellor.isActive = false;
            this.paymentMethod.isActive = false;
            
            // Reset paymentLink.form
            Object.keys(paymentLink.form).forEach(key => {
                const obj = paymentLink.form;
                if (obj.hasOwnProperty(key)) {
                    if (key === "isInstallment" || key === "isNextSlide") {
                        obj[key] = false;
                    } else if (key === "howManyInstallments") {
                        obj[key] = ["2", "3"];
                    } else {
                        obj[key] = "";
                    }
                }
            });
            
            // Reset paymentLink.payload
            Object.keys(paymentLink.payload).forEach(key => {
                const obj = paymentLink.payload;
                if (obj.hasOwnProperty(key)) {
                    if (key === "type") {
                        obj[key] = "link";
                    } else if (key === "view_less") {
                        obj[key] = 1;
                    } else if (key === "currency") {
                        obj[key] = "INR";
                    } else if (key === "partial_payment") {
                        obj[key] = 0;
                    } else {
                        obj[key] = "";
                    }
                }
            });
            
            // Reset createPayment.payload
            Object.keys(createPayment.payload).forEach(key => {
                const obj = createPayment.payload;
                if (obj.hasOwnProperty(key)) {
                    if (key === "payment_gateway") {
                        obj[key] = "generate_link";
                    } else if (key === "payment_mode") {
                        obj[key] = "online";
                    } else if (key === "currency") {
                        obj[key] = "INR";
                    } else if (key === "entity") {
                        obj[key] = "invoice";
                    } else {
                        obj[key] = "";
                    }
                }
            });
            
            this.hasReferralCode = false;
            this.referral.batch_id = "";
            this.referral.course_id = "";
            this.referral.payment_method = "";
            this.referral.referral_code = "";
            this.referral.user_id = "";
            this.referral.action = "create";

            this.org_parent = {
                name: "",
                email: "",
                phone: ""
            };

            this.payload = {
                user_id: "",
                course_id: "",
                batch_id: "",
                payment_gateway: "",
                payment_mode: "",
                amount: "",
                amount_due: "",
                total_instalments: "",
                instalment_amount: "",
                self_notes: "",
                currency_code: "",
                counselor_id: "",
                org_id: "",
                org_user_id: "",
                org_user_phone: "",
                org_user_name: "",
                org_user_email: "",
                org_crm_id: "",
                org_cohort: "",
                org_programs: "",
                org_parents: [],
                org_business_unit: "",
                referral_code_status: "",
                amount_paid: "",
                enrolled_by: ""
            }
        },
        paymentSuccess() {
            this.paymentLink.isLoading = false;
            this.paymentLink.successModal = true;
            this.pauseWatcher();
        },
        copyToClipboard(ele) {
            const copyText = document.getElementById(ele);
            copyText.select();
            copyText.setSelectionRange(0, 99999);
            document.execCommand("copy");
            
            this.$buefy.toast.open({
                duration: 2000,
                message: "Copy to clipboard",
                position: 'is-top',
                type: 'is-info'
            });
        },
        onModalClose() {

        },
        closeModal() {
            this.clearForm();
            this.paymentLink.successModal = false;
            this.$refs.paymentLinkObserver.reset();
        },
        onCourseChange($event) {
            const getData = this.allCourses.data;
            const paymentLinkForm = this.paymentLink.form;
            
            const course = getData.filter(el => el.post_id === $event);
            paymentLinkForm.course = course[0];
            
            this.fetchAllBatches(paymentLinkForm.courseID);
            
            const { unitPrice, subscription } = paymentLinkForm.course;
            paymentLinkForm.amount = unitPrice === 0 && subscription.length !== 0 
                ? subscription[0].value 
                : unitPrice;
        },
        onBatchChange($event) {
            const getData = this.allBatches.data;
            const paymentLinkForm = this.paymentLink.form;
            
            const batch = getData.filter(el => el.batch_id === $event);
            paymentLinkForm.batch = batch[0];
        },
        onUserSelect($event) {
            this.paymentLink.form.user = $event || "";
        },
        onBatchSelect($event) {
            if (!$event) {
                this.paymentLink.form.batch = "";
                this.paymentLink.form.batchID = "";
                return;
            }
            
            this.paymentLink.form.batch = $event;
            this.paymentLink.form.batchID = $event.batch_db_id;
        },
        gotBatches() {
            this.loader.isActive = false;
            this.loader.overlay = false;
        },
        fetchAllBatches(courseID) {
            this.loader.isActive = true;
            this.loader.overlay = true;
            
            const options = {
                apiURL: YUNOCommon.config.courseBatchesAPI(courseID),
                module: "gotData",
                store: "allBatches",
                callback: true,
                callbackFunc: () => this.gotBatches()
            };
            this.$store.dispatch('fetchData', options);
        },
        gotCourses(options) {
            if (options.response?.data?.code === 200) {
                const getData = options.response.data.data;
                getData.forEach(course => {
                    const getBatch = course.batch_label ? ` - ${course.batch_label}` : "";
                    course.search = `${course.course_code} (${course.name}${getBatch})`;
                });
                this.allCourses.data = getData;
            }
        },
        fetchAllCourses() {
            const options = {
                apiURL: this.getAllCoursesURL(),
                module: "gotData",
                store: "allCourses",
                addToModule: false,
                callback: true,
                callbackFunc: (options) => this.gotCourses(options)
            };
            this.$store.dispatch('fetchData', options);
        },
        getAllCoursesURL() {
            const role = this.userInfo.data.role;
            const urlMapping = {
                "org-admin": YUNOCommon.config.generic("orgBatches", false, false, false, this.academy),
                "yuno-admin": YUNOCommon.config.courseListAPI("batch"),
                "Counselor": YUNOCommon.config.courseListAPI("batch"),
                "yuno-category-admin": YUNOCommon.config.courseListAPI("batch")
            };
            return urlMapping[role] || null;
        },
        activeOrg() {
            return this.userInfo.data.current_state.org_id;
        },
        fetchFooter() {
            const options = {
                apiURL: YUNOCommon.config.footerAPI(),
                module: "gotData",
                store: "footer",
                callback: false
            };
            this.$store.dispatch('fetchData', options);
        }
    }
});