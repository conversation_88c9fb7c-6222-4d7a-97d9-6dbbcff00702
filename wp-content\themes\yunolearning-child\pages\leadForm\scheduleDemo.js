Vue.component('yuno-schedule-demo', {
    props: {
        category: {
            type: String,
            required: true
        }
    },
    template: `
        <section class="loginStep demoSchedule">
            <div class="container">
                <div class="columns is-centered">
                    <a href="#" @click="preStep" class="pre" :class="[isDatePicked ? 'isShow' : '']"><span class="material-icons">arrow_back_ios</span></a>
                    <div class="column is-full-mobile" :class="[ isDatePicked ? 'is-10' : 'is-7' ]">
                        <div class="columns is-multiline is-centered hasBorder">
                            <div class="column step1 is-full-mobile hasRightBorder noBtmPaddingMobile" :class="[isDatePicked ? 'isHideInMobile' : '']">
                                <div class="block">
                                    <h2 class="smallCaption dark87">{{ showCategory(category) }}</h2>
                                </div>
                                <div class="block">
                                    <h2 class="smallCaption dark87">Time zone</h2>
                                    <p class="body1"> {{ getFormattedTimeZone() }}</p>
                                </div>
                            </div>
                            <div class="column step1 is-full-mobile datepickerWrapper" :class="[isDatePicked ? 'isHideInMobile' : '']">
                                <template v-if="subform.loading">
                                    <b-skeleton width="100%" height="342px"></b-skeleton>
                                </template>
                                <template v-else-if="subform.success && subform.error === null">
                                    <h2 class="smallCaption dark87">Select a Date & Time</h2>
                                    <b-datepicker 
                                        v-model="scheduleDate" 
                                        inline 
                                        :min-date="startOfToday()"
                                        class="yunoDatePicker"
                                        :date-formatter="formatDate"
                                        @input="onDatePick"
                                    >
                                    </b-datepicker>
                                </template>
                                <template v-else-if="subform.success">
                                    {{ subform.errorData }}
                                </template>
                            </div>
                            <div class="column step2 is-3 is-full-mobile hasLeftBorder" v-if="isDatePicked">
                                <template v-if="subform.loading">
                                    <div class="slotsWrapper">
                                        <div class="slot" v-for="(slot, i) in 1" :key="i">
                                            <h2 class="largeTitle dark87"><b-skeleton active height="24px" width="100%"></b-skeleton></h2>
                                            <div class="time">
                                                <b-skeleton 
                                                    v-for="(time, j) in 3" 
                                                    :key="j"
                                                    active height="40px" width="100%"
                                                >
                                                </b-skeleton>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="subform.success && subform.error === null && subform.data.length !== 0">
                                    <div class="slotsWrapper" v-if="isDateTimePicked">
                                        <div class="selectedSlot">
                                            <h2 class="smallCaption dark87">{{ convertDateString(payload.class_date_time) }}</h2>
                                            <p class="smallCaption">{{ startEndTime }} ({{ timezone }})</p>
                                            <p class="overline capital">Duration: {{ payload.class_duration + " minutes" }}</p>
                                            <b-button 
                                                @click="onFormSubmit()" 
                                                :loading="subform3.isLoading"
                                                :disabled="subform3.isLoading"
                                                class="primaryCTA"
                                                expanded
                                            >
                                                Confirm
                                            </b-button>
                                            <b-button 
                                                @click="onCancel()" 
                                                expanded
                                            >
                                                Cancel
                                            </b-button>
                                        </div>
                                    </div>
                                    <div class="slotsWrapper" v-else>
                                        <div class="slot" v-for="(slot, i) in subform.data.available_slots.time_slots" :key="i">
                                            <h2 class="smallCaption dark87">{{ convertDateString(slot.date + ' ' + slot.day_of_week) }}</h2>
                                            <div class="time">
                                                <b-button 
                                                    v-for="(time, j) in slot.slots" 
                                                    v-if="time.status" 
                                                    @click="onTimeSelect(time, slot.date)" 
                                                    :key="j">
                                                    {{ convertTo12HourFormat(time.starttime) }} - {{ convertTo12HourFormat(time.endtime) }}
                                                </b-button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="subform.success">
                                    {{ subform.errorData }}
                                </template>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </section>                            
        
    `,
    data() {
        return {
            scheduleDate: new Date(),
            isDateTimePicked: false,
            isDatePicked: false,
            startEndTime: "",
            timezone: "",
            payload: {
                class_title: "",
                class_date_time: "",
                class_duration: "",
                instructor_id: [],
                category_id: "",
                academy_id:"",
                learners: []
            },
            slotsPayload: {
                resource_id: [],
                start_date: "",
                end_date: "",
                start_time: "",
                end_time: "23:59",
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'subform',
            'categoryList',
            'form',
            'subform3'
        ]),
    },
    async created() {

    },
    mounted() {

    },
    methods: {
        showCategory(category) {
            return this.findObjectByKey(this.categoryList.data, 'categorySlug', category).category;
        },
        preStep() {
            this.isDatePicked = false;
            this.onCancel();
        },
        convertToISOString(dateStr) {
            // Expecting a string like "2025-03-14 Friday"
            // Split the string and take the first part
            const [datePart] = dateStr.split(' ');
            const dateObj = new Date(datePart);
            return dateObj.toISOString();
        },
        getFormattedTimeZone() {
            const dt = new Date();
            const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Get the short abbreviation, e.g., "EST"
            const shortParts = new Intl.DateTimeFormat('en-US', { timeZone, timeZoneName: 'short' }).formatToParts(dt);
            const shortName = shortParts.find(part => part.type === 'timeZoneName').value;

            // Get the full time zone name, e.g., "Eastern Standard Time"
            const longParts = new Intl.DateTimeFormat('en-US', { timeZone, timeZoneName: 'long' }).formatToParts(dt);
            const longName = longParts.find(part => part.type === 'timeZoneName').value;

            return `${shortName} (${longName})`;
        },
        onCancel() {
            this.isDateTimePicked = false;
        },
        readableDate(date) {

        },
        // Converts a string "YYYY-MM-DD DayName" to "Weekday, Month DaySuffix Year"
        convertDateString(dateStr) {
            let parsedDateStr = dateStr;
            // If the dateStr matches "YYYY-MM-DD DayName" then convert it to an ISO string.
            if (/^\d{4}-\d{2}-\d{2} \w+/.test(dateStr)) {
                parsedDateStr = this.convertToISOString(dateStr);
            }
            const dateObj = new Date(parsedDateStr);

            if (isNaN(dateObj.getTime())) {
                // Return the input string if date is invalid
                return dateStr;
            }
            // Retrieve weekday, month, day, and year
            const weekday = dateObj.toLocaleString('en-US', { weekday: 'long' });
            const month = dateObj.toLocaleString('en-US', { month: 'long' });
            const day = dateObj.getDate();
            const year = dateObj.getFullYear();

            // Determine the appropriate day suffix
            let suffix = 'th';
            if (day % 10 === 1 && day !== 11) {
                suffix = 'st';
            } else if (day % 10 === 2 && day !== 12) {
                suffix = 'nd';
            } else if (day % 10 === 3 && day !== 13) {
                suffix = 'rd';
            }
            return `${weekday}, ${month} ${day}${suffix} ${year}`;
        },
        // Updates the scheduleDate with the provided time string
        updateDateTime(scheduleDate, timeStr) {
            let hours, minutes;
            const parts = timeStr.split(' ');
            if (parts.length === 2) {
                // Format: "hh:mm AM/PM"
                const [timePart, period] = parts;
                [hours, minutes] = timePart.split(':').map(Number);
                if (period.toUpperCase() === 'PM' && hours < 12) {
                    hours += 12;
                } else if (period.toUpperCase() === 'AM' && hours === 12) {
                    hours = 0;
                }
            } else {
                // Format: "HH:mm"
                [hours, minutes] = timeStr.split(':').map(Number);
            }
            const updatedDate = new Date(scheduleDate);
            updatedDate.setHours(hours, minutes, 0, 0);
            return updatedDate.toString();
        },
        // Calculate duration between start and end time strings (either 12/24-hour format)
        calculateDuration(startTimeStr, endTimeStr) {
            const parseTime = (timeStr) => {
                let hrs, mins;
                const parts = timeStr.split(' ');
                if (parts.length === 2) {
                    const [timePart, period] = parts;
                    [hrs, mins] = timePart.split(':').map(Number);
                    if (period.toUpperCase() === 'PM' && hrs < 12) {
                        hrs += 12;
                    } else if (period.toUpperCase() === 'AM' && hrs === 12) {
                        hrs = 0;
                    }
                } else {
                    [hrs, mins] = timeStr.split(':').map(Number);
                }
                return hrs * 60 + mins;
            };

            const startMinutes = parseTime(startTimeStr);
            const endMinutes = parseTime(endTimeStr);
            let diff = endMinutes - startMinutes;
            if (diff < 0) diff += 24 * 60; // Adjust for overnight time differences
            return diff;
        },
        // Returns the first object in 'array' where item[key] equals 'value'
        findObjectByKey(array, key, value) {
            return array.find(item => item[key] === value);
        },
        // Handles the selection of a time slot
        onTimeSelect(time, date) {
            // Update class_date_time using the selected start time
            this.startEndTime = `${this.convertTo12HourFormat(time.starttime)} - ${this.convertTo12HourFormat(time.endtime)}`;
            this.payload.class_date_time = this.updateDateTime(date, time.starttime);
            this.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Calculate and set class duration if both start and end times are provided
            if (time.starttime && time.endtime) {
                this.payload.class_duration = this.calculateDuration(time.starttime, time.endtime);
            }

            // Get instructors from available_slots and update payload.instructor_id, if available
            if (
                this.subform.data &&
                this.subform.data.available_slots &&
                Array.isArray(this.subform.data.available_slots.instructors)
            ) {
                this.payload.instructor_id = this.subform.data.available_slots.instructors.map(instructor => instructor.id);
            }

            // Update payload with selected category, academy and learner info
            const categoryObj = this.findObjectByKey(this.categoryList.data, 'categorySlug', this.category);
            if (categoryObj) {
                this.payload.category_id = categoryObj.category_id;
            }
            this.payload.academy_id = YUNOCommon.getQueryParameter("academy_id") || 0;
            this.payload.learners.push(isLoggedIn);
            this.isDateTimePicked = true;
            // this.onFormSubmit(true);
        },
        formPosted(options, debug) {
            this.subform3.loading = false;
            this.subform3.isLoading = false;

            const response = options?.response?.data;

            debug ? console.log(response) : "";

            if (response?.code === 201) {

                if (response.data && response.data.data) {
                    this.subform3.data = response.data.data;
                }

                this.$emit("showThankyou")

                debug ? console.log(response.message) : "";
            } else if (response?.code === 400) {
                this.$buefy.toast.open({
                    duration: 3000,
                    message: response.message,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            } else if (response?.message) {
                debug ? console.log(response.message) : "";

                this.$buefy.toast.open({
                    duration: 3000,
                    message: response.message,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        onFormSubmit(debug) {
            this.subform3.loading = true;
            this.subform3.isLoading = true;

            const options = {
                apiURL: YUNOCommon.config.user("classSchedule", false),
                module: "gotData",
                store: "subform3",
                payload: this.payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.formPosted(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        formatDateForPayload(date) {
            const dateObj = new Date(date);
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        setCurrentTime() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        // Sets isDatePicked flag on date selection
        onDatePick(date) {
            this.isDatePicked = true;
            const subformData = JSON.parse(JSON.stringify(this.subform));

            if (
                subformData.data &&
                subformData.data.available_slots &&
                Array.isArray(subformData.data.available_slots.instructors)
            ) {
                this.slotsPayload.resource_id = subformData.data.available_slots.instructors.map(instructor => instructor.id);
            }

            this.slotsPayload.start_date = this.formatDateForPayload(date);
            this.slotsPayload.end_date = this.formatDateForPayload(date);
            this.slotsPayload.start_time = this.setCurrentTime();
            this.fetchSlots(true);
        },
        gotSlots(options, debug) {
            this.subform.loading = false;
            this.subform.isLoading = false;

            const response = options?.response?.data;

            if (response?.code === 201) {
                if (response.data && response.data.data) {
                    this.subform.data = response.data.data;
                }
                debug ? console.log(response.message) : "";
            } else if (response?.message) {
                debug ? console.log(response.message) : "";
            }
        },
        fetchSlots(debug) {
            this.subform.loading = true;
            this.subform.isLoading = true;
            this.subform.success = false;

            const options = {
                apiURL: YUNOCommon.config.user("slots", false),
                module: "gotData",
                store: "subform",
                payload: this.slotsPayload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.gotSlots(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        // Formats a given date into "Weekday DaySuffix Month"
        formatDate(date) {
            const options = { weekday: 'long', month: 'long' };
            const day = date.getDate();
            const suffix = this.getDaySuffix(day);
            const formattedDate = date.toLocaleDateString('en-GB', options);
            const [weekday, month] = formattedDate.split(' ');
            return `${weekday} ${day}${suffix} ${month}`;
        },
        // Returns the appropriate suffix for a given day
        getDaySuffix(day) {
            if (day > 3 && day < 21) return 'th';
            switch (day % 10) {
                case 1: return 'st';
                case 2: return 'nd';
                case 3: return 'rd';
                default: return 'th';
            }
        },
        // Returns a Date object representing the start of today
        startOfToday() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return today;
        },
        // Converts a 24-hour time string ("HH:mm") to 12-hour formatted string ("h:mm AM/PM")
        convertTo12HourFormat(time) {
            const [hoursStr, minutes] = time.split(':');
            const hours = parseInt(hoursStr, 10);
            const period = hours >= 12 ? 'PM' : 'AM';
            const adjustedHours = hours % 12 || 12;
            return `${adjustedHours}:${minutes} ${period}`;
        }
    }
});