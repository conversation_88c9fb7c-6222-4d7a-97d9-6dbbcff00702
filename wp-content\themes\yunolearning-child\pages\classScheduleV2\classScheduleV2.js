window.Event = new Vue();
const validationMsg = {
	messages: {
		required: "This field is required",
		numeric: "Numbers only",
		min: "Minium 10 numbers required",
		max: "Maxium 15 numbers required",
		is: "This field is required",
		email: "Please enter the valid email id",
	},
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component("yuno-class-schedule-v2", {
	template: `
        <yuno-page-grid
			@onUserInfo="onUserInfo"
			:authorizedRoles="authorizedRoles"
			:hasSearchBar="false"
		>
			<template v-slot:main>
				<b-loading :is-full-page="true" :active="isInProcess" :can-cancel="false"></b-loading>
				<template v-if="userProfile.data.vc_permission">
					<template v-if="isFormLoading">
						<div class="smallLoader"></div>
					</template>
					<template v-if="isFormReady">
						<section id="classSchedule" class="container formSection classSchedule">
							<template v-if="isEditClass()">
								<h1 class="sectionTitle">Edit Class</h1>    
							</template>
							<template v-else>
								<h1 class="sectionTitle">Schedule Class</h1>    
							</template>
							<template v-if="isFormLoading">
								<div class="smallLoader"></div>
							</template>
							<validation-observer ref="classScheduleObserver" v-slot="{ handleSubmit }">
								<form id="classScheduleForm" @submit.prevent="handleSubmit(initForm)">
									<div class="row">
										<div class="col-12 col-md-7 col-lg-7">
											<div class="formWrapper">
												<b-field label="Choose Category" v-if="hideField">
													<b-autocomplete
														v-model="name"
														placeholder="e.g. Anne"
														:data="filteredDataObj"
														field="user.first_name"
														@select="option => (selected = option)"
														:clearable="true">
													</b-autocomplete>
												</b-field>
												<b-field label="Choose sub-category" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<b-field label="Class Title">
													<validation-provider :rules="{required:true, isSelected:filteredTitleObj.length}" v-slot="{ errors, classes }">
														<b-autocomplete
															:class="classes"
															ref="classTitleAutocomplete"
															v-model="classSchedule.payload.ClassTitle"
															:data="filteredTitleObj"
															placeholder="Search class title"
															field="title"
															@select="onTitleSelect($event)"
															:clearable="true">
															<template slot="header">
																<a @click="addNewTitle">
																	<span> Add new... </span>
																</a> 
															</template>
															<template slot="empty">No results for {{classSchedule.payload.ClassTitle}}</template>
														</b-autocomplete>
														<p class="error">{{errors[0]}}</p>
													</validation-provider>
												</b-field>
												<b-field label="Related Course">
													<b-select 
														@input="onRelatedCourceChange($event)"
														v-model="classSchedule.relatedCourses"
														placeholder="Select">
														<option value="">Select</option>
														<option 
															v-if="relatedCourses.data.length !== 0"
															v-for="(course, courseIndex) in relatedCourses.data" 
															:key="courseIndex"
															:value="course.course_id">
															{{course.course_name}}
														</option>
													</b-select>
												</b-field>
												<div class="field cardRadio" v-if="batches.isActive" label="Batch">
													<label class="label">Batch <small class="helper">(Choose one option)</small></label>
													<validation-provider 
														tag="div"
														:rules="{required: false}" 
														v-slot="{ errors, classes }">
															<div class="listWrapper">
																<div class="noBatchFound" v-if="allBatches.error">
																	<i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{allBatches.errorData}}
																</div>
																<template v-for="(item, index) in allBatches.data">
																	<b-radio-button 
																		:key="index"
																		v-model="classSchedule.payload.BatchID"
																		@input="onBatchSelect(item)"
																		:native-value="item.batch_id">
																		<ul class="batchList">
																			<li>
																				<div class="listValue">{{ item.time }}</div>
																				<div class="listValue">{{ item.class_days }}</div>
																				<div class="listValue">({{ 'Batch ID: ' + item.batch_id}})</div>
																			</li>
																		</ul>
																	</b-radio-button>
																</template>
															</div>
														<p class="error">{{errors[0]}}</p>
													</validation-provider>
												</div>
												<b-field label="Description">
													<b-input 
														v-model="classSchedule.payload.ClassDescription"
														type="textarea" 
														disabled>
													</b-input>
												</b-field>
												<b-field label="Related to a course" v-if="hideField">
													<b-taginput
														v-model="tags"
														:data="filteredTags"
														autocomplete
														field="user.first_name"
														icon="label"
														placeholder="Add a course"
														@typing="getFilteredTags">
														<template slot-scope="props">
															<strong>{{props.option.id}}</strong>: {{props.option.user.first_name}}
														</template>
														<template slot="empty">
															There are no items
														</template>
													</b-taginput>
												</b-field>
												<b-field label="Class Privacy" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<div class="inlineFields">
													<b-field label="Date">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-datepicker
																:class="classes"
																v-model="classSchedule.date"
																:date-formatter="formatDate"
																placeholder="Pick date"
																:mobile-native="false"
																trap-focus>
															</b-datepicker>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
													<b-field label="Start Time">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-timepicker
																:class="classes"
																v-model="classSchedule.time"
																placeholder="Pick time"
																hour-format="12"
																:mobile-native="false"
																icon="clock">
															</b-timepicker>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
													<b-field label="Duration">
														<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
															<b-select 
																:class="classes"
																v-model="classSchedule.payload.ClassDuration"
																placeholder="Select">
																<option value="">Select</option>
																<option value="15">15 Minutes</option>
																<option value="30">30 Minutes</option>
																<option value="45">45 Minutes</option>
																<option value="60">1 Hour</option>
																<option value="75"> 1 Hour 15 Minutes</option>
																<option value="90">1 Hour 30 Minutes</option>
																<option value="105">1 Hour 45 Minutes</option>
																<option value="120">2 Hours</option>
															</b-select>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
												</div>
												<b-field label="Instructor" v-if="hideField">
													<b-autocomplete
														v-model="name"
														placeholder="e.g. Anne"
														:data="filteredDataObj"
														field="user.first_name"
														@select="option => (selected = option)"
														:clearable="true">
													</b-autocomplete>
												</b-field>
												<b-field label="Zoom account to be used" v-if="hideField">
													<b-select placeholder="Select">
														<option
															v-for="option in data"
															:value="option.id"
															:key="option.id">
															{{ option.user.first_name }}
														</option>
													</b-select>
												</b-field>
												<div class="radioList groupElement" v-if="hideField">
													<p class="fieldLabel">Recording</p>
													<small class="helper">Choose one option</small>
													<div class="field">
														<b-radio 
															native-value="Do not record"
															name="recording">
															Do not record
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record but do not automatically share with anyone"
															name="recording">
															Record but do not automatically share with anyone
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record and share with anyone who enrolls"
															name="recording">
															Record and share with anyone who enrolls
														</b-radio>
													</div>
													<div class="field">
														<b-radio 
															native-value="Record and share with only those who attend"
															name="recording">
															Record and share with only those who attend
														</b-radio>
													</div>
												</div>
											</div>
											<div class="ctaWrapper">
												<b-button
													native-type="submit"
													class="yunoSecondaryCTA">
														<template v-if="isEditClass()">
															Update Class
														</template>
														<template v-else>
															Schedule       
														</template>
												</b-button>   
											</div>
										</div>
										<div class="col-12 col-md-5 col-lg-5 inviteWrapper">
											<b-field id="enrollLearners" label="Add People">
												<validation-provider ref="inviteUser" :rules="{email:inviteEmail}" v-slot="{ errors, classes }">
													<b-autocomplete
														v-model="classSchedule.currentLearner"
														ref="inviteUserAutocomplete"
														:class="classes"
														:data="filteredLernerObj"
														placeholder="Enter learner or group name"
														field="full_name"
														@select="onLernerSelect($event)"
														:clearable="true">
														<template slot="header">
															<a @click="inviteOtherUser">
																<span>Add this user...</span>
															</a> 
														</template>
														<template slot="empty">No results for {{classSchedule.currentLearner}}</template>
														<template slot-scope="props">
															<div class="userItem">
																<figure class="userImg">
																	<template v-if="props.option.image_url !== undefined && props.option.image_url !== ''">
																		<img :src="props.option.image_url" :alt="props.option.full_name">
																	</template>
																	<template v-else-if="props.option.image_url === ''">
																		<i class="fa fa-user-circle" aria-hidden="true"></i>
																	</template>
																	<template v-else>
																		<i class="fa fa-users" aria-hidden="true"></i>
																	</template>
																</figure>
																<div class="userName">{{props.option.full_name}}</div>
															</div>
														</template>
													</b-autocomplete>
													<p class="error">{{errors[0]}}</p>
												</validation-provider>
											</b-field>
											<template v-if="classSchedule.payload.learner.length">
												<p class="count">People Added: <span>{{classSchedule.learnerSelected}}</span></p>
												<ul class="selectedLearners list-group">
													<li
														class="list-group-item"
														v-for="(learner, learnerIndex) in classSchedule.payload.learner"
														:key="learnerIndex">
														<figure class="userImg">
															<template v-if="learner.image_url !== undefined && learner.image_url !== ''">
																<img :src="learner.image_url" :alt="learner.full_name">
															</template>
															<template v-else-if="learner.image_url === ''">
																<i class="fa fa-user-circle" aria-hidden="true"></i>
															</template>
															<template v-else>
																<template v-if="learner.group_id !== undefined">
																	<i class="fa fa-users" aria-hidden="true"></i>
																</template>
																<template v-else>
																	<i class="fa fa-user-circle" aria-hidden="true"></i>
																</template>
															</template>
														</figure>
														<div class="userName">
															<template v-if="learner.users !== undefined">
																<b-collapse animation="slide" :open="false" aria-id="contentIdForA11y1">
																	<div class="trigger" slot="trigger" aria-controls="contentIdForA11y1">
																		<span class="caption">{{learner.full_name}}</span>
																		<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>
																	</div>
																	<div class="groupLearners">
																		<ul>
																			<li v-for="(groupLearner, groupLearnerIndex) in learner.users">
																				{{groupLearner.name}} ({{groupLearner.email}})
																			</li>
																		</ul>
																	</div>
																</b-collapse>
															</template>
															<template v-else>
																<span class="caption">{{learner.full_name}}</span>
																<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>
															</template>
														</div>
													</li>
												</ul>
											</template>
											<template v-if="allLearners.loading">
												<ul class="suggestUser">
													<li class="title has2Col">
														<div class="caption">Suggested People</div>
													</li>
													<li v-for="i in suggestUser.loadingResult" :key="i" class="loading">
														<div class="media">
															<b-skeleton circle width="24px" height="24px"></b-skeleton>
															<b-skeleton active width="100px"></b-skeleton>
														</div>
													</li>
												</ul>
											</template>
											<template v-if="allLearners.success">
												<template v-if="allLearners.error === null">
													<ul class="suggestUser">
														<li class="title has2Col">
															<div class="caption">Suggested People</div>
															<div class="secondaryElement">
																<b-checkbox class="selectAll" v-model="suggestUser.selectAll" @input="suggestUserSelect" :disabled="suggestUser.selectAll">
																	Select All
																</b-checkbox>
															</div>
														</li>
														<li v-for="(item, i) in allLearners.data">
															<div class="media">
																<img class="userImg" :src="item.image_url" :alt="item.full_name" width="40" height="40">
																<span class="name">{{item.full_name}}</span>
															</div>
															<a href="#" @click.prevent="picksuggestedUser(item)" class="addToPeople"><span class="material-icons">add</span></a>
														</li>
													</ul>
												</template>
												<template v-else>
													<ul class="suggestUser">
														<li class="title has2Col">
															<div class="caption">Suggested People</div>
														</li>
														<li>
															{{ allLearners.errorData }}    
														</li>
													</ul>
												</template>
											</template>
										</div>
									</div>
								</form>    
							</validation-observer>
						</section>
						<b-modal 
							:active.sync="googlePermission.modal" 
							:width="500" 
							:can-cancel="false" 
							class="yunoModal">
								<div class="modalHeader">
									<h2 class="modalTitle">Google Permissions</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper">
										<p class="marginBtm16">Please grant following the permission to schedule a class on Google Meet:</p>
										<ul class="classFields">
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar</span>
											</li>
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar Events</span>
											</li>
											<li>
												<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Reports</span>
											</li>
										</ul>
										<p class="userNote"><i class="fa fa-info-circle" aria-hidden="true"></i> You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span></p>
									</div>
								</div>
								<div class="modalFooter">
									<b-button
										tag="a"
										:href="getGoogleMeetURL"
										@click="initGooglePermission()"
										class="yunoSecondaryCTA">
										Grant Permission
									</b-button>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="isCourseSelect" 
							:width="450" 
							:can-cancel="['escape', 'x']" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Please Select Academy</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<b-field label="Academy">
										<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
											<b-select 
											:class="classes"
											v-model="classSchedule.payload.academy_id"
											placeholder="Select Academy"
												expanded>
											<option value="">Select Academy</option>
											<template v-for="item in userInfo.data.academies">
												<option :value="item.id">{{item.name}}</option>
											</template>
											</b-select>
											<p class="error">{{errors[0]}}</p>
										</validation-provider>
										</b-field>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button
											:loading="classSchedule.isLoading === true ? true : false"
											:disabled="classSchedule.isLoading === true ? true : false" 
											native-type="submit"
											@click="confirmClass"
											class="yunoSecondaryCTA">
											Save
										</b-button>
									</div>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="classSchedule.modal" 
							:width="450" 
							:can-cancel="['escape', 'x']" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Confirm before scheduling</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<ul class="classFields">
											<li>
												<span class="caption">Class Title:</span>
												{{classSchedule.payload.ClassTitle}}
											</li>
											<li>
												<span class="caption">Date & Time:</span>
												{{dateTimeFormat}}
											</li>
											<li>
												<span class="caption">Duration:</span>
												{{classSchedule.payload.ClassDuration}} Minutes
											</li>
											<li v-if="classSchedule.payload.learner.length !== 0">
												<span class="caption">People({{ classSchedule.learnerSelected }}):</span>

												<ul class="selectedLearners list-group">
													<li
														class="list-group-item"
														v-for="(learner, learnerIndex) in classSchedule.payload.learner"
														:key="learnerIndex">
														<span class="caption">{{learner.full_name}}</span>
													</li>
												</ul>
											</li>
										</ul>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button
											:loading="classSchedule.isLoading === true ? true : false"
											:disabled="classSchedule.isLoading === true ? true : false" 
											native-type="submit"
											@click="classSchedule.payload.learner.length !== 0 ? submitForm() : noPeople()"
											class="yunoSecondaryCTA">
											Go ahead and schedule
										</b-button>
									</div>
								</div>
						</b-modal>
						<b-modal 
							:active.sync="classSchedule.successModal" 
							:width="500" 
							:can-cancel="false" 
							:on-cancel="onModalClose"
							class="yunoModal lightTheme">
								<div class="modalHeader">
									<h2 class="modalTitle">Class scheduled successfully</h2>
								</div>
								<div class="modalBody">
									<div class="wrapper marginBtm30">
										<ul class="classFields">
											<li>
												<span class="caption">Class Title:</span>
												{{classSchedule.data.Title}}
											</li>
											<li>
												<span class="caption">Date & Time:</span>
												{{dateTimeFormat}}
											</li>
											<li>
												<span class="caption">Guest link:</span>
												<p class="listSubtitle">Your learner will receive email and WhatsApp notifications regarding this class</p>
												<div class="clipboard">
													<b-input id="yunoClassLink" :value="classSchedule.data.guestURL" readonly></b-input>
													<i @click="copyToClipboard('yunoClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
												Note: anyone with a Yuno account can attend the class with this link
											</li>
											<li v-if="false">
												<span class="caption">Zoom Meeting ID:</span>
												<div class="clipboard">
													<b-input id="yunoZoomMeetingID" :value="classSchedule.data.zoom_meeting_id" readonly></b-input>
													<i @click="copyToClipboard('yunoZoomMeetingID')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
											<li v-if="false">
												<span class="caption">Yuno class link:</span>
												<div class="clipboard">
													<b-input id="yunoClassLink" :value="classSchedule.data.ClassLink" readonly></b-input>
													<i @click="copyToClipboard('yunoClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
											<li v-if="false">
												<span class="caption">Zoom class link:</span>
												<div class="clipboard">
													<b-input id="zoomClassLink" :value="classSchedule.data.ZoomClassLink" readonly></b-input>
													<i @click="copyToClipboard('zoomClassLink')" class="fa trigger fa-clipboard" aria-hidden="true"></i>
												</div>
											</li>
										</ul>
									</div>
									<div class="ctaWrapper alignLeft">
										<b-button tag="a"
											href="/instructor"
											class="yunoSecondaryCTA">
											Go to My Schedule
										</b-button>
									</div>
								</div>
						</b-modal>
					</template>
				</template>
				<template v-else>
					<section class="disableAccount">
						<div class="container">
							<div class="row">
								<div class="col-12 col-md-8 offset-md-2">
									<div class="wrapper">
										<span class="material-icons-outlined disableIcon">
											event
										</span>
										<p class="desciprtion">
											You can not schedule a class yet. To schedule classes please contact at <a href="mailto:<EMAIL>"><EMAIL></a>
										</p>
									</div>
								</div>
							</div>
						</div>
					</section>
				</template>
			</template>
		</yuno-page-grid>
    `,
	data() {
		return {
			isInProcess: false,
			isMiniSidebar: false,
			authorizedRoles: ["Instructor"],
			hideField: false,
			selected: null,
			isSelectOnly: false,
			tags: [],
			dateTimeFormat: "",
			inviteEmail: false,
			isCourseSelect: false,
			googlePermission: {
				modal: false,
				isLoading: false,
			},
			message: {
				common: "Error occurred, please try again later",
			},
			batches: {
				isActive: false,
				selected: null,
				current: "",
				isRequired: false,
			},
			suggestUser: {
				isActive: false,
				selectAll: false,
				loadingResult: 3,
				users: [
					{
						email: "<EMAIL>",
						id: 6939,
						name: "yunotesting 4 (<EMAIL>)",
						photo:
							"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg",
					},
					{
						email: "<EMAIL>",
						id: 6940,
						name: "yunotesting 64 (<EMAIL>)",
						photo:
							"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg",
					},
				],
			},
		};
	},
	computed: {
		...Vuex.mapState([
			"user",
			"header",
			"userProfile",
			"userRole",
			"footer",
			"classSchedule",
			"classTitle",
			"myLearners",
			"classEdit",
			"loader",
			"addClassTitle",
			"relatedCourses",
			"userInfo",
			"allBatches",
			"allLearners",
		]),
		filterBatches() {
			return this.allBatches.data.filter((option) => {
				return (
					option.search
						.toString()
						.toLowerCase()
						.indexOf(this.batches.current.toLowerCase()) >= 0
				);
			});
		},
		getHomeURL() {
			return this.$store.state.homeURL;
		},
		getGoogleMeetURL() {
			return this.$store.getters.googleMeet;
		},
		isFormLoading: {
			get() {
				let module = "";

				if (this.isEditClass()) {
					module =
						this.classTitle.loading ||
						this.myLearners.loading ||
						this.relatedCourses.loading ||
						this.classEdit.loading;
				} else {
					module =
						this.classTitle.loading ||
						this.myLearners.loading ||
						this.relatedCourses.loading;
				}

				return module;
			},
		},
		isFormReady: {
			get() {
				let module = "";
				if (this.isEditClass()) {
					module =
						this.classTitle.success &&
						this.myLearners.success &&
						this.relatedCourses.success &&
						this.classEdit.success;
				} else {
					module =
						this.classTitle.success &&
						this.myLearners.success &&
						this.relatedCourses.success;
				}

				return module;
			},
		},
		filteredTitleObj() {
			if (this.classTitle.data.length !== 0) {
				return this.classTitle.data.filter((option) => {
					return (
						option.title
							.toString()
							.toLowerCase()
							.indexOf(this.classSchedule.payload.ClassTitle.toLowerCase()) >= 0
					);
				});
			} else {
				return [];
			}
		},
		filteredLernerObj() {
			return this.myLearners.data.filter((option) => {
				return (
					option.full_name
						.toString()
						.toLowerCase()
						.indexOf(this.classSchedule.currentLearner.toLowerCase()) >= 0
				);
			});
		},
	},
	async created() { },
	mounted() {
		this.initiateEdit();
		localStorage.removeItem("userState");
	},
	methods: {
		onUserInfo(data) {
			if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
				// this.isPermissionModal(data);
				this.fetchMyLearners();
				this.fetchClassTitle();
				this.fetchRelatedCourses();
			}
		},
		formatDate(d) {
			return moment(d).format("ddd, MMM DD, YYYY");
		},
		suggestUserSelect(e) {
			let learners = this.classSchedule.payload.learner,
				users = this.allLearners.data;

			if (e) {
				for (let i = 0; i < users.length; i++) {
					const user = users[i],
						hasAdded = YUNOCommon.findObjectByKey(learners, "id", user.id);

					if (hasAdded === null) {
						learners.push(user);
						this.classSchedule.learnerSelected = parseInt(
							this.classSchedule.learnerSelected + 1
						);
					}
				}

				// this.classSchedule.learnerSelected = parseInt(this.classSchedule.learnerSelected + users.length);
			} else {
				this.classSchedule.payload.learner = [];
				this.classSchedule.learnerSelected = 0;
				// this.classSchedule.learnerSelected = Math.abs(parseInt(this.classSchedule.learnerSelected - users.length));
			}
		},
		onBatchSelect(data) {
			this.suggestUser.isActive = true;
			this.classSchedule.payload.academy_id = data.academy_id;
			this.fetchBatchLearners(data.batch_id);
		},
		gotBatchLearners(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				const data = options.response.data.data;
			}
		},
		fetchBatchLearners(batchID) {
			this.allLearners.success = false;
			this.allLearners.error = null;
			this.allLearners.errorData = [];
			this.allLearners.data = [];

			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.batch("learners", {batchID: batchID}),
				module: "gotData",
				store: "allLearners",
				callback: true,
				callbackFunc: function (options) {
					return instance.gotBatchLearners(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		onRelatedCourceChange(data) {
			let isData = data !== "";

			if (isData) {
				this.classSchedule.payload.BatchID = "";
				this.batches.isActive = true;
				this.batches.isRequired = true;
				this.enableLoader(true);
				this.fetchAllBatches(data);
			} else {
				this.batches.isActive = false;
				this.batches.isRequired = false;
				this.classSchedule.payload.BatchID = "";
			}
		},
		gotAllBatches(options, batchID) {
			this.enableLoader(false);

			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let getData = options.response.data.data;

				if (getData.length === 1) {
					// If there is only one batch
					this.classSchedule.payload.BatchID = getData[0].batch_id;
					this.onBatchSelect(getData[0]);
				}

				this.allBatches.data = getData;
				this.allBatches.error = null;

				if (this.isEditClass()) {
					//For Edit mode
					this.classSchedule.payload.BatchID = batchID;
				}
			} else {
				this.allBatches.data = [];
			}
		},
		fetchAllBatches(courseID, batchID) {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.courseListAPI("batch", courseID, isLoggedIn),
				module: "gotData",
				store: "allBatches",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotAllBatches(options, batchID);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		enableLoader(state) {
			this.isInProcess = state;
		},
		initGooglePermission() {
			localStorage.setItem("userState", window.location.pathname);
		},
		isPermissionModal(data) {
			if (
				data.vc_google &&
				data.ins_meet_permission &&
				!data.ins_meet_permission_verified
			) {
				this.googlePermission.modal = true;
			}
		},
		inviteOtherUser() {
			this.inviteEmail = true;

			setTimeout(() => {
				if (this.$refs.inviteUser.flags.valid) {
					let getLerners = this.classSchedule.payload.learner,
						userObj = {
							email: this.classSchedule.currentLearner,
							id: "",
							name: this.classSchedule.currentLearner,
							photo: "",
						};

					this.$refs.inviteUserAutocomplete.setSelected(userObj);
				}
			}, 40);
		},
		newTitleAdded(options) {
			this.loader.overlay = false;
			this.loader.isActive = false;

			if (options.response !== undefined && options.response.status === 201) {
				this.$buefy.toast.open({
					duration: 5000,
					message: `${options.response.data.message}`,
					position: "is-bottom",
				});

				let newTitle = {
					description: "",
					title: this.addClassTitle.payload.title,
				};

				this.classTitle.data.push(newTitle);
				this.$refs.classTitleAutocomplete.setSelected(newTitle);
			} else {
				this.$buefy.toast.open({
					duration: 5000,
					message: `${options.response.data.message}`,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		postNewTitle() {
			let payload = this.addClassTitle.payload,
				instance = this;

			const options = {
				apiURL: YUNOCommon.config.addClassTitleAPi(),
				module: "gotData",
				store: "addClassTitle",
				payload: payload,
				callback: true,
				callbackFunc: function (options) {
					return instance.newTitleAdded(options);
				},
			};

			this.$store.dispatch("postData", options);
		},
		addNewTitle() {
			let payload = this.addClassTitle.payload;

			payload.instructor_id = isLoggedIn;
			payload.title = this.classSchedule.payload.ClassTitle;

			this.loader.overlay = true;
			this.loader.isActive = true;
			this.postNewTitle();
		},
		updateLearners(userID, getMyLearners, classObj, userType) {
			let typeOfUser = "";

			if (userType === "group") {
				typeOfUser = "group_id";
			}

			if (userType === "learner") {
				typeOfUser = "id";
			}

			let selectedGroup = YUNOCommon.findObjectByKey(
				getMyLearners,
				typeOfUser,
				Number(userID)
			);

			classObj.push(selectedGroup);

			YUNOCommon.removeObjInArr(getMyLearners, "name", selectedGroup.name);

			if (selectedGroup.user_count !== undefined) {
				this.classSchedule.learnerSelected = parseInt(
					selectedGroup.user_count + this.classSchedule.learnerSelected
				);
			} else {
				this.classSchedule.learnerSelected =
					this.classSchedule.learnerSelected === ""
						? 1
						: parseInt(this.classSchedule.learnerSelected + 1);
			}
		},
		hasGroupID() {
			let isGroupID = YUNOCommon.getQueryParameter("groupID"),
				isLearnerID = YUNOCommon.getQueryParameter("learnerID"),
				getMyLearners = this.myLearners.data,
				classObj = this.classSchedule.payload.learner;

			if (isGroupID) {
				this.updateLearners(isGroupID, getMyLearners, classObj, "group");
			}

			if (isLearnerID) {
				this.updateLearners(isLearnerID, getMyLearners, classObj, "learner");
			}
		},
		isEditClass() {
			const getClassState = localStorage.getItem("classEditState");

			if (getClassState !== null) {
				return true;
			} else {
				return false;
			}
		},
		initiateEdit() {
			if (this.isEditClass()) {
				let getClassState = localStorage.getItem("classEditState"),
					parseIt = JSON.parse(getClassState);

				this.prefillClass(parseIt.id, isLoggedIn);
			}
		},
		gotClassDetail(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let getData = options.response.data.data,
					moduleObj = this.classEdit,
					myLearnerObj = this.myLearners,
					classObj = this.classSchedule,
					classPayload = classObj.payload,
					dateTime = new Date(getData.start + ":" + getData.time);

				classObj.date = dateTime;
				classObj.time = dateTime;

				classPayload.ClassTitle = getData.title;
				classPayload.ClassDescription = getData.description;
				classPayload.ClassDuration = getData.duration;
				classObj.relatedCourses = getData.related_course;
				classPayload.academy_id = getData.academy_id;

				if (getData.related_course !== "") {
					this.batches.isActive = true;
					this.batches.isRequired = true;
					this.fetchAllBatches(getData.related_course, getData.batch_id);
				}

				if (getData.users_groups.length !== 0) {
					for (var i = 0; i < getData.users_groups.length; i++) {
						classPayload.learner.push(getData.users_groups[i]);

						YUNOCommon.removeObjInArr(
							myLearnerObj.data,
							"name",
							getData.users_groups[i].name
						);

						if (getData.users_groups[i].user_count !== undefined) {
							classObj.learnerSelected = parseInt(
								getData.users_groups[i].user_count + classObj.learnerSelected
							);
						} else {
							classObj.learnerSelected =
								classObj.learnerSelected === ""
									? 1
									: parseInt(classObj.learnerSelected + 1);
						}
					}
				}

				moduleObj.data = getData;
			}
		},
		prefillClass(classID, userID) {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.editClassAPI(classID, userID),
				module: "gotData",
				store: "classEdit",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotClassDetail(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		initForm() {
			let dateObj = {
				date: moment(this.classSchedule.date).format("MMM DD, YYYY"),
				readableDate: moment(this.classSchedule.date).format(
					"ddd, MMM DD, YYYY"
				),
				time: moment(this.classSchedule.time).format("HH:mm"),
				time12Hour: moment(this.classSchedule.time).format("hh:mm a"),
			};

			let dateToUTC = moment(
				dateObj.date + " " + dateObj.time,
				"MMMM Do YYYY, HH:mm"
			).utc();

			this.classSchedule.payload.ClassDate = "" + dateToUTC._d + "";
			this.classSchedule.payload.ClassTime = "" + dateToUTC._d + "";
			this.classSchedule.payload.RelatedCourses = "";
			this.classSchedule.payload.RelatedCourses =
				this.classSchedule.relatedCourses;
			this.dateTimeFormat = dateObj.readableDate + ", " + dateObj.time12Hour;
			this.classSchedule.payload.academy_id !== ""
				? (this.classSchedule.modal = true)
				: (this.isCourseSelect = true);

			if (this.isEditClass()) {
				//For Edit mode
				let getClassState = localStorage.getItem("classEditState"),
					parseIt = JSON.parse(getClassState);

				this.classSchedule.payload.ClassId = parseIt.id;
			}
		},
		classScheduleDone(options) {
			this.classSchedule.isLoading = false;
			this.enableLoader(false);

			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 201
			) {
				localStorage.removeItem("classEditState");
				this.classSchedule.data = options.response.data.data;
				this.classSchedule.modal = false;
				this.classSchedule.successModal = true;
			} else {
				// let msg = this.classSchedule.errorData.TimeCheck !== undefined ? this.classSchedule.errorData.TimeCheck : this.classSchedule.errorData;

				this.$buefy.toast.open({
					duration: 5000,
					message: this.classSchedule.errorData,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		confirmClass() {
			if (this.classSchedule.payload.academy_id !== "") {
				this.classSchedule.modal = true;
				this.isCourseSelect = false;
			} else {
				this.classSchedule.modal = false;
				this.$buefy.toast.open({
					duration: 1000,
					message: `Please select an academy`,
					position: "is-bottom",
					type: "is-danger",
				});
			}
		},
		proceedSchedule() {
			this.enableLoader(true);
			this.submitForm();
		},
		noPeople() {
			this.classSchedule.modal = false;
			this.$buefy.dialog.confirm({
				title: "Add People",
				message: "You have not added any people in this class",
				cancelText: "Go Back",
				confirmText: "Proceed Anyway",
				onConfirm: () => this.proceedSchedule(),
			});
		},
		submitForm() {
			this.classSchedule.isLoading = true;
			let setAPIURL = "";

			if (this.isEditClass()) {
				//For Edit mode
				setAPIURL = YUNOCommon.config.updateClassAPi(isLoggedIn);
			} else {
				setAPIURL = YUNOCommon.config.createClassAPi(isLoggedIn);
			}
			const instance = this,
				options = {
					apiURL: setAPIURL,
					module: "gotData",
					store: "classSchedule",
					payload: this.classSchedule.payload,
					callback: true,
					callbackFunc: function (options) {
						return instance.classScheduleDone(options);
					},
				};

			this.$store.dispatch("postData", options);
		},
		copyToClipboard(ele) {
			let copyText = document.getElementById(ele);

			copyText.select();
			copyText.setSelectionRange(0, 99999);
			document.execCommand("copy");

			this.$buefy.toast.open({
				duration: 1000,
				message: `Copy to clipboard`,
				position: "is-bottom",
				type: "is-info",
			});
		},
		updateDateFormat(getDate, getTime) {
			this.dateTimeFormat = getDate + ", " + getTime;
		},
		onModalClose() { },
		removeLearner(obj) {
			let getLerners = this.classSchedule.payload.learner,
				refineLearnerObj = YUNOCommon.removeObjInArr(
					getLerners,
					"full_name",
					obj.full_name
				);

			this.myLearners.data.push(obj);
			getLerners = refineLearnerObj;

			if (obj.user_count !== undefined) {
				this.classSchedule.learnerSelected = Math.abs(
					parseInt(obj.user_count - this.classSchedule.learnerSelected)
				);
			} else {
				this.classSchedule.learnerSelected = Math.abs(
					parseInt(this.classSchedule.learnerSelected - 1)
				);
			}

			if (this.classSchedule.payload.learner.length === 0) {
				this.suggestUser.selectAll = false;
			}
		},
		onTitleSelect($event) {
			if ($event !== null) {
				let courseID = "";

				if ($event.course_id !== undefined) {
					courseID = $event.course_id !== "" ? Number($event.course_id) : "";
					this.onRelatedCourceChange(courseID);
				} else {
					courseID = "";
				}

				this.classSchedule.payload.ClassDescription = $event.description;
				this.classSchedule.relatedCourses = courseID;
			} else {
				this.classSchedule.payload.ClassDescription = "";
				this.classSchedule.relatedCourses = "";
				this.classSchedule.payload.BatchID = "";
				this.batches.isActive = false;
			}
		},
		picksuggestedUser(data) {
			let learners = this.classSchedule.payload.learner,
				userData = YUNOCommon.findObjectByKey(learners, "id", data.id);

			if (userData === null) {
				learners.push(data);

				this.classSchedule.learnerSelected =
					this.classSchedule.learnerSelected === ""
						? 1
						: parseInt(this.classSchedule.learnerSelected + 1);
			}
		},
		onLernerSelect($event) {
			if ($event !== null) {
				let getLerners = this.classSchedule.payload.learner;

				getLerners.push($event);

				let refineLearnerObj = YUNOCommon.removeObjInArr(
					this.myLearners.data,
					"full_name",
					$event.full_name
				);
				this.myLearners.data = refineLearnerObj;

				if ($event.user_count !== undefined) {
					this.classSchedule.learnerSelected = parseInt(
						$event.user_count + this.classSchedule.learnerSelected
					);
				} else {
					this.classSchedule.learnerSelected =
						this.classSchedule.learnerSelected === ""
							? 1
							: parseInt(this.classSchedule.learnerSelected + 1);
				}

				setTimeout(() => (this.classSchedule.currentLearner = ""), 10);
			}
		},
		gotMyLearner(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				for (let i = 0; i < data.length; i++) {
					const learner = data[i];
					const obj = {
						id: learner.id,
						full_name: learner.name,
						image_url: learner.photo,
						email: learner.email
					};

					this.myLearners.data.push(obj);
				}

				
				this.hasGroupID();
			} else {
				this.myLearners.data = [];
			}
		},
		fetchMyLearners() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.myLearnersAPi(isLoggedIn),
				module: "gotData",
				store: "myLearners",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotMyLearner(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		gotRelatedCourses(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				this.relatedCourses.data = data;
			} else {
				this.relatedCourses.data = [];
			}
		},
		fetchRelatedCourses() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.relatedCoursesAPI(isLoggedIn),
				module: "gotData",
				store: "relatedCourses",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotRelatedCourses(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		gotClassTitle(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				let data = options.response.data.data;

				this.classTitle.data = data;
			} else {
				this.classTitle.data = [];
			}
		},
		fetchClassTitle() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.classTitleAPi(isLoggedIn),
				module: "gotData",
				store: "classTitle",
				addToModule: false,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotClassTitle(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		getFilteredTags(text) {
			this.filteredTags = data.filter((option) => {
				return (
					option.user.first_name
						.toString()
						.toLowerCase()
						.indexOf(text.toLowerCase()) >= 0
				);
			});
		},
	},
});
