!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.moment=e()}(this,(function(){"use strict";var t;function e(){return t.apply(null,arguments)}function n(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function a(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function s(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function i(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;for(var e in t)if(s(t,e))return;return 1}function r(t){return void 0===t}function o(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function l(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function u(t,e){for(var n=[],a=t.length,s=0;s<a;++s)n.push(e(t[s],s));return n}function d(t,e){for(var n in e)s(e,n)&&(t[n]=e[n]);return s(e,"toString")&&(t.toString=e.toString),s(e,"valueOf")&&(t.valueOf=e.valueOf),t}function c(t,e,n,a){return Ce(t,e,n,a,!0).utc()}function h(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function m(t){var e,n,a=t._d&&!isNaN(t._d.getTime());return a&&(e=h(t),n=p.call(e.parsedDateParts,(function(t){return null!=t})),a=e.overflow<0&&!e.empty&&!e.invalidEra&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&n),t._strict)&&(a=a&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t)?a:(t._isValid=a,t._isValid)}function f(t){var e=c(NaN);return null!=t?d(h(e),t):h(e).userInvalidated=!0,e}var p=Array.prototype.some||function(t){for(var e=Object(this),n=e.length>>>0,a=0;a<n;a++)if(a in e&&t.call(this,e[a],a,e))return!0;return!1},v=e.momentProperties=[],g=!1;function y(t,e){var n,a,s,i=v.length;if(r(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),r(e._i)||(t._i=e._i),r(e._f)||(t._f=e._f),r(e._l)||(t._l=e._l),r(e._strict)||(t._strict=e._strict),r(e._tzm)||(t._tzm=e._tzm),r(e._isUTC)||(t._isUTC=e._isUTC),r(e._offset)||(t._offset=e._offset),r(e._pf)||(t._pf=h(e)),r(e._locale)||(t._locale=e._locale),0<i)for(n=0;n<i;n++)r(s=e[a=v[n]])||(t[a]=s);return t}function b(t){y(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===g&&(g=!0,e.updateOffset(this),g=!1)}function _(t){return t instanceof b||null!=t&&null!=t._isAMomentObject}function S(t){!1===e.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function w(t,n){var a=!0;return d((function(){if(null!=e.deprecationHandler&&e.deprecationHandler(null,t),a){for(var i,r,o=[],l=arguments.length,u=0;u<l;u++){if(i="","object"==typeof arguments[u]){for(r in i+="\n["+u+"] ",arguments[0])s(arguments[0],r)&&(i+=r+": "+arguments[0][r]+", ");i=i.slice(0,-2)}else i=arguments[u];o.push(i)}S(t+"\nArguments: "+Array.prototype.slice.call(o).join("")+"\n"+(new Error).stack),a=!1}return n.apply(this,arguments)}),n)}var k={};function M(t,n){null!=e.deprecationHandler&&e.deprecationHandler(t,n),k[t]||(S(n),k[t]=!0)}function A(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function C(t,e){var n,i=d({},t);for(n in e)s(e,n)&&(a(t[n])&&a(e[n])?(i[n]={},d(i[n],t[n]),d(i[n],e[n])):null!=e[n]?i[n]=e[n]:delete i[n]);for(n in t)s(t,n)&&!s(e,n)&&a(t[n])&&(i[n]=d({},i[n]));return i}function O(t){null!=t&&this.set(t)}e.suppressDeprecationWarnings=!1,e.deprecationHandler=null;var D=Object.keys||function(t){var e,n=[];for(e in t)s(t,e)&&n.push(e);return n};function T(t,e,n){var a=""+Math.abs(t);return(0<=t?n?"+":"":"-")+Math.pow(10,Math.max(0,e-a.length)).toString().substr(1)+a}var Y=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,I=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,x={},R={};function L(t,e,n,a){var s="string"==typeof a?function(){return this[a]()}:a;t&&(R[t]=s),e&&(R[e[0]]=function(){return T(s.apply(this,arguments),e[1],e[2])}),n&&(R[n]=function(){return this.localeData().ordinal(s.apply(this,arguments),t)})}function $(t,e){return t.isValid()?(e=N(e,t.localeData()),x[e]=x[e]||function(t){for(var e,n=t.match(Y),a=0,s=n.length;a<s;a++)R[n[a]]?n[a]=R[n[a]]:n[a]=(e=n[a]).match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"");return function(e){for(var a="",i=0;i<s;i++)a+=A(n[i])?n[i].call(e,t):n[i];return a}}(e),x[e](t)):t.localeData().invalidDate()}function N(t,e){var n=5;function a(t){return e.longDateFormat(t)||t}for(I.lastIndex=0;0<=n&&I.test(t);)t=t.replace(I,a),I.lastIndex=0,--n;return t}var P={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function U(t){return"string"==typeof t?P[t]||P[t.toLowerCase()]:void 0}function E(t){var e,n,a={};for(n in t)s(t,n)&&(e=U(n))&&(a[e]=t[n]);return a}var j={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},F=/\d/,W=/\d\d/,V=/\d{3}/,H=/\d{4}/,Z=/[+-]?\d{6}/,G=/\d\d?/,z=/\d\d\d\d?/,B=/\d\d\d\d\d\d?/,q=/\d{1,3}/,J=/\d{1,4}/,Q=/[+-]?\d{1,6}/,K=/\d+/,X=/[+-]?\d+/,tt=/Z|[+-]\d\d:?\d\d/gi,et=/Z|[+-]\d\d(?::?\d\d)?/gi,nt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,at=/^[1-9]\d?/,st=/^([1-9]\d|\d)/;function it(t,e,n){dt[t]=A(e)?e:function(t,a){return t&&n?n:e}}function rt(t,e){return s(dt,t)?dt[t](e._strict,e._locale):new RegExp(ot(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(t,e,n,a,s){return e||n||a||s}))))}function ot(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function lt(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function ut(t){var e=0;return 0!=(t=+t)&&isFinite(t)?lt(t):e}var dt={},ct={};function ht(t,e){var n,a,s=e;for("string"==typeof t&&(t=[t]),o(e)&&(s=function(t,n){n[e]=ut(t)}),a=t.length,n=0;n<a;n++)ct[t[n]]=s}function mt(t,e){ht(t,(function(t,n,a,s){a._w=a._w||{},e(t,a._w,a,s)}))}function ft(t){return t%4==0&&t%100!=0||t%400==0}var pt=0,vt=1,gt=2,yt=3,bt=4,_t=5,St=6,wt=7,kt=8;function Mt(t){return ft(t)?366:365}L("Y",0,0,(function(){var t=this.year();return t<=9999?T(t,4):"+"+t})),L(0,["YY",2],0,(function(){return this.year()%100})),L(0,["YYYY",4],0,"year"),L(0,["YYYYY",5],0,"year"),L(0,["YYYYYY",6,!0],0,"year"),it("Y",X),it("YY",G,W),it("YYYY",J,H),it("YYYYY",Q,Z),it("YYYYYY",Q,Z),ht(["YYYYY","YYYYYY"],pt),ht("YYYY",(function(t,n){n[pt]=2===t.length?e.parseTwoDigitYear(t):ut(t)})),ht("YY",(function(t,n){n[pt]=e.parseTwoDigitYear(t)})),ht("Y",(function(t,e){e[pt]=parseInt(t,10)})),e.parseTwoDigitYear=function(t){return ut(t)+(68<ut(t)?1900:2e3)};var At,Ct=Ot("FullYear",!0);function Ot(t,n){return function(a){return null!=a?(Tt(this,t,a),e.updateOffset(this,n),this):Dt(this,t)}}function Dt(t,e){if(!t.isValid())return NaN;var n=t._d,a=t._isUTC;switch(e){case"Milliseconds":return a?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return a?n.getUTCSeconds():n.getSeconds();case"Minutes":return a?n.getUTCMinutes():n.getMinutes();case"Hours":return a?n.getUTCHours():n.getHours();case"Date":return a?n.getUTCDate():n.getDate();case"Day":return a?n.getUTCDay():n.getDay();case"Month":return a?n.getUTCMonth():n.getMonth();case"FullYear":return a?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Tt(t,e,n){var a,s,i;if(t.isValid()&&!isNaN(n)){switch(a=t._d,s=t._isUTC,e){case"Milliseconds":return s?a.setUTCMilliseconds(n):a.setMilliseconds(n);case"Seconds":return s?a.setUTCSeconds(n):a.setSeconds(n);case"Minutes":return s?a.setUTCMinutes(n):a.setMinutes(n);case"Hours":return s?a.setUTCHours(n):a.setHours(n);case"Date":return s?a.setUTCDate(n):a.setDate(n);case"FullYear":break;default:return}e=n,i=t.month(),t=29!==(t=t.date())||1!==i||ft(e)?t:28,s?a.setUTCFullYear(e,i,t):a.setFullYear(e,i,t)}}function Yt(t,e){var n;return isNaN(t)||isNaN(e)?NaN:(t+=(e-(n=(e%(n=12)+n)%n))/12,1==n?ft(t)?29:28:31-n%7%2)}At=Array.prototype.indexOf||function(t){for(var e=0;e<this.length;++e)if(this[e]===t)return e;return-1},L("M",["MM",2],"Mo",(function(){return this.month()+1})),L("MMM",0,0,(function(t){return this.localeData().monthsShort(this,t)})),L("MMMM",0,0,(function(t){return this.localeData().months(this,t)})),it("M",G,at),it("MM",G,W),it("MMM",(function(t,e){return e.monthsShortRegex(t)})),it("MMMM",(function(t,e){return e.monthsRegex(t)})),ht(["M","MM"],(function(t,e){e[vt]=ut(t)-1})),ht(["MMM","MMMM"],(function(t,e,n,a){null!=(a=n._locale.monthsParse(t,a,n._strict))?e[vt]=a:h(n).invalidMonth=t}));var It="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),xt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Rt=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Lt=nt,$t=nt;function Nt(t,e){if(t.isValid()){if("string"==typeof e)if(/^\d+$/.test(e))e=ut(e);else if(!o(e=t.localeData().monthsParse(e)))return;var n=(n=t.date())<29?n:Math.min(n,Yt(t.year(),e));t._isUTC?t._d.setUTCMonth(e,n):t._d.setMonth(e,n)}}function Pt(t){return null!=t?(Nt(this,t),e.updateOffset(this,!0),this):Dt(this,"Month")}function Ut(){function t(t,e){return e.length-t.length}for(var e,n,a=[],s=[],i=[],r=0;r<12;r++)n=c([2e3,r]),e=ot(this.monthsShort(n,"")),n=ot(this.months(n,"")),a.push(e),s.push(n),i.push(n),i.push(e);a.sort(t),s.sort(t),i.sort(t),this._monthsRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Et(t,e,n,a,s,i,r){var o;return t<100&&0<=t?(o=new Date(t+400,e,n,a,s,i,r),isFinite(o.getFullYear())&&o.setFullYear(t)):o=new Date(t,e,n,a,s,i,r),o}function jt(t){var e;return t<100&&0<=t?((e=Array.prototype.slice.call(arguments))[0]=t+400,e=new Date(Date.UTC.apply(null,e)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function Ft(t,e,n){return(n=7+e-n)-(7+jt(t,0,n).getUTCDay()-e)%7-1}function Wt(t,e,n,a,s){var i;n=(e=1+7*(e-1)+(7+n-a)%7+Ft(t,a,s))<=0?Mt(i=t-1)+e:e>Mt(t)?(i=t+1,e-Mt(t)):(i=t,e);return{year:i,dayOfYear:n}}function Vt(t,e,n){var a,s,i=Ft(t.year(),e,n);return(i=Math.floor((t.dayOfYear()-i-1)/7)+1)<1?a=i+Ht(s=t.year()-1,e,n):i>Ht(t.year(),e,n)?(a=i-Ht(t.year(),e,n),s=t.year()+1):(s=t.year(),a=i),{week:a,year:s}}function Ht(t,e,n){var a=Ft(t,e,n);e=Ft(t+1,e,n);return(Mt(t)-a+e)/7}function Zt(t,e){return t.slice(e,7).concat(t.slice(0,e))}L("w",["ww",2],"wo","week"),L("W",["WW",2],"Wo","isoWeek"),it("w",G,at),it("ww",G,W),it("W",G,at),it("WW",G,W),mt(["w","ww","W","WW"],(function(t,e,n,a){e[a.substr(0,1)]=ut(t)})),L("d",0,"do","day"),L("dd",0,0,(function(t){return this.localeData().weekdaysMin(this,t)})),L("ddd",0,0,(function(t){return this.localeData().weekdaysShort(this,t)})),L("dddd",0,0,(function(t){return this.localeData().weekdays(this,t)})),L("e",0,0,"weekday"),L("E",0,0,"isoWeekday"),it("d",G),it("e",G),it("E",G),it("dd",(function(t,e){return e.weekdaysMinRegex(t)})),it("ddd",(function(t,e){return e.weekdaysShortRegex(t)})),it("dddd",(function(t,e){return e.weekdaysRegex(t)})),mt(["dd","ddd","dddd"],(function(t,e,n,a){null!=(a=n._locale.weekdaysParse(t,a,n._strict))?e.d=a:h(n).invalidWeekday=t})),mt(["d","e","E"],(function(t,e,n,a){e[a]=ut(t)}));var Gt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),zt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Bt="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),qt=nt,Jt=nt,Qt=nt;function Kt(){function t(t,e){return e.length-t.length}for(var e,n,a,s=[],i=[],r=[],o=[],l=0;l<7;l++)a=c([2e3,1]).day(l),e=ot(this.weekdaysMin(a,"")),n=ot(this.weekdaysShort(a,"")),a=ot(this.weekdays(a,"")),s.push(e),i.push(n),r.push(a),o.push(e),o.push(n),o.push(a);s.sort(t),i.sort(t),r.sort(t),o.sort(t),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Xt(){return this.hours()%12||12}function te(t,e){L(t,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)}))}function ee(t,e){return e._meridiemParse}L("H",["HH",2],0,"hour"),L("h",["hh",2],0,Xt),L("k",["kk",2],0,(function(){return this.hours()||24})),L("hmm",0,0,(function(){return""+Xt.apply(this)+T(this.minutes(),2)})),L("hmmss",0,0,(function(){return""+Xt.apply(this)+T(this.minutes(),2)+T(this.seconds(),2)})),L("Hmm",0,0,(function(){return""+this.hours()+T(this.minutes(),2)})),L("Hmmss",0,0,(function(){return""+this.hours()+T(this.minutes(),2)+T(this.seconds(),2)})),te("a",!0),te("A",!1),it("a",ee),it("A",ee),it("H",G,st),it("h",G,at),it("k",G,at),it("HH",G,W),it("hh",G,W),it("kk",G,W),it("hmm",z),it("hmmss",B),it("Hmm",z),it("Hmmss",B),ht(["H","HH"],yt),ht(["k","kk"],(function(t,e,n){t=ut(t),e[yt]=24===t?0:t})),ht(["a","A"],(function(t,e,n){n._isPm=n._locale.isPM(t),n._meridiem=t})),ht(["h","hh"],(function(t,e,n){e[yt]=ut(t),h(n).bigHour=!0})),ht("hmm",(function(t,e,n){var a=t.length-2;e[yt]=ut(t.substr(0,a)),e[bt]=ut(t.substr(a)),h(n).bigHour=!0})),ht("hmmss",(function(t,e,n){var a=t.length-4,s=t.length-2;e[yt]=ut(t.substr(0,a)),e[bt]=ut(t.substr(a,2)),e[_t]=ut(t.substr(s)),h(n).bigHour=!0})),ht("Hmm",(function(t,e,n){var a=t.length-2;e[yt]=ut(t.substr(0,a)),e[bt]=ut(t.substr(a))})),ht("Hmmss",(function(t,e,n){var a=t.length-4,s=t.length-2;e[yt]=ut(t.substr(0,a)),e[bt]=ut(t.substr(a,2)),e[_t]=ut(t.substr(s))})),nt=Ot("Hours",!0);var ne,ae={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:It,monthsShort:xt,week:{dow:0,doy:6},weekdays:Gt,weekdaysMin:Bt,weekdaysShort:zt,meridiemParse:/[ap]\.?m?\.?/i},se={},ie={};function re(t){return t&&t.toLowerCase().replace("_","-")}function oe(t){var e,n;if(void 0===se[t]&&"undefined"!=typeof module&&module&&module.exports&&(n=t)&&n.match("^[^/\\\\]*$"))try{e=ne._abbr,require("./locale/"+t),le(e)}catch(e){se[t]=null}return se[t]}function le(t,e){return t&&((e=r(e)?de(t):ue(t,e))?ne=e:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),ne._abbr}function ue(t,e){if(null===e)return delete se[t],null;var n,a=ae;if(e.abbr=t,null!=se[t])M("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=se[t]._config;else if(null!=e.parentLocale)if(null!=se[e.parentLocale])a=se[e.parentLocale]._config;else{if(null==(n=oe(e.parentLocale)))return ie[e.parentLocale]||(ie[e.parentLocale]=[]),ie[e.parentLocale].push({name:t,config:e}),null;a=n._config}return se[t]=new O(C(a,e)),ie[t]&&ie[t].forEach((function(t){ue(t.name,t.config)})),le(t),se[t]}function de(t){var e;if(!(t=t&&t._locale&&t._locale._abbr?t._locale._abbr:t))return ne;if(!n(t)){if(e=oe(t))return e;t=[t]}return function(t){for(var e,n,a,s,i=0;i<t.length;){for(e=(s=re(t[i]).split("-")).length,n=(n=re(t[i+1]))?n.split("-"):null;0<e;){if(a=oe(s.slice(0,e).join("-")))return a;if(n&&n.length>=e&&function(t,e){for(var n=Math.min(t.length,e.length),a=0;a<n;a+=1)if(t[a]!==e[a])return a;return n}(s,n)>=e-1)break;e--}i++}return ne}(t)}function ce(t){var e=t._a;return e&&-2===h(t).overflow&&(e=e[vt]<0||11<e[vt]?vt:e[gt]<1||e[gt]>Yt(e[pt],e[vt])?gt:e[yt]<0||24<e[yt]||24===e[yt]&&(0!==e[bt]||0!==e[_t]||0!==e[St])?yt:e[bt]<0||59<e[bt]?bt:e[_t]<0||59<e[_t]?_t:e[St]<0||999<e[St]?St:-1,h(t)._overflowDayOfYear&&(e<pt||gt<e)&&(e=gt),h(t)._overflowWeeks&&-1===e&&(e=wt),h(t)._overflowWeekday&&-1===e&&(e=kt),h(t).overflow=e),t}var he=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,me=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,fe=/Z|[+-]\d\d(?::?\d\d)?/,pe=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ve=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ge=/^\/?Date\((-?\d+)/i,ye=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,be={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function _e(t){var e,n,a,s,i,r,o=t._i,l=he.exec(o)||me.exec(o),u=(o=pe.length,ve.length);if(l){for(h(t).iso=!0,e=0,n=o;e<n;e++)if(pe[e][1].exec(l[1])){s=pe[e][0],a=!1!==pe[e][2];break}if(null==s)t._isValid=!1;else{if(l[3]){for(e=0,n=u;e<n;e++)if(ve[e][1].exec(l[3])){i=(l[2]||" ")+ve[e][0];break}if(null==i)return void(t._isValid=!1)}if(a||null==i){if(l[4]){if(!fe.exec(l[4]))return void(t._isValid=!1);r="Z"}t._f=s+(i||"")+(r||""),Me(t)}else t._isValid=!1}}else t._isValid=!1}function Se(t){var e,n,a=ye.exec(t._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));a?(e=function(t,e,n,a,s,i){return t=[function(t){return(t=parseInt(t,10))<=49?2e3+t:t<=999?1900+t:t}(t),xt.indexOf(e),parseInt(n,10),parseInt(a,10),parseInt(s,10)],i&&t.push(parseInt(i,10)),t}(a[4],a[3],a[2],a[5],a[6],a[7]),function(t,e,n){if(!t||zt.indexOf(t)===new Date(e[0],e[1],e[2]).getDay())return 1;h(n).weekdayMismatch=!0,n._isValid=!1}(a[1],e,t)&&(t._a=e,t._tzm=(e=a[8],n=a[9],a=a[10],e?be[e]:n?0:((e=parseInt(a,10))-(n=e%100))/100*60+n),t._d=jt.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),h(t).rfc2822=!0)):t._isValid=!1}function we(t,e,n){return null!=t?t:null!=e?e:n}function ke(t){var n,a,s,i,r,o,l,u,d,c,m,f=[];if(!t._d){for(s=t,i=new Date(e.now()),a=s._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],t._w&&null==t._a[gt]&&null==t._a[vt]&&(null!=(i=(s=t)._w).GG||null!=i.W||null!=i.E?(u=1,d=4,r=we(i.GG,s._a[pt],Vt(Oe(),1,4).year),o=we(i.W,1),((l=we(i.E,1))<1||7<l)&&(c=!0)):(u=s._locale._week.dow,d=s._locale._week.doy,m=Vt(Oe(),u,d),r=we(i.gg,s._a[pt],m.year),o=we(i.w,m.week),null!=i.d?((l=i.d)<0||6<l)&&(c=!0):null!=i.e?(l=i.e+u,(i.e<0||6<i.e)&&(c=!0)):l=u),o<1||o>Ht(r,u,d)?h(s)._overflowWeeks=!0:null!=c?h(s)._overflowWeekday=!0:(m=Wt(r,o,l,u,d),s._a[pt]=m.year,s._dayOfYear=m.dayOfYear)),null!=t._dayOfYear&&(i=we(t._a[pt],a[pt]),(t._dayOfYear>Mt(i)||0===t._dayOfYear)&&(h(t)._overflowDayOfYear=!0),c=jt(i,0,t._dayOfYear),t._a[vt]=c.getUTCMonth(),t._a[gt]=c.getUTCDate()),n=0;n<3&&null==t._a[n];++n)t._a[n]=f[n]=a[n];for(;n<7;n++)t._a[n]=f[n]=null==t._a[n]?2===n?1:0:t._a[n];24===t._a[yt]&&0===t._a[bt]&&0===t._a[_t]&&0===t._a[St]&&(t._nextDay=!0,t._a[yt]=0),t._d=(t._useUTC?jt:Et).apply(null,f),r=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[yt]=24),t._w&&void 0!==t._w.d&&t._w.d!==r&&(h(t).weekdayMismatch=!0)}}function Me(t){if(t._f===e.ISO_8601)_e(t);else if(t._f===e.RFC_2822)Se(t);else{t._a=[],h(t).empty=!0;for(var n,a,i,r,o,l=""+t._i,u=l.length,d=0,c=N(t._f,t._locale).match(Y)||[],m=c.length,f=0;f<m;f++)a=c[f],(n=(l.match(rt(a,t))||[])[0])&&(0<(i=l.substr(0,l.indexOf(n))).length&&h(t).unusedInput.push(i),l=l.slice(l.indexOf(n)+n.length),d+=n.length),R[a]?(n?h(t).empty=!1:h(t).unusedTokens.push(a),i=a,o=t,null!=(r=n)&&s(ct,i)&&ct[i](r,o._a,o,i)):t._strict&&!n&&h(t).unusedTokens.push(a);h(t).charsLeftOver=u-d,0<l.length&&h(t).unusedInput.push(l),t._a[yt]<=12&&!0===h(t).bigHour&&0<t._a[yt]&&(h(t).bigHour=void 0),h(t).parsedDateParts=t._a.slice(0),h(t).meridiem=t._meridiem,t._a[yt]=function(t,e,n){return null==n?e:null!=t.meridiemHour?t.meridiemHour(e,n):null!=t.isPM?((t=t.isPM(n))&&e<12&&(e+=12),e=t||12!==e?e:0):e}(t._locale,t._a[yt],t._meridiem),null!==(u=h(t).era)&&(t._a[pt]=t._locale.erasConvertYear(u,t._a[pt])),ke(t),ce(t)}}function Ae(t){var s,i,c,p=t._i,v=t._f;if(t._locale=t._locale||de(t._l),null===p||void 0===v&&""===p)return f({nullInput:!0});if("string"==typeof p&&(t._i=p=t._locale.preparse(p)),_(p))return new b(ce(p));if(l(p))t._d=p;else if(n(v)){var g,S,w,k,M,A,C=t,O=!1,D=C._f.length;if(0===D)h(C).invalidFormat=!0,C._d=new Date(NaN);else{for(k=0;k<D;k++)M=0,A=!1,g=y({},C),null!=C._useUTC&&(g._useUTC=C._useUTC),g._f=C._f[k],Me(g),m(g)&&(A=!0),M=(M+=h(g).charsLeftOver)+10*h(g).unusedTokens.length,h(g).score=M,O?M<w&&(w=M,S=g):(null==w||M<w||A)&&(w=M,S=g,A)&&(O=!0);d(C,S||g)}}else v?Me(t):r(v=(p=t)._i)?p._d=new Date(e.now()):l(v)?p._d=new Date(v.valueOf()):"string"==typeof v?(i=p,null!==(s=ge.exec(i._i))?i._d=new Date(+s[1]):(_e(i),!1===i._isValid&&(delete i._isValid,Se(i),!1===i._isValid)&&(delete i._isValid,i._strict?i._isValid=!1:e.createFromInputFallback(i)))):n(v)?(p._a=u(v.slice(0),(function(t){return parseInt(t,10)})),ke(p)):a(v)?(s=p)._d||(c=void 0===(i=E(s._i)).day?i.date:i.day,s._a=u([i.year,i.month,c,i.hour,i.minute,i.second,i.millisecond],(function(t){return t&&parseInt(t,10)})),ke(s)):o(v)?p._d=new Date(v):e.createFromInputFallback(p);return m(t)||(t._d=null),t}function Ce(t,e,s,r,o){var l={};return!0!==e&&!1!==e||(r=e,e=void 0),!0!==s&&!1!==s||(r=s,s=void 0),(a(t)&&i(t)||n(t)&&0===t.length)&&(t=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=o,l._l=s,l._i=t,l._f=e,l._strict=r,(o=new b(ce(Ae(o=l))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Oe(t,e,n,a){return Ce(t,e,n,a,!1)}function De(t,e){var a,s;if(!(e=1===e.length&&n(e[0])?e[0]:e).length)return Oe();for(a=e[0],s=1;s<e.length;++s)e[s].isValid()&&!e[s][t](a)||(a=e[s]);return a}e.createFromInputFallback=w("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))})),e.ISO_8601=function(){},e.RFC_2822=function(){},z=w("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var t=Oe.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:f()})),B=w("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var t=Oe.apply(null,arguments);return this.isValid()&&t.isValid()?this<t?this:t:f()}));var Te=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ye(t){var e=(t=E(t)).year||0,n=t.quarter||0,a=t.month||0,i=t.week||t.isoWeek||0,r=t.day||0,o=t.hour||0,l=t.minute||0,u=t.second||0,d=t.millisecond||0;this._isValid=function(t){var e,n,a=!1,i=Te.length;for(e in t)if(s(t,e)&&(-1===At.call(Te,e)||null!=t[e]&&isNaN(t[e])))return!1;for(n=0;n<i;++n)if(t[Te[n]]){if(a)return!1;parseFloat(t[Te[n]])!==ut(t[Te[n]])&&(a=!0)}return!0}(t),this._milliseconds=+d+1e3*u+6e4*l+1e3*o*60*60,this._days=+r+7*i,this._months=+a+3*n+12*e,this._data={},this._locale=de(),this._bubble()}function Ie(t){return t instanceof Ye}function xe(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Re(t,e){L(t,0,0,(function(){var t=this.utcOffset(),n="+";return t<0&&(t=-t,n="-"),n+T(~~(t/60),2)+e+T(~~t%60,2)}))}Re("Z",":"),Re("ZZ",""),it("Z",et),it("ZZ",et),ht(["Z","ZZ"],(function(t,e,n){n._useUTC=!0,n._tzm=$e(et,t)}));var Le=/([\+\-]|\d\d)/gi;function $e(t,e){return null===(e=(e||"").match(t))?null:0===(e=60*(t=((e[e.length-1]||[])+"").match(Le)||["-",0,0])[1]+ut(t[2]))?0:"+"===t[0]?e:-e}function Ne(t,n){var a;return n._isUTC?(n=n.clone(),a=(_(t)||l(t)?t:Oe(t)).valueOf()-n.valueOf(),n._d.setTime(n._d.valueOf()+a),e.updateOffset(n,!1),n):Oe(t).local()}function Pe(t){return-Math.round(t._d.getTimezoneOffset())}function Ue(){return!!this.isValid()&&this._isUTC&&0===this._offset}e.updateOffset=function(){};var Ee=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,je=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Fe(t,e){var n,a=t;return Ie(t)?a={ms:t._milliseconds,d:t._days,M:t._months}:o(t)||!isNaN(+t)?(a={},e?a[e]=+t:a.milliseconds=+t):(e=Ee.exec(t))?(n="-"===e[1]?-1:1,a={y:0,d:ut(e[gt])*n,h:ut(e[yt])*n,m:ut(e[bt])*n,s:ut(e[_t])*n,ms:ut(xe(1e3*e[St]))*n}):(e=je.exec(t))?(n="-"===e[1]?-1:1,a={y:We(e[2],n),M:We(e[3],n),w:We(e[4],n),d:We(e[5],n),h:We(e[6],n),m:We(e[7],n),s:We(e[8],n)}):null==a?a={}:"object"==typeof a&&("from"in a||"to"in a)&&(e=function(t,e){var n;return t.isValid()&&e.isValid()?(e=Ne(e,t),t.isBefore(e)?n=Ve(t,e):((n=Ve(e,t)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Oe(a.from),Oe(a.to)),(a={}).ms=e.milliseconds,a.M=e.months),n=new Ye(a),Ie(t)&&s(t,"_locale")&&(n._locale=t._locale),Ie(t)&&s(t,"_isValid")&&(n._isValid=t._isValid),n}function We(t,e){return t=t&&parseFloat(t.replace(",",".")),(isNaN(t)?0:t)*e}function Ve(t,e){var n={};return n.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(n.months,"M").isAfter(e)&&--n.months,n.milliseconds=+e-+t.clone().add(n.months,"M"),n}function He(t,e){return function(n,a){var s;return null===a||isNaN(+a)||(M(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=n,n=a,a=s),Ze(this,Fe(n,a),t),this}}function Ze(t,n,a,s){var i=n._milliseconds,r=xe(n._days);n=xe(n._months);t.isValid()&&(s=null==s||s,n&&Nt(t,Dt(t,"Month")+n*a),r&&Tt(t,"Date",Dt(t,"Date")+r*a),i&&t._d.setTime(t._d.valueOf()+i*a),s)&&e.updateOffset(t,r||n)}function Ge(t){return"string"==typeof t||t instanceof String}function ze(t,e){var n,a;return t.date()<e.date()?-ze(e,t):-((n=12*(e.year()-t.year())+(e.month()-t.month()))+(e-(a=t.clone().add(n,"months"))<0?(e-a)/(a-t.clone().add(n-1,"months")):(e-a)/(t.clone().add(1+n,"months")-a)))||0}function Be(t){return void 0===t?this._locale._abbr:(null!=(t=de(t))&&(this._locale=t),this)}function qe(){return this._locale}Fe.fn=Ye.prototype,Fe.invalid=function(){return Fe(NaN)},It=He(1,"add"),Gt=He(-1,"subtract"),e.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",e.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",Bt=w("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(t){return void 0===t?this.localeData():this.locale(t)}));var Je=126227808e5;function Qe(t,e){return(t%e+e)%e}function Ke(t,e,n){return t<100&&0<=t?new Date(t+400,e,n)-Je:new Date(t,e,n).valueOf()}function Xe(t,e,n){return t<100&&0<=t?Date.UTC(t+400,e,n)-Je:Date.UTC(t,e,n)}function tn(t,e){return e.erasAbbrRegex(t)}function en(){for(var t,e,n,a=[],s=[],i=[],r=[],o=this.eras(),l=0,u=o.length;l<u;++l)t=ot(o[l].name),e=ot(o[l].abbr),n=ot(o[l].narrow),s.push(t),a.push(e),i.push(n),r.push(t),r.push(e),r.push(n);this._erasRegex=new RegExp("^("+r.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+s.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+i.join("|")+")","i")}function nn(t,e){L(0,[t,t.length],0,e)}function an(t,e,n,a,s){var i;return null==t?Vt(this,a,s).year:(i=Ht(t,a,s),function(t,e,n,a,s){return e=jt((t=Wt(t,e,n,a,s)).year,0,t.dayOfYear),this.year(e.getUTCFullYear()),this.month(e.getUTCMonth()),this.date(e.getUTCDate()),this}.call(this,t,e=i<e?i:e,n,a,s))}L("N",0,0,"eraAbbr"),L("NN",0,0,"eraAbbr"),L("NNN",0,0,"eraAbbr"),L("NNNN",0,0,"eraName"),L("NNNNN",0,0,"eraNarrow"),L("y",["y",1],"yo","eraYear"),L("y",["yy",2],0,"eraYear"),L("y",["yyy",3],0,"eraYear"),L("y",["yyyy",4],0,"eraYear"),it("N",tn),it("NN",tn),it("NNN",tn),it("NNNN",(function(t,e){return e.erasNameRegex(t)})),it("NNNNN",(function(t,e){return e.erasNarrowRegex(t)})),ht(["N","NN","NNN","NNNN","NNNNN"],(function(t,e,n,a){(a=n._locale.erasParse(t,a,n._strict))?h(n).era=a:h(n).invalidEra=t})),it("y",K),it("yy",K),it("yyy",K),it("yyyy",K),it("yo",(function(t,e){return e._eraYearOrdinalRegex||K})),ht(["y","yy","yyy","yyyy"],pt),ht(["yo"],(function(t,e,n,a){var s;n._locale._eraYearOrdinalRegex&&(s=t.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?e[pt]=n._locale.eraYearOrdinalParse(t,s):e[pt]=parseInt(t,10)})),L(0,["gg",2],0,(function(){return this.weekYear()%100})),L(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),nn("gggg","weekYear"),nn("ggggg","weekYear"),nn("GGGG","isoWeekYear"),nn("GGGGG","isoWeekYear"),it("G",X),it("g",X),it("GG",G,W),it("gg",G,W),it("GGGG",J,H),it("gggg",J,H),it("GGGGG",Q,Z),it("ggggg",Q,Z),mt(["gggg","ggggg","GGGG","GGGGG"],(function(t,e,n,a){e[a.substr(0,2)]=ut(t)})),mt(["gg","GG"],(function(t,n,a,s){n[s]=e.parseTwoDigitYear(t)})),L("Q",0,"Qo","quarter"),it("Q",F),ht("Q",(function(t,e){e[vt]=3*(ut(t)-1)})),L("D",["DD",2],"Do","date"),it("D",G,at),it("DD",G,W),it("Do",(function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient})),ht(["D","DD"],gt),ht("Do",(function(t,e){e[gt]=ut(t.match(G)[0])})),J=Ot("Date",!0),L("DDD",["DDDD",3],"DDDo","dayOfYear"),it("DDD",q),it("DDDD",V),ht(["DDD","DDDD"],(function(t,e,n){n._dayOfYear=ut(t)})),L("m",["mm",2],0,"minute"),it("m",G,st),it("mm",G,W),ht(["m","mm"],bt);var sn;H=Ot("Minutes",!1),L("s",["ss",2],0,"second"),it("s",G,st),it("ss",G,W),ht(["s","ss"],_t),Q=Ot("Seconds",!1);for(L("S",0,0,(function(){return~~(this.millisecond()/100)})),L(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),L(0,["SSS",3],0,"millisecond"),L(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),L(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),L(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),L(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),L(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),L(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),it("S",q,F),it("SS",q,W),it("SSS",q,V),sn="SSSS";sn.length<=9;sn+="S")it(sn,K);function rn(t,e){e[St]=ut(1e3*("0."+t))}for(sn="S";sn.length<=9;sn+="S")ht(sn,rn);function on(t){return t}function ln(t,e,n,a){var s=de();a=c().set(a,e);return s[n](a,t)}function un(t,e,n){if(o(t)&&(e=t,t=void 0),t=t||"",null!=e)return ln(t,e,n,"month");for(var a=[],s=0;s<12;s++)a[s]=ln(t,s,n,"month");return a}function dn(t,e,n,a){"boolean"==typeof t?o(e)&&(n=e,e=void 0):(e=t,t=!1,o(n=e)&&(n=e,e=void 0)),e=e||"";var s,i=de(),r=t?i._week.dow:0,l=[];if(null!=n)return ln(e,(n+r)%7,a,"day");for(s=0;s<7;s++)l[s]=ln(e,(s+r)%7,a,"day");return l}Z=Ot("Milliseconds",!1),L("z",0,0,"zoneAbbr"),L("zz",0,0,"zoneName"),(at=b.prototype).add=It,at.calendar=function(t,r){1===arguments.length&&(arguments[0]?function(t){return _(t)||l(t)||Ge(t)||o(t)||function(t){var e=n(t),a=!1;return e&&(a=0===t.filter((function(e){return!o(e)&&Ge(t)})).length),e&&a}(t)||function(t){var e,n=a(t)&&!i(t),r=!1,o=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],l=o.length;for(e=0;e<l;e+=1)r=r||s(t,o[e]);return n&&r}(t)||null==t}(arguments[0])?(t=arguments[0],r=void 0):function(t){for(var e=a(t)&&!i(t),n=!1,r=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],o=0;o<r.length;o+=1)n=n||s(t,r[o]);return e&&n}(arguments[0])&&(r=arguments[0],t=void 0):r=t=void 0);var u=Ne(t=t||Oe(),this).startOf("day");u=e.calendarFormat(this,u)||"sameElse",r=r&&(A(r[u])?r[u].call(this,t):r[u]);return this.format(r||this.localeData().calendar(u,this,Oe(t)))},at.clone=function(){return new b(this)},at.diff=function(t,e,n){var a,s,i;if(!this.isValid())return NaN;if(!(a=Ne(t,this)).isValid())return NaN;switch(s=6e4*(a.utcOffset()-this.utcOffset()),e=U(e)){case"year":i=ze(this,a)/12;break;case"month":i=ze(this,a);break;case"quarter":i=ze(this,a)/3;break;case"second":i=(this-a)/1e3;break;case"minute":i=(this-a)/6e4;break;case"hour":i=(this-a)/36e5;break;case"day":i=(this-a-s)/864e5;break;case"week":i=(this-a-s)/6048e5;break;default:i=this-a}return n?i:lt(i)},at.endOf=function(t){var n,a;if(void 0!==(t=U(t))&&"millisecond"!==t&&this.isValid()){switch(a=this._isUTC?Xe:Ke,t){case"year":n=a(this.year()+1,0,1)-1;break;case"quarter":n=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":n=a(this.year(),this.month()+1,1)-1;break;case"week":n=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":n=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":n=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":n=this._d.valueOf(),n+=36e5-Qe(n+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":n=this._d.valueOf(),n+=6e4-Qe(n,6e4)-1;break;case"second":n=this._d.valueOf(),n+=1e3-Qe(n,1e3)-1}this._d.setTime(n),e.updateOffset(this,!0)}return this},at.format=function(t){return t=$(this,t=t||(this.isUtc()?e.defaultFormatUtc:e.defaultFormat)),this.localeData().postformat(t)},at.from=function(t,e){return this.isValid()&&(_(t)&&t.isValid()||Oe(t).isValid())?Fe({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},at.fromNow=function(t){return this.from(Oe(),t)},at.to=function(t,e){return this.isValid()&&(_(t)&&t.isValid()||Oe(t).isValid())?Fe({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},at.toNow=function(t){return this.to(Oe(),t)},at.get=function(t){return A(this[t=U(t)])?this[t]():this},at.invalidAt=function(){return h(this).overflow},at.isAfter=function(t,e){return t=_(t)?t:Oe(t),!(!this.isValid()||!t.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()>t.valueOf():t.valueOf()<this.clone().startOf(e).valueOf())},at.isBefore=function(t,e){return t=_(t)?t:Oe(t),!(!this.isValid()||!t.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()<t.valueOf():this.clone().endOf(e).valueOf()<t.valueOf())},at.isBetween=function(t,e,n,a){return t=_(t)?t:Oe(t),e=_(e)?e:Oe(e),!!(this.isValid()&&t.isValid()&&e.isValid())&&("("===(a=a||"()")[0]?this.isAfter(t,n):!this.isBefore(t,n))&&(")"===a[1]?this.isBefore(e,n):!this.isAfter(e,n))},at.isSame=function(t,e){t=_(t)?t:Oe(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()===t.valueOf():(t=t.valueOf(),this.clone().startOf(e).valueOf()<=t&&t<=this.clone().endOf(e).valueOf()))},at.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},at.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},at.isValid=function(){return m(this)},at.lang=Bt,at.locale=Be,at.localeData=qe,at.max=B,at.min=z,at.parsingFlags=function(){return d({},h(this))},at.set=function(t,e){if("object"==typeof t)for(var n=function(t){var e,n=[];for(e in t)s(t,e)&&n.push({unit:e,priority:j[e]});return n.sort((function(t,e){return t.priority-e.priority})),n}(t=E(t)),a=n.length,i=0;i<a;i++)this[n[i].unit](t[n[i].unit]);else if(A(this[t=U(t)]))return this[t](e);return this},at.startOf=function(t){var n,a;if(void 0!==(t=U(t))&&"millisecond"!==t&&this.isValid()){switch(a=this._isUTC?Xe:Ke,t){case"year":n=a(this.year(),0,1);break;case"quarter":n=a(this.year(),this.month()-this.month()%3,1);break;case"month":n=a(this.year(),this.month(),1);break;case"week":n=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":n=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":n=a(this.year(),this.month(),this.date());break;case"hour":n=this._d.valueOf(),n-=Qe(n+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":n=this._d.valueOf(),n-=Qe(n,6e4);break;case"second":n=this._d.valueOf(),n-=Qe(n,1e3)}this._d.setTime(n),e.updateOffset(this,!0)}return this},at.subtract=Gt,at.toArray=function(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]},at.toObject=function(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}},at.toDate=function(){return new Date(this.valueOf())},at.toISOString=function(t){var e;return this.isValid()?(e=(t=!0!==t)?this.clone().utc():this).year()<0||9999<e.year()?$(e,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):A(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",$(e,"Z")):$(e,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},at.inspect=function(){var t,e,n;return this.isValid()?(e="moment",t="",this.isLocal()||(e=0===this.utcOffset()?"moment.utc":"moment.parseZone",t="Z"),e="["+e+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(e+n+"-MM-DD[T]HH:mm:ss.SSS"+t+'[")]')):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(at[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),at.toJSON=function(){return this.isValid()?this.toISOString():null},at.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},at.unix=function(){return Math.floor(this.valueOf()/1e3)},at.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},at.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},at.eraName=function(){for(var t,e=this.localeData().eras(),n=0,a=e.length;n<a;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].name;if(e[n].until<=t&&t<=e[n].since)return e[n].name}return""},at.eraNarrow=function(){for(var t,e=this.localeData().eras(),n=0,a=e.length;n<a;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].narrow;if(e[n].until<=t&&t<=e[n].since)return e[n].narrow}return""},at.eraAbbr=function(){for(var t,e=this.localeData().eras(),n=0,a=e.length;n<a;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].abbr;if(e[n].until<=t&&t<=e[n].since)return e[n].abbr}return""},at.eraYear=function(){for(var t,n,a=this.localeData().eras(),s=0,i=a.length;s<i;++s)if(t=a[s].since<=a[s].until?1:-1,n=this.clone().startOf("day").valueOf(),a[s].since<=n&&n<=a[s].until||a[s].until<=n&&n<=a[s].since)return(this.year()-e(a[s].since).year())*t+a[s].offset;return this.year()},at.year=Ct,at.isLeapYear=function(){return ft(this.year())},at.weekYear=function(t){return an.call(this,t,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},at.isoWeekYear=function(t){return an.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},at.quarter=at.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},at.month=Pt,at.daysInMonth=function(){return Yt(this.year(),this.month())},at.week=at.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},at.isoWeek=at.isoWeeks=function(t){var e=Vt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},at.weeksInYear=function(){var t=this.localeData()._week;return Ht(this.year(),t.dow,t.doy)},at.weeksInWeekYear=function(){var t=this.localeData()._week;return Ht(this.weekYear(),t.dow,t.doy)},at.isoWeeksInYear=function(){return Ht(this.year(),1,4)},at.isoWeeksInISOWeekYear=function(){return Ht(this.isoWeekYear(),1,4)},at.date=J,at.day=at.days=function(t){var e,n,a;return this.isValid()?(e=Dt(this,"Day"),null!=t?(n=t,a=this.localeData(),t="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=a.weekdaysParse(n))?n:null:parseInt(n,10),this.add(t-e,"d")):e):null!=t?this:NaN},at.weekday=function(t){var e;return this.isValid()?(e=(this.day()+7-this.localeData()._week.dow)%7,null==t?e:this.add(t-e,"d")):null!=t?this:NaN},at.isoWeekday=function(t){var e,n;return this.isValid()?null!=t?(e=t,n=this.localeData(),n="string"==typeof e?n.weekdaysParse(e)%7||7:isNaN(e)?null:e,this.day(this.day()%7?n:n-7)):this.day()||7:null!=t?this:NaN},at.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},at.hour=at.hours=nt,at.minute=at.minutes=H,at.second=at.seconds=Q,at.millisecond=at.milliseconds=Z,at.utcOffset=function(t,n,a){var s,i=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null==t)return this._isUTC?i:Pe(this);if("string"==typeof t){if(null===(t=$e(et,t)))return this}else Math.abs(t)<16&&!a&&(t*=60);return!this._isUTC&&n&&(s=Pe(this)),this._offset=t,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==t&&(!n||this._changeInProgress?Ze(this,Fe(t-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,e.updateOffset(this,!0),this._changeInProgress=null)),this},at.utc=function(t){return this.utcOffset(0,t)},at.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t)&&this.subtract(Pe(this),"m"),this},at.parseZone=function(){var t;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(t=$e(tt,this._i))?this.utcOffset(t):this.utcOffset(0,!0)),this},at.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?Oe(t).utcOffset():0,(this.utcOffset()-t)%60==0)},at.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},at.isLocal=function(){return!!this.isValid()&&!this._isUTC},at.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},at.isUtc=Ue,at.isUTC=Ue,at.zoneAbbr=function(){return this._isUTC?"UTC":""},at.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},at.dates=w("dates accessor is deprecated. Use date instead.",J),at.months=w("months accessor is deprecated. Use month instead",Pt),at.years=w("years accessor is deprecated. Use year instead",Ct),at.zone=w("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(t,e){return null!=t?(this.utcOffset(t="string"!=typeof t?-t:t,e),this):-this.utcOffset()})),at.isDSTShifted=w("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){var t,e;return r(this._isDSTShifted)&&(y(t={},this),(t=Ae(t))._a?(e=(t._isUTC?c:Oe)(t._a),this._isDSTShifted=this.isValid()&&0<function(t,e,n){for(var a=Math.min(t.length,e.length),s=Math.abs(t.length-e.length),i=0,r=0;r<a;r++)ut(t[r])!==ut(e[r])&&i++;return i+s}(t._a,e.toArray())):this._isDSTShifted=!1),this._isDSTShifted})),(st=O.prototype).calendar=function(t,e,n){return A(t=this._calendar[t]||this._calendar.sameElse)?t.call(e,n):t},st.longDateFormat=function(t){var e=this._longDateFormat[t],n=this._longDateFormat[t.toUpperCase()];return e||!n?e:(this._longDateFormat[t]=n.match(Y).map((function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t})).join(""),this._longDateFormat[t])},st.invalidDate=function(){return this._invalidDate},st.ordinal=function(t){return this._ordinal.replace("%d",t)},st.preparse=on,st.postformat=on,st.relativeTime=function(t,e,n,a){var s=this._relativeTime[n];return A(s)?s(t,e,n,a):s.replace(/%d/i,t)},st.pastFuture=function(t,e){return A(t=this._relativeTime[0<t?"future":"past"])?t(e):t.replace(/%s/i,e)},st.set=function(t){var e,n;for(n in t)s(t,n)&&(A(e=t[n])?this[n]=e:this["_"+n]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},st.eras=function(t,n){for(var a,s=this._eras||de("en")._eras,i=0,r=s.length;i<r;++i){if("string"==typeof s[i].since)a=e(s[i].since).startOf("day"),s[i].since=a.valueOf();switch(typeof s[i].until){case"undefined":s[i].until=1/0;break;case"string":a=e(s[i].until).startOf("day").valueOf(),s[i].until=a.valueOf()}}return s},st.erasParse=function(t,e,n){var a,s,i,r,o,l=this.eras();for(t=t.toUpperCase(),a=0,s=l.length;a<s;++a)if(i=l[a].name.toUpperCase(),r=l[a].abbr.toUpperCase(),o=l[a].narrow.toUpperCase(),n)switch(e){case"N":case"NN":case"NNN":if(r===t)return l[a];break;case"NNNN":if(i===t)return l[a];break;case"NNNNN":if(o===t)return l[a]}else if(0<=[i,r,o].indexOf(t))return l[a]},st.erasConvertYear=function(t,n){var a=t.since<=t.until?1:-1;return void 0===n?e(t.since).year():e(t.since).year()+(n-t.offset)*a},st.erasAbbrRegex=function(t){return s(this,"_erasAbbrRegex")||en.call(this),t?this._erasAbbrRegex:this._erasRegex},st.erasNameRegex=function(t){return s(this,"_erasNameRegex")||en.call(this),t?this._erasNameRegex:this._erasRegex},st.erasNarrowRegex=function(t){return s(this,"_erasNarrowRegex")||en.call(this),t?this._erasNarrowRegex:this._erasRegex},st.months=function(t,e){return t?(n(this._months)?this._months:this._months[(this._months.isFormat||Rt).test(e)?"format":"standalone"])[t.month()]:n(this._months)?this._months:this._months.standalone},st.monthsShort=function(t,e){return t?(n(this._monthsShort)?this._monthsShort:this._monthsShort[Rt.test(e)?"format":"standalone"])[t.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},st.monthsParse=function(t,e,n){var a,s;if(this._monthsParseExact)return function(t,e,n){var a,s,i;t=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)i=c([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(i,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(i,"").toLocaleLowerCase();return n?"MMM"===e?-1!==(s=At.call(this._shortMonthsParse,t))?s:null:-1!==(s=At.call(this._longMonthsParse,t))?s:null:"MMM"===e?-1!==(s=At.call(this._shortMonthsParse,t))||-1!==(s=At.call(this._longMonthsParse,t))?s:null:-1!==(s=At.call(this._longMonthsParse,t))||-1!==(s=At.call(this._shortMonthsParse,t))?s:null}.call(this,t,e,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(s=c([2e3,a]),n&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),n||this._monthsParse[a]||(s="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[a]=new RegExp(s.replace(".",""),"i")),n&&"MMMM"===e&&this._longMonthsParse[a].test(t))return a;if(n&&"MMM"===e&&this._shortMonthsParse[a].test(t))return a;if(!n&&this._monthsParse[a].test(t))return a}},st.monthsRegex=function(t){return this._monthsParseExact?(s(this,"_monthsRegex")||Ut.call(this),t?this._monthsStrictRegex:this._monthsRegex):(s(this,"_monthsRegex")||(this._monthsRegex=$t),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},st.monthsShortRegex=function(t){return this._monthsParseExact?(s(this,"_monthsRegex")||Ut.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(s(this,"_monthsShortRegex")||(this._monthsShortRegex=Lt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},st.week=function(t){return Vt(t,this._week.dow,this._week.doy).week},st.firstDayOfYear=function(){return this._week.doy},st.firstDayOfWeek=function(){return this._week.dow},st.weekdays=function(t,e){return e=n(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"],!0===t?Zt(e,this._week.dow):t?e[t.day()]:e},st.weekdaysMin=function(t){return!0===t?Zt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},st.weekdaysShort=function(t){return!0===t?Zt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},st.weekdaysParse=function(t,e,n){var a,s;if(this._weekdaysParseExact)return function(t,e,n){var a,s,i;t=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)i=c([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(i,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(i,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(i,"").toLocaleLowerCase();return n?"dddd"===e?-1!==(s=At.call(this._weekdaysParse,t))?s:null:"ddd"===e?-1!==(s=At.call(this._shortWeekdaysParse,t))?s:null:-1!==(s=At.call(this._minWeekdaysParse,t))?s:null:"dddd"===e?-1!==(s=At.call(this._weekdaysParse,t))||-1!==(s=At.call(this._shortWeekdaysParse,t))||-1!==(s=At.call(this._minWeekdaysParse,t))?s:null:"ddd"===e?-1!==(s=At.call(this._shortWeekdaysParse,t))||-1!==(s=At.call(this._weekdaysParse,t))||-1!==(s=At.call(this._minWeekdaysParse,t))?s:null:-1!==(s=At.call(this._minWeekdaysParse,t))||-1!==(s=At.call(this._weekdaysParse,t))||-1!==(s=At.call(this._shortWeekdaysParse,t))?s:null}.call(this,t,e,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(s=c([2e3,1]).day(a),n&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(s,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(s,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(s,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(s="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[a]=new RegExp(s.replace(".",""),"i")),n&&"dddd"===e&&this._fullWeekdaysParse[a].test(t))return a;if(n&&"ddd"===e&&this._shortWeekdaysParse[a].test(t))return a;if(n&&"dd"===e&&this._minWeekdaysParse[a].test(t))return a;if(!n&&this._weekdaysParse[a].test(t))return a}},st.weekdaysRegex=function(t){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Kt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(s(this,"_weekdaysRegex")||(this._weekdaysRegex=qt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},st.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Kt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(s(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Jt),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},st.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Kt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(s(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Qt),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},st.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},st.meridiem=function(t,e,n){return 11<t?n?"pm":"PM":n?"am":"AM"},le("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===ut(t%100/10)?"th":1==e?"st":2==e?"nd":3==e?"rd":"th")}}),e.lang=w("moment.lang is deprecated. Use moment.locale instead.",le),e.langData=w("moment.langData is deprecated. Use moment.localeData instead.",de);var cn=Math.abs;function hn(t,e,n,a){return e=Fe(e,n),t._milliseconds+=a*e._milliseconds,t._days+=a*e._days,t._months+=a*e._months,t._bubble()}function mn(t){return t<0?Math.floor(t):Math.ceil(t)}function fn(t){return 4800*t/146097}function pn(t){return 146097*t/4800}function vn(t){return function(){return this.as(t)}}function gn(t){return function(){return this.isValid()?this._data[t]:NaN}}F=vn("ms"),W=vn("s"),q=vn("m"),V=vn("h"),It=vn("d"),B=vn("w"),z=vn("M"),Gt=vn("Q"),nt=vn("y"),H=F;Q=gn("milliseconds"),Z=gn("seconds"),J=gn("minutes"),Ct=gn("hours"),st=gn("days");var yn=gn("months"),bn=gn("years"),_n=Math.round,Sn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var wn=Math.abs;function kn(t){return(0<t)-(t<0)||+t}function Mn(){var t,e,n,a,s,i,r,o,l,u,d;return this.isValid()?(t=wn(this._milliseconds)/1e3,e=wn(this._days),n=wn(this._months),(o=this.asSeconds())?(a=lt(t/60),s=lt(a/60),t%=60,a%=60,i=lt(n/12),n%=12,r=t?t.toFixed(3).replace(/\.?0+$/,""):"",l=kn(this._months)!==kn(o)?"-":"",u=kn(this._days)!==kn(o)?"-":"",d=kn(this._milliseconds)!==kn(o)?"-":"",(o<0?"-":"")+"P"+(i?l+i+"Y":"")+(n?l+n+"M":"")+(e?u+e+"D":"")+(s||a||t?"T":"")+(s?d+s+"H":"")+(a?d+a+"M":"")+(t?d+r+"S":"")):"P0D"):this.localeData().invalidDate()}var An=Ye.prototype;return An.isValid=function(){return this._isValid},An.abs=function(){var t=this._data;return this._milliseconds=cn(this._milliseconds),this._days=cn(this._days),this._months=cn(this._months),t.milliseconds=cn(t.milliseconds),t.seconds=cn(t.seconds),t.minutes=cn(t.minutes),t.hours=cn(t.hours),t.months=cn(t.months),t.years=cn(t.years),this},An.add=function(t,e){return hn(this,t,e,1)},An.subtract=function(t,e){return hn(this,t,e,-1)},An.as=function(t){if(!this.isValid())return NaN;var e,n,a=this._milliseconds;if("month"===(t=U(t))||"quarter"===t||"year"===t)switch(e=this._days+a/864e5,n=this._months+fn(e),t){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(e=this._days+Math.round(pn(this._months)),t){case"week":return e/7+a/6048e5;case"day":return e+a/864e5;case"hour":return 24*e+a/36e5;case"minute":return 1440*e+a/6e4;case"second":return 86400*e+a/1e3;case"millisecond":return Math.floor(864e5*e)+a;default:throw new Error("Unknown unit "+t)}},An.asMilliseconds=F,An.asSeconds=W,An.asMinutes=q,An.asHours=V,An.asDays=It,An.asWeeks=B,An.asMonths=z,An.asQuarters=Gt,An.asYears=nt,An.valueOf=H,An._bubble=function(){var t=this._milliseconds,e=this._days,n=this._months,a=this._data;return 0<=t&&0<=e&&0<=n||t<=0&&e<=0&&n<=0||(t+=864e5*mn(pn(n)+e),n=e=0),a.milliseconds=t%1e3,t=lt(t/1e3),a.seconds=t%60,t=lt(t/60),a.minutes=t%60,t=lt(t/60),a.hours=t%24,e+=lt(t/24),n+=t=lt(fn(e)),e-=mn(pn(t)),t=lt(n/12),n%=12,a.days=e,a.months=n,a.years=t,this},An.clone=function(){return Fe(this)},An.get=function(t){return t=U(t),this.isValid()?this[t+"s"]():NaN},An.milliseconds=Q,An.seconds=Z,An.minutes=J,An.hours=Ct,An.days=st,An.weeks=function(){return lt(this.days()/7)},An.months=yn,An.years=bn,An.humanize=function(t,e){var n,a;return this.isValid()?(n=!1,a=Sn,"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(n=t),"object"==typeof e&&(a=Object.assign({},Sn,e),null!=e.s)&&null==e.ss&&(a.ss=e.s-1),e=function(t,e,n,a){var s=Fe(t).abs(),i=_n(s.as("s")),r=_n(s.as("m")),o=_n(s.as("h")),l=_n(s.as("d")),u=_n(s.as("M")),d=_n(s.as("w"));return s=_n(s.as("y")),i=(i<=n.ss?["s",i]:i<n.s&&["ss",i])||(r<=1?["m"]:r<n.m&&["mm",r])||(o<=1?["h"]:o<n.h&&["hh",o])||(l<=1?["d"]:l<n.d&&["dd",l]),(i=(i=null!=n.w?i||(d<=1?["w"]:d<n.w&&["ww",d]):i)||(u<=1?["M"]:u<n.M&&["MM",u])||(s<=1?["y"]:["yy",s]))[2]=e,i[3]=0<+t,i[4]=a,function(t,e,n,a,s){return s.relativeTime(e||1,!!n,t,a)}.apply(null,i)}(this,!n,a,t=this.localeData()),n&&(e=t.pastFuture(+this,e)),t.postformat(e)):this.localeData().invalidDate()},An.toISOString=Mn,An.toString=Mn,An.toJSON=Mn,An.locale=Be,An.localeData=qe,An.toIsoString=w("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Mn),An.lang=Bt,L("X",0,0,"unix"),L("x",0,0,"valueOf"),it("x",X),it("X",/[+-]?\d+(\.\d{1,3})?/),ht("X",(function(t,e,n){n._d=new Date(1e3*parseFloat(t))})),ht("x",(function(t,e,n){n._d=new Date(ut(t))})),e.version="2.30.1",t=Oe,e.fn=at,e.min=function(){return De("isBefore",[].slice.call(arguments,0))},e.max=function(){return De("isAfter",[].slice.call(arguments,0))},e.now=function(){return Date.now?Date.now():+new Date},e.utc=c,e.unix=function(t){return Oe(1e3*t)},e.months=function(t,e){return un(t,e,"months")},e.isDate=l,e.locale=le,e.invalid=f,e.duration=Fe,e.isMoment=_,e.weekdays=function(t,e,n){return dn(t,e,n,"weekdays")},e.parseZone=function(){return Oe.apply(null,arguments).parseZone()},e.localeData=de,e.isDuration=Ie,e.monthsShort=function(t,e){return un(t,e,"monthsShort")},e.weekdaysMin=function(t,e,n){return dn(t,e,n,"weekdaysMin")},e.defineLocale=ue,e.updateLocale=function(t,e){var n,a;return null!=e?(a=ae,null!=se[t]&&null!=se[t].parentLocale?se[t].set(C(se[t]._config,e)):(e=C(a=null!=(n=oe(t))?n._config:a,e),null==n&&(e.abbr=t),(a=new O(e)).parentLocale=se[t],se[t]=a),le(t)):null!=se[t]&&(null!=se[t].parentLocale?(se[t]=se[t].parentLocale,t===le()&&le(t)):null!=se[t]&&delete se[t]),se[t]},e.locales=function(){return D(se)},e.weekdaysShort=function(t,e,n){return dn(t,e,n,"weekdaysShort")},e.normalizeUnits=U,e.relativeTimeRounding=function(t){return void 0===t?_n:"function"==typeof t&&(_n=t,!0)},e.relativeTimeThreshold=function(t,e){return void 0!==Sn[t]&&(void 0===e?Sn[t]:(Sn[t]=e,"s"===t&&(Sn.ss=e-1),!0))},e.calendarFormat=function(t,e){return(t=t.diff(e,"days",!0))<-6?"sameElse":t<-1?"lastWeek":t<0?"lastDay":t<1?"sameDay":t<2?"nextDay":t<7?"nextWeek":"sameElse"},e.prototype=at,e.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},e}));var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}},$jscomp.arrayIterator=function(t){return{next:$jscomp.arrayIteratorImpl(t)}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.ISOLATE_POLYFILLS=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t},$jscomp.getGlobal=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")},$jscomp.global=$jscomp.getGlobal(this),$jscomp.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE,$jscomp.polyfills={},$jscomp.propertyToPolyfillSymbol={},$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(t,e){var n=$jscomp.propertyToPolyfillSymbol[e];return null==n?t[e]:void 0!==(n=t[n])?n:t[e]};$jscomp.polyfill=function(t,e,n,a){e&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(t,e,n,a):$jscomp.polyfillUnisolated(t,e,n,a))},$jscomp.polyfillUnisolated=function(t,e,n,a){for(n=$jscomp.global,t=t.split("."),a=0;a<t.length-1;a++){var s=t[a];if(!(s in n))return;n=n[s]}(e=e(a=n[t=t[t.length-1]]))!=a&&null!=e&&$jscomp.defineProperty(n,t,{configurable:!0,writable:!0,value:e})},$jscomp.polyfillIsolated=function(t,e,n,a){var s=t.split(".");t=1===s.length,a=s[0],a=!t&&a in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var i=0;i<s.length-1;i++){var r=s[i];if(!(r in a))return;a=a[r]}s=s[s.length-1],null!=(e=e(n=$jscomp.IS_SYMBOL_NATIVE&&"es6"===n?a[s]:null))&&(t?$jscomp.defineProperty($jscomp.polyfills,s,{configurable:!0,writable:!0,value:e}):e!==n&&($jscomp.propertyToPolyfillSymbol[s]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(s):$jscomp.POLYFILL_PREFIX+s,s=$jscomp.propertyToPolyfillSymbol[s],$jscomp.defineProperty(a,s,{configurable:!0,writable:!0,value:e})))},$jscomp.initSymbol=function(){},$jscomp.polyfill("Symbol",(function(t){if(t)return t;var e=function(t,e){this.$jscomp$symbol$id_=t,$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:e})};e.prototype.toString=function(){return this.$jscomp$symbol$id_};var n=0,a=function(t){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return new e("jscomp_symbol_"+(t||"")+"_"+n++,t)};return a}),"es6","es3"),$jscomp.initSymbolIterator=function(){},$jscomp.polyfill("Symbol.iterator",(function(t){if(t)return t;t=Symbol("Symbol.iterator");for(var e="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),n=0;n<e.length;n++){var a=$jscomp.global[e[n]];"function"==typeof a&&"function"!=typeof a.prototype[t]&&$jscomp.defineProperty(a.prototype,t,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return t}),"es6","es3"),$jscomp.initSymbolAsyncIterator=function(){},$jscomp.iteratorPrototype=function(t){return(t={next:t})[Symbol.iterator]=function(){return this},t},function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],e):e((t=t||self).VeeValidate={},t.Vue)}(this,(function(t,e){function n(t,e,n,a){return new(n||(n=Promise))((function(s,i){function r(t){try{l(a.next(t))}catch(t){i(t)}}function o(t){try{l(a.throw(t))}catch(t){i(t)}}function l(t){t.done?s(t.value):new n((function(e){e(t.value)})).then(r,o)}l((a=a.apply(t,e||[])).next())}))}function a(t,e){function n(n){return function(r){return function(n){if(a)throw new TypeError("Generator is already executing.");for(;o;)try{if(a=1,s&&(i=2&n[0]?s.return:n[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,n[1])).done)return i;switch(s=0,i&&(n=[2&n[0],i.value]),n[0]){case 0:case 1:i=n;break;case 4:return o.label++,{value:n[1],done:!1};case 5:o.label++,s=n[1],n=[0];continue;case 7:n=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=0<i.length&&i[i.length-1])||6!==n[0]&&2!==n[0])){o=0;continue}if(3===n[0]&&(!i||n[1]>i[0]&&n[1]<i[3]))o.label=n[1];else if(6===n[0]&&o.label<i[1])o.label=i[1],i=n;else{if(!(i&&o.label<i[2])){i[2]&&o.ops.pop(),o.trys.pop();continue}o.label=i[2],o.ops.push(n)}}n=e.call(t,o)}catch(t){n=[6,t],s=0}finally{a=i=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,r])}}var a,s,i,r,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r}function s(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;t=Array(t);var a=0;for(e=0;e<n;e++)for(var s=arguments[e],i=0,r=s.length;i<r;i++,a++)t[a]=s[i];return t}function i(t){return null==t}function r(t,e){if(t instanceof RegExp&&e instanceof RegExp)return r(t.source,e.source)&&r(t.flags,e.flags);if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;for(var n=0;n<t.length;n++)if(!r(t[n],e[n]))return!1;return!0}return B(t)&&B(e)?Object.keys(t).every((function(n){return r(t[n],e[n])}))&&Object.keys(e).every((function(n){return r(t[n],e[n])})):t!=t&&e!=e||t===e}function o(t){return""!==t&&!i(t)}function l(t){return"function"==typeof t}function u(t){return l(t)&&!!t.__locatorRef}function d(t,e){var n=Array.isArray(t)?t:h(t);if(l(n.findIndex))return n.findIndex(e);for(var a=0;a<n.length;a++)if(e(n[a],a))return a;return-1}function c(t,e){return-1!==t.indexOf(e)}function h(t){if(l(Array.from))return Array.from(t);for(var e=[],n=t.length,a=0;a<n;a++)e.push(t[a]);return e}function m(t){return l(Object.values)?Object.values(t):Object.keys(t).map((function(e){return t[e]}))}function f(t,e){return Object.keys(e).forEach((function(n){B(e[n])?(t[n]||(t[n]={}),f(t[n],e[n])):t[n]=e[n]})),t}function p(t){return t}function v(t,e,n){return void 0===e&&(e=0),void 0===n&&(n={cancelled:!1}),0===e?t:function(){for(var s=[],i=0;i<arguments.length;i++)s[i]=arguments[i];clearTimeout(a),a=setTimeout((function(){a=void 0,n.cancelled||t.apply(void 0,s)}),e)};var a}function g(t,e){return t.replace(/{([^}]+)}/g,(function(t,n){return n in e?e[n]:"{"+n+"}"}))}function y(t){var e={};return Object.defineProperty(e,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),t?B(t)&&t._$$isNormalized?t:B(t)?Object.keys(t).reduce((function(e,n){var a=!0===t[n]?[]:Array.isArray(t[n])||B(t[n])?t[n]:[t[n]];return!1!==t[n]&&(e[n]=b(n,a)),e}),e):"string"!=typeof t?(console.warn("[vee-validate] rules must be either a string or an object."),e):t.split("|").reduce((function(t,e){var n=[],a=e.split(":")[0];return c(e,":")&&(n=e.split(":").slice(1).join(":").split(",")),a?(t[a]=b(a,n),t):t}),e):e}function b(t,e){var n=J.getRuleDefinition(t);if(!n)return e;var a={};if(!n.params&&!Array.isArray(e))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(e)&&!n.params)return e;if(!n.params||n.params.length<e.length&&Array.isArray(e))var s,i=e.map((function(t,e){var a,i=null===(a=n.params)||void 0===a?void 0:a[e];return s=i||s,i||(i=s),i}));else i=n.params;for(var r=0;r<i.length;r++){var o=i[r],l=o.default;Array.isArray(e)?r in e&&(l=e[r]):o.name in e?l=e[o.name]:1===i.length&&(l=e),o.isTarget&&(l=_(l,o.cast)),"string"==typeof l&&"@"===l[0]&&(l=_(l.slice(1),o.cast)),!u(l)&&o.cast&&(l=o.cast(l)),a[o.name]?(a[o.name]=Array.isArray(a[o.name])?a[o.name]:[a[o.name]],a[o.name].push(l)):a[o.name]=l}return a}function _(t,e){var n=function(n){return n=n[t],e?e(n):n};return n.__locatorRef=t,n}function S(t,e,s){var i,r,o,l,u,d;return void 0===s&&(s={}),n(this,void 0,void 0,(function(){var n,c,h,m,f,p;return a(this,(function(a){switch(a.label){case 0:return n=null===(i=s)||void 0===i?void 0:i.bails,c=null===(r=s)||void 0===r?void 0:r.skipIfEmpty,[4,w({name:(null===(o=s)||void 0===o?void 0:o.name)||"{field}",rules:y(e),bails:null==n||n,skipIfEmpty:null==c||c,forceRequired:!1,crossTable:(null===(l=s)||void 0===l?void 0:l.values)||{},names:(null===(u=s)||void 0===u?void 0:u.names)||{},customMessages:(null===(d=s)||void 0===d?void 0:d.customMessages)||{}},t,s)];case 1:return h=a.sent(),m=[],f={},p={},h.errors.forEach((function(t){var e=t.msg();m.push(e),f[t.rule]=e,p[t.rule]=t.msg})),[2,{valid:h.valid,errors:m,failedRules:f,regenerateMap:p}]}}))}))}function w(t,e,s){var i=void 0!==(s=(void 0===s?{}:s).isInitial)&&s;return n(this,void 0,void 0,(function(){var n,s,r,o,l,u,d,c;return a(this,(function(a){switch(a.label){case 0:return[4,k(t,e)];case 1:if(n=a.sent(),s=n.shouldSkip,r=n.errors,s)return[2,{valid:!r.length,errors:r}];o=Object.keys(t.rules).filter((function(t){return!J.isRequireRule(t)})),l=o.length,u=0,a.label=2;case 2:return u<l?i&&J.isLazy(o[u])?[3,4]:(d=o[u],[4,M(t,e,{name:d,params:t.rules[d]})]):[3,5];case 3:if(!(c=a.sent()).valid&&c.error&&(r.push(c.error),t.bails))return[2,{valid:!1,errors:r}];a.label=4;case 4:return u++,[3,2];case 5:return[2,{valid:!r.length,errors:r}]}}))}))}function k(t,e){return n(this,void 0,void 0,(function(){var n,s,r,o,l,u,d,c,h;return a(this,(function(a){switch(a.label){case 0:var m;n=Object.keys(t.rules).filter(J.isRequireRule),s=n.length,r=[],(m=i(e)||""===e)||(m=Array.isArray(e)&&0===e.length),l=(o=m)&&t.skipIfEmpty,u=!1,d=0,a.label=1;case 1:return d<s?(c=n[d],[4,M(t,e,{name:c,params:t.rules[c]})]):[3,4];case 2:if(h=a.sent(),!B(h))throw Error("Require rules has to return an object (see docs)");if(h.required&&(u=!0),!h.valid&&h.error&&(r.push(h.error),t.bails))return[2,{shouldSkip:!0,errors:r}];a.label=3;case 3:return d++,[3,1];case 4:return o&&!u&&!t.skipIfEmpty||!t.bails&&!l?[2,{shouldSkip:!1,errors:r}]:[2,{shouldSkip:!u&&o,errors:r}]}}))}))}function M(t,e,s){return n(this,void 0,void 0,(function(){var n,i,r,o,l;return a(this,(function(a){switch(a.label){case 0:if(!(n=J.getRuleDefinition(s.name))||!n.validate)throw Error("No such validator '"+s.name+"' exists.");return i=n.castValue?n.castValue(e):e,r=function(t,e){if(Array.isArray(t))return t;var n={};return Object.keys(t).forEach((function(a){var s=t[a];s=u(s)?s(e):s,n[a]=s})),n}(s.params,t.crossTable),[4,n.validate(i,r)];case 1:return"string"==typeof(o=a.sent())?(l=z(z({},r||{}),{_field_:t.name,_value_:e,_rule_:s.name}),[2,{valid:!1,error:{rule:s.name,msg:function(){return g(o,l)}}}]):(B(o)||(o={valid:o}),[2,{valid:o.valid,required:o.required,error:o.valid?void 0:A(t,e,n,s.name,r)}])}}))}))}function A(t,e,n,a,s){var i,r=null!=(i=t.customMessages[a])?i:n.message;i=function(t,e,n){if(e=e.params,!e||0>=e.filter((function(t){return t.isTarget})).length)return{};var a={},s=t.rules[n];for(!Array.isArray(s)&&B(s)&&(s=e.map((function(t){return s[t.name]}))),n=0;n<e.length;n++){var i=e[n],r=s[n];u(r)&&(r=r.__locatorRef,a[i.name]=t.names[r]||r,a["_"+i.name+"_"]=t.crossTable[r])}return a}(t,n,a),n=function(t,e,n,a){var s={},i=t.rules[n],r=e.params||[];return i?(Object.keys(i).forEach((function(e,n){var a=i[e];if(!u(a))return{};var o=r[n];if(!o)return{};a=a.__locatorRef,s[o.name]=t.names[a]||a,s["_"+o.name+"_"]=t.crossTable[a]})),{userTargets:s,userMessage:a}):{}}(t,n,a,r),r=n.userTargets;var o=n.userMessage,l=z(z(z(z({},s||{}),{_field_:t.name,_value_:e,_rule_:a}),i),r);return{msg:function(){var e=o||Q.defaultMessage,n=t.name;return e="function"==typeof e?e(n,l):g(e,z(z({},l),{_field_:n}))},rule:a}}function C(){tt.$emit("change:locale")}function O(t){if(t.data){var e=t.data;if("model"in e)return e.model;if(t.data.directives)return function(t,e){var n=Array.isArray(t)?t:h(t),a=d(n,e);return-1===a?void 0:n[a]}(t.data.directives,(function(t){return"model"===t.name}))}}function D(t){var e,n,a,s=O(t);return s?{value:s.value}:(s=(null===(e=Y(t))||void 0===e?void 0:e.prop)||"value",null!==(n=t.componentOptions)&&void 0!==n&&n.propsData&&s in t.componentOptions.propsData?{value:t.componentOptions.propsData[s]}:null!==(a=t.data)&&void 0!==a&&a.domProps&&"value"in t.data.domProps?{value:t.data.domProps.value}:void 0)}function T(t){return Array.isArray(t)||void 0===D(t)?function(t){return Array.isArray(t)?t:Array.isArray(t.children)?t.children:t.componentOptions&&Array.isArray(t.componentOptions.children)?t.componentOptions.children:[]}(t).reduce((function(t,e){var n=T(e);return n.length&&t.push.apply(t,n),t}),[]):[t]}function Y(t){return t.componentOptions?t.componentOptions.Ctor.options.model:null}function I(t,e,n){i(t[e])?t[e]=[n]:l(t[e])&&t[e].fns?((t=t[e]).fns=Array.isArray(t.fns)?t.fns:[t.fns],c(t.fns,n)||t.fns.push(n)):(l(t[e])&&(t[e]=[t[e]]),Array.isArray(t[e])&&!c(t[e],n)&&t[e].push(n))}function x(t,e,n){t.componentOptions?t.componentOptions&&(t.componentOptions.listeners||(t.componentOptions.listeners={}),I(t.componentOptions.listeners,e,n)):(t.data||(t.data={}),i(t.data.on)&&(t.data.on={}),I(t.data.on,e,n))}function R(t,e){var n;return t.componentOptions?(Y(t)||{event:"input"}).event:null!==(n=null==e?void 0:e.modifiers)&&void 0!==n&&n.lazy?"change":nt(t)?"input":"change"}function L(t,e){return t.$scopedSlots.default?t.$scopedSlots.default(e)||[]:t.$slots.default||[]}function $(t){return z(z({},t.flags),{errors:t.errors,classes:t.classes,failedRules:t.failedRules,reset:function(){return t.reset()},validate:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t.validate.apply(t,e)},ariaInput:{"aria-invalid":t.flags.invalid?"true":"false","aria-required":t.isRequired?"true":"false","aria-errormessage":"vee_"+t.id},ariaMsg:{id:"vee_"+t.id,"aria-live":t.errors.length?"assertive":"off"}})}function N(t,e){t.initialized||(t.initialValue=e);var n=!!(!t._ignoreImmediate&&t.immediate||t.value!==e&&t.normalizedEvents.length||t._needsValidation||!t.initialized&&void 0===e);if(t._needsValidation=!1,t.value=e,t._ignoreImmediate=!0,n){var a=function(){if(t.immediate||t.flags.validated)return U(t);t.validateSilent()};t.initialized?a():t.$once("hook:mounted",(function(){return a()}))}}function P(t){return(l(t.mode)?t.mode:X[t.mode])(t)}function U(t){var e=t.validateSilent();return t._pendingValidation=e,e.then((function(n){return e===t._pendingValidation&&(t.applyResult(n),t._pendingValidation=void 0),n}))}function E(t){t.$veeOnInput||(t.$veeOnInput=function(e){t.syncValue(e),t.setFlags({dirty:!0,pristine:!1})});var e=t.$veeOnInput;t.$veeOnBlur||(t.$veeOnBlur=function(){t.setFlags({touched:!0,untouched:!1})});var n=t.$veeOnBlur,a=t.$veeHandler,s=P(t);return a&&t.$veeDebounce===t.debounce||(a=v((function(){t.$nextTick((function(){t._pendingReset||U(t),t._pendingReset=!1}))}),s.debounce||t.debounce),t.$veeHandler=a,t.$veeDebounce=t.debounce),{onInput:e,onBlur:n,onValidate:a}}function j(t){var e=t.$_veeObserver.refs;return t.fieldDeps.reduce((function(t,n){return e[n]?(t.values[n]=e[n].value,t.names[n]=e[n].name,t):t}),{names:{},values:{}})}function F(t,e,n){void 0===n&&(n=!0);var a=t.$_veeObserver.refs;if(t._veeWatchers||(t._veeWatchers={}),!a[e]&&n)return t.$once("hook:mounted",(function(){F(t,e,!1)}));!l(t._veeWatchers[e])&&a[e]&&(t._veeWatchers[e]=a[e].$watch("value",(function(){t.flags.validated&&(t._needsValidation=!0,t.validate())})))}function W(t){t.$_veeObserver&&t.$_veeObserver.unobserve(t.id,"observer")}function V(t){t.$_veeObserver&&t.$_veeObserver.observe(t,"observer")}function H(){return z(z({},{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1}),{valid:!0,invalid:!1})}function Z(){for(var t=s(m(this.refs),this.observers),e={},n=H(),a={},i=t.length,r=0;r<i;r++){var o=t[r];Array.isArray(o.errors)?(e[o.id]=o.errors,a[o.id]=z({id:o.id,name:o.name,failedRules:o.failedRules},o.flags)):(e=z(z({},e),o.errors),a=z(z({},a),o.fields))}return it.forEach((function(e){var a=e[0];n[a]=t[e[1]]((function(t){return t.flags[a]}))})),{errors:e,flags:n,fields:a}}e=e&&e.hasOwnProperty("default")?e.default:e;var G,z=function(){return z=Object.assign||function(t){for(var e,n=1,a=arguments.length;n<a;n++)for(var s in e=arguments[n])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t},z.apply(this,arguments)},B=function(t){return null!==t&&t&&"object"==typeof t&&!Array.isArray(t)},q={},J=function(){function t(){}return t.extend=function(t,e){var n=function(t){var e;return null!==(e=t.params)&&void 0!==e&&e.length&&(t.params=t.params.map((function(t){return"string"==typeof t?{name:t}:t}))),t}(e);q[t]=q[t]?f(q[t],e):z({lazy:!1,computesRequired:!1},n)},t.isLazy=function(t){var e;return!(null===(e=q[t])||void 0===e||!e.lazy)},t.isRequireRule=function(t){var e;return!(null===(e=q[t])||void 0===e||!e.computesRequired)},t.getRuleDefinition=function(t){return q[t]},t}(),Q=z({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),K=function(t){Q=z(z({},Q),t)},X={aggressive:function(){return{on:["input","blur"]}},eager:function(t){return t.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},tt=new e,et=function(){function t(t,e){this.container={},this.locale=t,this.merge(e)}return t.prototype.resolve=function(t,e,n){return this.format(this.locale,t,e,n)},t.prototype.format=function(t,e,n,a){var s,i,r,o,u,d,c,h;return(n=(null===(r=null===(i=null===(s=this.container[t])||void 0===s?void 0:s.fields)||void 0===i?void 0:i[e])||void 0===r?void 0:r[n])||(null===(u=null===(o=this.container[t])||void 0===o?void 0:o.messages)||void 0===u?void 0:u[n]))||(n="{field} is not valid"),e=null!=(h=null===(c=null===(d=this.container[t])||void 0===d?void 0:d.names)||void 0===c?void 0:c[e])?h:e,l(n)?n(e,a):g(n,z(z({},a),{_field_:e}))},t.prototype.merge=function(t){f(this.container,t)},t.prototype.hasRule=function(t){var e,n;return!(null===(n=null===(e=this.container[this.locale])||void 0===e?void 0:e.messages)||void 0===n||!n[t])},t}(),nt=function(t){var e,n=(null===(e=t.data)||void 0===e?void 0:e.attrs)||t.elm;return!("input"!==t.tag||n&&n.type)||"textarea"===t.tag||c("text password search email tel url number".split(" "),null==n?void 0:n.type)},at=0,st=e.extend({inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver={refs:{},observe:function(t){this.refs[t.id]=t},unobserve:function(t){delete this.refs[t]}}),this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,default:""},name:{type:String,default:null},mode:{type:[String,Function],default:function(){return Q.mode}},rules:{type:[Object,String],default:null},immediate:{type:Boolean,default:!1},bails:{type:Boolean,default:function(){return Q.bails}},skipIfEmpty:{type:Boolean,default:function(){return Q.skipOptional}},debounce:{type:Number,default:0},tag:{type:String,default:"span"},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},customMessages:{type:Object,default:function(){return{}}}},watch:{rules:{deep:!0,handler:function(t,e){this._needsValidation=!r(t,e)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1},failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var t=this;return Object.keys(this.normalizedRules).reduce((function(e,n){var a=function(t){return Array.isArray(t)?t.filter(u):Object.keys(t).filter((function(e){return u(t[e])})).map((function(e){return t[e]}))}(t.normalizedRules[n]).map((function(t){return t.__locatorRef}));return e.push.apply(e,a),a.forEach((function(e){F(t,e)})),e}),[])},normalizedEvents:function(){var t=this;return(P(this).on||[]).map((function(e){return"input"===e?t._inputEventName:e}))},isRequired:function(){var t=z(z({},this._resolvedRules),this.normalizedRules);return t=Object.keys(t).some(J.isRequireRule),this.flags.required=!!t,t},classes:function(){return function(t,e){for(var n={},a=Object.keys(e),s=a.length,r=function(s){s=a[s];var r=t&&t[s]||s,o=e[s];if(i(o)||("valid"===s||"invalid"===s)&&!e.validated)return"continue";"string"==typeof r?n[r]=o:Array.isArray(r)&&r.forEach((function(t){n[t]=o}))},o=0;o<s;o++)r(o);return n}(Q.classes,this.flags)},normalizedRules:function(){return y(this.rules)}},created:function(){var t=this,e=function(){if(t.flags.validated){var e=t._regenerateMap;if(e){var n=[],a={};Object.keys(e).forEach((function(t){var s=e[t]();n.push(s),a[t]=s})),t.applyResult({errors:n,failedRules:a,regenerateMap:e})}else t.validate()}};tt.$on("change:locale",e),this.$on("hook:beforeDestroy",(function(){tt.$off("change:locale",e)}))},render:function(t){var e=this;this.registerField();var n=$(this);return T(n=L(this,n)).forEach((function(t){var n,a,s,i,l;if(Q.useConstraintAttrs){var u,d=null===(u=t.data)||void 0===u?void 0:u.attrs;if(c(["input","select","textarea"],t.tag)&&d)if(u={},"required"in d&&!1!==d.required&&J.getRuleDefinition("required")&&(u.required="checkbox"!==d.type||[!0]),nt(t)){d=z,u=z({},u);var h=null===(l=t.data)||void 0===l?void 0:l.attrs;l={},h&&("email"===h.type&&J.getRuleDefinition("email")&&(l.email=["multiple"in h]),h.pattern&&J.getRuleDefinition("regex")&&(l.regex=h.pattern),0<=h.maxlength&&J.getRuleDefinition("max")&&(l.max=h.maxlength),0<=h.minlength&&J.getRuleDefinition("min")&&(l.min=h.minlength),"number"===h.type&&(o(h.min)&&J.getRuleDefinition("min_value")&&(l.min_value=Number(h.min)),o(h.max)&&J.getRuleDefinition("max_value")&&(l.max_value=Number(h.max)))),l=y(d(u,l))}else l=y(u);else l={}}else l={};r(e._resolvedRules,l)||(e._needsValidation=!0),c(["input","select","textarea"],t.tag)&&(e.fieldName=(null===(a=null===(n=t.data)||void 0===n?void 0:n.attrs)||void 0===a?void 0:a.name)||(null===(i=null===(s=t.data)||void 0===s?void 0:s.attrs)||void 0===i?void 0:i.id)),e._resolvedRules=l,function(t,e){var n=D(e);t._inputEventName=t._inputEventName||R(e,O(e)),N(t,null==n?void 0:n.value);var a=(n=E(t)).onBlur,s=n.onValidate;x(e,t._inputEventName,n.onInput),x(e,"blur",a),t.normalizedEvents.forEach((function(t){x(e,t,s)})),t.initialized=!0}(e,t)})),this.slim&&1>=n.length?n[0]:t(this.tag,n)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(t){var e=this;Object.keys(t).forEach((function(n){e.flags[n]=t[n]}))},syncValue:function(t){this.value=t=function(t){var e,n;return t&&("undefined"!=typeof Event&&l(Event)&&t instanceof Event||t&&t.srcElement)?"file"===(t=t.target).type&&t.files?h(t.files):null!==(e=t._vModifiers)&&void 0!==e&&e.number?(e=parseFloat(t.value))!=e?t.value:e:null!==(n=t._vModifiers)&&void 0!==n&&n.trim&&"string"==typeof t.value?t.value.trim():t.value:t}(t),this.flags.changed=this.initialValue!==t},reset:function(){var t=this;this.errors=[],this.initialValue=this.value;var e={untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1};e.required=this.isRequired,this.setFlags(e),this.failedRules={},this.validateSilent(),this._pendingValidation=void 0,this._pendingReset=!0,setTimeout((function(){t._pendingReset=!1}),this.debounce)},validate:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n(this,void 0,void 0,(function(){return a(this,(function(e){return 0<t.length&&this.syncValue(t[0]),[2,U(this)]}))}))},validateSilent:function(){return n(this,void 0,void 0,(function(){var t,e;return a(this,(function(n){switch(n.label){case 0:return this.setFlags({pending:!0}),t=z(z({},this._resolvedRules),this.normalizedRules),Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,S(this.value,t,z(z({name:this.name||this.fieldName},j(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return e=n.sent(),this.setFlags({pending:!1,valid:e.valid,invalid:!e.valid}),[2,e]}}))}))},setErrors:function(t){this.applyResult({errors:t,failedRules:{}})},applyResult:function(t){var e=t.errors,n=t.failedRules;t=t.regenerateMap,this.errors=e,this._regenerateMap=t,this.failedRules=z({},n||{}),this.setFlags({valid:!e.length,passed:!e.length,invalid:!!e.length,failed:!!e.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var t=function(t){return t.vid?t.vid:t.name?t.name:t.id?t.id:t.fieldName?t.fieldName:"_vee_"+ ++at}(this),e=this.id;!this.isActive||e===t&&this.$_veeObserver.refs[e]||(e!==t&&this.$_veeObserver.refs[e]===this&&this.$_veeObserver.unobserve(e),this.id=t,this.$_veeObserver.observe(this))}}}),it=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],rt=0,ot=e.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,default:"span"},vid:{type:String,default:function(){return"obs_"+rt++}},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:H(),fields:{}}},created:function(){var t=this;this.id=this.vid,V(this);var e=v((function(e){var n=e.flags,a=e.fields;t.errors=e.errors,t.flags=n,t.fields=a}),16);this.$watch(Z,e)},activated:function(){V(this)},deactivated:function(){W(this)},beforeDestroy:function(){W(this)},render:function(t){var e=L(this,z(z({},this.flags),{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=e.length?e[0]:t(this.tag,{on:this.$listeners},e)},methods:{observe:function(t,e){var n;void 0===e&&(e="provider"),"observer"===e?this.observers.push(t):this.refs=z(z({},this.refs),((n={})[t.id]=t,n))},unobserve:function(t,e){if(void 0===e&&(e="provider"),"provider"===e)this.refs[t]&&this.$delete(this.refs,t);else{var n=d(this.observers,(function(e){return e.id===t}));-1!==n&&this.observers.splice(n,1)}},validate:function(t){var e=void 0!==(t=(void 0===t?{}:t).silent)&&t;return n(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,Promise.all(s(m(this.refs).filter((function(t){return!t.disabled})).map((function(t){return t[e?"validateSilent":"validate"]().then((function(t){return t.valid}))})),this.observers.filter((function(t){return!t.disabled})).map((function(t){return t.validate({silent:e})}))))];case 1:return[2,t.sent().every((function(t){return t}))]}}))}))},handleSubmit:function(t){return n(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,this.validate()];case 1:return e.sent()&&t?[2,t()]:[2]}}))}))},reset:function(){return s(m(this.refs),this.observers).forEach((function(t){return t.reset()}))},setErrors:function(t){var e=this;Object.keys(t).forEach((function(n){var a=e.refs[n];a&&(n="string"==typeof(n=t[n]||[])?[n]:n,a.setErrors(n))})),this.observers.forEach((function(e){e.setErrors(t)}))}}});t.ValidationObserver=ot,t.ValidationProvider=st,t.configure=function(t){K(t)},t.extend=function(t,e){if(!l(e)&&!l(e.validate)&&!J.getRuleDefinition(t))throw Error("Extension Error: The validator '"+t+"' must be a function or have a 'validate' method.");"object"==typeof e?J.extend(t,e):J.extend(t,{validate:e})},t.localeChanged=C,t.localize=function(t,e){var n;G||(G=new et("en",{}),K({defaultMessage:function(t,e){return G.resolve(t,null==e?void 0:e._rule_,e||{})}})),"string"==typeof t?(G.locale=t,e&&G.merge(((n={})[t]=e,n)),C()):G.merge(t)},t.normalizeRules=y,t.setInteractionMode=function(t,e){if(K({mode:t}),e){if(!l(e))throw Error("A mode implementation must be a function");X[t]=e}},t.validate=S,t.version="3.2.3",t.withValidation=function(t,e){void 0===e&&(e=p);var n,a="options"in t?t.options:t,s=st.options;s={name:(a.name||"AnonymousHoc")+"WithValidation",props:z({},s.props),data:s.data,computed:z({},s.computed),methods:z({},s.methods),beforeDestroy:s.beforeDestroy,inject:s.inject};var i=(null===(n=null==a?void 0:a.model)||void 0===n?void 0:n.event)||"input";return s.render=function(t){var n;this.registerField();var s=$(this),r=z({},this.$listeners),o=O(this.$vnode);this._inputEventName=this._inputEventName||R(this.$vnode,o);var l=D(this.$vnode);N(this,null==l?void 0:l.value);var u=(l=E(this)).onBlur,d=l.onValidate;return I(r,i,l.onInput),I(r,"blur",u),this.normalizedEvents.forEach((function(t){I(r,t,d)})),l=(Y(this.$vnode)||{prop:"value"}).prop,s=z(z(z({},this.$attrs),((n={})[l]=null==o?void 0:o.value,n)),e(s)),t(a,{attrs:this.$attrs,props:s,on:r},function(t,e){return Object.keys(t).reduce((function(n,a){return t[a].forEach((function(n){n.context||(t[a].context=e,n.data||(n.data={}),n.data.slot=a)})),n.concat(t[a])}),[])}(this.$slots,this.$vnode.context))},s},Object.defineProperty(t,"__esModule",{value:!0})})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).VeeValidateRules={})}(this,(function(t){"use strict";var e={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},n={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},a={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},i=function(t,n){var a=(void 0===n?{}:n).locale,s=void 0===a?"":a;return Array.isArray(t)?t.every((function(t){return i(t,{locale:s})})):s?(e[s]||e.en).test(t):Object.keys(e).some((function(n){return e[n].test(t)}))},r={validate:i,params:[{name:"locale"}]},o=function(t,e){var n=(void 0===e?{}:e).locale,a=void 0===n?"":n;return Array.isArray(t)?t.every((function(t){return o(t,{locale:a})})):a?(s[a]||s.en).test(t):Object.keys(s).some((function(e){return s[e].test(t)}))},l={validate:o,params:[{name:"locale"}]},u=function(t,e){var n=(void 0===e?{}:e).locale,s=void 0===n?"":n;return Array.isArray(t)?t.every((function(t){return u(t,{locale:s})})):s?(a[s]||a.en).test(t):Object.keys(a).some((function(e){return a[e].test(t)}))},d={validate:u,params:[{name:"locale"}]},c=function(t,e){var a=(void 0===e?{}:e).locale,s=void 0===a?"":a;return Array.isArray(t)?t.every((function(t){return c(t,{locale:s})})):s?(n[s]||n.en).test(t):Object.keys(n).some((function(e){return n[e].test(t)}))},h={validate:c,params:[{name:"locale"}]},m=function(t,e){var n=void 0===e?{}:e,a=n.min,s=n.max;return Array.isArray(t)?t.every((function(t){return!!m(t,{min:a,max:s})})):Number(a)<=t&&Number(s)>=t},f={validate:m,params:[{name:"min"},{name:"max"}]},p={validate:function(t,e){var n=e.target;return String(t)===String(n)},params:[{name:"target",isTarget:!0}]},v=function(t,e){var n=e.length;if(Array.isArray(t))return t.every((function(t){return v(t,{length:n})}));var a=String(t);return/^[0-9]*$/.test(a)&&a.length===n},g={validate:v,params:[{name:"length",cast:function(t){return Number(t)}}]},y={validate:function(t,e){var n=e.width,a=e.height,s=[];t=Array.isArray(t)?t:[t];for(var i=0;i<t.length;i++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(t[i].name))return Promise.resolve(!1);s.push(t[i])}return Promise.all(s.map((function(t){return e=t,s=n,i=a,r=window.URL||window.webkitURL,new Promise((function(t){var n=new Image;n.onerror=function(){return t(!1)},n.onload=function(){return t(n.width===s&&n.height===i)},n.src=r.createObjectURL(e)}));var e,s,i,r}))).then((function(t){return t.every((function(t){return t}))}))},params:[{name:"width",cast:function(t){return Number(t)}},{name:"height",cast:function(t){return Number(t)}}]},b={validate:function(t,e){var n=(void 0===e?{}:e).multiple,a=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return n&&!Array.isArray(t)&&(t=String(t).split(",").map((function(t){return t.trim()}))),Array.isArray(t)?t.every((function(t){return a.test(String(t))})):a.test(String(t))},params:[{name:"multiple",default:!1}]};function _(t){return null==t}function S(t){return Array.isArray(t)&&0===t.length}function w(t){return"function"==typeof Array.from?Array.from(t):function(t){for(var e=[],n=t.length,a=0;a<n;a++)e.push(t[a]);return e}(t)}function k(t){return S(t)||-1!==[!1,null,void 0].indexOf(t)||!String(t).trim().length}var M=function(t,e){return Array.isArray(t)?t.every((function(t){return M(t,e)})):w(e).some((function(e){return e==t}))},A={validate:M},C={validate:function(t,e){return!M(t,e)}},O={validate:function(t,e){var n=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(t)?t.every((function(t){return n.test(t.name)})):n.test(t.name)}},D={validate:function(t){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(t)?t.every((function(t){return e.test(t.name)})):e.test(t.name)}},T={validate:function(t){return Array.isArray(t)?t.every((function(t){return/^-?[0-9]+$/.test(String(t))})):/^-?[0-9]+$/.test(String(t))}},Y={validate:function(t,e){var n=e.length;return!_(t)&&("number"==typeof t&&(t=String(t)),t.length||(t=w(t)),t.length===n)},params:[{name:"length",cast:function(t){return Number(t)}}]},I=function(t,e){var n=e.length;return _(t)?0<=n:Array.isArray(t)?t.every((function(t){return I(t,{length:n})})):String(t).length<=n},x={validate:I,params:[{name:"length",cast:function(t){return Number(t)}}]},R=function(t,e){var n=e.max;return!_(t)&&""!==t&&(Array.isArray(t)?0<t.length&&t.every((function(t){return R(t,{max:n})})):Number(t)<=n)},L={validate:R,params:[{name:"max",cast:function(t){return Number(t)}}]},$={validate:function(t,e){var n=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(t)?t.every((function(t){return n.test(t.type)})):n.test(t.type)}},N=function(t,e){var n=e.length;return!_(t)&&(Array.isArray(t)?t.every((function(t){return N(t,{length:n})})):String(t).length>=n)},P={validate:N,params:[{name:"length",cast:function(t){return Number(t)}}]},U=function(t,e){var n=e.min;return!_(t)&&""!==t&&(Array.isArray(t)?0<t.length&&t.every((function(t){return U(t,{min:n})})):Number(t)>=n)},E={validate:U,params:[{name:"min",cast:function(t){return Number(t)}}]},j=/^[٠١٢٣٤٥٦٧٨٩]+$/,F=/^[0-9]+$/,W={validate:function(t){function e(t){var e=String(t);return F.test(e)||j.test(e)}return Array.isArray(t)?t.every(e):e(t)}},V=function(t,e){var n=e.regex;return Array.isArray(t)?t.every((function(t){return V(t,{regex:n})})):n.test(String(t))},H={validate:V,params:[{name:"regex",cast:function(t){return"string"==typeof t?new RegExp(t):t}}]},Z={validate:function(t,e){var n=(void 0===e?{allowFalse:!0}:e).allowFalse,a={valid:!1,required:!0};return _(t)||S(t)||!1===t&&!n||(a.valid=!!String(t).trim().length),a},params:[{name:"allowFalse",default:!0}],computesRequired:!0},G={validate:function(t,e){var n,a=e.target,s=e.values;return(n=s&&s.length?(Array.isArray(s)||"string"!=typeof s||(s=[s]),s.some((function(t){return t==String(a).trim()}))):!k(a))?{valid:!k(t),required:n}:{valid:!0,required:n}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},z={validate:function(t,e){var n=e.size;if(isNaN(n))return!1;var a=1024*n;if(!Array.isArray(t))return t.size<=a;for(var s=0;s<t.length;s++)if(t[s].size>a)return!1;return!0},params:[{name:"size",cast:function(t){return Number(t)}}]};t.alpha=r,t.alpha_dash=l,t.alpha_num=d,t.alpha_spaces=h,t.between=f,t.confirmed=p,t.digits=g,t.dimensions=y,t.email=b,t.excluded=C,t.ext=O,t.image=D,t.integer=T,t.is={validate:function(t,e){return t===e.other},params:[{name:"other"}]},t.is_not={validate:function(t,e){return t!==e.other},params:[{name:"other"}]},t.length=Y,t.max=x,t.max_value=L,t.mimes=$,t.min=P,t.min_value=E,t.numeric=W,t.oneOf=A,t.regex=H,t.required=Z,t.required_if=G,t.size=z,Object.defineProperty(t,"__esModule",{value:!0})})),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid">\n                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(t){this.$emit("onUserInfo",t)},onMini(t){this.isMiniSidebar=t}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(t,e){t!==e&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(t,e){"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(t){this.updateActiveOrg(t.id)},orgUpdated(t){const e=t?.response?.data;201===e?.code?(sessionStorage.clear(),window.location.reload(!0)):e?.message&&console.log(e.message)},updateActiveOrg(t){this.$buefy.loading.open();const e={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:t},callback:!0,callbackFunc:t=>this.orgUpdated(t)};this.dispatchData("postData",e)},dispatchData(t,e){this.$store.dispatch(t,e)},init(){if("org-admin"===this.userInfo.data.role){const t=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=t}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){t.response.data.data}},fetchReferralCode(){const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const t=sessionStorage.getItem("isLHSMenu");null===t||this.isMobile?this.reduce=!1:(this.reduce="true"===t,this.sidebarToggle(!0))},sidebarToggle(t){t||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(t){let e=parseInt(t-10),n=parseInt(6e4*e);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data;this.config.yunoAPIToken="Bearer "+e.token,this.tokenExpiry.payload.token="Bearer "+e.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.doneRefreshAPIToken(t)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data,n=10;if(e.minutes<=n){let t={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(t)}else this.initTokenTime(e.minutes)}},fetchAPITokenExpiryTime(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.gotAPITokenExpiryTime(t)}};this.$store.dispatch("postData",n)},extractSlugFromURL(t){const e=t.replace(/\/$/,"").split("/");""===e[e.length-1]&&e.pop();return e[e.length-1]},manageCurrentPage(t){const e=t=>t.replace(/\/$/,""),n=e(window.location.origin+window.location.pathname);t.forEach((t=>{t.items.forEach((t=>{t.is_active=n===e(t.url);let a=!1;t.sub_items.forEach((s=>{s.is_active=n===e(s.url),s.is_active&&s.parent_id===t.id&&(a=!0)})),t.is_expended=!!a}))}))},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},gotPostLoginMenu(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";t?e=t.response.data.data:(e=this.header.data,this.header.success=!0),this.manageCurrentPage(e),this.header.data=e,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(t){const e={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,a={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(t){return n.gotPostLoginMenu(t)}};this.$store.dispatch("fetchData",a)},manageOrgAdmin(t){const{host:e}=YUNOCommon.config,{has_org:n,org_id:a}=t;null===sessionStorage.getItem("activeOrg")&&(n?a.length>1?(window.location.href=`${e()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(a[0].id)):window.location.href=`${e()}/create-organization-account`)},gotUserInfo(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";if(t?e=t.response.data.data:(e=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(e.role),this.userRole.data=e.role,this.userProfile.data=e,this.userProfile.success=!0,e.role,"Learner"===e.role&&this.fetchReferralCode(),"Learner"===e.role&&"pending"===e.is_signup_completed){const t=localStorage.getItem("userState");window.location.pathname+window.location.search!==t&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}e.role,this.$emit("userInfo",e)}},fetchUserInfo(){const t=this,e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(e){return t.gotUserInfo(e)}};this.$store.dispatch("fetchData",e)},getStorage(){const t=this.storage;let e=Number(JSON.parse(JSON.stringify(t.version)));lastStorage=t.name+"V"+--e,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(t.name+"V"+t.version);if(null!==n){const t=JSON.parse(n);this.header.data=t.menu}this.loginStatus()},setStorage(){const t=this.storage,e={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const t=this.storage;sessionStorage.removeItem(t.name+"V"+t.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(t){if(this.generateCode.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;Event.$emit("fetchReferralCode")}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("postData",e)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(t,e){0!==e.sub_items.length&&t.preventDefault(),"generate-code"===e.slug&&t.preventDefault(),"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){const e=t.response.data.data;this.referralCode.data=e}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},gotCode(t){if(this.moduleWithoutTab.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;this.fetchReferralCode()}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotCode(e)}};this.$store.dispatch("postData",e)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Referral":"Referral Earnings",isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minium 10 numbers required",max:"Maxium 15 numbers required",is:"This field is required",email:"Please enter the valid email id"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-class-schedule-v2",{template:'\n        <yuno-page-grid\n\t\t\t@onUserInfo="onUserInfo"\n\t\t\t:authorizedRoles="authorizedRoles"\n\t\t\t:hasSearchBar="false"\n\t\t>\n\t\t\t<template v-slot:main>\n\t\t\t\t<b-loading :is-full-page="true" :active="isInProcess" :can-cancel="false"></b-loading>\n\t\t\t\t<template v-if="userProfile.data.vc_permission">\n\t\t\t\t\t<template v-if="isFormLoading">\n\t\t\t\t\t\t<div class="smallLoader"></div>\n\t\t\t\t\t</template>\n\t\t\t\t\t<template v-if="isFormReady">\n\t\t\t\t\t\t<section id="classSchedule" class="container formSection classSchedule">\n\t\t\t\t\t\t\t<template v-if="isEditClass()">\n\t\t\t\t\t\t\t\t<h1 class="sectionTitle">Edit Class</h1>    \n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t<h1 class="sectionTitle">Schedule Class</h1>    \n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<template v-if="isFormLoading">\n\t\t\t\t\t\t\t\t<div class="smallLoader"></div>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<validation-observer ref="classScheduleObserver" v-slot="{ handleSubmit }">\n\t\t\t\t\t\t\t\t<form id="classScheduleForm" @submit.prevent="handleSubmit(initForm)">\n\t\t\t\t\t\t\t\t\t<div class="row">\n\t\t\t\t\t\t\t\t\t\t<div class="col-12 col-md-7 col-lg-7">\n\t\t\t\t\t\t\t\t\t\t\t<div class="formWrapper">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Choose Category" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-autocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="e.g. Anne"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:data="filteredDataObj"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield="user.first_name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@select="option => (selected = option)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:clearable="true">\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-autocomplete>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Choose sub-category" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select placeholder="Select">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="option in data"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.id"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="option.id">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.user.first_name }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Class Title">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider :rules="{required:true, isSelected:filteredTitleObj.length}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-autocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tref="classTitleAutocomplete"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.payload.ClassTitle"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:data="filteredTitleObj"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Search class title"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield="title"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@select="onTitleSelect($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:clearable="true">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot="header">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a @click="addNewTitle">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span> Add new... </span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</a> \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot="empty">No results for {{classSchedule.payload.ClassTitle}}</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-autocomplete>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Related Course">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@input="onRelatedCourceChange($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.relatedCourses"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Select">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="">Select</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if="relatedCourses.data.length !== 0"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(course, courseIndex) in relatedCourses.data" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="courseIndex"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="course.course_id">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{course.course_name}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="field cardRadio" v-if="batches.isActive" label="Batch">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label class="label">Batch <small class="helper">(Choose one option)</small></label>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{required: false}" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="listWrapper">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="noBatchFound" v-if="allBatches.error">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{allBatches.errorData}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-for="(item, index) in allBatches.data">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-radio-button \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="index"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.payload.BatchID"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@input="onBatchSelect(item)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:native-value="item.batch_id">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ul class="batchList">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="listValue">{{ item.time }}</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="listValue">{{ item.class_days }}</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="listValue">({{ \'Batch ID: \' + item.batch_id}})</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-radio-button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Description">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.payload.ClassDescription"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype="textarea" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Related to a course" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-taginput\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="tags"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:data="filteredTags"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tautocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield="user.first_name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon="label"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Add a course"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@typing="getFilteredTags">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope="props">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<strong>{{props.option.id}}</strong>: {{props.option.user.first_name}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot="empty">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tThere are no items\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-taginput>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Class Privacy" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select placeholder="Select">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="option in data"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.id"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="option.id">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.user.first_name }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="inlineFields">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Date">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-datepicker\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.date"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:date-formatter="formatDate"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Pick date"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:mobile-native="false"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttrap-focus>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-datepicker>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Start Time">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-timepicker\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.time"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Pick time"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thour-format="12"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:mobile-native="false"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon="clock">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-timepicker>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Duration">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.payload.ClassDuration"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Select">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="">Select</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="15">15 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="30">30 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="45">45 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="60">1 Hour</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="75"> 1 Hour 15 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="90">1 Hour 30 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="105">1 Hour 45 Minutes</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value="120">2 Hours</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Instructor" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-autocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="e.g. Anne"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:data="filteredDataObj"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield="user.first_name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@select="option => (selected = option)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:clearable="true">\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-autocomplete>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field label="Zoom account to be used" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select placeholder="Select">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="option in data"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.id"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="option.id">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.user.first_name }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="radioList groupElement" v-if="hideField">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="fieldLabel">Recording</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<small class="helper">Choose one option</small>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="field">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-radio \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnative-value="Do not record"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname="recording">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tDo not record\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-radio>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="field">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-radio \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnative-value="Record but do not automatically share with anyone"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname="recording">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tRecord but do not automatically share with anyone\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-radio>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="field">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-radio \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnative-value="Record and share with anyone who enrolls"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname="recording">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tRecord and share with anyone who enrolls\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-radio>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="field">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-radio \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnative-value="Record and share with only those who attend"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname="recording">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tRecord and share with only those who attend\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-radio>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t<div class="ctaWrapper">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\t\t\t\tnative-type="submit"\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="isEditClass()">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tUpdate Class\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSchedule       \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-button>   \n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div class="col-12 col-md-5 col-lg-5 inviteWrapper">\n\t\t\t\t\t\t\t\t\t\t\t<b-field id="enrollLearners" label="Add People">\n\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider ref="inviteUser" :rules="{email:inviteEmail}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-autocomplete\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.currentLearner"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tref="inviteUserAutocomplete"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:data="filteredLernerObj"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder="Enter learner or group name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield="full_name"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@select="onLernerSelect($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:clearable="true">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot="header">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a @click="inviteOtherUser">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>Add this user...</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</a> \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot="empty">No results for {{classSchedule.currentLearner}}</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope="props">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="userItem">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<figure class="userImg">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="props.option.image_url !== undefined && props.option.image_url !== \'\'">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img :src="props.option.image_url" :alt="props.option.full_name">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else-if="props.option.image_url === \'\'">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-user-circle" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-users" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</figure>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="userName">{{props.option.full_name}}</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-autocomplete>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="classSchedule.payload.learner.length">\n\t\t\t\t\t\t\t\t\t\t\t\t<p class="count">People Added: <span>{{classSchedule.learnerSelected}}</span></p>\n\t\t\t\t\t\t\t\t\t\t\t\t<ul class="selectedLearners list-group">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="list-group-item"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(learner, learnerIndex) in classSchedule.payload.learner"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="learnerIndex">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<figure class="userImg">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="learner.image_url !== undefined && learner.image_url !== \'\'">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img :src="learner.image_url" :alt="learner.full_name">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else-if="learner.image_url === \'\'">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-user-circle" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="learner.group_id !== undefined">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-users" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class="fa fa-user-circle" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</figure>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="userName">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="learner.users !== undefined">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-collapse animation="slide" :open="false" aria-id="contentIdForA11y1">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="trigger" slot="trigger" aria-controls="contentIdForA11y1">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">{{learner.full_name}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="groupLearners">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ul>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li v-for="(groupLearner, groupLearnerIndex) in learner.users">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{groupLearner.name}} ({{groupLearner.email}})\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-collapse>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">{{learner.full_name}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="removeLearner(learner)" class="fa fa-times-circle" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="allLearners.loading">\n\t\t\t\t\t\t\t\t\t\t\t\t<ul class="suggestUser">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li class="title has2Col">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="caption">Suggested People</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li v-for="i in suggestUser.loadingResult" :key="i" class="loading">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="media">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-skeleton circle width="24px" height="24px"></b-skeleton>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-skeleton active width="100px"></b-skeleton>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="allLearners.success">\n\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="allLearners.error === null">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ul class="suggestUser">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li class="title has2Col">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="caption">Suggested People</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="secondaryElement">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-checkbox class="selectAll" v-model="suggestUser.selectAll" @input="suggestUserSelect" :disabled="suggestUser.selectAll">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSelect All\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-checkbox>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li v-for="(item, i) in allLearners.data">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="media">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img class="userImg" :src="item.image_url" :alt="item.full_name" width="40" height="40">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="name">{{item.full_name}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href="#" @click.prevent="picksuggestedUser(item)" class="addToPeople"><span class="material-icons">add</span></a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ul class="suggestUser">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li class="title has2Col">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class="caption">Suggested People</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ allLearners.errorData }}    \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</form>    \n\t\t\t\t\t\t\t</validation-observer>\n\t\t\t\t\t\t</section>\n\t\t\t\t\t\t<b-modal \n\t\t\t\t\t\t\t:active.sync="googlePermission.modal" \n\t\t\t\t\t\t\t:width="500" \n\t\t\t\t\t\t\t:can-cancel="false" \n\t\t\t\t\t\t\tclass="yunoModal">\n\t\t\t\t\t\t\t\t<div class="modalHeader">\n\t\t\t\t\t\t\t\t\t<h2 class="modalTitle">Google Permissions</h2>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="modalBody">\n\t\t\t\t\t\t\t\t\t<div class="wrapper">\n\t\t\t\t\t\t\t\t\t\t<p class="marginBtm16">Please grant following the permission to schedule a class on Google Meet:</p>\n\t\t\t\t\t\t\t\t\t\t<ul class="classFields">\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar</span>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Calendar Events</span>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption"><i class="fa fa-arrow-circle-right" aria-hidden="true"></i> Reports</span>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t<p class="userNote"><i class="fa fa-info-circle" aria-hidden="true"></i> You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span></p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="modalFooter">\n\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\ttag="a"\n\t\t\t\t\t\t\t\t\t\t:href="getGoogleMeetURL"\n\t\t\t\t\t\t\t\t\t\t@click="initGooglePermission()"\n\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\tGrant Permission\n\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</b-modal>\n\t\t\t\t\t\t<b-modal \n\t\t\t\t\t\t\t:active.sync="isCourseSelect" \n\t\t\t\t\t\t\t:width="450" \n\t\t\t\t\t\t\t:can-cancel="[\'escape\', \'x\']" \n\t\t\t\t\t\t\t:on-cancel="onModalClose"\n\t\t\t\t\t\t\tclass="yunoModal lightTheme">\n\t\t\t\t\t\t\t\t<div class="modalHeader">\n\t\t\t\t\t\t\t\t\t<h2 class="modalTitle">Please Select Academy</h2>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="modalBody">\n\t\t\t\t\t\t\t\t\t<div class="wrapper marginBtm30">\n\t\t\t\t\t\t\t\t\t\t<b-field label="Academy">\n\t\t\t\t\t\t\t\t\t\t<validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n\t\t\t\t\t\t\t\t\t\t\t<b-select \n\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\tv-model="classSchedule.payload.academy_id"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder="Select Academy"\n\t\t\t\t\t\t\t\t\t\t\t\texpanded>\n\t\t\t\t\t\t\t\t\t\t\t<option value="">Select Academy</option>\n\t\t\t\t\t\t\t\t\t\t\t<template v-for="item in userInfo.data.academies">\n\t\t\t\t\t\t\t\t\t\t\t\t<option :value="item.id">{{item.name}}</option>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div class="ctaWrapper alignLeft">\n\t\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\t\t:loading="classSchedule.isLoading === true ? true : false"\n\t\t\t\t\t\t\t\t\t\t\t:disabled="classSchedule.isLoading === true ? true : false" \n\t\t\t\t\t\t\t\t\t\t\tnative-type="submit"\n\t\t\t\t\t\t\t\t\t\t\t@click="confirmClass"\n\t\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\t\tSave\n\t\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</b-modal>\n\t\t\t\t\t\t<b-modal \n\t\t\t\t\t\t\t:active.sync="classSchedule.modal" \n\t\t\t\t\t\t\t:width="450" \n\t\t\t\t\t\t\t:can-cancel="[\'escape\', \'x\']" \n\t\t\t\t\t\t\t:on-cancel="onModalClose"\n\t\t\t\t\t\t\tclass="yunoModal lightTheme">\n\t\t\t\t\t\t\t\t<div class="modalHeader">\n\t\t\t\t\t\t\t\t\t<h2 class="modalTitle">Confirm before scheduling</h2>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="modalBody">\n\t\t\t\t\t\t\t\t\t<div class="wrapper marginBtm30">\n\t\t\t\t\t\t\t\t\t\t<ul class="classFields">\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Class Title:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t{{classSchedule.payload.ClassTitle}}\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Date & Time:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t{{dateTimeFormat}}\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Duration:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t{{classSchedule.payload.ClassDuration}} Minutes\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li v-if="classSchedule.payload.learner.length !== 0">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">People({{ classSchedule.learnerSelected }}):</span>\n\n\t\t\t\t\t\t\t\t\t\t\t\t<ul class="selectedLearners list-group">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="list-group-item"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(learner, learnerIndex) in classSchedule.payload.learner"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="learnerIndex">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">{{learner.full_name}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div class="ctaWrapper alignLeft">\n\t\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\t\t:loading="classSchedule.isLoading === true ? true : false"\n\t\t\t\t\t\t\t\t\t\t\t:disabled="classSchedule.isLoading === true ? true : false" \n\t\t\t\t\t\t\t\t\t\t\tnative-type="submit"\n\t\t\t\t\t\t\t\t\t\t\t@click="classSchedule.payload.learner.length !== 0 ? submitForm() : noPeople()"\n\t\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\t\tGo ahead and schedule\n\t\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</b-modal>\n\t\t\t\t\t\t<b-modal \n\t\t\t\t\t\t\t:active.sync="classSchedule.successModal" \n\t\t\t\t\t\t\t:width="500" \n\t\t\t\t\t\t\t:can-cancel="false" \n\t\t\t\t\t\t\t:on-cancel="onModalClose"\n\t\t\t\t\t\t\tclass="yunoModal lightTheme">\n\t\t\t\t\t\t\t\t<div class="modalHeader">\n\t\t\t\t\t\t\t\t\t<h2 class="modalTitle">Class scheduled successfully</h2>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="modalBody">\n\t\t\t\t\t\t\t\t\t<div class="wrapper marginBtm30">\n\t\t\t\t\t\t\t\t\t\t<ul class="classFields">\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Class Title:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t{{classSchedule.data.Title}}\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Date & Time:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t{{dateTimeFormat}}\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Guest link:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<p class="listSubtitle">Your learner will receive email and WhatsApp notifications regarding this class</p>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="clipboard">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input id="yunoClassLink" :value="classSchedule.data.guestURL" readonly></b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="copyToClipboard(\'yunoClassLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\tNote: anyone with a Yuno account can attend the class with this link\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li v-if="false">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Zoom Meeting ID:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="clipboard">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input id="yunoZoomMeetingID" :value="classSchedule.data.zoom_meeting_id" readonly></b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="copyToClipboard(\'yunoZoomMeetingID\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li v-if="false">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Yuno class link:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="clipboard">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input id="yunoClassLink" :value="classSchedule.data.ClassLink" readonly></b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="copyToClipboard(\'yunoClassLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t<li v-if="false">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class="caption">Zoom class link:</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="clipboard">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input id="zoomClassLink" :value="classSchedule.data.ZoomClassLink" readonly></b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<i @click="copyToClipboard(\'zoomClassLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div class="ctaWrapper alignLeft">\n\t\t\t\t\t\t\t\t\t\t<b-button tag="a"\n\t\t\t\t\t\t\t\t\t\t\thref="/instructor"\n\t\t\t\t\t\t\t\t\t\t\tclass="yunoSecondaryCTA">\n\t\t\t\t\t\t\t\t\t\t\tGo to My Schedule\n\t\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</b-modal>\n\t\t\t\t\t</template>\n\t\t\t\t</template>\n\t\t\t\t<template v-else>\n\t\t\t\t\t<section class="disableAccount">\n\t\t\t\t\t\t<div class="container">\n\t\t\t\t\t\t\t<div class="row">\n\t\t\t\t\t\t\t\t<div class="col-12 col-md-8 offset-md-2">\n\t\t\t\t\t\t\t\t\t<div class="wrapper">\n\t\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined disableIcon">\n\t\t\t\t\t\t\t\t\t\t\tevent\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<p class="desciprtion">\n\t\t\t\t\t\t\t\t\t\t\tYou can not schedule a class yet. To schedule classes please contact at <a href="mailto:<EMAIL>"><EMAIL></a>\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</section>\n\t\t\t\t</template>\n\t\t\t</template>\n\t\t</yuno-page-grid>\n    ',data:()=>({isInProcess:!1,isMiniSidebar:!1,authorizedRoles:["Instructor"],hideField:!1,selected:null,isSelectOnly:!1,tags:[],dateTimeFormat:"",inviteEmail:!1,isCourseSelect:!1,googlePermission:{modal:!1,isLoading:!1},message:{common:"Error occurred, please try again later"},batches:{isActive:!1,selected:null,current:"",isRequired:!1},suggestUser:{isActive:!1,selectAll:!1,loadingResult:3,users:[{email:"<EMAIL>",id:6939,name:"yunotesting 4 (<EMAIL>)",photo:"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg"},{email:"<EMAIL>",id:6940,name:"yunotesting 64 (<EMAIL>)",photo:"https://lh5.googleusercontent.com/-guQmPZmax3M/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckQaW32V3nQdOFvuAdxfzx0Uu2kJA/photo.jpg"}]}}),computed:{...Vuex.mapState(["user","header","userProfile","userRole","footer","classSchedule","classTitle","myLearners","classEdit","loader","addClassTitle","relatedCourses","userInfo","allBatches","allLearners"]),filterBatches(){return this.allBatches.data.filter((t=>t.search.toString().toLowerCase().indexOf(this.batches.current.toLowerCase())>=0))},getHomeURL(){return this.$store.state.homeURL},getGoogleMeetURL(){return this.$store.getters.googleMeet},isFormLoading:{get(){let t="";return t=this.isEditClass()?this.classTitle.loading||this.myLearners.loading||this.relatedCourses.loading||this.classEdit.loading:this.classTitle.loading||this.myLearners.loading||this.relatedCourses.loading,t}},isFormReady:{get(){let t="";return t=this.isEditClass()?this.classTitle.success&&this.myLearners.success&&this.relatedCourses.success&&this.classEdit.success:this.classTitle.success&&this.myLearners.success&&this.relatedCourses.success,t}},filteredTitleObj(){return 0!==this.classTitle.data.length?this.classTitle.data.filter((t=>t.title.toString().toLowerCase().indexOf(this.classSchedule.payload.ClassTitle.toLowerCase())>=0)):[]},filteredLernerObj(){return this.myLearners.data.filter((t=>t.full_name.toString().toLowerCase().indexOf(this.classSchedule.currentLearner.toLowerCase())>=0))}},async created(){},mounted(){this.initiateEdit(),localStorage.removeItem("userState")},methods:{onUserInfo(t){YUNOCommon.findInArray(this.authorizedRoles,t.role)&&(this.fetchMyLearners(),this.fetchClassTitle(),this.fetchRelatedCourses())},formatDate:t=>moment(t).format("ddd, MMM DD, YYYY"),suggestUserSelect(t){let e=this.classSchedule.payload.learner,n=this.allLearners.data;if(t)for(let t=0;t<n.length;t++){const a=n[t];null===YUNOCommon.findObjectByKey(e,"id",a.id)&&(e.push(a),this.classSchedule.learnerSelected=parseInt(this.classSchedule.learnerSelected+1))}else this.classSchedule.payload.learner=[],this.classSchedule.learnerSelected=0},onBatchSelect(t){this.suggestUser.isActive=!0,this.classSchedule.payload.academy_id=t.academy_id,this.fetchBatchLearners(t.batch_id)},gotBatchLearners(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){t.response.data.data}},fetchBatchLearners(t){this.allLearners.success=!1,this.allLearners.error=null,this.allLearners.errorData=[],this.allLearners.data=[];const e=this,n={apiURL:YUNOCommon.config.batch("learners",{batchID:t}),module:"gotData",store:"allLearners",callback:!0,callbackFunc:function(t){return e.gotBatchLearners(t)}};this.$store.dispatch("fetchData",n)},onRelatedCourceChange(t){""!==t?(this.classSchedule.payload.BatchID="",this.batches.isActive=!0,this.batches.isRequired=!0,this.enableLoader(!0),this.fetchAllBatches(t)):(this.batches.isActive=!1,this.batches.isRequired=!1,this.classSchedule.payload.BatchID="")},gotAllBatches(t,e){if(this.enableLoader(!1),void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let n=t.response.data.data;1===n.length&&(this.classSchedule.payload.BatchID=n[0].batch_id,this.onBatchSelect(n[0])),this.allBatches.data=n,this.allBatches.error=null,this.isEditClass()&&(this.classSchedule.payload.BatchID=e)}else this.allBatches.data=[]},fetchAllBatches(t,e){const n=this,a={apiURL:YUNOCommon.config.courseListAPI("batch",t,isLoggedIn),module:"gotData",store:"allBatches",addToModule:!1,callback:!0,callbackFunc:function(t){return n.gotAllBatches(t,e)}};this.$store.dispatch("fetchData",a)},enableLoader(t){this.isInProcess=t},initGooglePermission(){localStorage.setItem("userState",window.location.pathname)},isPermissionModal(t){t.vc_google&&t.ins_meet_permission&&!t.ins_meet_permission_verified&&(this.googlePermission.modal=!0)},inviteOtherUser(){this.inviteEmail=!0,setTimeout((()=>{if(this.$refs.inviteUser.flags.valid){this.classSchedule.payload.learner;let t={email:this.classSchedule.currentLearner,id:"",name:this.classSchedule.currentLearner,photo:""};this.$refs.inviteUserAutocomplete.setSelected(t)}}),40)},newTitleAdded(t){if(this.loader.overlay=!1,this.loader.isActive=!1,void 0!==t.response&&201===t.response.status){this.$buefy.toast.open({duration:5e3,message:`${t.response.data.message}`,position:"is-bottom"});let e={description:"",title:this.addClassTitle.payload.title};this.classTitle.data.push(e),this.$refs.classTitleAutocomplete.setSelected(e)}else this.$buefy.toast.open({duration:5e3,message:`${t.response.data.message}`,position:"is-bottom",type:"is-danger"})},postNewTitle(){let t=this.addClassTitle.payload,e=this;const n={apiURL:YUNOCommon.config.addClassTitleAPi(),module:"gotData",store:"addClassTitle",payload:t,callback:!0,callbackFunc:function(t){return e.newTitleAdded(t)}};this.$store.dispatch("postData",n)},addNewTitle(){let t=this.addClassTitle.payload;t.instructor_id=isLoggedIn,t.title=this.classSchedule.payload.ClassTitle,this.loader.overlay=!0,this.loader.isActive=!0,this.postNewTitle()},updateLearners(t,e,n,a){let s="";"group"===a&&(s="group_id"),"learner"===a&&(s="id");let i=YUNOCommon.findObjectByKey(e,s,Number(t));n.push(i),YUNOCommon.removeObjInArr(e,"name",i.name),void 0!==i.user_count?this.classSchedule.learnerSelected=parseInt(i.user_count+this.classSchedule.learnerSelected):this.classSchedule.learnerSelected=""===this.classSchedule.learnerSelected?1:parseInt(this.classSchedule.learnerSelected+1)},hasGroupID(){let t=YUNOCommon.getQueryParameter("groupID"),e=YUNOCommon.getQueryParameter("learnerID"),n=this.myLearners.data,a=this.classSchedule.payload.learner;t&&this.updateLearners(t,n,a,"group"),e&&this.updateLearners(e,n,a,"learner")},isEditClass:()=>null!==localStorage.getItem("classEditState"),initiateEdit(){if(this.isEditClass()){let t=localStorage.getItem("classEditState"),e=JSON.parse(t);this.prefillClass(e.id,isLoggedIn)}},gotClassDetail(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let n=t.response.data.data,a=this.classEdit,s=this.myLearners,i=this.classSchedule,r=i.payload,o=new Date(n.start+":"+n.time);if(i.date=o,i.time=o,r.ClassTitle=n.title,r.ClassDescription=n.description,r.ClassDuration=n.duration,i.relatedCourses=n.related_course,r.academy_id=n.academy_id,""!==n.related_course&&(this.batches.isActive=!0,this.batches.isRequired=!0,this.fetchAllBatches(n.related_course,n.batch_id)),0!==n.users_groups.length)for(var e=0;e<n.users_groups.length;e++)r.learner.push(n.users_groups[e]),YUNOCommon.removeObjInArr(s.data,"name",n.users_groups[e].name),void 0!==n.users_groups[e].user_count?i.learnerSelected=parseInt(n.users_groups[e].user_count+i.learnerSelected):i.learnerSelected=""===i.learnerSelected?1:parseInt(i.learnerSelected+1);a.data=n}},prefillClass(t,e){const n=this,a={apiURL:YUNOCommon.config.editClassAPI(t,e),module:"gotData",store:"classEdit",addToModule:!1,callback:!0,callbackFunc:function(t){return n.gotClassDetail(t)}};this.$store.dispatch("fetchData",a)},initForm(){let t=moment(this.classSchedule.date).format("MMM DD, YYYY"),e=moment(this.classSchedule.date).format("ddd, MMM DD, YYYY"),n=moment(this.classSchedule.time).format("HH:mm"),a=moment(this.classSchedule.time).format("hh:mm a"),s=moment(t+" "+n,"MMMM Do YYYY, HH:mm").utc();if(this.classSchedule.payload.ClassDate=""+s._d,this.classSchedule.payload.ClassTime=""+s._d,this.classSchedule.payload.RelatedCourses="",this.classSchedule.payload.RelatedCourses=this.classSchedule.relatedCourses,this.dateTimeFormat=e+", "+a,""!==this.classSchedule.payload.academy_id?this.classSchedule.modal=!0:this.isCourseSelect=!0,this.isEditClass()){let t=localStorage.getItem("classEditState"),e=JSON.parse(t);this.classSchedule.payload.ClassId=e.id}},classScheduleDone(t){this.classSchedule.isLoading=!1,this.enableLoader(!1),void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code?(localStorage.removeItem("classEditState"),this.classSchedule.data=t.response.data.data,this.classSchedule.modal=!1,this.classSchedule.successModal=!0):this.$buefy.toast.open({duration:5e3,message:this.classSchedule.errorData,position:"is-bottom",type:"is-danger"})},confirmClass(){""!==this.classSchedule.payload.academy_id?(this.classSchedule.modal=!0,this.isCourseSelect=!1):(this.classSchedule.modal=!1,this.$buefy.toast.open({duration:1e3,message:"Please select an academy",position:"is-bottom",type:"is-danger"}))},proceedSchedule(){this.enableLoader(!0),this.submitForm()},noPeople(){this.classSchedule.modal=!1,this.$buefy.dialog.confirm({title:"Add People",message:"You have not added any people in this class",cancelText:"Go Back",confirmText:"Proceed Anyway",onConfirm:()=>this.proceedSchedule()})},submitForm(){this.classSchedule.isLoading=!0;let t="";t=this.isEditClass()?YUNOCommon.config.updateClassAPi(isLoggedIn):YUNOCommon.config.createClassAPi(isLoggedIn);const e=this,n={apiURL:t,module:"gotData",store:"classSchedule",payload:this.classSchedule.payload,callback:!0,callbackFunc:function(t){return e.classScheduleDone(t)}};this.$store.dispatch("postData",n)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom",type:"is-info"})},updateDateFormat(t,e){this.dateTimeFormat=t+", "+e},onModalClose(){},removeLearner(t){let e=this.classSchedule.payload.learner,n=YUNOCommon.removeObjInArr(e,"full_name",t.full_name);this.myLearners.data.push(t),e=n,void 0!==t.user_count?this.classSchedule.learnerSelected=Math.abs(parseInt(t.user_count-this.classSchedule.learnerSelected)):this.classSchedule.learnerSelected=Math.abs(parseInt(this.classSchedule.learnerSelected-1)),0===this.classSchedule.payload.learner.length&&(this.suggestUser.selectAll=!1)},onTitleSelect(t){if(null!==t){let e="";void 0!==t.course_id?(e=""!==t.course_id?Number(t.course_id):"",this.onRelatedCourceChange(e)):e="",this.classSchedule.payload.ClassDescription=t.description,this.classSchedule.relatedCourses=e}else this.classSchedule.payload.ClassDescription="",this.classSchedule.relatedCourses="",this.classSchedule.payload.BatchID="",this.batches.isActive=!1},picksuggestedUser(t){let e=this.classSchedule.payload.learner;null===YUNOCommon.findObjectByKey(e,"id",t.id)&&(e.push(t),this.classSchedule.learnerSelected=""===this.classSchedule.learnerSelected?1:parseInt(this.classSchedule.learnerSelected+1))},onLernerSelect(t){if(null!==t){this.classSchedule.payload.learner.push(t);let e=YUNOCommon.removeObjInArr(this.myLearners.data,"full_name",t.full_name);this.myLearners.data=e,void 0!==t.user_count?this.classSchedule.learnerSelected=parseInt(t.user_count+this.classSchedule.learnerSelected):this.classSchedule.learnerSelected=""===this.classSchedule.learnerSelected?1:parseInt(this.classSchedule.learnerSelected+1),setTimeout((()=>this.classSchedule.currentLearner=""),10)}},gotMyLearner(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let e=t.response.data.data;for(let t=0;t<e.length;t++){const n=e[t],a={id:n.id,full_name:n.name,image_url:n.photo,email:n.email};this.myLearners.data.push(a)}this.hasGroupID()}else this.myLearners.data=[]},fetchMyLearners(){const t=this,e={apiURL:YUNOCommon.config.myLearnersAPi(isLoggedIn),module:"gotData",store:"myLearners",addToModule:!1,callback:!0,callbackFunc:function(e){return t.gotMyLearner(e)}};this.$store.dispatch("fetchData",e)},gotRelatedCourses(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let e=t.response.data.data;this.relatedCourses.data=e}else this.relatedCourses.data=[]},fetchRelatedCourses(){const t=this,e={apiURL:YUNOCommon.config.relatedCoursesAPI(isLoggedIn),module:"gotData",store:"relatedCourses",addToModule:!1,callback:!0,callbackFunc:function(e){return t.gotRelatedCourses(e)}};this.$store.dispatch("fetchData",e)},gotClassTitle(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){let e=t.response.data.data;this.classTitle.data=e}else this.classTitle.data=[]},fetchClassTitle(){const t=this,e={apiURL:YUNOCommon.config.classTitleAPi(isLoggedIn),module:"gotData",store:"classTitle",addToModule:!1,callback:!0,callbackFunc:function(e){return t.gotClassTitle(e)}};this.$store.dispatch("fetchData",e)},getFilteredTags(t){this.filteredTags=data.filter((e=>e.user.first_name.toString().toLowerCase().indexOf(t.toLowerCase())>=0))}}});