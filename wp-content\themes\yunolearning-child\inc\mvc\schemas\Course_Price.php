<?php
return [
    'variants' => [
        [
            'type' => // Type of variant such as "personalization"
            [
                'based_on'=>'string', //The variation in the course on which the price is based on. E.g. Based on personalization i.e. Group classes or 1-to-1 classes, the course price can vary. Price can also vary based on the teaching mode i.e. online classes or in-person classes Allowed values: personalization  other
                'value'=>'string' //The name of the variant to be displayed E.g. this can be used to display to the learner Allowed values: Group 1-to-1
            ],
            'name' => 'string', // Label of the variant e.g. "Personalization"
            'list_price' => 'Refer#Price_List',
            'selling_price' => 'Refer#Price',
        ]
    ]
];
