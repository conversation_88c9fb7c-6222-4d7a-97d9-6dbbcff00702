@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.dark38 {
    @include setFontColor($primaryCopyColor, 0.38);
}

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

.largestTitle {
    @include setFont($headline3, 42px, 700, $gapSmall);

    @media (min-width: 768px) {
        @include setFont($headline1, 62px, 700, $gapSmall);
    }
}

.largerTitle {
    @include setFont($headline5, 28px, 500, $gapSmall);
    @extend .dark87;
}

.largeTitle {
    @include setFont($headline6, 24px, 500, 0);
}

.smallCaption {
    @include setFont($subtitle1, 24px, 500, 0);
}

.smallerCaption {
    @include setFont($body1, 25px, 500, $gapSmall);
}

.body1 {
    @include setFont($body2, 20px, 400, 0);
    @extend .dark60;
}

.body2 {
    @include setFont($caption2, 16px, 400, 0);
    @extend .dark60;
}

#app {
    @extend .dark87;

    .filterActive {
        height: 100vh;

        .mainBody {
            section + .container {
                position: static;
            }   
        }
    }

    .mainBody {
        &.postLogin {
            .filtersWrapper {
                top: 52px;
            }
        }
    }

    .forAd {
        .filtersWrapper {
            @media (min-width: 768px) {
                top: 0;
            }
        }

        
    }

    .filtersWrapper {
        top: 60px;

        &.mobileView {
            .categoryFilter {
                display: none;
            }
        }

        .filters {
            &.categoryFilter {
                justify-content: flex-end;
                position: absolute;
                right: 0;
                top: 0;

                @media (min-width: 768px) {
                    position: static;
                }
            }

            &.otherFilters {
                display: flex;
                margin-top: $gap15;

                .yunoDropdown {
                    display: none;

                    &:first-child {
                        display: block;
                    }
                }

                @media (min-width: 768px) {
                    margin-top: 0;

                    .yunoDropdown {
                        display: block;
                    }
                }

                &.mobileView {
                    .yunoDropdown {
                        display: block;
                    }
                }
            }
        }

        .categoryFilter {

            .category {
                display: none;
            }
        }

        @media (min-width: 768px) {
            top: 70px;
        }

        .b-tooltip.is-primary:after {
            background: $primary;
        }
    }

    .emptyStateV2 {
        display: flex;
        justify-content: center;
        margin-top: 5%;

        figure {
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 158px;
                height: auto;
            }

            figcaption {
                margin-top: $gap15;
                @include setFont($subtitle1, 24px, 500, 0);
            }

            .button {
                margin-top: $gap15;
            }
        }
    }

    .heroBanner {
        position: relative;
        box-sizing: border-box;
        padding: $gapLargest 0;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        height: auto;

        @media (min-width: 768px) {
            padding: $gapLargest * 2 0;
            // height: 350px;
        }

        .videoLPPlayer {
            position: relative;
            padding-bottom: 60.7%;
            overflow: hidden;
            max-width: 100%;
            margin: 0 auto;
    
            &.loading {
                .b-skeleton-item {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100% !important;
                }
            }
    
            iframe,
            object,
            embed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        .container {
            z-index: 3;
            height: 100%;

            .row {
                height: 100%;
            }
        }

        &:before {
            content: "";
            position: absolute;
            z-index: 2;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            @include setBGColor(#1D1D1F, 0.6);
        }

        .colLeft {
            color: white;
            align-self: center;

            .ctaWrapper {
                margin-top: $gap15;

                .button {
                    height: 48px;

                    .material-icons {
                        margin-left: $gapSmaller;
                    }
                }
            }
        }

        .colRight {
            align-self: center;

            .wrapper {
                background-color: white;
                border-radius: 4px;
                padding: $gapLargest;
                margin-top: $gap15;

                @media (min-width: 768px) {
                    margin-top: 0;
                }
            }

            .yunoSecondaryCTA {
                margin-top: $gap15
            }

            .observer {
                margin-top: $gap15;

                .field {
                    .label {
                        font-size: $subtitle2 !important;
                        @include setFontColor($primaryCopyColor, 0.6);
                    }

                    .error {
                        display: none;
                    }

                    .control {
                        input[type="text"] {
                            height: 40px !important;
                        }

                        &.invalid {
                            + .error {
                                display: block;
                            }
                        }
                    }
                    
                } 
            }

            .ctaWrapper {
                display: flex;
                justify-content: center;
            }
            .googleLogin {
                border: 1px solid #1976D2;
                border-radius: 4px;
                width: 100%;
                padding: 9px $gap15 8px;
                background-color: white;
                font-size: $button;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #1976D2;
                font-weight: 500;

                &.width70 {
                    width: 80%;
                }

                img {
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    margin-right: $gapSmall;
                }
            }
        }
    }

    .yunoTabsStatic {
        margin-top: $gap15;
        overflow-x: auto;
        position: sticky;
        top: 60px;
        background-color: #FFF;
        z-index: 8;

        @media (min-width: 768px) {
            overflow: visible;
            top: 72px;

            &.postLogin {
                top: 0;
            }
        }

        
        
        .tabsV2 {
            @include setBorderColor($primaryCopyColor, 0);
            justify-content: flex-start;
            display: flex;
            position: relative;

            &::after {
                content: "";
                width: 100%;
                height: 1px;
                @include setBGColor($primaryCopyColor, 0.08);
                position: absolute;
                left: 0;
                bottom: 0;
            }

            li {
                font-size: $subtitle2;
                flex: 0 0 auto;

                
                
                a {
                    @include setFontColor($primaryCopyColor, 0.38);
                    padding: $gap15 20px;
                    display: block;
                    border-bottom: 1px solid transparent;

                    &:hover {
                        text-decoration: none;
                        @extend .dark87;
                    }
                }

                &.is-active {
                    a {
                        @extend .dark87;
                        @include setBorderColor($primaryCopyColor, 1);
                        font-weight: 400;
                        background: rgba(0, 0, 0, 0.02);
                    }
                }
            }
        }
    }

    .section {
        padding: 0 0 $gapLargest;
        margin: 0 0 0;

        @media (min-width: 768px) {
            padding: 0 0 $gapLargest * 2;
            margin: $gapLargest 0 0;
        }

        .sectionHeader {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: $gap15;
            justify-content: space-between;
            
            @media (min-width: 768px) {
                justify-content: space-between;
            }

            .description {
                flex: 0 0 100%;
                @extend .body1;
                margin-top: $gapSmall;
            }
    
            .largeTitle {
                margin-bottom: 0;
            }
    
            .actions {
                display: flex;
                flex-wrap: wrap;
                margin: 0 (-$gapSmall);
                align-items: center;
    
                li {
                    padding: 0 $gapSmall;
                }
            }
        }
    }

    .categoryStudyMaterial {
        margin-top: $gap15;

        @media (min-width: 768px) {
            margin-top: $gapLargest;
        }
    }

    .courses {
        .coursesWrapper {
            margin: 0 (-$gap15);
            display: flex;
            flex-wrap: wrap;
        }

        .courseCard {
            flex: 0 0 100%;
            padding: 0 $gap15;
            margin-bottom: $gapLargest;

            @media (min-width: 768px) {
                flex: 0 0 25%;
                margin-bottom: 0;
            }

            .wrapper {
                box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
                background-color: white;
                padding: $gapLarger;
                height: 100%;
                display: block;
                border: 1px solid transparent;

                .smallerCaption {
                    color: initial;
                }

                &:hover {
                    text-decoration: none;
                    border-color: $grey;
                }
            }

            .courseFeatures {
                li {
                    display: flex;
                    align-items: center;
                    @include setFont($subtitle2, 20px, 400, 0);
                    @extend .dark60;
                    margin-bottom: $gapSmaller;

                    .material-icons-outlined {
                        font-size: 18px;
                        margin-right: $gapSmaller;
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .courseInstructor {
                font-size: $caption1;
                @extend .dark87;
                display: flex;
                justify-content: space-between;
                border-top: 1px solid rgba(0, 0, 0, 0.08);
                padding-top: $gapSmall;
                margin-top: $gapSmall;

                @media (min-width: 768px) {
                    justify-content: center;
                }

                .innerCaption {
                    @extend .dark60;
                    padding-left: $gapSmaller;
                }

                .instructorsList {
                    display: flex;
                    padding-left: $gapSmall;
                    padding-right: $gapSmall;
                    
                    li {
                        margin-left: -12px;
                    }

                    .instructorMapped {
                        $size: 24px;

                        img {
                            width: $size;
                            height: $size;
                            border-radius: 50%;
                            border: 2px solid #FFF;
                            font-size: 0;
                        }
                    }

                    .material-icons {
                        font-size: 24px;
                        background-color: $whiteBG;
                        border-radius: 50%;
                    }
                }
            }
        }
    }

    .studyMaterialWrapper {
        margin: 0 (-$gap15);
        display: flex;
        flex-wrap: wrap;

        .studyMaterialCard {
            padding: 0 $gap15;
            flex: 0 0 100%;
            margin-bottom: $gapLargest;

            @media (min-width: 768px) {
                flex: 0 0 33.3%;
                margin-bottom: 0;
            }

            .wrapper {
                box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
                background-color: white;
                padding: $gap15;
                height: 100%;
                display: block;
                border: 1px solid transparent;

                .largeTitle {
                    color: initial;
                }

                .studyMaterialFeatures li .val {
                    color: initial;
                }

                &:hover {
                    border-color: $grey;
                    text-decoration: none;
                }
            }

            figure {
                img {
                    max-width: 100%;
                    max-height: 180px;
                    height: auto;
                    display: block;
                    margin: 0 auto;
                }

                figcaption {
                    margin-bottom: $gap15;
                }

                .largeTitle {
                    margin: $gapSmall 0;
                }
            }

            .studyMaterialFeatures {
                margin: 0 (-$gap15);
                display: flex;
                flex-wrap: wrap;

                li {
                    flex: 0 0 50%;
                    padding: 0 $gap15;
                    display: flex;
                    align-items: center;
                    margin-top: $gapSmaller;

                    .val {
                        @include setFont($subtitle1, normal, 500, 0);
                        margin-right: $gapSmaller
                    }

                    .key {
                        @include setFont($caption1, normal, 400, 0);
                        @extend .dark60;
                    }

                    &:nth-child(odd) {
                        position: relative;

                        &::after {
                            content: "";
                            width: 1px;
                            height: 100%;
                            position: absolute;
                            right: 0;
                            top: 0;
                            background-color: rgba(0, 0, 0, 0.08);
                        }
                    }
                }
            }
        }
    }

    .videosWrapper {
        margin: 0 (-$gap15);
        display: flex;
        flex-wrap: wrap;

        .emptyStateV2 {
            flex: 0 0 100%;
        }

        .videoCard {
            flex: 0 0 100%;
            padding: 0 $gap15;
            margin-bottom: $gapLargest;

            @media (min-width: 768px) {
                flex: 0 0 33.3%;
            }

            .wrapper {
                box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
                background-color: white;
                padding: 0;
                border-radius: 4px;
                overflow: hidden;
            }

            figure {
                .imgWrapper {
                    display: block;
                    position: relative;
                    min-height: 196px;

                    &::before {
                        content: "\e037";
                        @extend .material-icons;
                        width: 40px;
                        height: 28px;
                        background-color: rgba(168, 30, 34, 0.38);
                        border-radius: 4px;
                        position: absolute;
                        left: calc(50% - 20px);
                        top: calc(50% - 14px);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: rgba(255, 255, 255, 0.645);
                    }
                }

                img {
                    width: 100%;
                    height: auto;
                    display: block;
                }

                figcaption {
                    padding: $gap15;
                }

                .largeTitle {
                    margin: 0 0 $gapSmall;
                }
            }

            .videoDetail {
                margin-top: $gap15;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .videoDate {
                    @include setFont($body2, normal, 400, 0);
                    @extend .dark60;
                }

                a {
                    @include setFont($caption1, normal, 500, 0);
                    text-decoration: underline;
                }
            }
        }
    }

    .videos {
        .videoCard {
            cursor: pointer;

            .wrapper {
                border: 1px solid transparent;

                &:hover {
                    border-color: rgba(0, 0, 0, 0.12);
                }
            }

            &.active {
                .wrapper {
                    border-color: rgba(0, 0, 0, 0.12);
                }
            }
        }
    }

    

    .meetInstructors {
        button[data-action="stop"], .tns-visually-hidden {
            display: none;
        }
    
        .tns-inner {
            overflow: hidden;
            margin: 0 (-$gap15);
            padding: $gapLargest $gap15;
    
            @media (min-width: 768px) {
                margin: 0 (-$gapLargest);
            }
        }
    
        .carouselListControls {
            display: flex;
            justify-content: center;
    
            button {
                width: 44px;
                height: 44px;
                background-color: $whiteBG;
                box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
                display: flex;
                align-items: center;
                justify-content: center;
                border: 0;
    
                &.prev {
                    margin-right: $gapLargest;
                }
            }
        }
    }

    .meetInstructorsWrapper {
        // margin: 0 (-$gap15);
        display: flex;
        flex-wrap: wrap;

        .meetInstructorsCard {
            // flex: 0 0 33.3%;
            padding: 0 $gap15;

            &.tns-slide-active {
                .wrapper  {
                    box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
                }
            }

            .wrapper {
                box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0);
                background-color: white;
                padding: $gapLarger;
                height: 100%;
            }

            .smallerCaption {
                margin: 0;
                line-height: normal;
            }


            figure {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: $gap15;

                .insImage {
                    flex: 0 0 64px;
                    margin-right: $gap15;

                    img {
                        width: 100%;
                        height: 64px;
                        border-radius: 50%;
                        background-color: $grey;
                        font-size: 0;
                        display: block;
                    }
                }

                figcaption {
                    flex: 0 0 calc(100% - 79px);
                }
            }

            .stars {
                display: flex;
                align-items: center;

                .vue-star-rating-rating-text {
                    @include setFont($body2, normal, 500, 0);
                    margin-left: $gapSmall;
                    position: relative;
                    top: 1px;
                }
            }

        }

        &.loading {
            .meetInstructorsCard {
                flex: 0 0 33.3%;
            }
        }
    }

    .studentsResultsWrapper {
        margin: 0 (-$gap15);
        display: flex;
        flex-wrap: wrap;

        

        .yunoStudentCard {
            flex: 0 0 100%;
            padding: 0 $gap15;
            

            @media (min-width: 768px) {
                flex: 0 0 50%;
                margin-bottom: 0;
            }

            .wrapper {
                background: #FFFFFF;
                box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                border-radius: 4px;
                padding: $gap15;
                margin-top: $gapLargest;
                @extend .dark87;
            }
    
            .cardHeader {
                margin-bottom: $gap15;
            }
    
            .userImg {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
    
                .userIcn {
                    flex: 0 0 64px;
                    height: 64px;
                    border-radius: 50%;
                    @include setBGColor($primaryCopyColor, 0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: $headline6;
                    text-transform: uppercase;
                    font-weight: 500;
                    @extend .dark87;

                    img {
                        height: 64px;
                        width: 100%;
                        border-radius: 50%;
                    }
                }
    
                figcaption {
                    margin-left: $gap15;
    
                    h3 {
                        font-size: $subtitle1;
                        font-weight: 500;
                        @extend .dark87;					
                        line-height: 24px;
                        margin: $gapSmaller 0 0;
                    }
                }
    
                .tag {
                    text-transform: uppercase;
                    font-size: $overline;
                    line-height: 16px;
                    @extend .dark60;
                    letter-spacing: 1.5px;
                    font-weight: 500;
                    border-radius: 15px;
                    @include setBGColor(#A5A6F6, 0.2);
    
                    &.yellow {
                        @include setBGColor(#F9B600, 0.2)
                    }
                }
            }
    
            .cardBody {
                h4 {
                    font-size: $subtitle1;
                    font-weight: 500;
                    line-height: 24px;
                    margin-bottom: $gapSmall;
                }
            }
    
            .scoreList {
                display: flex;
                flex-wrap: wrap;
    
                li {
                    display: flex;
                    align-items: center;
                    font-size: $subtitle1;
                    font-weight: 500;
                    margin-right: $gapSmall;
    
                    .helper {
                        font-size: $caption1;
                        @extend .dark60;
                        text-transform: lowercase;
                        font-weight: 400;
                        display: inline-block;
                        padding: 0 $gapSmall 0 $gapSmaller;
                    }
    
                    &::after {
                        content: "|";
                        display: inline-block;
                        @include setFontColor($primaryCopyColor, 0.2);
                    }
    
                    &:last-child {
                        margin-right: 0;
    
                        &::after {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .sections {
        .sectionWrapper {
            margin-bottom: $gapLargest;

            p {
                margin-bottom: $gap15;
            }

            h2 {
                @include setFont($headline4, auto, 500, $gap15);
                @extend .dark87;
                margin-top: $gap15
            }

            h3 {
                @include setFont($headline5, auto, 500, $gap15);
                @extend .dark87;
                margin-top: $gap15
            }

            h4 {
                @include setFont($headline6, auto, 500, $gap15);
                @extend .dark87;
                margin-top: $gap15
            }

            ul, ol {
                padding: 0;
                margin: 0 0 $gap15 18px;

                li {
                    list-style: disc outside;
                    margin-bottom: $gapSmaller;
                    font-size: $fontSizeLarge;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            ol {
                li {
                    list-style: decimal outside;
                }
            }
        }
    }

    .yunoAccordion {
        margin-bottom: $gapLargest * 2;
    }
    
    .practiceTests {
        .filters {
            margin: 0 0;
            display: flex;
            flex-wrap: wrap;
            margin-bottom: $gapLargest;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            padding-bottom: 0;

            li {
                padding: 0 0 0 0;
                flex: 0 0 auto;
    
                @media (min-width: 768px) {
                    padding: 0 0 0 $gap15;
                }
    
                .innerWrap {
                    background-color: transparent;
                    padding: 15px 20px;
                    @extend .dark38;
                    font-size: $body2;
                    display: block;
                    cursor: pointer;
                    position: relative;
    
                    &::after {
                        content: "";
                        width: 100%;
                        height: 1px;
                        background: rgba(0, 0, 0, 0.87);
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        display: none;
                    }
    
                    &:hover {
                        text-decoration: none;
                        @extend .dark87;
                    }
                }
    
                &.active {
                    .innerWrap {
                        background: rgba(0, 0, 0, 0.02);
                        @extend .dark87;
    
                        &:after {
                            display: block;
                        }
                    }
                }
            }
        }

        .cardWrapper {
            .listView {
                display: flex;
                margin: 0 (-$gap15);
                flex-wrap: wrap;
            }
    
            .yunoCard {
                padding: 0 $gap15;
                flex: 0 0 100%;
                margin-bottom: $gapLargest;
    
                @media (min-width: 1600px) {
                    flex: 0 0 25%;
                }
    
                @media (min-width: 1366px) {
                    flex: 0 0 33.33%;
                }
    
                @media (min-width: 1280px) {
                    flex: 0 0 33.33%;
                }
    
                .wrapper {
                    background: #FFFFFF;
                    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                    border-radius: 4px;
                    padding: $gapLarger;
                    height: 100%;

                    @media (min-width: 768px) {
                        .ctaWrapper {
                            visibility: hidden
                        }
        
                        &:hover {
                            .ctaWrapper {
                                visibility: visible
                            }
                        }
                    }
                }
    
                h2 {
                    @include setFont($headline6, 24px, 500, $gapSmaller);
                    @extend .dark87;
                }
    
                p {
                    @include setFont($body2, 20px, 400, $gapLargest);
                    @extend .dark38;
                    min-height: 80px;
                    overflow-wrap: anywhere;
                }

                .count {
                    @include setFont($subtitle2, 24px, 500, 0);
                    min-height: auto;
                    @extend .dark87;
                }
    
                .attempt {
                    display: flex;
                    flex-wrap: wrap;
                    gap: $gap15;
                    margin-bottom: $gap15;
    
                    li {
                        @include setFont($body2, 20px, 400, 0);
                        @extend .dark38;
                        padding-right: $gap15;
                        position: relative;
    
                        &:first-child {
                            &::after {
                                content: "|";
                                @extend .dark38;
                                position: absolute;
                                right: 0;
                                top: 0;
                            }
                        }
    
                        &:last-child {
                            &::after {
                                content: "";
                                display: none;
                            }
                        }
                    }
                }
    
                .instructorsListWrapper {
                    font-size: $caption1;
                    @extend .dark87;
                    display: flex;
                    align-items: center;
                    margin-bottom: $gap15;
        
                    .instructorsList {
                        display: flex;
                        padding-left: $gapSmall;
                        padding-right: $gapSmall;
                        
                        li {
                            margin-left: -12px;
                        }
        
                        .instructorMapped {
                            $size: 30px;
        
                            img {
                                width: $size;
                                height: $size;
                                border-radius: 50%;
                                border: 2px solid #FFF;
                                font-size: 0;
                            }
                        }
        
                        .material-icons {
                            font-size: 24px;
                            background-color: $whiteBG;
                            border-radius: 50%;
                        }
                    }
                }
    
                .ctaWrapper {
                    
                }
            }
        }
    }

    .categoryCourses {
        .filters {
            background: #FFF;
            z-index: 6;
            padding-bottom: $gap15;

            @media (min-width: 768px) {
                position: sticky;
                top: 72px;
            }
        }

        .filtersWrapper {
            padding: 0 $gap15 $gap15;
            display: flex;
            flex-wrap: wrap;
            margin: 0 (-$gap15) 0;

            @media (min-width: 768px) {
                padding: 0 0 $gapSmaller;
            }

            &.loading {
                .b-skeleton {
                    width: auto;
                    margin: 0 0 0 $gap15;
                }
            }

            .fa-filter {
                font-size: $fontSizeLarger + 6;
                padding-right: $gap15;
            }

            .button {
                &.is-primary {
                    background-color: $whiteBG;
                    color: $primaryCopyColor;
                    font-size: $body2;
                    @extend .dark87;
                    @include setBorderColor($primaryCopyColor, 0.12);
                    font-size: $fontSizeSmall;
                    border-radius: 4px;
                    padding: 8px $gap15 9px;

                    &:active, &:focus {
                        box-shadow: none;
                    }
                }

                .icon {
                    margin: 0 0 0 $gapSmaller;
                    position: relative;
                    top: 1px;

                    .mdi {
                        &.mdi-menu-down, &.mdi-menu-up {
                            &:after {
                                content: "\e5c5";
                                @extend .material-icons;
                            }
                        }

                        &.mdi-menu-up {
                            &:after {
                                content: "\e5c7";
                            }
                        }
                    }
                }
                
            }

            .filterMenu {
                flex: 0 0 100%;
                margin-bottom: $gap15;
                margin-left: 0;

                &.category {
                    display: none;
                }

                &.active {
                    button.filter {
                        @include setBorderColor($primary, 1);
                        @include setBGColor($primary, 0.04);
                        color: $primary;

                        .icon {
                            color: $primary;
                        }
                    }
                }

                &.is-active {
                    button {
                        @include setBorderColor($primaryCopyColor, 0.87);
                    }
                }

                &.multiSelect:not(.class_days_time) {
                    .dropdown-menu {
                        padding: 0;
                    }

                    .dropdown-content {
                        padding: 0;

                        
                        a:not(.dropdownTitle) {
                            padding: 6px 15px 6px 40px;
                            position: relative;

                            &:before {
                                content: "unCheck";
                                @extend .ylIcon;
                                position: absolute;
                                left: 15px;
                                top: 10px;
                            }

                            &.is-active {
                                &:before {
                                    content: "checked";
                                }
                            }
                        }
                    }

                    .ctaWrapper {
                        display: flex;
                        justify-content: space-between;
                        margin: $gapSmall $gap15 0;
                        padding: $gap15 0;
                        border-top: 1px solid;
                        @include setBorderColor($primaryCopyColor, 0.12);

                        .yunoSecondaryCTA {
                            border-color: $primary;
                        }
                    }

                    &.class_time {
                        .dropdown-content {
                            display: flex;
                            padding: $gapSmall $gap15 $gapSmall $gapSmall;
                            flex-wrap: wrap;

                            @media (min-width: 768px) {
                                width: 440px;
                            }

                            .dropdown-item {
                                flex: 0 0 25%;
                                display: flex;
                                flex-direction: column;
                                padding: $gapSmall $gap15;
                                align-items: center;
                                @extend .dark87;
                                border: 1px solid transparent;

                                &.is-active {
                                    @include setBorderColor($primary, 1);
                                    @include setBGColor($primary, 0.04);    
                                }

                                &:before {
                                    font-family: 'Material Icons' !important;
                                    @extend .material-icons;
                                    position: static;
                                }

                                &.morning {
                                    &:before {
                                        content: "\e1c6";
                                    }
                                }

                                &.afternoon {
                                    &:before {
                                        content: "\e518";
                                    }
                                }

                                &.evening {
                                    &:before {
                                        content: "\e1c6";
                                    }
                                }

                                &.night {
                                    &:before {
                                        content: "\e51c";
                                    }
                                }

                                .itemCaption {
                                    padding: $gapSmaller 0;
                                    font-size: $subtitle1;
                                    font-weight: 500;
                                }

                                .itemLabel {
                                    @include setFont($overline, normal, 400, 0);
                                    letter-spacing: 1.5px;
                                    @extend .dark60;
                                    text-transform: uppercase;
                                }

                            }
                        }

                        .ctaWrapper {
                            flex: 0 0 100%;
                            margin: 0;
                            padding-bottom: $gapSmaller;
                        }
                    }

                    &.class_days {
                        .dropdown-content {
                            display: flex;
                            padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                            flex-wrap: wrap;

                            @media (min-width: 768px) {
                                width: 440px;
                            }

                            .dropdown-item {
                                flex: 0 0 59px;
                                padding: $gapSmall $gap15 0 0;
                                box-sizing: border-box;

                                &:hover {
                                    background: none;
                                }

                                &:before {
                                    display: none;
                                }

                                .itemLabel {
                                    display: block;
                                    background: rgba(0, 0, 0, 0.02);
                                    border-radius: 100px;
                                    @include setFont($overline, 16px, 500, 0);
                                    text-transform: uppercase;
                                    text-align: center;
                                    border: 1px solid transparent;
                                    padding: 6px $gapSmaller;

                                    &:hover {
                                        @include setBorderColor($primaryCopyColor, 0.60);
                                    }
                                }

                                &.is-active {
                                    .itemLabel {
                                        @include setBorderColor($primary, 1);
                                        @include setBGColor($primary, 0.04);    
                                        color: $primary;
                                    }
                                }
                            }
                        }

                        .ctaWrapper {
                            flex: 0 0 100%;
                            margin: $gap15 0 0;
                            padding-bottom: $gapSmaller;
                        }
                    }
                }

                &.class_days_time {
                    .ctaWrapper {
                        display: flex;
                        justify-content: space-between;
                        margin: $gapSmall $gapSmall 0;
                        padding-top: $gapSmall;
                        border-top: 1px solid;
                        @include setBorderColor($primaryCopyColor, 0.12);
                        
                        .button {
                            border-color: $primary;
                        }
                    }
                }

                .filterSet {
                    .listCaption {
                        @include setFont($subtitle2, 24px, 500, 0);
                        @extend .dark87;
                        padding: 0 $gapSmall;
                    }

                    &.class_days {
                        .wrapper {
                            display: flex;
                            padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                            flex-wrap: wrap;

                            @media (min-width: 768px) {
                                width: 440px;
                            }
                        }

                        .dropdown-item {
                            flex: 0 0 59px;
                            padding: $gapSmall $gap15 0 0;
                            box-sizing: border-box;

                            &:hover {
                                background: none;
                            }

                            &:before {
                                display: none;
                            }

                            .itemLabel {
                                display: block;
                                background: rgba(0, 0, 0, 0.02);
                                border-radius: 100px;
                                @include setFont($overline, 16px, 500, 0);
                                text-transform: uppercase;
                                text-align: center;
                                border: 1px solid transparent;
                                padding: 6px $gapSmaller;

                                &:hover {
                                    @include setBorderColor($primaryCopyColor, 0.60);
                                }
                            }

                            &.is-active {
                                .itemLabel {
                                    @include setBorderColor($primary, 1);
                                    @include setBGColor($primary, 0.04);    
                                    color: $primary;
                                }
                            }
                        }
                    }

                    &.class_time {
                        .wrapper {
                            display: flex;
                            padding: $gapSmall $gap15 $gapSmall $gapSmall;
                            flex-wrap: wrap;

                            @media (min-width: 768px) {
                                width: 440px;
                            }

                            .dropdown-item {
                                flex: 0 0 25%;
                                display: flex;
                                flex-direction: column;
                                padding: $gapSmall $gap15;
                                align-items: center;
                                @extend .dark87;
                                border: 1px solid transparent;

                                &.is-active {
                                    @include setBorderColor($primary, 1);
                                    @include setBGColor($primary, 0.04);    
                                }

                                &:before {
                                    font-family: 'Material Icons' !important;
                                    @extend .material-icons;
                                    position: static;
                                }

                                &.morning {
                                    &:before {
                                        content: "\e1c6";
                                    }
                                }

                                &.afternoon {
                                    &:before {
                                        content: "\e518";
                                    }
                                }

                                &.evening {
                                    &:before {
                                        content: "\e1c6";
                                    }
                                }

                                &.night {
                                    &:before {
                                        content: "\e51c";
                                    }
                                }

                                .itemCaption {
                                    padding: $gapSmaller 0;
                                    font-size: $subtitle1;
                                    font-weight: 500;
                                }

                                .itemLabel {
                                    @include setFont($overline, normal, 400, 0);
                                    letter-spacing: 1.5px;
                                    @extend .dark60;
                                    text-transform: uppercase;
                                }

                            }
                        }

                        .ctaWrapper {
                            border: 0;
                            margin-bottom: 0;
                            padding-bottom: 0;
                        }
                    }
                }

                .dropdown-trigger {
                    width: 100%;
                }

                @media (min-width: 768px) {
                    flex: 0 0 auto;
                    margin-bottom: 0;
                    margin: $gapSmall 0 0 $gap15; 
                }

                button {
                    width: 100%;
                    justify-content: space-between;

                    @media (min-width: 768px) {
                        width: auto;
                        justify-content: center;
                    }

                    > span {
                        text-transform: capitalize;
                    }
                }
                .dropdown-content {
                    box-shadow: none;
                    border:0;
                    max-height: 300px;
                    overflow: hidden;
                    overflow-y: auto;

                    a {
                        @include setFontColor($primaryCopyColor, 0.5);

                        &.is-active {
                            background: none;
                            @include setFontColor($primaryCopyColor, 1);
                        }

                        &:active, &:focus {
                            background: none;
                            outline: none;
                        }
                    }
                }
            }
        }

        .clearFilters {
            @extend .dark87;
            display: flex;
            align-items: flex-start;
            margin-bottom: 0;
            margin-top: 0;
            flex-wrap: wrap;

            .tags {
                margin: $gapSmall $gapSmall 0 0;
                flex-wrap: nowrap;

                .tag {
                    margin-bottom: 0;
                    border: 1px solid rgba(0, 0, 0, 0.08);
                    border-right: 0;
                    height: 28px;
                    background: rgba(0, 0, 0, 0.02);
                }

                .is-delete {
                    border-left: 0;
                    border-right: 1px solid rgba(0, 0, 0, 0.08);
                    @extend .dark87;

                    &::after {
                        display: none;
                    }

                    &::before {
                        content: "\e5cd";
                        @extend .material-icons;
                        left: 0;
                        top: 6px;
                        transform: none;
                        background: none;
                        font-size: 14px;
                    }

                    &:hover {
                        background: rgba(0, 0, 0, 0.02);
                        color: $primary;
                    }
                }

                &:not(:last-child) {

                }
            }

            .multiChips {
                display: flex;
                margin: $gapSmall $gapSmall 0 0;

                .tags:not(.placeholder) {
                    margin: 0;

                    .tag {
                        border-left: 0;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                        text-transform: capitalize;
                    }

                    .is-delete {
                        border-right: 0;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }

                    &:first-child {
                        
                    }

                    &:last-child {
                        .is-delete {
                            border-right: 1px;
                            border-top-right-radius: 290486px;
                            border-bottom-right-radius: 290486px;
                        }
                    }
                }

                .tags {
                    &.placeholder {
                        margin: 0;

                        .tag {
                            padding-right: 0;
                        }

                        .is-delete {
                            display: none;
                        }
                    }
                }
            }
        }

        .result {
            margin: 0 0 $gap15;
            position: relative;
            z-index: 5;
        }

        .listView {
            margin: $gap15 0 0;

            &.detailView {
                margin-top: $gapSmaller
            }
        }

        .coursesCount {
            @include setFont($caption2, 16px, 500, 0);
            @extend .dark60;
        }

        .back {
            display: inline-flex;
            align-items: center;
            font-size: $caption1;
            @extend .dark60;
            cursor: pointer;

            .material-icons-outlined {
                margin-right: $gapSmaller;
                font-size: 18px;
            }
        }

        .courseCard {
            background: #FFFFFF;
            box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            padding: $gapLarger;
            border: 1px solid transparent;
            margin-bottom: $gapLargest;
            cursor: pointer;

            &:hover {
                border-color: $primary
            }

            &.hideCard {
                display: none;
            }

            &.active {
                border-color: $primary;
                @include setBGColor($primary, 0.04);
            }

            .cardBlock {
                margin: $gap15 0 0;
                padding: $gapLargest 0 0;
                border-top: 1px solid rgba(0, 0, 0, 0.08);

                &.addGutter1x {
                    margin-bottom: $gap15
                }

                &.addGap1x {
                    padding: $gap15 0 0 0
                }

                h3 {
                    @include setFont($headline6, 24px, 500, $gap15)
                }

                .note {
                    @include setFont($body2, normal, 400, 0);
                    @extend .dark60;
                }
            }

            .highlighhts {
                margin: $gap15 0 0;
                padding: $gapLargest 0 0;
                border-top: 1px solid rgba(0, 0, 0, 0.08);;

                h3 {
                    @include setFont($headline6, 24px, 500, $gap15)
                }

                .listWithCheck {
                    li {
                        @include setFont($body2, 20px, 400, $gap15);
                        @extend .dark60;
                        display: flex;
                        align-items: center;

                        .material-icons-outlined {
                            margin-right: $gapSmall;
                            font-size: 16px;
                        }
                    }
                }
            }

            .cardCategory {
                @include setFont($overline, 16px, 500, $gapSmall);
                display: block;
                letter-spacing: 1.5px;
                text-transform: uppercase;
                @include setFontColor($primaryCopyColor, 0.38);
            }

            .cardTitle {
                @include setFont($headline6, 24px, 500, $gapSmall);
            }

            .cardDescription {
                @include setFont($body2, 20px, 400, $gap15);
                letter-spacing: 0.25px;
                @extend .dark60;
            }

            .cardRating {
                display: flex;

                .vue-star-rating {
                    position: relative;
                    top: -4px;
                }

                .vue-star-rating-rating-text {
                    @include setFont($caption1, 16px, 400, 0);
                    @extend .dark60;
                    margin-left: $gapSmall;

                    .rating {
                        font-size: $subtitle2;
                        font-weight: 500;
                        @extend .dark87;
                    }   
                }
            }

            .cardStats {
                display: flex;
                margin: 0 (-$gap15);
                flex-wrap: wrap;
                position: relative;
                
                // &::before {
                //     content: "";
                //     height: 1px;
                //     width: calc(100% - 48px);
                //     background-color: rgba(0, 0, 0, 0.08);
                //     left: 24px;
                //     top: 0;
                //     position: absolute;
                // }

                li {
                    display: flex;
                    align-items: center;
                    padding: 0 $gap15;
                    position: relative;
                    flex: 0 0 50%;
                    margin-bottom: $gapSmall;

                    &::after {
                        content: "";
                        position: absolute;
                        right: 0;
                        top: 0;
                        width: 1px;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.08);
                    }

                    .material-icons-outlined {
                        @extend .dark60;
                        font-size: 20px;
                    }

                    .listLabel {
                        @include setFont($subtitle2, 24px, 500, 0);
                        margin-left: $gapSmaller;

                        .dark {
                            @extend .dark60;
                            font-weight: 400; 
                        }
                    }

                    &:nth-child(2n) {
                        &::after {
                            display: none
                        }
                    }

                    @media (min-width: 768px) {
                        flex: 0 0 50%;

                        // &:nth-child(2n) {
                        //     &::after {
                        //         display: block
                        //     }
                        // }

                        // &:nth-child(3n) {
                        //     &::after {
                        //         display: none
                        //     }
                        // }
                    }
                }
            }

            .totalTime {
                @include setFont($body2, 20px, 400, $gapSmall);
                @extend .dark60;
            }

            .ctaWrapper {
                display: flex;

                .yunoPrimaryCTA {
                    margin-right: $gap15;
                }
            }

            .instructorsListWrapper {
                font-size: $caption1;
                @extend .dark87;
                display: flex;
                align-items: center;

                .instructorsList {
                    display: flex;
                    padding-left: $gapSmall;
                    padding-right: $gapSmall;
                    
                    li {
                        margin-left: -12px;
                    }

                    .instructorMapped {
                        $size: 24px;

                        img {
                            width: $size;
                            height: $size;
                            border-radius: 50%;
                            border: 2px solid #FFF;
                            font-size: 0;
                        }
                    }

                    .material-icons {
                        font-size: 24px;
                        background-color: $whiteBG;
                        border-radius: 50%;
                    }
                }
            }
        }

        .batchNotSelected {
            display: flex;
            position: sticky;
            top: 179px;

            @media (min-width: 768px) {
                padding-top: 70px;
            }

            .arrow {
                width: 122px;
                height: 183px;
                background-image: url('../../../assets/images/lineWithArrow.svg');
                background-repeat: no-repeat;
                position: relative;
                top: 58px;
                display: none;

                @media (min-width: 768px) {
                    display: block;
                }
            }

            .block {
                width: 100%;
                height: 226px;
                background-image: url('../../../assets/images/batchEmpty.svg');
                background-repeat: no-repeat;
                background-size: contain;
                position: relative;

                @media (min-width: 768px) {
                    width: 360px;
                    height: 226px;
                }

                .caption {
                    display: block;
                    background: #fefbf4;
                    position: absolute;
                    width: calc(100% - 4px);
                    bottom: 29px;
                    left: 2px;
                    text-align: center;
                    @include setFont($body2, 20px, 400, 0);
                    @extend .dark60;

                    @media (min-width: 768px) {
                        display: none;    
                    }
                }
            }
        }

        .batchesWrapper {
            // @media (min-width: 768px) {
            //     position: sticky;
            //     top: 179px;
            // }
        }

        .batchesCount {
            @include setFont($caption2, normal, 500, $gap15);
            @extend .dark60;
        }

        .batches {
            display: flex;
            flex-wrap: wrap;
            margin: 0 (-$gap15);

            .batchCard {
                flex: 0 0 100%;
                padding: 0 $gap15;
                margin-bottom: $gapLargest;
                @extend .dark87;

                @media (min-width: 768px) {
                    flex: 0 0 50%;    
                }

                .wrapper {
                    background: #FFFFFF;
                    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                    border-radius: 4px;
                    padding: $gap15;
                    border: 1px solid transparent;

                    &:hover {
                        border: 1px solid rgba(0, 0, 0, 0.12);

                        .ctaWrapper, .full {
                            visibility: visible
                        }
                    }
                }

                .batchdate {
                    @include setFont($subtitle1, 24px, 500, $gapSmall);
                    display: flex;
                    align-items: center;

                    .material-icons {
                        margin-right: $gapSmaller;
                    }
                }

                .days {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: $gapSmall;

                    li {
                        @include setFont($overline, 16px, 500, 0);
                        letter-spacing: 1.5px;
                        @include setFontColor($primaryCopyColor, 0.38);
                        text-transform: uppercase;

                        &.isActive {
                            color: $primary;
                        }
                    }
                }

                .scheduleInfo {
                    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                    margin-bottom: $gapSmall;
                    padding-bottom: $gapSmall;

                    li {
                        display: flex;
                        @extend .dark60;
                        align-items: center;
                        margin-top: $gapSmaller;

                        .material-icons-outlined {
                            font-size: 16px;
                            
                        }

                        .itemCaption {
                            @include setFont($caption2, 16px, 500, 0);
                            margin-left: $gapSmaller;
                        }

                        .hasBG {
                            @include setFont($caption2, 16px, 500, 0);
                            background-color: rgba(168, 30, 34, 0.04);
                            border-radius: 100px;
                            display: flex;
                            align-items: center;
                            padding: 4px 10px;

                            .material-icons-outlined {
                                margin-right: $gapSmaller;
                            }
                        }
                    }
                }

                .mappedInstructor {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                    margin-bottom: $gapSmall;
                    padding-bottom: $gapSmall;

                    .imgWrapper {
                        flex: 0 0 64px;
                        margin-right: $gapSmall;

                        img {
                            width: 64px;
                            height: 64px;
                            border-radius: 50%;
                            font-size: 0;
                        }
                    }

                    figcaption {
                        flex: 0 0 calc(100% - 79px);
                    }

                    .insName {
                        @include setFont($subtitle1, 20px, 500, 0);

                    }

                    .insRating {
                        display: flex;
                        align-items: center;
                        position: relative;
                        left: -3px;

                        .material-icons {
                            color: #F9B600;
                            margin-right: $gapSmaller;
                            font-size: 18px;
                        }

                        .caption {
                            @include setFont($caption2, normal, 500, 0);
                            @extend .dark60;
                        }
                    }

                    .studentCount {
                        flex: 0 0 100%;
                        @include setFont($caption1, 16px, 400, 0);
                        margin-top: $gapSmall;
                        @extend .dark60;
                    }
                }

                .cardFooter {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    align-items: center;

                    .price {
                        @include setFont($subtitle1, 20px, 500, 0);
                    }

                    .ctaWrapper {
                        @media (min-width: 768px) {
                            visibility: hidden;
                        }
                    }

                    .full {
                        flex: 0 0 100%;
                        margin-top: $gapSmaller;
                        @include setFont($caption1, 16px, 400, 0);
                        @extend .dark60;
                        visibility: hidden;
                    }
                }
            }
        }
    }

    .cardsListing {
        padding-bottom: $gapLargest * 2;
    }

    .collectionCard {
        margin-bottom: $gapLargest;

        .innerWrap {
            display: flex;
            flex-wrap: wrap;
            background: #FFFFFF;
            box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            padding: $gap15;
            @extend .dark87;
            border: 1px solid transparent;

            &:hover {
                text-decoration: none;
                border-color: $primary
            }
        }

        .cardImgWrapper {
            flex: 0 0 100%;
            background-color: $grey;

            img {
                width: auto;
                height: auto;
                font-size: 0;
                max-height: 146px;
                display: block;
                margin: 0 auto;
            }

            @media (min-width: 768px) {
                flex: 0 0 260px;
            }
        }

        .info {
            flex: 0 0 100%;
            box-sizing: border-box;
            padding: $gap15 0 0 0;

            @media (min-width: 768px) {
                flex: 0 0 calc(100% - 260px);
                padding: 0 0 0 $gap15;
            }
        }
        
        .cardTitle {
            @include setFont($headline6, 24px, 500, $gapSmall);
        }

        .cardDescription {
            @include setFont($body2, 20px, 400, $gap15);
            @extend .dark60;
        }

        .relatedResource {
            display: flex;
            flex-wrap: wrap;

            li {
                position: relative;
                padding-right: $gap15;
                margin-right: $gap15;
                flex: 0 0 50%;
                display: flex;
                align-items: center;

                &:nth-child(2n) {
                    margin-right: 0;
                    flex: 0 0 calc(50% - 15px);

                    &::after {
                        display: none;
                    }

                    @media (min-width: 768px) {
                        flex: 0 0 auto;    
                        margin-right: $gap15;
                        
                        &::after {
                            display: block;
                        }
                    }
                }

                @media (min-width: 768px) {
                    flex: 0 0 auto;    

                    &:last-child {
                        &::after {
                            display: none
                        }
                    }
                }

                &::after {
                    content: "";
                    width: 1px;
                    height: 70%;
                    position: absolute;
                    right: 0;
                    top: 15%;
                    background-color: rgba(0, 0, 0, 0.08);
                }

                .listValue {
                    @include setFont($subtitle1, 24px, 500, 0);
                    margin-right: $gapSmaller;
                }

                .listLable {
                    @include setFont($caption1, 16px, 400, 0);
                    @extend .dark60;
                }
            }
        }
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 (-$gap15);

        li {
            padding: 0 $gapSmall;

            &.noRightGap {
                padding-right: 0;
            }

            &.page {
                padding-left: 0;
            }

            &.gapRight {
                padding-right: $gap15;
            }

            &.firstLast {
                display: none;

                @media (min-width: 768px) {
                    display: block;
                }
            }
        }

        button {
            background: #FFFFFF;
            border: 1px solid rgba(0, 0, 0, 0.12);
            border-radius: 4px;
            padding: 5px 15px;
            height: 36px;
            @include setFont($body1, 24px, 400, 0);

            &.nextPrev {
                padding: 0 15px; 
                display: flex;
                align-items: center;

                .material-icons {
                    font-size: 24px;
                }
            }
            
            &.disabled {
                cursor: not-allowed;

                &.active {
                    background: $primary;
                    border-color: transparent;
                    color: #FFFFFF;
                }
            }
        }
    }

    .resultsCard {
        margin-bottom: $gapLargest;

        &:last-child {
            margin-bottom: 0;
        }

        .studentInfo {
            border-radius: 4px;
            background-color: #FFF;
            box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
            padding: $gapLarge;
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            .studentInfo_image {
                flex: 0 0 100%;
                display: flex;
                align-items: center;

                .img {
                    border-radius: 50%;
                    width: 64px;
                    height: 64px;
                    background-color: $grey;
                    margin-right: $gap15;

                    img {
                        border-radius: 50%;    
                        width: 64px;
                        height: 64px;
                    }
                }

                figcaption {
                    flex: 0 0 calc(100% - 79px);
                    line-height: normal;

                    small {
                        @include setFont($overline, normal, 400, 0);
                        letter-spacing: 1.5px;
                        border-radius: 100px;
                        display: inline-block;
                        padding: 5px 8px;
                        text-transform: uppercase;

                        &.academic {
                            @include setBGColor(#F9B600, 0.2);
                        }

                        &.general {
                            @include setBGColor(#A5A6F6, 0.2);
                        }
                    }

                    h3 {
                        margin: $gapSmaller 0 $gapSmall;
                    }

                    h4 {
                        @include setFont($overline, 4px, 400, 0);
                        @extend .dark60;
                        letter-spacing: 1.5px;
                        text-transform: uppercase;
                    }

                    a {
                        @include setFont($overline, normal, 500, 0);
                        letter-spacing: 1.5px;
                    }
                }
            }

            .studentInfo_score {
                flex: 0 0 100%;
                margin-top: $gapSmall;

                @media (min-width: 768px) {
                    margin-top: 0;
                }

                .score {
                    display: flex;
                    flex-wrap: wrap;
                    margin-top: $gapSmall;

                    li {
                        position: relative;
                        padding-right: $gap15;
                        margin-right: $gap15;
                        flex: 0 0 50%;
                        display: flex;
                        align-items: center;

                        &:nth-child(2n) {
                            margin-right: 0;
                            flex: 0 0 calc(50% - 15px);

                            &::after {
                                display: none;
                            }

                            @media (min-width: 768px) {
                                flex: 0 0 auto;    
                                margin-right: $gap15;
                                
                                &::after {
                                    display: block;
                                }
                            }
                        }

                        @media (min-width: 768px) {
                            flex: 0 0 auto;    

                            &:last-child {
                                &::after {
                                    display: none
                                }
                            }
                        }

                        &::after {
                            content: "";
                            width: 1px;
                            height: 70%;
                            position: absolute;
                            right: 0;
                            top: 15%;
                            background-color: rgba(0, 0, 0, 0.08);
                        }

                        .listValue {
                            @include setFont($subtitle1, 24px, 500, 0);
                            margin-right: $gapSmaller;
                        }

                        .listLable {
                            @include setFont($caption1, 16px, 400, 0);
                            @extend .dark60;
                        }
                    }
                }
            }

            @media (min-width: 768px) {
                .studentInfo_image {
                    flex: 0 0 60%;
                }

                .studentInfo_score {
                    flex: 0 0 40%;
                }
            }
        }
    }

    .videoSelected {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: $gapLargest;
        align-items: flex-start;

        .videoDetails {
            flex: 0 0 100%;
            padding-top: $gap15;

            @media (min-width: 768px) {
                flex: 0 0 40%;
                padding-left: $gapLargest;
                padding-top: 0;
            }

            .description {
                @include setFont($body2, 20px, 400, 0);
                @extend .dark60;
                margin-top: $gap15;
    
                &.hasShowMore {
                    height: 150px;
                    overflow: hidden;
                    -webkit-mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
                    mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
                }
    
                .media {
                    div[data-oembed-url] {
                        width: 100%;
                        margin-bottom: $gap15;
                    }
                }
    
                p {
                    margin-bottom: $gap15;
                }
    
                h2 {
                    @include setFont($headline4, auto, 500, $gap15);
                    @extend .dark87;
                }
    
                h3 {
                    @include setFont($headline5, auto, 500, $gap15);
                    @extend .dark87;
                }
    
                h4 {
                    @include setFont($headline6, auto, 500, $gap15);
                    @extend .dark87;
                }
    
                ul, ol {
                    padding: 0;
                    margin: 0 0 $gap15 18px;
    
                    li {
                        list-style: disc outside;
                        margin-bottom: $gapSmaller;
                        font-size: $fontSizeLarge;
    
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
    
                ol {
                    li {
                        list-style: decimal outside;
                    }
                }
            }
    
            .showmore {
                display: inline-flex;
                align-items: center;
                @include setFont($caption1, 20px, 500, 0);
                text-decoration: none;
    
                &:hover {
                    text-decoration: none;
                }
    
                .anchorLabel {
                    text-decoration: underline;
                }
    
                .material-icons {
                    font-size: 18px;
                }
            }
        }

        .loadingCol {
            flex: 0 0 100%;

            @media (min-width: 768px) {
                flex: 0 0 60%;
            }
        }

        .videoWrapper {
            flex: 0 0 100%;
            position: relative;
            padding-bottom: 65.7%;
            overflow: hidden;
            max-width: 100%;
            margin: -10px 0 0 0;

            @media (min-width: 768px) {
                flex: 0 0 60%;
                padding-bottom: 36.7%;
            }
    
            &.loading {
                padding-bottom: 0;    
            }
    
            iframe,
            object,
            embed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        .socialList {
            display: flex;
            align-items: center;
            flex: 0 0 100%;
            margin-top: $gap15;        
            margin-bottom: $gapLargest;

            .socialItem {
                $size: 24px;

                width: $size;
                height: $size;
                border-radius: 50%;
                background-color: $grey;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;

                .fa {
                    font-size: 14px;
                }
                
                &:hover {
                    text-decoration: none;
                }

                &.share-network-whatsapp {
                    background-color: #25D366;
                    color: white;
                }

                &.share-network-twitter {
                    background-color: #1DA1F2;
                    color: white;
                }

                &.share-network-facebook {
                    background-color: #1877F2;
                    color: white;
                }

                &.share-network-linkedin {
                    background-color: #2867B2;
                    color: white;
                }
            }
        }
    }

    
}