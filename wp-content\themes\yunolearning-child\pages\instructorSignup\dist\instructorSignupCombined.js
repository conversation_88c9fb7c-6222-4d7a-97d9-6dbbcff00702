const YUNOAvailabilityPreview = (function($) {
    const availabilityPreview = function() {
        
        Vue.component('yuno-availability-preview', {
            props: ["data", "options"],
            template: `
                <b-modal 
                    :active.sync="options.modal" 
                    :width="800" 
                    :can-cancel="['escape', 'x']"
                    class="yunoModal preview">
                        <div class="modalHeader">
                            <h2 class="modalTitle">{{options.title}}</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper noBG">
                                <ul class="legends">
                                    <li class="yes">Available</li>
                                    <li class="no">Unavailable</li>
                                </ul>
                                <yuno-table :data="instructorAvailabilityGrid" :options="tableOptions"></yuno-table>
                            </div>
                        </div>
                </b-modal>
            `,
            data() {
                return {
                    tableOptions: {
                        isFluid: false,
                        pageLoading: false,
                        apiPaginated: false,
                        totalResult: "",
                        perPage: 50,
                        // defaultSort: "sun",
                        // sortDirection: "desc",
                        limit: 20,
                        offset: 0,
                        hasStriped: false
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'timeSlots',
                    'instructorAvailabilityGrid'
                ])
            },
            async created() {
                
            },
            mounted() {
                this.fetchResources(true);
            },
            methods: {
                additionalRow(rows) {
                },
                additionalCols(cols) {
                    let slot = {
                        field: "slot",
                        label: "",
                        sortable: true,
                        hasSlot: true
                    }

                    cols.push(slot);

                    for (let i = 0; i < cols.length; i++) {
                        const col = cols[i];
                        
                        if (col.field === "slot") {
                            col.hasTag = true;
                        }

                        if (col.field === "sun") {
                            col.hasTag = true;
                        }

                        if (col.field === "mon") {
                            col.hasTag = true;
                        }

                        if (col.field === "tue") {
                            col.hasTag = true;
                        }

                        if (col.field === "wed") {
                            col.hasTag = true;
                        }

                        if (col.field === "thu") {
                            col.hasTag = true;
                        }

                        if (col.field === "fri") {
                            col.hasTag = true;
                        }

                        if (col.field === "sat") {
                            col.hasTag = true;
                        }
                    };
                },
                gotResources(options) {
                    const module = this.instructorAvailabilityGrid;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        let getData = options.response.data.data,
                            rows = getData.rows,
                            cols = getData.columns,
                            count = options.response.data.count;

                        this.additionalCols(cols);
                        this.additionalRow(rows)

                        this.tableOptions.pageLoading = false;
                        this.tableOptions.totalResult = count;
                        module.data = getData;
                    } else {
                        module.data = [];
                        this.tableOptions.totalResult = 0;
                    }
                },
                fetchResources(moduleLoading) {
                    this.instructorAvailabilityGrid.data = [];
                    this.instructorAvailabilityGrid.success = false;

                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.availabilityGridAPI(isLoggedIn),
                        module: "gotData",
                        store: "instructorAvailabilityGrid",
                        moduleLoading: moduleLoading,
                        addToModule: false,
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotResources(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
            }
        });
    };

    return {
        availabilityPreview: availabilityPreview
    };
})(jQuery);




const YUNOAvailability = (function($) {
    const availability = function() {
        
        Vue.component('yuno-availability', {
            props: ["data", "options"],
            template: `
                <div class="availabilityWrapper">
                    <template v-if="isFormLoading">
                        <div class="smallLoader"></div>
                    </template>
                    <template v-if="isFormReady">
                        <validation-observer tag="div" ref="availabilityObserver" v-slot="{ handleSubmit }">
                            <form id="formNewAddress" @submit.prevent="handleSubmit(availabilityInit)">
                                <ul class="hoursWrapper">
                                    <li class="item" v-for="(item, index) in data.data.weeks" :key="index">
                                        <div class="itemWrapper">
                                            <div class="day">{{item.day}}s</div>
                                            <div class="slots">
                                                <b-field>
                                                    <b-switch v-model="item.isDayOff"
                                                        :true-value="false"
                                                        @input="onOpen($event ? false : true, item)"
                                                        :false-value="true">
                                                        <template v-if="item.isDayOff">
                                                            Unavailable
                                                        </template>
                                                        <template v-else>
                                                            Available
                                                        </template>
                                                    </b-switch>
                                                </b-field>
                                            </div>
                                        </div>
                                        <div class="hours" v-if="!item.isDayOff"> 
                                            <template v-for="(slot, slotIndex) in item.availablity">
                                                <div class="hourWrapper" :key="slotIndex" v-if="slot.isActive">
                                                    <validation-provider 
                                                        :customMessages="{ required: message.required }" 
                                                        tag="div"
                                                        class="chooseHour" 
                                                        :rules="{required:true, isOverlapping: slot.isOverlapping}" 
                                                        v-slot="{ errors, classes }"
                                                    >
                                                        <div :class="{ hasError: errors && errors.length > 0 }">
                                                            <b-dropdown
                                                                :class="classes"
                                                                :key="'start-' + slotIndex"
                                                                v-model="slot.startsAt"
                                                                aria-role="list"
                                                                class="filterMenu"
                                                            >
                                                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                                                    <span><template v-if="!item.is24Hours">Start</template> {{slot.startsAt}}</span>
                                                                    <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                                                </button>
                                                                <template v-for="(time, timeIndex) in timeSlots.data">
                                                                    <b-dropdown-item 
                                                                        @click="onFilterItemSelect(slot, time, 'start', item)"
                                                                        :value="time.label"
                                                                        :key="timeIndex"
                                                                        aria-role="listitem">
                                                                        <span>{{time.label}}</span>
                                                                    </b-dropdown-item>
                                                                </template>
                                                            </b-dropdown>
                                                            <p class="error" v-if="false">{{errors[0]}}</p>
                                                        </div>
                                                    </validation-provider>
                                                    <validation-provider 
                                                        :customMessages="{ required: message.required, is_not: message.isNot,  greaterThen: message.greaterThen}" 
                                                        tag="div" 
                                                        v-if="slot.isEnds"
                                                        class="chooseHour"
                                                        :rules="{required:true, is_not: slot.startsAt, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" 
                                                        v-slot="{ errors, classes }">
                                                        <div :class="{ hasError: errors && errors.length > 0 }">
                                                            <b-dropdown
                                                                :class="classes"
                                                                v-if="slot.isEnds"
                                                                :key="'end-' + slotIndex"
                                                                v-model="slot.endsAt"
                                                                aria-role="list"
                                                                class="filterMenu"
                                                            >
                                                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                                                    <span>End {{slot.endsAt}}</span>
                                                                    <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                                                </button>
                                                                <template v-for="(time, timeIndex) in timeSlots.data">
                                                                    <b-dropdown-item 
                                                                        @click="onFilterItemSelect(slot, time, 'end', item)"
                                                                        :value="time.label"
                                                                        :key="timeIndex"
                                                                        aria-role="listitem">
                                                                        <span>{{time.label}}</span>
                                                                    </b-dropdown-item>
                                                                </template>
                                                            </b-dropdown>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </div>
                                                    </validation-provider>
                                                    <b-button 
                                                        @click="removeSlot(item, slot)" 
                                                        class="yunoPrimaryCTA iconOnly removeSlot noBG">
                                                        <span class="material-icons-outlined">close</span>
                                                    </b-button>
                                                    <b-tooltip label="Copy time to all"
                                                        type="is-dark"
                                                        position="is-top">
                                                        <b-button 
                                                            @click="copySlot(item, slot)" 
                                                            v-if="slotIndex === 0"
                                                            class="yunoPrimaryCTA iconOnly copySlot noBG">
                                                            <span class="material-icons-outlined">content_copy</span>
                                                        </b-button>    
                                                    </b-tooltip>
                                                </div>
                                                <template v-if="slot.isEnds && slotIndex === item.availablity.length - 1">
                                                    <div class="addSlotWrapper">
                                                        <b-button 
                                                            v-if="slotIndex === item.availablity.length - 1"
                                                            @click="addSlot(item)" 
                                                            class="yunoPrimaryCTA addSlot noBG">
                                                            Add Hours
                                                        </b-button>
                                                    </div>
                                                </template>
                                            </template>
                                        </div>
                                    </li>
                                </ul>
                                <div class="ctaWrapper">
                                    <b-button
                                        native-type="submit"
                                        :loading="isLoading ? true : false"
                                        :disabled="isLoading ? true : false"
                                        class="yunoSecondaryCTA">
                                        Save
                                    </b-button>    
                                    <b-button
                                        v-if="typeof options.isPreview === 'undefined' || options.isPreview"
                                        @click="initPreview()"
                                        :disabled="data.data.hasRecord && preview.isMatched ? false : true" 
                                        class="yunoSecondaryCTA wired">
                                        Preview
                                    </b-button>    
                                </div>
                            </form>
                        </validation-observer>
                        <yuno-availability-preview v-if="preview.modal" :data="data.data" :options="preview"></yuno-availability-preview>
                    </template>
                </div>
            `,
            data() {
                return {
                    isLoading: false,
                    message: {
                        required: "Required",
                        isNot: "Value should not be same as start time",
                        error: ""
                    },
                    slot: {
                        id: "0",
                        startsAt: "",
                        endsAt: "",
                        start: "",
                        end: "",
                        isActive: true,
                        slotID: "",
                        isEnds: true,
                        isExist: false,
                        isOverlapping: false,
                        isEndTime: false
                    },
                    weeks: [],
                    preview: {
                        modal: false,
                        title: "Availability",
                        oldData: "",
                        isMatched: true
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'userProfile',
                    'userRole',
                    'timeSlots',
                    'instructorAvailability',
                    'instructorAvailabilityGrid',
                    'settings',
                    'resources'
                ]),
                isFormLoading: {
                    get() {
                        const module =  this.$props.data.loading 
                                        || this.timeSlots.loading 
                        return module
                    }
                },
                isFormReady: {
                    get() {
                        let module =  this.$props.data.success 
                                        && this.timeSlots.success;

                        if (module) {
                            this.initFormReady()
                        }
                        
                        return module
                    }
                },
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                timeToMinutes(timeStr) {
                    let [hours, minutes] = timeStr.split(':').map(Number)
                    if (hours === 24) hours = 0
                    return hours * 60 + minutes
                },
                manageEndTime(slot) {
                    const startMinutes = this.timeToMinutes(slot.start);
                    const endMinutes = this.timeToMinutes(slot.end);
                    // If end time is not later than start time, mark as invalid (set isEndTime true)
                    if (endMinutes <= startMinutes) {
                        slot.isEndTime = true;
                    } else {
                        slot.isEndTime = false;
                    };
                },
                manageOverlappingSlot(slot, availablity) {
                    // Convert current slot times
                    const currentStart = this.timeToMinutes(slot.start);
                    const currentEnd = this.timeToMinutes(slot.end);
                    // Reset overlapping flag
                    slot.isOverlapping = false;
                    // Loop over all existing slots in availablity array
                    availablity.availablity.forEach(existingSlot => {
                        // Skip self-check if IDs are the same
                        if(existingSlot.slotID === slot.slotID) return;
                        const existStart = this.timeToMinutes(existingSlot.start);
                        const existEnd = this.timeToMinutes(existingSlot.end);
                        // Basic overlapping: [A,B] overlaps if A < D && C < B
                        if(currentStart < existEnd && existStart < currentEnd) {
                            slot.isOverlapping = true;
                        }
                    });
                },
                copySlot(item, slot) {
                    const isSignup = this.$props.options.isSignup;
                    let tab = "";

                    if (isSignup) {
                        tab = this.instructorAvailabilityGrid.data;
                    } else {
                        tab = this.resources.data;
                    }

                    

                    const weeks = tab.weeks;

                    weeks.forEach(day => {
                        if (!day.isDayOff) {
                            const copiedAvailablity = JSON.parse(JSON.stringify(item.availablity));
                            const currentAvailablity = day.availablity;

                            currentAvailablity.forEach(element => {
                                element.isActive = false;
                            });

                            copiedAvailablity.forEach(element => {
                                element.id = "0"; 
                            });

                            day.availablity = copiedAvailablity;
                        }
                    });
                },
                initFormReady() {
                    if (this.preview.oldData === "") {
                        this.preview.oldData = JSON.stringify(this.$props.data.data);    
                    }

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                initPreview() {
                    this.preview.modal = true;
                },
                availabilityDone(options) {
                    this.isLoading = false;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${options.response.data.message}`,
                            position: 'is-bottom'
                        });
                        
                        if (typeof this.options.isPreview === 'undefined' || this.options.isPreview) {
                            this.weeks = [];
                            this.preview.oldData = "";
                            Event.$emit('initAvailability', this.$props.options.tab, this.$props.options.tabIndex);    
                        };

                    } else {
                        this.$buefy.dialog.alert({
                            title: "Availability",
                            message: `${options.response.data.message}`,
                            confirmText: 'Ok'
                        })
                    }
                },
                availabilityInit() {
                    this.isLoading = true;

                    const payload = JSON.parse(JSON.stringify(this.$props.data.data)),
                        weeks = payload.weeks,
                        existingWeeks = this.weeks,
                        instance = this;

                    for (let i = 0; i < weeks.length; i++) {
                        const day = weeks[i];
                        
                        if (existingWeeks.length !== 0) {
                            for (let j = 0; j < existingWeeks.length; j++) {
                                const existing = existingWeeks[j];
                                
                                if (day.day === existing.day) {
                                    const slots = existing.availablity;

                                    for (let k = 0; k < slots.length; k++) {
                                        const slot = slots[k];
                                        day.availablity.push(slot)    
                                    }
                                }
                            }
                        };
                    }

                    let url = "";

                    if (payload.hasRecord) {
                        url = YUNOCommon.config.createUpdateAvailabilityAPI("update")
                    } else {
                        url = YUNOCommon.config.createUpdateAvailabilityAPI("create")
                    }

                    const options = {
                        apiURL: url,
                        module: "gotData",
                        store: "instructorAvailability",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.availabilityDone(options)
                        }
                    };

                    if (payload.hasRecord) {
                        this.$store.dispatch('putData', options);
                    } else {
                        this.$store.dispatch('postData', options);
                    };
                },
                onFilterItemSelect(slot, data, type, item) {
                    slot[type] = data.slug;

                    if (data.slug !== "24") {
                        slot.isEnds = true;
                        item.is24Hours = false;
                    } else {
                        item.availablity = [];
                        item.availablity.push(slot)
                        item.is24Hours = true;
                        slot.isEnds = false;
                    };

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                    this.manageOverlappingSlot(slot, item);
                    this.manageEndTime(slot);
                },
                removeSlot(item, slot) {
                    const availablity = item.availablity;

                    if (slot.isExist) {
                        let dayObj = {
                            day: item.day,
                            availablity: []
                        }
                        
                        slot.isActive = false;
                        dayObj.availablity.push(slot)

                        this.weeks.push(dayObj)
                    };

                    YUNOCommon.removeObjInArr(availablity, "slotID", slot.slotID);

                    for (let i = 0; i < availablity.length; i++) {
                        const element = availablity[i];
                        element.slotID = i;
                    };

                    if (availablity.length === 0) {
                        item.isDayOff = true;
                    }

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                addSlot(item) {
                    let totalItem = item.availablity.length === 1 ? 0 : item.availablity.length - 1,
                        availablity = item.availablity; 
                    const slotObj = JSON.parse(JSON.stringify(this.slot));

                    
                    slotObj.slotID = totalItem + 1;
                    item.availablity.push(slotObj);
                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                enablePreview(data) {
                    if (data !== this.preview.oldData) {
                        this.preview.isMatched = false;
                    } else {
                        this.preview.isMatched = true;
                    }
                },
                onOpen(value, day) {
                    const slotObj = JSON.parse(JSON.stringify(this.slot));

                    slotObj.slotID = 0;

                    if (value) {
                        if (day.availablity.length === 0) {
                            day.availablity.push(slotObj);
                            this.enablePreview(JSON.stringify(this.$props.data.data));
                        }
                    } else {
                        day.availablity = [];    
                        this.enablePreview(JSON.stringify(this.$props.data.data));
                    }
                }
            }
        });
    };

    return {
        availability: availability
    };
})(jQuery);





const YUNOInstructorSignup = (function($) {

    const instructorSignup = function() {
        const validationMsg = {
            "messages": {
                "required": "This field is required",
                "numeric": "Numbers only",
                "min": "Minium 10 numbers required",
                "max": "Maxium 10 numbers required",
                "is": "Required",
                "is_not": "New batch shouldn't be same as current batch"
            }
        };
        
        YUNOCommon.assignVValidationObj(validationMsg);
        YUNOAvailability.availability();
        YUNOAvailabilityPreview.availabilityPreview();

        Vue.component('yuno-instructor-sign-up', {
            template: `
                <div>
                    <yuno-page-loader v-if="isPageLoading"></yuno-page-loader>
                    <div v-show="isPageReady">
                        <yuno-header-revamp :hasnav="false"></yuno-header-revamp>
                        <main id="yunoMain">
                            <template v-if="isUserAuthorized">
                                <template v-if="isFormLoading">
                                    <div class="smallLoader"></div>
                                </template>
                                <template v-if="isFormReady">
                                    <section id="instructorSteps" class="instructorSteps">
                                        <div class="container">
                                            <b-steps v-model="steps.active"
                                                :has-navigation="false"
                                                icon-prev="chevron-left"
                                                icon-next="chevron-right"
                                                icon-pack="fa">
                                                <template v-for="(step, i) in steps.items">
                                                    <b-step-item
                                                        :clickable="false"
                                                        :key="i"
                                                        :label="step.label">
                                                        <template v-if="step.slug === 'one'">
                                                            <section class="aboutYou">
                                                                <h1 class="sectionTitle">
                                                                    Tell us about yourself
                                                                </h1>
                                                                <validation-observer 
                                                                    tag="div" 
                                                                    ref="aboutYouObserver" 
                                                                    v-slot="{ handleSubmit, invalid }">
                                                                    <form id="aboutYouForm" @submit.prevent="handleSubmit(initAboutYou(1))">
                                                                        <div class="row isRelative">
                                                                            <div class="col-md-8">
                                                                                <div class="formWrapper">
                                                                                    <b-field label="First Name">
                                                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <b-input
                                                                                                v-model="step.payload.first_name" 
                                                                                                :class="classes">
                                                                                            </b-input>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </b-field>
                                                                                    <b-field label="Last Name">
                                                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <b-input 
                                                                                                v-model="step.payload.last_name" 
                                                                                                :class="classes">
                                                                                            </b-input>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </b-field>
                                                                                    <b-field label="Email">
                                                                                        <validation-provider :rules="{required:false}" v-slot="{ errors, classes }">
                                                                                            <b-input 
                                                                                                disabled
                                                                                                :readonly="true"
                                                                                                v-model="step.payload.email" 
                                                                                                :class="classes">
                                                                                            </b-input>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </b-field>
                                                                                    <b-field label="Country" :class="{'fieldLoading': countries.loading}">
                                                                                        <template v-if="countries.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="countries.success">
                                                                                            <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <b-select 
                                                                                                    :class="classes"
                                                                                                    v-model="step.payload.country"
                                                                                                    @input="onCountryChange($event, step)"
                                                                                                    placeholder="Choose your country">
                                                                                                    <option value="">Choose your country</option>
                                                                                                    <template v-for="(country, countryIndex) in countries.data">
                                                                                                        <option :key="countryIndex" :value="country.id">{{country.name}}</option>
                                                                                                    </template>
                                                                                                </b-select>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </b-field>
                                                                                    <b-field label="State" :class="{'fieldLoading': states.loading}" v-if="step.statesList.isActive">
                                                                                        <template v-if="states.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="states.success">
                                                                                            <validation-provider v-if="step.statesList.isField" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <b-select 
                                                                                                    :class="classes"
                                                                                                    @input="onStateChange($event, step)"
                                                                                                    v-model="step.payload.state"
                                                                                                    placeholder="Choose your state">
                                                                                                    <option value="">Choose your state</option>
                                                                                                    <template v-for="(state, stateIndex) in states.data">
                                                                                                        <option :key="stateIndex" :value="state.id">{{state.name}}</option>
                                                                                                    </template>
                                                                                                </b-select>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </b-field>
                                                                                    <b-field label="City" :class="{'fieldLoading': cities.loading}" v-if="step.citiesList.isActive">
                                                                                        <template v-if="cities.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="cities.success">
                                                                                            <validation-provider v-if="step.citiesList.isField" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <b-select 
                                                                                                    :class="classes"
                                                                                                    v-model="step.payload.city"
                                                                                                    placeholder="Choose your city">
                                                                                                    <option value="">Choose your city</option>
                                                                                                    <template v-for="(city, cityIndex) in cities.data">
                                                                                                        <option :key="cityIndex" :value="city.id">{{city.name}}</option>
                                                                                                    </template>
                                                                                                </b-select>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </b-field>
                                                                                    <b-field label="Phone Number">
                                                                                        <validation-provider :rules="{required:true, numeric:true, min:10, max:10, notAllowed:0}" v-slot="{ errors, classes }">
                                                                                            <b-input 
                                                                                                v-model="step.payload.phone" 
                                                                                                :class="classes">
                                                                                            </b-input>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </b-field>
                                                                                    <div class="field">
                                                                                        <b-checkbox class="isWhatsapp"
                                                                                            native-value="step.payload.yuno_user_whatsapp_check"
                                                                                            v-model="step.payload.yuno_user_whatsapp_check">
                                                                                            Receive important notifications on <span class="green">Whatsapp <i class="fa fa-whatsapp" aria-hidden="true"></i></span>
                                                                                        </b-checkbox>
                                                                                    </div>
                                                                                    <div class="field" :class="{'fieldLoading': languages.loading}">
                                                                                        <label class="label">
                                                                                            Native Language
                                                                                            <b-tooltip label="Select your native language"
                                                                                                type="is-dark"
                                                                                                position="is-top">
                                                                                                <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                                                            </b-tooltip>
                                                                                        </label>
                                                                                        <template v-if="languages.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="languages.success">
                                                                                            <validation-provider :rules="{required:true, isNotBlank:nativeLanguage.selected}" v-slot="{ errors, classes }">
                                                                                                <b-autocomplete
                                                                                                    v-model="nativeLanguage.current"
                                                                                                    :data="filteredAutocomplete(nativeLanguage, 'name', languages.data)"
                                                                                                    placeholder="Add a language"
                                                                                                    autocomplete="nativeLang"
                                                                                                    field="name"
                                                                                                    @select="onNativeSelect($event, step)"
                                                                                                    :clearable="true">
                                                                                                    <template slot="empty">No results for {{nativeLanguage.current}}</template>
                                                                                                    <template slot-scope="props">
                                                                                                        {{props.option.name}}
                                                                                                    </template>
                                                                                                </b-autocomplete> 
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </div>
                                                                                    <div class="field" :class="{'fieldLoading': languages.loading}">
                                                                                        <label class="label">
                                                                                            Enter the languages you understand
                                                                                            <b-tooltip label="Enter the languages which you are understood"
                                                                                                type="is-dark"
                                                                                                position="is-top">
                                                                                                <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                                                            </b-tooltip>
                                                                                        </label>
                                                                                        <template v-if="languages.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="languages.success">
                                                                                            <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <b-taginput
                                                                                                    :class="classes"
                                                                                                    v-model="understandSelected"
                                                                                                    :data="filteredLanguages"
                                                                                                    autocomplete
                                                                                                    field="name"
                                                                                                    icon="label"
                                                                                                    placeholder="Add a language"
                                                                                                    @add="onUnderstandSelect($event, 'add', step)"
                                                                                                    @remove="onUnderstandSelect($event, 'remove', step)"
                                                                                                    @typing="getFilteredLanguages">
                                                                                                    <template v-slot="props">
                                                                                                        {{props.option.name}}
                                                                                                    </template>
                                                                                                    <template #empty>
                                                                                                        There are no items
                                                                                                    </template>
                                                                                                </b-taginput>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </div>
                                                                                    <div class="field" :class="{'fieldLoading': languages.loading}">
                                                                                        <label class="label">
                                                                                            Enter the languages you’re fluent in
                                                                                            <b-tooltip label="Enter the languages which you are fluent in"
                                                                                                type="is-dark"
                                                                                                position="is-top">
                                                                                                <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                                                            </b-tooltip>
                                                                                        </label>
                                                                                        <template v-if="languages.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="languages.success">
                                                                                            <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <b-taginput
                                                                                                    :class="classes"
                                                                                                    v-model="fluentInSelected"
                                                                                                    :data="filteredLanguages"
                                                                                                    autocomplete
                                                                                                    field="name"
                                                                                                    icon="label"
                                                                                                    placeholder="Add a language"
                                                                                                    @add="onFluentInSelect($event, 'add', step)"
                                                                                                    @remove="onFluentInSelect($event, 'remove', step)"
                                                                                                    @typing="getFilteredLanguages">
                                                                                                    <template v-slot="props">
                                                                                                        {{props.option.name}}
                                                                                                    </template>
                                                                                                    <template #empty>
                                                                                                        There are no items
                                                                                                    </template>
                                                                                                </b-taginput>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </div>
                                                                                    <div class="fieldsGroup noBorder gapBtm15">
                                                                                        <h3 class="singleFieldTitle">Do you have a laptop or desktop in good condition?</h3>
                                                                                        <validation-provider class="vpWrapper radioWrapper" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <template v-for="(option, i) in hasLaptop">
                                                                                                <div class="field colorGrey" :key="i">
                                                                                                    <b-radio 
                                                                                                        v-model="step.payload.laptop_availability" 
                                                                                                        :native-value="option.slug"
                                                                                                        :class="classes" 
                                                                                                        name="hasLaptop">
                                                                                                        {{option.label}}
                                                                                                    </b-radio>
                                                                                                </div>
                                                                                            </template>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </div>
                                                                                    <div class="fieldsGroup noBorder gapBtm15">
                                                                                        <h3 class="singleFieldTitle">Do you have access to a broadband connection?</h3>
                                                                                        <validation-provider class="vpWrapper radioWrapper" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <template v-for="(option, i) in hasBroadband">
                                                                                                <div class="field colorGrey" :key="i">
                                                                                                    <b-radio 
                                                                                                        v-model="step.payload.broadband_connection_availability" 
                                                                                                        :native-value="option.slug"
                                                                                                        :class="classes" 
                                                                                                        name="hasBroadband">
                                                                                                        {{option.label}}
                                                                                                    </b-radio>
                                                                                                </div>
                                                                                            </template>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </div>
                                                                                    <div class="fieldsGroup noBorder gapBtm15">
                                                                                        <h3 class="singleFieldTitle">Do you have experience teaching online?</h3>
                                                                                        <validation-provider class="vpWrapper radioWrapper" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <template v-for="(option, i) in hasExperience">
                                                                                                <div class="field colorGrey" :key="i">
                                                                                                    <b-radio 
                                                                                                        v-model="step.payload.online_teaching_exp" 
                                                                                                        :native-value="option.slug"
                                                                                                        :class="classes" 
                                                                                                        name="hasExperience">
                                                                                                        {{option.label}}
                                                                                                    </b-radio>
                                                                                                </div>
                                                                                            </template>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </div>
                                                                                    <b-field label="Your birthday">
                                                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <b-datepicker
                                                                                                :class="classes"
                                                                                                v-model="step.payload.dob"
                                                                                                :date-formatter="formatDate"
                                                                                                placeholder="Pick date"
                                                                                                :mobile-native="false"
                                                                                                trap-focus>
                                                                                            </b-datepicker>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </b-field>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-4">
                                                                                <figure class="userImg">
                                                                                    <img :src="step.profileImg" :alt="step.first_name">
                                                                                </figure>
                                                                                <p class="userImgHelper">You can change your image in your Google account</p>
                                                                            </div>
                                                                        </div>
                                                                        <div class="ctaWrapper">
                                                                            <b-button
                                                                                :loading="step.isLoading ? true : false"
                                                                                :disabled="invalid"
                                                                                native-type="submit"
                                                                                class="yunoPrimaryCTA big">
                                                                                Next
                                                                            </b-button>
                                                                        </div>
                                                                    </form>
                                                                </validation-observer>
                                                            </section>
                                                        </template>
                                                        <template v-if="step.slug === 'two'">
                                                            <section class="whatYouTeach">
                                                                <h2 class="sectionTitle">{{step.label}}</h2>
                                                                <validation-observer 
                                                                    tag="div" 
                                                                    ref="whatYouTeachObserver" 
                                                                    v-slot="{ handleSubmit, invalid }">
                                                                    <form id="whatYouTeachForm" @submit.prevent="handleSubmit(initwhatYouTeach(2))">
                                                                        <div class="row isRelative">
                                                                            <div class="col-12 col-md-12">
                                                                                <div class="formWrapper">
                                                                                    <b-field :class="{'fieldLoading': subjectsList.loading}" label="What subjects do you teach?">
                                                                                        <template v-if="subjectsList.loading">
                                                                                            <div class="smallLoader withField"></div>
                                                                                        </template>
                                                                                        <template v-if="subjectsList.success">
                                                                                            <validation-provider tag="div" class="checkboxTag" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                                <template v-for="(item, i) in subjectsList.data">
                                                                                                    <b-checkbox-button v-model="step.payload.can_teach"
                                                                                                        :key="i"
                                                                                                        :native-value="item.name">
                                                                                                        <span>{{item.name}}</span>
                                                                                                    </b-checkbox-button>
                                                                                                </template>
                                                                                                <p class="error">{{errors[0]}}</p>
                                                                                            </validation-provider>
                                                                                        </template>
                                                                                    </b-field>
                                                                                    <div class="fieldsGroup noBorder">
                                                                                        <h3 class="singleFieldTitle hasHelper">How do you prefer to teach?</h3>
                                                                                        <small class="helper">Choose at least one</small>
                                                                                        <validation-provider tag="div" class="vpWrapper checkboxWrapper" :rules="{required:true}" v-slot="{ errors, classes }">
                                                                                            <template v-for="(option, i) in teachPrefer">
                                                                                                <div class="field colorGrey" :key="i">
                                                                                                <b-checkbox
                                                                                                    :class="classes"
                                                                                                    name="teachPrefer"
                                                                                                    :native-value="option.slug"
                                                                                                    v-model="step.payload.teaching_preference">
                                                                                                    {{option.label}}
                                                                                                </b-checkbox>
                                                                                                </div>
                                                                                            </template>
                                                                                            <p class="error">{{errors[0]}}</p>
                                                                                        </validation-provider>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="ctaWrapper">
                                                                            <b-button
                                                                                :loading="step.isLoading ? true : false"
                                                                                :disabled="invalid"
                                                                                native-type="submit"
                                                                                class="yunoPrimaryCTA big">
                                                                                Next
                                                                            </b-button>
                                                                        </div>
                                                                    </form>
                                                                </validation-observer>
                                                            </section>
                                                        </template>
                                                        <template v-if="step.slug === 'three'">
                                                            <section class="availability">
                                                                <h2 class="sectionTitle">{{step.label}}</h2>
                                                                <yuno-availability :data="instructorAvailabilityGrid" :options="{isPreview: false, isSignup: true}"></yuno-availability>
                                                                <div class="ctaWrapper" v-if="instructorAvailabilityGrid.success && timeSlots.success">
                                                                    <b-button
                                                                        :loading="step.isLoading ? true : false"
                                                                        @click="finishSignup()"
                                                                        class="yunoPrimaryCTA big">
                                                                        Finish
                                                                    </b-button>
                                                                </div>
                                                            </section>
                                                        </template>
                                                    </b-step-item>
                                                </template>
                                            </b-steps>
                                        </div>
                                    </section>
                                </template>
                            </template>
                            <template v-else>
                                <div class="container">
                                    <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                                </div>
                            </template> 
                        </main>
                        <yuno-footer :isnav="false"></yuno-footer>
                    </div>
                </div>
            `,
            data() {
                return {
                    filteredLanguages: [],
                    fluentInSelected: [],
                    understandSelected: [],
                    nativeLanguage: {
                        selected: null,
                        current: "",
                        data: []
                    },
                    hasLaptop: [
                        {
                            label: "Yes",
                            slug: "yes"
                        },
                        {
                            label: "No",
                            slug: "no"
                        }
                    ],
                    hasBroadband: [
                        {
                            label: "Yes",
                            slug: "yes"
                        },
                        {
                            label: "No",
                            slug: "no"
                        },
                        {
                            label: "Don't know",
                            slug: "do not know"
                        }
                    ],
                    hasExperience: [
                        {
                            label: "Yes, more than 2 years",
                            slug: "yes more than 2 years"
                        },
                        {
                            label: "Yes, less than 2 years",
                            slug: "yes less than 2 years"
                        },
                        {
                            label: "No, I don’t have any experience teaching online",
                            slug: "no i do not have any experience teaching online"
                        }
                    ],
                    teachPrefer: [
                        {
                            label: "Online",
                            slug: "online"
                        },
                        {
                            label: "In-person",
                            slug: "in-person"
                        }
                    ],
                    subjects: [
                        "Subject-1",
                        "Subject-2",
                        "Subject-3",
                        "Subject-4",
                        "Subject-5",
                    ],
                    steps: {
                        active: 0,
                        items: [
                            {
                                label: 'About you',
                                slug: 'one',
                                isLoading: false,
                                profileImg: "",
                                statesList: {
                                    isActive: false,
                                    isField: false,
                                },
                                citiesList: {
                                    isActive: false,
                                    isField: false,
                                },
                                payload: {
                                    "first_name": "",
                                    "last_name": "",
                                    "email": "",
                                    "country": "",
                                    "state": "",
                                    "city": "",
                                    "phone": "",
                                    "yuno_user_whatsapp_check": true,
                                    "understand": [],
                                    "fluent_in": [],
                                    "native_language": "",
                                    "laptop_availability": "",
                                    "broadband_connection_availability": "",
                                    "online_teaching_exp": "",
                                    "dob": null
                                }
                            },
                            {
                                label: 'What you teach',
                                slug: 'two',
                                isLoading: false,
                                payload: {
                                    "can_teach": [],
                                    "teaching_preference": []
                                }
                            },
                            {
                                label: 'Set your availablity',
                                slug: 'three',
                                isLoading: false
                            }
                        ]
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'header',
                    'userProfile',
                    'userRole',
                    'footer',
                    'countries',
                    'states',
                    'cities',
                    'languages',
                    'instructor',
                    'moduleWithoutTab',
                    'subjectsList',
                    'instructorAvailabilityGrid',
                    'timeSlots',
                    'form'
                ]),
                emptyStates() {
                    return {
                        state: "notAuthorized"
                    }
                },
                isUserAuthorized: {
                    get() {
                        let module = "";

                        if (this.user.isLoggedin && this.userInfo.data.is_signup_completed !== 'completed') {
                            module = true;
                        } else {
                            module = false;
                        }

                        return module
                    }
                },
                isPageLoading: {
                    get() {
                        const module = this.userInfo.loading;
                        
                        return module
                    }
                },
                isPageReady: {
                    get() {
                        let module = "";

                        if (this.user.isLoggedin) {
                            module = this.userInfo.success;
                        } else {
                            module = true;
                                        
                        }

                        return module
                    }
                },
                isFormReady: {
                    get() {
                        let module = this.instructor.success;

                        return module
                    }
                },
                isFormLoading: {
                    get() {
                        let module = this.instructor.loading;

                        return module
                    }
                },
            },
            async created() {
                this.emitEvents();
            },
            mounted() {
                
            },
            methods: {
                formatDate(d) {
                    return moment(d).format('MMMM DD, YYYY');
                },
                manageArray(array, item, payload) {
                    let isFound = YUNOCommon.hasInArray(array, item);

                    if (isFound) {
                        YUNOCommon.removeValInArr(array, item);
                    } else {
                        payload.push(item)
                    }
                },
                onFluentInSelect(data, state, step) {
                    let payload = step.payload;

                    if (state === "add") {
                        this.manageArray(
                            this.fluentInSelected, 
                            data.name,
                            payload.fluent_in
                        )    
                    } else {
                        let item = data.name !== undefined ? data.name : data;

                        YUNOCommon.removeValInArr(this.fluentInSelected, item);
                        YUNOCommon.removeValInArr(payload.fluent_in, item);
                    }
                },
                filteredAutocomplete(data, key, dump) {
                    data.data = dump;

                    return data.data.filter(option => {
                        return (
                            option[key].toString().toLowerCase().indexOf(data.current.toLowerCase()) >= 0
                        )
                    })
                },
                onNativeSelect(data, step) {
                    let payload = step.payload;

                    if (data !== null) {
                        payload.native_language = data.name;
                        this.nativeLanguage.selected = data;
                    } else {
                        payload.native_language = ""
                        this.nativeLanguage.selected = null;
                    }
                },
                onUnderstandSelect(data, state, step) {
                    let payload = step.payload;

                    if (state === "add") {
                        this.manageArray(
                            this.understandSelected, 
                            data.name,
                            payload.understand
                        )    
                    } else {
                        let item = data.name !== undefined ? data.name : data;

                        YUNOCommon.removeValInArr(this.understandSelected, item);
                        YUNOCommon.removeValInArr(payload.understand, item);
                    }
                },
                getFilteredLanguages(text) {
                    this.filteredLanguages = this.languages.data.filter((option) => {
                        return option.name
                            .toString()
                            .toLowerCase()
                            .indexOf(text.toLowerCase()) >= 0
                    })
                },
                refineTimeSlot(options, module) {
                    const getWeeks = module.data.weeks;

                    for (let i = 0; i < getWeeks.length; i++) {
                        const day = getWeeks[i];

                        if (day.is24Hours) {
                            const obj = {
                                id: "0",
                                startsAt: "24 hours",
                                endsAt: "",
                                start: "24",
                                end: "",
                                isActive: true,
                                slotID: 0,
                                isEnds: false,
                                isExist: false
                            };

                            day.availablity.push(obj);
                        } else {
                            if (day.availablity.length !== 0) {
                                const slots = day.availablity;
                                for (let j = 0; j < slots.length; j++) {
                                    const slot = slots[j],
                                        getStart = YUNOCommon.findObjectByKey(this.timeSlots.data, "slug", slot.start),
                                        getEnd = YUNOCommon.findObjectByKey(this.timeSlots.data, "slug", slot.end);
                                    
                                    slot.startsAt = getStart.label;
                                    slot.endsAt = getEnd.label;
                                    slot.isEnds = true;
                                    slot.isExist = true;
                                }
                            }
                        };
                    };
                },
                gotTimeSlots(options, module, availabilityData, isUpdated) {
                    if (isUpdated) {
                        this.refineTimeSlot(options, module);
                    };

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        this.refineTimeSlot(options, module);
                    };
                },
                fetchTimeSlots(module, availabilityData) {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.timeSlotsAPI(),
                        module: "gotData",
                        store: "timeSlots",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotTimeSlots(options, module, availabilityData, false)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                gotAvailability(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        const data = options.response.data.data;

                        this.fetchTimeSlots(this.instructorAvailabilityGrid, data);
                    };
                },
                fetchAvailability() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.instructorAvailabilityAPI(isLoggedIn),
                        module: "gotData",
                        store: "instructorAvailabilityGrid",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotAvailability(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                fetchSubjects() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.subjectsListAPI(),
                        module: "gotData",
                        store: "subjectsList",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
                gotInstructor(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        const data = options.response.data.data,
                            steps = data.steps;

                        data.criteria = "instructor";
                        data.user_id = isLoggedIn;

                        for (let i = 0; i < steps.length; i++) {
                            const step = steps[i];
                            
                            if (!step.is_step_completed) {
                                this.steps.active = i;
                                break;
                            }
                        };
                    }
                },
                fetchInstructor() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.instructorProfileAPI(isLoggedIn),
                        module: "gotData",
                        store: "instructor",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotInstructor(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                gotLanguages(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        this.filteredLanguages = this.languages.data;
                    }
                },
                fetchLanguages() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.languageListAPI(),
                        module: "gotData",
                        store: "languages",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotLanguages(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                onStateChange(stateID, step) {
                    const payload = step.payload;

                    step.citiesList.isActive = true;
                    step.citiesList.isField = false;
                    payload.city = ""
                    this.fetchCities(stateID, step);
                },
                onCountryChange(countryID, step) {
                    const payload = step.payload;

                    step.statesList.isActive = true;
                    step.statesList.isField = false;
                    step.citiesList.isActive = false;
                    step.citiesList.isField = false;
                    payload.state = "";
                    payload.city = ""
                    this.fetchStates(countryID, step);
                },
                gotCities(options, step) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        step.citiesList.isField = true;
                    };
                },
                fetchCities(stateID, step) {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.cityListAPI(stateID),
                        module: "gotData",
                        store: "cities",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotCities(options, step)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                gotStates(options, step) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        step.statesList.isField = true;
                    };
                },
                fetchStates(countryID, step) {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.stateListAPI(countryID),
                        module: "gotData",
                        store: "states",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotStates(options, step)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                fetchCountries() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.countriesListAPI(),
                        module: "gotData",
                        store: "countries",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
                findNameByID(obj, key, value) {
                    const data = YUNOCommon.findObjectByKey(obj, key, value)

                    return data.name;
                },
                finishSignup() {
                    const stepThree = YUNOCommon.findObjectByKey(this.steps.items, 'slug', 'three'),
                        instructorObj = this.instructor.data,
                        instructorStepThree = YUNOCommon.findObjectByKey(instructorObj.steps, 'type', 'three');
                    
                    stepThree.isLoading = true;
                    instructorStepThree.is_step_completed = true;
                    this.postStep(instructorObj, stepThree, "finish");
                },
                initwhatYouTeach(nextStep) {
                    const stepTwo = YUNOCommon.findObjectByKey(this.steps.items, 'slug', 'two'),
                        payload = JSON.parse(JSON.stringify(stepTwo.payload)),
                        instructorObj = this.instructor.data,
                        instructorStepTwo = YUNOCommon.findObjectByKey(instructorObj.steps, 'type', 'two');

                    stepTwo.isLoading = true;
                    instructorStepTwo.fields = payload;
                    instructorStepTwo.is_step_completed = true;
                    this.postStep(instructorObj, stepTwo, nextStep);
                },
                initAboutYou(nextStep) {
                    const stepOne = YUNOCommon.findObjectByKey(this.steps.items, 'slug', 'one'),
                        payload = JSON.parse(JSON.stringify(stepOne.payload)),
                        instructorObj = this.instructor.data,
                        instructorStepOne = YUNOCommon.findObjectByKey(instructorObj.steps, 'type', 'one');

                    stepOne.isLoading = true;
                    payload.country = this.findNameByID(this.countries.data, 'id', payload.country);
                    payload.state = this.findNameByID(this.states.data, 'id', payload.state);
                    instructorStepOne.fields = payload;
                    instructorStepOne.is_step_completed = true;
                    this.postStep(instructorObj, stepOne, nextStep);
                },
                coursesMapped(options, step) {
                    step.isLoading = false;

                    const response = options?.response?.data;

                    if (response?.code === 201) {
                        console.log(response.message)
                        window.location.href = YUNOCommon.config.host() + "/welcome"
                    } else if (response?.message) { 
                        console.log(response.message)
                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${response.message}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        });
                    }
                },
                mapCourses(step) {
                    let payload = {
                        instructor_id: isLoggedIn,
                        course_ids: this.userInfo.data.courses_to_be_map
                    };

                    const options = {
                        apiURL: YUNOCommon.config.course("mapCourses"), 
                        module: "gotData", 
                        store: "form", 
                        payload: payload, 
                        callback: true, 
                        callbackFunc: (options) => this.coursesMapped(options, step)
                    };
        
                    this.dispatchData('postData', options);
                },
                dispatchData(action, options) {
                    this.$store.dispatch(action, options);
                },
                stepPosted(options, currentStep, nextStep) {
                    if (nextStep === "finish") {
                        currentStep.isLoading = true;
                    } else {
                        currentStep.isLoading = false;
                    }

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                        if (nextStep === "finish") {
                            localStorage.removeItem('skipSignUp');
                            localStorage.removeItem('userSignUp');
                            localStorage.removeItem('userState');

                            if (this.userInfo.data.courses_to_be_map.length > 0) {
                                this.mapCourses(currentStep);    
                            } else {
                                setTimeout(() => { 
                                    window.location.href = YUNOCommon.config.host() + "/welcome"
                                }, 50);
                            }
                        } else {
                            this.steps.active = nextStep;
                            YUNOCommon.scrollToElement("#instructorSteps", 500);
                        };
                    } else {
                        const response = options.response.data;

                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${response.message}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        });
                    };
                },
                postStep(payload, currentStep, nextStep) {
                    //Reset module
                    this.moduleWithoutTab.success = false;
                    this.moduleWithoutTab.data = [];
                    this.moduleWithoutTab.error = null;
                    this.moduleWithoutTab.errorData = [];

                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.updateInstructorDetailAPI(),
                        module: "gotData",
                        store: "moduleWithoutTab",
                        payload: payload,
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.stepPosted(options, currentStep, nextStep)
                        }
                    };

                    this.$store.dispatch('postData', options);
                },
                nextStep(data) {
                    data.action();
                },
                preFillForm(userData) {
                    const stepOne = YUNOCommon.findObjectByKey(this.steps.items, 'slug', 'one'),
                        payload = stepOne.payload;

                    payload.first_name = userData.first_name;
                    payload.last_name = userData.last_name;
                    payload.email = userData.email;
                    payload.phone = userData.mobile;
                    stepOne.profileImg = userData.profile_img;
                },
                fetchModules(userData) {
                    this.fetchInstructor(userData);
                    this.fetchSubjects();
                    this.fetchCountries();
                    this.fetchLanguages();
                    this.fetchAvailability();

                    if (userData !== undefined) {
                        if (userData.is_signup_completed === "completed") {
                            window.location.href = YUNOCommon.config.host() + "/instructor"; 
                        } else {
                            this.preFillForm(userData);
                        }
                    };
                },
                emitEvents() {
                    Event.$on('checkLoggedInState', (status) => {
                        if (status) {
                            Event.$on('gotUserRole', (role, userData) => {
                                this.fetchModules(userData);
                            });
                        };
                    });
                }
            }
        });
    };

    return {
        instructorSignup: instructorSignup
    };
})(jQuery);



if (typeof YUNOInstructorSignup !== 'undefined') {
    window.Event = new Vue();
    YUNOInstructorSignup.instructorSignup();
};