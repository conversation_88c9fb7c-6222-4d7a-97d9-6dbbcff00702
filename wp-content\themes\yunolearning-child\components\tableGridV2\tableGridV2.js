Vue.component('yuno-table-grid', {
    props: {
        apiURL: {
            type: Object,
            required: true,
        },
        filterAPIURL: {
            type: Object,
            required: true,
        },
        apiMethodType: {
            type: String,
            required: true
        },
        payload: {
            type: Object,
            required: false
        },
        sessionStorage: {
            type: Object,
            required: true
        },
        emptyStateCTA: {
            type: Array,
            default: () => []
        },
        emptyStateImg: {
            type: String,
        },
        recordFoundCaption: {
            type: String,
            default: "found"
        },
    },
    template: `
        <div :class="[isFilterMobile ? 'filterActive' : '']">
            <div class="mainHeader">
                <yuno-table-grid-filters
                    @onDropdownChange="onDropdownChange"
                    @manageFilters="onManageFilters"
                    @clearFilter="onClearFilter"
                    :data="filterResult.payload"
                    :defaultFilters="defaultFilters"
                    :options="{'isFilterMobile': isFilterMobile}"
                    @onQuerySearch="onQuerySearch"
                    @onSearchInput="onSearchInput"
                >
                </yuno-table-grid-filters>
                <slot name="stats"></slot>
                <div class="gridInfo" v-if="filterResult.success && !filterResult.error">
                    <p class="note">
                        <span>{{ filterResult.count }}</span> {{ recordFoundCaption }} 
                    </p>
                    <ul class="actions">
                        <template v-for="(action, i) in gridActions">
                            <li v-if="action.type === 'dropdown'" :key="i">
                                <div class="yunoDropdown iconOnly" :class="[action.filter]">
                                    <b-dropdown 
                                        v-model="action.selected" 
                                        aria-role="list"
                                        :mobile-modal="false"
                                        :multiple="true"
                                        @change="onHideColChange($event, action)"
                                    >
                                        <template #trigger>
                                            <div class="labelWrapper">
                                                <span class="selectedItem"><span class="material-icons-outlined">{{ action.icon }}</span></span>
                                                <span class="material-icons icon">expand_more</span>
                                            </div>
                                        </template>
                                        <p class="listCaption">Hide Columns</p>
                                        <template v-for="(option, j) in filterResult.data.columns">
                                            <b-dropdown-item 
                                                :value="option.field.parent" 
                                                :key="j"
                                                aria-role="listitem"
                                                @click="onHideColItemSelect(option, action)"
                                            >
                                                {{ option.label }}
                                            </b-dropdown-item>
                                        </template>
                                    </b-dropdown>
                                </div>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
            <yuno-csv-download-modal :data="filterResult"></yuno-csv-download-modal>
            <section class="yunoTable">
                <template v-if="filterResult.loading">
                    <b-skeleton height="500px"></b-skeleton>
                </template>
                <template v-if="filterResult.success">
                    <template v-if="filterResult.error">
                        <yuno-empty-state-v2 
                            :options="{'type': 'noDataFound', 'message': filterResult.errorData}"
                            :cta="emptyStateCTA"
                            :image="emptyStateImg"
                        >
                        </yuno-empty-state-v2>
                    </template>    
                    <template v-else>
                        <b-table
                            :data="filterResult.data.rows"
                            :loading="filterResult.isLoadMore"
                            :paginated="true"
                            :backend-pagination="true"
                            :total="filterResult.count"
                            :per-page="filterResult.limit"
                            :current-page="filterResult.currentPage"
                            :striped="true"
                            backend-sorting
                            @sort="sortPressed"
                            :default-sort-direction="defaultSortOrder"
                            :default-sort="defaultSort"
                            @page-change="pageChange($event)"
                            ref="table">
                            <b-table-column
                                v-for="(column, i) in filterResult.data.columns" 
                                :key="i" 
                                :field="column.field.parent" 
                                :visible="column.is_active"
                                :label="column.label" 
                                v-slot="props"
                                :sortable="column.sortable">
                                <template v-if="column.field.child.length > 0">
                                    <template v-if="column.field.parent ===  'start_date' || column.field.parent === 'end_date'">
                                        <div :class="[column.field.parent]">
                                            <template v-for="(child, j) in column.field.child">
                                                {{ formatDate(props.row[column.field.parent][child]) }}
                                            </template>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'learner' || column.field.parent === 'instructor' || column.field.parent === 'counselor'">
                                        <figure 
                                            :class="[column.field.parent]" class="userWithPhoto"
                                        >
                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                            <figcaption>
                                                {{ props.row[column.field.parent].full_name }}
                                            </figcaption>
                                        </figure>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'course'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <template v-for="(child, j) in column.field.child">
                                                <span class="gridItem">
                                                    <template v-if="child === 'title'">
                                                        {{ props.row[column.field.parent][child] }}
                                                    </template>
                                                    <template v-if="child === 'id'">
                                                        {{ "(" +  props.row[column.field.parent][child] + ")" }}
                                                    </template>
                                                </span>
                                                <template v-if="child === 'url'">
                                                    <a class="gridItem" :href="props.row[column.field.parent][child]" target="_blank"><span class="material-icons">open_in_new</span></a>
                                                </template>
                                            </template>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'academy' || column.field.parent === 'of_org'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <span class="gridItem">
                                                {{ props.row[column.field.parent].name }}
                                            </span>
                                            <span class="gridItem">
                                                ({{ props.row[column.field.parent].id }})
                                            </span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'list_price' || column.field.parent === 'selling_price'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <span class="gridItem">
                                                {{ props.row[column.field.parent].exclusive_tax }}
                                            </span>
                                            <span class="gridItem">
                                                ({{ props.row[column.field.parent].inclusive_tax }})
                                            </span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'classes'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <span class="gridItem">
                                                {{ props.row[column.field.parent].attended }}
                                            </span>
                                            <span class="gridItem">
                                                ({{ props.row[column.field.parent].total }})
                                            </span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'attendance'">
                                        <template v-if="props.row[column.field.parent].percentage === 0 || props.row[column.field.parent].percentage === ''">
                                            NA
                                        </template>
                                        <div class="percentageBlock" v-else>
                                            <b-progress 
                                                :type="{
                                                    'is-red': props.row[column.field.parent].percentage <= 30,
                                                    'is-orange': props.row[column.field.parent].percentage > 30,
                                                    'is-yellow': props.row[column.field.parent].percentage > 50,
                                                    'is-lightGreen': props.row[column.field.parent].percentage > 70,
                                                    'is-darkGreen': props.row[column.field.parent].percentage > 80
                                                }"  
                                                format="percent"    
                                                :value="Number(props.row[column.field.parent].percentage)">
                                                {{props.row[column.field.parent].percentage}}
                                            </b-progress>
                                            <div class="percentage">{{ props.row[column.field.parent].percentage }}% <a href="#" @click.prevent="manageDrawer(props.row, column.field.parent)"><span class="material-icons">pin_invoke</span></a></div>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'enrolled_by'">
                                        <figure 
                                            :class="[column.field.parent]" class="userWithPhoto"
                                        >
                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                            <figcaption>
                                                {{ props.row[column.field.parent].full_name + " " + "(" + props.row[column.field.parent].id + " " + props.row[column.field.parent].role + ")" }} 
                                            </figcaption>
                                        </figure>    
                                    </template>
                                    <template v-else>
                                        <template v-if="column.field.parent === 'referral'">
                                            <figure 
                                                :class="[column.field.parent]" class="userWithPhoto"
                                            >
                                                <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                                <figcaption>
                                                    {{ props.row[column.field.parent].full_name }}
                                                </figcaption>
                                            </figure>    
                                        </template>
                                        <template v-else>
                                            <div :class="[column.field.parent]">
                                                <template v-for="(child, j) in column.field.child">
                                                    <template v-if="child.sub_child && child.sub_child.length > 0">
                                                        <template v-for="(subChild, k) in child.sub_child">
                                                            <span class="subChild">
                                                                {{ formatDate(props.row[column.field.parent][child.field][subChild], column.field.parent) }}
                                                            </span>
                                                        </template>
                                                    </template>
                                                    <template v-else>
                                                        {{ props.row[column.field.parent][child] }}
                                                    </template>
                                                </template>
                                            </div>
                                        </template>
                                    </template>
                                </template>
                                <template v-else>
                                    <template v-if="column.field.parent === 'status'">
                                        <div :class="[column.field.parent, 'hasActiveInactive', props.row[column.field.parent] === 'ACTIVE' ? 'active' : 'inactive']">
                                            <span class="value">{{ props.row[column.field.parent] }}</span>
                                        </div>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'is_active'">
                                        <div :class="[column.field.parent, 'hasActiveInactive', props.row[column.field.parent] ? 'active' : 'inactive']">
                                            <template v-if="props.row[column.field.parent]">
                                                <span class="material-icons">how_to_reg</span>
                                            </template>
                                            <template v-else>
                                                <span class="material-icons">person_off</span>
                                            </template>
                                        </div>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'actions'">
                                        <ul :class="[column.field.parent]">
                                            <li 
                                                v-for="(item, k) in props.row[column.field.parent]"
                                                v-if="item.is_active"
                                                :key="'action-' + k">
                                                <b-tooltip :label="item.label"
                                                    type="is-dark"
                                                    position="is-left">
                                                    <a :href="item.url !== false ? item.url : '#'" :target="item.link_target" @click="initAction(props.row, item, $event)">
                                                        <span class="itemLabel">{{ item.label }}</span>
                                                        <span class="itemIcon" :class="[item.active_class]" @mouseover="manageMouse(item, 'over')" @mouseout="manageMouse(item, 'out')">
                                                            {{ item.icon.value === undefined ?  item.icon.font : item.icon.value }}
                                                        </span>
                                                    </a>
                                                </b-tooltip>
                                            </li>
                                        </ul>    
                                    </template>
                                    <template v-else>
                                        <div :class="[column.field.parent]">{{ props.row[column.field.parent] }}</div>    
                                    </template>
                                </template>
                            </b-table-column>
                        </b-table>    
                    </template>    
                </template>
            </section>
        </div>
    `,
    data() {
        return {
            hasDrawerCols: [
                "enrollment_count",
                "attendance_count"
            ],
            isFilterMobile: false,
            defaultFilters: [],
            selectedOption: [],
            defaultSortOrder: "asc",
            defaultSort: [],
            gridActions: [
                {
                    message: "Request CSV download",
                    icon: "summarize",
                    filter: "request_CSV_download",
                    is_active: true,
                    selected: "",
                    type: "custom",
                    placeholder: "Role",
                    items: []
                },
                {
                    message: "Hide columns",
                    icon: "settings",
                    filter: "hideColumn",
                    is_active: true,
                    selected: [],
                    type: "dropdown",
                    placeholder: "Role",
                    items: []
                }
            ],
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'filterResult',
            'learnerInsightsClass',
            'filters'
        ]),
        attendance() {
            return this.learnerInsightsClass.data;
        }
    },
    watch: {
        'filterResult.refreshTable': {
            handler(newValue, oldValue) {
                if (newValue) {
                    this.refreshTable();
                }
            },
            deep: true
        }
    },
    created() {
        this.manageDefaultFilters();
    },
    mounted() {
        this.getStorage();
    },
    methods: {
        /**
         * Formats a date string into a readable format
         * @param {string} dateStr - The date string to format
         * @param {string} parent - Optional parent context for date formatting
         * @returns {string} Formatted date string or original input if invalid
         */
        formatDate(dateStr, parent) {
            // Create a new Date object from the input string
            const date = new Date(dateStr);

            // Return original string if it's a number or invalid date
            if (dateStr === "0" || /^\d+$/.test(dateStr) || isNaN(date.getTime())) {
                return dateStr;
            }

            // Define arrays for weekday and month names
            const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
            const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
            
            // Extract date components
            const weekday = weekdays[date.getDay()];
            const month = months[date.getMonth()];
            const day = date.getDate();
            const year = date.getFullYear();
            
            // Return formatted date string
            return `${weekday}, ${month} ${day}, ${year}`;
        },
        /**
         * Copies text to clipboard and shows a toast notification
         * @param {Object} row - The row data containing the text to copy
         * @param {Object} action - The action object containing the URL to copy
         */
        async copyToClipboard(row, action) {
            try {
                // Attempt to write the URL to clipboard
                await navigator.clipboard.writeText(action.url);
                // Show success toast
                this.$buefy.toast.open({
                    duration: 1000,
                    message: `Copy to clipboard`,
                    position: 'is-bottom'
                });
            } catch (error) {
                // Log error and show failure toast
                console.error('Failed to copy:', error);
                this.$buefy.toast.open({
                    duration: 1000,
                    message: `Failed to copy!`,
                    position: 'is-bottom'
                });
            }
        },
        /**
         * Initializes an action based on its slug type
         * @param {Object} row - The row data
         * @param {Object} action - The action object containing the action details
         * @param {Event} e - The event object
         */
        initAction(row, action, e) {
            // Handle different action types based on slug
            switch (action.slug) {
                case "clickToCopy":
                    // Prevent default behavior and copy to clipboard
                    e.preventDefault();
                    this.copyToClipboard(row, action);
                    break;
            }

            // Emit the action event to parent component
            this.$emit("initAction", row, action, e);
        },
        /**
         * Generates recording actions based on recording data
         * @param {Object} row - The row data containing recording information
         * @param {string} col - The column identifier
         * @returns {Array|boolean} Array of recording actions or false if no recordings
         */
        recordingActions(row, col) {
            // Create list object with recording ID
            const list = {
                id: row.recording
            }, 
            // Encode the list as URL parameters
            params = encodeURI(JSON.stringify(list));

            // Return actions array if recordings exist, false otherwise
            if (row.recording.length !== 0) {
                return [
                    {
                        "label": "Open in new tab",
                        "is_active": true,
                        "slug": "openInNewTab",
                        "active_class": "material-icons-outlined",
                        "url": YUNOCommon.config.host() + "/recording/?videoID=" + params + "&class_id="+ row.id +"",
                        "link_target": "_blank",
                        "icon": {
                            "type": "mdl",
                            "class": "material-icons-outlined",
                            "hover": "material-icons",
                            "font": "open_in_new"
                        }
                    },
                    {
                        "label": "Click to copy",
                        "is_active": true,
                        "slug": "clickToCopy",
                        "active_class": "material-icons-outlined",
                        "url": YUNOCommon.config.host() + "/recording/?videoID=" + params + "&class_id="+ row.id +"",
                        "link_target": "_blank",
                        "icon": {
                            "type": "mdl",
                            "class": "material-icons-outlined",
                            "hover": "material-icons",
                            "font": "file_copy"
                        }
                    }
                ]
            } else {
                return false
            }
        },
        /**
         * Checks if a column is a drawer column
         * @param {Array} list - List of drawer columns
         * @param {string} col - Column to check
         * @returns {string|undefined} The column if found in list, undefined otherwise
         */
        isDrawerCol(list, col) {
            // Return the column if found in the list
            if (YUNOCommon.findInArray(list, col)) { 
                return col;
            }
        },
        /**
         * Emits an event to manage the drawer component
         * @param {Object} row - The row data
         * @param {string} col - The column identifier
         */
        manageDrawer(row, col) {
            // Emit event to parent component with row and column data
            this.$emit("manageDrawer", row, col)
        },
        /**
         * Fetches class details for a specific class and user
         * @param {Object} data - Object containing class_id and user_id
         */
        getClassDetail(data) {
            // Call fetchClassDetails with extracted IDs
            this.fetchClassDetails(data.class_id, data.user_id)
        },
        /**
         * Handles the response from fetching class details
         * @param {Object} options - Response options containing class data
         */
        gotClassDetails(options) {
            const { response } = options;
            // Check if response indicates success
            if (response?.data?.code === 200) {
                const { data } = response;
                // Set error message if user is absent
                if (data?.is_absent) {
                    this.learnerInsightsClass.errorData = response.data.message;
                }
            }
        },
        /**
         * Fetches class details from the API
         * @param {string} classID - The class identifier
         * @param {string} learnerID - The learner identifier
         */
        fetchClassDetails(classID, learnerID) {
            // Reset learner insights class state
            Object.assign(this.learnerInsightsClass, { success: false, error: null, data: [] });
        
            // Prepare API request options
            const options = {
                apiURL: YUNOCommon.config.learnerInsightsClassAPI(classID, learnerID, true),
                module: "gotData",
                store: "learnerInsightsClass",
                callback: true,
                callbackFunc: options => this.gotClassDetails(options)
            };
        
            // Dispatch API request
            this.$store.dispatch('fetchData', options);
        },
        /**
         * Manages and constructs the API URL
         * @returns {string} The constructed API URL
         */
        manageAPIURL() {
            // Construct and return the API URL
            return "/wp-json/" + YUNOCommon.getFromString(
                YUNOCommon.config[this.$props.apiURL.configMethod](
                    this.$props.apiURL.type, 
                    this.$props.apiURL.role, 
                    this.$props.apiURL.view, 
                    this.filterResult.limit, 
                    this.filterResult.offset
                ), /wp-json\/(.*)/)
        },
        /**
         * Handles column visibility toggle in the hide columns dropdown
         * @param {Object} option - The column option to toggle
         * @param {Object} action - The action object containing dropdown state
         */
        onHideColItemSelect(option, action) {
            // Toggle column visibility
            if (option.is_active) {
                option.is_active = false
            } else {
                option.is_active = true
            }
            // Update storage with new visibility state
            this.setStorage(true);
        },
        /**
         * Handles column visibility change event
         * @param {Event} e - The change event
         * @param {Object} filter - The filter object
         */
        onHideColChange(e, filter) {
            // Handle column visibility change
        },
        /**
         * Manages mouse hover states for action icons
         * @param {Object} data - The action data
         * @param {string} type - The mouse event type ('over' or 'out')
         */
        manageMouse(data, type) {
            // Update icon class based on hover state
            if (type === "over") {
                data.active_class = data.icon.hover;
            } else {
                data.active_class = data.icon.class;
            };
        },
        /**
         * Gets the active organization ID
         * @returns {string|null} The active organization ID or null
         */
        activeOrg() {
            // Get active org ID from user info or return null
            const activeOrg = this.userInfo.data.current_state.org_id;
            return activeOrg || null;
        },
        /**
         * Retrieves and applies stored table state from sessionStorage
         */
        getStorage() {
            // Get storage configuration from props
            const storage = this.$props.sessionStorage;
            // Calculate previous version number
            let version = Number(JSON.parse(JSON.stringify(storage.version)));
            let lastStorage = storage.name + "V" + (--version);

            // Remove old version storage
            sessionStorage.removeItem(lastStorage);

            // Get current version storage
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store !== null) {
                // Parse and apply stored data
                const data = JSON.parse(store);
                this.filterResult.payload = data.payload;
                this.filters.data = data.filters;
                this.filters.success = true;

                // Update org filters for org-admin
                if (this.userRole.data === "org-admin") {
                    const activeOrganization = this.activeOrg();
                    
                    ['org_id', 'org'].forEach(key => {
                        if (key in this.filterResult.payload) {
                            this.filterResult.payload[key] = activeOrganization;
                        }
                    });
                }

                // Apply stored state
                this.gridActions = data.gridActions;
                this.filterResult.currentPage = data.currentPage;
                this.filterResult.offset = data.offset;
                // this.fetchFilters(true);
                this.fetchGrid(true);
            } else {
                // Initialize with default state
                this.fetchFilters(true);
                this.fetchGrid(true);
            }
        },
        /**
         * Stores the current table state in sessionStorage
         * @param {boolean} hasColumns - Whether to include column visibility state
         */
        setStorage(hasColumns) {
            // Get storage configuration from props
            const storage = this.$props.sessionStorage;
            // Prepare data to store
            const store = {
                payload: this.filterResult.payload,
                filters: this.filters.data,
                gridActions: this.gridActions,
                currentPage: this.filterResult.currentPage,
                offset: this.filterResult.offset,
                currentTab: this.filterResult.tabs.activeTab,
            };
            // Store data with delay to prevent rapid updates
            setTimeout(() => {
                sessionStorage.setItem(storage.name + "V" + storage.version, JSON.stringify(store));
            }, 100);
        },
        /**
         * Initializes default filters from props
         */
        manageDefaultFilters() {
            // Create deep copy of payload for default filters
            this.defaultFilters = JSON.parse(JSON.stringify(this.$props.payload));
        },
        /**
         * Clears a specific filter and resets to default
         * @param {Object} selected - The selected filter value
         * @param {Object} filter - The filter object to clear
         */
        onClearFilter(selected, filter) {
            // Reset filter to default value
            const payload = this.$props.payload; 
            payload[filter.filter] = this.defaultFilters[filter.filter];
            filter.selected = this.defaultFilters[filter.filter];
            // Reset table and fetch new data
            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Handles dropdown filter changes
         * @param {Object} selectOption - The selected option
         * @param {Object} filter - The filter object
         */
        onDropdownChange(selectOption, filter) {
            // Reset table and fetch new data with updated filter
            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Toggles mobile filter visibility
         */
        onManageFilters() {
            // Toggle mobile filter state
            this.isFilterMobile = !this.isFilterMobile;
        },
        /**
         * Resets table state to initial values
         */
        resetTable() {
            // Reset all table state properties
            this.filterResult.currentPage = 1;
            this.filterResult.error = null;
            this.filterResult.data = [];
            this.filterResult.success = false;
            this.filterResult.offset = 0;
            this.filterResult.payload.offset = this.filterResult.offset;
        },
        /**
         * Handles table sorting
         * @param {string} field - The field to sort by
         * @param {string} order - The sort order
         * @param {Event} event - The sort event
         */
        sortPressed(field, order, event) {
            // Update payload with sort parameters
            const payload = this.$props.payload;
            payload.sorting.column = field;
            payload.sorting.type = order;
            // Set loading state and fetch sorted data
            this.filterResult.isLoadMore = true;
            this.fetchGrid(false);
        },
        /**
         * Refreshes the table data
         */
        refreshTable() {
            // Set loading state and fetch fresh data
            this.filterResult.isLoadMore = true;
            this.fetchGrid(false);
        },
        /**
         * Handles pagination page changes
         * @param {number} e - The new page number
         */
        pageChange(e) {
            // Update pagination state
            this.filterResult.currentPage = e;
            this.filterResult.isLoadMore = true;
            this.filterResult.offset = Math.floor(this.filterResult.limit * e - this.filterResult.limit);
            // Emit page change event and fetch new page data
            this.$emit("onPageChange");
            this.fetchGrid(false);
        },
        /**
         * Manages column visibility based on user preferences
         * @param {Array} columns - The columns to manage
         */
        manageColumnsVisibility(columns) {
            // Find hide column filter
            const filter = YUNOCommon.findObjectByKey(this.gridActions, "filter", "hideColumn");

            // Update column visibility based on selected columns
            if (filter.selected.length !== 0) {
                for (const selectedColumn of filter.selected) {
                    const col = columns.find(colItem => colItem.field.parent === selectedColumn);
                    if (col) {
                        col.is_active = false;
                    }
                }
            }
        },
        /**
         * Handles search query changes
         * @param {string} name - The search field name
         * @param {Object} filter - The filter object
         * @param {boolean} isSelect - Whether this is a select event
         */
        onQuerySearch(name, filter, isSelect) {
            // Emit search event to parent
            this.$emit("onQuerySearch", name, filter, isSelect);

            // Reset and fetch if it's a select event
            if (isSelect) {
                this.resetTable();
                this.fetchGrid(true);
            }
        },
        /**
         * Handles search input changes
         * @param {string} name - The search field name
         * @param {Object} filter - The filter object
         */
        onSearchInput(name, filter) {
            // Reset table and fetch new data with search input
            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Parses a column field string into a structured object
         * @param {string} fieldStr - The field string to parse
         * @returns {Object} Parsed field object with parent and child structure
         */
        parseColumnField(fieldStr) {
            // Handle comma-separated fields
            if (fieldStr.indexOf(',') !== -1) {
                let tokens = fieldStr.split(',').map(s => s.trim());
                let parent = '';
                let childrenGrouped = {};
        
                // Process each token
                tokens.forEach((token, index) => {
                    let parts = token.split('.');
                    if (index === 0) {
                        parent = parts[0];
                    }
                    if (parts.length === 1) {
                        childrenGrouped[parts[0]] = [];
                    } else if (parts.length === 2) {
                        let child = parts[1];
                        if (!childrenGrouped[child]) {
                            childrenGrouped[child] = [];
                        }
                    } else if (parts.length >= 3) {
                        let child = parts[1];
                        let subChild = parts.slice(2).join('.');
                        if (!childrenGrouped[child]) {
                            childrenGrouped[child] = [];
                        }
                        if (childrenGrouped[child].indexOf(subChild) === -1) {
                            childrenGrouped[child].push(subChild);
                        }
                    }
                });
        
                // Convert grouped children to array format
                let childrenArray = [];
                for (let child in childrenGrouped) {
                    if (childrenGrouped[child].length > 0) {
                        childrenArray.push({ field: child, sub_child: childrenGrouped[child] });
                    } else {
                        childrenArray.push(child);
                    }
                }
                return { parent, child: childrenArray };
            } else {
                // Handle single field
                if (fieldStr.indexOf('.') !== -1) {
                    let parts = fieldStr.split('.');
                    if (parts.length >= 3) {
                        return { parent: parts[0], child: [{ field: parts[1], sub_child: parts.slice(2) }] };
                    } else {
                        return { parent: parts[0], child: parts.slice(1) };
                    }
                } else {
                    return { parent: fieldStr, child: [] };
                }
            }
        },
        /**
         * Processes and structures column definitions
         * @param {Array} columns - The columns to process
         */
        manageColumns(columns) {
            // Process each column
            for (let i = 0; i < columns.length; i++) {
                const column = columns[i];
                // Parse field structure and set default visibility
                column.field = this.parseColumnField(column.field);
                column.is_active = true;
            }
        },
        /**
         * Converts an object to a query string
         * @param {Object} obj - The object to convert
         * @returns {string} The query string
         */
        objectToQueryString(obj) {
            // Convert object to URLSearchParams and return as string
            return new URLSearchParams(obj).toString();
        },
        /**
         * Handles grid data response
         * @param {Object} options - The response options
         */
        gotGrid(options) {
            // Reset loading states
            this.filterResult.isLoadMore = false;
            this.filterResult.loading = false;
            this.filterResult.refreshTable = false;
            
            // Extract response data
            const { code, data, count } = options.response?.data || {};
            
            // Process successful response
            if (code === 201 || code === 200) {
                // Process columns and update state
                this.manageColumns(data.columns);
                this.manageColumnsVisibility(data.columns);
                this.$emit("gotGrid", data);
                this.filterResult.count = count;
                this.filterResult.data = data;
            }
        },
        /**
         * Fetches grid data from the API
         * @param {boolean} moduleLoading - Whether to show loading state
         */
        fetchGrid(moduleLoading) {
            // Update storage and reset mobile filter
            this.setStorage();
            this.isFilterMobile = false;
            this.filterResult.loading = moduleLoading;

            // Prepare request parameters
            const params = {
                view: this.$props.apiURL.options.view,
                params: `?${this.objectToQueryString(this.filterResult.payload)}`
            };

            // Prepare API request options
            let options = { 
                apiURL: YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type, params),
                module: "gotData",
                store: "filterResult",
                moduleLoading: moduleLoading,
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotGrid(options)
            };

            // Add POST-specific options if needed
            if (this.$props.apiMethodType === "POST") {
                options.payload = this.filterResult.payload;
                options.headers = {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                }
            };

            // Dispatch API request
            this.$store.dispatch(this.$props.apiMethodType === "POST" ? "postData" : "fetchData", options); 
        },
        /**
         * Handles filter data response
         * @param {Object} options - The response options
         */
        gotFilters(options) {
            // Extract response data
            const { code, data } = options.response?.data || {};
            
            // Process successful response
            if (code === 201 || code === 200) {
                // Emit event and update filters state
                this.$emit("gotFilters", data);
                this.filters.data = data;
            }
        },
        /**
         * Fetches filter data from the API
         * @param {boolean} moduleLoading - Whether to show loading state
         */
        fetchFilters(moduleLoading) {
            // Prepare API request options
            let options = { 
                apiURL: YUNOCommon.config[this.$props.filterAPIURL.configMethod](this.$props.filterAPIURL.type, this.$props.filterAPIURL.options),
                module: "gotData",
                store: "filters",
                moduleLoading: moduleLoading,
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotFilters(options)
            };

            // Dispatch API request
            this.$store.dispatch("fetchData", options); 
        }
    }
});
