@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

@font-face {
    font-family: 'Material Icons Outlined';
    font-style: normal;
    font-weight: 400;
    src: url('../../../dist/fonts/material-Icons.woff2?6qrc5l') format('woff2');
}
  
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url('../../../dist/fonts/material-Icons-filled.woff2?8qrc5l') format('woff2');
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.yunoLoader {
    display: none;

    &.isActive {
        display: flex;
    }

    &.withOverlay {
        width: 100%;
        height: 100%;
        @include setBGColor($whiteBG, 0.8);
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        position: fixed;
        z-index: 7777;
    }

    .yunoSpinner {
        width: 100px;
        height: 100px;
        position: static;
    }
}

.poweredBy {
    display: inline-flex;
    padding: 4px 8px;
    align-items: center;
    gap: 8px;
    border-radius: 6px;
    border: 1px solid #B7B8C0;

    p {
        color: #ACACAD;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.4px;
    }
}

.yunoSpinner {
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: $primaryColor;

    -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */

    &:before {
        content: "";
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: $secondaryColor;

        -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
        animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

    &:after {
        content: "";
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #f9c922;

        -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
          animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }
}

@-webkit-keyframes spin {
    0%   { 
        -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);  /* IE 9 */
        transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);  /* IE 9 */
        transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
    }
}
@keyframes spin {
    0%   { 
        -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);  /* IE 9 */
        transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);  /* IE 9 */
        transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
    }
}



a {
    color: $primary;
}

.button.is-focused, .button:focus {
    border-color: $primary
}

.button.is-focused:not(:active), .button:focus:not(:active) {
    box-shadow: none
}


html body {
    @include setFontFamily('Roboto', 400);
    @extend .dark87;
    
}

#app {
    .dark38 {
        @include setFontColor($primaryCopyColor, 0.38);
    }
    
    .dark87 {
        @include setFontColor($onSurface, 0.87);
    }
    
    .dark60 {
        @include setFontColor($onSurfaceVariant, 0.6);
    }

    .input:active, .input:focus, .is-active.input, .is-active.textarea, .is-focused.input, .is-focused.textarea, .select select.is-active, .select select.is-focused, .select select:active, .select select:focus, .taginput .is-active.taginput-container.is-focusable, .taginput .is-focused.taginput-container.is-focusable, .taginput .taginput-container.is-focusable:active, .taginput .taginput-container.is-focusable:focus, .textarea:active, .textarea:focus {
        box-shadow: inset 0 0 0.0625em 0.125em rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.3);
    }

    .b-checkbox.checkbox input[type=checkbox]:checked+.check {
        background-color: $primary;
        border-color: $primary;
    }

    .largestTitle {
        @include setFont($headline3, 42px, 700, $gapSmall);
    
        @media (min-width: 768px) {
            @include setFont($headline2, 62px, 700, $gapSmall);
        }
    }
    
    .largerTitle {
        @include setFont($headline5, 28px, 500, $gapSmall);
        @extend .dark87;
    }
    
    .largeTitle {
        @include setFont($headline6, 24px, 500, 0);
    }

    .largerSubheading {
        @include setFont($headline3, 40px, 500, 0);
    }
    
    .smallCaption {
        @include setFont($subtitle1, 24px, 500, 0);
    }
    
    .smallerCaption {
        @include setFont($body1, 25px, 500, $gapSmall);
    }
    
    .body1 {
        @include setFont($body2, 20px, 400, 0);
        @extend .dark60;
    }
    
    .body2 {
        @include setFont($caption2, 16px, 400, 0);
        @extend .dark60;
    }

    .overline {
        @include setFont($overline, 14px, 400, 0);

        &.capital {
            text-transform: uppercase;
        }
    }

    .brandHeader {
        justify-content: center;
        display: flex;
        margin-top: $gap15 * 2;

        .navbar-brand {
            img {
                width: 102px;
                height: auto;
                max-height: none;
            }
        }

        .navbar-menu {display: none;}
    }

    .step-container {}
    
    .slide-left-enter-active, .slide-right-leave-active {
    transition: transform 0.4s ease;
    }
    .slide-left-enter, .slide-right-leave-to {
    transform: translateX(100%);
    }
    .slide-left-leave-active, .slide-right-enter-active {
    transition: transform 0.2s ease;
    }
    .slide-left-leave-to, .slide-right-enter {
    transform: translateX(-100%);
    }

    .hideInMobile {
        display: none;

        @media (min-width: 768px) {
            display: block;
        }
    }

    .loginStep {
        margin-top: 0;
        margin-bottom: $gapLargest;
        padding: $gap15;
        text-align: center;

        @media (min-width: 768px) {
            margin-top: 0;
            margin-bottom: $gapLargest * 2;
        }

        .hasBorder {
            @media (min-width: 768px) {
                border-radius: 3px;
                border: 1px solid $grey;
            }
        }
        

        &.demoSchedule {
            text-align: left;
            position: relative;
            z-index: 31;

            .pre {
                position: absolute;
                left: 0;
                top: -47px;
                display: none;

                &.isShow {
                    display: block;

                    @media (min-width: 768px) {
                        display: none;
                    }
                }

                @media (min-width: 768px) {
                    display: none;
                }
            }

            .step1 {
                text-align: center;

                @media (min-width: 768px) {
                    text-align: left;
                }

                &.isHideInMobile {
                    display: none;

                    @media (min-width: 768px) {
                        display: block;
                    }
                }
            }

            .step2 {
                text-align: center;

                @media (min-width: 768px) {
                    text-align: left;
                }
            }

            .datepickerWrapper {
                border-top: 1px solid $grey;

                @media (min-width: 768px) {
                    border: 0;
                }
            }

            .largeTitle {
                color: $onSurface;
            }

            .column {
                padding: $gap15;

                @media (min-width: 768px) {
                    padding: $gap15 * 2;
                }

                &.noBtmPaddingMobile {
                    padding-bottom: 0;

                    @media (min-width: 768px) {
                        padding-bottom: $gap15 * 2;
                    }
                }

                &.hasRightBorder {
                    position: relative;

                    &::after {
                        content: "";
                        position: absolute;
                        right: 0;
                        top: 15px;
                        bottom: 0;
                        width: 1px;
                        height: calc(100% - 30px);
                        background: $grey;
                        display: none;
                    }

                    @media (min-width: 768px) {
                        &::after {
                            display: block;
                        }
                    }
                }

                &.hasLeftBorder {
                    position: relative;

                    &::before {
                        content: "";
                        position: absolute;
                        left: 0;
                        top: 15px;
                        bottom: 0;
                        width: 1px;
                        height: calc(100% - 30px);
                        background: $grey;
                        display: none;
                    }

                    @media (min-width: 768px) {
                        &::after {
                            display: block;
                        }
                    }
                }
            }
        }

        .largerSubheading {
            display: none;

            @media (min-width: 768px) {
                display: block;
            }
        }

        &.topGap {
            margin-top: 5%;
        }

        .time {
            max-height: 100vh;
            overflow-y: auto;
            margin-top: $gap15;    

            @media (min-width: 768px) {
                max-height: 400px;
            }

            .button {
                width: 100%;
                height: 45px;
                margin-bottom: $gapSmall;
                font-weight: 500;
                font-size: $body2;

                @media (min-width: 768px) {
                    font-size: $body2;
                }
            }
        }

        .selectedSlot {
            .largeTitle {
                color: $onSurface;
                margin-bottom: $gapSmall;
            }
        }

        .yunoDatePicker {
            margin-top: $gap15;

            .dropdown {
                display: block !important;

                @media (min-width: 768px) {
                    display: inline-flex !important;
                }
                
                .dropdown-menu {
                    display: block;

                    @media (min-width: 768px) {
                        display: inline-block;
                        width: 100%;
                    }
                }
            }

            a.datepicker-cell {
                color: $onSurface;
            }

            .datepicker-cell {
                &.is-selected {
                    background-color: $primary;
                    color: white;
                }

                &.is-today {
                    border-color: $primary;
                }
            }

            .pagination {
                .pagination-previous, .pagination-next {
                    &:focus {
                        border-color: $primary;
                        outline: none;
                    }

                    i {
                        display: flex;
                        align-items: center;

                        &::before {
                            @extend .material-icons;
                            color: $primary;
                        }
                    }
                }

                .pagination-previous {
                    i {
                        &::before {
                            content: "\e5cb";
                        }
                    }
                }

                .pagination-next {
                    i {
                        &::before {
                            content: "\e5cc";
                        }

                    }
                }
            }
            .pagination-list {
                .field {
                    .control {
                        // &:nth-child(2) {
                        //     display: none;
                        // }

                        .select {
                            &::after {
                                color: $primary;
                                border-color: $primary;
                            }
                        }

                        select {
                            color: $onSurface;
                        }
                    }
                        
                }
            }
        }

        .loginFieldWrapper {
            text-align: left;
            margin-top: $gap15 * 3;
            display: flex;
            flex-direction: column;
            padding: 0 $gap15;

            @media (min-width: 768px) {
                flex-direction: row;
            }

            .alignC {
                align-self: center;
                order: 2;

                @media (min-width: 768px) {
                    order: 1;    
                }
            }

            .loginWrapper {
                order: 1;

                @media (min-width: 768px) {
                    order: 2;    
                }
            }
        }

        .questionsWrapper {
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.12);
            background: #FFF;
            padding: $gap15 * 2;

            .card-content {
                padding-bottom: 0;
            }
        }

        .loginWrapper {
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.12);
            background: #FFF;
            padding: $gapLarger;

            @media (min-width: 768px) {
                padding: $gap15 * 3;
            }

            .label {
                font-size: $subtitle2;
                @extend .dark60;
            }
        }

        .ratingWrapper {
            display: flex;
            flex-direction: column;
            align-items: center;

            @media (min-width: 768px) {
                flex-direction: row;
                align-items: flex-start;
            }

            figure {
                padding: $gap15 0 0 ;
                margin: 0 $gapSmall 0;

                img {
                    width: 40px;
                    height: auto;
                }
            }

            .ratingInfo {
                h3 {
                    margin: $gapSmaller;

                    span {
                        display: block;
                        margin-top: $gapSmall;
                    }
                }
            }
        }

        &.noBtmGap {
            margin-top: $gapLargest;
            margin-bottom: 0;

            @media (min-width: 768px) {
                margin-top: $gapLargest;
            }
        }

        .card {
            box-shadow: none;

            &.thankyou {
                h3 {
                    margin-bottom: $gapLargest;
                }

                .material-icons-outlined {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background: #15BE56;
                    font-size: 32px;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto $gapLargest;
                }

                .button {
                    background-color: $primary;
                    color: white;
                    margin-top: $gap15;
                }
            }
        }

        &.noTopGap {
            margin-top: 0;
        }

        .largestTitle {
            text-align: center;
        }

        .largeTitle {
            margin: 0;
            @extend .dark60;
            font-weight: 400;
        }

        .body1 {
            margin: 0 0 $gap15;
        }

        .stars {
            color: #F9B600;

            .material-icons {
                font-size: 32px;
            }
        }

        .loginObserver {
            padding-bottom: 24px;
        }

        .instrutorList {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: $gapLargest;

            .list {
                display: flex;
                padding: 0 10px;

                .material-icons {
                    font-size: 30px;
                    margin-left: -16px;
                    border-radius: 50%;
                    border: 1px solid #FFF;
                    background-color: #FFF;
                }
            }
        }

        .agreeTerms {
            .error {
                @include setFont($body2, normal, 400, 0);
                color: red;
            }
        }

        .card-content {
            position: relative;
            padding: 0;

            .pre {
                position: absolute;
                left: -18px;
                top: 0;
            }
        }

        .field {
            .fieldWrapper {
                position: relative;

                .updateNumber {
                    position: absolute;
                    right: 0;
                    top: 0;
                    padding: 0 8px;
                    margin: 0;
                    border: 0;
                    background: none;
                    height: 40px;

                    &:hover {
                        .material-icons {
                            color: $primary;
                        }
                    }
                }

                input {
                    padding-right: 40px;
                }
            }

            input {
                height: 40px;
            }

            .error {
                @include setFont($body2, normal, 400, 0);
                color: red;
                margin-top: $gapSmall;
            }
        }

        .optionSelector {
            
            .label {
                margin-bottom: $gap15;
                small {
                    display: block;
                    font-weight: 400;
                }
            }

            .field-body {
                .field {
                    flex-wrap: wrap;
                    margin: 0 (-$gap15);
                    

                    @media (min-width: 768px) {
                        justify-content: center;    
                    }

                    .control {
                        flex: 0 0 50%;
                        padding: 0 $gap15 $gap15;

                        @media (min-width: 768px) {
                            flex: 0 0 33.3%;    
                            padding: 0 $gap15 $gapLargest;
                        }
                        
                        label {
                            border-radius: 4px;
                            margin: -1px 0 0 0;
                            height: 80px;

                            &.is-success {
                                border-color: $primary;
                                background: rgba(168, 30, 34, 0.04);
                                color: $primary;
                            }
                        }

                        
                    }
                }
            }

            &.time_of_study {
                .field-body {
                    .field {
                        .control {
                            flex: 0 0 100%;

                            @media (min-width: 768px) {
                                flex: 0 0 50%;
                            }
                        }
                    }
                }
            }
        }

        .card-footer {
            &.googleWrapper {
                border: 1px solid;
                border-color: #1976d2;
                border-radius: 4px;
                margin: 0 0;
            }
            
            .button {
                border: 0;
                height: 40px;

                &.hasGoogle {
                    color: #1976d2;
                    height: 40px;

                    span {
                        display: flex;
                        align-items: center;

                        img {
                            margin-right: $gapSmall;
                            width: 24px;
                            height: 24px;
                        }
                    }

                    &:hover {
                        background-color: #1976d2;
                        color: white;

                        span {
                            img {
                                background-color: white;
                            }
                        }
                    }
                }

                &.primary {
                    background-color: $primary;
                    color: white;
                }
            }
        }
    }

    .slotsWrapper {
        display: flex;
        margin: 0 (-$gapSmall);

        &.toRight{
            justify-content: flex-end;
        } 

        .slot {
            flex: 0 0 100%;
            padding: 0 $gapSmall;

            @media (min-width: 768px) {
                flex: 0 0 100%;    
            }

            .largeTitle {
                min-height: 48px;
                font-size: $subtitle1;

                @media (min-width: 768px) {
                    min-height: max-content;
                    font-size: $headline6;
                    text-align: left;
                }
            }
        }

        .selectedSlot {
            flex: 0 0 100%;

            .overline {
                margin-top: $gap15;
            }

            .button {
                &.primaryCTA {
                    background-color: $primary;
                    color: white;
                    margin-top: $gapLargest;
                    margin-bottom: $gapSmall;
                }
            }
        }
    }
}

