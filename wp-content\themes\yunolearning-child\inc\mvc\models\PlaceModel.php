<?php
namespace V4;

/**
 * Place model
 */

class PlaceModel extends Model {
    
    /**
     * Constructor to initialize the PlaceModel
     */
    function __construct()
    {   
        parent::__construct();
    
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadLibary('cache');
    }

    public function addPlace($data) 
    {
        // Code to create a new place record in the database
    
        // Prepare core post data
        $postData = array(
            'post_title'   => isset($data['name']) ? sanitize_text_field($data['name']) : 'Untitled Place',
            'post_type'    => 'place',
            'post_status'  => 'publish',
            'post_content' => isset($data['long_description']) ? wp_kses_post($data['long_description']) : '',
            'post_excerpt' => isset($data['short_description']) ? sanitize_text_field($data['short_description']) : '',
        );
    
        // Insert the post
        $placeId = wp_insert_post( $postData );
        
        if ( is_wp_error( $placeId ) ) {
            return false; // return the error
        }
    
        // Now update ACF fields
        // Ensure these field keys/names match your ACF configuration.  
        // Example assumes field names like 'type', 'address', 'coordinates', etc.
        if ( ! empty( $data['org_id'] ) ) {
            update_field( 'field_place_org_id', sanitize_text_field($data['org_id']), $placeId );
        }

        if ( ! empty( $data['type'] ) ) {
            update_field( 'field_place_type', sanitize_text_field($data['type']), $placeId );
        }
    
        // Address (ACF group)
        if ( ! empty( $data['address'] ) ) {
            $address = $data['address'];
    
            $addressData = array(
                'type'              => isset($address['type']) ? sanitize_text_field($address['type']) : '',
                'title'            => isset($address['title']) ? sanitize_text_field($address['title']) : '',
                'formatted_address' => isset($address['formatted_address']) ? sanitize_text_field($address['formatted_address']) : '',
                'address_1'        => isset($address['address_1']) ? sanitize_text_field($address['address_1']) : '',
                'address_2'        => isset($address['address_2']) ? sanitize_text_field($address['address_2']) : '',
                'floor'            => isset($address['floor']) ? array(
                    'type'   => sanitize_text_field($address['floor']['type']),
                    'number' => sanitize_text_field($address['floor']['number'])
                ) : array(),
                'landmark'         => isset($address['landmark']) ? sanitize_text_field($address['landmark']) : '',
                'city'            => isset($address['city']) ? sanitize_text_field($address['city']) : '',
                'state'           => isset($address['state']) ? array(
                    'name' => sanitize_text_field($address['state']['name']),
                    'code' => sanitize_text_field($address['state']['code'])
                ) : array(),
                'country'         => isset($address['country']) ? array(
                    'id'   => intval($address['country']['id']),
                    'name' => sanitize_text_field($address['country']['name']),
                    'code' => sanitize_text_field($address['country']['code'])
                ) : array(),
                'pin_zip'         => isset($address['pin_zip']) ? sanitize_text_field($address['pin_zip']) : '',
                'coordinates'     => isset($address['coordinates']) ? array(
                    'lat'  => floatval($address['coordinates']['latitude']),
                    'lon' => floatval($address['coordinates']['longitude'])
                ) : array(),
                'google_maps'     => isset($address['google_maps']) ? array(
                    'place_id'         => sanitize_text_field($address['google_maps']['place_id']),
                    'type'            => is_array($address['google_maps']['type']) ? implode(',', array_map('sanitize_text_field', $address['google_maps']['type'])) : sanitize_text_field($address['google_maps']['type'] ?? ''),
                    'colloquial_area' => isset($address['google_maps']['colloquial_area']) ? sanitize_text_field($address['google_maps']['colloquial_area']) : '',
                    'locality'        => isset($address['google_maps']['locality']) ? sanitize_text_field($address['google_maps']['locality']) : '',
                    'sublocality'     => isset($address['google_maps']['sublocality']) ? array(
                        'sublocality_level_1' => sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_1'] ?? ''),
                        'sublocality_level_2' => sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_2'] ?? ''),
                        'sublocality_level_3' => sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_3'] ?? ''),
                        'sublocality_level_4' => sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_4'] ?? ''),
                        'sublocality_level_5' => sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_5'] ?? '')
                    ) : array(),
                    'neighborhood'     => isset($address['google_maps']['neighborhood']) ? sanitize_text_field($address['google_maps']['neighborhood']) : '',
                    'postal_code'     => isset($address['google_maps']['postal_code']) ? sanitize_text_field($address['google_maps']['postal_code']) : '',
                    'floor'           => isset($address['google_maps']['floor']) ? sanitize_text_field($address['google_maps']['floor']) : '',
                    'landmark'        => isset($address['google_maps']['landmark']) ? sanitize_text_field($address['google_maps']['landmark']) : '',
                    'administrative_area' => isset($address['google_maps']['administrative_area']) ? array(
                        'administrative_area_level_1' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_1'] ?? ''),
                        'administrative_area_level_2' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_2'] ?? ''),
                        'administrative_area_level_3' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_3'] ?? ''),
                        'administrative_area_level_4' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_4'] ?? ''),
                        'administrative_area_level_5' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_5'] ?? ''),
                        'administrative_area_level_6' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_6'] ?? ''),
                        'administrative_area_level_7' => sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_7'] ?? '')
                    ) : array()
                ) : array()
            );
    
            update_field( 'field_place_address', $addressData, $placeId );
        }
    
        // Facilities (ACF group)
        if ( ! empty( $data['facilities'] ) ) {
            $fac = $data['facilities'];
            $facData = array(
                'car_parking' => array(
                    'self_parking'  => isset($fac['car_parking']['self_parking']) ? (bool)$fac['car_parking']['self_parking'] : false,
                    'valet_service' => isset($fac['car_parking']['valet_service']) ? (bool)$fac['car_parking']['valet_service'] : false,
                ),
                'bike_parking' => isset($fac['bike_parking']) ? (bool)$fac['bike_parking'] : false,
            );

            update_field( 'field_place_facilities', $facData, $placeId );
        }
    
        // Open Hours (ACF repeater)
        // The provided structure suggests a different schema than originally defined.
        // Adjusting the logic to what you previously set in ACF:
        if ( ! empty( $data['open_hours'] ) && is_array( $data['open_hours'] ) ) {
            $openHoursData = array();
        
            foreach ( $data['open_hours'] as $dayItem ) {
                $timeSlotData = array();
                
                if ( ! empty( $dayItem['time_slot'] ) && is_array( $dayItem['time_slot'] ) ) {
                    foreach ( $dayItem['time_slot'] as $slot ) {
                        $start = isset($slot['start']) ? sanitize_text_field($this->dt->convertToSystemDT($slot['start'],"H:i")) : '';
                        $end   = isset($slot['end'])   ? sanitize_text_field($this->dt->convertToSystemDT($slot['end'],"H:i"))   : '';
                        
                        $timeSlotData[] = array(
                            'start' => $start,
                            'end'   => $end
                        );
                    }
                }
        
                $openHoursData[] = array(
                    'day'          => isset($dayItem['day']) ? sanitize_text_field($dayItem['day']) : '',
                    'is_available' => isset($dayItem['is_available']) ? (bool)$dayItem['is_available'] : false,
                    'time_slot'    => $timeSlotData
                );
            }
        
            // Update the field using the unique ACF field key for 'open_hours'
            // Replace 'field_place_open_hours' with the actual field key from ACF
            update_field('field_place_open_hours', $openHoursData, $placeId);
        }
        
        // Prepare data for Elasticsearch indexing
        $esData = array(
            'place_id'          => (int)$placeId,
            'org_id'            => !empty($data['org_id']) ? (int)$data['org_id'] : null,
            'type'              => !empty($data['type']) ? sanitize_text_field($data['type']) : '',
            'name'              => !empty($data['name']) ? sanitize_text_field($data['name']) : '',
            'short_description' => !empty($data['short_description']) ? sanitize_text_field($data['short_description']) : '',
            'long_description'  => !empty($data['long_description']) ? wp_kses_post($data['long_description']) : '',
            'address'           => $addressData,
            'facilities'        => $facData,
            'open_hours'        => array()
        );

        // Transform open_hours data to Elasticsearch format
        if (!empty($openHoursData)) {
            $esOpenHours = array();
            foreach ($openHoursData as $oh) {
                $timeSlots = array();
                if (!empty($oh['time_slot'])) {
                    foreach ($oh['time_slot'] as $slot) {
                        // 'start' and 'end' should be directly the time in HH:mm
                        $timeSlots[] = array(
                            'start' => $slot['start'],
                            'end'   => $slot['end']
                        );
                    }
                }

                $esOpenHours[] = array(
                    'day'       => $oh['day'],
                    'is_available' => $oh['is_available'],
                    'time_slot' => $timeSlots
                );
            }
            $esData['open_hours'] = $esOpenHours;
        }

        // Set created_at and updated_at in a suitable format (e.g. ISO8601)
        $now = $this->dt->currentSystemDT(); 
        $esData['created_at'] = $now;
        $esData['updated_at'] = $now;
       
        // Insert document into Elasticsearch
        $esResponse = $this->es->create('places', $esData, 'place-'.$placeId);
        
        if ($esResponse['status_code'] == 201) {
            return $placeId;
        }else{
            // Delete the post if indexing fails
            wp_delete_post($placeId, true);
            return false;
        }
        // Return the newly created post ID or some success message
        return false;
    }
        

    public function getPlace($query, $filter = []) 
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $placeDataResponse = $this->es->read('places', 'place-' . $query['id']);
        } else {
            return false;
        }

        if ($placeDataResponse['status_code'] == 200) {
            $body = $placeDataResponse['body']['_source'];

            $workingHoursArray = [];
            if (!empty($body['open_hours']) && is_array($body['open_hours'])) {
                $daysArray = [];

                foreach ($body['open_hours'] as $oh) {
                    $dayCode = isset($oh['day']) ? $oh['day'] : '';
                    $timeSlots = isset($oh['time_slot']) && is_array($oh['time_slot']) ? $oh['time_slot'] : [];

                    $isAvailable = !empty($timeSlots);
                    $totalWorkingHours = 0.0;
                    $convertedTimeSlots = [];

                    foreach ($timeSlots as $slot) {
                        $startTime = isset($slot['start']) ? $slot['start'] : '';
                        $endTime = isset($slot['end']) ? $slot['end'] : '';

                        // Calculate duration in hours
                        // Assuming start and end are HH:MM format
                        if ($startTime && $endTime) {
                            $startParts = explode(':', $startTime);
                            $endParts = explode(':', $endTime);

                            if (count($startParts) == 2 && count($endParts) == 2) {
                                $startHour = (int)$startParts[0];
                                $startMinute = (int)$startParts[1];
                                $endHour = (int)$endParts[0];
                                $endMinute = (int)$endParts[1];

                                // Compute the difference in hours (simple calculation)
                                $startInMinutes = $startHour * 60 + $startMinute;
                                $endInMinutes = $endHour * 60 + $endMinute;
                                $diffInMinutes = $endInMinutes - $startInMinutes;
                                if ($diffInMinutes > 0) {
                                    $totalWorkingHours += $diffInMinutes / 60.0;
                                }
                            }
                        }

                        // Start Time object
                        $startTimeObject = [
                            'time' => $this->dt->convertToActiveDT($startTime, "H:i"),
                            'timezone' => $this->locale->activeTimezone()
                        ];

                        // End Time object
                        $endTimeObject = [
                            'time' => $this->dt->convertToActiveDT($endTime, "H:i"),
                            'timezone' => $this->locale->activeTimezone()
                        ];

                        $convertedTimeSlots[] = [
                            'start' => $startTimeObject,
                            'end'   => $endTimeObject
                        ];
                    } //time_slotes

                    $daysArray[] = [
                        'day' => $dayCode,
                        'name' => $this->dt->getWeekDays($dayCode),
                        'is_available' => $isAvailable,
                        'working_hours' => $totalWorkingHours,
                        'time_slot' => $isAvailable ? $convertedTimeSlots : [
                            [
                                'start' => ["time" => "", 'timezone' => ""],
                                'end' => ["time" => "", 'timezone' => ""]
                            ],
                            [
                                'start' => ["time" => "", 'timezone' => ""],
                                'end' => ["time" => "", 'timezone' => ""]
                            ]
                        ]
                    ];
                }

                $workingHoursArray = [
                    'resource' => [
                        'type' => 'PLACE',
                        'name' => 'Place'
                    ],
                    'days' => $daysArray
                ];
            }

            $responseData = [
                'id' => $body['place_id'],
                'org' => $this->load->subData('org', 'getOrganization', $body['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => ['id' => 0, 'name' => '']]),
                'type' => $body['type'],
                'name' => $body['name'],
                'short_description' => $body['short_description'],
                'long_description' => $body['long_description'],
                'address' => [
                    'type' => $body['address']['type'],
                    'title' => $body['address']['title'],
                    'formatted_address' => $body['address']['formatted_address'],
                    'address_1' => $body['address']['address_1'],
                    'address_2' => $body['address']['address_2'],
                    'floor' => [
                        'type' => $body['address']['floor']['type'],
                        'number' => $body['address']['floor']['number']
                    ],
                    'landmark' => $body['address']['landmark'],
                    'city' => $body['address']['city'],
                    'state' => [
                        'name' => $body['address']['state']['name'],
                        'code' => $body['address']['state']['code']
                    ],
                    'country' => [
                        'id' => $body['address']['country']['id'],
                        'name' => $body['address']['country']['name'],
                        'code' => $body['address']['country']['code']
                    ],
                    'pin_zip' => $body['address']['pin_zip'],
                    'coordinates' => [
                        'latitude' => $body['address']['coordinates']['lat'],
                        'longitude' => $body['address']['coordinates']['lon']
                    ],
                    'google_maps' => [
                        'place_id' => $body['address']['google_maps']['place_id'],
                        'type' => !empty($body['address']['google_maps']['type']) ? explode(',', $body['address']['google_maps']['type']) : [],
                        'colloquial_area' => $body['address']['google_maps']['colloquial_area'],
                        'locality' => $body['address']['google_maps']['locality'],
                        'sublocality' => [
                            'sublocality_level_1' => $body['address']['google_maps']['sublocality']['sublocality_level_1'],
                            'sublocality_level_2' => $body['address']['google_maps']['sublocality']['sublocality_level_2'],
                            'sublocality_level_3' => $body['address']['google_maps']['sublocality']['sublocality_level_3'],
                            'sublocality_level_4' => $body['address']['google_maps']['sublocality']['sublocality_level_4'],
                            'sublocality_level_5' => $body['address']['google_maps']['sublocality']['sublocality_level_5']
                        ],
                        'neighborhood' => $body['address']['google_maps']['neighborhood'],
                        'postal_code' => $body['address']['google_maps']['postal_code'],
                        'floor' => $body['address']['google_maps']['floor'],
                        'landmark' => $body['address']['google_maps']['landmark'],
                        'administrative_area' => [
                            'administrative_area_level_1' => $body['address']['google_maps']['administrative_area']['administrative_area_level_1'],
                            'administrative_area_level_2' => $body['address']['google_maps']['administrative_area']['administrative_area_level_2'],
                            'administrative_area_level_3' => $body['address']['google_maps']['administrative_area']['administrative_area_level_3'],
                            'administrative_area_level_4' => $body['address']['google_maps']['administrative_area']['administrative_area_level_4'],
                            'administrative_area_level_5' => $body['address']['google_maps']['administrative_area']['administrative_area_level_5'],
                            'administrative_area_level_6' => $body['address']['google_maps']['administrative_area']['administrative_area_level_6'],
                            'administrative_area_level_7' => $body['address']['google_maps']['administrative_area']['administrative_area_level_7']
                        ]
                    ]
                ],
                'facilities' => [
                    'car_parking' => [
                        'self_parking' => $body['facilities']['car_parking']['self_parking'],
                        'valet_service' => $body['facilities']['car_parking']['valet_service']
                    ],
                    'bike_parking' => $body['facilities']['bike_parking']
                ],
                'open_hours' => $workingHoursArray,
                'created_at' => [
                    'time' => $this->dt->convertToActiveDT($body['created_at']),
                    'timezone' => $this->locale->activeTimezone()
                ],
                'updated_at' => [
                    'time' => $this->dt->convertToActiveDT($body['updated_at']),
                    'timezone' => $this->locale->activeTimezone()
                ],
                'classrooms' => $this->load->subData('classroom', 'getClassrooms', ['placeId'=>$body['place_id']], ['key'=>'data'])
            ];
            
            return  $this->schema->validate($responseData, 'Place', $filter);
        }

        return false;
    }
    
    function getPlaces($query, $filter = [])
    {
        $cacheObj = ['class' => __CLASS__, 'method' => __METHOD__, 'query' => $query, 'filter' => $filter];
        $cachedResponse = $this->cache->getCache($cacheObj);
        if ($cachedResponse !== false) {   
            return $cachedResponse;
        }

        if (isset($query['custom'])) {
            $placeCntResponse = $this->es->count('places', $query['custom']);
            if ($placeCntResponse['status_code'] == 200) {
                $placeDataResponse = $this->es->customQuery($query['custom'], 'places', $query['qryStr'] ?? null );
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($placeDataResponse['status_code'] == 200) {
            $responseCount = $placeCntResponse['body']['count'];
            $places = $placeDataResponse['body']['hits']['hits'];
            if ($responseCount > 0 && is_countable($places) && count($places) > 0) {
                $responseData = [];

                foreach ($places as $place) {
                    $body = $place['_source'];
                    
                    $workingHoursArray = [];
                    if (!empty($body['open_hours']) && is_array($body['open_hours'])) {
                        $daysArray = [];

                        foreach ($body['open_hours'] as $oh) {
                            $dayCode = isset($oh['day']) ? $oh['day'] : '';
                            $timeSlots = isset($oh['time_slot']) && is_array($oh['time_slot']) ? $oh['time_slot'] : [];

                            $isAvailable = !empty($timeSlots);
                            $totalWorkingHours = 0.0;
                            $convertedTimeSlots = [];

                            foreach ($timeSlots as $slot) {
                                $startTime = isset($slot['start']) ? $slot['start'] : '';
                                $endTime = isset($slot['end']) ? $slot['end'] : '';

                                // Calculate duration in hours
                                // Assuming start and end are HH:MM format
                                if ($startTime && $endTime) {
                                    $startParts = explode(':', $startTime);
                                    $endParts = explode(':', $endTime);

                                    if (count($startParts) == 2 && count($endParts) == 2) {
                                        $startHour = (int)$startParts[0];
                                        $startMinute = (int)$startParts[1];
                                        $endHour = (int)$endParts[0];
                                        $endMinute = (int)$endParts[1];

                                        // Compute the difference in hours (simple calculation)
                                        $startInMinutes = $startHour * 60 + $startMinute;
                                        $endInMinutes = $endHour * 60 + $endMinute;
                                        $diffInMinutes = $endInMinutes - $startInMinutes;
                                        if ($diffInMinutes > 0) {
                                            $totalWorkingHours += $diffInMinutes / 60.0;
                                        }
                                    }
                                }

                                // Start Time object
                                $startTimeObject = [
                                    'time' => $this->dt->convertToActiveDT($startTime, "H:i"),
                                    'timezone' => $this->locale->activeTimezone()
                                ];

                                // End Time object
                                $endTimeObject = [
                                    'time' => $this->dt->convertToActiveDT($endTime, "H:i"),
                                    'timezone' => $this->locale->activeTimezone()
                                ];

                                $convertedTimeSlots[] = [
                                    'start' => $startTimeObject,
                                    'end'   => $endTimeObject
                                ];
                            } //time_slotes

                            $daysArray[] = [
                                'day' => $dayCode,
                                'name' => $this->dt->getWeekDays($dayCode),
                                'is_available' => $isAvailable,
                                'working_hours' => $totalWorkingHours,
                                'time_slot' => $isAvailable ? $convertedTimeSlots : [
                                    [
                                        'start' => ["time" => "", 'timezone' => ""],
                                        'end' => ["time" => "", 'timezone' => ""]
                                    ],
                                    [
                                        'start' => ["time" => "", 'timezone' => ""],
                                        'end' => ["time" => "", 'timezone' => ""]
                                    ]
                                ]
                            ];
                        }

                        $workingHoursArray = [
                            'resource' => [
                                'type' => 'PLACE',
                                'name' => 'Place'
                            ],
                            'days' => $daysArray
                        ];
                    }
                    
                    $responseData[] = [
                        'id' => $body['place_id'],
                        'org' => $this->load->subData('org', 'getOrganization', $body['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => true]),
                        'type' => $body['type'],
                        'name' => $body['name'],
                        'short_description' => $body['short_description'],
                        'long_description' => $body['long_description'],
                        'address' => [
                            'type' => $body['address']['type'],
                            'title' => $body['address']['title'],
                            'formatted_address' => $body['address']['formatted_address'],
                            'address_1' => $body['address']['address_1'],
                            'address_2' => $body['address']['address_2'],
                            'floor' => [
                                'type' => $body['address']['floor']['type'],
                                'number' => $body['address']['floor']['number']
                            ],
                            'landmark' => $body['address']['landmark'],
                            'city' => $body['address']['city'],
                            'state' => [
                                'name' => $body['address']['state']['name'],
                                'code' => $body['address']['state']['code']
                            ],
                            'country' => [
                                'id' => $body['address']['country']['id'],
                                'name' => $body['address']['country']['name'],
                                'code' => $body['address']['country']['code']
                            ],
                            'pin_zip' => $body['address']['pin_zip'],
                            'coordinates' => [
                                'lat' => $body['address']['coordinates']['lat'],
                                'lon' => $body['address']['coordinates']['lon']
                            ],
                            'google_maps' => [
                                'place_id' => $body['address']['google_maps']['place_id'],
                                'type' => !empty($body['address']['google_maps']['type']) ? explode(',', $body['address']['google_maps']['type']) : [],
                                'colloquial_area' => $body['address']['google_maps']['colloquial_area'],
                                'locality' => $body['address']['google_maps']['locality'],
                                'sublocality' => [
                                    'sublocality_level_1' => $body['address']['google_maps']['sublocality']['sublocality_level_1'],
                                    'sublocality_level_2' => $body['address']['google_maps']['sublocality']['sublocality_level_2'],
                                    'sublocality_level_3' => $body['address']['google_maps']['sublocality']['sublocality_level_3'],
                                    'sublocality_level_4' => $body['address']['google_maps']['sublocality']['sublocality_level_4'],
                                    'sublocality_level_5' => $body['address']['google_maps']['sublocality']['sublocality_level_5']
                                ],
                                'neighborhood' => $body['address']['google_maps']['neighborhood'],
                                'postal_code' => $body['address']['google_maps']['postal_code'],
                                'floor' => $body['address']['google_maps']['floor'],
                                'landmark' => $body['address']['google_maps']['landmark'],
                                'administrative_area' => [
                                    'administrative_area_level_1' => $body['address']['google_maps']['administrative_area']['administrative_area_level_1'],
                                    'administrative_area_level_2' => $body['address']['google_maps']['administrative_area']['administrative_area_level_2'],
                                    'administrative_area_level_3' => $body['address']['google_maps']['administrative_area']['administrative_area_level_3'],
                                    'administrative_area_level_4' => $body['address']['google_maps']['administrative_area']['administrative_area_level_4'],
                                    'administrative_area_level_5' => $body['address']['google_maps']['administrative_area']['administrative_area_level_5'],
                                    'administrative_area_level_6' => $body['address']['google_maps']['administrative_area']['administrative_area_level_6'],
                                    'administrative_area_level_7' => $body['address']['google_maps']['administrative_area']['administrative_area_level_7']
                                ]
                            ]
                        ],
                        'facilities' => [
                            'car_parking' => [
                                'self_parking' => $body['facilities']['car_parking']['self_parking'],
                                'valet_service' => $body['facilities']['car_parking']['valet_service']
                            ],
                            'bike_parking' => $body['facilities']['bike_parking']
                        ],
                        'open_hours' => $workingHoursArray,
                        'created_at' => [
                            'time' => $this->dt->convertToActiveDT($body['created_at']),
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'updated_at' => [
                            'time' => $this->dt->convertToActiveDT($body['updated_at']),
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'classrooms' => $this->load->subData('classroom', 'getClassrooms', ['placeId'=>$body['place_id']], ['key'=>'data', 'noResponse' => ['Refer#Classroom']])
                    ];

                }

                if(isset($filter['schema'])){
                    $filter['schema'] = ['count'=>'integer','data'=>[$filter['schema']]];
                }
                
                $processedResponse = $this->schema->validate(['count'=>$responseCount,'data'=>$responseData], ['count'=>'integer','data'=>['Refer#Place']], $filter);
                $this->cache->setCache($cacheObj, $processedResponse);
                return $processedResponse;
            }
        }

        return false;
    }

    public function updPlace($placeId, $data) 
    {
        // Make sure $placeId is valid and the post exists
        $post = get_post($placeId);
        if ( ! $post || 'place' !== get_post_type($placeId) ) {
            return false; // Invalid post ID or not a place
        }
    
        // Prepare updated post data
        $postData = array(
            'ID'           => $placeId,
            'post_title'   => isset($data['name']) ? sanitize_text_field($data['name']) : 'Untitled Place',
            'post_content' => isset($data['long_description']) ? wp_kses_post($data['long_description']) : '',
            'post_excerpt' => isset($data['short_description']) ? sanitize_text_field($data['short_description']) : '',
        );
    
        // Update the post
        $updatedId = wp_update_post( $postData );
        if ( is_wp_error( $updatedId ) ) {
            return false; // return the error
        }
    
        // Update ACF fields
        // org_id
        if ( ! empty( $data['org_id'] ) ) {
            update_field( 'field_place_org_id', sanitize_text_field($data['org_id']), $placeId );
        }
    
        // type
        if ( ! empty( $data['type'] ) ) {
            update_field( 'field_place_type', sanitize_text_field($data['type']), $placeId );
        }
    
        // Address
        if ( ! empty( $data['address'] ) && is_array($data['address']) ) {
            $address = $data['address'];
            $addressData = array(
                'type'              => isset($address['type']) ? sanitize_text_field($address['type']) : '',
                'title'            => isset($address['title']) ? sanitize_text_field($address['title']) : '',
                'formatted_address' => isset($address['formatted_address']) ? sanitize_text_field($address['formatted_address']) : '',
                'address_1'        => isset($address['address_1']) ? sanitize_text_field($address['address_1']) : '',
                'address_2'        => isset($address['address_2']) ? sanitize_text_field($address['address_2']) : '',
                'floor'            => isset($address['floor']) && is_array($address['floor']) ? array(
                    'type'   => isset($address['floor']['type']) ? sanitize_text_field($address['floor']['type']) : '',
                    'number' => isset($address['floor']['number']) ? sanitize_text_field($address['floor']['number']) : 0
                ) : array(),
                'landmark'         => isset($address['landmark']) ? sanitize_text_field($address['landmark']) : '',
                'city'            => isset($address['city']) ? sanitize_text_field($address['city']) : '',
                'state'           => isset($address['state']) && is_array($address['state']) ? array(
                    'name' => isset($address['state']['name']) ? sanitize_text_field($address['state']['name']) : '',
                    'code' => isset($address['state']['code']) ? sanitize_text_field($address['state']['code']) : ''
                ) : array(),
                'country'         => isset($address['country']) && is_array($address['country']) ? array(
                    'id'   => isset($address['country']['id']) ? intval($address['country']['id']) : 0,
                    'name' => isset($address['country']['name']) ? sanitize_text_field($address['country']['name']) : '',
                    'code' => isset($address['country']['code']) ? sanitize_text_field($address['country']['code']) : ''
                ) : array(),
                'pin_zip'         => isset($address['pin_zip']) ? sanitize_text_field($address['pin_zip']) : '',
                'coordinates'     => isset($address['coordinates']) && is_array($address['coordinates']) ? array(
                    'lat'  => floatval($address['coordinates']['latitude']),
                    'lon' => floatval($address['coordinates']['longitude'])
                ) : array(),
                'google_maps'     => isset($address['google_maps']) && is_array($address['google_maps']) ? array(
                    'place_id'         => sanitize_text_field($address['google_maps']['place_id']),
                    'type'            => is_array($address['google_maps']['type']) ? implode(',', array_map('sanitize_text_field', $address['google_maps']['type'])) : sanitize_text_field($address['google_maps']['type'] ?? ''),
                    'colloquial_area' => isset($address['google_maps']['colloquial_area']) ? sanitize_text_field($address['google_maps']['colloquial_area']) : '',
                    'locality'        => isset($address['google_maps']['locality']) ? sanitize_text_field($address['google_maps']['locality']) : '',
                    'sublocality'     => isset($address['google_maps']['sublocality']) && is_array($address['google_maps']['sublocality']) ? array(
                        'sublocality_level_1' => isset($address['google_maps']['sublocality']['sublocality_level_1']) ? sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_1']) : '',
                        'sublocality_level_2' => isset($address['google_maps']['sublocality']['sublocality_level_2']) ? sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_2']) : '',
                        'sublocality_level_3' => isset($address['google_maps']['sublocality']['sublocality_level_3']) ? sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_3']) : '',
                        'sublocality_level_4' => isset($address['google_maps']['sublocality']['sublocality_level_4']) ? sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_4']) : '',
                        'sublocality_level_5' => isset($address['google_maps']['sublocality']['sublocality_level_5']) ? sanitize_text_field($address['google_maps']['sublocality']['sublocality_level_5']) : ''
                    ) : array(),
                    'neighborhood'     => isset($address['google_maps']['neighborhood']) ? sanitize_text_field($address['google_maps']['neighborhood']) : '',
                    'postal_code'     => isset($address['google_maps']['postal_code']) ? sanitize_text_field($address['google_maps']['postal_code']) : '',
                    'floor'           => isset($address['google_maps']['floor']) ? sanitize_text_field($address['google_maps']['floor']) : '',
                    'landmark'        => isset($address['google_maps']['landmark']) ? sanitize_text_field($address['google_maps']['landmark']) : '',
                    'administrative_area' => isset($address['google_maps']['administrative_area']) && is_array($address['google_maps']['administrative_area']) ? array(
                        'administrative_area_level_1' => isset($address['google_maps']['administrative_area']['administrative_area_level_1']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_1']) : '',
                        'administrative_area_level_2' => isset($address['google_maps']['administrative_area']['administrative_area_level_2']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_2']) : '',
                        'administrative_area_level_3' => isset($address['google_maps']['administrative_area']['administrative_area_level_3']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_3']) : '',
                        'administrative_area_level_4' => isset($address['google_maps']['administrative_area']['administrative_area_level_4']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_4']) : '',
                        'administrative_area_level_5' => isset($address['google_maps']['administrative_area']['administrative_area_level_5']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_5']) : '',
                        'administrative_area_level_6' => isset($address['google_maps']['administrative_area']['administrative_area_level_6']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_6']) : '',
                        'administrative_area_level_7' => isset($address['google_maps']['administrative_area']['administrative_area_level_7']) ? sanitize_text_field($address['google_maps']['administrative_area']['administrative_area_level_7']) : ''
                    ) : array()
                ) : array()
            );
            update_field( 'field_place_address', $addressData, $placeId );
        }
    
        // Facilities
        if ( ! empty( $data['facilities'] ) && is_array($data['facilities']) ) {
            $fac = $data['facilities'];
            $facData = array(
                'car_parking' => array(
                    'self_parking'  => isset($fac['car_parking']['self_parking']) ? (bool)$fac['car_parking']['self_parking'] : false,
                    'valet_service' => isset($fac['car_parking']['valet_service']) ? (bool)$fac['car_parking']['valet_service'] : false,
                ),
                'bike_parking' => isset($fac['bike_parking']) ? (bool)$fac['bike_parking'] : false,
            );
            update_field( 'field_place_facilities', $facData, $placeId );
        }
    
        // Open Hours
        if ( ! empty( $data['open_hours'] ) && is_array( $data['open_hours'] ) ) {
            $openHoursData = array();
        
            foreach ( $data['open_hours'] as $dayItem ) {
                if (!is_array($dayItem)) continue;
                
                $timeSlotData = array();
                
                if ( ! empty( $dayItem['time_slot'] ) && is_array( $dayItem['time_slot'] ) ) {
                    foreach ( $dayItem['time_slot'] as $slot ) {
                        if (!is_array($slot)) continue;
                        
                        $start = isset($slot['start']) ? sanitize_text_field($this->dt->convertToSystemDT($slot['start'],"H:i")) : '';
                        $end   = isset($slot['end'])   ? sanitize_text_field($this->dt->convertToSystemDT($slot['end'],"H:i"))   : '';
                        
                        $timeSlotData[] = array(
                            'start' => $start,
                            'end'   => $end
                        );
                    }
                }
        
                $openHoursData[] = array(
                    'day'          => isset($dayItem['day']) ? sanitize_text_field($dayItem['day']) : '',
                    'is_available' => isset($dayItem['is_available']) ? (bool)$dayItem['is_available'] : false,
                    'time_slot'    => $timeSlotData
                );
            }
        
            update_field('field_place_open_hours', $openHoursData, $placeId);
        }
        
        // Prepare data for Elasticsearch indexing
        $esData = array(
            'place_id'          => (int)$placeId,
            'org_id'            => !empty($data['org_id']) ? (int)$data['org_id'] : null,
            'type'              => !empty($data['type']) ? sanitize_text_field($data['type']) : '',
            'name'              => !empty($data['name']) ? sanitize_text_field($data['name']) : '',
            'short_description' => !empty($data['short_description']) ? sanitize_text_field($data['short_description']) : '',
            'long_description'  => !empty($data['long_description']) ? wp_kses_post($data['long_description']) : '',
            'address'           => isset($addressData) ? $addressData : array(),
            'facilities'        => isset($facData) ? $facData : array(),
            'open_hours'        => array()
        );

        if (!empty($openHoursData)) {
            $esOpenHours = array();
            foreach ($openHoursData as $oh) {
                if (!is_array($oh)) continue;
                
                $timeSlots = array();
                if (!empty($oh['time_slot']) && is_array($oh['time_slot'])) {
                    foreach ($oh['time_slot'] as $slot) {
                        if (!is_array($slot)) continue;
                        
                        $timeSlots[] = array(
                            'start' => isset($slot['start']) ? $slot['start'] : '',
                            'end'   => isset($slot['end']) ? $slot['end'] : ''
                        );
                    }
                }

                $esOpenHours[] = array(
                    'day'       => isset($oh['day']) ? $oh['day'] : '',
                    'is_available' => isset($oh['is_available']) ? $oh['is_available'] : false,
                    'time_slot' => $timeSlots
                );
            }
            $esData['open_hours'] = $esOpenHours;
        }

        // Set updated_at (and optionally created_at if needed)
        // Assuming this method returns an ISO8601 compatible datetime
        $now = $this->dt->currentSystemDT(); 
        // If we have a stored created_at, we could retrieve it. Otherwise, set it if needed.
        // For simplicity, let's update updated_at only
        $esData['updated_at'] = $now;

        // Insert/update document in Elasticsearch
        $esResponse = $this->es->update('places', 'place-'.$placeId, $esData);
        
        if($esResponse['status_code'] == 200) {
            return $placeId; // Elasticsearch update failed
        }
        // Return the post ID to confirm successful update
        return false;
    }
    

    public function delPlace($placeId) 
    {
        $this->loadModel('classroom');

        // Delete the corresponding Elasticsearch document
        $this->es->delete('places', 'place-'.$placeId);

        $classroomsData = $this->classroomModel->getClassrooms(['custom'=>['query'=>['term'=>['place_id'=>$placeId]]]],['schema'=>'Refer#Classroom_Minimal']);

        if($classroomsData!==false && $classroomsData['count']>0){
            foreach($classroomsData['data'] as $classroom){
                $this->classroomModel->delClassroom($classroom['id']);
            }
        }

        // Ensure the provided ID is valid and corresponds to a place post
        $post = get_post($placeId);
        if (!$post || 'place' !== get_post_type($placeId)) {
            return false; // Invalid place ID or not a place
        }
        // Attempt to delete the post
        $result = wp_delete_post($placeId, true); // `true` forces permanent deletion

        if ($result === false) {
            return false; // Deletion failed
        }

        return true; // Deletion successful
    }
}
