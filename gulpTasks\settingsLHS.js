const gulp = require('gulp'),
	gp_concat = require('gulp-concat'),
 	gp_rename = require('gulp-rename'),
    terser = require('terser');
	gulpTerser = require('gulp-terser'),
	uglifycss = require('gulp-uglifycss');

gulp.task('settingsLHSJS', function () {
    const fileName = "settingsLHS",
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/assets/js/lib/lodash.min.js',
            ''+ path +'/components/pageGrid/*.js',
            ''+ path +'/components/table/table.js',
            ''+ path +'/components/tabsV2/*.js',
            ''+ path +'/components/notifications/notifications.js',
            ''+ path +'/components/address/address.js',
            ''+ path +'/components/account/account.js',
            ''+ path +'/components/availabilityV3/preview.js',
            ''+ path +'/components/availabilityV3/availabilityV3.js',
            ''+ path +'/components/headerWithLeftMenu/*.js',
            ''+ path +'/components/courseSearchBar/*.js',
            ''+ path +'/components/resourceSearch/*.js',
            ''+ path +'/components/previewAvailability/previewAvailability.js',
            ''+ path +'/pages/'+ fileName +'/*.js'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.js`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}Combined.js`))
    .pipe(gulp.dest(saveToFolder))
    .pipe(gp_rename(renameFile))
    .pipe(gulpTerser({}, terser.minify))
    .pipe(gulp.dest(saveToFolder));
});

gulp.task('settingsLHSCSS', function () {
    const fileName = "settingsLHS",
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/components/pageGrid/*.css',
            ''+ path +'/components/headerWithLeftMenu/headerWithLeftMenu.css',
            ''+ path +'/components/courseSearchBar/*.css',
            ''+ path +'/components/table/table.css',
            ''+ path +'/components/tabs/tabs.css',
            ''+ path +'/components/notifications/notifications.css',
            ''+ path +'/components/address/address.css',
            ''+ path +'/components/account/account.css',
            ''+ path +'/components/availabilityV3/availabilityV3.css',
            ''+ path +'/components/resourceSearch/*.css',
            ''+ path +'/components/previewAvailability/previewAvailability.css',
            ''+ path +'/components/courseSearchBar/*.css',
            ''+ path +'/components/tabsV2/*.css',
            ''+ path +'/assets/scss/commonV2.css',
            ''+ path +'/pages/'+ fileName +'/*.css',
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.css`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}.min.css`))
    .pipe(uglifycss())
    .pipe(gulp.dest(saveToFolder))
});

gulp.task('settingsLHS', function() {
    const pages = "wp-content/themes/yunolearning-child/pages/",
        components = "wp-content/themes/yunolearning-child/components/",
        assets = "wp-content/themes/yunolearning-child/assets/";

    gulp.watch([
        ''+ components +'pageGrid/*.js',
        ''+ components +'tabsV2/*.js',
        ''+ components +'headerWithLeftMenu/*.js',
        ''+ components +'courseSearchBar/*.js',
        ''+ components + 'availabilityV3/*.js',
        ''+ components +'previewAvailability/*.js',
        ''+ components +'resourceSearch/*.js',
        ''+ pages +'settingsLHS/*.js'
    ], gulp.series('settingsLHSJS'));

    gulp.watch([
        ''+ assets +'scss/commonV2.css',
        ''+ components +'tabsV2/*.css',
        ''+ components +'pageGrid/*.css',
        ''+ components +'courseSearchBar/*.css',
        ''+ components +'headerWithLeftMenu/headerWithLeftMenu.css',
        ''+ components + 'availabilityV3/*.css',
        ''+ components +'previewAvailability/*.css',
        ''+ components +'resourceSearch/*.css',
        ''+ pages +'settingsLHS/*.css'
    ], gulp.series('settingsLHSCSS'));
});
