Vue.component('yuno-batches', {
    props: ["data", "options"],
    template: `
        <section class="batchesWrapper">
            <section class="filtersWrapper">
                <ul>
                    <template v-for="(filter, i) in options.filters">
                        <template v-if="filter.type === 'dropdown'">
                            <li 
                                :key="i"
                                v-if="filter.is_active"
                                class="filter">
                                <b-dropdown
                                    :key="i"
                                    v-model="filter.selected"
                                    :multiple="false"
                                    aria-role="list"
                                    :disabled="filter.is_disabled"
                                    @change="onFilterChange($event, filter)"
                                    class="filterMenu"
                                    :class="[filter.is_highlighted ? 'active' : '']">
                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">
                                        <template v-if="filter.selected === ''">
                                            <span>{{filter.placeholder}}</span>
                                        </template>
                                        <template v-else>
                                            <span>{{filter.selected.label}}</span>
                                        </template>
                                        <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                    </button>
                                    <template v-for="(item, j) in filter.items">
                                        <b-dropdown-item 
                                            @click="onFilterItemSelect(item, filter)"
                                            :class="[item.slug]"
                                            :value="item"
                                            :key="item.slug + '-' + j"
                                            aria-role="listitem">
                                            <span class="itemLabel">{{item.label}}</span>
                                        </b-dropdown-item>
                                    </template>
                                </b-dropdown>
                            </li>
                        </template>
                        <template v-if="filter.type === 'groupMultiSelect'">
                            <li 
                                :key="i"
                                v-if="filter.is_active"
                                class="filter">
                                <b-dropdown
                                    :key="i"
                                    v-model="filter.selected"
                                    ref="groupMultiSelect"
                                    :multiple="true"
                                    aria-role="list"
                                    :disabled="filter.is_disabled"
                                    @change="onFilterChange($event, filter)"
                                    class="filterMenu"
                                    :class="[filter.is_highlighted ? 'active' : '']">
                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">
                                        <span>{{filter.placeholder}}</span>
                                        <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                    </button>
                                    <template v-for="(item, j) in filter.items">
                                        <div class="filterSet" :class="[item.slug]">
                                            <p :key="item.slug + '-' + j" class="listCaption">
                                                {{item.label}}
                                            </p>
                                            <div class="innerWrapper">
                                                <template v-for="(subItem, l) in item.items">
                                                    <b-dropdown-item 
                                                        @click="onFilterItemSelect(subItem, filter)"
                                                        :class="[subItem.slug]"
                                                        :value="subItem.slug"
                                                        :key="item.slug + '-sub-' + l"
                                                        aria-role="listitem">
                                                        <span class="itemLabel">{{subItem.label}}</span>
                                                    </b-dropdown-item>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="ctaWrapper">
                                        <b-button class="noBorder" @click="clearMe(filter)">Clear All</b-button>
                                        <b-button class="yunoSecondaryCTA" @click="applyFilter(filter)">Apply</b-button>
                                    </div>
                                </b-dropdown>
                            </li>
                        </template>
                    </template>
                </ul>
            </section>
            <template v-if="data.loading">
                <div class="batches">
                    <article class="batchCard" v-for="i in loadingResult" :key="i">
                        <div class="wrapper">
                            <h4 class="batchdate"><b-skeleton active></b-skeleton></h4>
                            <ul class="scheduleInfo">
                                <li>
                                    <b-skeleton active width="50%"></b-skeleton>
                                </li>
                            </ul>
                            <figure class="mappedInstructor">
                                <div class="imgWrapper">
                                    <b-skeleton circle width="44px" height="44px"></b-skeleton>
                                </div>
                                <figcaption>
                                    <h3 class="insName"><b-skeleton active></b-skeleton></h3>
                                </figcaption>
                            </figure>
                        </div>
                    </article>
                </div>
            </template>
            <template v-if="data.success">
                <template v-if="data.error">
                    <p>{{ data.errorData }}</p>
                </template>
                <template v-else>
                    <p class="batchesCount">{{ options.batchesOptions.batch_count }}</p>
                    <div class="batches">
                        <article class="batchCard" v-for="(batch, i) in data.data" :key="i">
                            <div class="wrapper">
                                <h4 class="batchdate"><span class="material-icons">schedule</span>{{ batch.batch_start_time }}</h4>
                                <ul class="days">
                                    <li 
                                        v-for="(day, j) in batch.class_days" 
                                        :key="'day-' + j"
                                        :class="[day.is_available ? 'isActive' : '']">
                                        {{ day.label }}
                                    </li>
                                </ul>
                                <ul class="scheduleInfo">
                                    <li>
                                        <span class="material-icons-outlined">event</span>
                                        <span class="itemCaption">{{ batch.starts_on }} {{ batch.ends_on }}</span>
                                    </li>
                                    <li class="hasGrid">
                                        <div class="hasBG">
                                            <span class="material-icons-outlined">groups</span>
                                            <b-tooltip :label="tooltipLabel(batch)"
                                                type="is-dark"
                                                :multilined="true"
                                                position="is-top">
                                                Group classes
                                            </b-tooltip>
                                        </div>
                                        <span class="itemCaption">
                                            <b-tooltip label="Live classes will happen through the Zoom app. You can attend from your laptop or mobile device"
                                                type="is-dark"
                                                :multilined="true"
                                                position="is-top">
                                                | Online
                                            </b-tooltip>
                                        </span>
                                    </li>
                                </ul>
                                <figure class="mappedInstructor">
                                    <div class="imgWrapper">
                                        <img width="44" height="44" :src="batch.instructor.image" :alt="batch.instructor.name">
                                    </div>
                                    <figcaption>
                                        <h3 class="insName"><a :href="batch.instructor.profile_url" target="_blank">{{batch.instructor.name}}</a></h3>
                                        <div class="insRating" v-if="batch.instructor.rating !== 0">
                                            <span class="material-icons">star</span>
                                            <span class="caption">{{ batch.instructor.rating }} of 5</span>
                                        </div>
                                    </figcaption>
                                    <small class="studentCount">{{ batch.successful_enrollment }} successful students</small>
                                </figure>
                                <ul class="cardFooter">
                                    <li class="price">
                                        <template v-if="batch.price === 0 && batch.subscription.length === 0">
                                            {{"Free"}}
                                        </template>
                                        <template v-else-if="batch.price === 0 && batch.subscription.length !== 0">
                                            ₹{{batch.subscription[0].value}}
                                        </template>
                                        <template v-else>
                                            ₹{{batch.price}}
                                        </template>
                                    </li>
                                    <li class="ctaWrapper"><b-button @click="changeBatch(batch)" class="yunoSecondaryCTA">Change to this</b-button></li>
                                </ul>
                            </div>
                        </article>
                    </div>
                </template>
            </template>
        </section>
    `,
    data() {
        return {
            loadingResult: 3
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        tooltipLabel(data) {
            return "You'll be one of the maximum of "+ data.max_seats +" students in the class"
        },
        changeBatch(data) {
            Event.$emit('changeBatch', data);
        },
        clearMe(filter) {
            filter.selected = [];
            filter.current = [];
        },
        applyFilter(filter) {
            this.$refs.groupMultiSelect[0].isActive = false;
            Event.$emit('onApplyFilter', filter);
        },
        onFilterChange(e, filter) {
            Event.$emit('onFilterChange', e, filter);
        },
        onFilterItemSelect(data, filter) {
            if (data.filter === "class_days_time") {
                const option = YUNOCommon.findInArray(filter.selected, data.slug);

                if (option) {
                    filter.current.push(data)
                } else {
                    YUNOCommon.removeObjInArr(filter.current, "slug", data.slug); 
                }
            };
            
            Event.$emit('onFilterItemSelect', data, filter);
        }
    }
});