<?php

namespace V4;

use WP_Query;

/**
 * Class EnrollmentModel
 * Handles enrollment-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class EnrollmentModel extends Model
{
    /**
     * Constructor for EnrollmentModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('response');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadLibary('cache');
    }

    /**
     * Retrieves enrollment data based on the provided query.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or ID.
     * @param array $filter Additional filters for response formatting.
     * @return array|false Returns formatted enrollment data or false if not found.
     * <AUTHOR>
     */
    public function getEnrollment($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->read('batchenrollmentevent', $query['id']);
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollment = $enrollmentDataResponse['body']['_source']['data']['details'];
            } else {
                return false;
            }
        } elseif (isset($query['custom'])) {
            $enrollmentDataResponse = $this->es->customQuery($query['custom'], 'batchenrollmentevent');
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollment = $enrollmentDataResponse['body']['hits']['hits'][0]['_source']['data']['details'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        if (isset($enrollment)) {
            $this->loadModel('locale');
            $sellingQuery = [
                'ccCode' => $enrollment['selling_price']['currency_code'] ?? '',
                'inclusiveTax' => $enrollment['selling_price']['inclusive_tax'] ?? 0,
                'enclusiveTax' => $enrollment['selling_price']['inclusive_tax'] ?? 0,
                'taxPer' => $enrollment['selling_price']['tax']['percentage'] ?? 0,
                'taxAmt' => $enrollment['selling_price']['tax']['amount'] ?? 0
            ];
            $listingQuery = [
                'ccCode' => $enrollment['listing_price']['currency_code'] ?? '',
                'inclusiveTax' => $enrollment['listing_price']['inclusive_tax'] ?? 0,
                'enclusiveTax' => $enrollment['listing_price']['inclusive_tax'] ?? 0,
                'taxPer' => $enrollment['listing_price']['tax']['percentage'] ?? 0,
                'taxAmt' => $enrollment['selling_price']['tax']['amount'] ?? 0
            ];
            //get payment of enrollment
            $paymentQuery['custom'] = [
                "query" => [
                    "terms" => [
                        "data.details.enrollment_id" => $enrollment['enrollment_id'] // Use the keyword subfield
                    ]
                ]
            ];
            // Build the structured response for enrollment
            $enrollmentResponse = array(
                'id' => $enrollment['enrollment_id'] ?? '',  // Unique ID for the enrollment
                'is_active' => $enrollment['enrollment_status'] === 'ACTIVE' ? true : false,  // Status of the enrollment
                'status' => $enrollment['enrollment_status'] ?? '',
                'start_date' => array(
                    'time' => $this->dt->convertToSystemDT($enrollment['enrolled_on']) ?? '',  // Start date // DateTime Missing
                    'timezone' => $this->locale->activeTimezone()  // Timezone 
                ), // for future
                'end_date' => array(
                    'time' => $this->dt->convertToSystemDT($enrollment['enrolled_end']) ?? '',
                    'timezone' => $this->locale->activeTimezone()  // Timezone
                ), // for future
                'days_left' => $enrollment['enrolled_days_left'] ?? '',  // Days left
                'learner' => $this->load->subData("user", "getUser", $enrollment['user_id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]),
                'enrolled_by' => $this->load->subData("user", "getUser", $enrollment['enrolled_by'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]),
                'counselor' => $this->load->subData("counselor", "getCounselor", $enrollment['counselor_id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]),
                'batch' =>  $this->load->subData("batch", "getBatch", $enrollment['batch_id'], ['schema' => 'Batch_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                'instructor' => $this->load->subData("user", "getUser", $enrollment['instructor_id'] ?? 0, ['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']),
                'course' => $this->load->subData("course", "getCourse", $enrollment['course_id'], ['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                'academy' => $this->load->subData("course", "getCourse", $enrollment['course_id'], ['key' => 'academy', 'noResponse' => 'Academy_Minimal']),
                'of_org' => $this->load->subData("org", "getOrganization", $enrollment['org_admin']['id'], ['schema' => 'Organization_Minimal', 'noResponse' => ['id' => 0, 'name' => '']]),
                'selling_price' => $this->load->subData("invoice", "displayPrice", $sellingQuery, ['noResponse' => 'Price']),
                'list_price' => $this->load->subData("invoice", "displayPrice", $listingQuery, ['noResponse' => 'Price_List']),
                'full_part' => array(
                    'type' => $enrollment['full_part']['type'] ?? '',  // Payment type (FULLPAYMENT or PARTPAYMENT)
                    'total_installments' =>  $enrollment['full_part']['total_installments'] ?? ''  // Total installments (if part payment)
                ),
                'in_crm' => $this->load->subData("user", "getUser", $enrollment['user_id'], ['key' => 'in_crm', 'noResponse' => ['platform' => '', 'id' => '']]),
                'referral' => ['referrer' => $this->load->subData("user", "getUser", $enrollment['refer']['referrer']['id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']])],
                'payment' => $this->load->subData("payment", "getPayment", $paymentQuery, ['noResponse' => 'Payment_Minimal']),
                'classes' => [
                    'attended' => $enrollment['attended_classes_counter'] ?? 0,
                    'total' => $enrollment['enrolled_classes_counter'] ?? 0
                ],
                'attendance' => [
                    'percentage' =>  $enrollment['attendance_percentage'] ?? '',
                    'status' => ''
                ],
                'is_change_batch' => $enrollment['is_change_batch'] ?? false,
                'is_unenroll' => $enrollment['is_unenroll'] ?? false
            );

            // Validate the formatted response using the 'Enrollment' schema
            return $this->schema->validate($enrollmentResponse, 'Enrollment', $filter);
        }
        return false;
    }

    /**
     * Retrieves multiple enrollments based on a query.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Query parameters.
     * @param array $filter Additional filters for response formatting.
     * @return array|false Returns formatted enrollment list or false if not found.
     * <AUTHOR>
     */
    public function getEnrollments($query, $filter = [])
    {

        $cacheObj = ['class' => __CLASS__, 'method' => __METHOD__, 'query' => $query, 'filter' => $filter];
        $cachedResponse = $this->cache->getCache($cacheObj);
        if ($cachedResponse !== false) {
            return $cachedResponse;
        }

        if (isset($query['custom'])) {
            $enrollmentCntResponse = $this->es->count('batchenrollmentevent', $query['custom']);
            if ($enrollmentCntResponse['status_code'] == 200) {
                $enrollmentDataResponse = $this->es->customQuery($query['custom'], 'batchenrollmentevent', $query['qryStr']);
                if ($enrollmentDataResponse['status_code'] == 200) {
                    $enrollments = $enrollmentDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        if (count($enrollments)) {
            $this->loadModel('locale');
            $responseCount = $enrollmentCntResponse['body']['count'];
            foreach ($enrollments as $enrollment) {
                // Assign the repeated part to a variable
                $details = $enrollment['_source']['data']['details'];
                $sellingQuery = [
                    'ccCode' => $details['selling_price']['currency_code'] ?? '',
                    'inclusiveTax' => $details['selling_price']['inclusive_tax'] ?? 0,
                    'enclusiveTax' => $details['selling_price']['inclusive_tax'] ?? 0,
                    'taxPer' => $details['selling_price']['tax']['percentage'] ?? 0,
                    'taxAmt' => $details['selling_price']['tax']['amount'] ?? 0
                ];
                $listingQuery = [
                    'ccCode' => $details['listing_price']['currency_code'] ?? '',
                    'inclusiveTax' => $details['listing_price']['inclusive_tax'] ?? 0,
                    'enclusiveTax' => $details['listing_price']['inclusive_tax'] ?? 0,
                    'taxPer' => $details['listing_price']['tax']['percentage'] ?? 0,
                    'taxAmt' => $details['selling_price']['tax']['amount'] ?? 0
                ];
                //get payment of enrollment
                $paymentQuery['custom'] = [
                    "query" => [
                        "terms" => [
                            "data.details.enrollment_id" => $details['enrollment_id'] // Use the keyword subfield
                        ]
                    ]
                ];
                $enrollmentsResponse[]  = array(
                    'id' => $details['enrollment_id'] ?? '',  // Unique ID for the enrollment
                    'is_active' => $details['enrollment_status'] === 'ACTIVE' ? true : false,  // Status of the enrollment
                    'status' => $details['enrollment_status'] ?? '',
                    'start_date' => array(
                        'time' => $this->dt->convertToSystemDT($details['enrolled_on']) ?? '',  // Start date // DateTime Missing
                        'timezone' => $this->locale->activeTimezone()  // Timezone 
                    ), // for future
                    'end_date' => array(
                        'time' => $this->dt->convertToSystemDT($details['enrolled_end']) ?? '',
                        'timezone' => $this->locale->activeTimezone()  // Timezone
                    ), // for future
                    'days_left' => $details['enrolled_days_left'] ?? '',  // Days left
                    'learner' => $this->load->subData("user", "getUser", $details['user_id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]),
                    'enrolled_by' => $this->load->subData("user", "getUser", $details['enrolled_by'] ?? 0, ['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']),
                    'counselor' => $this->load->subData("counselor", "getCounselor", $details['counselor_id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]),
                    'batch' =>  $this->load->subData("batch", "getBatch", $details['batch_id'], ['schema' => 'Batch_Minimal', 'noResponse' => 'Batch_Minimal']),
                    'instructor' => $this->load->subData("user", "getUser", $details['instructor_id'] ?? 0, ['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']),
                    'course' => $this->load->subData("course", "getCourse", $details['course_id'] ?? 0, ['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                    'academy' => $this->load->subData("course", "getCourse", $details['course_id'] ?? 0, ['key' => 'academy', 'noResponse' => 'Academy_Minimal']),
                    'of_org' => $this->load->subData("org", "getOrganization", $details['org_admin']['id'], ['schema' => 'Organization_Minimal', 'noResponse' => ['id' => 0, 'name' => '']]),
                    'selling_price' => $this->load->subData("invoice", "displayPrice", $sellingQuery, ['noResponse' => 'Price']),
                    'list_price' => $this->load->subData("invoice", "displayPrice", $listingQuery, ['noResponse' => 'Price_List']),
                    'full_part' => array(
                        'type' => $details['full_part']['type'] ?? '',  // Payment type (FULLPAYMENT or PARTPAYMENT)
                        'total_installments' =>  $details['full_part']['total_installments'] ?? ''  // Total installments (if part payment)
                    ),
                    'in_crm' => $this->load->subData("user", "getUser", $details['user_id'], ['key' => 'in_crm', 'noResponse' => ['platform' => '', 'id' => '']]),
                    'referral' => ['referrer' => $this->load->subData("user", "getUser", $details['refer']['referrer']['id'], ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']])],
                    'payment' => $this->load->subData("payment", "getPayment", $paymentQuery, ['noResponse' => 'Payment_Minimal']),
                    'classes' => [
                        'attended' => $details['attended_classes_counter'] ?? 0,
                        'total' => $details['enrolled_classes_counter'] ?? 0
                    ],
                    'attendance' => [
                        'percentage' =>  $details['attendance_percentage'] ?? '',
                        'status' => ''
                    ],
                    'is_change_batch' => $details['is_change_batch'] ?? false,
                    'is_unenroll' => $details['is_unenroll'] ?? false
                );
            }

            if (isset($filter['schema'])) {
                $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
            }

            //$filter['debug'] = true;
            $processedResponse = $this->schema->validate(['count' => $responseCount, 'data' => $enrollmentsResponse], ['count' => 'integer', 'data' => ['Refer#Enrollment']], $filter);

            $this->cache->setCache($cacheObj, $processedResponse);

            return $processedResponse;
        }
        return false;
    }

    /**
     * Retrieves the Elasticsearch ID of an enrollment.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Query parameters.
     * @param array $filter Additional filters for response formatting.
     * @return string|false Returns the Elasticsearch ID or false if not found.
     * <AUTHOR>
     */
    public function getEnrollmentEsId($query, $filter = [])
    {
        if ($query['custom']) {
            $enrollmentDataResponse = $this->es->customQuery($query['custom'], 'batchenrollmentevent');
            if ($enrollmentDataResponse['status_code'] == 200) {
                return $enrollmentDataResponse['body']['_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Check if the user is already enrolled in the course and batch.
     *
     * @param int $user_id The ID of the user.
     * @param int $course_id The ID of the course.
     * @param int $batch_id The ID of the batch.
     * @return bool|WP_Error Returns true if enrolled, or WP_Error if already enrolled.
     */
    public function isUserAlreadyEnrolled($userId, $courseId, $batchId, $source)
    {
        global $wpdb;
        $logFile = ABSPATH . "error-logs/isUserAlreadyEnrolled.log";
        error_log("addEnrollment - isUserAlreadyEnrolled " . date("Y-m-d H:i:s") . " === " . json_encode($source) . "\n\n", 3, $logFile);
        // Query the custom post type 'enrollment' to check if a post exists with these conditions
        if ($source === 'POST') {
            $args = [
                'post_type'      => 'enrollment',
                'post_status'    => 'publish',
                'meta_query'     => [
                    'relation' => 'AND',
                    [
                        'key'   => 'user_id',
                        'value' => $userId,
                        'compare' => '='
                    ],
                    [
                        'key'   => 'course_id', // Course ID
                        'value' => $courseId,
                        'compare' => '='
                    ],
                    [
                        'key'   => 'batch_id',
                        'value' => $batchId,
                        'compare' => '='
                    ]
                ],
                'fields' => 'ids', // Only return post IDs for better performance
            ];

            $enrollmentPosts = get_posts($args);
            $enrollmentStatus = get_post_meta($enrollmentPosts[0], 'enrollment_status', true);

            $enrollmentPostsData = array(
                'id' => $enrollmentPosts[0],
                'status' => $enrollmentStatus
            );

            // Check if any enrollment matches the criteria
            if (empty($enrollmentPosts)) {
                return false;
            }

            return $enrollmentPostsData; // User is not enrolled

        } elseif ($source === 'ES') {
            $query = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.course_id" => $courseId]],
                            ["match" => ["data.details.batch_id" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.course_id",
                    "data.details.enrollment_status"
                ]
            ];

            error_log("addEnrollment - isUserAlreadyEnrolled -- ES query " . date("Y-m-d H:i:s") . " === " . json_encode($query) . "\n\n", 3, $logFile);

            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->customQuery($query, 'batchenrollmentevent');

            error_log("addEnrollment - isUserAlreadyEnrolled -- ES query response" . date("Y-m-d H:i:s") . " === " . json_encode($enrollmentDataResponse) . "\n\n", 3, $logFile);

            if (isset($enrollmentDataResponse) && !empty($enrollmentDataResponse)) {
                // Extract all results into an array
                return $enrollmentDataResponse[0];
            } else {
                return false;
            }
        } else {
            return false;
        }
        return false;
    }


    /**
     * Checks if an enrollment already exists for the given user, course, and batch.
     *
     * @param int $userId The ID of the user.
     * @param int $courseId The ID of the course.
     * @param int $batchId The ID of the batch.
     * @return int|WP_Error Returns the enrollment ID if it exists, or a WP_Error if an error occurs.
     */
    public function getExistingEnrollmentId($userId, $courseId, $batchId)
    {
        // Define query arguments for the custom post type 'enrollment'
        $queryArgs = [
            'post_type'      => 'enrollment',
            'meta_query'     => [
                'relation' => 'AND',
                [
                    'key'     => 'user_id',
                    'value'   => $userId,
                    'compare' => '='
                ],
                [
                    'key'     => 'course_id',
                    'value'   => $courseId,
                    'compare' => '='
                ],
                [
                    'key'     => 'batch_id',
                    'value'   => $batchId,
                    'compare' => '='
                ]
            ],
            'posts_per_page' => 1, // Limit to a single result
            'fields'         => 'ids' // Fetch only the post IDs for efficiency
        ];

        // Execute the query
        $query = new WP_Query($queryArgs);

        // If an enrollment exists, return the post ID
        if ($query->have_posts()) {
            return $query->posts[0];
        }

        return true; // Return true if no enrollment exists
    }

    /**
     * Inserts enrollment data into the custom post type 'enrollment'.
     *
     * @param array $request The request data containing enrollment information.
     * @param array $enrollmentData The enrollment-specific data to be added.
     * @param int $enrollmentId The unique ID of the enrollment.
     * @return mixed The post ID on success or WP_Error on failure.
     */
    public function addCustomPostEnrollment($reqArgs, $batchDetails)
    {
        global $wpdb;

        // Prepare data for inserting into the custom post type 'enrollment'
        $postData = [
            'post_title'   => 'Enrollment for User ID: ' . $reqArgs['user_id'], // Adjust the title as needed
            'post_content' => '', // Add relevant content if needed
            'post_status'  => 'publish', // Set the status to publish or draft as needed
            'post_type'    => 'enrollment', // Your custom post type slug
        ];

        // Insert the post into the custom post type
        $postId = wp_insert_post($postData);

        // Check if the post was inserted successfully
        if (is_wp_error($postId)) {
            error_log('Failed to insert enrollment post: ' . $postId->get_error_message());
            return false;
        } else {
            $duration = (int)$batchDetails['duration'];
            $startDate = $this->dt->currentSystemDT(); // Using date-time library
            $endDate = date(\DateTime::ATOM, strtotime("+$duration days", strtotime($startDate)));

            $this->loadModel('course');
            $economics = $this->courseModel->getCourse($reqArgs['course_id'])['economics']; //return key

            //$economics = $this->getUserCourseEconomics($reqArgs['course_id']);
            $listingPrice = $this->getListingPrice($economics);
            $query = [
                'cntryCode' => 'IN',
                'basePrice' => $reqArgs['amount'],
                'taxPer' => 18.0
            ];
            $sellingPrice = $this->getSellingPrice($query);

            // Update post meta with enrollment data
            update_post_meta($postId, 'user_id', $reqArgs['user_id']);
            update_post_meta($postId, 'course_type', 'BATCH'); // Assuming product type based on the payload
            update_post_meta($postId, 'course_id', $reqArgs['course_id']); // Replace with actual Zoho product ID logic
            update_post_meta($postId, 'instructor_id', get_post_meta($reqArgs['batch_id'], 'instructor_id', true)); // Assuming instructor_id is stored with batch
            update_post_meta($postId, 'batch_id', $reqArgs['batch_id']);
            update_post_meta($postId, 'counselor_id', $reqArgs['counselor_id']);
            update_post_meta($postId, 'org_id', $reqArgs['org_id'] ?? 0); // Default to 0 if not provided
            update_post_meta($postId, 'org_user_id', $reqArgs['org_user_id'] ?? ''); // Default to 0 if not provided
            update_post_meta($postId, 'org_user_phone', $reqArgs['org_user_phone'] ?? ''); // Default to 0 if not provided
            update_post_meta($postId, 'org_user_name', $reqArgs['org_id'] ?? ''); // Default to 0 if not provided
            update_post_meta($postId, 'org_user_email', $reqArgs['org_user_email'] ?? ''); // Default to 0 if not provided
            update_post_meta($postId, 'enroll_time', $startDate);
            update_post_meta($postId, 'enrollment_status', 'INACTIVE'); // Assuming 'PENDING' by default
            update_post_meta($postId, 'enrollment_duration', $duration);
            update_post_meta($postId, 'enrollment_start_date', $startDate);
            update_post_meta($postId, 'enrollment_end_date', $endDate);
            //update_user_meta($reqArgs['user_id'], 'http_user_agent', ''); // offline 
            update_user_meta($postId, 'enrolled_by', $reqArgs['enrolled_by']); // offline
            update_user_meta($postId, 'listing_price', $listingPrice); // $listingPrice
            update_user_meta($postId, 'selling_price', $sellingPrice); // sellingPrice
            update_user_meta($postId, 'is_change_batch', true);
            update_user_meta($postId, 'is_unenroll', true);

            // Log success
            error_log("Enrollment post created with ID: $postId");
            return $postId;
        }

        return false;
    }

    /**
     * Sends enrollment notifications via WhatsApp and email.
     *
     * @param array $request The request data containing enrollment and user details.
     * @param int $enrollmentId The ID of the enrollment record.
     * @return void
     */
    public function sendWhatsappNotifications($reqArgs)
    {
        $this->loadModel('course');
        $course = $this->courseModel->getCourse($reqArgs['course_id'], ['schema' => 'Course_Minimal']);

        $notificationData = [
            'event_id'     => '8',
            'user_ids'     => explode(' ', $reqArgs['user_id']),
            'customfields' => [
                [
                    'key'   => 1,
                    'type'  => 'static',
                    'value' => $course['url']
                ]
            ]
        ];

        $notificationPayload = json_encode($notificationData);

        // Send notification
        $response = $this->sendNotificationHandler($notificationPayload, 'whatsapp', 'enrolled_in_course');

        // Check if notification sending was successful
        if ($response === true) {
            return $response;
        } else {
            return false;
        }
    }

    /**
     * Sends email notifications to the learner and the instructor.
     *
     * @param array $reqArgs Request arguments containing course and user details.
     * @param array $batchDetails Batch details including instructor and start date.
     * @return bool Returns true if both email notifications are sent successfully, false otherwise.
     */
    public function sendEmailNotifications($reqArgs, $batchDetails)
    {
        // Prepare email data
        $emailData = [
            'course_id'             => $reqArgs['course_id'],
            'course_url'            => get_permalink($reqArgs['course_id']),
            'instructor_id'         => $batchDetails['instructor_id'],
            'batch_start_date_time' => $batchDetails['start_date'],
            'learner_id'            => $reqArgs['user_id']
        ];

        // Send email notifications
        $learnerEmailStatus = $this->emailNotification('ENROLLED_IN_COURSE', $reqArgs['user_id'], $emailData);
        $instructorEmailStatus = $this->emailNotification('INSTRUCTOR_NEW_LEARNER_ENROLLED_IN_COURSE', $batchDetails['instructor_id'], $emailData);

        // Check if both email notifications were successful
        if ($learnerEmailStatus && $instructorEmailStatus) {
            return true;
        }

        // Log failure details for debugging
        if (!$learnerEmailStatus) {
            error_log("Failed to send email to learner ID: {$reqArgs['user_id']}");
        }
        if (!$instructorEmailStatus) {
            error_log("Failed to send email to instructor ID: {$batchDetails['instructor_id']}");
        }

        return false;
    }

    /**
     * Sends a notification to a specific platform.
     *
     * @param string $payload The JSON-encoded notification payload.
     * @param string $platform The platform to send the notification (e.g., "whatsapp").
     * @param string $eventType The event type for the notification.
     * @return void
     */
    public function sendNotificationHandler($payload, $platform, $eventType)
    {
        // Capture the response from sendNotification function
        $response = sendNotification($payload, $platform, $eventType);

        // Log the response
        error_log("sendNotification - Response: " . date("Y-m-d H:i:s") . " === Payload: " . json_encode($payload) . " Platform: " . $platform . " EventType: " . $eventType . " Response: " . json_encode($response) . "\n\n", 3, ABSPATH . "error-logs/new_enrollment_not.log");
    }

    /**
     * Sends an email notification to the specified user.
     *
     * @param string $templateId The ID of the email template to use.
     * @param int $userId The ID of the user to receive the email.
     * @param array $emailData The data to include in the email.
     * @return void
     */
    public function emailNotification($templateId, $userId, $emailData)
    {
        $response = email_notification($templateId, $userId, $emailData);

        // Log the response
        error_log("sendNotification - Response: " . date("Y-m-d H:i:s") . " === templateId: " . json_encode($templateId) . " userId: " . $userId . " emailData: " . $emailData . " Response: " . json_encode($response) . "\n\n", 3, ABSPATH . "error-logs/new_enrollment_not.log");
    }

    /**
     * Enrolls a user in a group based on batch and course IDs.
     *
     * @param int $batchId The ID of the batch.
     * @param int $courseId The ID of the course.
     * @param int $userId The ID of the user.
     * @return bool|void True on successful enrollment, false or void on failure.
     */
    public function linkGroup($userId, $courseId, $batchId)
    {
        global $wpdb;

        // Retrieve the group ID based on batch and course IDs
        $groupIdResult = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT group_id FROM wp_group_relationships WHERE batch_id = %d AND product_id = %d",
                $batchId,
                $courseId
            )
        );

        if (empty($groupIdResult)) {
            error_log("No group found for batch ID: $batchId and course ID: $courseId");
            return false; // No group found for the given batch and course
        }

        $groupId = $groupIdResult[0]->group_id;

        // Check if the user is already linked to the group
        $existingUserGroup = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}groups_user_group WHERE user_id = %d AND group_id = %d",
                $userId,
                $groupId
            )
        );

        // If the user is already linked, return true
        if ($existingUserGroup > 0) {
            return true; // User is already linked to this group
        }

        // Add the user to the group
        $addUserResponse = $wpdb->insert(
            'wp_groups_user_group',
            [
                'user_id'  => $userId,
                'group_id' => $groupId
            ]
        );

        if ($addUserResponse === false) {
            error_log("Failed to add user ID: $userId to group ID: $groupId");
            return false; // Failed to add user to the group
        }

        return true; // User successfully added to the group
    }

    /**
     * Inserts enrollment data into the wp_enrollment table and triggers Elasticsearch processing.
     *
     * @param array|object $request The request data containing enrollment and payment information.
     * @param array $batchDetails The batch details including duration and other metadata.
     * @param int $paymentId The ID of the payment record.
     * @return int|bool The ID of the inserted custom post type or false on failure.
     */
    public function addCustomTableEnrollment($reqArgs, $batchDetails)
    {
        global $wpdb;
        $enrollmentStatus = 'INACTIVE';
        $duration = (int)$batchDetails['duration'];
        $startDate = $this->dt->currentSystemDT("Y-m-d H:i:s"); // Using date-time library
        $endDate = date("Y-m-d H:i:s", strtotime("+$duration days", strtotime($startDate)));

        // Prepare enrollment data for insertion
        $enrollmentData = [
            'user_id'               => $reqArgs['user_id'],
            'product_zoho_id'       => get_post_meta($reqArgs['course_id'], 'id', true),
            'instructor_id'         => get_post_meta($reqArgs['batch_id'], 'instructor_id', true),
            'batch_id'              => $reqArgs['batch_id'],
            'product_db_id'         => $reqArgs['course_id'],
            'org_id'                => $reqArgs['org_id'] ?? 0,
            'enrollment_status'     => $enrollmentStatus,
            'enrollment_duration'   => $duration,
            'enroll_time' => $this->dt->currentSystemDT("Y-m-d H:i:s"),
            'enrollment_start_date' => $startDate,
            'enrollment_end_date'   => $endDate,
            'migrated' => 'yes'
        ];

        // Insert data into the wp_enrollment table
        $insertResult = $wpdb->insert("{$wpdb->prefix}enrollment", $enrollmentData);

        // Check for errors during insertion
        if ($insertResult === false) {
            return false;
        }

        // Retrieve the ID of the last inserted row
        return $wpdb->insert_id;
    }

    /**
     * Processes enrollment data and sends it to Elasticsearch.
     *
     * @param array $enrollmentData The enrollment data to process.
     * @param int $enrollmentId The ID of the enrollment.
     * @return void|WP_Error Returns a WP_Error on failure.
     */
    public function addEnrollment($reqArgs, $batchDetails, $enrollmentId, $referrerId)
    {
        $logFile = ABSPATH . "error-logs/addEnrollmentES.log";
        // Extract relevant data
        $duration = (int)$batchDetails['duration'];
        $enrollStartDate = $this->dt->currentSystemDT();
        $enrollEndDate = date(\DateTime::ATOM, strtotime("+$duration days", strtotime($enrollStartDate)));
        $enrolledDaysLeft = max(0, round((strtotime($enrollEndDate) - time()) / (60 * 60 * 24)));
        $enrollmentStatus = 'INACTIVE';
        $isBatchChange = $enrollmentStatus === 'ACTIVE';
        $isUnenrolled = $enrollmentStatus !== 'ACTIVE';

        //  Fetch user details (image, name) from Elasticsearch instead of userModel
        //$userDetails = $this->es->getDocumentFields('signedup', 'signedup-' . $reqArgs['user_id'], ['user']);
        $userDetails = $this->es->read('signedup', 'signedup-' . $reqArgs['user_id'], ['_source' => 'data.details.user']);
        $userImage = $userDetails['body']['_source']['data']['details']['user']['image'] ?? '';
        $userName = $userDetails['body']['_source']['data']['details']['user']['full_name'] ?? '';

        //  Fetch instructor details (image, name) from Elasticsearch
        //$instructorDetails = $this->es->getDocumentFields('signedup', 'signedup-' . $batchDetails['instructor_id'], ['user']);
        $instructorDetails = $this->es->read('signedup', 'signedup-' . $batchDetails['instructor_id'], ['_source' => 'data.details.user']);
        $instructorImage = $instructorDetails['body']['_source']['data']['details']['user']['image_url'] ?? '';
        $instructorName = $instructorDetails['body']['_source']['data']['details']['user']['full_name'] ?? '';

        //  Fetch counselor details (if available)
        $counselorDetails = $this->es->read('signedup', 'signedup-' . $reqArgs['counselor_id'], ['_source' => 'data.details.user']);
        $counselorName = $counselorDetails['body']['_source']['data']['details']['user']['full_name'] ?? '';

        //  Fetch organization admin details from Elasticsearch
        $orgAdmin = ['id' => 0, 'image' => '', 'name' => ''];
        if (!empty($reqArgs['org_id'])) {
            $orgDetails = $this->es->read('org', 'org-' . $reqArgs['org_id'], ['_source' => 'data.details.logo_image', 'data.details.organisation_name']);
            $orgAdmin = [
                'id'    => $reqArgs['body']['_source']['data']['details']['org_id'],
                'image' => $orgDetails['body']['_source']['data']['details']['logo_image'] ?? '',
                'name'  => $orgDetails['body']['_source']['data']['details']['organisation_name'] ?? ''
            ];
        }

        //  Fetch `economics` from Elasticsearch instead of course model
        // $economics = $this->es->getDocumentFields('course', 'course-'.$reqArgs['course_id'], ['economics']);
        $this->loadModel('course');
        $economics = $this->courseModel->getCourse($reqArgs['course_id'])['economics']; //return key

        // Fetch `category` from Elasticsearch instead of using model
        $categoryData = $this->es->read('course', 'course-' . $reqArgs['course_id'], ['_source' => 'data.details.category']);
        $categoryId = $categoryData['body']['_source']['data']['details']['category']['id'] ?? 0;
        $categorySlug = $categoryData['body']['_source']['data']['details']['category']['slug'] ?? '';

        //  Fetch `course_name` from Elasticsearch
        $courseName = $this->es->read('course', 'course-' . $reqArgs['course_id'], ['_source' => 'data.details.title'])['body']['_source']['data']['details']['title'] ?? '';

        // Calculate listing price
        $updateListingPrice = $this->getListingPrice($economics);

        // Calculate selling price
        $query = [
            'cntryCode' => 'IN',
            'basePrice' => $reqArgs['amount'],
            'taxPer' => 18.0
        ];
        $updateSellingPrice = $this->getSellingPrice($query);

        // Prepare full_part array for installment payments
        $fullPart = [
            'type' => '',
            'total_installments' => 0
        ];
        if ($reqArgs['payment_gateway'] === 'offline' && $reqArgs['payment_mode'] === 'offline') {
            $fullPart['type'] = 'FULLPAYMENT';
        } elseif ($reqArgs['payment_gateway'] === 'generate_link' && $reqArgs['payment_mode'] === 'online') {
            $fullPart['type'] = 'FULLPAYMENT';
        } elseif ($reqArgs['payment_gateway'] === 'direct_payment' && $reqArgs['payment_mode'] === 'online') {
            $fullPart['type'] = 'FULLPAYMENT';
        } elseif (
            $reqArgs['payment_gateway'] === 'generate_link' &&
            $reqArgs['payment_mode'] === 'online' &&
            isset($reqArgs['total_installments']) &&
            in_array($reqArgs['total_installments'], [1, 2, 3])
        ) {
            $fullPart['type'] = 'ININSTALLMENTS';
            $fullPart['total_installments'] = $reqArgs['total_installments'];
        }

        // Prepare Elasticsearch payload
        $elasticsearchPayload = [
            'data' => [
                'details' => [
                    'user_id'            => $reqArgs['user_id'],
                    'image'              => $userImage,
                    'event_type'         => 'batchenrollmentevent',
                    'enrollment_type'    => $batchDetails['enrollment_type'],
                    'personalization'    => $batchDetails['group_type'],
                    'event_label'        => 'Enrolled in batch',
                    'name'               => $userName,
                    'enrolled_on'        => date('F j, Y', strtotime($enrollStartDate)),
                    'enrolled_end'       => date('F j, Y', strtotime($enrollEndDate)),
                    'enrollment_id'      => $enrollmentId,
                    'enrolled_days_left' => $enrolledDaysLeft,
                    'org_admin'          => $orgAdmin,
                    'course_id'          => $reqArgs['course_id'],
                    'course_name'        => $courseName,
                    'batch_name'         => $batchDetails['batch_name'],
                    'instructor_id'      => $batchDetails['instructor_id'],
                    'counselor_id'       => $reqArgs['counselor_id'],
                    'instructor_image'   => $instructorImage,
                    'instructor_name'    => $instructorName,
                    'counselor_name'     => $counselorName,
                    'enrollment_status'  => $enrollmentStatus,
                    'term_id'            => $categoryId,
                    'batch_end_date'     => $enrollEndDate,
                    'course_duration_in_days' => $duration . 'days',
                    'parent_cat_id'      => $categoryId,
                    'category'           => $categorySlug,
                    'batch_id'           => $reqArgs['batch_id'],
                    'is_unenroll'        => $isUnenrolled,
                    'is_change_batch'    => $isBatchChange,
                    'payment_status'     => 'PENDING',
                    'payment_method'     => $reqArgs['payment_gateway'],
                    'total_amount'       => $reqArgs['amount'],
                    'amount_received'    => $reqArgs['amount'],
                    'amount_pending'     => '',
                    'payment_description' => '',
                    'payment'            => [],
                    'enrolled_by'        => $reqArgs['enrolled_by'],
                    'country_code'       => $reqArgs['currency_code'],
                    'selling_price'      => $updateSellingPrice,
                    'listing_price'      => $updateListingPrice,
                    'event_date'         => date('Y-m-d H:i:s', strtotime($enrollStartDate)),
                    'full_part'          => $fullPart
                ],
                '@timestamp' => $this->dt->currentSystemDT()
            ]
        ];

        error_log("addEnrollment - elasticsearchPayload - " . date("Y-m-d H:i:s") . " === ID: " . json_encode($elasticsearchPayload) . "\n\n", 3, $logFile);

        // Send data to Elasticsearch
        $esResponse = $this->es->create('batchenrollmentevent', $elasticsearchPayload);

        error_log("addEnrollment - esResponse - " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esResponse) . "\n\n", 3, $logFile);

        return ($esResponse['status_code'] == 201) ? $esResponse : false;
    }

    /**
     * Retrieves referral discount details.
     *
     * @param array $details The details required for the referral discount.
     * @return array Referral details.
     */
    private function getDiscountedAmount($details)
    {
        return to_get_discounted_amount($details);
    }

    /**
     * Updates the enrollment status in the custom enrollment table.
     *
     * @param int $enrollmentId The ID of the enrollment record to update.
     * @param string $enrollmentStatus The new status to set for the enrollment.
     * @return bool Returns true if the update is successful, false otherwise.
     */
    public function updateCustomTableEnrollment($enrollmentId, $args)
    {
        global $wpdb;

        // Ensure valid input
        if (empty($enrollmentId)) {
            error_log("Invalid enrollment ID or status provided for update.");
            return false;
        }

        // Prepare the data for update
        $updateData = [
            'enrollment_status' => $args['status'],
            'user_deal_status' => $args['deal_status'],
            'deal_zoho_id' => $args['deal_id']

        ];

        // Execute the update query
        $updateResult = $wpdb->update(
            "{$wpdb->prefix}enrollment", // Table name
            $updateData,                // Data to update
            ['id' => $enrollmentId]     // Where clause
        );

        // Return true if rows were affected, false otherwise
        if ($updateResult !== false) {
            error_log("Enrollment status updated successfully for enrollment ID: {$enrollmentId}");
            return true;
        } else {
            error_log("Failed to update enrollment status for enrollment ID: {$enrollmentId}");
            return false;
        }
    }

    /**
     * Updates the custom post type 'enrollment' with the provided arguments.
     *
     * @param int $enrollmentPostId The ID of the enrollment custom post to update.
     * @param array $args An associative array containing the data to update. Expected keys:
     *                    - 'enrollment_status' (string): The new enrollment status.
     * @return bool Returns true on successful update, false otherwise.
     */
    public function updateCustomPostEnrollment($enrollmentPostId, $args)
    {
        // Validate input
        if (empty($enrollmentPostId) || empty($args['status'])) {
            error_log("Invalid enrollment post ID or status provided for update.");
            return false;
        }

        // Update enrollment status in the custom post type
        $statusUpdated = update_post_meta($enrollmentPostId, 'enrollment_status', $args['status']);
        update_post_meta($enrollmentPostId, 'enrollment_status', $args['status']);

        // Log the update result
        if ($statusUpdated) {
            error_log("Enrollment custom post updated successfully. Post ID: {$enrollmentPostId}, Status: {$args['enrollment_status']}");
            return true;
        } else {
            error_log("Failed to update enrollment custom post. Post ID: {$enrollmentPostId}");
            return false;
        }
    }

    public function updateEnrollment($enrollmentId, $args)
    {
        $paymentData = [
            "data" => [
                "details" => [
                    "enrollment_status" => $args['status']
                ]
            ]
        ];

        $paymentDataResponse = $this->es->update('batchenrollmentevent', $enrollmentId, $paymentData);
        if ($paymentDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    public function getEnrollmentsCount()
    {
        // Handle the response and return the document count
        $responseData = $this->es->count('batchenrollmentevent');

        if ($responseData['status_code'] == 200) {
            $responseCount = $responseData['body']['count'];
        } else {
            return false;
        }

        if (isset($responseCount)) {
            return (int) $responseCount;
        }
        return 0;
    }

    public function getListingPrice($economics)
    {

        $groupFound = false; // Flag to track if a group is found
        foreach ($economics as $item) {
            if (isset($item['personalization']['type']) && isset($item['price']['variants'][0]['list_price'])) {
                $personalizationType = strtolower($item['personalization']['type']); // Normalize for consistent comparison
                $listingPrice = $item['price']['variants'][0]['list_price'];

                // Prioritize 'group' type
                if ($personalizationType === 'group') {
                    $updateListingPrice = [
                        'currency_code' => $listingPrice['currency']['code'],
                        'exclusive_tax' => $listingPrice['exclusive_tax'],
                        'inclusive_tax' => $listingPrice['inclusive_tax'],
                        'tax' => [
                            'type' => $listingPrice['tax']['type'],
                            'percentage' => $listingPrice['tax']['percentage'],
                            'amount' => $listingPrice['tax']['amount'],
                        ],
                    ];
                    $groupFound = true; // Mark group as found
                    break; // Stop processing further since group takes priority
                }
            }
        }

        // If no 'group' was found, process '1to1' types
        if (!$groupFound) {
            foreach ($economics as $item) {
                if (isset($item['personalization']['type']) && isset($item['price']['variants'][0]['list_price'])) {
                    $personalizationType = strtolower($item['personalization']['type']); // Normalize for consistent comparison
                    $listingPrice = $item['price']['variants'][0]['list_price'];

                    if ($personalizationType === '1to1') {
                        $updateListingPrice = [
                            'currency_code' => $listingPrice['currency']['code'],
                            'exclusive_tax' => $listingPrice['exclusive_tax'],
                            'inclusive_tax' => $listingPrice['inclusive_tax'],
                            'tax' => [
                                'type' => $listingPrice['tax']['type'],
                                'percentage' => $listingPrice['tax']['percentage'],
                                'amount' => $listingPrice['tax']['amount'],
                            ],
                        ];
                        break; // Output only the first 1to1 listing if no group exists
                    }
                }
            }
        }
        return $updateListingPrice;
    }

    public function getSellingPrice($query)
    {
        $this->loadModel('invoice');
        $sellingPrice = $this->invoiceModel->getSellPrice($query);
        return [
            'currency_code' => $sellingPrice['currency']['code'], // Extract 'code' from 'currency'
            'exclusive_tax' => $sellingPrice['exclusive_tax'],
            'inclusive_tax' => $sellingPrice['inclusive_tax'],
            'tax' => [
                'type' => $sellingPrice['tax']['type'],
                'percentage' => $sellingPrice['tax']['percentage'],
                'amount' => $sellingPrice['tax']['amount'],
            ],
            'discount' => [
                'amount' => $sellingPrice['discount']['amount'],
                'percentage' => $sellingPrice['discount']['percentage'],
            ],
        ];
    }

    /**
     * Checks if the enrollment_id exists in the custom post type 'enrollment'
     * and retrieves batch_id, course_id, and user_id.
     */
    private function getEnrollmentDetails($enrollmentId)
    {
        // Check if the enrollment post exists in the 'enrollment' custom post type
        $enrollment = get_post($enrollmentId);

        if (!$enrollment || $enrollment->post_type !== 'enrollment') {
            return false;
        }

        // Retrieve post meta values
        $batchId = get_post_meta($enrollmentId, 'batch_id', true);
        $courseId = get_post_meta($enrollmentId, 'course_id', true);
        $userId = get_post_meta($enrollmentId, 'user_id', true);
        $enrollmentStatus  = get_post_meta($enrollmentId, 'enrollment_status', true);

        // Ensure all required meta values exist
        if (empty($batchId) || empty($courseId) || empty($userId)) {
            return false;
        }

        return [
            'batch_id' => (int) $batchId,
            'course_id' => (int) $courseId,
            'user_id' => (int) $userId,
            'status' => $enrollmentStatus
        ];
    }

    public function getUserEnrolledBatches($userId, $courseId)
    {
        global $wpdb;
        $enrolledBatches = [];

        // Fetch batch IDs from CPT
        $args = [
            'post_type'      => 'enrollment',
            'post_status'    => 'publish',
            'meta_query'     => [
                'relation' => 'AND',
                [
                    'key'   => 'user_id',
                    'value' => $userId,
                    'compare' => '='
                ],
                [
                    'key'   => 'course_id',
                    'value' => $courseId,
                    'compare' => '='
                ]
            ],
            'fields' => 'ids',
        ];

        $enrollmentPosts = get_posts($args);

        foreach ($enrollmentPosts as $postId) {
            $batchId = get_post_meta($postId, 'batch_id', true);
            if (!empty($batchId)) {
                $enrolledBatches[] = [
                    'batch_id' => $batchId,
                    'source'   => 'CPT'  // Source: Custom Post Type
                ];
            }
        }

        // Fetch batch IDs from Elasticsearch
        $query['custom'] = [
            "query" => [
                "bool" => [
                    "must" => [
                        ["match" => ["data.details.user_id" => $userId]],
                        ["match" => ["data.details.course_id" => $courseId]]
                    ]
                ]
            ],
            "_source" => [
                "data.details.user_id",
                "data.details.course_id",
                "data.details.batch_id",
                "data.details.enrollment_status"
            ]
        ];

        // Fetch enrollment data from Elasticsearch (or any other data source)
        $enrollmentDataResponse = $this->es->customQuery($query, 'batchenrollmentevent');

        if (isset($enrollmentDataResponse) && !empty($enrollmentDataResponse)) {
            // Extract the first result from Elasticsearch response
            $record = $enrollmentDataResponse[0];
            $batchId = $record['batch_id'] ?? null;

            if (!empty($batchId)) {
                $enrolledBatches[] = [
                    'batch_id' => $batchId,
                    'source'   => 'ES'  // Source: Elasticsearch
                ];
            }
        }

        return $enrolledBatches;
    }
    /**
     * Updates the batch for an existing enrollment.
     *
     * This function updates the batch ID for a given enrollment. It ensures that
     * the provided enrollment exists and performs necessary validations before
     * updating the record.
     *
     * @param int $enrollmentId The unique identifier of the enrollment.
     * @param int $data The unique identifier of the new batch.
     * @return array JSON response indicating success or failure of the update.
     * @throws Exception If the enrollment does not exist or an error occurs while updating.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function updateBatch($enrollmentId, $data)
    {
        // Retrieve enrollment details from the custom post type
        $enrollmentDetails = $this->getEnrollmentDetails($enrollmentId);
        if (!is_array($enrollmentDetails)) {
            return $enrollmentDetails; // Return error if validation fails
        }

        // Extract existing details
        $batchId = $enrollmentDetails['batch_id'];
        $courseId = $enrollmentDetails['course_id'];
        $userId = $enrollmentDetails['user_id'];
        $status = $enrollmentDetails['status']; // Check if enrollment is active

        //  Only Active Enrollments are eligible
        if ($status !== 'active') {
            return ['error' => 'Only active enrollments can be updated.'];
        }

        // Check if the new batch is different from the current one
        if ($batchId === $data) {
            return ['error' => 'Learner is already enrolled in this batch.'];
        }

        //  Retrieve new batch details
        $newBatch = $this->batchModel->getBatchDetails($data);
        if (!$newBatch) {
            return ['error' => 'Invalid new batch selected.'];
        }

        //  Retrieve existing batch details
        $currentBatch = $this->batchModel->getBatchDetails($batchId);
        if (!$currentBatch) {
            return ['error' => 'Invalid current batch data.'];
        }

        //  Current & New Batch must belong to the same course
        if ($newBatch['course_id'] !== $courseId) {
            return ['error' => 'New batch does not belong to the same course.'];
        }

        //  New Batch should have enough days remaining
        $today = strtotime(date('Y-m-d'));
        $remainingDays = (strtotime($newBatch['end_date']) - $today) / 86400; // Convert seconds to days
        if ($remainingDays < 3) { // Assuming 3 days as the minimum requirement
            return ['error' => 'New batch must have enough days remaining.'];
        }

        //  Fixed and Locked Batches cannot be selected
        if ($newBatch['is_locked'] || $newBatch['is_fixed']) {
            return ['error' => 'Cannot enroll in a locked or fixed batch.'];
        }

        //  Batch should not belong to multiple courses
        if ($this->batchModel->isBatchLinkedToMultipleCourses($data)) {
            return ['error' => 'The selected batch is linked to multiple courses.'];
        }

        //  Only Active & Ongoing Batches are Selectable
        if ($newBatch['status'] !== 'active' && $newBatch['status'] !== 'ongoing') {
            return ['error' => 'Only active and ongoing batches are allowed.'];
        }

        //  Change Batch feature allows only Yuno Admins and Org Admins to change a learner’s batch
        if (!$this->isUserAllowedToChangeBatch($userId)) {
            return ['error' => 'Only Yuno Admins and Org Admins can update a batch.'];
        }

        //  Proceed with updating the batch
        $updateSuccess = $this->updateEnrollmentBatch($enrollmentId, $data);
        if (!$updateSuccess) {
            return ['error' => 'Batch update failed. Please try again.'];
        }

        return ['success' => 'Batch updated successfully.'];
    }
    public function isUserAllowedToChangeBatch($userId)
    {
        $userRole = $this->getUserRole($userId);
        return in_array($userRole, ['yuno_admin', 'org_admin']);
    }
    public function updateEnrollmentBatch($enrollmentId, $newBatchId)
    {
        return $this->enrollmentModel->update([
            'batch_id' => $newBatchId
        ], ['id' => $enrollmentId]);
    }
    public function generateEnrollmentStatusFilters($status)
    {
        return [
            'filter' => 'status',
            'title' => 'Status',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Status',
            'ui_control_type' => 'dropdown',
            'selected' => $status,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All enrollments', 'filter' => 'status'],
                ['slug' => 'active', 'label' => 'Active enrollments', 'filter' => 'status'],
                ['slug' => 'inactive', 'label' => 'Inactive enrollments', 'filter' => 'status']
            ]
        ];
    }

    public function getUserCourseEconomics($courseId)
    {
        // Define the Elasticsearch index
        $index = 'course'; // Change this to your actual course index

        // Define the field to fetch (only `economics`)
        $fields = ["economics"];

        // Fetch course data from Elasticsearch
        $courseData = $this->es->getDocumentFields($index, $courseId, $fields);

        // Return only the `economics` key if it exists, otherwise return null
        return $courseData['economics'] ?? null;
    }
    public function unEnrollLearner($enrollmentId, $data)
    {
        $this->loadModel('batch');
        $logFile = ABSPATH . "error-logs/unEnrollLearner.log";
        error_log("updEnrollment - data  " . date("Y-m-d H:i:s") . " === " . json_encode($data) . "\n\n", 3, $logFile);
        $enrollmentDetails = $this->getEnrollmentDetails($enrollmentId);
        error_log("updEnrollment - getEnrollmentDetails  " . date("Y-m-d H:i:s") . " === " . json_encode($enrollmentDetails) . "\n\n", 3, $logFile);


        if (!is_array($enrollmentDetails)) {
            return $enrollmentDetails; // Return error if validation fails
        }
        // Extract existing details
        $batchId = $enrollmentDetails['batch_id'];
        $courseId = $enrollmentDetails['course_id'];
        $userId = $enrollmentDetails['user_id'];
        $status = $enrollmentDetails['status']; // Check if enrollment is active

        error_log("updEnrollment - status  " . date("Y-m-d H:i:s") . " === " . $status . "\n\n", 3, $logFile);

        //  Only Active Enrollments are eligible
        if ($status !== 'active' && $status !== 'ACTIVE') {
            return false;
        }

        error_log("updEnrollment - batchId  " . date("Y-m-d H:i:s") . " === " . $batchId . "\n\n", 3, $logFile);

        //  Retrieve existing batch details
        $currentBatch = $this->batchModel->getBatchDetails($batchId);
        error_log("updEnrollment - currentBatch  " . date("Y-m-d H:i:s") . " === " . json_encode($currentBatch) . "\n\n", 3, $logFile);
        if (!$currentBatch) {
            //return ['error' => 'Invalid current batch data.'];
            return false;
        }
        //  Change Batch feature allows only Yuno Admins and Org Admins to change a learner’s batch
        error_log("updEnrollment - userId  " . date("Y-m-d H:i:s") . " === " . $userId . "\n\n", 3, $logFile);
        // if (!$this->isUserAllowedToUnenrollLearner($userId)) { // need id from payload for this
        //     return false;
        // }

        //  Extract the unenroll status
        $isUnenroll = $data['is_unenroll'];
        error_log("updEnrollment - isUnenroll  " . date("Y-m-d H:i:s") . " === " . $isUnenroll . "\n\n", 3, $logFile);
        //  Proceed with updating the batch
        $updateSuccess = $this->updateEnrollmentUnenroll($enrollmentId, $isUnenroll);
        error_log("updEnrollment - updateSuccess  " . date("Y-m-d H:i:s") . " === " . json_encode($updateSuccess) . "\n\n", 3, $logFile);
        if (!$updateSuccess) {
            // return ['error' => 'Unenrollment failed. Please try again.'];
            return false;
        }
        //return ['success' => 'Unenrolled successfully.'];
        return true;
    }
    public function isUserAllowedToUnenrollLearner($userId)
    {
        // Load user model
        $this->loadModel('user');

        // Get User Role
        $role = $this->userModel->getUserRole($userId);

        // Allow only 'yuno-admin' or 'org-admin' roles
        if ($role === 'yuno-admin' || $role === 'org-admin') {
            return true;
        }

        return false;
    }

    public function updateEnrollmentUnenroll($enrollmentId, $unenrollStatus)
    {
        $logFile = ABSPATH . "error-logs/updateEnrollmentUnenroll.log";
        error_log("updateEnrollmentUnenroll  " . date("Y-m-d H:i:s") . " === " . $enrollmentId . "\n\n", 3, $logFile);

        // Update in WordPress (Custom Post Type)
        $postId = $this->getPostIdByEnrollmentId($enrollmentId);
        error_log("updateEnrollmentUnenroll postId " . date("Y-m-d H:i:s") . " === " . $postId . "\n\n", 3, $logFile);

        if (!$postId) {
            return false; // Return false if the post is not found
        }

        // Update post meta in WordPress
        $status = $unenrollStatus ? 'INACTIVE' : 'ACTIVE';
        update_post_meta($postId, 'enrollment_status', $status);
        update_post_meta($postId, 'is_unenroll', $unenrollStatus);

        error_log("updateEnrollmentUnenroll status " . date("Y-m-d H:i:s") . " === " . $status . "\n\n", 3, $logFile);

        // Check if WordPress updates were successful
        $dbUpdateSuccess = (
            get_post_meta($postId, 'enrollment_status', true) === $status &&
            get_post_meta($postId, 'is_unenroll', true) === $unenrollStatus
        );

        error_log("updEnrollment - updateSuccess  " . date("Y-m-d H:i:s") . " === " . json_encode($dbUpdateSuccess) . "\n\n", 3, $logFile);

        // Update in Elasticsearch
        $esUpdateSuccess = $this->updateEnrollmentInES($enrollmentId, $unenrollStatus);

        error_log("updEnrollment - updateSuccess  " . date("Y-m-d H:i:s") . " === " . json_encode($esUpdateSuccess) . "\n\n", 3, $logFile);

        // Return true only if both updates succeeded
        return $dbUpdateSuccess && $esUpdateSuccess;
    }

    private function updateEnrollmentInES($enrollmentId, $unenrollStatus)
    {
        $searchQuery['custom'] = [
            "query" => [
                "bool" => [
                    "must" => [
                        ["match" => ["data.details.enrollment_id" => $enrollmentId]]
                    ]
                ]
            ],
            "_source" => [
                "data.details.enrollment_id",
                "data.details.enrollment_status",
                "data.details.is_unenroll"
            ],
            "fields" => ["_id"] // Fetch only the document ID
        ];

        // Fetch enrollment data from Elasticsearch (or any other data source)
        $enrollmentDataResponse = $this->es->customQuery($searchQuery, 'batchenrollmentevent');
        // Construct the document ID and the fields to update
        if (!empty($enrollmentDataResponse['hits']['hits'])) {
            $documentId = $enrollmentDataResponse['hits']['hits'][0]['_id'];
        }
        $fieldsToUpdate = [
            'enrollment_status' => $unenrollStatus === 0 ? 'inactive' : 'active',
            'is_unenroll' => $unenrollStatus == 0 ? false : true,
        ];

        // Perform the Elasticsearch update
        try {
            $params = [
                'index' => 'batchenrollmentevent', // Replace with your Elasticsearch index name
                'id'    => $documentId,
                'body'  => [
                    'doc' => $fieldsToUpdate
                ]
            ];

            $response = $this->es->update($params);

            // Check for successful response (adjust as needed for your Elasticsearch client)
            return isset($response['_shards']['successful']) && $response['_shards']['successful'] > 0;
        } catch (\Exception $e) {
            // Log or handle exceptions as needed
            return false;
        }
    }

    public function getPostIdByEnrollmentId($enrollmentId)
    {
        // Query the WordPress database for the post with the custom meta key
        // Replace 'enrollment_id' with the actual meta key used to store the enrollment ID
        $args = [
            'post_type' => 'enrollment', // Adjust to your custom post type
            'meta_query' => [
                [
                    'key' => 'enrollment_id', // The meta key storing the enrollment ID
                    'value' => $enrollmentId,
                    'compare' => '='
                ]
            ],
            'fields' => 'ids', // Only retrieve post IDs
            'posts_per_page' => 1,
        ];

        $query = new WP_Query($args);
        $posts = $query->get_posts();

        // If a matching post is found, return its ID
        if (!empty($posts)) {
            return $posts[0];
        }

        // Return null if no post is found
        return null;
    }

    public function getActiveEnrollmentsByBatchId($batchId)
    {
        $this->loadModel('user');
        $query = [
            "query" => [
                "bool" => [
                    "must" => [
                        ["match" => ["data.details.batch_id" => $batchId]],
                        ["match" => ["data.details.enrollment_status" => 'ACTIVE']]
                    ]
                ]
            ],
            "_source" => [
                "data.details.user_id",
                "data.details.course_id",
                "data.details.batch_id",
                "data.details.enrollment_status"
            ]
        ];
        // Fetch enrollment data from Elasticsearch (or any other data source)
        $enrollmentDataResponse = $this->es->customQuery($query, 'batchenrollmentevent');
        $results = [];
        if (isset($enrollmentDataResponse['body']['hits']['hits']) && !empty($enrollmentDataResponse['body']['hits']['hits'])) {
            foreach ($enrollmentDataResponse['body']['hits']['hits'] as $record) {

                $userId = $record['_source']['data']['details']['user_id'];
                $userResponse = $this->userModel->getUser($userId, ['schema' => 'User_Minimal']);
                // Extract the relevant fields from the Elasticsearch response
                $results[] = $userResponse;
            }
        }
        // Return the results
        if (empty($results)) {
            return false;
        }
        return $results;
    }
}
