const gulp = require('gulp'),
	gp_concat = require('gulp-concat'),
 	gp_rename = require('gulp-rename'),
    terser = require('terser');
	gulpTerser = require('gulp-terser'),
	uglifycss = require('gulp-uglifycss'),
    gulpIgnore = require('gulp-ignore');

const folder = "courseV2";

const tasks = [
    "courseV2",
    "courseSearchV2",
    "categoryLanding",
    "classesV2",
    "courseInsights"
];

function taskList(list, type) {
    let allTask = [];

    for (let i = 0; i < list.length; i++) {
        const element = list[i];

        allTask.push(element + type)
    };
    
    return allTask;
}

gulp.task(''+ folder +'JS', function () {
    const fileName = folder,
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/assets/js/lib/lodash.min.js',
            ''+ path +'/components/common.js',
            ''+ path +'/components/store.js',
            ''+ path +'/components/pageGrid/*.js',
            ''+ path +'/components/pageLoader.js',
            ''+ path +'/components/loader.js',
            ''+ path +'/components/chooseAccoutType/*.js',
            ''+ path +'/components/header/*.js',
            ''+ path +'/components/footer/*.js',
            ''+ path +'/assets/js/lib/vee-validate.min.js',
            ''+ path +'/assets/js/lib/vue-social-sharing.js',
            ''+ path +'/components/headerWithLeftMenu/*.js',
            ''+ path +'/components/courseSearchBar/*.js',
            ''+ path +'/components/resourceSharing/*.js',
            ''+ path +'/components/instructorCardV2/*.js',
            ''+ path +'/components/tableV1/tableV1.js',
            ''+ path +'/pages/'+ fileName +'/*.js'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.js`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}Combined.js`))
    .pipe(gulp.dest(saveToFolder))
    .pipe(gp_rename(renameFile))
    .pipe(gulpTerser({}, terser.minify))
    .pipe(gulp.dest(saveToFolder));
});

gulp.task(''+ folder +'CSS', function () {
    const fileName = folder,
        path = "wp-content/themes/yunolearning-child/", 
        getFiles = [
            ''+ path +'/components/tableV1/tableV1.css',
            ''+ path +'/components/pageGrid/*.css',
            ''+ path +'/components/header/*.css',
            ''+ path +'/components/footer/*.css',
            ''+ path +'/components/chooseAccoutType/*.css',
            ''+ path +'/components/headerWithLeftMenu/headerWithLeftMenu.css',
            ''+ path +'/components/courseSearchBar/*.css',
            ''+ path +'/components/resourceSharing/*.css',
            ''+ path +'/components/instructorCardV2/*.css',
            ''+ path +'/assets/scss/commonV2.css',
            ''+ path +'/pages/'+ fileName +'/*.css'
        ],
        saveToFolder = `${path}/pages/${fileName}/dist`,
        renameFile = `${fileName}.min.css`;

    return gulp.src(getFiles)
    .pipe(gp_concat(`${fileName}.min.css`))
    .pipe(uglifycss())
    .pipe(gulp.dest(saveToFolder))
});

gulp.task(folder, function() {
    const path = "wp-content/themes/yunolearning-child",
        pages = `${path}/pages/`,
        components = `${path}/components/`;

    gulp.watch([
        ''+ components +'resourceSharing/*.js',
        ''+ components +'instructorCardV2/*.js',
        ''+ components +'pageGrid/*.js',
        ''+ components +'headerWithLeftMenu/*.js',
        ''+ components +'courseSearchBar/*.js',
        ''+ components +'common.js',
        ''+ components +'chooseAccoutType/*.js',
        ''+ components +'store.js',
        ''+ pages + folder +'/*.js'
    ], gulp.series(taskList(tasks, 'JS')));

    gulp.watch([
        ''+ path +'/assets/scss/commonV2.css',
        ''+ components +'resourceSharing/*.css',
        ''+ components +'instructorCardV2/*.css',
        ''+ components +'pageGrid/*.css',
        ''+ components +'headerWithLeftMenu/headerWithLeftMenu.css',
        ''+ components +'courseSearchBar/*.css',
        ''+ components +'chooseAccoutType/*.css',
        ''+ pages + folder +'/*.css'
    ], gulp.series(taskList(tasks, 'CSS')));
});


