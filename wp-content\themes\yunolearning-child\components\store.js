const YUNOStore = (function($) {

    const updateModule = function(moduleObj, options, data, isSuccess, isPush) {

        if (isSuccess) {
            if (options.addToModule === undefined || options.addToModule) {

                if (isPush !== undefined && isPush) {
                    for (let i = 0; i < data.length; i++) {
                        moduleObj.data.push(data[i]);
                    };

                    if (options.hasLoadmore) {
                        moduleObj.count = options.response.data.count;
                        moduleObj.currentCount = moduleObj.data.length;
                        moduleObj.offset = moduleObj.currentCount;
                    };
    
                } else {
                    moduleObj.data = data;
                }
            }
        } else {
            if (options.moduleTabs) {
                moduleObj.error = data;
                moduleObj.loading = false;
            } else {
                moduleObj.error = data;
            }


        };

        moduleObj.success = true;
        moduleObj.loading = false;
    };                                                                                                                                                                                          

    /*
        @method: manageLoading()
        @description: Every batch object has a property call "isLoading". 
            When user initiate payment on particular batch"isLoading" state is being update with this method.
    */
    const manageLoading = function(options, forInstructor, courseBatches) {
        if (options.pageType === "instructorBatch") {
            if (forInstructor !== undefined) forInstructor.isLoading = false;
        } else {
            let currentBatch = courseBatches.filter(function(el) {
                return el.type === options.filterType;
            });

            currentBatch[0].data[options.batchIndex].isLoading = false;
        }
    };

    const init = function() {
        const store = new Vuex.Store({
            state: {
                pageLoader: false,
                loader: {
                    overlay: false,
                    isActive: false
                },
                themeURL: themeURL,
                homeURL: homePage,
                config: {
                    signInURL: "",
                    yunoAPIToken: typeof yunoAPIToken !== 'undefined' ? yunoAPIToken : "",
                    unauthorizedModal: false,
                    vimeoToken: "Bearer 4c4b3e1ac1851a23047dd7a338f5caee",
                    vimeoVideoToken: "Bearer 878869c3fe96f7ec679b9455c539ee77",
                    vimeoVideoEditToken: "Bearer 4c4b3e1ac1851a23047dd7a338f5caee",
                    googleAPIKey: "AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",
                    googleMapAPIKey: "AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",
                    googleMapLocationAPIKey: "AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI",
                },
                user: {
                    isLoggedin: false,
                    userID: isLoggedIn
                },
                userRole: {
                    loading: false,
                    error: null,
                    success: false,
                    data: [],
                    response: []
                },
                userProfile: {
                    loading: false,
                    error: null,
                    success: false,
                    data: []
                },
                header: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                footer: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                blogList: {
                    loading: false,
                    error: null,
                    success: false,
                    data: []
                },
                courseListIELTS: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                allCourseListIELTS: {
                    title: "All our courses are delivered by expert IELTS trainers",
                    demoPageURL: "/demo-classes",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: {
                        single: [],
                        multiple: []
                    }
                },
                resultsIELTS: {
                    title: "Our students' results",
                    isLoadMore: false,
                    count: "",
                    currentCount: "",
                    limit: 8,
                    offset: 0,
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                faqIELTS: {
                    title: "FAQs",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                recordedClasses: {
                    title: "Checkout actual classes that we recorded",
                    videos: [
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",
                            caption: "Subject Verb Agreement Class - Learn IELTS - Yuno Learning"
                        },
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",
                            caption: "IELTS Writing Task 2 Tips"
                        },
                        {
                            url: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",
                            poster: "https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",
                            caption: "How to Paraphrase - Learn IELTS - Yuno Learning"
                        }
                    ]
                },
                course: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0
                },
                courseV2: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0
                },
                schedule: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                courseBatches: {
                    title: "",
                    coursesCount: "",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    count: "",
                    currentCount: "",
                    limit: 4,
                    offset: 0
                },
                courseBatchesFilters: {
                    currentCourse: "",
                    tabs: [
                        {
                            title: "Any Time",
                            type: "anytime",
                            isActive: true,
                            data: []
                        },
                        {
                            title: "Morning",
                            type: "morning",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Afternoon",
                            type: "afternoon",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Evening",
                            type: "evening",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Night",
                            type: "night",
                            isActive: false,
                            data: []
                        }
                    ]
                },
                courseTabs: [],
                instructor: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: [],
                    tabs: []
                },
                learnerCourses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: [
                        {
                            title: "My Courses",
                            tab: "Upcoming and Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "batchCard",
                            errorMsg: "You have not enrolled any course yet",
                            isExploreCTA: true,
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            manageState: true

                        },
                        {
                            title: "My Courses",
                            tab: "Past",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "batchCard",
                            errorMsg: "You do not have any past course yet",
                            isExploreCTA: true,
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            manageState: true
                        }
                    ]
                },
                instructorCourses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    data: []
                },
                instructorBasicDetails: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    isLoading: false,
                    isUpdateDetail: false,
                    fluentInSelected: [],
                    understandSelected: [],
                    payload: {
                        user_id: "",
                        flat_house_number: "",
                        street: "",
                        landmark: "",
                        pin_code: "",
                        country: "",
                        state: "",
                        city: "",
                        experience: "",
                        fluent_in: [],
                        understand: [],
                        is_about: false
                    }
                },
                instructorAbout: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    isLoading: false,
                    isAbout: false,
                    payload: {
                        user_id: "",
                        is_about: true,
                        about: ""
                    }
                },
                instructorDemoClasses: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 2,
                    offset: 0,
                    isLoadMore: false,
                    data: []
                },
                instructorMyCourses: {
                    title: "My Courses",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                homeCategories: {
                    title: "Top Courses on Yuno",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                homeCarouselList: {
                    title: "Featured Courses",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorslList: {
                    title: "Meet Our Expert Instructors",
                    description: "",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                whyLearn: {
                    title: "Why Choose Yuno for Your Learning Journey",
                    list: [
                        {
                            title: "Online classes with personalized attention",
                            description: "All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",
                            icon: "portrait",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Instructors who care for your success",
                            description: "Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",
                            icon: "emoji_events",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Best instructors but affordable pricing",
                            description: "All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",
                            icon: "account_balance_wallet",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Really smooth experience of technology",
                            description: "We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",
                            icon: "code",
                            iconType: "material-icons-outlined"
                        },
                        {
                            title: "Your counsellor is just a call away",
                            description: "You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",
                            icon: "call",
                            iconType: "material-icons"
                        }
                    ]
                },
                homeHero: {
                    list: [
                        {
                            title: "Coding classes don’t have to cost so much. Try Yuno’s affordable classes",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/coding-for-kids",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-kids.jpg"
                        },
                        {
                            title: "Fluency builds confidence among children. Try our English speaking classes",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/english-speaking",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-English-Speaking.jpg"
                        },
                        {
                            title: "Communicate. Succeed. English speaking classes for working professionals",
                            subTitle: "",
                            cta: "Learn More",
                            ctaURL: "/english-speaking",
                            category: "",
                            img: ""+ themeURL +"/assets/images/homeHero-professional.jpg"
                        }
                    ]
                    
                },
                chooseType: {
                    title: "English Speaking Online Classes",
                    subTitle: "Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",
                    data: [],
                    loading: false,
                    error: null,
                    success: false,
                    list: [
                        {
                            title: "Working Professionals",
                            type: "professionals",
                            age: "22+",
                            description: "We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                },
                                {
                                    monthly: "2900",
                                    perClass: "242",
                                    level: "Advanced"
                                }
                            ]
                        },
                        {
                            title: "Students",
                            type: "students",
                            age: "15 - 22",
                            description: "Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        },
                        {
                            title: "Kids",
                            type: "kids",
                            age: "8 to 14",
                            description: "There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        },
                        {
                            title: "Homemakers",
                            type: "homemakers",
                            age: "25 to 55",
                            description: "You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",
                            price: [
                                {
                                    monthly: "1800",
                                    perClass: "150",
                                    level: "Intermediate"
                                }
                            ]
                        }
                    ]                    
                },
                meetInstructor: {
                    title: "Instructors who really care",
                    description: "You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                featuredTestimonials: {
                    title: "So immersive that you’d want more classes",
                    description: "We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",
                    footer: {
                        title: "Affordable pricing",
                        description: "So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."
                    }
                },
                spokenEnglishContentBlock: {
                    title: "You don’t only get to practise, but you acquire English language skills for life",
                    description: "The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",
                    img: ""+ themeURL +"/assets/images/languages.svg"
                },
                kidsHero: {
                    description: "I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",
                    img: ""+ themeURL +"/assets/images/codingForKids.svg",
                    author: {
                        name: "Elon Musk",
                        img: ""+ themeURL +"/assets/images/Elon-Musk.jpg",
                        about: "Elon Musk, Founder of Tesla and SpaceX"
                    }
                },
                kidsOfferBanner: {
                    title: "Introducing programming foundation course for 7 - 16 years old",
                    img: ""+ themeURL +"/assets/images/offerPrice.svg",
                    productURL: "/course/learn-to-code-with-mit-scratch",
                    list: [
                        "We make learning to code fun for your child",
                        "24 hours of live classes with homework assignments",
                        "3 classes per week",
                        "Rs. 349 per class",
                        "All classes by expert computer science instructors",
                        "Industry standard curriculum designed by MIT, USA",
                        "Certificate on successful completion"
                    ]
                },
                kidsTestimonials: {
                    title: "What parents have to say"
                },
                kidsHighlights: {
                    title: "Highlights",
                    call: "Call us at <span>+91 62390 91798<\/span>",
                    list: [
                        {
                            type: "liveClass",
                            name: "Live Class",
                            label: "21 hours of live classes"
                        },
                        {
                            type: "oneToOne",
                            name: "One to One",
                            label: "one-to-one doubt clearing sessions"
                        },
                        {
                            type: "price",
                            name: "Price",
                            label: "&#8377;349 per class"
                        },
                        {
                            type: "game",
                            name: "Game",
                            label: "Game development by students"
                        },
                        {
                            type: "programming",
                            name: "Programming",
                            label: "Programming fundamentals"
                        },
                        {
                            type: "flexible",
                            name: "Flexible",
                            label: "Flexible timings with multiple batches"
                        },
                        {
                            type: "certificate",
                            name: "Certificate",
                            label: "Certificate of completion"
                        },
                        {
                            type: "demo",
                            name: "Demo",
                            label: "Free Demo Class"
                        },
                        {
                            type: "cv",
                            name: "CV",
                            label: "Curriculum by MIT, USA"
                        }
                    ]
                },
                gamesShowcase: {
                    title: "Game Developed by Students",
                    list: [
                        {
                            title: "Pop The Balloon Game",
                            img: ""+ themeURL +"/assets/images/PopUPthebaloon.png",
                            url: "https://scratch.mit.edu/projects/419275974/"
                        },
                        {
                            title: "Apple Catcher",
                            img: ""+ themeURL +"/assets/images/Applecatcher.png",
                            url: "https://scratch.mit.edu/projects/423139061/"
                        },
                        {
                            title: "Killing Zombies",
                            img: ""+ themeURL +"/assets/images/Kill-Zombies.png",
                            url: "https://scratch.mit.edu/projects/425774405/"
                        },
                        {
                            title: "Pac-man",
                            img: ""+ themeURL +"/assets/images/PACMAN.png",
                            url: "https://scratch.mit.edu/projects/429660245/"
                        }
                    ]
                },
                ieltsReviews: {
                    title: "What our students say",
                    description: "",
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                ourInstructors: {
                    title: "Our Instructors",
                    description: "You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                curriculum: {
                    title: "Best-in-class curriculum by MIT, USA",
                    description: "The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",
                    link: {
                        label: "See curriculum",
                        url: "/course/learn-to-code-with-mit-scratch"
                    },
                    img: ""+ themeURL +"/assets/images/scratch.svg",
                    author: {
                        name: "Mitchel Resnik",
                        about: "PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",
                        img: ""+ themeURL +"/assets/images/MitchellResnickThumb.jpg",
                        link: {
                            label: "Watch Mitchel Resnik’s Video on TED",
                            url: "https://www.youtube.com/watch?v=Ok6LbV6bqaE"
                        }
                    }
                },
                signUpForm: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                loginWithGoogle: {
                    isLoading: false,
                    data: [],
                    payload: {
                        State: loginState    
                    }
                },
                isUserSignUp: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                enrollment: {
                    isLoading: false,
                    isCourseEnrolled: false,
                    data: [],
                    error: null,
                    errorData: [],
                    success: {
                        username: "",
                        productTitle: "",
                        amount: "",
                        message: "You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"
                    },
                    payload: {
                        id: "",
                        receipt: "",
                        user_id: "",
                        course_id: "",
                        batch_id: "",
                        batch_name: "",
                        batch_end_date: "",
                        payment_gateway: "razorpay",
                        payment_mode: "",
                        amount: "",
                        amount_due: "",
                        total_instalments: 0,
                        instalment_amount: 0,
                        duration: "",
                        status: "",
                        description: "",
                        self_notes: "",
                        currency: "INR",
                        counselor_id: 0,
                        short_url: "",
                        zoho_product_id: ""
                    }
                },
                paymentDismiss: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classSchedule: {
                    isLoading: false,
                    modal: false,
                    successModal: false,
                    data: [],
                    error: null,
                    errorData: [],
                    currentLearner: "",
                    date: typeof moment !== 'undefined' ? new Date(moment(new Date).add(5, 'minutes').utc()) : "",
                    time: typeof moment !== 'undefined' ? new Date(moment(new Date).add(5, 'minutes').utc()) : "",
                    learnerSelected: "",
                    relatedCourses: "",
                    payload: {
                        ClassTitle: "",
                        ClassDescription: "",
                        ClassDate: "",
                        ClassTime: "",
                        ClassDuration: "",
                        classSchedule: "",
                        learner: [],
                        RelatedCourses: "",
                        BatchID: "",
                        academy_id: "",
                    }
                },
                classEdit: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                classDelete: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                classTitle: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                addClassTitle: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: {
                        instructor_id: "",
                        title: ""
                    }
                },
                myLearners: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learner: {
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Schedule",
                            tab: "Upcoming & Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any upcoming and ongoing class",
                            isExploreCTA: false,
                            emptyStateCTA: {
                                ctaLabel: "See past classes & recordings",
                                tab: "yunoPast"
                            },
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true
                        },
                        {
                            title: "My Schedule",
                            tab: "Past Classes & Recordings",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any past class",
                            isExploreCTA: false,
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true,
                            emptyStateCTA: false
                        }
                    ]
                },
                instructorHome: {
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Schedule",
                            tab: "Upcoming & Ongoing",
                            url: "yunoUpcomingOngoing",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            createAction: "Schedule New Class",
                            createURL: "/class-schedule",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any upcoming and ongoing class",
                            isExploreCTA: false,
                            manageState: true,
                            emptyStateCTA: false,
                            count: "",
                            currentCount: "",
                            isLoadMore: false,
                            limit: 20,
                            offset: 0,
                            hasLoadMore: true
                        },
                        {
                            title: "My Schedule",
                            tab: "Past Classes & Recordings",
                            url: "yunoPast",
                            isActive: false,
                            hasData: false,
                            type: "classCard",
                            createAction: "Schedule New Class",
                            createURL: "/class-schedule",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don’t have any past class",
                            isExploreCTA: false,
                            manageState: true,
                            emptyStateCTA: false,
                            count: "",
                            currentCount: "",
                            isLoadMore: false,
                            limit: 20,
                            offset: 0,
                            hasLoadMore: true
                        }
                    ]
                },
                instructorLearners: {
                    scheduleClass: {
                        modal: false,
                        data: []
                    },
                    updateTitle: {
                        modal: false,
                        modalData: null,
                        isLoading: false,
                        payload: {
                            title: "",
                            group_id: ""
                        }
                    },
                    addLearner: {
                        modal: false,
                        modalData: null,
                        isLoading: false,
                        selectedLearner: "",
                        deleteUser: [],
                        newAddedUser: [],
                        payload: {
                            add_user_ids: [],
                            delete_user_ids: [],
                            group_id: ""
                        }
                    },
                    newGroupModal: {
                        modal: false,
                        learners: [],
                        selectedLearner: [],
                        isLoading: false,
                        data: [],
                        payload: {
                            title: "",
                            owner_id: "",
                            role: "",
                            access: "rw",
                            user_ids: []
                        }
                    },
                    loading: false,
                    error: null,
                    success: false,
                    tabs: [
                        {
                            title: "My Learners",
                            tab: "All",
                            url: "yunoAllLearners",
                            isActive: false,
                            hasData: false,
                            type: "tableGrid-2",
                            defaultSort: "name",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don't have any learner",
                            defaultFilters: [
                                {
                                    type: "viewBy",
                                    val: "all"
                                }
                            ],
                            appliedFilters: [],
                            filters: [
                                {
                                    selected: "By Learner Type",
                                    default: "By Learner Type",
                                    type: "viewBy",
                                    module: "dropdown",
                                    isActive: false,
                                    items: [
                                        {
                                            label: "All",
                                            val: "all",
                                            default: "all"
                                        },
                                        {
                                            label: "My Contacts",
                                            val: "myContacts",
                                            default: "all"
                                        },
                                        {
                                            label: "My Referrals",
                                            val: "myReferrals",
                                            default: "all"
                                        }
                                    ]
                                },
                            ],
                            tableOptions: {
                                isFluid: true,
                                pageLoading: false,
                                apiPaginated: true,
                                totalResult: "",
                                perPage: 20,
                                currentPage: 1,
                                limit: 100,
                                offset: 0
                            },
                            manageState: true
                        },
                        {
                            title: "My Groups",
                            tab: "Groups",
                            url: "yunoGroups",
                            isActive: false,
                            hasData: false,
                            type: "groupCard",
                            data: [],
                            loading: false,
                            error: null,
                            errorData: [],
                            success: false,
                            errorMsg: "You don't have any group",
                            count: "",
                            currentCount: "",
                            limit: 20,
                            offset: 0,
                            isLoadMore: false,
                            hasLoadMore: true,
                            manageState: true
                        }
                    ]
                },
                allLearners: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classDetail: {
                    classType: "",
                    data: [],
                    tabs: [],
                    loading: false,
                    error: null,
                    errorMsg: "",
                    errorData: [],
                    success: false
                },
                demoClassEnroll: {
                    isLoading: false,
                    modal: false,
                    successModal: false,
                    error: null,
                    errorData: [],
                    data: [],
                    payload: {
                        class_id: "",
                        instructor_id: "",
                        user_id: "",
                        start_date: "",
                        end_date: "",
                        class_title: "",
                        class_description: ""
                    }
                },
                paymentLink: {
                    isLoading: false,
                    data: [],
                    successModal: false,
                    form: {
                        amount: "",
                        selectedUser: "",
                        user: "",
                        selectedBatch: "",
                        batchID: "",
                        batch: "",
                        courseID: "",
                        course: "",
                        paymentType: "",
                        noteForSelf: "",
                        isInstallment: false,
                        installments: "",
                        isNextSlide: false,
                        howManyInstallments: ["2","3"]
                    },
                    payload: {
                        customer_name: "",
                        customer_email: "",
                        customer_contact: "",
                        type: "link",
                        view_less: 1,
                        amount: "",
                        currency: "INR",
                        payment_description: "",
                        receipt: "",
                        partial_payment: 0
                    }
                },
                crmContacts: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allCourses: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allBatches: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                enrollmentStatus: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createPayment: {
                    data: [],
                    payload: {
                        id: "",
                        receipt: "",
                        Order_id: "",
                        customer_id: "",
                        user_id: "",
                        course_id: "",
                        batch_id: "",
                        batch_name: "",
                        batch_end_date: "",
                        payment_gateway: "generate_link",
                        payment_mode: "online",
                        amount: "",
                        amount_due: "",
                        total_instalments: "",
                        instalment_amount: "",
                        duration: "",
                        status: "",
                        description: "",
                        self_notes: "",
                        currency: "INR",
                        entity: "invoice",
                        counselor_id: "",
                        short_url: "",
                        org_id: "",
                        org_user_id: "",
                        org_user_phone: "",
                        org_user_name: "",
                        org_user_email: "",
                        org_crm_id: "",
                        org_cohort: "",
                        org_programs: "",
                        org_business_unit: "",
                        org_parents: []
                    }
                },
                reviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                updatePaymentLink: {
                    data: [],
                    successModal: false,
                    payload: {
                        id: ""
                    }
                },
                updateLink: {
                    data: [],
                    error: null,
                    errorData: [],
                    loading: false,
                    success: false,
                    successModal: false,
                    payload: {
                        receipt_id: "",
                        razerpay_invoice_id: "",
                        short_url: ""
                    }
                },
                instructorList: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                profileReviews: {
                    title: "Reviews"
                },
                reviewsByType: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                demoClasses: {
                    loading: false,
                    error: null,
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0,
                    isLoadMore: false,
                    data: [],
                    filters: [
                        {
                            title: "Any Time",
                            type: "anytime",
                            isActive: true,
                            data: []
                        },
                        {
                            title: "Morning",
                            type: "morning",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Afternoon",
                            type: "afternoon",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Evening",
                            type: "evening",
                            isActive: false,
                            data: []
                        },
                        {
                            title: "Night",
                            type: "night",
                            isActive: false,
                            data: []
                        }
                    ]
                },
                manageEnroll: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                changeBatch: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                blogs: {
                    data: [],
                    headers: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    limit: 20,
                    offset: 0,
                    isLoadMore: false
                },
                blogDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                postCategories: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                anilLambaHero: {
                    list: [
                        {
                            title: "Anil Lamba on finance",
                            img: ""+ themeURL +"/assets/images/hero-anilLamba.png"
                        }
                    ]
                    
                },
                settings: {
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                notifications: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                counsellorList: {
                    data: [],
                    refinedData: [],
                    loading: false,
                    error: null,
                    success: false
                },
                pageDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    success: false
                },
                googleContacts: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                participants: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: {
                        login_email: "",
                        first_name: "",
                        last_name: "",
                        user_id: ""
                    }
                },
                mapCourses: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                relatedCourses: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                categoryList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    selected: ""
                },
                categoryTaxonomy: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createEBook: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                publishedEBooks: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                deleteEBookAttachment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                eBookEmail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                createResource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                publishedResources: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    manageState: false,
                    defaultFilters: [],
                    appliedFilters: [],
                    filters: []
                },
                deleteResourceAttachment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                resourceEmail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                videoList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                userInfo: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                paymentLinkList: {
                    data: [],
                    changeBatch: "",
                    paymentDetail: "",
                    createPayment: "",
                    generateLink: "",
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                instructorInsights: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                instructorStats: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                goalsAchieved: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerRating: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                reviewVariations: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                completedEnrollments: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                classDelivered: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                activeEnrollment: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                qtRating: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                topIssuesCited: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorReviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                ratingBreakdown: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                countries: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                states: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                cities: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                languages: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                mappedInstructor: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                batchCreateUpdate: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                batchDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                timeSlots: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorAvailability: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorAvailabilityGrid: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                instructorsByCategory: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                capabilities: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                cancelPaymentLink: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                inviteLink: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                invitedByUser: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                updateUserCategory: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                apiTokenExpiryTime: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                apiTokenRefresh: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerProfileHeader: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                staticPage: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                learnerProfile: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                resources: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    tabs: []
                },
                resource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 15,
                    offset: 0,
                },
                learnerInsightsClass: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0,
                },
                videoTestimonials: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                ieltsResults: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 10,
                    offset: 0,
                },
                deleteResource: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                eventDetail: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                moduleWithoutTab: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    limit: 20,
                    offset: 0,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                },
                moduleWithLoadMore: {
                    data: [],
                    other: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 3,
                    offset: 0,
                },
                signupLastStep: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                subjectsList: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                filters: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    filters: null,
                    payload: []
                },
                filterResult: {
                    data: [],
                    additional: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    currentPage: 1,
                    isSidebar: false,
                    limit: 20,
                    offset: 0,
                    payload: [],
                    modal: {
                        isActive: false,
                        data: []
                    },
                    tabs: [],
                    refreshTable: false
                },
                enrollmentV2: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                allReviews: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    count: "",
                    currentCount: "",
                    isLoadMore: false,
                    limit: 20,
                    offset: 0
                },
                enableDisableInstructor: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                module: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                drawer: {
                    data: [],
                    isActive: false,
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                form: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: [],
                    fields: []
                },
                subform: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                subform2: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                subform3: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false,
                    payload: null,
                    isLoading: false,
                    additional: []
                },
                orgAdmin: {
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                referralCode: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                generateCode: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                searchSuggestions: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
                chooseAccountTypeModal: {
                    modal: false,
                    data: [],
                    loading: false,
                    error: null,
                    errorData: [],
                    success: false
                },
                tabs: {
                    data: [],
                    loading: false,
                    additional: [],
                    error: null,
                    errorData: [],
                    success: false
                },
            },
            mutations: {
                gotData(state, options) {
                    if (options.isError) {
                        let getData = options.response.response,
                            isPush = options.pushData !== undefined && options.pushData ? true : false,
                            module = state[options.store],
                            errorMsg = "";

                        if (getData !== undefined) {
                            errorMsg = getData.data !== undefined && getData.data.message !== undefined ? getData.data.message : YUNOCommon.config.errorMsg.common;
                        } else {
                            errorMsg = YUNOCommon.config.errorMsg.common;
                        }
                        
                        console.log(errorMsg)
                        console.log(options.store)

                        if (options.tabs) {
                            updateModule(module.tabs[options.tabIndex], options, getData, false, isPush);
                            module.tabs[options.tabIndex].error = true;
                            module.tabs[options.tabIndex].errorData = errorMsg;
                            if (options.callback) { options.callbackFunc(module.tabs[options.tabIndex].errorData)};
                        } else {
                            updateModule(module, options, getData, false, isPush);
                            module.error = true;
                            module.errorData = errorMsg;
                            if (options.callback) { options.callbackFunc(module.errorData)};
                        }
                    } else {
                        let getData = options.response.data.data,
                            isPush = options.pushData !== undefined && options.pushData ? true : false,
                            hasError = options.response.data,
                            module = state[options.store];

                        if (options.tabs) {
                            if (hasError.code === 204) {
                                module.tabs[options.tabIndex].error = true;
                                module.tabs[options.tabIndex].errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };

                            if (hasError.code === 401) {
                                module.tabs[options.tabIndex].error = true;
                                module.tabs[options.tabIndex].errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };

                            updateModule(module.tabs[options.tabIndex], options, getData, true, isPush);
                            
                        } else {
                            if (hasError.code === 204) {
                                module.error = true;
                                module.errorData = hasError.message;
                                console.log(hasError.message)
                                console.log(options.store)
                            }

                            if (hasError.code === 401) {
                                module.error = true;
                                module.errorData = hasError.message;

                                console.log(hasError.message)
                                console.log(options.store)
                            };
                                
                            updateModule(module, options, getData, true, isPush);
                        }
                        
                        if (options.callback) { options.callbackFunc(options)};
                    }
                },
                thirdParty(state, options) {
                    module = state[options.store];

                    if (module.error) {
                        options.callbackFunc(module)
                    } else {
                        options.callbackFunc(options)
                    }
                },
                mapCourses(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        if (options.overrideData === undefined || options.overrideData) {
                            updateModule(state[options.module], options, getData, false);
                        }
                        
                        if (options.callback) { options.callbackFunc(state[options.module].errorData)};
                    } else {
                        let getData = options.response.data.data;

                        if (options.overrideData === undefined || options.overrideData) {
                            updateModule(state[options.module], options, getData, true);    
                        }
                        
                        if (options.callback) { options.callbackFunc(options)};
                    }
                },
                reviewsByType(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        updateModule(state[options.module], options, getData, false);
                    } else {
                        let getData = options.response.data.data;

                        if (!options.isTabAdded) {
                            state.instructor.data.tabs.push(
                                {
                                    tab: "Reviews",
                                    url: "yunoFeaturedTestimonials"
                                }
                            );    
                        }

                        updateModule(state[options.module], options, getData, true);

                        options.isTabAdded = true;

                        setTimeout(() => { 
                            options.componentInstance.$refs.testimonialWrapper.initSlider();
                        }, 30);
                    }
                },
                crmContacts(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        updateModule(state[options.module], options, getData, false);
                    } else {
                        let getData = options.response.data.data;

                        for (var i = 0; i < getData.length; i++) {
                            getData[i].username_email_phone = `${getData[i].username_email} (${getData[i].phone})`;
                        };

                        updateModule(state[options.module], options, getData, true);
                    }
                },
                classDelete(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `${YUNOCommon.config.errorMsg.common}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        })
                    } else {
                        state.loader.isActive = false;
                        state.loader.overlay = false;

                        if (options.classID !== undefined) {
                            let getClasses = state.instructorHome.tabs[0].data;

                            YUNOCommon.removeObjInArr(getClasses, "id", options.classID); 

                            if (getClasses.length === 0) {
                                state.instructorHome.tabs[0].error = true;
                            };

                            options.componentInstance.$buefy.toast.open({
                                duration: 5000,
                                message: `Class successfully deleted`,
                                position: 'is-bottom'
                            });
                        } else {
                            options.componentInstance.$buefy.dialog.alert({
                                title: 'Delete',
                                message: 'Class successfully deleted',
                                confirmText: 'Ok',
                                type: 'is-danger',
                                onConfirm: () => window.location.href = YUNOCommon.config.host() + "/instructor"
                            });
                        };

                        state[options.module].data = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        
                    }
                },
                demoClassEnroll(state, options) {
                    if (options.isError) {
                        state[options.module].isLoading = false;

                        if (options.classIndex !== undefined) {
                            state.loader.isActive = false;
                            state.loader.overlay = false;
                        }

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `${YUNOCommon.config.errorMsg.common}`,
                            position: 'is-bottom',
                            type: 'is-danger'
                        })
                        
                    } else {
                        localStorage.removeItem('demoClassState');
                        state[options.module].isLoading = false;
                        state[options.module].data = options.response.data.data;

                        //Instructor Profile
                        if (options.classIndex !== undefined) {
                            let getModule = state[options.parentModule].data[options.classIndex];

                            if (getModule !== undefined) {
                                getModule.isLoading = false    
                                getModule.is_enrolled = true;
                            }

                            state.loader.isActive = false;
                            state.loader.overlay = false;
                        } else {
                            state[options.parentModule].data.is_enrolled = true;
                        }

                        options.componentInstance.$buefy.toast.open({
                            duration: 5000,
                            message: `You have successfully enrolled`,
                            position: 'is-bottom'
                        })

                        const isUserSignUp = localStorage.getItem('userSignUp');

                        if (isUserSignUp !== null && isUserSignUp === "pending") {
                            localStorage.setItem('oldUserState', "/learner/");
                            localStorage.setItem('userState', "/sign-up");
                            window.location.href = YUNOCommon.config.host() + "/sign-up";    
                        }
                    }
                },
                userRole(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;    
                    } else {
                        state[options.module].data = options.response.data.data;
                        state[options.module].response = options.response.data;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        if (options.componentInstance !== "") {
                            options.componentInstance.getUserRole(options.response.data.data);   
                        }
                    }
                },
                userProfile(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false;
                    } else {
                        state[options.module].data = options.response.data.data;
                        state[options.module].success = true;
                        state[options.module].loading = false;

                        if (options.instance !== "" && typeof options.instance.gotUserProfile !== 'undefined') {
                            options.instance.gotUserProfile(options.response.data.data);   
                        }
                    }
                },
                classDetail(state, options) {
                    if (options.isError) {
                        let getData = options.response;

                        if (options.nestedTab === "enrolled") {
                            state[options.module].tabs[options.tabIndex].loading = false;
                            state[options.module].tabs[options.tabIndex].pageLoading = false;
                            state[options.module].tabs[options.tabIndex].error = getData;
                        } else {
                            updateModule(state[options.module], options, getData, false);
                        }
                        
                    } else {
                        if (options.callback) { 
                            options.callbackFunc(options, options.response)
                        };
                    }
                },
                instructorMyCourses(state, options) {
                    if (options.isError) {
                        if (options.batches) {
                            let getTab = state[options.module].data[options.courseIndex].tabs[options.tabIndex];

                            getTab.error = options.response
                        } else {
                            state[options.module].error = options.response;    
                        }
                        
                        state[options.module].success = true;
                        state[options.module].loading = false; 
                    } else {
                        let getData = options.response.data.data;

                        if (options.batches) {
                            state[options.module].data[options.courseIndex].isBatches = true;

                            for (let i = 0; i < getData.length; i++) {
                                state[options.module].data[options.courseIndex].tabs[options.tabIndex].data.push(getData[i])
                            };

                            let getTab = state[options.module].data[options.courseIndex].tabs[options.tabIndex];
                            
                            getTab.count = options.response.data.count;
                            getTab.currentCount = getTab.data.length;
                            getTab.offset = getTab.currentCount;
                            getTab.isLoadMore = false;
                        } else {
                            for (let i = 0; i < getData.length; i++) {
                                getData[i].isBatches = false;
                                getData[i].isLoading = false;

                                getData[i].tabs = [
                                    {
                                        tab: "Upcoming & Ongoing",
                                        isActive: true,
                                        type: "upcomingOngoing",
                                        data: [],
                                        isLoadMore: false,
                                        count: "",
                                        currentCount: "",
                                        limit: 4,
                                        offset: 0,
                                        error: null 
                                    },
                                    {
                                        tab: "Past",
                                        isActive: false,
                                        type: "past",
                                        data: [],
                                        isLoadMore: false,
                                        count: "",
                                        currentCount: "",
                                        limit: 4,
                                        offset: 0,
                                        error: null
                                    }
                                ]
                            };
                            state[options.module].data = getData;
                        }

                        state[options.module].success = true;
                        state[options.module].loading = false;
                    }
                },
                allLearners(state, options) {
                    if (options.isError) {
                        state[options.module].error = options.response;
                        state[options.module].success = true;
                        state[options.module].loading = false; 

                        if (options.nested !== undefined) {
                            state[options.nested].tabs[0].error = true;
                        };
                    } else {
                        let getData = options.response.data.data;

                        if (options.nested !== undefined) {
                            let getColumns = getData.columns,
                                getRows = getData.rows,
                                newCol = {
                                    field: "actions",
                                    label: "Actions",
                                    sortable: false
                                };
                            
                            if (options.userRole === "Instructor") {
                                getColumns.push(newCol);    
                            }

                            for (var i = 0; i < getRows.length; i++) {
                                getRows[i].scheduleClass = {
                                    active: true,
                                    url: "/class-schedule/?learnerID="+ getRows[i].id +""
                                };
                            }

                            state[options.nested].tabs[0].data = getData;
                            state[options.nested].tabs[0].totalResult = options.response.data.count
                            state[options.nested].tabs[0].pageLoading = false;
                        };

                        state[options.module].data = getData.rows;
                        state[options.module].success = true;
                        state[options.module].loading = false;
                    }
                },
                instructorHome(state, options) {
                    const updateData = function(isSuccess) {

                        if (isSuccess) {
                            state[options.module].tabs[options.index].hasData = true;
                            state[options.module].tabs[options.index].data = options.response.data.data;    
                        } else {
                            state[options.module].tabs[options.index].hasData = false;
                            state[options.module].tabs[options.index].data = options.response;
                            state[options.module].tabs[options.index].error = true;
                        }

                        state[options.module].success = true;
                        state[options.module].loading = false;
                        state[options.module].tabs[options.index].success = true;
                        state[options.module].tabs[options.index].loading = false;
                    }

                    if (options.isError) {
                        updateData(false);
                    } else {
                        updateData(true);
                    }
                },
                instructorLearners(state, options) {
                    const updateData = function(isSuccess) {
                        if (isSuccess && options.response.data !== undefined) {
                            let getData = options.response.data.data;

                            if (options.form !== undefined) {
                                state[options.module][options.form].data = getData;
                                state[options.module][options.form].modal = false;

                                if (options.form === "newGroupModal") {
                                    let groupObj = {
                                        date: YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(getData.group_created_time)[0]),
                                        group_created_time: getData.group_created_time,
                                        group_id: ""+getData.group_id+"",
                                        group_name: options.payload.title,
                                        total_users: 0,
                                        user: options.learners,
                                        scheduleClassURL: `/class-schedule/?groupID=${getData.group_id}`
                                    };

                                    state[options.module].tabs[options.index].data.unshift(groupObj);

                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Group successfully created`,
                                        position: 'is-bottom'
                                    });
                                };

                                if (options.form === "addLearner") {

                                    let getGroups = state[options.module].tabs[options.index].data.filter(function (el) {
                                        return el.group_id === options.group.group_id;
                                    });

                                    getGroups[0].user = options.group.user;

                                    let getModalObj = state.instructorLearners.addLearner;

                                    //Resetting data
                                    getModalObj.selectedLearner = "";
                                    getModalObj.payload.group_id = "";
                                    getModalObj.payload.owner_id = "";
                                    getModalObj.payload.user_ids = [];
                                    getModalObj.deleteUser = [];
                                    getModalObj.newAddedUser = [];
                                    getModalObj.payload.add_user_ids = [];
                                    getModalObj.payload.delete_user_ids = [];

                                    options.componentInstance.learnerAdded = true;
                                    options.componentInstance.group = null;
                                    options.componentInstance.learnersList = [];

                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Learners added successfully`,
                                        position: 'is-bottom'
                                    });   
                                }

                                if (options.form === "updateTitle") {
                                    options.componentInstance.$buefy.toast.open({
                                        duration: 5000,
                                        message: `Group title updated successfully`,
                                        position: 'is-bottom'
                                    });   
                                }

                            } else {
                                const groupTab = state[options.module].tabs[options.index];

                                groupTab.hasData = true;
                                groupTab.isLoadMore = false;

                                for (var i = 0; i < getData.length; i++) {
                                    getData[i].date = YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(getData[i].group_created_time)[0]);
                                    getData[i].scheduleClassURL = `/class-schedule/?groupID=${getData[i].group_id}`;
                                    groupTab.data.push(getData[i])
                                };

                                groupTab.count = options.response.data.count;
                                groupTab.currentCount = groupTab.data.length;
                                groupTab.offset = groupTab.currentCount;
                            }

                        } else {
                            if (options.form !== undefined) {
                                state[options.module][options.form].modal = false;

                                options.componentInstance.$buefy.toast.open({
                                    duration: 5000,
                                    message: `${options.response.response.data.message}`,
                                    position: 'is-bottom',
                                    type: 'is-danger'
                                });
                            } else {
                                state[options.module].tabs[options.index].hasData = false;
                                state[options.module].tabs[options.index].data = options.response;
                                state[options.module].tabs[options.index].error = true;
                                state[options.module].tabs[options.index].errorData = options.response;
                            }
                        }

                        if (options.form !== undefined) {
                            state[options.module][options.form].isLoading = false
                        } else {
                            state[options.module].success = true;
                            state[options.module].loading = false;
                            state[options.module].tabs[options.index].success = true;
                            state[options.module].tabs[options.index].loading = false;    
                        }
                    };

                    if (options.isError) {
                        updateData(false);
                    } else {
                        updateData(true);

                        if (options.response.data.code === 204) {
                            state[options.module].tabs[options.index].error = true;
                            state[options.module].tabs[options.index].errorData = options.response.data.message;

                            console.log(options.response.data.message)
                        };
                    }
                }
            },
            actions: {
                fetchThirdPartyData({commit, state}, options) {
                    let module = state[options.store];

                    module.loading = true;

                    axios.get(options.apiURL, {
                        headers: options.headers !== undefined ? options.headers : "",
                    }).then(response => {
                        module.loading = false;
                        options.response = response;
                        module.error = null;
                        commit(options.module, options);
                    }).catch(e => {
                        module.loading = false;
                        module.errorData = e;
                        options.response = e;
                        module.error = true;
                        commit(options.module, options); 
                    })
                },
                postThirdPartyData({commit, state}, options) {
                    let module = state[options.store];
                    module.loading = true;

                    axios.defaults.timeout = options.timeout === undefined ? 0 : options.timeout; 

                    if (options.method === "post") {
                        axios.post(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "patch") {
                        axios.patch(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "head") {
                        axios.head(options.apiURL, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else if (options.method === "put") {
                        axios.put(options.apiURL, options.payload, {
                            headers: options.headers,
                        }).then(response => {
                                module.loading = false;
                                options.response = response;
                                module.error = null;
                                commit(options.module, options);
                        }).catch(e => {
                            module.loading = false;
                            module.errorData = e;
                            options.response = e;
                            module.error = true;
                            commit(options.module, options);
                        })
                    } else {
                        console.log("not defined")
                    }

                    
                },
                fetchData({commit, state}, options) {
                    let module = "",
                        getHeaders = "";

                    if (isLoggedIn !== "0") {
                        getHeaders = {
                            'authorization': state.config.yunoAPIToken
                        }
                    } else {
                        getHeaders = {
                            'authorization': ""
                        }    
                    };

                    if (options.store !== undefined) {
                        if (options.tabs) {
                            module = state[options.store].tabs[options.tabIndex];
                        } else {
                            module = state[options.store];
                        }
                    } else {
                        module = state[options.module];
                    }

                    if (options.moduleLoading === undefined || options.moduleLoading) {
                        module.loading = true;
                    } else {
                        module.loading = false;
                    }

                    if (options.moduleTabs && options.isTabLoader) {
                        module.loading = true;
                    };

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.get(options.apiURL, {
                        headers: getHeaders,
                    }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);

                            let hasErrorCode = options.response?.data?.data?.status ?? '';

                            if (hasErrorCode === 403) {
                                if (!state.config.unauthorizedModal) {
                                    if (options.store === "userInfo") {
                                        state.header.success = true
                                        state.footer.success = true
                                        state.capabilities.success = true
                                    }
                                    state.config.unauthorizedModal = true
                                }
                            }
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;
                            state[options.store].loading = false;
                            state[options.store].success = true;

                            let module = "";

                            if (options.store !== undefined) {
                                if (options.tabs) {
                                    module = state[options.store].tabs[options.tabIndex];
                                } else {
                                    module = state[options.store];
                                }
                            } else {
                                module = state[options.module];
                            }
                            
                            if (module.errorData !== undefined && e.response !== undefined) {
                                module.errorData = e.response
                            }
                            
                            let hasErrorCode = e.response.data.data.status;

                            if (hasErrorCode === 403) {
                                if (!state.config.unauthorizedModal) {
                                    if (options.store === "userInfo") {
                                        state.header.success = true;
                                        state.footer.success = true;
                                        state.capabilities.success = true;
                                    };
                                    state.config.unauthorizedModal = true;        
                                };
                            };

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                putData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) {
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }

                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.put(options.apiURL, options.payload, {
                        headers: getHeaders
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                awsPutData({commit, state}, options) {
                    let getHeaders = {
                        'accept': 'application/json',
                        'content-type': 'application/json',
                        'authorization': ""
                    };
                    
                    if (isLoggedIn !== "0") {
                        getHeaders.authorization = state.config.yunoAPIToken
                    } else {
                        getHeaders.authorization = "";
                    };

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.put(options.apiURL, JSON.stringify(options.payload), {
                        headers: getHeaders
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;
                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                postData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) { // Add authorization key value pair in case headers is defined
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }
                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json',
                            // 'authorization': ""
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.post(options.apiURL, options.payload, {
                        headers: getHeaders,
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }

                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                awsPostData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) {
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }
                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.post(options.apiURL, JSON.stringify(options.payload), {
                        headers: getHeaders,
                        }).then(response => {
                            options.response = response;
                            options.isError = false;
                            commit(options.module, options);
                        })
                        .catch(e => {
                            console.log(options.store)
                            console.log(e);
                            state[options.store].error = true;

                            let getModule = "";

                            if (options.store !== undefined) {
                                getModule = state[options.store];
                            } else {
                                getModule = state[options.module];
                            }
                            
                            if (getModule.errorData !== undefined && e.response !== undefined) {
                                getModule.errorData = e.response
                            }
                            
                            options.response = e;
                            options.isError = true;
                            commit(options.module, options);
                        })
                },
                deleteData({commit, state}, options) {
                    let getHeaders = "";

                    if (options.headers !== undefined) { // Add authorization key value pair in case headers is defined
                        if (isLoggedIn !== "0") {
                            options.headers.authorization = state.config.yunoAPIToken
                        } else {
                            options.headers.authorization = ""
                        }

                        getHeaders = options.headers
                    } else {
                        getHeaders = {
                            'content-type': 'text/json'
                        }

                        if (isLoggedIn !== "0") {
                            getHeaders.authorization = state.config.yunoAPIToken
                        } else {
                            getHeaders.authorization = ""
                        }
                    }

                    axios.defaults.timeout = options.timeout === undefined ? 60 * 1 * 1000 : options.timeout;

                    axios.delete(options.apiURL, {
                        headers: getHeaders,
                        data: options.payload
                    }).then(response => {
                        options.response = response;
                        options.isError = false;
                        commit(options.module, options);
                    })
                    .catch(e => {
                        console.log(options.store)
                        console.log(e);
                        state[options.store].error = true;
                        options.response = e;
                        options.isError = true;
                        commit(options.module, options);
                    });
                }
            },
            getters: {
                getSignInURL() {
                    const scopes = [
                        "email",
                        "profile"
                    ];
                    
                    // Old google auth URL
                    // let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state=stateUpdate&scope="+ scopes.join("%20") +"&approval_prompt=auto&include_granted_scopes=true"
                    
                    // New google auth URL
                    let url = "https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+ gRU +"&client_id="+ gCID +"&scope="+ scopes.join("%20") +"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow"
                    
                    return url;
                },
                googleMeet() {
                    const scopes = [
                        "email",
                        "profile",
                        "https://www.googleapis.com/auth/calendar",
                        "https://www.googleapis.com/auth/calendar.events",
                        "https://www.googleapis.com/auth/admin.reports.audit.readonly",
                        "https://www.googleapis.com/auth/drive.readonly"
                    ];

                    const stateObj = {
                            googleMeet: true
                        },
                        state = encodeURI(JSON.stringify(stateObj));

                    // Old URL
                    // let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&prompt=consent&include_granted_scopes=true"

                    // New URL
                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&approval_prompt=force&flowName=GeneralOAuthFlow"
                    
                    return url;
                },
                googleContacts() {
                    const scopes = [
                        "email",
                        "profile",
                        "https://www.googleapis.com/auth/contacts.readonly",
                        "https://www.googleapis.com/auth/contacts.other.readonly"
                    ];

                    const stateObj = "stateUpdate",
                    state = encodeURI(JSON.stringify(stateObj));

                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state="+ state +"&scope="+ scopes.join("%20") +"&approval_prompt=force&flowName=GeneralOAuthFlow";
                    
                    return url;
                },
                getSwitchAccountURL(state) {
                    let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+ gCID +"&redirect_uri="+ gRU +"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow";
                    
                    return url;
                }
            }
        });

        return store;
    };

    return {
        init: init
    };

})(jQuery);