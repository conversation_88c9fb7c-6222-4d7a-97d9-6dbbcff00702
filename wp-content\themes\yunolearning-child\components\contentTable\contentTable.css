.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

#app .contentTable {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .contentTable {
    padding: 60px 0 40px 0;
  }
}

#app .contentTable .alignC {
  text-align: center;
}

#app .contentTable .container {
  margin: 0 auto;
  padding: 0 20px;
}

#app .contentTable .tableWrapper {
  margin-bottom: 30px;
}

#app .contentTable .tableWrapper table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #E6E6E6;
}

#app .contentTable .tableWrapper table thead {
  background: #f5dddb;
}

#app .contentTable .tableWrapper table thead tr th {
  padding: 20px;
  text-align: start;
}

@media (min-width: 768px) {
  #app .contentTable .tableWrapper table thead tr th {
    text-align: center;
  }
}

#app .contentTable .tableWrapper table tbody tr {
  border-bottom: 1px solid #E6E6E6;
}

#app .contentTable .tableWrapper table tbody tr .wrapper {
  padding: 10px 10px;
}

#app .contentTable .tableWrapper table tbody tr td .feature {
  padding: 1rem 2rem 0rem 2rem;
}

@media (min-width: 768px) {
  #app .contentTable .tableWrapper table tbody tr td .feature {
    padding: 0.8rem 2rem;
  }
}

#app .contentTable .tableWrapper table tbody tr td .description {
  padding: 0rem 2rem 1.5rem 2rem;
}

@media (min-width: 768px) {
  #app .contentTable .tableWrapper table tbody tr td .description {
    padding: 0.8rem 2rem;
  }
}
/*# sourceMappingURL=contentTable.css.map */