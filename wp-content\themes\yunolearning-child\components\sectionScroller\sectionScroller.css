#app .sectionScroller {
  padding: 30px 0;
  top: 0px;
  z-index: 999;
  background-color: white;
  position: -webkit-sticky;
  position: sticky;
}

@media (min-width: 768px) {
  #app .sectionScroller {
    padding: 60px 0 15px 0;
    top: 30px;
    z-index: 9;
  }
}

#app .sectionScroller .sectionTabs .tabs ul li a {
  border-color: #e6e6e6;
}

#app .sectionScroller .sectionTabs .tabs ul li.is-active a {
  background-color: #a81e22;
  color: #ffffff;
  border-color: #e6e6e6;
}

#app .sectionScroller .sectionTabs .tab-content {
  padding: 0;
}
/*# sourceMappingURL=sectionScroller.css.map */