@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.dark87 {
  @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
  @include setFontColor($primaryCopyColor, 0.6);
}

.dark38 {
  @include setFontColor($primaryCopyColor, 0.38);
}

.yunoPreviewAvailability {
  $cell-height: 32px;
  $cell-spacing: 8px;
  $border-radius: 6px;
  $available-color: rgb(84, 172, 117);
  $unavailable-color: #E0E0E0;
  
  width: 100%;
  padding: 20px;
  
  .days-row {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: $cell-spacing;
    margin-bottom: 5px;
    padding-right: 100px; 
    border-bottom: 1px solid #e6e6e6;
    
    .dayHeader {
      text-align: start;
      padding: 8px 0;
    }
  }
  
  .grid-container {
    position: relative;
    padding-right: 100px; // Space for time labels
    
    .slots-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      grid-auto-rows: $cell-height;
      gap: $cell-spacing;
      
      .slot {
        width: auto;
        height: 26px;
        background-color: $unavailable-color;
        border-radius: $border-radius;

        @media screen and (min-width: 768px) {
          width: 53px;
        }
        
        &.available {
          background-color: $available-color;
        }
      }
    }
    
    .time-labels {
      position: absolute;
      right: 0;
      top: 0;
      width: 90px;
      
      .time-label {
        height: $cell-height;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 12px;
        color: #666;
        margin-bottom: $cell-spacing;
      }
    }
  }
  
  @media screen and (max-width: 768px) {
    padding: 10px;
    
    .days-row {
      padding-right: 80px;
      gap: 4px;
      
      .dayHeader {
        font-size: 12px;
        padding: 4px 0;
      }
    }
    
    .grid-container {
      padding-right: 80px;
      
      .slots-grid {
        gap: 4px;
      }
      
      .time-labels {
        width: 75px;
        
        .time-label {
          font-size: 11px;
          margin-bottom: 4px;
        }
      }
    }
  }
}

