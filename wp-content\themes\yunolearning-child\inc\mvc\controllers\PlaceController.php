<?php

namespace V4;

/**
 *  Place Controller
 */


class PlaceController extends Controller
{
    /**
     * Constructor to initialize the UserController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');


        $this->loadModel('org');
        $this->loadModel('place');
        
    }

    /**
     * Get user information
     *
     * @param WP_REST_Request $request The request object
     * @return void
     */
    public function addPlace($request)
    {
        try {
            // Get request data
            $payload = json_decode($request->get_body(), true);

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            // Validate each field using the common functions
            $validationChecks = [
                "org_id" => '/^[0-9]+$/',  // org should be numeric. Adjust max length if needed.
                "type" => '/^(SCHOOL|COLLEGE|COMMERCIAL_BUILDING)$/[LABEL:Building Type]',
                "name" => '/^.{1,255}$/[LABEL:Name]', 
                "short_description" => '/^.{1,150}$/u[REQ:NOT][LABEL:Short Description]', // up to 150 characters
                // For long_description, exact length or word count checks are complex with regex.
                // This just ensures it is a string of reasonable length. Adjust as needed.
                "long_description" => '/^.{1,5000}$/us[REQ:NOT][LABEL:Long Description]',
            
                "address" => [
                    "type" => '/^(HOME|OFFICE|OTHER)$/[LABEL:Address Type]',
                    "title" => '/^.{1,255}$/[LABEL:Title]',
                    "formatted_address" => '/^.{1,500}$/[LABEL:Formatted Address]',
                    "address_1" => '/^.{1,255}$/[LABEL:Street Address]',
                    "address_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Additional Address]',
                    "floor" => [
                        "type" => '/^(GROUNDPLUS|ONEPLUS)$/[REQ:NOT][LABEL:Floor Type]',
                        "number" => '/^.{1,255}$/[REQ:NOT][LABEL:Floor Number]'
                    ],
                    "landmark" => '/^.{0,255}$/[REQ:NOT][LABEL:Landmark]',
                    "city" => '/^[a-zA-Z0-9_\s-]{1,255}$/[LABEL:City]',
                    "state" => [
                        "name" => '/^[a-zA-Z_\s-]{1,255}$/[LABEL:State Name]',
                        "code" => '/^[A-Z]{2}$/[LABEL:State Code]'
                    ],
                    "country" => [
                        "id" => '/^[0-9]{1,5}$/[LABEL:Country ID]',
                        "name" => '/^[a-zA-Z_\s-]{1,255}$/[LABEL:Country Name]',
                        "code" => '/^[A-Z]{2}$/[LABEL:Country Code]'
                    ],
                    "pin_zip" => '/^[A-Za-z0-9-]{1,10}$/[LABEL:Postal Code]',
                    "coordinates" => [
                        "latitude" => '/^-?\d+(\.\d+)?$/[LABEL:Latitude]',
                        "longitude" => '/^-?\d+(\.\d+)?$/[LABEL:Longitude]'
                    ],
                    "google_maps" => [
                        "place_id" => '/^.{1,255}$/[REQ:NOT][LABEL:Google Maps Place ID]',
                        "type" => '/^[a-zA-Z0-9_\s-]{0,255}$/[REQ:NOT][LABEL:Google Maps Type]',
                        "colloquial_area" => '/^.{0,255}$/[REQ:NOT][LABEL:Colloquial Area]',
                        "locality" => '/^.{0,255}$/[REQ:NOT][LABEL:Locality]',
                        "sublocality" => [
                            "sublocality_level_1" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 1]',
                            "sublocality_level_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 2]',
                            "sublocality_level_3" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 3]',
                            "sublocality_level_4" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 4]',
                            "sublocality_level_5" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 5]'
                        ],
                        "neighborhood" => '/^.{0,255}$/[REQ:NOT][LABEL:Neighborhood]',
                        "postal_code" => '/^.{0,20}$/[REQ:NOT][LABEL:Postal Code]',
                        "floor" => '/^.{0,10}$/[REQ:NOT][LABEL:Floor]',
                        "landmark" => '/^.{0,255}$/[REQ:NOT][LABEL:Landmark]',
                        "administrative_area" => [
                            "administrative_area_level_1" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 1]',
                            "administrative_area_level_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 2]',
                            "administrative_area_level_3" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 3]',
                            "administrative_area_level_4" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 4]',
                            "administrative_area_level_5" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 5]',
                            "administrative_area_level_6" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 6]',
                            "administrative_area_level_7" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 7]'
                        ]
                    ]
                ],
            
                "facilities" => [
                    "car_parking" => [
                        "self_parking" => 'boolean[LABEL:Self Parking]',
                        "valet_service" => 'boolean[LABEL:Valet Service]'
                    ],
                    "bike_parking" => 'boolean[LABEL:Bike Parking]'
                ],

                "open_hours" => [
                    // open_hours is an array, each item having a "days" array.
                    // We'll define rules for each element of "days".
                    [
                        "day" => '/^(MON|TUE|WED|THU|FRI|SAT|SUN)$/[LABEL:Day]',
                        "is_available" => 'boolean[LABEL:Is Available]',
                        "time_slot" => [
                            [
                                "start" => '/^([0-9]{1,2}):([0-9]{1,2})$/[LABEL:Start Time]', // HH:MM 24-hour format
                                "end" => '/^([0-9]{1,2}):([0-9]{1,2})$/[LABEL:End Time]'
                            ]
                        ]
                    ]
                ]    
            ];
            
            $errors = $this->validate->validateData($payload, $validationChecks);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            //GET ES record with payload

            $orgData = $this->orgModel->getOrganization($payload['org_id'],['schema' => 'Organizatio_Minimal']);

            if (!$orgData) {
                return $this->response->error('GET_FAIL',['replace'=>'Org']);
            }

            if ($placeId = $this->placeModel->addPlace($payload)) {
                return $this->response->success('POST_INSERT', ["placeId"=>$placeId], ['replace'=>'Place'] );
            } else {
                return $this->response->error('POST_INSERT_FAIL', ['replace'=>'Place'] );
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('POST_INSERT_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieve place information by its ID.
     *
     * @param int $placeId The ID of the place to retrieve.
     * @return WP_REST_Response|WP_Error The response object or an error object.
     */
    public function getPlace($request)
    {
        try {
            // Get request data
            $placeId = (int)$request['placeId'];

            // Validate each field using the common functions
            $validation_checks = [
                'placeId' => 'numeric'
            ];

            $prmBody = [
                'placeId' => $placeId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }
            // Retrieve place data
            $placeData = $this->placeModel->getPlace($placeId);

            // If place not found, return an error
            if (!$placeData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Place']);
            }

            // If you need additional validation checks on the retrieved data, add them here
            // For example:
            // if (!$this->someAdditionalValidation($placeData)) {
            //     return $this->response->error('VALIDATION_FAIL', ['message' => 'Additional validation failed.']);
            // }

            // Return success response with place details
            return $this->response->success('GET_SUCCESS', $placeData, ['message' => 'Place details retrieved successfully']);

        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    function getPlaces($request)
    {  
        try {
            $getVars = $request->get_query_params();
            $viewType = $request->get_param('viewType');

            if(isset($getVars['org_id'])){
                $query['custom'] = [
                    "query" => [
                        "term" => [
                            "org_id" => $getVars['org_id']
                        ]
                    ],
                    "sort" => [
                        [
                            "place_id" => [
                                "order" => "asc"
                            ]
                        ]
                    ]
                ];
            } else {
                $query['custom'] = [
                    "query" => [
                        "match_all" => (object)[]
                    ],
                    "sort" => [
                        [
                            "place_id" => [
                                "order" => "asc"
                            ]
                        ]
                    ]
                ];
            }

            if (isset($getVars['limit']) && isset($getVars['offset'])) {
                $query['qryStr'] = [
                    "from" => $getVars['offset'],
                    "size" => $getVars['limit']
                ];
            }
            
            //$query['cache'] = true;
            $placesData = $this->placeModel->getPlaces($query, ['schema' => 'Refer#Place_Basic']);
            // If place not found, return an error
            if (!$placesData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Places']);
            }

            if($viewType == 'grid'){
                
                $columns =[
                            [
                                "field"    => "org.name",
                                "label"    => "Organization",
                                "sortable" => true
                            ],
                            [
                                "field"    => "type",
                                "label"    => "Type",
                                "sortable" => true
                            ],
                            [
                                "field"    => "name",
                                "label"    => "Name",
                                "sortable" => true
                            ],
                            [
                                "field"    => "short_description",
                                "label"    => "Short Description",
                                "sortable" => true
                            ],
                            [
                                "field"    => "address.city",
                                "label"    => "City",
                                "sortable" => true
                            ],
                            [
                                "field"    => "address.state",
                                "label"    => "State",
                                "sortable" => true
                            ],
                            [
                                "field"    => "address.country.name",
                                "label"    => "Country",
                                "sortable" => true
                            ],
                            [
                                "field"    => "coordinates.latitude",
                                "label"    => "Latitude",
                                "sortable" => true
                            ],
                            [
                                "field"    => "coordinates.longitude",
                                "label"    => "Longitude",
                                "sortable" => true
                            ],
                            [
                                "field"    => "google_maps.place_id",
                                "label"    => "Google Maps Place ID",
                                "sortable" => true
                            ],
                            [
                                "field"    => "google_maps.type",
                                "label"    => "Google Maps Type",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.car_parking.self_parking",
                                "label"    => "Self Parking",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.car_parking.valet_service",
                                "label"    => "Valet Service",
                                "sortable" => true
                            ],
                            [
                                "field"    => "facilities.bike_parking",
                                "label"    => "Bike Parking",
                                "sortable" => true
                            ],
                            [
                                "field"    => "open_hours",
                                "label"    => "Open Hours",
                                "sortable" => true
                            ],
                            [
                                "field"    => "created_at",
                                "label"    => "Created At",
                                "sortable" => true
                            ],
                            [
                                "field"    => "updated_at",
                                "label"    => "Updated At",
                                "sortable" => true
                            ]
                        ];
                        
                $placesData['data'] = [
                    "columns" => $columns,
                    "rows" => $placesData['data']
                ];

                return $this->response->success('GET_SUCCESS', $placesData, ['message' => 'Places data retrieved successfully']);
            }
            // Return success response with place details
            return $this->response->success('GET_SUCCESS', $placesData, ['message' => 'Places data retrieved successfully']);
            
        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }
    /**
     * Update user region information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function updPlace($request){
        try {

            $placeId = (int)$request['placeId'];

            // Get request data
            $payload = json_decode($request->get_body(), true);

            // Validate each field using the common functions
            $validation_checks = [
                'placeId' => 'numeric'
            ];

            $prmBody = [
                'placeId' => $placeId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            // Validate each field using the common functions
            $validationChecks = [
                "org_id" => '/^[0-9]+$/',  // org should be numeric. Adjust max length if needed.
                "type" => '/^(SCHOOL|COLLEGE|COMMERCIAL_BUILDING)$/[LABEL:Building Type]',
                "name" => '/^.{1,255}$/[LABEL:Name]', 
                "short_description" => '/^.{1,150}$/u[REQ:NOT][LABEL:Short Description]', // up to 150 characters
                // For long_description, exact length or word count checks are complex with regex.
                // This just ensures it is a string of reasonable length. Adjust as needed.
                "long_description" => '/^.{1,5000}$/us[REQ:NOT][LABEL:Long Description]',
            
                "address" => [
                    "type" => '/^(HOME|OFFICE|OTHER)$/[LABEL:Address Type]',
                    "title" => '/^.{1,255}$/[LABEL:Title]',
                    "formatted_address" => '/^.{1,500}$/[LABEL:Formatted Address]',
                    "address_1" => '/^.{1,255}$/[LABEL:Street Address]',
                    "address_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Additional Address]',
                    "floor" => [
                        "type" => '/^(GROUNDPLUS|ONEPLUS)$/[REQ:NOT][LABEL:Floor Type]',
                        "number" => '/^.{1,255}$/[REQ:NOT][LABEL:Floor Number]'
                    ],
                    "landmark" => '/^.{0,255}$/[REQ:NOT][LABEL:Landmark]',
                    "city" => '/^[a-zA-Z0-9_\s-]{1,255}$/[LABEL:City]',
                    "state" => [
                        "name" => '/^[a-zA-Z_\s-]{1,255}$/[LABEL:State Name]',
                        "code" => '/^[A-Z]{2}$/[LABEL:State Code]'
                    ],
                    "country" => [
                        "id" => '/^[0-9]{1,5}$/[LABEL:Country ID]',
                        "name" => '/^[a-zA-Z_\s-]{1,255}$/[LABEL:Country Name]',
                        "code" => '/^[A-Z]{2}$/[LABEL:Country Code]'
                    ],
                    "pin_zip" => '/^[A-Za-z0-9-]{1,10}$/[LABEL:Postal Code]',
                    "coordinates" => [
                        "latitude" => '/^-?\d+(\.\d+)?$/[LABEL:Latitude]',
                        "longitude" => '/^-?\d+(\.\d+)?$/[LABEL:Longitude]'
                    ],
                    "google_maps" => [
                        "place_id" => '/^.{1,255}$/[REQ:NOT][LABEL:Google Maps Place ID]',
                        "type" => '/^[a-zA-Z0-9_\s-]{0,255}$/[REQ:NOT][LABEL:Google Maps Type]',
                        "colloquial_area" => '/^.{0,255}$/[REQ:NOT][LABEL:Colloquial Area]',
                        "locality" => '/^.{0,255}$/[REQ:NOT][LABEL:Locality]',
                        "sublocality" => [
                            "sublocality_level_1" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 1]',
                            "sublocality_level_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 2]',
                            "sublocality_level_3" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 3]',
                            "sublocality_level_4" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 4]',
                            "sublocality_level_5" => '/^.{0,255}$/[REQ:NOT][LABEL:Sublocality Level 5]'
                        ],
                        "neighborhood" => '/^.{0,255}$/[REQ:NOT][LABEL:Neighborhood]',
                        "postal_code" => '/^.{0,20}$/[REQ:NOT][LABEL:Postal Code]',
                        "floor" => '/^.{0,10}$/[REQ:NOT][LABEL:Floor]',
                        "landmark" => '/^.{0,255}$/[REQ:NOT][LABEL:Landmark]',
                        "administrative_area" => [
                            "administrative_area_level_1" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 1]',
                            "administrative_area_level_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 2]',
                            "administrative_area_level_3" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 3]',
                            "administrative_area_level_4" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 4]',
                            "administrative_area_level_5" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 5]',
                            "administrative_area_level_6" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 6]',
                            "administrative_area_level_7" => '/^.{0,255}$/[REQ:NOT][LABEL:Administrative Area Level 7]'
                        ]
                    ]
                ],
            
                "facilities" => [
                    "car_parking" => [
                        "self_parking" => 'boolean[LABEL:Self Parking]',
                        "valet_service" => 'boolean[LABEL:Valet Service]'
                    ],
                    "bike_parking" => 'boolean[LABEL:Bike Parking]'
                ],

                "open_hours" => [
                    // open_hours is an array, each item having a "days" array.
                    // We'll define rules for each element of "days".
                    [
                        "day" => '/^(MON|TUE|WED|THU|FRI|SAT|SUN)$/[LABEL:Day]',
                        "is_available" => 'boolean[LABEL:Is Available]',
                        "time_slot" => [
                            [
                                "start" => '/^([0-9]{1,2}):([0-9]{1,2})$/[LABEL:Start Time]', // HH:MM 24-hour format
                                "end" => '/^([0-9]{1,2}):([0-9]{1,2})$/[LABEL:End Time]'
                            ]
                        ]
                    ]
                ]    
            ];
            
            $errors = $this->validate->validateData($payload, $validationChecks);
            
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            //GET ES record with payload

            $orgData = $this->orgModel->getOrganization($payload['org_id'], ['schema' => 'Organizatio_Minimal']);

            if (!$orgData) {
                return $this->response->error('GET_FAIL',['replace'=>'Org']);
            }

            if ($this->placeModel->updPlace($placeId, $payload)) {
                return $this->response->success('PUT_UPDATE', null, ['replace'=>'Place'] );
            } else {
                return $this->response->error('PUT_UPDATE_FAIL', ['replace'=>'Place'] );
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Delete place information
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error The response object or error object
     */
    public function delPlace($request){
        try {
            $placeId = (int)$request['placeId'];

            // Validate required fields
            $validation_checks = [
                'placeId' => 'numeric'
            ];

            $prmBody = [
                'placeId' => $placeId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Check if the place exists before deleting
            /*
            $placeData = $this->placeModel->getPlace($placeId, ['schema' => 'Place_Minimal']);
            if (!$placeData) {
                return $this->response->error('GET_FAIL', ['replace' => 'Place']);
            }
            */
            // Perform delete operation
            if ($this->placeModel->delPlace($placeId)) {
                return $this->response->success('DELETE_DATA', null, ['replace' => 'Place']);
            } else {
                return $this->response->error('DELETE_DATA_FAIL', ['replace' => 'Place']);
            }

        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('DELETE_DATA_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }
}
