.dark87, #app .disableAccount, #app .disableAccount .sectionTitle span {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .disableAccount .disableIcon, #app .disableAccount .desciprtion, #app .disableAccount .sectionTitle {
  color: rgba(0, 0, 0, 0.6);
}

#app button.button, #app a.button {
  height: auto;
}

#app .classSchedule {
  padding: 30px 15px;
}

@media (min-width: 768px) {
  #app .classSchedule {
    padding: 30px 15px 60px;
  }
}

#app .classSchedule .sectionTitle {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .classSchedule .formWrapper {
  background-color: #f7f9fb;
  padding: 15px;
  border: 1px solid #D9D9D9;
  border-radius: 6px;
}

@media (min-width: 768px) {
  #app .classSchedule .formWrapper {
    padding: 30px;
  }
}

#app .classSchedule .formWrapper button.button {
  padding: 10px 30px;
}

#app .classSchedule .formWrapper .field.cardRadio {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .classSchedule .formWrapper .field.cardRadio .noBatchFound {
  background: #edc264;
  font-size: 14px;
  text-align: center;
  border-radius: 4px;
  padding: 10px;
}

#app .classSchedule .formWrapper .field.cardRadio > .label {
  margin-bottom: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .classSchedule .formWrapper .field.cardRadio .helper {
  color: rgba(0, 0, 0, 0.5);
  margin-left: 5px;
}

#app .classSchedule .formWrapper .field.cardRadio .control {
  margin-right: 0;
}

#app .classSchedule .formWrapper .field.cardRadio .listWrapper {
  max-height: 247px;
  overflow-y: auto;
}

#app .classSchedule .formWrapper .field.cardRadio .radio {
  height: auto;
  width: 100%;
  text-align: left;
  display: block;
  padding: 0;
}

#app .classSchedule .formWrapper .field.cardRadio .radio.is-primary {
  background: #002F5A;
}

#app .classSchedule .formWrapper .field.cardRadio .radio.is-primary .batchList li .listLabel {
  color: rgba(255, 255, 255, 0.5);
}

#app .classSchedule .formWrapper .field.cardRadio .batchList li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 8px 15px;
  font-size: 14px;
}

#app .classSchedule .formWrapper .field.cardRadio .batchList li .listValue {
  margin-right: 10px;
}

#app .classSchedule .formWrapper .field .control textarea {
  height: 200px;
}

#app .classSchedule .ctaWrapper {
  padding-top: 30px;
}

#app .classSchedule .inlineFields {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .classSchedule .inlineFields > .field {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 10px;
}

#app .classSchedule .inlineFields > .field:last-child {
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  #app .classSchedule .inlineFields > .field {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
    margin-bottom: 0;
  }
  #app .classSchedule .inlineFields > .field:last-child {
    margin-bottom: 0;
  }
}

#app .classSchedule .taginput .taginput-container .autocomplete input {
  background: none;
  border: 0;
}

#app .classSchedule .inviteWrapper {
  margin-top: 30px;
}

@media (min-width: 768px) {
  #app .classSchedule .inviteWrapper {
    margin-top: 0;
  }
}

#app .classSchedule .inviteWrapper .autocomplete .dropdown-content {
  max-height: 345px;
}

#app .classSchedule .count {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .classSchedule .count span {
  font-weight: 400;
}

#app .classSchedule .selectedLearners {
  max-height: 250px;
  overflow-y: auto;
  margin-bottom: 30px;
}

#app .classSchedule .selectedLearners > li {
  border-color: rgba(0, 0, 0, 0.2);
  padding: 8px 15px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .classSchedule .selectedLearners > li .userImg {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 24px;
          flex: 0 0 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid;
  border-color: #FFF;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  -ms-flex-item-align: start;
      align-self: flex-start;
}

#app .classSchedule .selectedLearners > li .userImg img {
  width: 35px;
  height: 35px;
}

#app .classSchedule .selectedLearners > li .userName {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 34px);
          flex: 0 0 calc(100% - 34px);
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  padding-right: 18px;
  padding-left: 10px;
}

#app .classSchedule .selectedLearners > li .userName .fa {
  font-size: 18px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 3px;
}

#app .classSchedule .selectedLearners > li .caption {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#app .classSchedule .selectedLearners .groupLearners {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 1px solid;
  border-color: rgba(255, 255, 255, 0.8);
  margin: 10px 0;
}

#app .classSchedule .selectedLearners .groupLearners li {
  font-size: 12px;
  padding: 5px 10px;
  border-bottom: 1px solid;
  border-color: rgba(255, 255, 255, 0.8);
}

#app .classSchedule .selectedLearners .groupLearners li:last-child {
  margin-bottom: 0;
}

#app .classSchedule .selectedLearners .collapse:not(.show) {
  display: block;
}

#app .userNote {
  margin-top: 15px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}

#app .userNote .darkColor {
  color: #000;
}

#app .classFields > li {
  font-size: 16px;
  line-height: normal;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

#app .classFields > li .listSubtitle {
  margin-bottom: 10px;
  font-size: 14px;
}

#app .classFields > li .caption {
  font-weight: 500;
  color: #000;
  display: block;
  margin-bottom: 5px;
}

#app .classFields > li:last-child {
  margin-bottom: 0;
}

#app .classFields > li .selectedLearners {
  margin-top: 10px;
  max-height: 245px;
  overflow-y: auto;
}

#app .classFields > li .selectedLearners li {
  padding: 5px 10px;
  font-weight: 400;
}

#app .classFields > li .selectedLearners li .caption {
  font-weight: 400;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

#app .classFields > li .clipboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .classFields > li .clipboard .control {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 85%;
          flex: 0 0 85%;
}

#app .classFields > li .clipboard .trigger {
  margin-left: 15px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .courseList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .courseList .img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
}

#app .courseList .img .fa {
  font-size: 40px;
  color: rgba(0, 0, 0, 0.5);
}

#app .courseList .img img {
  width: 40px;
  height: 40px;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px;
  overflow: hidden;
}

#app .courseList .courseContent {
  padding-left: 15px;
}

#app .courseList .courseContent .instructorName {
  text-transform: capitalize;
}

#app .suggestUser li.loading .b-skeleton + .b-skeleton {
  margin-top: 0;
  margin-left: 15px;
}

#app .suggestUser li.has2Col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .suggestUser li .secondaryElement .checkbox {
  margin: 0;
  font-weight: 400;
}

#app .suggestUser li .secondaryElement .checkbox .check {
  display: none;
}

#app .suggestUser li .secondaryElement .checkbox .control-label {
  padding: 0;
  color: #A81E22;
  text-decoration: underline;
  font-size: 12px;
}

#app .suggestUser li:not(.title) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-color: #fff3cd;
  border: 1px solid;
  border-color: #ffeeba;
  border-radius: 4px;
  padding: 8px 0 10px 15px;
  margin-bottom: 15px;
  font-size: 14px;
}

#app .suggestUser li .addToPeople {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .suggestUser li .addToPeople:hover {
  text-decoration: none;
}

#app .suggestUser li .media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
}

#app .suggestUser li .name {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

#app .suggestUser li .userImg {
  border-radius: 50%;
  font-size: 0;
  background-color: #FFF;
  margin-right: 10px;
  width: 24px;
  height: 24px;
}

#app .suggestUser li.title {
  font-size: 16px;
  line-height: normal;
  font-weight: 600;
  margin-bottom: 15px;
}

#app .disableAccount {
  padding: 60px 0;
}

#app .disableAccount .wrapper {
  padding: 24px;
  text-align: center;
}

#app .disableAccount .disableIcon {
  width: 80px;
  height: 80px;
  background-color: rgba(168, 30, 34, 0.04);
  border-radius: 50%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 39px;
  margin-bottom: 15px;
}

#app .disableAccount .desciprtion {
  margin-bottom: 30px;
  font-size: 16px;
}

#app .disableAccount .sectionTitle {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 15px;
}

.notices .toast {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
}

.notices .toast.is-danger {
  background-color: red;
}
/*# sourceMappingURL=classScheduleV2.css.map */