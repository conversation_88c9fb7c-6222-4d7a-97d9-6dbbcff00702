Vue.component("yuno-content-table", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
    <section class="contentTable">
      <div class="container">
        <h2 class="headline3 text-center">{{ data.title }}</h2>
        <div class="tableWrapper p-top-largest-times-1">
          <table v-for="(item, index) in data.table" :key="index">
            <thead>
              <tr>
                <th colspan="2">
                  <h3 class="headline5">{{ item.title }}</h3>
                  <p class="onSurfaceVariant subtitle1 noBold pt-2">{{ item.description }}</p>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in item.items" :key="rowIndex">
                <td>
                  <div class="row wrapper">
                    <div class="feature col-12 col-md-4">
                      <h3 class="headline6 onSurface">{{ row.title }}</h3>
                    </div>
                    <div class="description col-12 col-md-8">
                      <p class="onSurfaceVariant subtitle2 noBold">{{ row.subtitle }}</p>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  `,
  data() {
    return {};
  },
});
