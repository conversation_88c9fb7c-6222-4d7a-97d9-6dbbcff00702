Vue.component("yuno-carousel-slides", {
  props: {
    data: {
      type: Object,
      required: true,
    },
    options: {
      type: Object,
      required: false,
    },
  },
  template: `
        <section class="carouselSlides">
			<div class="container">
				<b-carousel-list v-model="slide" :data="data.items" :items-to-show="1">
					<template #item="list">
  						<h2 class="headline3 onSurface p-top-larger-times-2 text-center">{{ list.title }}</h2>
						<p class="subtitle1 noBold onSurfaceVariant p-top-larger-times-1 text-center">{{ list.description }}</p>
						<div class="row hero-body d-flex">
							<div :class="options?.columns === 'one' ? 'col-md-12' : 'col-md-6'" class="contentWrapper pt-3 col-12 order-2">
								<h1 class="title headline5 onSurface pb-2">{{ list.name }}</h1>
								<p class="description subtitle1 noBold onSurfaceVariant pt-2 pb-5">{{ list.excerpt }}</p>
								<div class="cta" v-if="false">
									<b-button 
										tag="a"
										class="secondaryCTA"
									>
										{{ list.cta.label }}
									</b-button>
								</div>    
							</div>
							<div :class="options?.columns === 'one' ? 'col-md-10 offset-md-1' : 'col-md-6'"  class="videoWrapper col-12 order-1 ">
								<video width="100%" autoplay muted loop>
									<source :src="list.video_url" type="video/mp4">
									Your browser does not support the video tag.
								</video>
							</div>
						</div>
					</template>
				</b-carousel-list>
				<div class="carousel-dots text-center">
					<span
						v-for="(item, index) in data.items"
						:key="index"
						:class="{'dot': true, 'active': index === slide}"
						@click="slide = index">
					</span>
					</div>
				</div>
        </section>
    `,
  data() {
    return {
      slide: 0,
    };
  },
});
