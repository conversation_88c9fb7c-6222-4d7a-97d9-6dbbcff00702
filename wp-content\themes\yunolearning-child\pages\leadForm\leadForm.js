window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 10 numbers required",
        "is": "This field is required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-lead-form', {
    props: {
        type: {
            type: String,
            required: true,
        },
    },
    template: `
    <section class="leadForm">
        <yuno-page-loader v-if="isUserLoading"></yuno-page-loader>
        <yuno-org-theme v-if="isOrg" :options="{
                'orgID': orgID
            }" 
            @orgFetched="onOrgFetched"
        >
        </yuno-org-theme>
        <template v-if="isUserReady">
            <yuno-header :isOrg="isOrg"></yuno-header>
            <template v-if="stepsBegin">
                <yuno-schedule-demo
                    v-if="bookADemo"
                    :category="type"
                    @showThankyou="showThankyou"
                >
                </yuno-schedule-demo>
                <yuno-thank-you 
                    v-if="isThankyou" 
                    :options="{'category': type}"
                    :data="findObjectByKey(categories, 'slug', type)" 
                >
                </yuno-thank-you>
                
                <yuno-steps 
                    :options="{'category': type}" 
                    :data="findObjectByKey(categories, 'slug', type)" 
                    @manageThankyou="manageThankyou" 
                    @initSteps="initSteps" 
                    v-if="!isThankyou && !bookADemo"
                >
                </yuno-steps>
            </template>
            <template v-else>
                <yuno-login-step 
                    :isOrg="isOrg"
                    :options="{'category': type}" 
                    :data="findObjectByKey(categories, 'slug', type)"
                >
                </yuno-login-step>
            </template>
            <yuno-footer v-if="isOrg" :isOrg="isOrg"></yuno-footer>
        </template>
    </section>
    
    `,
    data() {
        return {
            orgID: "",
            stepsBegin: false,
            isThankyou: false,
            bookADemo: false,
            isOrg: false,
            categories: [
                {
                    slug: "ielts",
                    label: "IELTS",
                    title: "IELTS Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right IELTS online classes"
                },
                {
                    slug: "english-speaking",
                    label: "English Speaking",
                    title: "English Speaking Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right English Speaking online classes"
                },
                {
                    slug: "pte",
                    label: "PTE",
                    title: "PTE Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right PTE online classes"
                },
                {
                    slug: "duolingo",
                    label: "Duolingo",
                    title: "Duolingo Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right Duolingo online classes"
                },
                {
                    slug: "toefl",
                    label: "TOEFL",
                    title: "TOEFL Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right TOEFL online classes"
                },
                {
                    slug: "french",
                    label: "French",
                    title: "French Live Online Classes",
                    subTitle: "BOOK A DEMO CLASS",
                    questionTitle: "Let us help you find the right French online classes"
                },
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'form',
            'categoryList',
            'orgAdmin'
        ]),
        isUserLoading: {
            get() {
                const module =  this.userInfo.loading 

                return module
            }
        },
        isUserReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module = this.userInfo.success
                } else {
                    module = true 
                }

                return module
            }
        },
    },
    async created() {
        this.manageState();
    },
    mounted() {
        this.emitEvents();
    },
    methods: {
        onOrgFetched() {

        },
        findObjectByKey(array, key, value) {
            return array.find(item => item[key] === value);
        },
        initSteps() {
            this.fetchSteps(true);
        },
        showThankyou() {
            this.isThankyou = true;
            this.bookADemo = false;
        },
        manageThankyou() {
            const hasAcademyID = YUNOCommon.getQueryParameter("academy_id");

            if (hasAcademyID) {
                this.bookADemo = true;    
                this.isThankyou = false;
            } else {
                this.isThankyou = true;
                this.bookADemo = false;    
            }
        },
        emitEvents() {
            Event.$on('stepsBegins', () => {
                this.stepsBegin = true;
            });
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchUserInfo() {
            const options = { 
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: (options) => this.gotUserInfo(options)
            };

            this.$store.dispatch('fetchData', options); 
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                this.fetchUserInfo();
            } else {
                this.user.isLoggedin = false;
            }

            const stepsBegin = YUNOCommon.getQueryParameter("stepsBegin")

            if (this.user.isLoggedin && stepsBegin) {
                this.stepsBegin = true;
            }
        },
        gotOrgInfo(options) {
            // Destructure the response object
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {};
        },
        fetchOrgInfo(orgID) {
            // API call options
            const options = { 
                apiURL: YUNOCommon.config.org("info", orgID),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: (options) => this.gotOrgInfo(options)
            };

             // Dispatch the API call
            this.$store.dispatch('fetchData', options); 
        },
        manageUserQueryParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const userid = urlParams.get('user_id');
            const token = urlParams.get('yuno_token');

            if (userid && token) {
                isLoggedIn = userid;
                this.$store.state.config.yunoAPIToken = "Bearer " + token;
                this.user.userID = userid;
                this.user.isLoggedin = true;
            }
        },  
        manageState() {
            const orgID = YUNOCommon.getQueryParameter("org_id");

            if (orgID) {
                this.isOrg = true;
                this.orgID = orgID;
                // this.fetchOrgInfo(orgID);
            };

            this.manageUserQueryParams();
            this.loginStatus();
            this.fetchSteps(false);
            this.fetchCategories();
        },
        gotSteps(options) {
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                const steps = data.form_fields,
                    examType = YUNOCommon.findObjectByKey(steps, "name", "which_ielts_exam_do_you_want_to_take");

                if (this.$props.type === "ielts" && examType.selected === "academic") {
                    YUNOCommon.removeObjInArr(steps, "name", "What_do_you_wish_to_pursue"); 
                };

                this.form.data = data;
            }
        },
        fetchSteps(resetModule) {
            if (resetModule) {
                this.form.data = [];
                this.form.success = false;
            };

            const options = { 
                apiURL: YUNOCommon.config.leadForm("steps", isLoggedIn, this.$props.type),
                module: "gotData",
                store: "form",
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotSteps(options)
            };

            this.$store.dispatch('fetchData', options); 
        },
        fetchCategories() {
            const options = { 
                apiURL: YUNOCommon.config.categoryListAPI(),
                module: "gotData",
                store: "categoryList",
                callback: false
            };

            this.$store.dispatch('fetchData', options); 
        },
    }
});