window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 15 numbers required ",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-enrollments-v4', {
    template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
        >
            <template v-slot:main>
                <div class="container-fluid">
                    <div class="mainHeader">
                        <div class="block">
                            <h1 class="pageTitle">{{ pageHeader.title }} 
                                <a 
                                    class="refresh" 
                                    @click.prevent="refreshTable()"
                                    href="#">
                                    <b-tooltip label="Refresh"
                                        type="is-dark"
                                        position="is-right">
                                        <span class="material-icons">refresh</span>
                                    </b-tooltip>
                                </a>
                            </h1>
                            <div class="action" v-if="pageHeader.button.isActive">
                                <template v-if="pageHeader.button.type === 'custom'">
                                    <b-button 
                                        @click="pageCTA()"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                                <template v-else>
                                    <b-button tag="a"
                                        :href="pageHeader.button.url"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                            </div> 
                        </div>
                    </div>
                    <figure class="infiniteSpinner" v-if="manageTabsVisibility('loading')">
                        <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                    </figure>
                </div>
                <yuno-tabs-v2
                    :destroyOnHide="true"
                    @tabChange="tabChange"
                    v-if="manageTabsVisibility('success')"
                >
                    <template v-slot:all>
                        <yuno-all
                            @tabChanged="onTabChanged"
                            ref="yunoAll" 
                            :storage="storage"
                            :apiURL="apiURL"
                            :filterAPIURL="filterAPIURL"
                            @onPageChange="onPageChange"
                        >
                        </yuno-all>
                    </template>
                    <template v-slot:academy>
                        <yuno-all
                            @tabChanged="onTabChanged"
                            ref="yunoAll" 
                            :storage="storage"
                            :apiURL="apiURL"
                            :filterAPIURL="filterAPIURL"
                            @onPageChange="onPageChange"
                        >
                        </yuno-all>
                    </template>
                </yuno-tabs-v2>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            apiURL: null,
            filterAPIURL: null,
            isMiniSidebar: false,
            storage: {
                name: "enrollmentInsightsV4",
                version: 1
            },
            authorizedRoles: [
                "yuno-admin",
                "org-admin",
                "Instructor",
                "Counselor",
            ],
            pageHeader: {
                title: "Enrollments",
                button: {
                    label: "New Enrollment",
                    type: "link",
                    url: "/generate-link/",
                    isActive: true
                }
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'loader',
            'filterResult',
            'orgAdmin',
            'filters'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        manageTabsVisibility(state) {
            switch (state) {
                case "loading":
                    switch (this.userRole.data) {
                        case "org-admin":
                            return this.orgAdmin.loading;
                        case "yuno-admin":
                        case "Instructor":
                        case "Counselor":
                            return false;
                        default:
                            return false;
                    }
                case "success":
                    switch (this.userRole.data) {
                        case "org-admin":
                            return this.orgAdmin.success;
                        case "yuno-admin":
                        case "Instructor":
                        case "Counselor":
                            return true;
                        default:
                            return false;
                    }
                default:
                    return false;
            }
        },
        refreshTable() {
            this.filterResult.refreshTable = true;
        },
        gotOrgInfo(options) {
            const { response: { data: { code, data } = {} } = {} } = options;
            if (code === 200) {
                const academies = data.yuno_academy_subscription.academies;

                if (academies.length > 0) {
                    this.setupTabs("tabsAvailable");
                } else {
                    this.setupTabs("tabsNotAvailable");
                }
            }

            this.setupTableGrid(this.userRole.data);
            this.setupFilters();
        },
        fetchOrgInfo() {
            const options = { 
                apiURL: YUNOCommon.config.generic("org", false, false, this.activeOrg()),
                module: "gotData",
                store: "orgAdmin",
                callback: true,
                callbackFunc: this.gotOrgInfo
            };
        
            this.$store.dispatch('fetchData', options); 
        },
        fetchModules(role) {
            if (role === "org-admin") {
                this.fetchOrgInfo();    
            } else {
                this.setupTabs("tabsNotAvailable");
                this.setupTableGrid(this.userRole.data);
                this.setupFilters();
            }
        },
        onUserInfo(data) {
            this.fetchModules(data.role);
            // this.setupTabs("tabsAvailable");
            
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        },
        tabChange(e) {
            this.manageSessionStorage();
            this.resetModule();
            this.setupTableGrid(this.userRole.data, e);
            setTimeout(() => {
                const tabRefs = [this.$refs.yunoUpcomingOngoing, this.$refs.yunoPast];
                const tabRef = tabRefs[e];
                
                if (tabRef) {
                    tabRef.onTabChanged(e);
                }
            }, 50);
        },
        onTabChanged() {
            
        },
        resetModule() {
            this.filterResult.data = [];
            this.filterResult.error = null;
            this.filterResult.errorData = [];
            this.filterResult.success = false;
            this.filterResult.count = "";
            this.filterResult.currentCount = "";
            this.filterResult.offset = 0;
            this.filterResult.payload = [];
            this.filters.data = [];
            this.filters.error = null;
            this.filters.errorData = [];
            this.filters.success = false;
        },
        setupFilters() {
            const payload = JSON.parse(JSON.stringify(this.filterResult.payload));
            delete payload.limit;
            delete payload.offset;

            this.filterAPIURL = {
                configMethod: "enrollmentsV4",
                type: "filters",
                options: {
                    params: `?${this.objectToQueryString(payload)}`,
                }
            }
        },
        onPageChange() {
            this.setupTableGrid(this.userRole.data);
        },
        setupTableGrid(role, tabIndex) {
            // Base payload with common properties
            const basePayload = {
                "user_id": isLoggedIn,
                "learner_id": 0,
                "status": "all",
                "course_id": 0,
                "limit": this.filterResult.limit,
                "offset": this.filterResult.offset
            };

            // Role-specific payload configurations
            const rolePayloads = {
                "Counselor": {
                    ...basePayload,
                    "referrals": "all"
                },
                "org-admin": basePayload,
                "Instructor": basePayload,
                "yuno-admin": {
                    ...basePayload,
                    "referrals": "all",
                    "instructor_id": 0,
                    "org_id": 0,
                    "counselor_id": 0,
                    "payment_status": "all"
                }
            };

            const currentTab = this.filterResult.tabs.items[this.filterResult.tabs.activeTab];

            if (currentTab.slug === "academy" && role === "org-admin") {
                rolePayloads["org-admin"].academy_id = 0;
                rolePayloads["org-admin"].org_id = this.activeOrg();
            }

            if (currentTab.slug === "all" && role === "org-admin") {
                rolePayloads["org-admin"].org_id = this.activeOrg();
            }

            // Set the appropriate payload based on role
            this.filterResult.payload = rolePayloads[role] || basePayload;

            this.apiURL = {
                configMethod: "enrollmentsV4",
                type: "list",
                options: {
                    view: "grid",
                    params: this.filterResult.payload
                }
            };
        },
        objectToQueryString(obj) {
            return new URLSearchParams(obj).toString();
        },
        manageSessionStorage() {
            const storage = this.storage;
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store) {
                sessionStorage.removeItem(storage.name + "V" + storage.version);
            }
        },
        setupTabs(wrapperClass) {
            function createTab(label, slug, eleClass, isVisible) {
                return {
                    label,
                    slug,
                    isActive: false,
                    isVisible: isVisible,
                    class: eleClass
                };
            };

            const storage = this.storage;
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);
            let activeTab = "";

            if (store) {
                activeTab = JSON.parse(store).currentTab;
            } else {
                activeTab = 0;
            }

            this.filterResult.tabs = {
                activeTab: activeTab,
                wrapperClass: wrapperClass,
                items: [
                    createTab("All", "all", "yunoAll", true),
                    createTab("My Academy", "academy", "yunoAcademy", true),
                ],
            };
        },
    }
});