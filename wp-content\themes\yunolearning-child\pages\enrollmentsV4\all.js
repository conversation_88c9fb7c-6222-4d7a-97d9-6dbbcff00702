Vue.component('yuno-all', {
    props: {
        storage: {
            type: Object,
            required: true
        },
        apiURL: {
            type: Object,
            required: true,
        },
        filterAPIURL: {
            type: Object,
            required: true,
        }
    },
    template: `
        <div>   
            <b-loading :is-full-page="true" v-model="isLoading" :can-cancel="false"></b-loading>
            <yuno-table-grid
                :apiURL="apiURL"
                :filterAPIURL="filterAPIURL"
                apiMethodType="GET"
                :payload="filterResult.payload"
                :sessionStorage="storage"
                emptyStateImg="/assets/images/group.png"
                @onRefineFilters="onRefineFilters"
                @onQuerySearch="onQuerySearch"
                @onSearchInput="onSearchInput"
                @manageDrawer="manageDrawer"
                @gotFilters="gotFilters"
                @onPageChange="onPageChange"
                @gotGrid="onGotGrid"
                @initAction="initAction"
                recordFoundCaption="Enrollments found"
            >
            </yuno-table-grid>
            <b-modal 
                :active.sync="drawerModal.modal" 
                :width="drawerModal.width" 
                animation="slide-out"
                :can-cancel="['escape', 'x']"
                :on-cancel="closeDrawer"
                class="yunoModal drawerModal">
                    <template v-if="drawerModal.modal">
                        <div class="scrollable">
                            <h2 class="drawerTitle">
                                {{ drawerModal.data.learner.full_name }}
                            </h2>
                            <yuno-learner-attendance v-if="drawerModal.type === 'attendance'" :data="drawer" @getClassDetail="onGetClassDetail"></yuno-learner-attendance>
                        </div>
                    </template>
            </b-modal>
            <b-modal 
                :active.sync="changeBatchModal.modal" 
                :width="500" 
                :can-cancel="['escape', 'x']"
                :on-cancel="changeBatchClose"
                class="yunoModal lightTheme">
                    <template v-if="changeBatchModal.modal">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Change Batch</h2>
                        </div>
                        <validation-observer tag="div" ref="changeBatchObserver" v-slot="{ handleSubmit }">
                            <form id="changeBatchForm" @submit.prevent="handleSubmit(initChangeBatch)">
                                <div class="modalBody">
                                    <b-field label="New Batch">
                                        <validation-provider :rules="{required:true, isBatchSelected:filteredBatchObj.length}" v-slot="{ errors, classes }">
                                            <b-autocomplete
                                                :class="classes"
                                                v-model="changeBatchModal.selectedBatch"
                                                :data="filteredBatchObj"
                                                placeholder="Search by name"
                                                field="name"
                                                @select="onBatchSelect($event)"
                                                :clearable="true">
                                            </b-autocomplete>
                                            <p class="error">{{errors[0]}}</p>
                                        </validation-provider>
                                    </b-field>
                                    <b-field label="Current Batch">
                                        <b-input :value="changeBatchModal.data.batch.id" readonly></b-input>
                                    </b-field>
                                    <div class="ctaWrapper alignLeft">
                                        <b-button
                                            :loading="changeBatchModal.loading"
                                            :disabled="changeBatchModal.loading" 
                                            native-type="submit"
                                            class="yunoSecondaryCTA">
                                            Change Batch
                                        </b-button>
                                    </div>        
                                </div>
                            </form>
                        </validation-observer>
                    </template>
            </b-modal>
        </div>
    `,
    data() {
        return {
            isLoading: false,
            changeBatchModal: {
                modal: false,
                data: [],
                selectedBatch: "",
                loading: false,
                payload: {
                    new_batch_id: "",
                    action: "CHG_BATCH"
                }
            },
            drawerModal: {
                modal: false,
                data: [],
                width: 1000,
                type: ""
            },
        }
    },
    computed: {
        ...Vuex.mapState([
            'userInfo',
            'filters',
            'filterResult',
            'userRole',
            'drawer',
            'orgAdmin',
            'counsellorList',
            'allCourses',
            'allBatches',
            'learnerInsightsClass'
        ]),
        filteredBatchObj() {
            if (!this.allBatches.data) return [];
            return this.allBatches.data.filter(option => 
                option.name.toLowerCase().includes(this.changeBatchModal.selectedBatch.toLowerCase())
            );
        }
    },
    mounted() {
        this.fetchModules();
    },
    methods: {
        fetchModules() {
            this.fetchAllBatches();
        },
        fetchAllBatches() {
            this.allBatches.data = [];
            this.allBatches.success = false;
            this.allBatches.error = null;

            const options = {
                apiURL: YUNOCommon.config.allBatchesAPI(),
                module: "gotData",
                store: "allBatches",
                callback: false
            };

            this.$store.dispatch('fetchData', options);
        },
        enrollDone(options, action, row) {
            this.isLoading = false;
            const response = options.response?.data;
            const message = response?.message;
            const isSuccess = response?.code === 201;

            this.$buefy.toast.open({
                duration: 5000,
                message: message,
                position: 'is-bottom',
                type: isSuccess ? 'is-success' : 'is-danger'
            });
        },
        initEnroll(row, action) {
            this.isLoading = true;

            const options = {
                apiURL: YUNOCommon.config.enrollmentsV4("enrollToggle", { enrollmentID: row.id }),
                module: "gotData",
                store: "manageEnroll",
                payload: {
                    is_unenroll: !!row.is_unenroll
                },
                callback: true,
                callbackFunc: (options) => this.enrollDone(options, action, row)
            };

            this.$store.dispatch('putData', options);
        },
        initAction(row, action) {
            switch (action.slug) {
                case "changeBatch":
                    this.changeBatchModal.modal = true;
                    this.changeBatchModal.data = row;
                    break;

                case "enrollToggle":
                    const title = row.is_unenroll ? "Unenroll" : "Enroll";
                    this.$buefy.dialog.confirm({
                        title: `${title} User`,
                        message: `Are you sure you want to ${title.toLowerCase()} this user?`,
                        cancelText: 'Cancel',
                        confirmText: 'Yes',
                        type: 'is-danger',
                        onConfirm: () => this.initEnroll(row, action)
                    });    
                    break;
            }
        },
        changeBatchClose() {
            const modalObj = this.changeBatchModal;
            modalObj.data = [];
            modalObj.selectedBatch = "";
            modalObj.payload.new_batch_id = "";
            this.$refs.changeBatchObserver.reset();
            modalObj.modal = false;
        },
        onBatchSelect(e) {
            const payload = this.changeBatchModal.payload;
            payload.new_batch_id = e ? e.batch_id : "";
        },
        changeBatchDone(options) {
            const modalObj = this.changeBatchModal;
            modalObj.loading = false;
            
            const response = options.response?.data;
            const message = response?.message;
            const isSuccess = response?.code === 201;
            this.filterResult.refreshTable = true;

            this.$buefy.toast.open({
                duration: 5000,
                message: message,
                position: 'is-bottom',
                type: isSuccess ? 'is-success' : 'is-danger'
            });

            if (isSuccess) {
                this.changeBatchClose();
            }
        },
        initChangeBatch() {
            const modalObj = this.changeBatchModal;
            modalObj.loading = true;

            const options = {
                apiURL: YUNOCommon.config.enrollmentsV4("changeBatch", { enrollmentID: modalObj.data.id }),
                module: "gotData",
                store: "changeBatch",
                payload: modalObj.payload,
                callback: true,
                callbackFunc: (options) => this.changeBatchDone(options)
            };

            this.$store.dispatch('putData', options);
        },
        isFilterAvailable(data) {
            return YUNOCommon.findInArray(data, this.userRole.data);
        },
        onGotGrid(data) {
            const rows = data.rows;
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                
                row.actions = [
                    {
                        label: "Change Batch",
                        is_active: this.isFilterAvailable(["yuno-admin", "org-admin"]),
                        slug: "changeBatch",
                        active_class: "material-icons-outlined",
                        url: false,
                        link_target: "",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: "edit"
                        }
                    },
                    {
                        label: row.is_unenroll ? "Unenroll" : "Enroll",
                        is_active: this.isFilterAvailable(["yuno-admin", "Counselor", "org-admin"]),
                        slug: "enrollToggle",
                        active_class: "material-icons-outlined",
                        url: false,
                        link_target: "",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: row.is_unenroll ? "person_remove" : "person_add"
                        }
                    }
                ];
            }
        },
        onPageChange() {
            this.$emit("onPageChange");
        },
        onRefineFilters(filters) {
            // Implementation if needed
        },
        onQuerySearch(name, filter, isSelect) {
            if (isSelect) {
                const payload = this.filterResult.payload;
                payload[filter.filter] = filter.selected ?? 0;
            } else {
                switch (filter.filter) {
                    case "course_id":
                        this.fetchCourseSuggestions(name, filter);
                        break;
                    case "instructor_id":
                        this.fetchInstructorSuggestions(name, filter);
                        break;
                    case "learner_id":
                        this.fetchLearnerSuggestions(name, filter);
                        break;
                }
            }
        },
        onSearchInput(name, filter) {
            // Implementation if needed
        },
        gotClassDetails(options) {
            if (options.response?.data?.code === 200) {
                const data = options.response.data.data;
                if (data.is_absent) {
                    this.learnerInsightsClass.errorData = options.response.data.message;
                }
            }
        },
        fetchClassDetails(classID, learnerID) {
            this.learnerInsightsClass.success = false;
            this.learnerInsightsClass.error = null;
            this.learnerInsightsClass.data = [];

            const options = {
                apiURL: YUNOCommon.config.learnerInsightsClassAPI(classID, learnerID, true),
                module: "gotData",
                store: "learnerInsightsClass",
                callback: true,
                callbackFunc: (options) => this.gotClassDetails(options)
            };

            this.$store.dispatch('fetchData', options);
        },
        onGetClassDetail(day, attendanceDetail) {
            const allDays = attendanceDetail.days;
            const learnerID = attendanceDetail.learner.id;

            allDays.forEach(day => {
                day.hasClicked = false;
            });
            
            day.hasClicked = true;
            this.fetchClassDetails(day.class_ID, learnerID);
        },
        gotLearnerDetails(options) {
            if (options.response?.data?.code === 200) {
                // Implementation if needed
            }
        },
        fetchLearnerDetails(row) {
            this.drawer.success = false;
            this.drawer.data = [];
            this.drawer.error = null;

            this.learnerInsightsClass.success = false;
            this.learnerInsightsClass.data = [];
            this.learnerInsightsClass.error = null;

            const options = {
                apiURL: YUNOCommon.config.enrollmentClassDetailAPI(row.learner.id, row.batch.id),
                module: "gotData",
                store: "drawer",
                callback: true,
                callbackFunc: (options) => this.gotLearnerDetails(options)
            };

            this.$store.dispatch('fetchData', options);
        },
        manageDrawer(row, col) {
            this.drawerModal.data = row;
            this.drawerModal.modal = true;    
            this.drawerModal.type = "";

            if (col === "attendance") {
                this.drawerModal.type = "attendance";
                this.fetchLearnerDetails(row);
            }
        },
        closeDrawer() {
            this.drawerModal.modal = false;
            this.drawerModal.data = [];
            this.drawerModal.type = "";
        },
        activeOrg() {
            return this.userInfo.data.current_state.org_id || 0;
        },
        gotLearnerSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                filter.items = options.response.data.data;
            }
        },
        fetchLearnerSuggestions(name, filter) {
            const options = {
                apiURL: YUNOCommon.config.generic("userSearch", name, "learner", this.activeOrg()),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: (options) => this.gotLearnerSuggestions(options, filter)
            };
        
            this.$store.dispatch('fetchData', options);
        },
        gotInstructorSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                filter.items = options.response.data.data;
            }
        },
        fetchInstructorSuggestions(name, filter) {
            const options = {
                apiURL: YUNOCommon.config.generic("userSearch", name, "instructor", this.activeOrg()),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: (options) => this.gotInstructorSuggestions(options, filter)
            };
        
            this.$store.dispatch('fetchData', options);
        },
        gotCourseSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                const { course } = options.response.data.data;
                if (course) {
                    filter.items.push(...course);
                }
            }
        },
        fetchCourseSuggestions(name, filter) {
            const options = {
                apiURL: YUNOCommon.config.generic("courseSuggestions", name),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: (options) => this.gotCourseSuggestions(options, filter)
            };
        
            this.$store.dispatch('fetchData', options);
        },
        gotFilters(data) {
            const filters = data;
            const organization = YUNOCommon.findObjectByKey(filters, "filter", "org_id");
            const counselor = YUNOCommon.findObjectByKey(filters, "filter", "counselor_id");

            filters.forEach(filter => {
                filter.search_field = "";
                filter.current = "";
                filter.id_field = "";
        
                switch (filter.filter) {
                    case "course_id":
                        filter.search_field = "title";
                        filter.current = filter.selected ? filter.selected.title : "";
                        filter.id_field = "id";
                        break;
                    case "instructor_id":
                    case "learner_id":
                        filter.search_field = "name";
                        filter.current = filter.selected ? filter.selected.name : "";
                        filter.id_field = "user_id";
                        break;
                }
            });
            

            if (this.userRole.data.includes("yuno-admin")) {
                this.fetchOrgAdminList(organization, "all");
                this.fetchCounsellorList(counselor);    
            }

            
        },
        gotOrgAdminList(options, filter) {
            filter.loading = false;
            filter.success = true;
            const { response } = options;
            const { code, data } = response?.data || {};

            if (code === 200 || code === 201) {
                (data || []).forEach(org => {
                    if (org.organisation_name) {
                        filter.items.push({
                            filter: filter.filter,
                            label: org.organisation_name,
                            slug: org.id,
                        });    
                    }
                });
            }
        },
        fetchOrgAdminList(filter, search) {
            filter.loading = true;
            this.orgAdmin.data = [];
            this.orgAdmin.success = false;
            this.orgAdmin.error = null;

            const searchParam = `?search=${search}`;
            const options = {
                apiURL: YUNOCommon.config.orgList(searchParam),
                module: "gotData",
                store: "orgAdmin",
                addToModule: false,
                callback: true,
                callbackFunc: (options) => this.gotOrgAdminList(options, filter)
            };

            this.$store.dispatch('fetchData', options);
        },
        gotCounsellors(options, filter) {
            filter.loading = false;
            filter.success = true;
            const { response } = options;
            const { code, data } = response?.data || {};

            if (code === 200 || code === 201) {
                (data || []).forEach(user => {
                    filter.items.push({
                        filter: filter.filter,
                        label: user.name_email,
                        slug: user.id,
                    });    
                });
            }
        },
        fetchCounsellorList(filter) {
            filter.loading = true;
            this.counsellorList.data = [];
            this.counsellorList.success = false;
            this.counsellorList.error = null;

            const options = {
                apiURL: YUNOCommon.config.listOfCounsellorsAPI(),
                module: "gotData",
                store: "counsellorList",
                addToModule: false,
                callback: true,
                callbackFunc: (options) => this.gotCounsellors(options, filter)
            };

            this.$store.dispatch('fetchData', options);
        }
    }
});