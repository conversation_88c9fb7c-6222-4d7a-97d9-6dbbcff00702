Vue.component('yuno-multi-select-dropdown', {
    props: ["data", "options", "defaultFilters"],
    template: `
        <div class="yunoDropdown multiSelect" :class="[data.filter]">
            <b-dropdown 
                v-model="selectedOption" 
                aria-role="list"
                :mobile-modal="false"
                :multiple="true"
                @change="onChange($event, data)"
            >
                <template #trigger>
                    <div class="labelWrapper">
                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>
                        <span class="selectedItem hasGrid">
                            <template v-if="data.selected.length === 0">
                                {{ data.placeholder }}
                            </template>
                            <template v-else>
                                <template v-for="(selected, i) in data.selected">
                                    <div class="item" :key="i">
                                        <span>
                                            {{ selected.label }}
                                        </span>
                                        <a 
                                            href="#" 
                                            class="clearFilter" 
                                            v-if="manageClearFilterCTA(data)"
                                            @click="clearFilter($event, selected, data)"
                                        >
                                            <span class="material-icons">cancel</span>
                                        </a>
                                    </div>
                                </template>
                                
                            </template>
                        </span>
                        <span class="material-icons icon">expand_more</span>
                    </div>
                </template>
                <template v-for="(option, i) in data.items">
                    <b-dropdown-item 
                        :value="option" 
                        aria-role="listitem"
                        :class="option.is_checked ? 'is-active' : ''"
                        @click="onItemSelect(option, data)"
                    >
                        {{ option.label }}
                    </b-dropdown-item>
                </template>
            </b-dropdown>
        </div>
    `,
    data() {
        return {
            selectedOption: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onItemSelect(option, filter) {
            if (option.is_checked) {
                option.is_checked = false
            } else {
                option.is_checked = true
            }
        },
        manageClearFilterCTA(data) {
            if (data.selected.length === 1 && data.selected[0].slug === "all") {
                return false
            } else {
                return true
            }
        },
        clearFilter(e, selected, data) {
            e.preventDefault();

            const payload = this.$props.options.payload; 

            YUNOCommon.removeObjInArr(data.selected, "slug", selected.slug);
            YUNOCommon.removeValInArr(payload[data.filter], selected.slug);  

            if (payload[data.filter].length === 0) {
                payload[data.filter] = this.$props.defaultFilters[this.$props.data.filter]
            }

            this.$emit("onDropdownChange", e, data);
        },
        onChange(e, data) {
            const payload = this.$props.options.payload; 

            if (e[0].slug === "all") {
                payload[data.filter] = [e[0].slug]
            } else {
                YUNOCommon.removeValInArr(payload[data.filter], "all");
                YUNOCommon.removeValInArr(data.selected, "all");

                if (YUNOCommon.findInArray(payload[data.filter], e[0].slug)) {
                    YUNOCommon.removeValInArr(payload[data.filter], e[0].slug);    
                    YUNOCommon.removeObjInArr(data.selected, "slug", e[0].slug);
                } else {
                    payload[data.filter].push(e[0].slug);
                    data.selected.push(e[0]);
                }
            }
            this.$emit("onDropdownChange", e, data)
        }
    }
});