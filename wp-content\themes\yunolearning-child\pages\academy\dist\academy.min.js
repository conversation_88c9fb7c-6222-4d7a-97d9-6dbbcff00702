/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,o){for(var a=-1,r=null==e?0:e.length;++a<r;){var i=e[a];t(o,i,n(i),e)}return o}function n(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}function r(e,t){for(var n=-1,o=null==e?0:e.length,a=0,r=[];++n<o;){var i=e[n];t(i,n,e)&&(r[a++]=i)}return r}function i(e,t){return!(null==e||!e.length)&&g(e,t,0)>-1}function s(e,t,n){for(var o=-1,a=null==e?0:e.length;++o<a;)if(n(t,e[o]))return!0;return!1}function l(e,t){for(var n=-1,o=null==e?0:e.length,a=Array(o);++n<o;)a[n]=t(e[n],n,e);return a}function u(e,t){for(var n=-1,o=t.length,a=e.length;++n<o;)e[a+n]=t[n];return e}function c(e,t,n,o){var a=-1,r=null==e?0:e.length;for(o&&r&&(n=e[++a]);++a<r;)n=t(n,e[a],a,e);return n}function d(e,t,n,o){var a=null==e?0:e.length;for(o&&a&&(n=e[--a]);a--;)n=t(n,e[a],a,e);return n}function p(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}function m(e){return e.match(Ke)||[]}function f(e,t,n){var o;return n(e,(function(e,n,a){if(t(e,n,a))return o=n,!1})),o}function h(e,t,n,o){for(var a=e.length,r=n+(o?1:-1);o?r--:++r<a;)if(t(e[r],r,e))return r;return-1}function g(e,t,n){return t==t?function(e,t,n){for(var o=n-1,a=e.length;++o<a;)if(e[o]===t)return o;return-1}(e,t,n):h(e,y,n)}function v(e,t,n,o){for(var a=n-1,r=e.length;++a<r;)if(o(e[a],t))return a;return-1}function y(e){return e!=e}function b(e,t){var n=null==e?0:e.length;return n?A(e,t)/n:X}function w(e){return function(t){return null==t?F:t[e]}}function _(e){return function(t){return null==e?F:e[t]}}function k(e,t,n,o,a){return a(e,(function(e,a,r){n=o?(o=!1,e):t(n,e,a,r)})),n}function A(e,t){for(var n,o=-1,a=e.length;++o<a;){var r=t(e[o]);r!==F&&(n=n===F?r:n+r)}return n}function I(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function S(e){return e?e.slice(0,N(e)+1).replace(qe,""):e}function L(e){return function(t){return e(t)}}function C(e,t){return l(t,(function(t){return e[t]}))}function j(e,t){return e.has(t)}function P(e,t){for(var n=-1,o=e.length;++n<o&&g(t,e[n],0)>-1;);return n}function T(e,t){for(var n=e.length;n--&&g(t,e[n],0)>-1;);return n}function $(e){return"\\"+Jt[e]}function x(e){return Yt.test(e)}function R(e){return Vt.test(e)}function D(e){var t=-1,n=Array(e.size);return e.forEach((function(e,o){n[++t]=[o,e]})),n}function U(e,t){return function(n){return e(t(n))}}function O(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var i=e[n];i!==t&&i!==V||(e[n]=V,r[a++]=n)}return r}function M(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function H(e){return x(e)?function(e){for(var t=Ft.lastIndex=0;Ft.test(e);)++t;return t}(e):fn(e)}function E(e){return x(e)?function(e){return e.match(Ft)||[]}(e):function(e){return e.split("")}(e)}function N(e){for(var t=e.length;t--&&We.test(e.charAt(t)););return t}function B(e){return e.match(zt)||[]}var F,z="Expected a function",Y="__lodash_hash_undefined__",V="__lodash_placeholder__",q=16,W=32,G=64,Z=128,J=256,K=1/0,Q=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,oe=[["ary",Z],["bind",1],["bindKey",2],["curry",8],["curryRight",q],["flip",512],["partial",W],["partialRight",G],["rearg",J]],ae="[object Arguments]",re="[object Array]",ie="[object Boolean]",se="[object Date]",le="[object Error]",ue="[object Function]",ce="[object GeneratorFunction]",de="[object Map]",pe="[object Number]",me="[object Object]",fe="[object Promise]",he="[object RegExp]",ge="[object Set]",ve="[object String]",ye="[object Symbol]",be="[object WeakMap]",we="[object ArrayBuffer]",_e="[object DataView]",ke="[object Float32Array]",Ae="[object Float64Array]",Ie="[object Int8Array]",Se="[object Int16Array]",Le="[object Int32Array]",Ce="[object Uint8Array]",je="[object Uint8ClampedArray]",Pe="[object Uint16Array]",Te="[object Uint32Array]",$e=/\b__p \+= '';/g,xe=/\b(__p \+=) '' \+/g,Re=/(__e\(.*?\)|\b__t\)) \+\n'';/g,De=/&(?:amp|lt|gt|quot|#39);/g,Ue=/[&<>"']/g,Oe=RegExp(De.source),Me=RegExp(Ue.source),He=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,Ne=/<%=([\s\S]+?)%>/g,Be=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Fe=/^\w*$/,ze=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ye=/[\\^$.*+?()[\]{}|]/g,Ve=RegExp(Ye.source),qe=/^\s+/,We=/\s/,Ge=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ze=/\{\n\/\* \[wrapped with (.+)\] \*/,Je=/,? & /,Ke=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Qe=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,ot=/^0b[01]+$/i,at=/^\[object .+?Constructor\]$/,rt=/^0o[0-7]+$/i,it=/^(?:0|[1-9]\d*)$/,st=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ut=/['\n\r\u2028\u2029\\]/g,ct="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pt="\\u2700-\\u27bf",mt="a-z\\xdf-\\xf6\\xf8-\\xff",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",ht="\\ufe0e\\ufe0f",gt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",yt="["+ct+"]",bt="["+gt+"]",wt="["+dt+"]",_t="\\d+",kt="["+pt+"]",At="["+mt+"]",It="[^"+ct+gt+_t+pt+mt+ft+"]",St="\\ud83c[\\udffb-\\udfff]",Lt="[^"+ct+"]",Ct="(?:\\ud83c[\\udde6-\\uddff]){2}",jt="[\\ud800-\\udbff][\\udc00-\\udfff]",Pt="["+ft+"]",Tt="\\u200d",$t="(?:"+At+"|"+It+")",xt="(?:"+Pt+"|"+It+")",Rt="(?:['’](?:d|ll|m|re|s|t|ve))?",Dt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ut="(?:"+wt+"|"+St+")"+"?",Ot="["+ht+"]?",Mt=Ot+Ut+("(?:"+Tt+"(?:"+[Lt,Ct,jt].join("|")+")"+Ot+Ut+")*"),Ht="(?:"+[kt,Ct,jt].join("|")+")"+Mt,Et="(?:"+[Lt+wt+"?",wt,Ct,jt,yt].join("|")+")",Nt=RegExp(vt,"g"),Bt=RegExp(wt,"g"),Ft=RegExp(St+"(?="+St+")|"+Et+Mt,"g"),zt=RegExp([Pt+"?"+At+"+"+Rt+"(?="+[bt,Pt,"$"].join("|")+")",xt+"+"+Dt+"(?="+[bt,Pt+$t,"$"].join("|")+")",Pt+"?"+$t+"+"+Rt,Pt+"+"+Dt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_t,Ht].join("|"),"g"),Yt=RegExp("["+Tt+ct+dt+ht+"]"),Vt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,qt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Wt=-1,Gt={};Gt[ke]=Gt[Ae]=Gt[Ie]=Gt[Se]=Gt[Le]=Gt[Ce]=Gt[je]=Gt[Pe]=Gt[Te]=!0,Gt[ae]=Gt[re]=Gt[we]=Gt[ie]=Gt[_e]=Gt[se]=Gt[le]=Gt[ue]=Gt[de]=Gt[pe]=Gt[me]=Gt[he]=Gt[ge]=Gt[ve]=Gt[be]=!1;var Zt={};Zt[ae]=Zt[re]=Zt[we]=Zt[_e]=Zt[ie]=Zt[se]=Zt[ke]=Zt[Ae]=Zt[Ie]=Zt[Se]=Zt[Le]=Zt[de]=Zt[pe]=Zt[me]=Zt[he]=Zt[ge]=Zt[ve]=Zt[ye]=Zt[Ce]=Zt[je]=Zt[Pe]=Zt[Te]=!0,Zt[le]=Zt[ue]=Zt[be]=!1;var Jt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Kt=parseFloat,Qt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,on=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=on&&on.exports===nn,rn=an&&Xt.process,sn=function(){try{var e=on&&on.require&&on.require("util").types;return e||rn&&rn.binding&&rn.binding("util")}catch(e){}}(),ln=sn&&sn.isArrayBuffer,un=sn&&sn.isDate,cn=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,mn=sn&&sn.isTypedArray,fn=w("length"),hn=_({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),gn=_({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=_({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function _(We){function Ke(e){if(Ba(e)&&!Ts(e)&&!(e instanceof pt)){if(e instanceof dt)return e;if(Tr.call(e,"__wrapped__"))return ma(e)}return new dt(e)}function ct(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=F}function pt(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new ht;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new ft(e)).size}function yt(e,t){var n=Ts(e),o=!n&&Ps(e),a=!n&&!o&&xs(e),r=!n&&!o&&!a&&Ms(e),i=n||o||a||r,s=i?I(e.length,Ar):[],l=s.length;for(var u in e)!t&&!Tr.call(e,u)||i&&("length"==u||a&&("offset"==u||"parent"==u)||r&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Ko(u,l))||s.push(u);return s}function bt(e){var t=e.length;return t?e[Rn(0,t-1)]:F}function wt(e,t){return ua(co(e),Pt(t,0,e.length))}function _t(e){return ua(co(e))}function kt(e,t,n){(n===F||Ra(e[t],n))&&(n!==F||t in e)||Ct(e,t,n)}function At(e,t,n){var o=e[t];Tr.call(e,t)&&Ra(o,n)&&(n!==F||t in e)||Ct(e,t,n)}function It(e,t){for(var n=e.length;n--;)if(Ra(e[n][0],t))return n;return-1}function St(e,t,n,o){return ji(e,(function(e,a,r){t(o,e,n(e),r)})),o}function Lt(e,t){return e&&po(t,tr(t),e)}function Ct(e,t,n){"__proto__"==t&&Gr?Gr(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function jt(e,t){for(var n=-1,o=t.length,a=gr(o),r=null==e;++n<o;)a[n]=r?F:Xa(e,t[n]);return a}function Pt(e,t,n){return e==e&&(n!==F&&(e=e<=n?e:n),t!==F&&(e=e>=t?e:t)),e}function Tt(e,t,o,a,r,i){var s,l=1&t,u=2&t,c=4&t;if(o&&(s=r?o(e,a,r,i):o(e)),s!==F)return s;if(!Na(e))return e;var d=Ts(e);if(d){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Tr.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return co(e,s)}else{var p=Ni(e),m=p==ue||p==ce;if(xs(e))return ao(e,l);if(p==me||p==ae||m&&!r){if(s=u||m?{}:Zo(e),!l)return u?function(e,t){return po(e,Ei(e),t)}(e,function(e,t){return e&&po(t,nr(t),e)}(s,e)):function(e,t){return po(e,Hi(e),t)}(e,Lt(s,e))}else{if(!Zt[p])return r?e:{};s=function(e,t,n){var o=e.constructor;switch(t){case we:return ro(e);case ie:case se:return new o(+e);case _e:return function(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case ke:case Ae:case Ie:case Se:case Le:case Ce:case je:case Pe:case Te:return io(e,n);case de:return new o;case pe:case ve:return new o(e);case he:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case ge:return new o;case ye:return function(e){return Si?_r(Si.call(e)):{}}(e)}}(e,p,l)}}i||(i=new vt);var f=i.get(e);if(f)return f;i.set(e,s),Os(e)?e.forEach((function(n){s.add(Tt(n,t,o,n,e,i))})):Ds(e)&&e.forEach((function(n,a){s.set(a,Tt(n,t,o,a,e,i))}));var h=d?F:(c?u?Bo:No:u?nr:tr)(e);return n(h||e,(function(n,a){h&&(n=e[a=n]),At(s,a,Tt(n,t,o,a,e,i))})),s}function $t(e,t,n){var o=n.length;if(null==e)return!o;for(e=_r(e);o--;){var a=n[o],r=t[a],i=e[a];if(i===F&&!(a in e)||!r(i))return!1}return!0}function xt(e,t,n){if("function"!=typeof e)throw new Ir(z);return zi((function(){e.apply(F,n)}),t)}function Rt(e,t,n,o){var a=-1,r=i,u=!0,c=e.length,d=[],p=t.length;if(!c)return d;n&&(t=l(t,L(n))),o?(r=s,u=!1):t.length>=200&&(r=j,u=!1,t=new gt(t));e:for(;++a<c;){var m=e[a],f=null==n?m:n(m);if(m=o||0!==m?m:0,u&&f==f){for(var h=p;h--;)if(t[h]===f)continue e;d.push(m)}else r(t,f,o)||d.push(m)}return d}function Dt(e,t){var n=!0;return ji(e,(function(e,o,a){return n=!!t(e,o,a)})),n}function Ut(e,t,n){for(var o=-1,a=e.length;++o<a;){var r=e[o],i=t(r);if(null!=i&&(s===F?i==i&&!Va(i):n(i,s)))var s=i,l=r}return l}function Ot(e,t){var n=[];return ji(e,(function(e,o,a){t(e,o,a)&&n.push(e)})),n}function Mt(e,t,n,o,a){var r=-1,i=e.length;for(n||(n=Jo),a||(a=[]);++r<i;){var s=e[r];t>0&&n(s)?t>1?Mt(s,t-1,n,o,a):u(a,s):o||(a[a.length]=s)}return a}function Ht(e,t){return e&&Ti(e,t,tr)}function Et(e,t){return e&&$i(e,t,tr)}function Ft(e,t){return r(t,(function(t){return Ma(e[t])}))}function zt(e,t){for(var n=0,o=(t=no(t,e)).length;null!=e&&n<o;)e=e[ca(t[n++])];return n&&n==o?e:F}function Yt(e,t,n){var o=t(e);return Ts(e)?o:u(o,n(e))}function Vt(e){return null==e?e===F?"[object Undefined]":"[object Null]":Wr&&Wr in _r(e)?function(e){var t=Tr.call(e,Wr),n=e[Wr];try{e[Wr]=F;var o=!0}catch(e){}var a=Rr.call(e);return o&&(t?e[Wr]=n:delete e[Wr]),a}(e):function(e){return Rr.call(e)}(e)}function Jt(e,t){return e>t}function Xt(e,t){return null!=e&&Tr.call(e,t)}function en(e,t){return null!=e&&t in _r(e)}function nn(e,t,n){for(var o=n?s:i,a=e[0].length,r=e.length,u=r,c=gr(r),d=1/0,p=[];u--;){var m=e[u];u&&t&&(m=l(m,L(t))),d=ii(m.length,d),c[u]=!n&&(t||a>=120&&m.length>=120)?new gt(u&&m):F}m=e[0];var f=-1,h=c[0];e:for(;++f<a&&p.length<d;){var g=m[f],v=t?t(g):g;if(g=n||0!==g?g:0,!(h?j(h,v):o(p,v,n))){for(u=r;--u;){var y=c[u];if(!(y?j(y,v):o(e[u],v,n)))continue e}h&&h.push(v),p.push(g)}}return p}function on(t,n,o){var a=null==(t=ra(t,n=no(n,t)))?t:t[ca(ya(n))];return null==a?F:e(a,t,o)}function rn(e){return Ba(e)&&Vt(e)==ae}function sn(e,t,n,o,a){return e===t||(null==e||null==t||!Ba(e)&&!Ba(t)?e!=e&&t!=t:function(e,t,n,o,a,r){var i=Ts(e),s=Ts(t),l=i?re:Ni(e),u=s?re:Ni(t);l=l==ae?me:l,u=u==ae?me:u;var c=l==me,d=u==me,p=l==u;if(p&&xs(e)){if(!xs(t))return!1;i=!0,c=!1}if(p&&!c)return r||(r=new vt),i||Ms(e)?Ho(e,t,n,o,a,r):function(e,t,n,o,a,r,i){switch(n){case _e:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case we:return!(e.byteLength!=t.byteLength||!r(new Er(e),new Er(t)));case ie:case se:case pe:return Ra(+e,+t);case le:return e.name==t.name&&e.message==t.message;case he:case ve:return e==t+"";case de:var s=D;case ge:var l=1&o;if(s||(s=M),e.size!=t.size&&!l)return!1;var u=i.get(e);if(u)return u==t;o|=2,i.set(e,t);var c=Ho(s(e),s(t),o,a,r,i);return i.delete(e),c;case ye:if(Si)return Si.call(e)==Si.call(t)}return!1}(e,t,l,n,o,a,r);if(!(1&n)){var m=c&&Tr.call(e,"__wrapped__"),f=d&&Tr.call(t,"__wrapped__");if(m||f){var h=m?e.value():e,g=f?t.value():t;return r||(r=new vt),a(h,g,n,o,r)}}return!!p&&(r||(r=new vt),function(e,t,n,o,a,r){var i=1&n,s=No(e),l=s.length;if(l!=No(t).length&&!i)return!1;for(var u=l;u--;){var c=s[u];if(!(i?c in t:Tr.call(t,c)))return!1}var d=r.get(e),p=r.get(t);if(d&&p)return d==t&&p==e;var m=!0;r.set(e,t),r.set(t,e);for(var f=i;++u<l;){var h=e[c=s[u]],g=t[c];if(o)var v=i?o(g,h,c,t,e,r):o(h,g,c,e,t,r);if(!(v===F?h===g||a(h,g,n,o,r):v)){m=!1;break}f||(f="constructor"==c)}if(m&&!f){var y=e.constructor,b=t.constructor;y!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return r.delete(e),r.delete(t),m}(e,t,n,o,a,r))}(e,t,n,o,sn,a))}function fn(e,t,n,o){var a=n.length,r=a,i=!o;if(null==e)return!r;for(e=_r(e);a--;){var s=n[a];if(i&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++a<r;){var l=(s=n[a])[0],u=e[l],c=s[1];if(i&&s[2]){if(u===F&&!(l in e))return!1}else{var d=new vt;if(o)var p=o(u,c,l,e,t,d);if(!(p===F?sn(c,u,3,o,d):p))return!1}}return!0}function bn(e){return!(!Na(e)||function(e){return!!xr&&xr in e}(e))&&(Ma(e)?Or:at).test(da(e))}function wn(e){return"function"==typeof e?e:null==e?ur:"object"==typeof e?Ts(e)?Ln(e[0],e[1]):Sn(e):mr(e)}function _n(e){if(!ta(e))return ai(e);var t=[];for(var n in _r(e))Tr.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kn(e){if(!Na(e))return function(e){var t=[];if(null!=e)for(var n in _r(e))t.push(n);return t}(e);var t=ta(e),n=[];for(var o in e)("constructor"!=o||!t&&Tr.call(e,o))&&n.push(o);return n}function An(e,t){return e<t}function In(e,t){var n=-1,o=Da(e)?gr(e.length):[];return ji(e,(function(e,a,r){o[++n]=t(e,a,r)})),o}function Sn(e){var t=qo(e);return 1==t.length&&t[0][2]?oa(t[0][0],t[0][1]):function(n){return n===e||fn(n,e,t)}}function Ln(e,t){return Xo(e)&&na(t)?oa(ca(e),t):function(n){var o=Xa(n,e);return o===F&&o===t?er(n,e):sn(t,o,3)}}function Cn(e,t,n,o,a){e!==t&&Ti(t,(function(r,i){if(a||(a=new vt),Na(r))!function(e,t,n,o,a,r,i){var s=ia(e,n),l=ia(t,n),u=i.get(l);if(u)return kt(e,n,u),F;var c=r?r(s,l,n+"",e,t,i):F,d=c===F;if(d){var p=Ts(l),m=!p&&xs(l),f=!p&&!m&&Ms(l);c=l,p||m||f?Ts(s)?c=s:Ua(s)?c=co(s):m?(d=!1,c=ao(l,!0)):f?(d=!1,c=io(l,!0)):c=[]:za(l)||Ps(l)?(c=s,Ps(s)?c=Ka(s):Na(s)&&!Ma(s)||(c=Zo(l))):d=!1}d&&(i.set(l,c),a(c,l,o,r,i),i.delete(l)),kt(e,n,c)}(e,t,i,n,Cn,o,a);else{var s=o?o(ia(e,i),r,i+"",e,t,a):F;s===F&&(s=r),kt(e,i,s)}}),nr)}function jn(e,t){var n=e.length;if(n)return Ko(t+=t<0?n:0,n)?e[t]:F}function Pn(e,t,n){t=t.length?l(t,(function(e){return Ts(e)?function(t){return zt(t,1===e.length?e[0]:e)}:e})):[ur];var o=-1;return t=l(t,L(Yo())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(In(e,(function(e,n,a){return{criteria:l(t,(function(t){return t(e)})),index:++o,value:e}})),(function(e,t){return function(e,t,n){for(var o=-1,a=e.criteria,r=t.criteria,i=a.length,s=n.length;++o<i;){var l=so(a[o],r[o]);if(l)return o>=s?l:l*("desc"==n[o]?-1:1)}return e.index-t.index}(e,t,n)}))}function Tn(e,t,n){for(var o=-1,a=t.length,r={};++o<a;){var i=t[o],s=zt(e,i);n(s,i)&&Hn(r,no(i,e),s)}return r}function $n(e,t,n,o){var a=o?v:g,r=-1,i=t.length,s=e;for(e===t&&(t=co(t)),n&&(s=l(e,L(n)));++r<i;)for(var u=0,c=t[r],d=n?n(c):c;(u=a(s,d,u,o))>-1;)s!==e&&Yr.call(s,u,1),Yr.call(e,u,1);return e}function xn(e,t){for(var n=e?t.length:0,o=n-1;n--;){var a=t[n];if(n==o||a!==r){var r=a;Ko(a)?Yr.call(e,a,1):Gn(e,a)}}return e}function Rn(e,t){return e+Xr(ui()*(t-e+1))}function Dn(e,t){var n="";if(!e||t<1||t>Q)return n;do{t%2&&(n+=e),(t=Xr(t/2))&&(e+=e)}while(t);return n}function Un(e,t){return Yi(aa(e,t,ur),e+"")}function On(e){return bt(ar(e))}function Mn(e,t){var n=ar(e);return ua(n,Pt(t,0,n.length))}function Hn(e,t,n,o){if(!Na(e))return e;for(var a=-1,r=(t=no(t,e)).length,i=r-1,s=e;null!=s&&++a<r;){var l=ca(t[a]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(a!=i){var c=s[l];(u=o?o(c,l,s):F)===F&&(u=Na(c)?c:Ko(t[a+1])?[]:{})}At(s,l,u),s=s[l]}return e}function En(e){return ua(ar(e))}function Nn(e,t,n){var o=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(n=n>a?a:n)<0&&(n+=a),a=t>n?0:n-t>>>0,t>>>=0;for(var r=gr(a);++o<a;)r[o]=e[o+t];return r}function Bn(e,t){var n;return ji(e,(function(e,o,a){return!(n=t(e,o,a))})),!!n}function Fn(e,t,n){var o=0,a=null==e?o:e.length;if("number"==typeof t&&t==t&&a<=ne){for(;o<a;){var r=o+a>>>1,i=e[r];null!==i&&!Va(i)&&(n?i<=t:i<t)?o=r+1:a=r}return a}return zn(e,t,ur,n)}function zn(e,t,n,o){var a=0,r=null==e?0:e.length;if(0===r)return 0;for(var i=(t=n(t))!=t,s=null===t,l=Va(t),u=t===F;a<r;){var c=Xr((a+r)/2),d=n(e[c]),p=d!==F,m=null===d,f=d==d,h=Va(d);if(i)var g=o||f;else g=u?f&&(o||p):s?f&&p&&(o||!m):l?f&&p&&!m&&(o||!h):!m&&!h&&(o?d<=t:d<t);g?a=c+1:r=c}return ii(r,te)}function Yn(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var i=e[n],s=t?t(i):i;if(!n||!Ra(s,l)){var l=s;r[a++]=0===i?0:i}}return r}function Vn(e){return"number"==typeof e?e:Va(e)?X:+e}function qn(e){if("string"==typeof e)return e;if(Ts(e))return l(e,qn)+"";if(Va(e))return Li?Li.call(e):"";var t=e+"";return"0"==t&&1/e==-K?"-0":t}function Wn(e,t,n){var o=-1,a=i,r=e.length,l=!0,u=[],c=u;if(n)l=!1,a=s;else if(r>=200){var d=t?null:Oi(e);if(d)return M(d);l=!1,a=j,c=new gt}else c=t?[]:u;e:for(;++o<r;){var p=e[o],m=t?t(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=c.length;f--;)if(c[f]===m)continue e;t&&c.push(m),u.push(p)}else a(c,m,n)||(c!==u&&c.push(m),u.push(p))}return u}function Gn(e,t){return null==(e=ra(e,t=no(t,e)))||delete e[ca(ya(t))]}function Zn(e,t,n,o){return Hn(e,t,n(zt(e,t)),o)}function Jn(e,t,n,o){for(var a=e.length,r=o?a:-1;(o?r--:++r<a)&&t(e[r],r,e););return n?Nn(e,o?0:r,o?r+1:a):Nn(e,o?r+1:0,o?a:r)}function Kn(e,t){var n=e;return n instanceof pt&&(n=n.value()),c(t,(function(e,t){return t.func.apply(t.thisArg,u([e],t.args))}),n)}function Qn(e,t,n){var o=e.length;if(o<2)return o?Wn(e[0]):[];for(var a=-1,r=gr(o);++a<o;)for(var i=e[a],s=-1;++s<o;)s!=a&&(r[a]=Rt(r[a]||i,e[s],t,n));return Wn(Mt(r,1),t,n)}function Xn(e,t,n){for(var o=-1,a=e.length,r=t.length,i={};++o<a;)n(i,e[o],o<r?t[o]:F);return i}function eo(e){return Ua(e)?e:[]}function to(e){return"function"==typeof e?e:ur}function no(e,t){return Ts(e)?e:Xo(e,t)?[e]:Vi(Qa(e))}function oo(e,t,n){var o=e.length;return n=n===F?o:n,!t&&n>=o?e:Nn(e,t,n)}function ao(e,t){if(t)return e.slice();var n=e.length,o=Nr?Nr(n):new e.constructor(n);return e.copy(o),o}function ro(e){var t=new e.constructor(e.byteLength);return new Er(t).set(new Er(e)),t}function io(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.length)}function so(e,t){if(e!==t){var n=e!==F,o=null===e,a=e==e,r=Va(e),i=t!==F,s=null===t,l=t==t,u=Va(t);if(!s&&!u&&!r&&e>t||r&&i&&l&&!s&&!u||o&&i&&l||!n&&l||!a)return 1;if(!o&&!r&&!u&&e<t||u&&n&&a&&!o&&!r||s&&n&&a||!i&&a||!l)return-1}return 0}function lo(e,t,n,o){for(var a=-1,r=e.length,i=n.length,s=-1,l=t.length,u=ri(r-i,0),c=gr(l+u),d=!o;++s<l;)c[s]=t[s];for(;++a<i;)(d||a<r)&&(c[n[a]]=e[a]);for(;u--;)c[s++]=e[a++];return c}function uo(e,t,n,o){for(var a=-1,r=e.length,i=-1,s=n.length,l=-1,u=t.length,c=ri(r-s,0),d=gr(c+u),p=!o;++a<c;)d[a]=e[a];for(var m=a;++l<u;)d[m+l]=t[l];for(;++i<s;)(p||a<r)&&(d[m+n[i]]=e[a++]);return d}function co(e,t){var n=-1,o=e.length;for(t||(t=gr(o));++n<o;)t[n]=e[n];return t}function po(e,t,n,o){var a=!n;n||(n={});for(var r=-1,i=t.length;++r<i;){var s=t[r],l=o?o(n[s],e[s],s,n,e):F;l===F&&(l=e[s]),a?Ct(n,s,l):At(n,s,l)}return n}function mo(e,n){return function(o,a){var r=Ts(o)?t:St,i=n?n():{};return r(o,e,Yo(a,2),i)}}function fo(e){return Un((function(t,n){var o=-1,a=n.length,r=a>1?n[a-1]:F,i=a>2?n[2]:F;for(r=e.length>3&&"function"==typeof r?(a--,r):F,i&&Qo(n[0],n[1],i)&&(r=a<3?F:r,a=1),t=_r(t);++o<a;){var s=n[o];s&&e(t,s,o,r)}return t}))}function ho(e,t){return function(n,o){if(null==n)return n;if(!Da(n))return e(n,o);for(var a=n.length,r=t?a:-1,i=_r(n);(t?r--:++r<a)&&!1!==o(i[r],r,i););return n}}function go(e){return function(t,n,o){for(var a=-1,r=_r(t),i=o(t),s=i.length;s--;){var l=i[e?s:++a];if(!1===n(r[l],l,r))break}return t}}function vo(e){return function(t){var n=x(t=Qa(t))?E(t):F,o=n?n[0]:t.charAt(0),a=n?oo(n,1).join(""):t.slice(1);return o[e]()+a}}function yo(e){return function(t){return c(sr(ir(t).replace(Nt,"")),e,"")}}function bo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Ci(e.prototype),o=e.apply(n,t);return Na(o)?o:n}}function wo(t,n,o){var a=bo(t);return function r(){for(var i=arguments.length,s=gr(i),l=i,u=zo(r);l--;)s[l]=arguments[l];var c=i<3&&s[0]!==u&&s[i-1]!==u?[]:O(s,u);return(i-=c.length)<o?$o(t,n,Ao,r.placeholder,F,s,c,F,F,o-i):e(this&&this!==tn&&this instanceof r?a:t,this,s)}}function _o(e){return function(t,n,o){var a=_r(t);if(!Da(t)){var r=Yo(n,3);t=tr(t),n=function(e){return r(a[e],e,a)}}var i=e(t,n,o);return i>-1?a[r?t[i]:i]:F}}function ko(e){return Eo((function(t){var n=t.length,o=n,a=dt.prototype.thru;for(e&&t.reverse();o--;){var r=t[o];if("function"!=typeof r)throw new Ir(z);if(a&&!i&&"wrapper"==Fo(r))var i=new dt([],!0)}for(o=i?o:n;++o<n;){var s=Fo(r=t[o]),l="wrapper"==s?Mi(r):F;i=l&&ea(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?i[Fo(l[0])].apply(i,l[3]):1==r.length&&ea(r)?i[s]():i.thru(r)}return function(){var e=arguments,o=e[0];if(i&&1==e.length&&Ts(o))return i.plant(o).value();for(var a=0,r=n?t[a].apply(this,e):o;++a<n;)r=t[a].call(this,r);return r}}))}function Ao(e,t,n,o,a,r,i,s,l,u){var c=t&Z,d=1&t,p=2&t,m=24&t,f=512&t,h=p?F:bo(e);return function g(){for(var v=arguments.length,y=gr(v),b=v;b--;)y[b]=arguments[b];if(m)var w=zo(g),_=function(e,t){for(var n=e.length,o=0;n--;)e[n]===t&&++o;return o}(y,w);if(o&&(y=lo(y,o,a,m)),r&&(y=uo(y,r,i,m)),v-=_,m&&v<u)return $o(e,t,Ao,g.placeholder,n,y,O(y,w),s,l,u-v);var k=d?n:this,A=p?k[e]:e;return v=y.length,s?y=function(e,t){for(var n=e.length,o=ii(t.length,n),a=co(e);o--;){var r=t[o];e[o]=Ko(r,n)?a[r]:F}return e}(y,s):f&&v>1&&y.reverse(),c&&l<v&&(y.length=l),this&&this!==tn&&this instanceof g&&(A=h||bo(A)),A.apply(k,y)}}function Io(e,t){return function(n,o){return function(e,t,n,o){return Ht(e,(function(e,a,r){t(o,n(e),a,r)})),o}(n,e,t(o),{})}}function So(e,t){return function(n,o){var a;if(n===F&&o===F)return t;if(n!==F&&(a=n),o!==F){if(a===F)return o;"string"==typeof n||"string"==typeof o?(n=qn(n),o=qn(o)):(n=Vn(n),o=Vn(o)),a=e(n,o)}return a}}function Lo(t){return Eo((function(n){return n=l(n,L(Yo())),Un((function(o){var a=this;return t(n,(function(t){return e(t,a,o)}))}))}))}function Co(e,t){var n=(t=t===F?" ":qn(t)).length;if(n<2)return n?Dn(t,e):t;var o=Dn(t,Qr(e/H(t)));return x(t)?oo(E(o),0,e).join(""):o.slice(0,e)}function jo(t,n,o,a){var r=1&n,i=bo(t);return function n(){for(var s=-1,l=arguments.length,u=-1,c=a.length,d=gr(c+l),p=this&&this!==tn&&this instanceof n?i:t;++u<c;)d[u]=a[u];for(;l--;)d[u++]=arguments[++s];return e(p,r?o:this,d)}}function Po(e){return function(t,n,o){return o&&"number"!=typeof o&&Qo(t,n,o)&&(n=o=F),t=Wa(t),n===F?(n=t,t=0):n=Wa(n),function(e,t,n,o){for(var a=-1,r=ri(Qr((t-e)/(n||1)),0),i=gr(r);r--;)i[o?r:++a]=e,e+=n;return i}(t,n,o=o===F?t<n?1:-1:Wa(o),e)}}function To(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Ja(t),n=Ja(n)),e(t,n)}}function $o(e,t,n,o,a,r,i,s,l,u){var c=8&t;t|=c?W:G,4&(t&=~(c?G:W))||(t&=-4);var d=[e,t,a,c?r:F,c?i:F,c?F:r,c?F:i,s,l,u],p=n.apply(F,d);return ea(e)&&Fi(p,d),p.placeholder=o,sa(p,e,t)}function xo(e){var t=wr[e];return function(e,n){if(e=Ja(e),(n=null==n?0:ii(Ga(n),292))&&ni(e)){var o=(Qa(e)+"e").split("e");return+((o=(Qa(t(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return t(e)}}function Ro(e){return function(t){var n=Ni(t);return n==de?D(t):n==ge?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return l(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Do(e,t,n,o,a,r,i,s){var l=2&t;if(!l&&"function"!=typeof e)throw new Ir(z);var u=o?o.length:0;if(u||(t&=-97,o=a=F),i=i===F?i:ri(Ga(i),0),s=s===F?s:Ga(s),u-=a?a.length:0,t&G){var c=o,d=a;o=a=F}var p=l?F:Mi(e),m=[e,t,n,o,a,c,d,r,i,s];if(p&&function(e,t){var n=e[1],o=t[1],a=n|o,r=a<131,i=o==Z&&8==n||o==Z&&n==J&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!r&&!i)return e;1&o&&(e[2]=t[2],a|=1&n?0:4);var s=t[3];if(s){var l=e[3];e[3]=l?lo(l,s,t[4]):s,e[4]=l?O(e[3],V):t[4]}s=t[5],s&&(l=e[5],e[5]=l?uo(l,s,t[6]):s,e[6]=l?O(e[5],V):t[6]),s=t[7],s&&(e[7]=s),o&Z&&(e[8]=null==e[8]?t[8]:ii(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=a}(m,p),e=m[0],t=m[1],n=m[2],o=m[3],a=m[4],!(s=m[9]=m[9]===F?l?0:e.length:ri(m[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)f=8==t||t==q?wo(e,t,s):t!=W&&33!=t||a.length?Ao.apply(F,m):jo(e,t,n,o);else var f=function(e,t,n){var o=1&t,a=bo(e);return function t(){return(this&&this!==tn&&this instanceof t?a:e).apply(o?n:this,arguments)}}(e,t,n);return sa((p?xi:Fi)(f,m),e,t)}function Uo(e,t,n,o){return e===F||Ra(e,Cr[n])&&!Tr.call(o,n)?t:e}function Oo(e,t,n,o,a,r){return Na(e)&&Na(t)&&(r.set(t,e),Cn(e,t,F,Oo,r),r.delete(t)),e}function Mo(e){return za(e)?F:e}function Ho(e,t,n,o,a,r){var i=1&n,s=e.length,l=t.length;if(s!=l&&!(i&&l>s))return!1;var u=r.get(e),c=r.get(t);if(u&&c)return u==t&&c==e;var d=-1,m=!0,f=2&n?new gt:F;for(r.set(e,t),r.set(t,e);++d<s;){var h=e[d],g=t[d];if(o)var v=i?o(g,h,d,t,e,r):o(h,g,d,e,t,r);if(v!==F){if(v)continue;m=!1;break}if(f){if(!p(t,(function(e,t){if(!j(f,t)&&(h===e||a(h,e,n,o,r)))return f.push(t)}))){m=!1;break}}else if(h!==g&&!a(h,g,n,o,r)){m=!1;break}}return r.delete(e),r.delete(t),m}function Eo(e){return Yi(aa(e,F,ga),e+"")}function No(e){return Yt(e,tr,Hi)}function Bo(e){return Yt(e,nr,Ei)}function Fo(e){for(var t=e.name+"",n=yi[t],o=Tr.call(yi,t)?n.length:0;o--;){var a=n[o],r=a.func;if(null==r||r==e)return a.name}return t}function zo(e){return(Tr.call(Ke,"placeholder")?Ke:e).placeholder}function Yo(){var e=Ke.iteratee||cr;return e=e===cr?wn:e,arguments.length?e(arguments[0],arguments[1]):e}function Vo(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function qo(e){for(var t=tr(e),n=t.length;n--;){var o=t[n],a=e[o];t[n]=[o,a,na(a)]}return t}function Wo(e,t){var n=function(e,t){return null==e?F:e[t]}(e,t);return bn(n)?n:F}function Go(e,t,n){for(var o=-1,a=(t=no(t,e)).length,r=!1;++o<a;){var i=ca(t[o]);if(!(r=null!=e&&n(e,i)))break;e=e[i]}return r||++o!=a?r:!!(a=null==e?0:e.length)&&Ea(a)&&Ko(i,a)&&(Ts(e)||Ps(e))}function Zo(e){return"function"!=typeof e.constructor||ta(e)?{}:Ci(Br(e))}function Jo(e){return Ts(e)||Ps(e)||!!(Vr&&e&&e[Vr])}function Ko(e,t){var n=typeof e;return!!(t=null==t?Q:t)&&("number"==n||"symbol"!=n&&it.test(e))&&e>-1&&e%1==0&&e<t}function Qo(e,t,n){if(!Na(n))return!1;var o=typeof t;return!!("number"==o?Da(n)&&Ko(t,n.length):"string"==o&&t in n)&&Ra(n[t],e)}function Xo(e,t){if(Ts(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Va(e))||Fe.test(e)||!Be.test(e)||null!=t&&e in _r(t)}function ea(e){var t=Fo(e),n=Ke[t];if("function"!=typeof n||!(t in pt.prototype))return!1;if(e===n)return!0;var o=Mi(n);return!!o&&e===o[0]}function ta(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Cr)}function na(e){return e==e&&!Na(e)}function oa(e,t){return function(n){return null!=n&&n[e]===t&&(t!==F||e in _r(n))}}function aa(t,n,o){return n=ri(n===F?t.length-1:n,0),function(){for(var a=arguments,r=-1,i=ri(a.length-n,0),s=gr(i);++r<i;)s[r]=a[n+r];r=-1;for(var l=gr(n+1);++r<n;)l[r]=a[r];return l[n]=o(s),e(t,this,l)}}function ra(e,t){return t.length<2?e:zt(e,Nn(t,0,-1))}function ia(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function sa(e,t,n){var o=t+"";return Yi(e,function(e,t){var n=t.length;if(!n)return e;var o=n-1;return t[o]=(n>1?"& ":"")+t[o],t=t.join(n>2?", ":" "),e.replace(Ge,"{\n/* [wrapped with "+t+"] */\n")}(o,pa(function(e){var t=e.match(Ze);return t?t[1].split(Je):[]}(o),n)))}function la(e){var t=0,n=0;return function(){var o=si(),a=16-(o-n);if(n=o,a>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(F,arguments)}}function ua(e,t){var n=-1,o=e.length,a=o-1;for(t=t===F?o:t;++n<t;){var r=Rn(n,a),i=e[r];e[r]=e[n],e[n]=i}return e.length=t,e}function ca(e){if("string"==typeof e||Va(e))return e;var t=e+"";return"0"==t&&1/e==-K?"-0":t}function da(e){if(null!=e){try{return Pr.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function pa(e,t){return n(oe,(function(n){var o="_."+n[0];t&n[1]&&!i(e,o)&&e.push(o)})),e.sort()}function ma(e){if(e instanceof pt)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=co(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function fa(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:Ga(n);return a<0&&(a=ri(o+a,0)),h(e,Yo(t,3),a)}function ha(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o-1;return n!==F&&(a=Ga(n),a=n<0?ri(o+a,0):ii(a,o-1)),h(e,Yo(t,3),a,!0)}function ga(e){return null!=e&&e.length?Mt(e,1):[]}function va(e){return e&&e.length?e[0]:F}function ya(e){var t=null==e?0:e.length;return t?e[t-1]:F}function ba(e,t){return e&&e.length&&t&&t.length?$n(e,t):e}function wa(e){return null==e?e:ci.call(e)}function _a(e){if(!e||!e.length)return[];var t=0;return e=r(e,(function(e){if(Ua(e))return t=ri(e.length,t),!0})),I(t,(function(t){return l(e,w(t))}))}function ka(t,n){if(!t||!t.length)return[];var o=_a(t);return null==n?o:l(o,(function(t){return e(n,F,t)}))}function Aa(e){var t=Ke(e);return t.__chain__=!0,t}function Ia(e,t){return t(e)}function Sa(e,t){return(Ts(e)?n:ji)(e,Yo(t,3))}function La(e,t){return(Ts(e)?o:Pi)(e,Yo(t,3))}function Ca(e,t){return(Ts(e)?l:In)(e,Yo(t,3))}function ja(e,t,n){return t=n?F:t,t=e&&null==t?e.length:t,Do(e,Z,F,F,F,F,t)}function Pa(e,t){var n;if("function"!=typeof t)throw new Ir(z);return e=Ga(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=F),n}}function Ta(e,t,n){function o(t){var n=l,o=u;return l=u=F,f=t,d=e.apply(o,n)}function a(e){var n=e-m;return m===F||n>=t||n<0||g&&e-f>=c}function r(){var e=ys();return a(e)?i(e):(p=zi(r,function(e){var n=t-(e-m);return g?ii(n,c-(e-f)):n}(e)),F)}function i(e){return p=F,v&&l?o(e):(l=u=F,d)}function s(){var e=ys(),n=a(e);if(l=arguments,u=this,m=e,n){if(p===F)return function(e){return f=e,p=zi(r,t),h?o(e):d}(m);if(g)return Ui(p),p=zi(r,t),o(m)}return p===F&&(p=zi(r,t)),d}var l,u,c,d,p,m,f=0,h=!1,g=!1,v=!0;if("function"!=typeof e)throw new Ir(z);return t=Ja(t)||0,Na(n)&&(h=!!n.leading,c=(g="maxWait"in n)?ri(Ja(n.maxWait)||0,t):c,v="trailing"in n?!!n.trailing:v),s.cancel=function(){p!==F&&Ui(p),f=0,l=m=u=p=F},s.flush=function(){return p===F?d:i(ys())},s}function $a(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Ir(z);var n=function(){var o=arguments,a=t?t.apply(this,o):o[0],r=n.cache;if(r.has(a))return r.get(a);var i=e.apply(this,o);return n.cache=r.set(a,i)||r,i};return n.cache=new($a.Cache||ht),n}function xa(e){if("function"!=typeof e)throw new Ir(z);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Ra(e,t){return e===t||e!=e&&t!=t}function Da(e){return null!=e&&Ea(e.length)&&!Ma(e)}function Ua(e){return Ba(e)&&Da(e)}function Oa(e){if(!Ba(e))return!1;var t=Vt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!za(e)}function Ma(e){if(!Na(e))return!1;var t=Vt(e);return t==ue||t==ce||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Ha(e){return"number"==typeof e&&e==Ga(e)}function Ea(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Q}function Na(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Ba(e){return null!=e&&"object"==typeof e}function Fa(e){return"number"==typeof e||Ba(e)&&Vt(e)==pe}function za(e){if(!Ba(e)||Vt(e)!=me)return!1;var t=Br(e);if(null===t)return!0;var n=Tr.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Pr.call(n)==Dr}function Ya(e){return"string"==typeof e||!Ts(e)&&Ba(e)&&Vt(e)==ve}function Va(e){return"symbol"==typeof e||Ba(e)&&Vt(e)==ye}function qa(e){if(!e)return[];if(Da(e))return Ya(e)?E(e):co(e);if(qr&&e[qr])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[qr]());var t=Ni(e);return(t==de?D:t==ge?M:ar)(e)}function Wa(e){return e?(e=Ja(e))===K||e===-K?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function Ga(e){var t=Wa(e),n=t%1;return t==t?n?t-n:t:0}function Za(e){return e?Pt(Ga(e),0,ee):0}function Ja(e){if("number"==typeof e)return e;if(Va(e))return X;if(Na(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Na(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=S(e);var n=ot.test(e);return n||rt.test(e)?Qt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Ka(e){return po(e,nr(e))}function Qa(e){return null==e?"":qn(e)}function Xa(e,t,n){var o=null==e?F:zt(e,t);return o===F?n:o}function er(e,t){return null!=e&&Go(e,t,en)}function tr(e){return Da(e)?yt(e):_n(e)}function nr(e){return Da(e)?yt(e,!0):kn(e)}function or(e,t){if(null==e)return{};var n=l(Bo(e),(function(e){return[e]}));return t=Yo(t),Tn(e,n,(function(e,n){return t(e,n[0])}))}function ar(e){return null==e?[]:C(e,tr(e))}function rr(e){return ul(Qa(e).toLowerCase())}function ir(e){return(e=Qa(e))&&e.replace(st,hn).replace(Bt,"")}function sr(e,t,n){return e=Qa(e),(t=n?F:t)===F?R(e)?B(e):m(e):e.match(t)||[]}function lr(e){return function(){return e}}function ur(e){return e}function cr(e){return wn("function"==typeof e?e:Tt(e,1))}function dr(e,t,o){var a=tr(t),r=Ft(t,a);null!=o||Na(t)&&(r.length||!a.length)||(o=t,t=e,e=this,r=Ft(t,tr(t)));var i=!(Na(o)&&"chain"in o&&!o.chain),s=Ma(e);return n(r,(function(n){var o=t[n];e[n]=o,s&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=co(this.__actions__)).push({func:o,args:arguments,thisArg:e}),n.__chain__=t,n}return o.apply(e,u([this.value()],arguments))})})),e}function pr(){}function mr(e){return Xo(e)?w(ca(e)):function(e){return function(t){return zt(t,e)}}(e)}function fr(){return[]}function hr(){return!1}var gr=(We=null==We?tn:yn.defaults(tn.Object(),We,yn.pick(tn,qt))).Array,vr=We.Date,yr=We.Error,br=We.Function,wr=We.Math,_r=We.Object,kr=We.RegExp,Ar=We.String,Ir=We.TypeError,Sr=gr.prototype,Lr=br.prototype,Cr=_r.prototype,jr=We["__core-js_shared__"],Pr=Lr.toString,Tr=Cr.hasOwnProperty,$r=0,xr=function(){var e=/[^.]+$/.exec(jr&&jr.keys&&jr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Rr=Cr.toString,Dr=Pr.call(_r),Ur=tn._,Or=kr("^"+Pr.call(Tr).replace(Ye,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Mr=an?We.Buffer:F,Hr=We.Symbol,Er=We.Uint8Array,Nr=Mr?Mr.allocUnsafe:F,Br=U(_r.getPrototypeOf,_r),Fr=_r.create,zr=Cr.propertyIsEnumerable,Yr=Sr.splice,Vr=Hr?Hr.isConcatSpreadable:F,qr=Hr?Hr.iterator:F,Wr=Hr?Hr.toStringTag:F,Gr=function(){try{var e=Wo(_r,"defineProperty");return e({},"",{}),e}catch(e){}}(),Zr=We.clearTimeout!==tn.clearTimeout&&We.clearTimeout,Jr=vr&&vr.now!==tn.Date.now&&vr.now,Kr=We.setTimeout!==tn.setTimeout&&We.setTimeout,Qr=wr.ceil,Xr=wr.floor,ei=_r.getOwnPropertySymbols,ti=Mr?Mr.isBuffer:F,ni=We.isFinite,oi=Sr.join,ai=U(_r.keys,_r),ri=wr.max,ii=wr.min,si=vr.now,li=We.parseInt,ui=wr.random,ci=Sr.reverse,di=Wo(We,"DataView"),pi=Wo(We,"Map"),mi=Wo(We,"Promise"),fi=Wo(We,"Set"),hi=Wo(We,"WeakMap"),gi=Wo(_r,"create"),vi=hi&&new hi,yi={},bi=da(di),wi=da(pi),_i=da(mi),ki=da(fi),Ai=da(hi),Ii=Hr?Hr.prototype:F,Si=Ii?Ii.valueOf:F,Li=Ii?Ii.toString:F,Ci=function(){function e(){}return function(t){if(!Na(t))return{};if(Fr)return Fr(t);e.prototype=t;var n=new e;return e.prototype=F,n}}();Ke.templateSettings={escape:He,evaluate:Ee,interpolate:Ne,variable:"",imports:{_:Ke}},Ke.prototype=ct.prototype,Ke.prototype.constructor=Ke,dt.prototype=Ci(ct.prototype),dt.prototype.constructor=dt,pt.prototype=Ci(ct.prototype),pt.prototype.constructor=pt,mt.prototype.clear=function(){this.__data__=gi?gi(null):{},this.size=0},mt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},mt.prototype.get=function(e){var t=this.__data__;if(gi){var n=t[e];return n===Y?F:n}return Tr.call(t,e)?t[e]:F},mt.prototype.has=function(e){var t=this.__data__;return gi?t[e]!==F:Tr.call(t,e)},mt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=gi&&t===F?Y:t,this},ft.prototype.clear=function(){this.__data__=[],this.size=0},ft.prototype.delete=function(e){var t=this.__data__,n=It(t,e);return!(n<0||(n==t.length-1?t.pop():Yr.call(t,n,1),--this.size,0))},ft.prototype.get=function(e){var t=this.__data__,n=It(t,e);return n<0?F:t[n][1]},ft.prototype.has=function(e){return It(this.__data__,e)>-1},ft.prototype.set=function(e,t){var n=this.__data__,o=It(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this},ht.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(pi||ft),string:new mt}},ht.prototype.delete=function(e){var t=Vo(this,e).delete(e);return this.size-=t?1:0,t},ht.prototype.get=function(e){return Vo(this,e).get(e)},ht.prototype.has=function(e){return Vo(this,e).has(e)},ht.prototype.set=function(e,t){var n=Vo(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this},gt.prototype.add=gt.prototype.push=function(e){return this.__data__.set(e,Y),this},gt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new ft,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ft){var o=n.__data__;if(!pi||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new ht(o)}return n.set(e,t),this.size=n.size,this};var ji=ho(Ht),Pi=ho(Et,!0),Ti=go(),$i=go(!0),xi=vi?function(e,t){return vi.set(e,t),e}:ur,Ri=Gr?function(e,t){return Gr(e,"toString",{configurable:!0,enumerable:!1,value:lr(t),writable:!0})}:ur,Di=Un,Ui=Zr||function(e){return tn.clearTimeout(e)},Oi=fi&&1/M(new fi([,-0]))[1]==K?function(e){return new fi(e)}:pr,Mi=vi?function(e){return vi.get(e)}:pr,Hi=ei?function(e){return null==e?[]:(e=_r(e),r(ei(e),(function(t){return zr.call(e,t)})))}:fr,Ei=ei?function(e){for(var t=[];e;)u(t,Hi(e)),e=Br(e);return t}:fr,Ni=Vt;(di&&Ni(new di(new ArrayBuffer(1)))!=_e||pi&&Ni(new pi)!=de||mi&&Ni(mi.resolve())!=fe||fi&&Ni(new fi)!=ge||hi&&Ni(new hi)!=be)&&(Ni=function(e){var t=Vt(e),n=t==me?e.constructor:F,o=n?da(n):"";if(o)switch(o){case bi:return _e;case wi:return de;case _i:return fe;case ki:return ge;case Ai:return be}return t});var Bi=jr?Ma:hr,Fi=la(xi),zi=Kr||function(e,t){return tn.setTimeout(e,t)},Yi=la(Ri),Vi=function(e){var t=$a(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(ze,(function(e,n,o,a){t.push(o?a.replace(Xe,"$1"):n||e)})),t})),qi=Un((function(e,t){return Ua(e)?Rt(e,Mt(t,1,Ua,!0)):[]})),Wi=Un((function(e,t){var n=ya(t);return Ua(n)&&(n=F),Ua(e)?Rt(e,Mt(t,1,Ua,!0),Yo(n,2)):[]})),Gi=Un((function(e,t){var n=ya(t);return Ua(n)&&(n=F),Ua(e)?Rt(e,Mt(t,1,Ua,!0),F,n):[]})),Zi=Un((function(e){var t=l(e,eo);return t.length&&t[0]===e[0]?nn(t):[]})),Ji=Un((function(e){var t=ya(e),n=l(e,eo);return t===ya(n)?t=F:n.pop(),n.length&&n[0]===e[0]?nn(n,Yo(t,2)):[]})),Ki=Un((function(e){var t=ya(e),n=l(e,eo);return(t="function"==typeof t?t:F)&&n.pop(),n.length&&n[0]===e[0]?nn(n,F,t):[]})),Qi=Un(ba),Xi=Eo((function(e,t){var n=null==e?0:e.length,o=jt(e,t);return xn(e,l(t,(function(e){return Ko(e,n)?+e:e})).sort(so)),o})),es=Un((function(e){return Wn(Mt(e,1,Ua,!0))})),ts=Un((function(e){var t=ya(e);return Ua(t)&&(t=F),Wn(Mt(e,1,Ua,!0),Yo(t,2))})),ns=Un((function(e){var t=ya(e);return t="function"==typeof t?t:F,Wn(Mt(e,1,Ua,!0),F,t)})),os=Un((function(e,t){return Ua(e)?Rt(e,t):[]})),as=Un((function(e){return Qn(r(e,Ua))})),rs=Un((function(e){var t=ya(e);return Ua(t)&&(t=F),Qn(r(e,Ua),Yo(t,2))})),is=Un((function(e){var t=ya(e);return t="function"==typeof t?t:F,Qn(r(e,Ua),F,t)})),ss=Un(_a),ls=Un((function(e){var t=e.length,n=t>1?e[t-1]:F;return n="function"==typeof n?(e.pop(),n):F,ka(e,n)})),us=Eo((function(e){var t=e.length,n=t?e[0]:0,o=this.__wrapped__,a=function(t){return jt(t,e)};return!(t>1||this.__actions__.length)&&o instanceof pt&&Ko(n)?((o=o.slice(n,+n+(t?1:0))).__actions__.push({func:Ia,args:[a],thisArg:F}),new dt(o,this.__chain__).thru((function(e){return t&&!e.length&&e.push(F),e}))):this.thru(a)})),cs=mo((function(e,t,n){Tr.call(e,n)?++e[n]:Ct(e,n,1)})),ds=_o(fa),ps=_o(ha),ms=mo((function(e,t,n){Tr.call(e,n)?e[n].push(t):Ct(e,n,[t])})),fs=Un((function(t,n,o){var a=-1,r="function"==typeof n,i=Da(t)?gr(t.length):[];return ji(t,(function(t){i[++a]=r?e(n,t,o):on(t,n,o)})),i})),hs=mo((function(e,t,n){Ct(e,n,t)})),gs=mo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),vs=Un((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Qo(e,t[0],t[1])?t=[]:n>2&&Qo(t[0],t[1],t[2])&&(t=[t[0]]),Pn(e,Mt(t,1),[])})),ys=Jr||function(){return tn.Date.now()},bs=Un((function(e,t,n){var o=1;if(n.length){var a=O(n,zo(bs));o|=W}return Do(e,o,t,n,a)})),ws=Un((function(e,t,n){var o=3;if(n.length){var a=O(n,zo(ws));o|=W}return Do(t,o,e,n,a)})),_s=Un((function(e,t){return xt(e,1,t)})),ks=Un((function(e,t,n){return xt(e,Ja(t)||0,n)}));$a.Cache=ht;var As=Di((function(t,n){var o=(n=1==n.length&&Ts(n[0])?l(n[0],L(Yo())):l(Mt(n,1),L(Yo()))).length;return Un((function(a){for(var r=-1,i=ii(a.length,o);++r<i;)a[r]=n[r].call(this,a[r]);return e(t,this,a)}))})),Is=Un((function(e,t){return Do(e,W,F,t,O(t,zo(Is)))})),Ss=Un((function(e,t){return Do(e,G,F,t,O(t,zo(Ss)))})),Ls=Eo((function(e,t){return Do(e,J,F,F,F,t)})),Cs=To(Jt),js=To((function(e,t){return e>=t})),Ps=rn(function(){return arguments}())?rn:function(e){return Ba(e)&&Tr.call(e,"callee")&&!zr.call(e,"callee")},Ts=gr.isArray,$s=ln?L(ln):function(e){return Ba(e)&&Vt(e)==we},xs=ti||hr,Rs=un?L(un):function(e){return Ba(e)&&Vt(e)==se},Ds=cn?L(cn):function(e){return Ba(e)&&Ni(e)==de},Us=dn?L(dn):function(e){return Ba(e)&&Vt(e)==he},Os=pn?L(pn):function(e){return Ba(e)&&Ni(e)==ge},Ms=mn?L(mn):function(e){return Ba(e)&&Ea(e.length)&&!!Gt[Vt(e)]},Hs=To(An),Es=To((function(e,t){return e<=t})),Ns=fo((function(e,t){if(ta(t)||Da(t))return po(t,tr(t),e),F;for(var n in t)Tr.call(t,n)&&At(e,n,t[n])})),Bs=fo((function(e,t){po(t,nr(t),e)})),Fs=fo((function(e,t,n,o){po(t,nr(t),e,o)})),zs=fo((function(e,t,n,o){po(t,tr(t),e,o)})),Ys=Eo(jt),Vs=Un((function(e,t){e=_r(e);var n=-1,o=t.length,a=o>2?t[2]:F;for(a&&Qo(t[0],t[1],a)&&(o=1);++n<o;)for(var r=t[n],i=nr(r),s=-1,l=i.length;++s<l;){var u=i[s],c=e[u];(c===F||Ra(c,Cr[u])&&!Tr.call(e,u))&&(e[u]=r[u])}return e})),qs=Un((function(t){return t.push(F,Oo),e(Ks,F,t)})),Ws=Io((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Rr.call(t)),e[t]=n}),lr(ur)),Gs=Io((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Rr.call(t)),Tr.call(e,t)?e[t].push(n):e[t]=[n]}),Yo),Zs=Un(on),Js=fo((function(e,t,n){Cn(e,t,n)})),Ks=fo((function(e,t,n,o){Cn(e,t,n,o)})),Qs=Eo((function(e,t){var n={};if(null==e)return n;var o=!1;t=l(t,(function(t){return t=no(t,e),o||(o=t.length>1),t})),po(e,Bo(e),n),o&&(n=Tt(n,7,Mo));for(var a=t.length;a--;)Gn(n,t[a]);return n})),Xs=Eo((function(e,t){return null==e?{}:function(e,t){return Tn(e,t,(function(t,n){return er(e,n)}))}(e,t)})),el=Ro(tr),tl=Ro(nr),nl=yo((function(e,t,n){return t=t.toLowerCase(),e+(n?rr(t):t)})),ol=yo((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),al=yo((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),rl=vo("toLowerCase"),il=yo((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),sl=yo((function(e,t,n){return e+(n?" ":"")+ul(t)})),ll=yo((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),ul=vo("toUpperCase"),cl=Un((function(t,n){try{return e(t,F,n)}catch(e){return Oa(e)?e:new yr(e)}})),dl=Eo((function(e,t){return n(t,(function(t){t=ca(t),Ct(e,t,bs(e[t],e))})),e})),pl=ko(),ml=ko(!0),fl=Un((function(e,t){return function(n){return on(n,e,t)}})),hl=Un((function(e,t){return function(n){return on(e,n,t)}})),gl=Lo(l),vl=Lo(a),yl=Lo(p),bl=Po(),wl=Po(!0),_l=So((function(e,t){return e+t}),0),kl=xo("ceil"),Al=So((function(e,t){return e/t}),1),Il=xo("floor"),Sl=So((function(e,t){return e*t}),1),Ll=xo("round"),Cl=So((function(e,t){return e-t}),0);return Ke.after=function(e,t){if("function"!=typeof t)throw new Ir(z);return e=Ga(e),function(){if(--e<1)return t.apply(this,arguments)}},Ke.ary=ja,Ke.assign=Ns,Ke.assignIn=Bs,Ke.assignInWith=Fs,Ke.assignWith=zs,Ke.at=Ys,Ke.before=Pa,Ke.bind=bs,Ke.bindAll=dl,Ke.bindKey=ws,Ke.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ts(e)?e:[e]},Ke.chain=Aa,Ke.chunk=function(e,t,n){t=(n?Qo(e,t,n):t===F)?1:ri(Ga(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var a=0,r=0,i=gr(Qr(o/t));a<o;)i[r++]=Nn(e,a,a+=t);return i},Ke.compact=function(e){for(var t=-1,n=null==e?0:e.length,o=0,a=[];++t<n;){var r=e[t];r&&(a[o++]=r)}return a},Ke.concat=function(){var e=arguments.length;if(!e)return[];for(var t=gr(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return u(Ts(n)?co(n):[n],Mt(t,1))},Ke.cond=function(t){var n=null==t?0:t.length,o=Yo();return t=n?l(t,(function(e){if("function"!=typeof e[1])throw new Ir(z);return[o(e[0]),e[1]]})):[],Un((function(o){for(var a=-1;++a<n;){var r=t[a];if(e(r[0],this,o))return e(r[1],this,o)}}))},Ke.conforms=function(e){return function(e){var t=tr(e);return function(n){return $t(n,e,t)}}(Tt(e,1))},Ke.constant=lr,Ke.countBy=cs,Ke.create=function(e,t){var n=Ci(e);return null==t?n:Lt(n,t)},Ke.curry=function e(t,n,o){var a=Do(t,8,F,F,F,F,F,n=o?F:n);return a.placeholder=e.placeholder,a},Ke.curryRight=function e(t,n,o){var a=Do(t,q,F,F,F,F,F,n=o?F:n);return a.placeholder=e.placeholder,a},Ke.debounce=Ta,Ke.defaults=Vs,Ke.defaultsDeep=qs,Ke.defer=_s,Ke.delay=ks,Ke.difference=qi,Ke.differenceBy=Wi,Ke.differenceWith=Gi,Ke.drop=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,(t=n||t===F?1:Ga(t))<0?0:t,o):[]},Ke.dropRight=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,0,(t=o-(t=n||t===F?1:Ga(t)))<0?0:t):[]},Ke.dropRightWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!0,!0):[]},Ke.dropWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!0):[]},Ke.fill=function(e,t,n,o){var a=null==e?0:e.length;return a?(n&&"number"!=typeof n&&Qo(e,t,n)&&(n=0,o=a),function(e,t,n,o){var a=e.length;for((n=Ga(n))<0&&(n=-n>a?0:a+n),(o=o===F||o>a?a:Ga(o))<0&&(o+=a),o=n>o?0:Za(o);n<o;)e[n++]=t;return e}(e,t,n,o)):[]},Ke.filter=function(e,t){return(Ts(e)?r:Ot)(e,Yo(t,3))},Ke.flatMap=function(e,t){return Mt(Ca(e,t),1)},Ke.flatMapDeep=function(e,t){return Mt(Ca(e,t),K)},Ke.flatMapDepth=function(e,t,n){return n=n===F?1:Ga(n),Mt(Ca(e,t),n)},Ke.flatten=ga,Ke.flattenDeep=function(e){return null!=e&&e.length?Mt(e,K):[]},Ke.flattenDepth=function(e,t){return null!=e&&e.length?Mt(e,t=t===F?1:Ga(t)):[]},Ke.flip=function(e){return Do(e,512)},Ke.flow=pl,Ke.flowRight=ml,Ke.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,o={};++t<n;){var a=e[t];o[a[0]]=a[1]}return o},Ke.functions=function(e){return null==e?[]:Ft(e,tr(e))},Ke.functionsIn=function(e){return null==e?[]:Ft(e,nr(e))},Ke.groupBy=ms,Ke.initial=function(e){return null!=e&&e.length?Nn(e,0,-1):[]},Ke.intersection=Zi,Ke.intersectionBy=Ji,Ke.intersectionWith=Ki,Ke.invert=Ws,Ke.invertBy=Gs,Ke.invokeMap=fs,Ke.iteratee=cr,Ke.keyBy=hs,Ke.keys=tr,Ke.keysIn=nr,Ke.map=Ca,Ke.mapKeys=function(e,t){var n={};return t=Yo(t,3),Ht(e,(function(e,o,a){Ct(n,t(e,o,a),e)})),n},Ke.mapValues=function(e,t){var n={};return t=Yo(t,3),Ht(e,(function(e,o,a){Ct(n,o,t(e,o,a))})),n},Ke.matches=function(e){return Sn(Tt(e,1))},Ke.matchesProperty=function(e,t){return Ln(e,Tt(t,1))},Ke.memoize=$a,Ke.merge=Js,Ke.mergeWith=Ks,Ke.method=fl,Ke.methodOf=hl,Ke.mixin=dr,Ke.negate=xa,Ke.nthArg=function(e){return e=Ga(e),Un((function(t){return jn(t,e)}))},Ke.omit=Qs,Ke.omitBy=function(e,t){return or(e,xa(Yo(t)))},Ke.once=function(e){return Pa(2,e)},Ke.orderBy=function(e,t,n,o){return null==e?[]:(Ts(t)||(t=null==t?[]:[t]),Ts(n=o?F:n)||(n=null==n?[]:[n]),Pn(e,t,n))},Ke.over=gl,Ke.overArgs=As,Ke.overEvery=vl,Ke.overSome=yl,Ke.partial=Is,Ke.partialRight=Ss,Ke.partition=gs,Ke.pick=Xs,Ke.pickBy=or,Ke.property=mr,Ke.propertyOf=function(e){return function(t){return null==e?F:zt(e,t)}},Ke.pull=Qi,Ke.pullAll=ba,Ke.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?$n(e,t,Yo(n,2)):e},Ke.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?$n(e,t,F,n):e},Ke.pullAt=Xi,Ke.range=bl,Ke.rangeRight=wl,Ke.rearg=Ls,Ke.reject=function(e,t){return(Ts(e)?r:Ot)(e,xa(Yo(t,3)))},Ke.remove=function(e,t){var n=[];if(!e||!e.length)return n;var o=-1,a=[],r=e.length;for(t=Yo(t,3);++o<r;){var i=e[o];t(i,o,e)&&(n.push(i),a.push(o))}return xn(e,a),n},Ke.rest=function(e,t){if("function"!=typeof e)throw new Ir(z);return Un(e,t=t===F?t:Ga(t))},Ke.reverse=wa,Ke.sampleSize=function(e,t,n){return t=(n?Qo(e,t,n):t===F)?1:Ga(t),(Ts(e)?wt:Mn)(e,t)},Ke.set=function(e,t,n){return null==e?e:Hn(e,t,n)},Ke.setWith=function(e,t,n,o){return o="function"==typeof o?o:F,null==e?e:Hn(e,t,n,o)},Ke.shuffle=function(e){return(Ts(e)?_t:En)(e)},Ke.slice=function(e,t,n){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Qo(e,t,n)?(t=0,n=o):(t=null==t?0:Ga(t),n=n===F?o:Ga(n)),Nn(e,t,n)):[]},Ke.sortBy=vs,Ke.sortedUniq=function(e){return e&&e.length?Yn(e):[]},Ke.sortedUniqBy=function(e,t){return e&&e.length?Yn(e,Yo(t,2)):[]},Ke.split=function(e,t,n){return n&&"number"!=typeof n&&Qo(e,t,n)&&(t=n=F),(n=n===F?ee:n>>>0)?(e=Qa(e))&&("string"==typeof t||null!=t&&!Us(t))&&(!(t=qn(t))&&x(e))?oo(E(e),0,n):e.split(t,n):[]},Ke.spread=function(t,n){if("function"!=typeof t)throw new Ir(z);return n=null==n?0:ri(Ga(n),0),Un((function(o){var a=o[n],r=oo(o,0,n);return a&&u(r,a),e(t,this,r)}))},Ke.tail=function(e){var t=null==e?0:e.length;return t?Nn(e,1,t):[]},Ke.take=function(e,t,n){return e&&e.length?Nn(e,0,(t=n||t===F?1:Ga(t))<0?0:t):[]},Ke.takeRight=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,(t=o-(t=n||t===F?1:Ga(t)))<0?0:t,o):[]},Ke.takeRightWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!1,!0):[]},Ke.takeWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3)):[]},Ke.tap=function(e,t){return t(e),e},Ke.throttle=function(e,t,n){var o=!0,a=!0;if("function"!=typeof e)throw new Ir(z);return Na(n)&&(o="leading"in n?!!n.leading:o,a="trailing"in n?!!n.trailing:a),Ta(e,t,{leading:o,maxWait:t,trailing:a})},Ke.thru=Ia,Ke.toArray=qa,Ke.toPairs=el,Ke.toPairsIn=tl,Ke.toPath=function(e){return Ts(e)?l(e,ca):Va(e)?[e]:co(Vi(Qa(e)))},Ke.toPlainObject=Ka,Ke.transform=function(e,t,o){var a=Ts(e),r=a||xs(e)||Ms(e);if(t=Yo(t,4),null==o){var i=e&&e.constructor;o=r?a?new i:[]:Na(e)&&Ma(i)?Ci(Br(e)):{}}return(r?n:Ht)(e,(function(e,n,a){return t(o,e,n,a)})),o},Ke.unary=function(e){return ja(e,1)},Ke.union=es,Ke.unionBy=ts,Ke.unionWith=ns,Ke.uniq=function(e){return e&&e.length?Wn(e):[]},Ke.uniqBy=function(e,t){return e&&e.length?Wn(e,Yo(t,2)):[]},Ke.uniqWith=function(e,t){return t="function"==typeof t?t:F,e&&e.length?Wn(e,F,t):[]},Ke.unset=function(e,t){return null==e||Gn(e,t)},Ke.unzip=_a,Ke.unzipWith=ka,Ke.update=function(e,t,n){return null==e?e:Zn(e,t,to(n))},Ke.updateWith=function(e,t,n,o){return o="function"==typeof o?o:F,null==e?e:Zn(e,t,to(n),o)},Ke.values=ar,Ke.valuesIn=function(e){return null==e?[]:C(e,nr(e))},Ke.without=os,Ke.words=sr,Ke.wrap=function(e,t){return Is(to(t),e)},Ke.xor=as,Ke.xorBy=rs,Ke.xorWith=is,Ke.zip=ss,Ke.zipObject=function(e,t){return Xn(e||[],t||[],At)},Ke.zipObjectDeep=function(e,t){return Xn(e||[],t||[],Hn)},Ke.zipWith=ls,Ke.entries=el,Ke.entriesIn=tl,Ke.extend=Bs,Ke.extendWith=Fs,dr(Ke,Ke),Ke.add=_l,Ke.attempt=cl,Ke.camelCase=nl,Ke.capitalize=rr,Ke.ceil=kl,Ke.clamp=function(e,t,n){return n===F&&(n=t,t=F),n!==F&&(n=(n=Ja(n))==n?n:0),t!==F&&(t=(t=Ja(t))==t?t:0),Pt(Ja(e),t,n)},Ke.clone=function(e){return Tt(e,4)},Ke.cloneDeep=function(e){return Tt(e,5)},Ke.cloneDeepWith=function(e,t){return Tt(e,5,t="function"==typeof t?t:F)},Ke.cloneWith=function(e,t){return Tt(e,4,t="function"==typeof t?t:F)},Ke.conformsTo=function(e,t){return null==t||$t(e,t,tr(t))},Ke.deburr=ir,Ke.defaultTo=function(e,t){return null==e||e!=e?t:e},Ke.divide=Al,Ke.endsWith=function(e,t,n){e=Qa(e),t=qn(t);var o=e.length,a=n=n===F?o:Pt(Ga(n),0,o);return(n-=t.length)>=0&&e.slice(n,a)==t},Ke.eq=Ra,Ke.escape=function(e){return(e=Qa(e))&&Me.test(e)?e.replace(Ue,gn):e},Ke.escapeRegExp=function(e){return(e=Qa(e))&&Ve.test(e)?e.replace(Ye,"\\$&"):e},Ke.every=function(e,t,n){var o=Ts(e)?a:Dt;return n&&Qo(e,t,n)&&(t=F),o(e,Yo(t,3))},Ke.find=ds,Ke.findIndex=fa,Ke.findKey=function(e,t){return f(e,Yo(t,3),Ht)},Ke.findLast=ps,Ke.findLastIndex=ha,Ke.findLastKey=function(e,t){return f(e,Yo(t,3),Et)},Ke.floor=Il,Ke.forEach=Sa,Ke.forEachRight=La,Ke.forIn=function(e,t){return null==e?e:Ti(e,Yo(t,3),nr)},Ke.forInRight=function(e,t){return null==e?e:$i(e,Yo(t,3),nr)},Ke.forOwn=function(e,t){return e&&Ht(e,Yo(t,3))},Ke.forOwnRight=function(e,t){return e&&Et(e,Yo(t,3))},Ke.get=Xa,Ke.gt=Cs,Ke.gte=js,Ke.has=function(e,t){return null!=e&&Go(e,t,Xt)},Ke.hasIn=er,Ke.head=va,Ke.identity=ur,Ke.includes=function(e,t,n,o){e=Da(e)?e:ar(e),n=n&&!o?Ga(n):0;var a=e.length;return n<0&&(n=ri(a+n,0)),Ya(e)?n<=a&&e.indexOf(t,n)>-1:!!a&&g(e,t,n)>-1},Ke.indexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:Ga(n);return a<0&&(a=ri(o+a,0)),g(e,t,a)},Ke.inRange=function(e,t,n){return t=Wa(t),n===F?(n=t,t=0):n=Wa(n),function(e,t,n){return e>=ii(t,n)&&e<ri(t,n)}(e=Ja(e),t,n)},Ke.invoke=Zs,Ke.isArguments=Ps,Ke.isArray=Ts,Ke.isArrayBuffer=$s,Ke.isArrayLike=Da,Ke.isArrayLikeObject=Ua,Ke.isBoolean=function(e){return!0===e||!1===e||Ba(e)&&Vt(e)==ie},Ke.isBuffer=xs,Ke.isDate=Rs,Ke.isElement=function(e){return Ba(e)&&1===e.nodeType&&!za(e)},Ke.isEmpty=function(e){if(null==e)return!0;if(Da(e)&&(Ts(e)||"string"==typeof e||"function"==typeof e.splice||xs(e)||Ms(e)||Ps(e)))return!e.length;var t=Ni(e);if(t==de||t==ge)return!e.size;if(ta(e))return!_n(e).length;for(var n in e)if(Tr.call(e,n))return!1;return!0},Ke.isEqual=function(e,t){return sn(e,t)},Ke.isEqualWith=function(e,t,n){var o=(n="function"==typeof n?n:F)?n(e,t):F;return o===F?sn(e,t,F,n):!!o},Ke.isError=Oa,Ke.isFinite=function(e){return"number"==typeof e&&ni(e)},Ke.isFunction=Ma,Ke.isInteger=Ha,Ke.isLength=Ea,Ke.isMap=Ds,Ke.isMatch=function(e,t){return e===t||fn(e,t,qo(t))},Ke.isMatchWith=function(e,t,n){return n="function"==typeof n?n:F,fn(e,t,qo(t),n)},Ke.isNaN=function(e){return Fa(e)&&e!=+e},Ke.isNative=function(e){if(Bi(e))throw new yr("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(e)},Ke.isNil=function(e){return null==e},Ke.isNull=function(e){return null===e},Ke.isNumber=Fa,Ke.isObject=Na,Ke.isObjectLike=Ba,Ke.isPlainObject=za,Ke.isRegExp=Us,Ke.isSafeInteger=function(e){return Ha(e)&&e>=-Q&&e<=Q},Ke.isSet=Os,Ke.isString=Ya,Ke.isSymbol=Va,Ke.isTypedArray=Ms,Ke.isUndefined=function(e){return e===F},Ke.isWeakMap=function(e){return Ba(e)&&Ni(e)==be},Ke.isWeakSet=function(e){return Ba(e)&&"[object WeakSet]"==Vt(e)},Ke.join=function(e,t){return null==e?"":oi.call(e,t)},Ke.kebabCase=ol,Ke.last=ya,Ke.lastIndexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o;return n!==F&&(a=(a=Ga(n))<0?ri(o+a,0):ii(a,o-1)),t==t?function(e,t,n){for(var o=n+1;o--;)if(e[o]===t)return o;return o}(e,t,a):h(e,y,a,!0)},Ke.lowerCase=al,Ke.lowerFirst=rl,Ke.lt=Hs,Ke.lte=Es,Ke.max=function(e){return e&&e.length?Ut(e,ur,Jt):F},Ke.maxBy=function(e,t){return e&&e.length?Ut(e,Yo(t,2),Jt):F},Ke.mean=function(e){return b(e,ur)},Ke.meanBy=function(e,t){return b(e,Yo(t,2))},Ke.min=function(e){return e&&e.length?Ut(e,ur,An):F},Ke.minBy=function(e,t){return e&&e.length?Ut(e,Yo(t,2),An):F},Ke.stubArray=fr,Ke.stubFalse=hr,Ke.stubObject=function(){return{}},Ke.stubString=function(){return""},Ke.stubTrue=function(){return!0},Ke.multiply=Sl,Ke.nth=function(e,t){return e&&e.length?jn(e,Ga(t)):F},Ke.noConflict=function(){return tn._===this&&(tn._=Ur),this},Ke.noop=pr,Ke.now=ys,Ke.pad=function(e,t,n){e=Qa(e);var o=(t=Ga(t))?H(e):0;if(!t||o>=t)return e;var a=(t-o)/2;return Co(Xr(a),n)+e+Co(Qr(a),n)},Ke.padEnd=function(e,t,n){e=Qa(e);var o=(t=Ga(t))?H(e):0;return t&&o<t?e+Co(t-o,n):e},Ke.padStart=function(e,t,n){e=Qa(e);var o=(t=Ga(t))?H(e):0;return t&&o<t?Co(t-o,n)+e:e},Ke.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),li(Qa(e).replace(qe,""),t||0)},Ke.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Qo(e,t,n)&&(t=n=F),n===F&&("boolean"==typeof t?(n=t,t=F):"boolean"==typeof e&&(n=e,e=F)),e===F&&t===F?(e=0,t=1):(e=Wa(e),t===F?(t=e,e=0):t=Wa(t)),e>t){var o=e;e=t,t=o}if(n||e%1||t%1){var a=ui();return ii(e+a*(t-e+Kt("1e-"+((a+"").length-1))),t)}return Rn(e,t)},Ke.reduce=function(e,t,n){var o=Ts(e)?c:k,a=arguments.length<3;return o(e,Yo(t,4),n,a,ji)},Ke.reduceRight=function(e,t,n){var o=Ts(e)?d:k,a=arguments.length<3;return o(e,Yo(t,4),n,a,Pi)},Ke.repeat=function(e,t,n){return t=(n?Qo(e,t,n):t===F)?1:Ga(t),Dn(Qa(e),t)},Ke.replace=function(){var e=arguments,t=Qa(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Ke.result=function(e,t,n){var o=-1,a=(t=no(t,e)).length;for(a||(a=1,e=F);++o<a;){var r=null==e?F:e[ca(t[o])];r===F&&(o=a,r=n),e=Ma(r)?r.call(e):r}return e},Ke.round=Ll,Ke.runInContext=_,Ke.sample=function(e){return(Ts(e)?bt:On)(e)},Ke.size=function(e){if(null==e)return 0;if(Da(e))return Ya(e)?H(e):e.length;var t=Ni(e);return t==de||t==ge?e.size:_n(e).length},Ke.snakeCase=il,Ke.some=function(e,t,n){var o=Ts(e)?p:Bn;return n&&Qo(e,t,n)&&(t=F),o(e,Yo(t,3))},Ke.sortedIndex=function(e,t){return Fn(e,t)},Ke.sortedIndexBy=function(e,t,n){return zn(e,t,Yo(n,2))},Ke.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var o=Fn(e,t);if(o<n&&Ra(e[o],t))return o}return-1},Ke.sortedLastIndex=function(e,t){return Fn(e,t,!0)},Ke.sortedLastIndexBy=function(e,t,n){return zn(e,t,Yo(n,2),!0)},Ke.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Fn(e,t,!0)-1;if(Ra(e[n],t))return n}return-1},Ke.startCase=sl,Ke.startsWith=function(e,t,n){return e=Qa(e),n=null==n?0:Pt(Ga(n),0,e.length),t=qn(t),e.slice(n,n+t.length)==t},Ke.subtract=Cl,Ke.sum=function(e){return e&&e.length?A(e,ur):0},Ke.sumBy=function(e,t){return e&&e.length?A(e,Yo(t,2)):0},Ke.template=function(e,t,n){var o=Ke.templateSettings;n&&Qo(e,t,n)&&(t=F),e=Qa(e),t=Fs({},t,o,Uo);var a,r,i=Fs({},t.imports,o.imports,Uo),s=tr(i),l=C(i,s),u=0,c=t.interpolate||lt,d="__p += '",p=kr((t.escape||lt).source+"|"+c.source+"|"+(c===Ne?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),m="//# sourceURL="+(Tr.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Wt+"]")+"\n";e.replace(p,(function(t,n,o,i,s,l){return o||(o=i),d+=e.slice(u,l).replace(ut,$),n&&(a=!0,d+="' +\n__e("+n+") +\n'"),s&&(r=!0,d+="';\n"+s+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),u=l+t.length,t})),d+="';\n";var f=Tr.call(t,"variable")&&t.variable;if(f){if(Qe.test(f))throw new yr("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(r?d.replace($e,""):d).replace(xe,"$1").replace(Re,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(r?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var h=cl((function(){return br(s,m+"return "+d).apply(F,l)}));if(h.source=d,Oa(h))throw h;return h},Ke.times=function(e,t){if((e=Ga(e))<1||e>Q)return[];var n=ee,o=ii(e,ee);t=Yo(t),e-=ee;for(var a=I(o,t);++n<e;)t(n);return a},Ke.toFinite=Wa,Ke.toInteger=Ga,Ke.toLength=Za,Ke.toLower=function(e){return Qa(e).toLowerCase()},Ke.toNumber=Ja,Ke.toSafeInteger=function(e){return e?Pt(Ga(e),-Q,Q):0===e?e:0},Ke.toString=Qa,Ke.toUpper=function(e){return Qa(e).toUpperCase()},Ke.trim=function(e,t,n){if((e=Qa(e))&&(n||t===F))return S(e);if(!e||!(t=qn(t)))return e;var o=E(e),a=E(t);return oo(o,P(o,a),T(o,a)+1).join("")},Ke.trimEnd=function(e,t,n){if((e=Qa(e))&&(n||t===F))return e.slice(0,N(e)+1);if(!e||!(t=qn(t)))return e;var o=E(e);return oo(o,0,T(o,E(t))+1).join("")},Ke.trimStart=function(e,t,n){if((e=Qa(e))&&(n||t===F))return e.replace(qe,"");if(!e||!(t=qn(t)))return e;var o=E(e);return oo(o,P(o,E(t))).join("")},Ke.truncate=function(e,t){var n=30,o="...";if(Na(t)){var a="separator"in t?t.separator:a;n="length"in t?Ga(t.length):n,o="omission"in t?qn(t.omission):o}var r=(e=Qa(e)).length;if(x(e)){var i=E(e);r=i.length}if(n>=r)return e;var s=n-H(o);if(s<1)return o;var l=i?oo(i,0,s).join(""):e.slice(0,s);if(a===F)return l+o;if(i&&(s+=l.length-s),Us(a)){if(e.slice(s).search(a)){var u,c=l;for(a.global||(a=kr(a.source,Qa(tt.exec(a))+"g")),a.lastIndex=0;u=a.exec(c);)var d=u.index;l=l.slice(0,d===F?s:d)}}else if(e.indexOf(qn(a),s)!=s){var p=l.lastIndexOf(a);p>-1&&(l=l.slice(0,p))}return l+o},Ke.unescape=function(e){return(e=Qa(e))&&Oe.test(e)?e.replace(De,vn):e},Ke.uniqueId=function(e){var t=++$r;return Qa(e)+t},Ke.upperCase=ll,Ke.upperFirst=ul,Ke.each=Sa,Ke.eachRight=La,Ke.first=va,dr(Ke,function(){var e={};return Ht(Ke,(function(t,n){Tr.call(Ke.prototype,n)||(e[n]=t)})),e}(),{chain:!1}),Ke.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Ke[e].placeholder=Ke})),n(["drop","take"],(function(e,t){pt.prototype[e]=function(n){n=n===F?1:ri(Ga(n),0);var o=this.__filtered__&&!t?new pt(this):this.clone();return o.__filtered__?o.__takeCount__=ii(n,o.__takeCount__):o.__views__.push({size:ii(n,ee),type:e+(o.__dir__<0?"Right":"")}),o},pt.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),n(["filter","map","takeWhile"],(function(e,t){var n=t+1,o=1==n||3==n;pt.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Yo(e,3),type:n}),t.__filtered__=t.__filtered__||o,t}})),n(["head","last"],(function(e,t){var n="take"+(t?"Right":"");pt.prototype[e]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");pt.prototype[e]=function(){return this.__filtered__?new pt(this):this[n](1)}})),pt.prototype.compact=function(){return this.filter(ur)},pt.prototype.find=function(e){return this.filter(e).head()},pt.prototype.findLast=function(e){return this.reverse().find(e)},pt.prototype.invokeMap=Un((function(e,t){return"function"==typeof e?new pt(this):this.map((function(n){return on(n,e,t)}))})),pt.prototype.reject=function(e){return this.filter(xa(Yo(e)))},pt.prototype.slice=function(e,t){e=Ga(e);var n=this;return n.__filtered__&&(e>0||t<0)?new pt(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==F&&(n=(t=Ga(t))<0?n.dropRight(-t):n.take(t-e)),n)},pt.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},pt.prototype.toArray=function(){return this.take(ee)},Ht(pt.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),a=Ke[o?"take"+("last"==t?"Right":""):t],r=o||/^find/.test(t);a&&(Ke.prototype[t]=function(){var t=this.__wrapped__,i=o?[1]:arguments,s=t instanceof pt,l=i[0],c=s||Ts(t),d=function(e){var t=a.apply(Ke,u([e],i));return o&&p?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(s=c=!1);var p=this.__chain__,m=!!this.__actions__.length,f=r&&!p,h=s&&!m;if(!r&&c){t=h?t:new pt(this);var g=e.apply(t,i);return g.__actions__.push({func:Ia,args:[d],thisArg:F}),new dt(g,p)}return f&&h?e.apply(this,i):(g=this.thru(d),f?o?g.value()[0]:g.value():g)})})),n(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Sr[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);Ke.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var a=this.value();return t.apply(Ts(a)?a:[],e)}return this[n]((function(n){return t.apply(Ts(n)?n:[],e)}))}})),Ht(pt.prototype,(function(e,t){var n=Ke[t];if(n){var o=n.name+"";Tr.call(yi,o)||(yi[o]=[]),yi[o].push({name:t,func:n})}})),yi[Ao(F,2).name]=[{name:"wrapper",func:F}],pt.prototype.clone=function(){var e=new pt(this.__wrapped__);return e.__actions__=co(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=co(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=co(this.__views__),e},pt.prototype.reverse=function(){if(this.__filtered__){var e=new pt(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},pt.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ts(e),o=t<0,a=n?e.length:0,r=function(e,t,n){for(var o=-1,a=n.length;++o<a;){var r=n[o],i=r.size;switch(r.type){case"drop":e+=i;break;case"dropRight":t-=i;break;case"take":t=ii(t,e+i);break;case"takeRight":e=ri(e,t-i)}}return{start:e,end:t}}(0,a,this.__views__),i=r.start,s=r.end,l=s-i,u=o?s:i-1,c=this.__iteratees__,d=c.length,p=0,m=ii(l,this.__takeCount__);if(!n||!o&&a==l&&m==l)return Kn(e,this.__actions__);var f=[];e:for(;l--&&p<m;){for(var h=-1,g=e[u+=t];++h<d;){var v=c[h],y=v.iteratee,b=v.type,w=y(g);if(2==b)g=w;else if(!w){if(1==b)continue e;break e}}f[p++]=g}return f},Ke.prototype.at=us,Ke.prototype.chain=function(){return Aa(this)},Ke.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Ke.prototype.next=function(){this.__values__===F&&(this.__values__=qa(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?F:this.__values__[this.__index__++]}},Ke.prototype.plant=function(e){for(var t,n=this;n instanceof ct;){var o=ma(n);o.__index__=0,o.__values__=F,t?a.__wrapped__=o:t=o;var a=o;n=n.__wrapped__}return a.__wrapped__=e,t},Ke.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof pt){var t=e;return this.__actions__.length&&(t=new pt(this)),(t=t.reverse()).__actions__.push({func:Ia,args:[wa],thisArg:F}),new dt(t,this.__chain__)}return this.thru(wa)},Ke.prototype.toJSON=Ke.prototype.valueOf=Ke.prototype.value=function(){return Kn(this.__wrapped__,this.__actions__)},Ke.prototype.first=Ke.prototype.head,qr&&(Ke.prototype[qr]=function(){return this}),Ke}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define((function(){return yn}))):on?((on.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this);const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":"https://local.yunolearning.com"===this.host()?"https://dev.yunolearning.com":this.host()},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let n="";void 0!==t&&(n="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+n},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+n},studentResultsAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+n},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+n+"/"+o+"/"+a+"/"+r},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+n+"/"+o},classAPi:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classesByViewAPi:function(e,t,n,a,r,i,s,l,u,c){let d="",p="",m="";c&&(d="?ver="+o()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==i&&!1!==i&&(m="/"+i);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+n+"/"+a+"/"+r+m+"/"+s+"/"+l+"/"+u+d},groupsAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+n+"/"+o},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,n,o,a){let r="";!1!==a&&(r="?filter="+a);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+n+"/"+o+r},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+a},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentsList:function(e,t,n,o,a,r,i,s,l,u,c,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+m+"/"+c+"/"+d},paymentsList:function(e,t,n,a,r,i,s,l,u,c){let d="";c&&(d="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let n="",o="";void 0!==t&&!1!==t&&(o="/"+t),void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+o},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/status/toggle"},changeBatchAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+n+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+a},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+n+"/"+o},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,n,o,a,r,i,s,l){let u="";void 0!==i&&!1!==i&&(u="/"+i);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+u+"/"+s+"/"+l},mapCoursesAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+n},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},deleteResourceAttachmentAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+n},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},videoListAPI:function(e,t){let n="",o="";void 0!==e&&(n=e),void 0===o&&!1===o||(o=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,n,o,a){let r="";if(!1!==t)r=t;else{let t="";void 0!==n&&(t=n),r=e+"/"+t+"/"+o+"/"+a}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+r},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+n},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,n,a,r,i,s){let l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==a&&!1!==a&&(d="/"+a),void 0!==r&&!1!==r&&(p="/"+r),void 0!==i&&!1!==i&&(m="/"+i),void 0!==n&&!1!==n&&(c="/"+n),s&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+t+c+d+p+m+u},courseListAPI:function(e,t,n,a){let r="",i="",s="";a&&(r="?ver="+o()),void 0!==t&&!1!==t&&(i="/"+t),void 0!==n&&!1!==n&&(s="/"+n);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+i+s+r},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+n},learnerListAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+n},instructorAvailabilityAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+r+a},createUpdateAvailabilityAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+n},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+n},instructorsByCategoryAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+n},capabilitiesAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+n},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+n},signInURLWithState(e){const t=["email","profile"],n=encodeURI(JSON.stringify(e));let o="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",n);o=e.toString()}else o="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return o},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+a},eventDetailAPI:function(e,t,n,a,r){let i="";r&&(i="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+n+"?uuid="+a+i},profileDetailAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+a},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},resourcesDetailAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o},videoTestimonialAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n},createExamResultAPI:function(e,t,n,o){let a="",r="";void 0===o&&!1===o&&(o="v1"),void 0!==t&&!1!==t&&(a="/"+t),void 0!==n&&!1!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/"+o+"/examresult/"+e+a+r},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,n){let o="",a="";void 0!==t&&(o="/"+t),void 0!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o+a},videotestimonialListAPI:function(e,t,n,o){let a="",r="";void 0!==n&&(a="/"+n),void 0!==o&&(r="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+a+r},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,n,o,a,r,i){let s="",l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(u="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==n&&!1!==n&&(c="/"+n),void 0!==o&&!1!==o&&(d="/"+o),void 0!==a&&!1!==a&&(p="/"+a),void 0!==r&&!1!==r&&(s="/"+r),void 0!==i&&!1!==i&&(l="/"+i);return this.pickHost()+"/wp-json/yuno/v1/article"+u+m+c+d+p+s+l},webinarSingleAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+a},webinarListingAPI:function(e,t,n,a,r){let i="";r&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+a+i},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+o},managelearningContentAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+n},learningContentAPI:function(e,t,n,o,a){let r="",i="",s="",l="";void 0!==t&&!1!==t&&(r="/"+t),void 0!==n&&!1!==n&&(i="/"+n),void 0!==o&&!1!==o&&(s="/"+o),void 0!==a&&!1!==a&&(l="/"+a);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+r+i+s+l},learnerInsightsAPI:function(e,t,n,a,r,i,s,l,u){let c="";u&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+c},learnerInsightsClassAPI:function(e,t,n,a){let r="",i="";i=void 0!==n&&!1!==n?"v2":"v1",a&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/"+i+"/learner/class/"+e+"/"+t+r},signupFormAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+r+a},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,n,a,r){let i="";r&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+n+"/"+a+i},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,n,o){let a="";!1!==o&&(a="?filters="+encodeURI(JSON.stringify(o)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+n+"/"+a},resourcesResultsAPI:function(e,t,n,o,a,r,i){let s="";!1!==r&&(s="?filters="+encodeURI(JSON.stringify(r))),void 0!==i&&!1!==i||(i="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+i+"/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+s},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+a},instructorInsightsAPI:function(e,t,n,a,r,i,s,l){let u="";l&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+u},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+n},dashboardListAPI:function(e,t,n,o,a){let r="",i="",s="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==t&&!1!==t&&(s="/"+t),void 0!==o&&!1!==o&&(r="/"+o),void 0!==a&&!1!==a&&(i="/"+a);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+s+l+r+i},enrollmentDashboardAPI:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+n+"/"+o+"/"+a},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+n},publishedBlogsAPI:function(e,t,n,o,a,r){let i="",s="",l="",u="",c="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(l="/"+t),void 0!==n&&!1!==n&&(u="/"+n),void 0!==o&&!1!==o&&(c="/"+o),void 0!==a&&!1!==a&&(i="/"+a),void 0!==r&&!1!==r&&(s="/"+r);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+u+c+i+s},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,n,o,a,r){r=void 0!==r&&!1!==r?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"";return this.pickHost()+"/wp-json/yuno/v1/"+r+"/"+e+t+n+o+a},instructorMyCourses:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+n},instructorCourseBatches:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+n+"/batches/"+o+"/"+a+"/"+r},manageBookmarkAPI:function(e,t,n,o,a,r){void 0!==t&&!1!==t||(t="v1"),n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"",r=void 0!==r&&!1!==r?"?filters="+encodeURI(JSON.stringify(r)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+n+o+a+r},availableCourses:function(e,t,n,o){t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+n+o},availableBatches:function(e,t,n,o){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+n},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+n+"/"+o},pastBatchesList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,n,o,a,r,i,s,l,u,c,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+n+"/"+o+"/"+a},instructorVideotestimonial:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+n},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+n},classReviews:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classReviewsByInstructor:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a},listOfUser:function(e,t,n){let a="";n&&(a="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+a},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,n,o,a,r,i,s,l,u,c){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n},courseEconomicsSummary:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n+"/"+o},csvList:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+n+"/"+o+"/"+a},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,n,o){void 0===o&&(o="v1");return this.pickHost()+"/wp-json/yuno/"+o+"/referrer/user/"+e+"/"+t+"/"+n},referralReports:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+n+"/"+o},orgToken:function(e,t){let n="";return n="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,n},webhooks:function(e,t,n,o,a,r){let i="";return"grid"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+n+"/"+o+"/"+a+"/"+r:"create"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),i},seo:function(e,t,n,o){let a="";return"status"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+o),a},quiz:function(e,t,n,o){let a="";return void 0!==o&&!1!==o||(o=""),"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+o:"quizgrid"===e?a=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?a=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+n:"delete"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),a},question:function(e,t,n,o,a){let r="";return"questions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+n:"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?r=this.pickHost()+"/wp-json/yuno/v1/question/"+o:"deleteQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"attempt"===e?r=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+o:"attemptQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+a:"questionset"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"questionsetQuestions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+a:"questionsetQuestionsList"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+a:"questionsetQuestionsReorder"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),r},enrollments:function(e,t,n,o){let a="";return"active"===e?a=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+n+"/learners":"extendDate"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),a},blog:function(e,t,n,o){let a="";return"recentSingle"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+o+"?is_list=true&category_id="+t:"categoriesList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),a},writingTask:function(e,t,n,o,a,r,i,s){let l="";return"type"===e?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+n:"create"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o+"/"+a+"/"+r+"/"+i+"/"+s:"payload"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o:"delete"===e&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+o),l},categoryLandingPage:function(e,t){let n="";return"category"===e?n=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+n+"/"+o},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,n,o,a,r,i,s,l,u,c,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${o}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${a}/${r}/${i}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${a}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${s}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${t}/${o}/${a}/${u}/${c}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${n}/${o}/${a}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,n){let o="";return"steps"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+n:"postStep"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+n:"updateMobile"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),o},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,n,o,a,r){let i="";switch(e){case"insights":i=`/wp-json/yuno/v2/users/${t}/learner/${n}/${o}/${a}`;break;case"demoRequests":i=`/wp-json/yuno/v1/demo-requests/${t}/list/${o}/${a}`;break;case"demoRequestsOrg":i=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${n}/${o}/${a}`;break;case"learnerDetailOrg":i=`/wp-json/yuno/v1/demo-requests/${t}/${r}`;break;case"learnerDetail":i=`/wp-json/yuno/v1/demo-requests/${r}`;break;case"instructorLearnerDetail":i=`/wp-json/yuno/v2/instructor/mylearner/${r}`;break;case"orgAdminLearners":i=`/wp-json/yuno/v2/orgadmin/learner/${n}/${o}/${a}`}return this.pickHost()+i},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,n,o,a){let r="";switch(e){case"googleFonts":r=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":r=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":r=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":r=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${n}/${o}`;break;case"orgBatches":r=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${a}`;break;case"org":r=`${this.pickHost()}/wp-json/yuno/v2/org/${o}`;break;case"categories":r=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return r},course:function(e,t,n,o,a,r){let i="";switch(e){case"payload":i=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":i=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${n}/${o}/${a}/${r}`;break;case"createBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return i},learner:function(e,t,n,o,a){let r="";switch(e){case"enrolledCourses":r=`/wp-json/yuno/v3/learner/${t}/enrollments/${n}`;break;case"classes":r=`/wp-json/yuno/v4/classes/${n}/${o}/${t}?limit=${a.limit}&offset=${a.offset}&course=${a.course}&batch=${a.batch}&academy=${a.academy}`;break;case"filters":r=`/wp-json/yuno/v4/classes/filter/${o}/${t}`}return this.pickHost()+r},classes:function(e,t,n,o,a,r,i,s,l,u,c){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${n}/${o}/${a}/${r}/${i}/${s}/${l}/${u}/${c}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,fetchMyLearners:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivateClass:"/wp-json/yuno/v4/classes/private",updatePrivateClass:`/wp-json/yuno/v4/classes/private/${t.classID}`,getClassDetail:`/wp-json/yuno/v4/classes/${t.classID}`,createAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/create`,updateAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/update`,getInstructorAvailability:`/wp-json/yuno/v4/working_hours/instructor/${t.id}`}[e]||"")},attendance:function(e,t,n,o,a){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${n}/${o}/${a}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots"}[e]||"")},classInsights:function(e,t,n,o,a){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${n}/${o}/${a}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${n}/${o}/${a}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",activeOrg:"/wp-json/yuno/v3/user/state",getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${t.id}/0/0/0/active/all/0/0/all/all/0/list-view/100/0`,addDemoInstructor:"/wp-json/yuno/v3/academy/demo-instructors/add",getDemoInstructor:`/wp-json/yuno/v3/academy/demo-instructors/${t.id}/${t.org_id}/${t.academy_id}`,updateDemoInstructor:"/wp-json/yuno/v3/academy/demo-instructors/edit"}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`,detail:`/place/details/json?place_id=${t.placeID}&key=${t.key}`}[e]||"")},createPlace:function(e,t){return this.pickHost()+({create:"/wp-json/yuno/v4/places",createClassroom:"/wp-json/yuno/v4/classrooms"}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules"}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")}},n=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){e(!0)},n.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),o=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,n)=>e.find((e=>e[t]===n))||null,heightOfEle:function(e,t){let n=e.offsetHeight;if(t){let t=getComputedStyle(e);return n+=parseInt(t.marginTop)+parseInt(t.marginBottom),n}return n},assignVValidationObj:function(e){const t=window.VeeValidate,n=window.VeeValidateRules,o=t.ValidationProvider,a=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let o in e.messages)t.extend(o,n[o]);t.localize("validationMsg",e),Vue.component("ValidationProvider",o),Vue.component("ValidationObserver",a)},removeObjInArr:function(e,t,n){let o=e.length;for(;o--;)e[o]&&e[o].hasOwnProperty(t)&&arguments.length>2&&e[o][t]===n&&e.splice(o,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return o[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){n.then((function(t){e(t)}))},setCookie:function(e,t,n){let o=new Date;void 0===n&&(n=30),o.setTime(o.getTime()+24*n*60*60*1e3);let a="expires="+o.toGMTString();document.cookie=e+"="+t+";"+a+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:o,removeValInArr:function(e){let t,n,o=arguments,a=o.length;for(;a>1&&e.length;)for(t=o[--a];-1!==(n=e.indexOf(t));)e.splice(n,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,n){let o=e.match(t);return null!=o&&(!0===n?o[1].replace(/\/$/,""):o[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,n){let o=window.pageYOffset,a=(r=e,window.pageYOffset+document.querySelector(r).getBoundingClientRect().top);var r;targetY=document.body.scrollHeight-a<window.innerHeight?document.body.scrollHeight-window.innerHeight:a,customHeight=void 0!==n?n:74,diff=targetY-o-customHeight;let i="";diff&&window.requestAnimationFrame((function e(n){i||(i=n);let a=n-i,r=Math.min(a/t,1);var s;r=(s=r)<.5?4*s*s*s:(s-1)*(2*s-2)*(2*s-2)+1,window.scrollTo(0,o+diff*r),a<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var n=e.substring(1).split("&"),o=0;o<n.length;o++){var a=n[o].split("=");if(a[0]==t)return a[1]}return!1},cleanTextAndTruncate:function(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n}}}(jQuery),YUNOStore=function(e){const t=function(e,t,n,o,a){if(o){if(void 0===t.addToModule||t.addToModule)if(void 0!==a&&a){for(let t=0;t<n.length;t++)e.data.push(n[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=n}else t.moduleTabs?(e.error=n,e.loading=!1):e.error=n;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[]},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{class_title:"",class_date_time:"",class_duration:"",instructor_id:parseInt(isLoggedIn,10),category_id:0,academy_id:0,learners:[],batch_id:0,course_id:0}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,n){if(n.isError){let o=n.response.response,a=!(void 0===n.pushData||!n.pushData),r=e[n.store],i="";i=void 0!==o&&void 0!==o.data&&void 0!==o.data.message?o.data.message:YUNOCommon.config.errorMsg.common,console.log(i),console.log(n.store),n.tabs?(t(r.tabs[n.tabIndex],n,o,!1,a),r.tabs[n.tabIndex].error=!0,r.tabs[n.tabIndex].errorData=i,n.callback&&n.callbackFunc(r.tabs[n.tabIndex].errorData)):(t(r,n,o,!1,a),r.error=!0,r.errorData=i,n.callback&&n.callbackFunc(r.errorData))}else{let o=n.response.data.data,a=!(void 0===n.pushData||!n.pushData),r=n.response.data,i=e[n.store];n.tabs?(204===r.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),t(i.tabs[n.tabIndex],n,o,!0,a)):(204===r.code&&(i.error=!0,i.errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(i.error=!0,i.errorData=r.message,console.log(r.message),console.log(n.store)),t(i,n,o,!0,a)),n.callback&&n.callbackFunc(n)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,n){if(n.isError){let o=n.response;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!1),n.callback&&n.callbackFunc(e[n.module].errorData)}else{let o=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let o=n.response.data.data;n.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[n.module],n,o,!0),n.isTabAdded=!0,setTimeout((()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let a=n.response.data.data;for(var o=0;o<a.length;o++)a[o].username_email_phone=`${a[o].username_email} (${a[o].phone})`;t(e[n.module],n,a,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let n=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",t.classID),0===n.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let n=e[t.parentModule].data[t.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,n){if(n.isError){let o=n.response;"enrolled"===n.nestedTab?(e[n.module].tabs[n.tabIndex].loading=!1,e[n.module].tabs[n.tabIndex].pageLoading=!1,e[n.module].tabs[n.tabIndex].error=o):t(e[n.module],n,o,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let n=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let o=0;o<n.length;o++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(n[o]);let o=e[t.module].data[t.courseIndex].tabs[t.tabIndex];o.count=t.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount,o.isLoadMore=!1}else{for(let e=0;e<n.length;e++)n[e].isBatches=!1,n[e].isLoading=!1,n[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=n}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let o=t.response.data.data;if(void 0!==t.nested){let a=o.columns,r=o.rows,i={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&a.push(i);for(var n=0;n<r.length;n++)r[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+r[n].id};e[t.nested].tabs[0].data=o,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=o.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const n=function(n){n?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?n(!1):n(!0)},instructorLearners(e,t){const n=function(n){if(n&&void 0!==t.response.data){let n=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=n,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let o={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};e[t.module].tabs[t.index].data.unshift(o),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let n=e.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const a=e[t.module].tabs[t.index];a.hasData=!0,a.isLoadMore=!1;for(var o=0;o<n.length;o++)n[o].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[o].group_created_time)[0]),n[o].scheduleClassURL=`/class-schedule/?groupID=${n[o].group_id}`,a.data.push(n[o]);a.count=t.response.data.count,a.currentCount=a.data.length,a.offset=a.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?n(!1):(n(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)}))},postThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):console.log("not defined")},fetchData({commit:e,state:t},n){let o="",a="";a="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},o=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0===n.moduleLoading||n.moduleLoading?o.loading=!0:o.loading=!1,n.moduleTabs&&n.isTabLoader&&(o.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:a}).then((o=>{n.response=o,n.isError=!1,e(n.module,n),403===(n.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,t[n.store].loading=!1,t[n.store].success=!0;let a="";a=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),403===o.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),n.response=o,n.isError=!0,e(n.module,n)}))},putData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPutData({commit:e,state:t},n){let o={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))},postData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPostData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},deleteData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:o,data:n.payload}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery);Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid">\n                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}});const YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}});Vue.component("yuno-choose-account-type",{template:'\n        <b-modal \n            :active.sync="chooseAccountTypeModal.modal" \n            :width="920" \n            :can-cancel="[\'escape\', \'x\']"\n            @close="close" \n            class="yunoModal lightTheme chooseAccountType">\n                <template v-if="chooseAccountTypeModal.modal">\n                    <div class="modalBody">\n                        <h2 class="h1">{{ details.title }}</h2>\n                        <p class="caption1">{{ details.subtitle }}</p>\n                        <div class="accountTypes">\n                            <div v-for="(type, index) in details.accountTypes" :key="index" class="accountType">\n                                <div class="innerWrapper">\n                                    <h3 class="h2">{{ type.title }}</h3>\n                                    <ul>\n                                        <li v-for="(feature, index) in type.features" :key="index">\n                                            <span class="material-icons" :class="[feature.isAllowed ? \'isGreen\' : \'isRed\']">\n                                                {{ feature.isAllowed ? \'done\' : \'close\' }}\n                                            </span>\n                                            <p>{{ feature.label }}</p>\n                                        </li>\n                                    </ul>\n                                </div>\n                                <div class="ctaWrapper">\n                                    <template v-if="type.cta.disabled">\n                                        <b-tooltip label="Coming Soon!"\n                                            type="is-dark"\n                                            position="is-top">\n                                            <b-button\n                                                tag="a"\n                                                href="/login/?type=signup"\n                                                disabled="type.cta.disabled"\n                                                @click="initLoginPage(type.cta)"\n                                                class="yunoSecondaryCTA button fat"\n                                            >\n                                                {{ type.cta.label }}\n                                            </b-button>\n                                        </b-tooltip>\n                                    </template>\n                                    <template v-else>\n                                        <b-button\n                                            tag="a"\n                                            :href="type.cta.url"\n                                            :target="type.cta.target"\n                                            :disabled="type.cta.disabled"\n                                            @click="initLoginPage(type.cta)"\n                                            class="yunoSecondaryCTA button fat"\n                                        >\n                                            {{ type.cta.label }}\n                                        </b-button>\n                                    </template>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </template>\n        </b-modal>\n    ',data:()=>({details:{title:"Choose the type of account",subtitle:"You can sign up as an individual instructor or an academy owner. See the difference below.",accountTypes:[{title:"Academy Owners",features:[{label:"Can create & share study material and practice tests",isAllowed:!0},{label:"Can publish courses and batches",isAllowed:!0},{label:"Can invite a team of instructors to teach",isAllowed:!0},{label:"Can collect reviews and ratings on their academy profile",isAllowed:!0},{label:"Can promote courses, enroll learners & collect payments",isAllowed:!0}],cta:{label:"Learn More",action:"academyOwner",url:"/create-academy/",target:"_self",disabled:!1}},{title:"Independent Instructors",features:[{label:"Can create & share study material and practice tests",isAllowed:!0},{label:"Cannot publish courses or create batches",isAllowed:!1},{label:"Can schedule classes and teach online, but as an instructor of an academy",isAllowed:!0},{label:"Can collect reviews and ratings on their instructor profile",isAllowed:!0},{label:"Since they cannot publish courses, they can't enroll learners & collect payments",isAllowed:!1}],cta:{label:"Learn More",action:"instructor",url:"/become-an-instructor/",target:"_self",disabled:!1}}]}}),computed:{...Vuex.mapState(["user","chooseAccountTypeModal"])},async created(){},mounted(){},methods:{close(){sessionStorage.removeItem("landingPage")},initLoginPage(e){if(!this.user.isLoggedin){const t={url:"",pageTitle:"",category:"",role:{academyOwner:"org-admin",instructor:"instructor"}[e.action]||""};sessionStorage.setItem("landingPage",JSON.stringify(t))}}}});const YUNOHeader=(jQuery,{header:function(){Vue.component("yuno-header",{props:["isnav","scrollenabled","logoAlignment","postsignup","hassearchbar","options"],template:' \n                <div>\n                    <header id="yunoHeader" class="yunoHeader" :class="{\'noNav\': isnav === false, \'scrollEnabled\': scrollenabled, \'logoCenter\': logoAlignment}">\n                        <div class="container-fluid noOverflow">\n                            <nav class="navbar navbar-expand-lg" :class="[hassearchbar !== undefined && hassearchbar.isActive ? \'hasSearchBar\' : \'\',]">\n                                <figure class="logo navbar-brand">\n                                    <a :href="isnav !== false ? getHomeURL : \'#\'"><img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                </figure>\n                                \n                                <button v-if="isnav !== false" class="navbar-toggler" type="button" aria-controls="navbarSupportedContent" :aria-expanded="enabledSubmenu" aria-label="Toggle navigation" @click="toggleMenu">\n                                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                </button>\n                                <div v-if="isnav !== undefined && !isnav" class="yunoLogin">\n                                    <div v-if="false" class="yunoCallUs noSpacer" :class="{\'preLogin\': getUserStatus !== true}">\n                                        <a class="whatsapp" href="https://api.whatsapp.com/send?phone=+918847251466" target="_blank"><i class="fa fa-whatsapp" aria-hidden="true"></i></a>\n                                    </div>        \n                                </div>\n                                <div v-if="isnav !== false" class="collapse navbar-collapse yunoMainNav" id="yunoMainNav" :class="[!getUserStatus || getUserStatus && userRole.data === \'Learner\' ? \'hasSearch\' : \'\', userRole.data === \'yuno-admin\' ? \'hasAdmin\' : \'\', enabledSubmenu ? \'show\' : \'collapsed\']">\n                                    <ul class="navbar-nav align-items-center w-100">\n                                        <template v-if="headerMenu.loading" v-for="(menu, menuIndex) in 3">\n                                            <li :style="{ marginLeft: \'30px\' }">\n                                                <b-skeleton width="100px" height="32px" active></b-skeleton>\n                                            </li>\n                                        </template>\n                                        <template v-if="headerMenu.success" v-for="(menu, menuIndex) in headerMenu.data">\n                                            <li class="hasCTA" v-if="detectPlatform() === \'android\'">\n                                                <b-button tag="a"\n                                                    href="https://play.google.com/store/apps/details?id=com.yunolearning.learn"\n                                                    target="_blank"\n                                                    class="yunoPrimaryCTA small">\n                                                    Get the app\n                                                </b-button>\n                                            </li>\n                                            <template v-if="menu.type === \'menu_item\'">\n                                                <li \n                                                    class="nav-item" \n                                                    :class="{\'active\': menu.is_active === true, \'dropdown\': menu.items.length, \'isCTA\': menu.section === \'Invite\'}" \n                                                    :key="menuIndex">\n                                                    <template v-if="menu.items.length">\n                                                        <a \n                                                            :id="\'submenu\' + menuIndex"\n                                                            :class="{\'dropdownToggle\': menu.items.length}" \n                                                            @click="manageSubmenu($event, menu, \'submenu\' + menuIndex)"\n                                                            :ref="\'submenu\' + menuIndex"\n                                                            :href="menu.url">\n                                                            {{menu.section}}\n                                                        </a>\n                                                    </template>\n                                                    <a \n                                                        v-else\n                                                        :class="{\'dropdown-toggle\': menu.items.length}" \n                                                        @click="manageCustomLink($event, menu)"\n                                                        :href="menu.url">\n                                                            {{menu.section}}\n                                                    </a>\n                                                </li>\n                                            </template>\n                                            <template v-if="menu.type === \'search_item\'">\n                                                <li class="hasSearchBar">\n                                                    <div class="searchBarWrapper">\n                                                        <validation-observer \n                                                            tag="div" \n                                                            ref="searchObserver" \n                                                            v-slot="{ handleSubmit, invalid }">\n                                                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                                                <b-field class="searchFieldWrapper">\n                                                                    <validation-provider \n                                                                        tag="div"\n                                                                        class="searchField"\n                                                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                                                        v-slot="{ errors, classes }">\n                                                                        <b-autocomplete\n                                                                            :class="classes"\n                                                                            v-model="categories.current"\n                                                                            :data="categories.data"\n                                                                            autocomplete="courseSearch"\n                                                                            :loading="categories.isLoading"\n                                                                            placeholder="Search..."\n                                                                            @typing="searchOnTyping"\n                                                                            @select="onSelect($event)"\n                                                                            :clearable="true"\n                                                                        >\n                                                                            <template slot-scope="props">\n                                                                                <template v-if="props.option.course_url">\n                                                                                    <div class="suggestion courseBlock">\n                                                                                        <figure>\n                                                                                            <div class="imageWrapper" v-if="false">\n                                                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                                                            </div>\n                                                                                            <figcaption>\n                                                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                                                <p class="courseDetail">\n                                                                                                    <span class="caption">Course</span>\n                                                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                                                </p>\n                                                                                            </figcaption>\n                                                                                        </figure>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                            </template>\n                                                                        </b-autocomplete>\n                                                                    </validation-provider>\n                                                                    <div class="ctaWrapper">\n                                                                        <b-button\n                                                                            native-type="submit"\n                                                                            class="doSearch">\n                                                                            <span class="material-icons-outlined">search</span>\n                                                                        </b-button>  \n                                                                    </div>\n                                                                </b-field>\n                                                            </form>\n                                                        </validation-observer>\n                                                    </div>\n                                                </li> \n                                            </template>\n                                        </template>\n                                    </ul>\n                                </div>\n                                <template v-if="isnav !== false">\n                                    <div class="yunoLogin" v-if="!getUserStatus">\n                                        <a \n                                            class="marginRight15"\n                                            @click="trackLoginLandingPage()"\n                                            href="/login/">\n                                            <span class="yuno-login-with-google-on-pages grey wired">Log in</span>\n                                        </a>\n                                        <a @click="trackLoginLandingPage()" href="/login/?type=signup">\n                                            <span class="yuno-login-with-google-on-pages">Sign up</span>\n                                        </a>\n                                        <div class="yunoCallUs" v-if="false" :class="{\'preLogin\': getUserStatus !== true}">\n                                            <template v-if="false">\n                                                <span class="caption">Call us at</span>\n                                                <a href="tel:+91-8847251466"><span class="material-icons">call</span> <span class="value">+91-8847251466</span></a>\n                                            </template>\n                                            <a href="#" @click.prevent="bookADemo()">Book a demo class</a>\n                                        </div>        \n                                    </div>\n                                </template>\n                                <template v-if="isnav !== false || hassearchbar !== undefined && hassearchbar.isActive">\n                                    <template v-if="getUserStatus">\n                                        <div class="dropdown yunoLoginDropdown" v-if="getUserProfile.success">\n                                            <a class="dropdown-toggle" href="#" role="button" id="userProfile" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                                                <figure class="profilePic">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.first_name">\n                                                </figure>\n                                            </a>\n                                            <div class="dropdown-menu" aria-labelledby="userProfile">\n                                                <figure class="menuHeader">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.yuno_display_name">\n                                                    <figcaption>\n                                                        <p class="userName">{{ getUserProfile.data.yuno_display_name }}</p>\n                                                        <p class="userEmail">{{ getUserProfile.data.email }}</p>\n                                                    </figcaption>\n                                                </figure>\n                                                <ul class="userlinks">\n                                                    <li \n                                                        v-for="(item, l) in usermenu"\n                                                        :key="l"\n                                                        v-if="item.isActive"\n                                                        :class="[item.slug === \'switchAccount\' ? \'linksFooter\' : \'\']">\n                                                        <a \n                                                            v-if="item.callbackFunc !== false" \n                                                            class="dropdown-item" \n                                                            @click="item.callbackFunc" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                        <a \n                                                            v-else \n                                                            class="dropdown-item" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                    </li>\n                                                </ul>\n                                                <div class="additionalItems" v-if="isItemAvailable([\'Instructor\'])">\n                                                    <template v-if="referralCode.loading">\n                                                        <div class="loaderWrapper">\n                                                            <div class="smallLoader"></div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-if="referralCode.success">\n                                                        <template v-if="referralCode.error === null">\n                                                            <div class="item">\n                                                                <b-field label="Referral code">\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="material-icons-outlined">content_copy</span>\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </template>\n                                </template>\n                            </nav>\n                        </div>\n                        <b-modal \n                            :active.sync="invite.modal" \n                            :width="500" \n                            :can-cancel="[\'escape\', \'x\']"\n                            class="yunoModal inviteModal">\n                                <div class="modalHeader">\n                                    <h2 class="modalTitle">Invite Link</h2>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="wrapper">\n                                        <template v-if="inviteLink.loading">\n                                            <div class="loaderWrapper">\n                                                <div class="smallLoader"></div>\n                                            </div>\n                                        </template>\n                                        <template v-if="inviteLink.success">\n                                            <template v-if="inviteLink.error === null">\n                                                <ul class="classFields">\n                                                    <li>\n                                                        <div class="clipboard">\n                                                            <b-input id="inviteLink" :value="inviteLink.data.invitation_link" readonly></b-input>\n                                                            <i @click="copyToClipboard(\'inviteLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n                                                        </div>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else>\n                                                <p>{{inviteLink.error}}</p>\n                                            </template>\n                                        </template>\n                                    </div>\n                                </div>\n                        </b-modal>\n                    </header>\n                    <div class="yunoSubmenu" \n                        :class="[submenu.isActive ? \'active\' : \'inActive\']"\n                        v-if="submenu.isActive && scrollenabled === undefined || submenu.isActive && scrollenabled !== undefined && !scrollenabled" \n                        v-closable="{exclude: [submenu.ref], handler: \'onClose\'}">\n                        <div class="container">\n                            <a href="#" class="closeSubmenu" @click.prevent="closeSubmenu()"><span class="material-icons">arrow_back_ios</span></a>\n                            <ul class="submenuList" :class="[submenu.data.items.length < 3 ? \'col2\' : \'\']">\n                                <li v-for="(nav, i) in submenu.data.items" :key="i">\n                                    <a :href="nav.url">\n                                        <figure>\n                                            <div class="imgWrapper"><img :src="nav.image" :alt="nav.label"></div>\n                                            <figcaption>\n                                                <p class="navLabel">{{ nav.label }}</p>\n                                                <p class="navDes">{{nav.excerpt}}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </a>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    <yuno-choose-account-type>\n                    </yuno-choose-account-type>\n                    <b-modal \n                        :active.sync="bookademo.modal" \n                        :width="388" \n                        :can-cancel="[\'escape\', \'x\']"\n                        :on-cancel="bookademoModalClose"\n                        class="yunoModal lightTheme">\n                            <template v-if="bookademo.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">\n                                        {{ bookademo.title }}\n                                    </h3>\n                                </div>\n                                <div class="modalBody">\n                                    <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observerWrapper"\n                                        ref="bookademoobserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form @submit.prevent="handleSubmit(initBookADemo)">\n                                            <validation-provider \n                                                tag="div" \n                                                class="categoryWithImage" \n                                                :rules="{required:true}" \n                                                v-slot="{ errors, classes }"\n                                            >\n                                                <template v-for="(option, i) in bookademo.data">\n                                                    <div class="fieldWrapper">\n                                                        <div class="inner">\n                                                            <b-field :key="i" :style="categoryImg(option)">\n                                                                <b-radio-button \n                                                                    :class="classes"\n                                                                    v-model="bookademo.selected"\n                                                                    @input="initBookADemo()"\n                                                                    name="bookademo"\n                                                                    :native-value="option">\n                                                                    {{option.label}}\n                                                                </b-radio-button>\n                                                            </b-field>\n                                                            <div class="catLabel">{{option.label}}</div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>\n                                            <div class="ctaWrapper alignLeft" v-if="false">\n                                                <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>\n                                            </div>\n                                        </form>\n                                    </validation-observer>\n                                </div>\n                            </template>\n                    </b-modal>\n                \n                </div>\n            ',data(){return{enabledSubmenu:!1,bookademo:{modal:!1,title:"Book a Demo Class",subtitle:"Choose Subject Category",selected:"",data:[]},showSubmenu:!1,invite:{modal:!1},submenu:{isActive:!1,data:[],ref:""},usermenu:[{label:"Home",slug:"learnerDashboard",icon:"home",iconType:"material-icons-outlined",url:"/learner/",isActive:!1,callbackFunc:!1},{label:"Bookmarks",slug:"myBookmarks",icon:"bookmark_border",iconType:"material-icons-outlined",url:"/my-bookmarks/",isActive:!1,callbackFunc:!1},{label:"Enrolled Courses",slug:"myEnrollments",icon:"local_library",iconType:"material-icons-outlined",url:"/learner-courses/",isActive:!1,callbackFunc:!1},{label:"Subscribed Categories",slug:"categories",icon:"category",iconType:"material-icons-outlined",url:"/subscribed-categories",isActive:!1,callbackFunc:!1},{label:"My Profile",slug:"myProfile",icon:"account_circle",iconType:"material-icons-outlined",url:"",isActive:!1,callbackFunc:!1},{label:"Settings",slug:"settings",icon:"settings",iconType:"material-icons-outlined",url:"/settings/",isActive:!0,callbackFunc:!1},{label:"Switch account",slug:"switchAccount",icon:"swap_horiz",iconType:"material-icons-outlined",url:this.$store.getters.getSwitchAccountURL,isActive:!0,callbackFunc:()=>this.chooseAccountState()},{label:"Logout",slug:"logout",icon:"logout",iconType:"material-icons-outlined",url:"/logout/",isActive:!0,callbackFunc:!1}],errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}},computed:{...Vuex.mapState(["loginWithGoogle","user","userRole","inviteLink","module","referralCode","searchSuggestions","chooseAccountTypeModal"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},getSignInURL(){return this.$store.getters.getSignInURL},switchAccountURL(){return this.$store.getters.getSwitchAccountURL},headerMenu:{get(){return this.$store.state.header}},getUserStatus:{get:()=>0!==Number(isLoggedIn)},getUserProfile:{get(){return this.$store.state.userProfile}}},async created(){this.emitEvents()},mounted(){this.logoGoogleSchema(),this.getUserStatus?this.getState():(["userState","oldUserState","paymentState","demoClassState","skipSignUp","userSignUpPage","userSignUp","noSignupRequired","isWelcomePage","isReferrer","isQuiz"].forEach((e=>localStorage.removeItem(e))),sessionStorage.removeItem("activeUserV1"))},methods:{toggleMenu(){this.enabledSubmenu?this.enabledSubmenu=!1:this.enabledSubmenu=!0},manageCustomLink(e,t){"Teach on Yuno"===t.section&&(e.preventDefault(),this.chooseAccountTypeModal.modal=!0)},isObjectBlank:e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&0===Object.keys(e).length,getQueryParams(e){const t=new URLSearchParams(e.split("?")[1]);let n={};for(const[e,o]of t)n[e]=o;return n},trackLoginLandingPage(){if(!this.user.isLoggedin){let e="",t="";e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",t=this.isObjectBlank(this.getQueryParams(window.location.origin+window.location.pathname+window.location.search))?null:this.getQueryParams(window.location.origin+window.location.pathname+window.location.search);const n={url:window.location.origin+window.location.pathname,pageTitle:document.title,category:e,urlParams:t,zohoMeta:this.$props.options.zohoMeta};sessionStorage.setItem("landingPage",JSON.stringify(n))}},categoryImg:e=>({"background-image":`url(${e.image.replace(/ /g,"%20")})`}),bookademoModalClose(){this.bookademo.selected="",this.bookademo.data=[]},initBookADemo(){window.location.href=this.bookademo.selected.url},bookADemo(){this.bookademo.modal=!0;let e=JSON.parse(JSON.stringify(YUNOCommon.findObjectByKey(this.headerMenu.data,"label","Explore").submenu));e.forEach((e=>{e.slug=e.label.toLowerCase(),"ielts"===e.slug?e.url="/for-ads/ielts/ielts-for-all-v3/":"english speaking"===e.slug?e.url="/for-ads/english-speaking/english-for-all-v3/":"pte"===e.slug?e.url="/for-ads/pte/pte-for-all-v3/":"duolingo"===e.slug?e.url="/for-ads/duolingo-for-all/":"toefl"===e.slug?e.url="/for-ads/toefl-for-all/":"french"===e.slug&&(e.url="/for-ads/french/french-for-all-v3/")})),this.bookademo.data=e},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},initForm(){const e=this.categories.selected,t={filter:"category",id:e.category_id,label:e.category,parent_id:0,slug:e.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",t,n)},onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:o}=e.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(e){e.length>2?(this.categories.isLoading=!0,this.categories.data=[],this.fetchCourseSuggestions(e)):this.categories.data=[]}),700),gotReferralCode(e){void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code&&e.response.data.data},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},emitEvents(){Event.$on("updateHeaderSearch",(()=>{})),Event.$on("checkLoggedInState",(e=>{e&&Event.$on("gotUserRole",((e,t)=>{"Learner"===e&&(this.$store.state.homeURL="/learner"),"Instructor"===e&&this.fetchReferralCode()}))}))},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},logoGoogleSchema(){let e={"@context":"https://schema.org","@type":"Organization",url:"https://www.yunolearning.com/",logo:YUNOCommon.config.host()+"/wp-content/themes/yunolearning-child/assets/images/yuno.jpeg"};this.structuredData(e)},manageLogin(e,t){e.preventDefault(),Event.$emit("manageLogin",e,t)},detectPlatform:()=>/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":!!/Android/i.test(window.navigator.userAgent)&&"android",gotInviteLink(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code)this.inviteLink.data=e.response.data.data;else{const t=void 0!==e.response.data&&void 0!==e.response.data.message?e.response.data.message:YUNOCommon.config.errorMsg.common;this.inviteLink.error=t}},fetchInviteLink(){const e=this,t={apiURL:YUNOCommon.config.getInviteURLAPI(isLoggedIn),module:"gotData",store:"inviteLink",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotInviteLink(t)}};this.$store.dispatch("fetchData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},initInviteModal(e){e.preventDefault(),this.invite.modal=!0,0===this.inviteLink.data.length&&this.fetchInviteLink()},whichPage:()=>window.location.href.indexOf("open")>-1?"open":null,submenuClasses(){const e=document.querySelectorAll(".dropdown-menu.level3");for(let t=0;t<e.length;t++)e[t].classList.remove("show")},closeSubmenu(){this.onClose()},onClose(){this.submenu.isActive=!1,this.submenu.data=[],this.submenu.ref=""},manageSubmenu(e,t,n){e.preventDefault(),this.submenu.isActive=!this.submenu.isActive,this.submenu.data=t,this.submenu.ref=n},manageSubmenuLevel3(e){let t=document.querySelectorAll(".dropdown-menu."+e)[0];this.submenuClasses(),t.classList.toggle("show")},isPageHasCategory(){"undefined"!=typeof assignedCategory&&localStorage.setItem("userSignUpPage",assignedCategory)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},getState(){if(null!==localStorage.getItem("isChooseAccountState"))return["userState","isChooseAccountState"].forEach((e=>localStorage.removeItem(e))),!1}}})}}),YUNOHeaderRevamp=(jQuery,{headerRevamp:function(){let e;Vue.directive("closable",{bind(t,n,o){e=e=>{e.stopPropagation();const{handler:a,exclude:r}=n.value;let i=!1;r.forEach((t=>{if(!i){const n=o.context.$refs[t];i=n[0].contains(e.target)}})),t.contains(e.target)||i||o.context[a]()},document.addEventListener("click",e),document.addEventListener("touchstart",e)},unbind(){document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}}),Vue.component("yuno-header-revamp",{props:["hasnav","hasscrollenabled","haslogoAlignment","postsignup","hassearchbar","fetchAPI","options"],template:'\n            <div class="isSticky">\n                <div>\n                    <section class="appPrompt" v-if="appInfo.platform === \'android\'">\n                        <div class="container">\n                            <ul class="colGrid">\n                                <li class="appIcon">\n                                    <div class="closePrompt">\n                                        <i @click="onPromptClose()" class="fa fa-times" aria-hidden="true"></i>\n                                    </div>\n                                    <figure class="appMedia">\n                                        <div class="iconWrap">\n                                            <img :src="appInfo.icon" :alt="appInfo.shortName">\n                                        </div>\n                                        <figcaption class="appInfo">\n                                            <p class="infoTitle">{{ appInfo.shortName }}</p>\n                                            <p class="infoCaption" v-if="appInfo.hasOS === \'android\'">{{ appInfo.androidCaption }}</p>\n                                        </figcaption>\n                                    </figure>\n                                </li>\n                                <li>\n                                    <b-button tag="a"\n                                        :href="appInfo.hasOS === \'android\' ? appInfo.androidURL : \'\'"\n                                        target="_blank"\n                                        rel="nofollow"\n                                        @click="onPromptView($event)"\n                                        class="yunoPrimaryCTA small">\n                                        Open\n                                    </b-button>\n                                </li>\n                            </ul>\n                        </div>\n                    </section>\n                    <template v-if="userInfo.error">\n                        <header id="yunoHeader" class="yunoHeader">\n                            <div class="container noOverflow">\n                                <nav class="navbar navbar-expand-lg">\n                                    <figure class="logo navbar-brand">\n                                        <a :href="getHomeURL"><img :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                    </figure>\n                                    <button class="navbar-toggler" data-toggle="collapse" data-target="#yunoMainNav" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">\n                                        <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                    </button>\n                                    <div class="collapse navbar-collapse yunoMainNav" id="yunoMainNav">\n                                        <ul class="navbar-nav justify-content-end align-items-center w-100">\n                                            <li class="nav-item">\n                                                <a \n                                                    href="/logout">\n                                                    Logout\n                                                </a>\n                                            </li>\n                                        </ul>\n                                    </div>\n                                </nav>\n                            </div>\n                        </header>\n                        <main id="yunoMain">\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </main>\n                    </template>\n                    <template v-else>\n                        <div class="notificationBar" :class="{ notificationHide: notification.isHidden, notificationShow: !notification.isHidden, notVisibleInDOM: !notification.isVisible }">\n                            <a :href="notification.url" target="_blank">\n                                {{ notification.label }}\n                            </a>\n                            <span class="material-icons-outlined" @click="hideNotificationBar">close</span>\n                        </div>\n                        <yuno-header \n                            :isnav="hasnav !== undefined ? hasnav : undefined" \n                            :logoAlignment="haslogoAlignment !== undefined ? haslogoAlignment : undefined" \n                            :scrollenabled="hasscrollenabled !== undefined ? hasscrollenabled : undefined"\n                            :postsignup="postsignup"\n                            :hassearchbar="hassearchbar"\n                            :options="{zohoMeta: options !== undefined ? options.zohoMeta : \'\'}">\n                        </yuno-header>\n                    </template>\n                    <b-modal \n                        :active.sync="config.unauthorizedModal" \n                        :width="450" \n                        :can-cancel="[\'escape\', \'x\']" \n                        :on-cancel="unauthorizedModalClose"\n                        class="yunoModal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">Session Expired</h2>\n                            </div>\n                            <div class="modalBody">\n                                <div class="wrapper">\n                                    <p>{{sessionExpired}}</p>\n                                </div>\n                            </div>\n                            <div class="modalFooter">\n                                <div class="unauthorizedLogin">\n                                    <a \n                                        @click.prevent="setState()"\n                                        href="#">\n                                        <span class="g_icon"></span>\n                                        <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                    </a>\n                                </div>\n                            </div>\n                    </b-modal>\n                </div>\n                <b-modal \n                    :active.sync="loginModal.modal" \n                    :width="360" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="loginModalClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="loginModal.modal">\n                            <div class="modalHeader">\n                                <h3 class="modalTitle">\n                                    <template v-if="loginModal.active === \'login\'">\n                                        Log in\n                                    </template>\n                                    <template v-else>\n                                        Sign up\n                                    </template>\n                                </h3>\n                            </div>\n                            <div class="modalBody">\n                                <template v-if="loginModal.active === \'login\'">\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <div class="ctaWrapper">\n                                        <button class="googleLogin width70" @click="setState">\n                                            <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                        </button>\n                                    </div>\n                                    <p class="footerCaption">\n                                        {{loginModal[loginModal.active].footer.label}} <a @click="updateLoginState($event, \'signup\')" :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                    </p>\n                                </template>\n                                <template v-else>\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observer"\n                                        ref="commonSignupObserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                            <b-field label="Phone number">\n                                                <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                    <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                    <p class="error">{{errors[0]}}</p>\n                                                </validation-provider>    \n                                            </b-field>\n                                            <div class="ctaWrapper">\n                                                <button class="googleLogin" type="submit">\n                                                    <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                                </button>\n                                            </div>\n                                            <p class="helperCaption">\n                                                {{loginModal[loginModal.active].helper.label}} <a @click="updateLoginState($event, \'login\')" :href="loginModal[loginModal.active].helper.actionURL">{{loginModal[loginModal.active].helper.actionLabel}}</a>\n                                            </p>\n                                            <p class="footerCaption" v-if="loginModal[loginModal.active].footer.isActive">\n                                                {{loginModal[loginModal.active].footer.label}} <a :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                            </p>\n                                        </form>\n                                    </validation-observer>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </div>\n            ',data(){return{tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,appInfo:{shortName:"Yuno Learning",name:"Yuno Learning",icon:this.$store.state.themeURL+"/assets/images/yunoLogo.svg",androidURL:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",hasOS:"android",androidCaption:"FREE - In the Google Play Store",platform:"",daysHidden:1,daysReminder:90},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}},loginModal:{modal:!1,active:"",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",login:{title:"Log into your Yuno Learning account",hasMobile:!1,cta:"Login with Google",footer:{label:"Don't have an account yet?",actionLabel:"Sign Up",actionURL:"#"}},signup:{title:"Sign up to create a free account on Yuno Learning",hasMobile:!0,cta:"Sign up with Google",helper:{label:"Already a user?",actionLabel:"Login",actionURL:"#"},footer:{label:"Are you an instructor?",isActive:!0,actionLabel:"Sign up here",actionURL:"/ielts/become-an-instructor"}}},notification:{isHidden:!1,isVisible:!1,label:"Free summer online workshop for kids and teenagers",url:"/free-summer-workshops-for-kids",hideNotificationDate:""}}},computed:{...Vuex.mapState(["user","userRole","userInfo","userProfile","header","footer","config","apiTokenExpiryTime","apiTokenRefresh"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},emptyStates:()=>({state:"dataNotFound"}),getSignInURL(){return this.$store.getters.getSignInURL}},async created(){this.emitEvents()},mounted(){this.loginStatus()},beforeDestroy(){},methods:{hideNotificationBar(){this.notification.isVisible&&(this.notification.isVisible=!1,this.notification.isHidden=!0)},setSigninProps(){localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const{zohoMeta:e={}}=this.$props.options||{},t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general";this.signIn={...this.signIn,categoryURL:`/${t}`,landing_page:{url:window.location.origin+window.location.pathname,title:document.title},productCode:e.productcode||"",leadStatus:e.leadstatus||"",utmSource:YUNOCommon.getQueryParameter("utm_source"),utmCampaign:YUNOCommon.getQueryParameter("utm_campaign"),utmMedium:YUNOCommon.getQueryParameter("utm_medium"),adGroupID:YUNOCommon.getQueryParameter("adgroupid"),adContent:YUNOCommon.getQueryParameter("ad_content"),utmTerm:YUNOCommon.getQueryParameter("utm_term"),gclid:YUNOCommon.getQueryParameter("gclid"),content:{type:e.content_type||"",id:e.content_id||""}}},isFirefoxPrivate(e){null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},emitEvents(){void 0!==Event.$emit&&(Event.$on("manageLogin",((e,t)=>{this.manageLogin(e,t)})),Event.$on("initLoginModal",((e,t,n)=>{this.manageLogin(e,"signup"),this.loginModal[this.loginModal.active].footer.isActive=!1})),Event.$on("initSignupModal",(e=>{this.manageLogin(e,"signup")})))},updateLoginState(e,t){e.preventDefault(),this.loginModal.active=t},initCommonSignup(){this.setState()},loginModalClose(){setTimeout((()=>{this.loginModal.active=""}),300),localStorage.removeItem("paymentState"),localStorage.removeItem("isQuiz")},manageLogin(e,t){this.loginModal.modal=!0,this.loginModal.active=t},onPromptClose(){const e=this.appInfo.daysHidden;YUNOCommon.setCookie("yunoAppPrompt2",!0,e),this.detectPlatform()},onPromptView(e){this.detectPlatform()},detectPlatform(){if(/iPhone|iPad|iPod/i.test(window.navigator.userAgent))this.appInfo.platform="ios";else if(/Android/i.test(window.navigator.userAgent)){const e=YUNOCommon.getCookie("yunoAppPrompt2");this.appInfo.platform=void 0===e&&"android"}else this.appInfo.platform=!1},unauthorizedModalClose(){window.location.href="/logout"},roleSpecificAPI(e,t){"Learner"===e&&this.detectPlatform(),void 0!==Event.$emit&&Event.$emit("gotUserRole",e,t)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):(this.user.isLoggedin=!1,this.detectPlatform(),void 0===this.$props.hasnav||this.$props.hasnav?(this.fetchPreLoginMenu(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin))},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.userRole.data=t.role,this.userRole.success=!0,this.userProfile.data=t,this.userProfile.success=!0,(void 0===this.$props.hasnav||this.$props.hasnav)&&0!==this.header.data.length&&this.fetchPostLoginMenu(t.role),this.$props.fetchAPI&&this.fetchPostLoginMenu(t.role);const n=localStorage.getItem("skipSignUp"),o=localStorage.getItem("paymentState");if("Instructor"===t.role&&"de-active"===t.account_status&&"/account-disabled/"!==window.location.pathname)return window.location.href=YUNOCommon.config.host()+"/account-disabled",!1;"pending"===t.is_signup_completed&&"/sign-up/"!==window.location.pathname&&"/instructor-sign-up/"!==window.location.pathname&&null===n&&null===o&&setTimeout((()=>{"Instructor"===t.role?window.location.href=YUNOCommon.config.host()+"/instructor-sign-up":window.location.href=YUNOCommon.config.host()+"/sign-up"}),50),this.roleSpecificAPI(t.role,t)}else this.userInfo.error=!0,this.header.success=!0,this.footer.success=!0},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",e)},highlightCurrentPage(e){const t=e.response.data.data.map((e=>({...e,isActive:e.url===window.location.href})));this.header.data=t},gotPostLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotUserMenu"))},fetchPostLoginMenu(e){const t={apiURL:YUNOCommon.config.headerMenuAPIV2(isLoggedIn,e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:this.gotPostLoginMenu};this.$store.dispatch("fetchData",t)},gotPreLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotPreLoginMenu"))},fetchPreLoginMenu(){const e={userID:isLoggedIn,orgID:0},t={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",callback:!0,callbackFunc:this.gotPreLoginMenu};this.$store.dispatch("fetchData",t)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <template v-if="footer.loading">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in 4">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <ul class="linkList">\n                                            <template v-for="(item, j) in 4">\n                                                <li \n                                                    :key="j">\n                                                    <b-skeleton active></b-skeleton>\n                                                </li>\n                                            </template>\n                                        </ul>  \n                                    </div>        \n                                </template>\n                            </div>\n                        </template>\n                        <template v-if="footer.success">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in footer.data.primary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="primary.type === \'withIcon\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :class="item.icon"\n                                                        :key="j">\n                                                        <a rel="nofollow" :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'basic\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'withOrderList\'">\n                                            <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        {{item.label}}\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                            <b-button tag="a"\n                                                :href="primary.cta.url"\n                                                target="_blank"\n                                                rel="nofollow noopener"\n                                                class="yunoSecondaryCTA">\n                                                {{ primary.cta.label }}\n                                            </b-button>\n                                        </template>\n                                        <template v-if="primary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <div class="col-12">\n                                    <div class="spacer"></div>\n                                </div>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(secondary, i) in footer.data.secondary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="secondary.type === \'basic\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="secondary.title !== undefined" class="listTitle">\n                                                    <h3>{{ secondary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in secondary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="secondary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                            <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row">\n                                <div class="col-12">\n                                    <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                        <li class="copy" v-if="false">© {{currentYear}} Yunolearning. All rights reserved.</li>\n                                        <li v-if="isLogo === undefined || isLogo">\n                                            <figure class="logo">\n                                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                            </figure>\n                                        </li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </template>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"916239571825"}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},methods:{}})}});var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}},$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.ISOLATE_POLYFILLS=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,n){return e==Array.prototype||e==Object.prototype||(e[t]=n.value),e},$jscomp.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var t=0;t<e.length;++t){var n=e[t];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")},$jscomp.global=$jscomp.getGlobal(this),$jscomp.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE,$jscomp.polyfills={},$jscomp.propertyToPolyfillSymbol={},$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(e,t){var n=$jscomp.propertyToPolyfillSymbol[t];return null==n?e[t]:void 0!==(n=e[n])?n:e[t]};$jscomp.polyfill=function(e,t,n,o){t&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(e,t,n,o):$jscomp.polyfillUnisolated(e,t,n,o))},$jscomp.polyfillUnisolated=function(e,t,n,o){for(n=$jscomp.global,e=e.split("."),o=0;o<e.length-1;o++){var a=e[o];if(!(a in n))return;n=n[a]}(t=t(o=n[e=e[e.length-1]]))!=o&&null!=t&&$jscomp.defineProperty(n,e,{configurable:!0,writable:!0,value:t})},$jscomp.polyfillIsolated=function(e,t,n,o){var a=e.split(".");e=1===a.length,o=a[0],o=!e&&o in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var r=0;r<a.length-1;r++){var i=a[r];if(!(i in o))return;o=o[i]}a=a[a.length-1],null!=(t=t(n=$jscomp.IS_SYMBOL_NATIVE&&"es6"===n?o[a]:null))&&(e?$jscomp.defineProperty($jscomp.polyfills,a,{configurable:!0,writable:!0,value:t}):t!==n&&($jscomp.propertyToPolyfillSymbol[a]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(a):$jscomp.POLYFILL_PREFIX+a,a=$jscomp.propertyToPolyfillSymbol[a],$jscomp.defineProperty(o,a,{configurable:!0,writable:!0,value:t})))},$jscomp.initSymbol=function(){},$jscomp.polyfill("Symbol",(function(e){if(e)return e;var t=function(e,t){this.$jscomp$symbol$id_=e,$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:t})};t.prototype.toString=function(){return this.$jscomp$symbol$id_};var n=0,o=function(e){if(this instanceof o)throw new TypeError("Symbol is not a constructor");return new t("jscomp_symbol_"+(e||"")+"_"+n++,e)};return o}),"es6","es3"),$jscomp.initSymbolIterator=function(){},$jscomp.polyfill("Symbol.iterator",(function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),n=0;n<t.length;n++){var o=$jscomp.global[t[n]];"function"==typeof o&&"function"!=typeof o.prototype[e]&&$jscomp.defineProperty(o.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return e}),"es6","es3"),$jscomp.initSymbolAsyncIterator=function(){},$jscomp.iteratorPrototype=function(e){return(e={next:e})[Symbol.iterator]=function(){return this},e},function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e=e||self).VeeValidate={},e.Vue)}(this,(function(e,t){function n(e,t,n,o){return new(n||(n=Promise))((function(a,r){function i(e){try{l(o.next(e))}catch(e){r(e)}}function s(e){try{l(o.throw(e))}catch(e){r(e)}}function l(e){e.done?a(e.value):new n((function(t){t(e.value)})).then(i,s)}l((o=o.apply(e,t||[])).next())}))}function o(e,t){function n(n){return function(i){return function(n){if(o)throw new TypeError("Generator is already executing.");for(;s;)try{if(o=1,a&&(r=2&n[0]?a.return:n[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,n[1])).done)return r;switch(a=0,r&&(n=[2&n[0],r.value]),n[0]){case 0:case 1:r=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,a=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=0<r.length&&r[r.length-1])||6!==n[0]&&2!==n[0])){s=0;continue}if(3===n[0]&&(!r||n[1]>r[0]&&n[1]<r[3]))s.label=n[1];else if(6===n[0]&&s.label<r[1])s.label=r[1],r=n;else{if(!(r&&s.label<r[2])){r[2]&&s.ops.pop(),s.trys.pop();continue}s.label=r[2],s.ops.push(n)}}n=t.call(e,s)}catch(e){n=[6,e],a=0}finally{o=r=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,i])}}var o,a,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i}function a(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;e=Array(e);var o=0;for(t=0;t<n;t++)for(var a=arguments[t],r=0,i=a.length;r<i;r++,o++)e[o]=a[r];return e}function r(e){return null==e}function i(e,t){if(e instanceof RegExp&&t instanceof RegExp)return i(e.source,t.source)&&i(e.flags,t.flags);if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!i(e[n],t[n]))return!1;return!0}return G(e)&&G(t)?Object.keys(e).every((function(n){return i(e[n],t[n])}))&&Object.keys(t).every((function(n){return i(e[n],t[n])})):e!=e&&t!=t||e===t}function s(e){return""!==e&&!r(e)}function l(e){return"function"==typeof e}function u(e){return l(e)&&!!e.__locatorRef}function c(e,t){var n=Array.isArray(e)?e:p(e);if(l(n.findIndex))return n.findIndex(t);for(var o=0;o<n.length;o++)if(t(n[o],o))return o;return-1}function d(e,t){return-1!==e.indexOf(t)}function p(e){if(l(Array.from))return Array.from(e);for(var t=[],n=e.length,o=0;o<n;o++)t.push(e[o]);return t}function m(e){return l(Object.values)?Object.values(e):Object.keys(e).map((function(t){return e[t]}))}function f(e,t){return Object.keys(t).forEach((function(n){G(t[n])?(e[n]||(e[n]={}),f(e[n],t[n])):e[n]=t[n]})),e}function h(e){return e}function g(e,t,n){return void 0===t&&(t=0),void 0===n&&(n={cancelled:!1}),0===t?e:function(){for(var a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];clearTimeout(o),o=setTimeout((function(){o=void 0,n.cancelled||e.apply(void 0,a)}),t)};var o}function v(e,t){return e.replace(/{([^}]+)}/g,(function(e,n){return n in t?t[n]:"{"+n+"}"}))}function y(e){var t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?G(e)&&e._$$isNormalized?e:G(e)?Object.keys(e).reduce((function(t,n){var o=!0===e[n]?[]:Array.isArray(e[n])||G(e[n])?e[n]:[e[n]];return!1!==e[n]&&(t[n]=b(n,o)),t}),t):"string"!=typeof e?(console.warn("[vee-validate] rules must be either a string or an object."),t):e.split("|").reduce((function(e,t){var n=[],o=t.split(":")[0];return d(t,":")&&(n=t.split(":").slice(1).join(":").split(",")),o?(e[o]=b(o,n),e):e}),t):t}function b(e,t){var n=J.getRuleDefinition(e);if(!n)return t;var o={};if(!n.params&&!Array.isArray(t))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(t)&&!n.params)return t;if(!n.params||n.params.length<t.length&&Array.isArray(t))var a,r=t.map((function(e,t){var o,r=null===(o=n.params)||void 0===o?void 0:o[t];return a=r||a,r||(r=a),r}));else r=n.params;for(var i=0;i<r.length;i++){var s=r[i],l=s.default;Array.isArray(t)?i in t&&(l=t[i]):s.name in t?l=t[s.name]:1===r.length&&(l=t),s.isTarget&&(l=w(l,s.cast)),"string"==typeof l&&"@"===l[0]&&(l=w(l.slice(1),s.cast)),!u(l)&&s.cast&&(l=s.cast(l)),o[s.name]?(o[s.name]=Array.isArray(o[s.name])?o[s.name]:[o[s.name]],o[s.name].push(l)):o[s.name]=l}return o}function w(e,t){var n=function(n){return n=n[e],t?t(n):n};return n.__locatorRef=e,n}function _(e,t,a){var r,i,s,l,u,c;return void 0===a&&(a={}),n(this,void 0,void 0,(function(){var n,d,p,m,f,h;return o(this,(function(o){switch(o.label){case 0:return n=null===(r=a)||void 0===r?void 0:r.bails,d=null===(i=a)||void 0===i?void 0:i.skipIfEmpty,[4,k({name:(null===(s=a)||void 0===s?void 0:s.name)||"{field}",rules:y(t),bails:null==n||n,skipIfEmpty:null==d||d,forceRequired:!1,crossTable:(null===(l=a)||void 0===l?void 0:l.values)||{},names:(null===(u=a)||void 0===u?void 0:u.names)||{},customMessages:(null===(c=a)||void 0===c?void 0:c.customMessages)||{}},e,a)];case 1:return p=o.sent(),m=[],f={},h={},p.errors.forEach((function(e){var t=e.msg();m.push(t),f[e.rule]=t,h[e.rule]=e.msg})),[2,{valid:p.valid,errors:m,failedRules:f,regenerateMap:h}]}}))}))}function k(e,t,a){var r=void 0!==(a=(void 0===a?{}:a).isInitial)&&a;return n(this,void 0,void 0,(function(){var n,a,i,s,l,u,c,d;return o(this,(function(o){switch(o.label){case 0:return[4,A(e,t)];case 1:if(n=o.sent(),a=n.shouldSkip,i=n.errors,a)return[2,{valid:!i.length,errors:i}];s=Object.keys(e.rules).filter((function(e){return!J.isRequireRule(e)})),l=s.length,u=0,o.label=2;case 2:return u<l?r&&J.isLazy(s[u])?[3,4]:(c=s[u],[4,I(e,t,{name:c,params:e.rules[c]})]):[3,5];case 3:if(!(d=o.sent()).valid&&d.error&&(i.push(d.error),e.bails))return[2,{valid:!1,errors:i}];o.label=4;case 4:return u++,[3,2];case 5:return[2,{valid:!i.length,errors:i}]}}))}))}function A(e,t){return n(this,void 0,void 0,(function(){var n,a,i,s,l,u,c,d,p;return o(this,(function(o){switch(o.label){case 0:var m;n=Object.keys(e.rules).filter(J.isRequireRule),a=n.length,i=[],(m=r(t)||""===t)||(m=Array.isArray(t)&&0===t.length),l=(s=m)&&e.skipIfEmpty,u=!1,c=0,o.label=1;case 1:return c<a?(d=n[c],[4,I(e,t,{name:d,params:e.rules[d]})]):[3,4];case 2:if(p=o.sent(),!G(p))throw Error("Require rules has to return an object (see docs)");if(p.required&&(u=!0),!p.valid&&p.error&&(i.push(p.error),e.bails))return[2,{shouldSkip:!0,errors:i}];o.label=3;case 3:return c++,[3,1];case 4:return s&&!u&&!e.skipIfEmpty||!e.bails&&!l?[2,{shouldSkip:!1,errors:i}]:[2,{shouldSkip:!u&&s,errors:i}]}}))}))}function I(e,t,a){return n(this,void 0,void 0,(function(){var n,r,i,s,l;return o(this,(function(o){switch(o.label){case 0:if(!(n=J.getRuleDefinition(a.name))||!n.validate)throw Error("No such validator '"+a.name+"' exists.");return r=n.castValue?n.castValue(t):t,i=function(e,t){if(Array.isArray(e))return e;var n={};return Object.keys(e).forEach((function(o){var a=e[o];a=u(a)?a(t):a,n[o]=a})),n}(a.params,e.crossTable),[4,n.validate(r,i)];case 1:return"string"==typeof(s=o.sent())?(l=W(W({},i||{}),{_field_:e.name,_value_:t,_rule_:a.name}),[2,{valid:!1,error:{rule:a.name,msg:function(){return v(s,l)}}}]):(G(s)||(s={valid:s}),[2,{valid:s.valid,required:s.required,error:s.valid?void 0:S(e,t,n,a.name,i)}])}}))}))}function S(e,t,n,o,a){var r,i=null!=(r=e.customMessages[o])?r:n.message;r=function(e,t,n){if(t=t.params,!t||0>=t.filter((function(e){return e.isTarget})).length)return{};var o={},a=e.rules[n];for(!Array.isArray(a)&&G(a)&&(a=t.map((function(e){return a[e.name]}))),n=0;n<t.length;n++){var r=t[n],i=a[n];u(i)&&(i=i.__locatorRef,o[r.name]=e.names[i]||i,o["_"+r.name+"_"]=e.crossTable[i])}return o}(e,n,o),n=function(e,t,n,o){var a={},r=e.rules[n],i=t.params||[];return r?(Object.keys(r).forEach((function(t,n){var o=r[t];if(!u(o))return{};var s=i[n];if(!s)return{};o=o.__locatorRef,a[s.name]=e.names[o]||o,a["_"+s.name+"_"]=e.crossTable[o]})),{userTargets:a,userMessage:o}):{}}(e,n,o,i),i=n.userTargets;var s=n.userMessage,l=W(W(W(W({},a||{}),{_field_:e.name,_value_:t,_rule_:o}),r),i);return{msg:function(){var t=s||K.defaultMessage,n=e.name;return t="function"==typeof t?t(n,l):v(t,W(W({},l),{_field_:n}))},rule:o}}function L(){ee.$emit("change:locale")}function C(e){if(e.data){var t=e.data;if("model"in t)return t.model;if(e.data.directives)return function(e,t){var n=Array.isArray(e)?e:p(e),o=c(n,t);return-1===o?void 0:n[o]}(e.data.directives,(function(e){return"model"===e.name}))}}function j(e){var t,n,o,a=C(e);return a?{value:a.value}:(a=(null===(t=T(e))||void 0===t?void 0:t.prop)||"value",null!==(n=e.componentOptions)&&void 0!==n&&n.propsData&&a in e.componentOptions.propsData?{value:e.componentOptions.propsData[a]}:null!==(o=e.data)&&void 0!==o&&o.domProps&&"value"in e.data.domProps?{value:e.data.domProps.value}:void 0)}function P(e){return Array.isArray(e)||void 0===j(e)?function(e){return Array.isArray(e)?e:Array.isArray(e.children)?e.children:e.componentOptions&&Array.isArray(e.componentOptions.children)?e.componentOptions.children:[]}(e).reduce((function(e,t){var n=P(t);return n.length&&e.push.apply(e,n),e}),[]):[e]}function T(e){return e.componentOptions?e.componentOptions.Ctor.options.model:null}function $(e,t,n){r(e[t])?e[t]=[n]:l(e[t])&&e[t].fns?((e=e[t]).fns=Array.isArray(e.fns)?e.fns:[e.fns],d(e.fns,n)||e.fns.push(n)):(l(e[t])&&(e[t]=[e[t]]),Array.isArray(e[t])&&!d(e[t],n)&&e[t].push(n))}function x(e,t,n){e.componentOptions?e.componentOptions&&(e.componentOptions.listeners||(e.componentOptions.listeners={}),$(e.componentOptions.listeners,t,n)):(e.data||(e.data={}),r(e.data.on)&&(e.data.on={}),$(e.data.on,t,n))}function R(e,t){var n;return e.componentOptions?(T(e)||{event:"input"}).event:null!==(n=null==t?void 0:t.modifiers)&&void 0!==n&&n.lazy?"change":ne(e)?"input":"change"}function D(e,t){return e.$scopedSlots.default?e.$scopedSlots.default(t)||[]:e.$slots.default||[]}function U(e){return W(W({},e.flags),{errors:e.errors,classes:e.classes,failedRules:e.failedRules,reset:function(){return e.reset()},validate:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.validate.apply(e,t)},ariaInput:{"aria-invalid":e.flags.invalid?"true":"false","aria-required":e.isRequired?"true":"false","aria-errormessage":"vee_"+e.id},ariaMsg:{id:"vee_"+e.id,"aria-live":e.errors.length?"assertive":"off"}})}function O(e,t){e.initialized||(e.initialValue=t);var n=!!(!e._ignoreImmediate&&e.immediate||e.value!==t&&e.normalizedEvents.length||e._needsValidation||!e.initialized&&void 0===t);if(e._needsValidation=!1,e.value=t,e._ignoreImmediate=!0,n){var o=function(){if(e.immediate||e.flags.validated)return H(e);e.validateSilent()};e.initialized?o():e.$once("hook:mounted",(function(){return o()}))}}function M(e){return(l(e.mode)?e.mode:X[e.mode])(e)}function H(e){var t=e.validateSilent();return e._pendingValidation=t,t.then((function(n){return t===e._pendingValidation&&(e.applyResult(n),e._pendingValidation=void 0),n}))}function E(e){e.$veeOnInput||(e.$veeOnInput=function(t){e.syncValue(t),e.setFlags({dirty:!0,pristine:!1})});var t=e.$veeOnInput;e.$veeOnBlur||(e.$veeOnBlur=function(){e.setFlags({touched:!0,untouched:!1})});var n=e.$veeOnBlur,o=e.$veeHandler,a=M(e);return o&&e.$veeDebounce===e.debounce||(o=g((function(){e.$nextTick((function(){e._pendingReset||H(e),e._pendingReset=!1}))}),a.debounce||e.debounce),e.$veeHandler=o,e.$veeDebounce=e.debounce),{onInput:t,onBlur:n,onValidate:o}}function N(e){var t=e.$_veeObserver.refs;return e.fieldDeps.reduce((function(e,n){return t[n]?(e.values[n]=t[n].value,e.names[n]=t[n].name,e):e}),{names:{},values:{}})}function B(e,t,n){void 0===n&&(n=!0);var o=e.$_veeObserver.refs;if(e._veeWatchers||(e._veeWatchers={}),!o[t]&&n)return e.$once("hook:mounted",(function(){B(e,t,!1)}));!l(e._veeWatchers[t])&&o[t]&&(e._veeWatchers[t]=o[t].$watch("value",(function(){e.flags.validated&&(e._needsValidation=!0,e.validate())})))}function F(e){e.$_veeObserver&&e.$_veeObserver.unobserve(e.id,"observer")}function z(e){e.$_veeObserver&&e.$_veeObserver.observe(e,"observer")}function Y(){return W(W({},{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1}),{valid:!0,invalid:!1})}function V(){for(var e=a(m(this.refs),this.observers),t={},n=Y(),o={},r=e.length,i=0;i<r;i++){var s=e[i];Array.isArray(s.errors)?(t[s.id]=s.errors,o[s.id]=W({id:s.id,name:s.name,failedRules:s.failedRules},s.flags)):(t=W(W({},t),s.errors),o=W(W({},o),s.fields))}return re.forEach((function(t){var o=t[0];n[o]=e[t[1]]((function(e){return e.flags[o]}))})),{errors:t,flags:n,fields:o}}t=t&&t.hasOwnProperty("default")?t.default:t;var q,W=function(){return W=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},W.apply(this,arguments)},G=function(e){return null!==e&&e&&"object"==typeof e&&!Array.isArray(e)},Z={},J=function(){function e(){}return e.extend=function(e,t){var n=function(e){var t;return null!==(t=e.params)&&void 0!==t&&t.length&&(e.params=e.params.map((function(e){return"string"==typeof e?{name:e}:e}))),e}(t);Z[e]=Z[e]?f(Z[e],t):W({lazy:!1,computesRequired:!1},n)},e.isLazy=function(e){var t;return!(null===(t=Z[e])||void 0===t||!t.lazy)},e.isRequireRule=function(e){var t;return!(null===(t=Z[e])||void 0===t||!t.computesRequired)},e.getRuleDefinition=function(e){return Z[e]},e}(),K=W({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Q=function(e){K=W(W({},K),e)},X={aggressive:function(){return{on:["input","blur"]}},eager:function(e){return e.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},ee=new t,te=function(){function e(e,t){this.container={},this.locale=e,this.merge(t)}return e.prototype.resolve=function(e,t,n){return this.format(this.locale,e,t,n)},e.prototype.format=function(e,t,n,o){var a,r,i,s,u,c,d,p;return(n=(null===(i=null===(r=null===(a=this.container[e])||void 0===a?void 0:a.fields)||void 0===r?void 0:r[t])||void 0===i?void 0:i[n])||(null===(u=null===(s=this.container[e])||void 0===s?void 0:s.messages)||void 0===u?void 0:u[n]))||(n="{field} is not valid"),t=null!=(p=null===(d=null===(c=this.container[e])||void 0===c?void 0:c.names)||void 0===d?void 0:d[t])?p:t,l(n)?n(t,o):v(n,W(W({},o),{_field_:t}))},e.prototype.merge=function(e){f(this.container,e)},e.prototype.hasRule=function(e){var t,n;return!(null===(n=null===(t=this.container[this.locale])||void 0===t?void 0:t.messages)||void 0===n||!n[e])},e}(),ne=function(e){var t,n=(null===(t=e.data)||void 0===t?void 0:t.attrs)||e.elm;return!("input"!==e.tag||n&&n.type)||"textarea"===e.tag||d("text password search email tel url number".split(" "),null==n?void 0:n.type)},oe=0,ae=t.extend({inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver={refs:{},observe:function(e){this.refs[e.id]=e},unobserve:function(e){delete this.refs[e]}}),this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,default:""},name:{type:String,default:null},mode:{type:[String,Function],default:function(){return K.mode}},rules:{type:[Object,String],default:null},immediate:{type:Boolean,default:!1},bails:{type:Boolean,default:function(){return K.bails}},skipIfEmpty:{type:Boolean,default:function(){return K.skipOptional}},debounce:{type:Number,default:0},tag:{type:String,default:"span"},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},customMessages:{type:Object,default:function(){return{}}}},watch:{rules:{deep:!0,handler:function(e,t){this._needsValidation=!i(e,t)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1},failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var e=this;return Object.keys(this.normalizedRules).reduce((function(t,n){var o=function(e){return Array.isArray(e)?e.filter(u):Object.keys(e).filter((function(t){return u(e[t])})).map((function(t){return e[t]}))}(e.normalizedRules[n]).map((function(e){return e.__locatorRef}));return t.push.apply(t,o),o.forEach((function(t){B(e,t)})),t}),[])},normalizedEvents:function(){var e=this;return(M(this).on||[]).map((function(t){return"input"===t?e._inputEventName:t}))},isRequired:function(){var e=W(W({},this._resolvedRules),this.normalizedRules);return e=Object.keys(e).some(J.isRequireRule),this.flags.required=!!e,e},classes:function(){return function(e,t){for(var n={},o=Object.keys(t),a=o.length,i=function(a){a=o[a];var i=e&&e[a]||a,s=t[a];if(r(s)||("valid"===a||"invalid"===a)&&!t.validated)return"continue";"string"==typeof i?n[i]=s:Array.isArray(i)&&i.forEach((function(e){n[e]=s}))},s=0;s<a;s++)i(s);return n}(K.classes,this.flags)},normalizedRules:function(){return y(this.rules)}},created:function(){var e=this,t=function(){if(e.flags.validated){var t=e._regenerateMap;if(t){var n=[],o={};Object.keys(t).forEach((function(e){var a=t[e]();n.push(a),o[e]=a})),e.applyResult({errors:n,failedRules:o,regenerateMap:t})}else e.validate()}};ee.$on("change:locale",t),this.$on("hook:beforeDestroy",(function(){ee.$off("change:locale",t)}))},render:function(e){var t=this;this.registerField();var n=U(this);return P(n=D(this,n)).forEach((function(e){var n,o,a,r,l;if(K.useConstraintAttrs){var u,c=null===(u=e.data)||void 0===u?void 0:u.attrs;if(d(["input","select","textarea"],e.tag)&&c)if(u={},"required"in c&&!1!==c.required&&J.getRuleDefinition("required")&&(u.required="checkbox"!==c.type||[!0]),ne(e)){c=W,u=W({},u);var p=null===(l=e.data)||void 0===l?void 0:l.attrs;l={},p&&("email"===p.type&&J.getRuleDefinition("email")&&(l.email=["multiple"in p]),p.pattern&&J.getRuleDefinition("regex")&&(l.regex=p.pattern),0<=p.maxlength&&J.getRuleDefinition("max")&&(l.max=p.maxlength),0<=p.minlength&&J.getRuleDefinition("min")&&(l.min=p.minlength),"number"===p.type&&(s(p.min)&&J.getRuleDefinition("min_value")&&(l.min_value=Number(p.min)),s(p.max)&&J.getRuleDefinition("max_value")&&(l.max_value=Number(p.max)))),l=y(c(u,l))}else l=y(u);else l={}}else l={};i(t._resolvedRules,l)||(t._needsValidation=!0),d(["input","select","textarea"],e.tag)&&(t.fieldName=(null===(o=null===(n=e.data)||void 0===n?void 0:n.attrs)||void 0===o?void 0:o.name)||(null===(r=null===(a=e.data)||void 0===a?void 0:a.attrs)||void 0===r?void 0:r.id)),t._resolvedRules=l,function(e,t){var n=j(t);e._inputEventName=e._inputEventName||R(t,C(t)),O(e,null==n?void 0:n.value);var o=(n=E(e)).onBlur,a=n.onValidate;x(t,e._inputEventName,n.onInput),x(t,"blur",o),e.normalizedEvents.forEach((function(e){x(t,e,a)})),e.initialized=!0}(t,e)})),this.slim&&1>=n.length?n[0]:e(this.tag,n)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(e){var t=this;Object.keys(e).forEach((function(n){t.flags[n]=e[n]}))},syncValue:function(e){this.value=e=function(e){var t,n;return e&&("undefined"!=typeof Event&&l(Event)&&e instanceof Event||e&&e.srcElement)?"file"===(e=e.target).type&&e.files?p(e.files):null!==(t=e._vModifiers)&&void 0!==t&&t.number?(t=parseFloat(e.value))!=t?e.value:t:null!==(n=e._vModifiers)&&void 0!==n&&n.trim&&"string"==typeof e.value?e.value.trim():e.value:e}(e),this.flags.changed=this.initialValue!==e},reset:function(){var e=this;this.errors=[],this.initialValue=this.value;var t={untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1};t.required=this.isRequired,this.setFlags(t),this.failedRules={},this.validateSilent(),this._pendingValidation=void 0,this._pendingReset=!0,setTimeout((function(){e._pendingReset=!1}),this.debounce)},validate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,(function(){return o(this,(function(t){return 0<e.length&&this.syncValue(e[0]),[2,H(this)]}))}))},validateSilent:function(){return n(this,void 0,void 0,(function(){var e,t;return o(this,(function(n){switch(n.label){case 0:return this.setFlags({pending:!0}),e=W(W({},this._resolvedRules),this.normalizedRules),Object.defineProperty(e,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,_(this.value,e,W(W({name:this.name||this.fieldName},N(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return t=n.sent(),this.setFlags({pending:!1,valid:t.valid,invalid:!t.valid}),[2,t]}}))}))},setErrors:function(e){this.applyResult({errors:e,failedRules:{}})},applyResult:function(e){var t=e.errors,n=e.failedRules;e=e.regenerateMap,this.errors=t,this._regenerateMap=e,this.failedRules=W({},n||{}),this.setFlags({valid:!t.length,passed:!t.length,invalid:!!t.length,failed:!!t.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var e=function(e){return e.vid?e.vid:e.name?e.name:e.id?e.id:e.fieldName?e.fieldName:"_vee_"+ ++oe}(this),t=this.id;!this.isActive||t===e&&this.$_veeObserver.refs[t]||(t!==e&&this.$_veeObserver.refs[t]===this&&this.$_veeObserver.unobserve(t),this.id=e,this.$_veeObserver.observe(this))}}}),re=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],ie=0,se=t.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,default:"span"},vid:{type:String,default:function(){return"obs_"+ie++}},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:Y(),fields:{}}},created:function(){var e=this;this.id=this.vid,z(this);var t=g((function(t){var n=t.flags,o=t.fields;e.errors=t.errors,e.flags=n,e.fields=o}),16);this.$watch(V,t)},activated:function(){z(this)},deactivated:function(){F(this)},beforeDestroy:function(){F(this)},render:function(e){var t=D(this,W(W({},this.flags),{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=t.length?t[0]:e(this.tag,{on:this.$listeners},t)},methods:{observe:function(e,t){var n;void 0===t&&(t="provider"),"observer"===t?this.observers.push(e):this.refs=W(W({},this.refs),((n={})[e.id]=e,n))},unobserve:function(e,t){if(void 0===t&&(t="provider"),"provider"===t)this.refs[e]&&this.$delete(this.refs,e);else{var n=c(this.observers,(function(t){return t.id===e}));-1!==n&&this.observers.splice(n,1)}},validate:function(e){var t=void 0!==(e=(void 0===e?{}:e).silent)&&e;return n(this,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return[4,Promise.all(a(m(this.refs).filter((function(e){return!e.disabled})).map((function(e){return e[t?"validateSilent":"validate"]().then((function(e){return e.valid}))})),this.observers.filter((function(e){return!e.disabled})).map((function(e){return e.validate({silent:t})}))))];case 1:return[2,e.sent().every((function(e){return e}))]}}))}))},handleSubmit:function(e){return n(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,this.validate()];case 1:return t.sent()&&e?[2,e()]:[2]}}))}))},reset:function(){return a(m(this.refs),this.observers).forEach((function(e){return e.reset()}))},setErrors:function(e){var t=this;Object.keys(e).forEach((function(n){var o=t.refs[n];o&&(n="string"==typeof(n=e[n]||[])?[n]:n,o.setErrors(n))})),this.observers.forEach((function(t){t.setErrors(e)}))}}});e.ValidationObserver=se,e.ValidationProvider=ae,e.configure=function(e){Q(e)},e.extend=function(e,t){if(!l(t)&&!l(t.validate)&&!J.getRuleDefinition(e))throw Error("Extension Error: The validator '"+e+"' must be a function or have a 'validate' method.");"object"==typeof t?J.extend(e,t):J.extend(e,{validate:t})},e.localeChanged=L,e.localize=function(e,t){var n;q||(q=new te("en",{}),Q({defaultMessage:function(e,t){return q.resolve(e,null==t?void 0:t._rule_,t||{})}})),"string"==typeof e?(q.locale=e,t&&q.merge(((n={})[e]=t,n)),L()):q.merge(e)},e.normalizeRules=y,e.setInteractionMode=function(e,t){if(Q({mode:e}),t){if(!l(t))throw Error("A mode implementation must be a function");X[e]=t}},e.validate=_,e.version="3.2.3",e.withValidation=function(e,t){void 0===t&&(t=h);var n,o="options"in e?e.options:e,a=ae.options;a={name:(o.name||"AnonymousHoc")+"WithValidation",props:W({},a.props),data:a.data,computed:W({},a.computed),methods:W({},a.methods),beforeDestroy:a.beforeDestroy,inject:a.inject};var r=(null===(n=null==o?void 0:o.model)||void 0===n?void 0:n.event)||"input";return a.render=function(e){var n;this.registerField();var a=U(this),i=W({},this.$listeners),s=C(this.$vnode);this._inputEventName=this._inputEventName||R(this.$vnode,s);var l=j(this.$vnode);O(this,null==l?void 0:l.value);var u=(l=E(this)).onBlur,c=l.onValidate;return $(i,r,l.onInput),$(i,"blur",u),this.normalizedEvents.forEach((function(e){$(i,e,c)})),l=(T(this.$vnode)||{prop:"value"}).prop,a=W(W(W({},this.$attrs),((n={})[l]=null==s?void 0:s.value,n)),t(a)),e(o,{attrs:this.$attrs,props:a,on:i},function(e,t){return Object.keys(e).reduce((function(n,o){return e[o].forEach((function(n){n.context||(e[o].context=t,n.data||(n.data={}),n.data.slot=o)})),n.concat(e[o])}),[])}(this.$slots,this.$vnode.context))},a},Object.defineProperty(e,"__esModule",{value:!0})})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).VeeValidateRules={})}(this,(function(e){"use strict";var t={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},n={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},o={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},r=function(e,n){var o=(void 0===n?{}:n).locale,a=void 0===o?"":o;return Array.isArray(e)?e.every((function(e){return r(e,{locale:a})})):a?(t[a]||t.en).test(e):Object.keys(t).some((function(n){return t[n].test(e)}))},i={validate:r,params:[{name:"locale"}]},s=function(e,t){var n=(void 0===t?{}:t).locale,o=void 0===n?"":n;return Array.isArray(e)?e.every((function(e){return s(e,{locale:o})})):o?(a[o]||a.en).test(e):Object.keys(a).some((function(t){return a[t].test(e)}))},l={validate:s,params:[{name:"locale"}]},u=function(e,t){var n=(void 0===t?{}:t).locale,a=void 0===n?"":n;return Array.isArray(e)?e.every((function(e){return u(e,{locale:a})})):a?(o[a]||o.en).test(e):Object.keys(o).some((function(t){return o[t].test(e)}))},c={validate:u,params:[{name:"locale"}]},d=function(e,t){var o=(void 0===t?{}:t).locale,a=void 0===o?"":o;return Array.isArray(e)?e.every((function(e){return d(e,{locale:a})})):a?(n[a]||n.en).test(e):Object.keys(n).some((function(t){return n[t].test(e)}))},p={validate:d,params:[{name:"locale"}]},m=function(e,t){var n=void 0===t?{}:t,o=n.min,a=n.max;return Array.isArray(e)?e.every((function(e){return!!m(e,{min:o,max:a})})):Number(o)<=e&&Number(a)>=e},f={validate:m,params:[{name:"min"},{name:"max"}]},h={validate:function(e,t){var n=t.target;return String(e)===String(n)},params:[{name:"target",isTarget:!0}]},g=function(e,t){var n=t.length;if(Array.isArray(e))return e.every((function(e){return g(e,{length:n})}));var o=String(e);return/^[0-9]*$/.test(o)&&o.length===n},v={validate:g,params:[{name:"length",cast:function(e){return Number(e)}}]},y={validate:function(e,t){var n=t.width,o=t.height,a=[];e=Array.isArray(e)?e:[e];for(var r=0;r<e.length;r++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(e[r].name))return Promise.resolve(!1);a.push(e[r])}return Promise.all(a.map((function(e){return t=e,a=n,r=o,i=window.URL||window.webkitURL,new Promise((function(e){var n=new Image;n.onerror=function(){return e(!1)},n.onload=function(){return e(n.width===a&&n.height===r)},n.src=i.createObjectURL(t)}));var t,a,r,i}))).then((function(e){return e.every((function(e){return e}))}))},params:[{name:"width",cast:function(e){return Number(e)}},{name:"height",cast:function(e){return Number(e)}}]},b={validate:function(e,t){var n=(void 0===t?{}:t).multiple,o=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return n&&!Array.isArray(e)&&(e=String(e).split(",").map((function(e){return e.trim()}))),Array.isArray(e)?e.every((function(e){return o.test(String(e))})):o.test(String(e))},params:[{name:"multiple",default:!1}]};function w(e){return null==e}function _(e){return Array.isArray(e)&&0===e.length}function k(e){return"function"==typeof Array.from?Array.from(e):function(e){for(var t=[],n=e.length,o=0;o<n;o++)t.push(e[o]);return t}(e)}function A(e){return _(e)||-1!==[!1,null,void 0].indexOf(e)||!String(e).trim().length}var I=function(e,t){return Array.isArray(e)?e.every((function(e){return I(e,t)})):k(t).some((function(t){return t==e}))},S={validate:I},L={validate:function(e,t){return!I(e,t)}},C={validate:function(e,t){var n=new RegExp(".("+t.join("|")+")$","i");return Array.isArray(e)?e.every((function(e){return n.test(e.name)})):n.test(e.name)}},j={validate:function(e){var t=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(e)?e.every((function(e){return t.test(e.name)})):t.test(e.name)}},P={validate:function(e){return Array.isArray(e)?e.every((function(e){return/^-?[0-9]+$/.test(String(e))})):/^-?[0-9]+$/.test(String(e))}},T={validate:function(e,t){var n=t.length;return!w(e)&&("number"==typeof e&&(e=String(e)),e.length||(e=k(e)),e.length===n)},params:[{name:"length",cast:function(e){return Number(e)}}]},$=function(e,t){var n=t.length;return w(e)?0<=n:Array.isArray(e)?e.every((function(e){return $(e,{length:n})})):String(e).length<=n},x={validate:$,params:[{name:"length",cast:function(e){return Number(e)}}]},R=function(e,t){var n=t.max;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every((function(e){return R(e,{max:n})})):Number(e)<=n)},D={validate:R,params:[{name:"max",cast:function(e){return Number(e)}}]},U={validate:function(e,t){var n=new RegExp(t.join("|").replace("*",".+")+"$","i");return Array.isArray(e)?e.every((function(e){return n.test(e.type)})):n.test(e.type)}},O=function(e,t){var n=t.length;return!w(e)&&(Array.isArray(e)?e.every((function(e){return O(e,{length:n})})):String(e).length>=n)},M={validate:O,params:[{name:"length",cast:function(e){return Number(e)}}]},H=function(e,t){var n=t.min;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every((function(e){return H(e,{min:n})})):Number(e)>=n)},E={validate:H,params:[{name:"min",cast:function(e){return Number(e)}}]},N=/^[٠١٢٣٤٥٦٧٨٩]+$/,B=/^[0-9]+$/,F={validate:function(e){function t(e){var t=String(e);return B.test(t)||N.test(t)}return Array.isArray(e)?e.every(t):t(e)}},z=function(e,t){var n=t.regex;return Array.isArray(e)?e.every((function(e){return z(e,{regex:n})})):n.test(String(e))},Y={validate:z,params:[{name:"regex",cast:function(e){return"string"==typeof e?new RegExp(e):e}}]},V={validate:function(e,t){var n=(void 0===t?{allowFalse:!0}:t).allowFalse,o={valid:!1,required:!0};return w(e)||_(e)||!1===e&&!n||(o.valid=!!String(e).trim().length),o},params:[{name:"allowFalse",default:!0}],computesRequired:!0},q={validate:function(e,t){var n,o=t.target,a=t.values;return(n=a&&a.length?(Array.isArray(a)||"string"!=typeof a||(a=[a]),a.some((function(e){return e==String(o).trim()}))):!A(o))?{valid:!A(e),required:n}:{valid:!0,required:n}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},W={validate:function(e,t){var n=t.size;if(isNaN(n))return!1;var o=1024*n;if(!Array.isArray(e))return e.size<=o;for(var a=0;a<e.length;a++)if(e[a].size>o)return!1;return!0},params:[{name:"size",cast:function(e){return Number(e)}}]};e.alpha=i,e.alpha_dash=l,e.alpha_num=c,e.alpha_spaces=p,e.between=f,e.confirmed=h,e.digits=v,e.dimensions=y,e.email=b,e.excluded=L,e.ext=C,e.image=j,e.integer=P,e.is={validate:function(e,t){return e===t.other},params:[{name:"other"}]},e.is_not={validate:function(e,t){return e!==t.other},params:[{name:"other"}]},e.length=T,e.max=x,e.max_value=D,e.mimes=U,e.min=M,e.min_value=E,e.numeric=F,e.oneOf=S,e.regex=Y,e.required=V,e.required_if=q,e.size=W,Object.defineProperty(e,"__esModule",{value:!0})})),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach((e=>{e.items.forEach((e=>{e.is_active=n===t(e.url);let o=!1;e.sub_items.forEach((a=>{a.is_active=n===t(a.url),a.is_active&&a.parent_id===e.id&&(o=!0)})),e.is_expended=!!o}))}))},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,o={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",o)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:o}=e;null===sessionStorage.getItem("activeOrg")&&(n?o.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(o[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-course-search-bar",{props:["data","options"],template:'\n        <div class="hasSearchBar">\n            <template v-if="userInfo.loading">\n                <b-skeleton height="31px"></b-skeleton>\n            </template>\n            <div class="searchBarWrapper" v-if="userInfo.success && userInfo.data.role === \'Learner\'">\n                <validation-observer  \n                    tag="div" \n                    ref="searchObserver" \n                    v-slot="{ handleSubmit, invalid }">\n                    <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                        <b-field class="searchFieldWrapper">\n                            <validation-provider \n                                tag="div"\n                                class="searchField"\n                                :customMessages="{ isNotBlank: errorMsg.subject }"\n                                :rules="{required:true, isNotBlank:categories.selected}" \n                                v-slot="{ errors, classes }">\n                                <b-autocomplete\n                                    :class="classes"\n                                    v-model="categories.current"\n                                    :data="categories.data"\n                                    autocomplete="courseSearch"\n                                    :loading="categories.isLoading"\n                                    placeholder="Search..."\n                                    @typing="searchOnTyping"\n                                    @select="onSelect($event)"\n                                    :clearable="true"\n                                >\n                                    <template slot-scope="props">\n                                        <template v-if="props.option.course_url">\n                                            <div class="suggestion courseBlock">\n                                                <figure>\n                                                    <div class="imageWrapper">\n                                                        <img :src="props.option.imageurl" :alt="props.option.title">\n                                                    </div>\n                                                    <figcaption>\n                                                        <p class="courseTitle">{{ props.option.title }}</p>\n                                                        <p class="courseDetail">\n                                                            <span class="caption">Course</span>\n                                                            <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                        </p>\n                                                    </figcaption>\n                                                </figure>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                    </template>\n                                </b-autocomplete>\n                            </validation-provider>\n                            <div class="ctaWrapper">\n                                <b-button\n                                    native-type="submit"\n                                    class="doSearch">\n                                    <span class="material-icons-outlined">search</span>\n                                </b-button>  \n                            </div>\n                        </b-field>\n                    </form>\n                </validation-observer>\n            </div>\n        </div>\n    ',data:()=>({errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}),computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","header","module","searchSuggestions"])},async created(){},mounted(){},methods:{onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:o}=e.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(e){e.length>2?(this.categories.isLoading=!0,this.fetchCourseSuggestions(e)):this.categories.data=[]}),700),initForm(){const e=this.categories.selected;e.category_id,e.category,e.categorySlug;void 0===this.$props.hassearchbar&&(window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)))}}}),Vue.component("yuno-grid-detail-view",{props:{type:{type:String,required:!0},h1:{type:String,required:!1},h2:{type:String,required:!1},description:{type:String,required:!1},img:{type:String,required:!1},video:{type:String,required:!1},imgOrder:{type:Number,required:!0},featureList:{type:Array,required:!0},featureListOrder:{type:Number,required:!0},cta:{type:Object,required:!1}},template:'\n        <section class="gridDetailView" :class="[type]">\n            <div class="container">\n                <h1 v-if="h1" class="h1">{{ h1 }}</h1>\n                <h2 v-if="h2" class="h2">{{ h2 }}</h2>\n                <p v-if="description" class="body1 fontColorDarkVariant">{{ description }}</p>\n                <div class="row">\n                    <div class="col-md-6">\n                        <div v-if="img" :class="[\'order-\'+ imgOrder]">\n                            <img :src="img" alt="Image">\n                        </div>\n                        <div v-if="video" class="videoWrapper">\n                            <iframe :src="video + \'?autoplay=1\'" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>\n                        </div>\n                    </div>\n                    <div class="col-md-6" :class="[\'order-\'+ featureListOrder]">\n                        <ul class="featureList">\n                            <li v-for="(feature, index) in featureList" :key="index">\n                                <div class="wrapper">\n                                    <h3 class="h3 fontColorDarkVariant">{{ feature.title }}</h3>\n                                    <p class="body2 fontColorDarkVariant">{{ feature.description }}</p>\n                                </div>\n                            </li>\n                        </ul>\n                        <div class="cta" v-if="cta">\n                            <b-button \n                                tag="a"\n                                :href="cta.disabled ? \'#\' : cta.url"\n                                :disabled="cta.disabled"\n                                class="yunoPrimaryCTA"\n                                @click="$emit(\'ctaClick\', cta)"\n                            >\n                                {{ cta.title }}\n                            </b-button>\n                        </div>                                \n                    </div>\n                </div>        \n            </div>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["blogDetail"]),detail(){return this.blogDetail.data}},async created(){},mounted(){},methods:{}}),Vue.component("yuno-vertical-tabs",{props:{data:{type:Object,required:!0},options:{type:Array}},template:'\n        <section class="verticalTabs">\n            <div class="container">\n                <h2 class="largestTitle alignC">{{ data.title }}</h2>\n                <p v-if="data.titledescription" class="alignC">{{ data.titledescription }} </p>\n                <b-tabs \n                    position="is-left"\n                    vertical\n                    @input="tabChange"\n                >\n                    <b-tab-item \n                        v-for="(tab, i) in data.tabs"\n                        :key="i"\n                    >\n                        <template #header >\n                            <span class="primaryLabel">{{ tab.label }}</span>\n                            <span class="secondaryLabel" v-if="tab.subtitle !== undefined">{{ tab.subtitle }}</span>\n                        </template>\n                        <b-carousel \n                            :arrow="manageArrow(tab.data)"\n                            :indicator="manageArrow(tab.data)"\n                            :autoplay="false"\n                        >\n                            <b-carousel-item v-for="(carousel, j) in tab.data" :key="j">\n                                <figure>\n                                    <figcaption class="itemInfo">\n                                        {{ carousel.description }}\n                                    </figcaption>\n                                    <img :src="carousel.image" :alt="carousel.description" width="750" height="422">\n                                </figure>\n                            </b-carousel-item>\n                        </b-carousel>\n                    </b-tab-item>\n                </b-tabs>\n            </div>\n        </section>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{manageArrow:e=>e.length>1,tabChange(){const e=void 0!==this.$props.data.scrollTo?this.$props.data.scrollTo:"tab-content";YUNOCommon.scrollToElement("."+e,500)}}}),Vue.component("yuno-tab-with-cards",{props:{data:{type:Object,required:!0},options:{type:Array},hideTabs:{type:Boolean,required:!1}},template:'\n\t  <section \n\t\tclass="tabsWithCards"\n\t\t:class="{ hideTabsEnabled: hideTabs }"\n\t  >\n\t\t<div class="container">\n\t\t  <h2 class="largestTitle">{{ data.title }}</h2>\n\t\t  <p v-if="data.description" class="body1 fontColorDarkVariant alignC">\n\t\t\t{{ data.description }}\n\t\t  </p>\n\t\t  <b-tabs\n\t\t\tclass="yunoTabsV2"\n\t\t\tv-model="data.data.activeTab"\n\t\t\t@input="tabChange"\n\t\t\t:animated="false"\n\t\t  >\n\t\t\t<b-tab-item \n\t\t\t  v-for="(tab, i) in tabs"\n\t\t\t  :visible="tab.isActive"\n\t\t\t  :key="i"\n\t\t\t  :label="tab.label"\n\t\t\t>\n\t\t\t  <div class="grid" :class="{ \'alignCenter\': tab.data.length < 3 }">\n\t\t\t\t<article \n\t\t\t\t  v-for="(card, j) in tab.data"\n\t\t\t\t  :key="j"\n\t\t\t\t  class="item"\n\t\t\t\t>\n\t\t\t\t  <div class="wrapper">\n\t\t\t\t\t<div class="pr-5 pl-5">\n\t\t\t\t\t  <p class="headline6 alignC">{{ card.title }}</p>\n\t\t\t\t\t  \n\t\t\t\t\t  <p \n\t\t\t\t\t\tclass="caption1 noBold onSurfaceVariant alignC pt-2"\n\t\t\t\t\t  >\n\t\t\t\t\t\t{{ card.subtitle }}\n\t\t\t\t\t  </p>\n\t\t\t\t\t  \n\t\t\t\t\t  \n\t\t\t\t\t  <hr>\n\t\t\t\t\t  <div class="d-flex justify-content-center align-items-center">\n  \t\t\t\t\t\t<span class="subtitle1">Rs.</span>\n\t\t\t\t\t\t<h4 class="headline3 alignC">\n\t\t\t\t\t\t\t{{ card.perInstructorPrice }}\n\t\t\t\t\t\t</h4>\n\t\t\t\t\t\t<span style="white-space: pre-line;line-height: 1.3;font-size: 11px;color: #534342;padding-left: 5px" v-html="card.priceStatement"></span>\n\t\t\t\t\t  </div>\n\t\t\t\t\n\t\t\t\t\t  \n\t\t\t\t\t  <div class="alignC subtitle2 noBold onSurfaceVariant pt-2">\n\t\t\t\t\t\t<span >\n\t\t\t\t\t\t  Total Rs. {{ card.total }}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t  </div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<ul class="checkedList">\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tv-for="(item, h) in card.items"\n\t\t\t\t\t\t\t:key="h"\n\t\t\t\t\t\t\tclass="list-item"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class="textWithTooltip">\n\t\t\t\t\t\t\t\t{{ item.label }}\n\t\t\t\t\t\t\t\t<b-tooltip v-if="item?.tooltip.hasTooltip" :label="item?.tooltip.label" >\n\t\t\t\t\t\t\t\t\t<span class="material-icons">info</span>\n\t\t\t\t\t\t\t\t</b-tooltip>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t</ul>\n\t\t\t\t\t<div v-if="false" class="cta alignC">\n\t\t\t\t\t  <a href="#" class="button yunoPrimaryCTA">\n\t\t\t\t\t\t{{ card.cta }}\n\t\t\t\t\t  </a>\n\t\t\t\t\t</div>\n\t\t\t\t  </div>\n\t\t\t\t</article>\n\t\t\t  </div>\n\t\t\t</b-tab-item>\n\t\t  </b-tabs>\n\t\t</div>\n\t  </section>\n\t',data:()=>({numbers:Array.from({length:19},((e,t)=>t+6)),selectedInstructor:6,currency:"INR",prices:{INR:{Annual:500,Symbol:"Rs."},USD:{Annual:8,Symbol:"$"}}}),computed:{tabs(){return this.$props.data.data.tabs}},async created(){},mounted(){},methods:{tabChange(){},calculateYearlyPrice(e){const t=this.currency||"INR",n=this.prices[t].Symbol,o=this.prices[t].Annual;return`${n} ${("Basic"===e.title?0:o*this.selectedInstructor).toLocaleString()}/month`}}});const YUNOAccordion=(jQuery,{accordion:function(){Vue.component("yuno-accordion",{props:["data","options"],template:'\n                <section class="yunoAccordion" :class="[options.type]">\n                    <div class="container">\n                        <template v-if="options.type === \'faq\'">\n                            <template v-if="options.hasColumn !== undefined">\n                                <div class="row">\n                                    <div :class="[options.hasColumn]">\n                                        <div class="wrapper">\n                                            <template v-if="options.fetchData !== undefined && options.fetchData">\n                                                <template v-if="data.loading">\n                                                    <b-collapse\n                                                        class="faqCard"\n                                                        animation="slide"\n                                                        v-for="i in loadingResult"\n                                                        :key="i"\n                                                        :open="isOpen == i">\n                                                        <div\n                                                            slot="trigger"\n                                                            slot-scope="props"\n                                                            class="card-header"\n                                                            :class="[props.open ? \'active\' : \'\']"\n                                                            role="button">\n                                                            <p class="card-header-title">\n                                                                <b-skeleton width="50%" active></b-skeleton>\n                                                            </p>\n                                                            <a class="card-header-icon">\n                                                                <b-icon\n                                                                    :icon="props.open ? \'menu-down\' : \'menu-up\'">\n                                                                </b-icon>\n                                                            </a>\n                                                        </div>\n                                                        <div class="card-content">\n                                                            <b-skeleton width="100%" height="80px" active></b-skeleton>\n                                                        </div>\n                                                    </b-collapse>\n                                                </template>\n                                                <template v-if="data.success">\n                                                    <h2 class="sectionTitle">{{ data.title }}</h2>\n                                                    <b-collapse\n                                                        class="faqCard"\n                                                        animation="slide"\n                                                        v-for="(collapse, index) of data.data"\n                                                        :key="index"\n                                                        :open="isOpen == index"\n                                                        @open="isOpen = index">\n                                                        <div\n                                                            slot="trigger"\n                                                            slot-scope="props"\n                                                            class="card-header"\n                                                            :class="[props.open ? \'active\' : \'\']"\n                                                            role="button">\n                                                            <p class="card-header-title">\n                                                                {{ collapse.question }}\n                                                            </p>\n                                                            <a class="card-header-icon">\n                                                                <b-icon\n                                                                    :icon="props.open ? \'menu-down\' : \'menu-up\'">\n                                                                </b-icon>\n                                                            </a>\n                                                        </div>\n                                                        <div class="card-content">\n                                                            <div class="content" v-html="collapse.answer"></div>\n                                                        </div>\n                                                    </b-collapse>\n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <h2 class="sectionTitle">{{ data.title }}</h2>\n                                                <b-collapse\n                                                    class="faqCard"\n                                                    animation="slide"\n                                                    v-for="(collapse, index) of data.list"\n                                                    :key="index"\n                                                    :open="isOpen == index"\n                                                    @open="isOpen = index">\n                                                    <div\n                                                        slot="trigger"\n                                                        slot-scope="props"\n                                                        class="card-header"\n                                                        :class="[props.open ? \'active\' : \'\']"\n                                                        role="button">\n                                                        <p class="card-header-title">\n                                                            {{ collapse.question }}\n                                                        </p>\n                                                        <a class="card-header-icon">\n                                                            <b-icon\n                                                                :icon="props.open ? \'menu-down\' : \'menu-up\'">\n                                                            </b-icon>\n                                                        </a>\n                                                    </div>\n                                                    <div class="card-content">\n                                                        <div class="content" v-html="collapse.answer"></div>\n                                                    </div>\n                                                </b-collapse>\n                                            </template>\n                                        </div>\n                                    </div>\n                                </div>\n                            </template>\n                        </template>\n                    </div>\n                </section>\n            ',data:()=>({isOpen:0,loadingResult:2}),computed:{},async created(){},mounted(){"faq"===this.$props.options.type&&void 0===this.$props.options.fetchData&&this.faqRichSnippet(this.$props.data.list)},methods:{structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},faqRichSnippet(e){if(0!==e.length){let n={"@context":"https://schema.org","@type":"FAQPage",mainEntity:[]};for(var t=0;t<e.length;t++){const o=e[t];let a={"@type":"Question",name:o.question,acceptedAnswer:{"@type":"Answer",text:YUNOCommon.removeTagsFromString(o.answer)}};n.mainEntity.push(a)}this.structuredData(n)}}}})}});Vue.component("yuno-section-scroller",{props:{data:{type:Array,required:!0}},template:'\n    <section class="sectionScroller">\n      <div class="container">\n        <div class="row justify-content-center">\n          <div class="sectionTabs col-md-8">\n            <b-tabs\n              type="is-toggle-rounded"\n              class="d-flex justify-content-center"\n              v-model="activeTab" \n              @input="onTabChange"\n            >\n              <b-tab-item\n                v-for="(tab, index) in data"\n                :key="index"\n                :label="tab.label"\n              >\n              </b-tab-item>\n            </b-tabs>\n          </div>\n        </div>\n      </div>\n    </section>\n  ',data:()=>({activeTab:0,isScrolling:!1}),mounted(){window.addEventListener("scroll",this.debouncedHandleScroll)},beforeDestroy(){window.removeEventListener("scroll",this.debouncedHandleScroll)},created(){this.debouncedHandleScroll=this.debounce(this.handleScroll,50)},methods:{onTabChange(e){this.activeTab=e,this.scrollToSection(this.data[e].targetClass)},scrollToSection(e){const t=document.querySelector(`.${e}`);if(t){const e=100,n=t.getBoundingClientRect().top+window.scrollY-e;this.isScrolling=!0,window.scrollTo({top:n,behavior:"smooth"}),setTimeout((()=>{this.isScrolling=!1}),600)}else console.warn(`Element with class '${e}' not found.`)},handleScroll(){if(this.isScrolling)return;let e=0;this.data.forEach(((t,n)=>{const o=document.querySelector(`.${t.targetClass}`);if(o){o.getBoundingClientRect().top<=150&&(e=n)}})),e!==this.activeTab&&(this.activeTab=e)},debounce(e,t){let n;return function(...o){clearTimeout(n),n=setTimeout((()=>{e.apply(this,o)}),t)}}}}),Vue.component("yuno-banner-v2",{props:["data","options"],template:'\n        <section class="bannerV2">\n            <div class="container">\n                <div class="row">\n                    <div class="col-md-8 offset-md-2 wrapper">\n                        <div class="lftCol">\n                            <h2>\n                                {{ data.title }}\n                            </h2>\n                        </div>\n                        <div class="ritCol">\n                            <b-button tag="a"\n                                :href="data.cta.disabled ? \'#\' : data.cta.url"\n                                :target="data.cta.target"\n                                :disabled="data.cta.disabled"\n                                @click="onPrimaryCTAClick()"\n                                class="yunoPrimaryCTA">\n                                {{ data.cta.label }}\n                            </b-button>\n                            <div class="secondaryCTA" v-if="data.secondaryCTA !== undefined">\n                                <p class="smallerBody">{{ data.secondaryCTA.helperText }}</p>\n                                <a :href="data.secondaryCTA.url" class="plainLink" target="_blank">{{ data.secondaryCTA.label }}</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{onPrimaryCTAClick(){this.$emit("onPrimaryCTAClick")}}}),Vue.component("yuno-carousel-slides",{props:{data:{type:Object,required:!0},options:{type:Object,required:!1}},template:'\n        <section class="carouselSlides">\n\t\t\t<div class="container">\n\t\t\t\t<b-carousel-list v-model="slide" :data="data.items" :items-to-show="1">\n\t\t\t\t\t<template #item="list">\n  \t\t\t\t\t\t<h2 class="headline3 onSurface p-top-larger-times-2 text-center">{{ list.title }}</h2>\n\t\t\t\t\t\t<p class="subtitle1 noBold onSurfaceVariant p-top-larger-times-1 text-center">{{ list.description }}</p>\n\t\t\t\t\t\t<div class="row hero-body d-flex">\n\t\t\t\t\t\t\t<div :class="options?.columns === \'one\' ? \'col-md-12\' : \'col-md-6\'" class="contentWrapper pt-3 col-12 order-2">\n\t\t\t\t\t\t\t\t<h1 class="title headline5 onSurface pb-2">{{ list.name }}</h1>\n\t\t\t\t\t\t\t\t<p class="description subtitle1 noBold onSurfaceVariant pt-2 pb-5">{{ list.excerpt }}</p>\n\t\t\t\t\t\t\t\t<div class="cta" v-if="false">\n\t\t\t\t\t\t\t\t\t<b-button \n\t\t\t\t\t\t\t\t\t\ttag="a"\n\t\t\t\t\t\t\t\t\t\tclass="secondaryCTA"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t{{ list.cta.label }}\n\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t</div>    \n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div :class="options?.columns === \'one\' ? \'col-md-10 offset-md-1\' : \'col-md-6\'"  class="videoWrapper col-12 order-1 ">\n\t\t\t\t\t\t\t\t<video width="100%" autoplay muted loop>\n\t\t\t\t\t\t\t\t\t<source :src="list.video_url" type="video/mp4">\n\t\t\t\t\t\t\t\t\tYour browser does not support the video tag.\n\t\t\t\t\t\t\t\t</video>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</template>\n\t\t\t\t</b-carousel-list>\n\t\t\t\t<div class="carousel-dots text-center">\n\t\t\t\t\t<span\n\t\t\t\t\t\tv-for="(item, index) in data.items"\n\t\t\t\t\t\t:key="index"\n\t\t\t\t\t\t:class="{\'dot\': true, \'active\': index === slide}"\n\t\t\t\t\t\t@click="slide = index">\n\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n        </section>\n    ',data:()=>({slide:0})}),Vue.component("yuno-accordion-media",{props:{data:{type:Object,required:!0}},template:'\n        <section class="accordionMedia">\n            <div class="container">\n                <h2 class="headline3 onSurface text-center">{{ data.title }}</h2>\n                <p v-if="data.titledescription" class="alignC mb-4">{{ data.titledescription }}</p>\n                <div class="row p-top-largest-times-1">\n                    <div class="col-md-6">\n                        <img :src="data.media.image" :alt="data.title" class="img-fluid">\n                    </div>\n                    <div class="col-md-6 ">\n\t\t\t\t\t\t<b-collapse \n\t\t\t\t\t\t\tclass="accordian" \n\t\t\t\t\t\t\tanimation="slide" \n\t\t\t\t\t\t\tv-for="(item, index) in data.items"\n\t\t\t\t\t\t\t:key="index"\n\t\t\t\t\t\t\t:open="isOpen == index"\n\t\t\t\t\t\t\t@open="isOpen = index"\n\t\t\t\t\t\t\t:aria-id="\'accordion-\' + index"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<template #trigger="props">\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass="card-header"\n\t\t\t\t\t\t\t\t\trole="button"\n\t\t\t\t\t\t\t\t\t:aria-controls="\'accordion-\' + index"\n\t\t\t\t\t\t\t\t\t:aria-expanded="props.open"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<a class="card-header-icon">\n\t\t\t\t\t\t\t\t\t\t<span class="material-icons headline6 onSurface">{{ props.open ? \'expand_less\' : \'expand_more\' }}</span>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t<p class="card-header-title headline6 onSurface">\n\t\t\t\t\t\t\t\t\t\t{{ item.title }}\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<div class="card-content">\n\t\t\t\t\t\t\t\t<div class="content subtitle2 noBold onSurfaceVariant">\n\t\t\t\t\t\t\t\t\t{{ item.description }}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</b-collapse>\n\t\t\t\t\t</div>\n                </div>\n            </div>\n        </section>\n    ',data:()=>({isOpen:0}),methods:{}}),Vue.component("yuno-content-table",{props:{data:{type:Object,required:!0}},template:'\n    <section class="contentTable">\n      <div class="container">\n        <h2 class="headline3 text-center">{{ data.title }}</h2>\n        <div class="tableWrapper p-top-largest-times-1">\n          <table v-for="(item, index) in data.table" :key="index">\n            <thead>\n              <tr>\n                <th colspan="2">\n                  <h3 class="headline5">{{ item.title }}</h3>\n                  <p class="onSurfaceVariant subtitle1 noBold pt-2">{{ item.description }}</p>\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for="(row, rowIndex) in item.items" :key="rowIndex">\n                <td>\n                  <div class="row wrapper">\n                    <div class="feature col-12 col-md-4">\n                      <h3 class="headline6 onSurface">{{ row.title }}</h3>\n                    </div>\n                    <div class="description col-12 col-md-8">\n                      <p class="onSurfaceVariant subtitle2 noBold">{{ row.subtitle }}</p>\n                    </div>\n                  </div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </section>\n  ',data:()=>({})}),Vue.component("yuno-two-column-faq",{props:{data:{type:Object,required:!0}},template:'\n        <section class="twoColumnFAQ">\n            <div class="container">\n                <h2 class="headline3 text-center">{{ data.title }}</h2>\n                <div class="faq-list p-top-largest-times-1">\n                    <template v-for="(item, index) in data.items">\n                        <div class="row" :key="index">\n                            <div class="question col-12 col-md-6">\n                                <h2 class="headline5 onSurface">{{ item.question }}</h2>\n                            </div>\n                            <div class="answer col-12 col-md-6" >\n                            \t<p class="subtitle1 noBold onSurfaceVariant">{{ item.answer }}</p>\n                            </div>\n                        </div>\n                    </template>\n                </div>\n            </div>\n        </section>\n    \n    ',data:()=>({})}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),YUNOAccordion.accordion(),Vue.component("yuno-academy",{template:'\n        <yuno-page-grid\n            @onUserInfo="onUserInfo"\n        >\n            <template v-slot:main>\n                <template v-if="moduleWithoutTab.loading">\n                    <div class="container hasTopGap">\n                        <figure class="infiniteSpinner">\n                            <img width="150" height="75" :src="manageImgURL(\'infinite-spinner.svg\')" alt="Yuno Learning">\n                        </figure>\n                    </div>\n                </template>\n                <template v-else>\n                    <yuno-grid-detail-view\n                        type="heroBanner"\n                        h1="Instructor-led live learning, powered by AI"\n                        description="Turn underutilized seats into revenue streams, and dramatically improve learning quality"\n                        :img="manageImgURL(\'academy.png\')"\n                        :imgOrder="1"\n                        :featureList="heroFeatureList"\n                        :featureListOrder="2"\n                        @ctaClick="applyForAccess"\n                        :cta="{\n                            \'title\': \'Create Academy\',\n                            \'url\': createAcademyURL,\n                            \'target\': \'_self\',\n                            \'disabled\': createAcademyDisabled    \n                        }"\n                    >\n                    </yuno-grid-detail-view>\n                    <yuno-section-scroller\n                        :data="sectionsTabs"\n                    >\n                    </yuno-section-scroller>\n                    <yuno-carousel-slides\n                      :data="carouselItems"\n\t\t\t\t\t  :options="{\n\t\t\t\t\t\t\'columns\': \'one\',\n\t\t\t\t\t  }"\n                    >\n                    </yuno-carousel-slides>\n\t\t\t\t\t<yuno-accordion-media\n\t\t\t\t\t\t:data="accordionMedia"\n\t\t\t\t\t>\n\t\t\t\t\t</yuno-accordion-media>\n\t\t\t\t\t<yuno-content-table\n\t\t\t\t\t\t:data="contentTable"\n\t\t\t\t\t>\n\t\t\t\t\t</yuno-content-table>\n                  <yuno-tab-with-cards\n                      :data="pricing"\n                      :hideTabs="true"\n                    >\n                  </yuno-tab-with-cards>\n                  <yuno-two-column-faq\n                      :data="faq" \n                    >\n                  </yuno-two-column-faq>\n                  <yuno-banner-v2\n                      :data="interested"\n                      @onPrimaryCTAClick="applyForAccess"\n                  >\n                  </yuno-banner-v2> \n                </template>\n            </template>\n        </yuno-page-grid>\n    ',data(){return{createAcademyDisabled:!0,createAcademyURL:"/login/?type=signup",isMiniSidebar:!1,authorizedRoles:["Instructor","Learner","yuno-admin","org-admin","Counselor"],sectionsTabs:[{label:"Features",targetClass:"contentTable"},{label:"Pricing",targetClass:"tabsWithCards"},{label:"FAQs",targetClass:"twoColumnFAQ"}],carouselItems:{items:[{title:"Turn Visitors into Students—Automatically",description:"Don’t have your students fill lead forms. Have them book demo classes",name:"Book A Demo Class",excerpt:"Students book demos instantly, Yuno schedules automatically. No manual forms or follow-ups—just seamless scheduling that boosts your conversions.",video_url:this.$store.state.themeURL+"/assets/images/Book_a_Demo_class.mp4",cta:{label:"Call To Action"}},{title:"Find the Perfect Instructor Instantly",description:"Match instructors to upcoming batches - powered by AI",name:"Find an instructor",excerpt:"Stop juggling instructor skills and schedules. Yuno AI instantly finds the right instructor available to teach your next batch—saving you time and stress.",video_url:this.$store.state.themeURL+"/assets/images/Find_instructor.mp4",cta:{label:"Call To Action"}},{title:"Build Courses in Minutes, Not Days",description:"Effortless course creation with AI",name:"AI Course Creation",excerpt:"Tell Yuno your course goals, duration, and format—our AI handles the rest. Create complete, engaging courses effortlessly and reclaim your valuable time.",video_url:this.$store.state.themeURL+"/assets/images/Course_creation.mp4",cta:{label:"Call To Action"}},{title:"Batch Management Made Simple",description:"Increase bottom line - visualize, optimize, and maximize your batch utilization",name:"Batch Management",excerpt:"Easily track batch timings, seat availability, and capacity at a glance. Yuno helps you maximize enrollment and boost your academy’s profitability.",video_url:this.$store.state.themeURL+"/assets/images/Batch_insights.mp4",cta:{label:"Call To Action"}},{title:"Attendance Insights that Drive Results",description:"Understand attendance to improve learning outcomes and instructor quality",name:"Attendance Insights",excerpt:"Spot attendance patterns instantly. Yuno highlights student progress, instructor impact, and curriculum effectiveness—empowering you to take action fast.",video_url:this.$store.state.themeURL+"/assets/images/Attendance_insights.mp4",cta:{label:"Call To Action"}}]},accordionMedia:{title:"Your Branded App. Free!",media:{image:this.$store.state.themeURL+"/assets/images/your_brand_app.png"},items:[{title:"Your academy. Your app. Your brand",description:"Get your personalized mobile app, custom domain, and branding for free. Offer students access exclusively to your courses—or showcase the entire Yuno catalog. Customize freely, grow confidently."},{title:"Live Classes. Instant Recordings",description:"Let your students easily view upcoming classes, join live sessions, and revisit recordings anytime. A complete learning experience—seamlessly integrated into your branded app."},{title:"Unlimited Study Material",description:"Offer your students instant access to an extensive library of study resources—all seamlessly integrated into your own branded app."},{title:"Hundreds of Tests. Unlimited Practice",description:"Empower your students with hundreds of ready-to-use practice tests, directly accessible from your branded app."}]},contentTable:{title:"Features",table:[{title:"Live Teaching, in Group or 1-on-1",description:"All the tools you need to teach the way you want: in group classes or 1-on-1",items:[{title:"Course Creation",subtitle:"Create detailed courses with customizable curriculums and pricing",description:"A course on Yuno serves as the core offering that students enroll in.Each course is built around live classes, designed to cover a specific subject area over a defined duration, typically spanning several weeks. Courses are structured with a set number of live classes and follow a fixed daily or weekly schedule. They also come with a pre-determined enrollment fee, ensuring clarity and consistency for both students and academies. This structured approach allows academies to deliver focused and high-quality learning experiences."},{title:"Batch Creation",subtitle:"Manage batch schedules, batch capacity and instructor assignments effortlessly",description:"A batch represents a specific group of students enrolled in a particular course, assigned to an instructor. Each batch is designed to match the course’s duration and structure while defining key parameters such as start and end dates, class days, and timing. For example, a batch may accommodate up to 10 students, starting on October 12th, with classes scheduled on Tuesdays, Thursdays, and Saturdays at 6 PM. Batches provide the flexibility to manage multiple groups under the same course, enabling academies to cater to varied student availability while maintaining a consistent learning experience."},{title:"Enrollment Management",subtitle:"Track student enrollment timelines with automated start and end date allocation",description:"An enrollment refers to a student’s participation in a specific batch of a course. If a student joins two different batches, each is treated as a separate enrollment. The platform automatically assigns start and end dates to each enrollment, ensuring structured timelines. It also tracks ongoing registrations, overdue enrollments, and pending payments, providing a real-time overview for smooth operations, making it easier to handle your student database efficiently."},{title:"Live Classes with Zoom or Google Meet",subtitle:"Easily integrate your own license of Zoom or Google Meet to deliver virtual classes",description:"Live classes are an integral part of a course, conducted in real time to facilitate interactive learning. With integrated Zoom and Google Meet, scheduling and managing live sessions becomes straightforward. Students receive automatic reminders and can join sessions seamlessly, while instructors can monitor attendance and engagement, making the learning experience dynamic and engaging."}]},{title:"Teach alone or bring a team of instructors",description:"Everything you need to manage a large number of instructors who work for your academy",items:[{title:"Instructor Management",subtitle:"Map courses to instructors, track availability, and manage workloads",description:"Whether your academy has a single instructor or a team of hundreds, managing them is seamless. You can add or remove instructors, assign them to specific courses or batches, and monitor their workload efficiently. This flexibility ensures that instructors are aligned with the academy’s needs, whether for a single batch or multiple concurrent sessions, maintaining smooth operations at all scales."},{title:"Batch Management",subtitle:"Manage batch schedules, batch capacity and instructor assignments effortlessly",description:"Once a batch is created, managing it involves tracking enrollment capacity, monitoring attendance, and balancing instructor workloads. Batches can be locked once full to maintain consistency or adjusted to accommodate changes like transferring students or extending enrollment durations. These tools ensure efficient batch operations and an optimal learning experience for students."},{title:"Instructor Reviews",subtitle:"Track instructor performance and feedback",description:"Instructor reviews provide a comprehensive overview of instructor performance, including attendance, engagement, and feedback from students. This data helps academies evaluate instructor effectiveness and make informed decisions about instructor assignments and training."}]},{title:"Enable Students to Self-Study",description:"Accompanied by live classes, your students need guided self-study to achieve learning outcomes. We help you achieve that",items:[{title:"Access to content on Yuno",subtitle:"Leverage Yuno’s library of expert-reviewed content for your courses",description:"With Yuno’s vast repository of 2000+ expert-reviewed study materials, academies can enhance their courses by incorporating high-quality content. The platform provides access to videos, articles, PDFs, and e-books, offering a diverse range of learning resources. These materials are designed to complement live sessions, making learning more effective and engaging for students."},{title:"Publish Content by Type",subtitle:"Upload videos, PDFs, and e-books to create a diverse learning experience",description:"Academies can create and publish custom content in multiple formats, including videos, articles, PDFs, and e-books, directly on Yuno. Upload videos to the platform, where storage and seamless streaming are handled automatically. Use AI-powered tools to write and share articles quickly and effectively, ensuring professional quality. Upload PDF documents to allow students to download study materials effortlessly, while gaining insights into who accessed them."},{title:"Practice Tests",subtitle:"Design mock exams and track results to prepare students for real tests",description:"A practice test is a mock exam designed to simulate real test conditions. Leveraging AI-powered features, the test creation process is simplified, enabling you to design and distribute tests efficiently. These tests help students evaluate their readiness and identify areas for improvement. Instructors can create practice tests on the platform and analyze performance data to provide targeted feedback. This feature ensures students are well-prepared and confident for their exams."}]},{title:"Data Insights",description:"You'll teach better if you have insights from data. Know which students are not attending your classes regularly, which instructors get top-level feedback and much more",items:[{title:"Enrollment Insights",subtitle:"Monitor batch capacity, pending enrollments, and dropout trends effortlessly",description:"Gain a deeper understanding of your students with detailed enrollment insights. Track student engagement levels, analyze course popularity, and review student demographics to make informed decisions. These insights enable you to optimize course offerings, adjust batch sizes, and refine schedules to better align with student preferences and needs."},{title:"Attendance Insights",subtitle:"Monitor and analyze attendance rates for every student and batch",description:"Tracking attendance is effortless with Yuno, which records not just student presence but also the duration spent in each class. These insights provide a deeper understanding of student engagement and help instructors identify attendance patterns. The data allows academies to intervene early with students who are falling behind, ensuring no learner is left out."},{title:"Instructor Insights",subtitle:"Gather feedback and track engagement through attendance",description:"Instructor insights offer data on teaching hours, attendance rates, and feedback from students. These metrics help academies assess instructor effectiveness and plan schedules to balance workloads. By identifying top-performing instructors, academies can also build mentorship programs or expand high-demand courses."},{title:"Content Insights",subtitle:"Track engagement with study materials to optimize your content strategy",description:"Content insights analyze how students engage with study materials. Metrics such as completion rates, time spent on videos, and feedback scores help academies refine their content strategy. This ensures that the materials provided are effective, relevant, and aligned with learning objectives."}]},{title:"Money on the Table",description:"To sell, you need student leads. For leads, you need to promote your academy and for that, you need tools",items:[{title:"Lead generation through demo requests",subtitle:"Capture leads with demo requests and track their enrollment journey",description:"Publicly listing your courses allows students to easily discover and explore your offerings. Interested learners can submit demo requests, providing them with a firsthand experience of your teaching quality. These demos not only showcase your academy's courses but also serve as a powerful lead generation tool, attracting potential students at no additional cost. This direct interaction with prospective students creates opportunities to increase enrollments and drive revenue growth."},{title:"E-mail & Whatsapp Campaigns",subtitle:"Run automated email and WhatsApp campaigns to convert leads faster",description:"Create impactful communication strategies with user-friendly tools for email and WhatsApp campaigns. These features enable academies to deliver targeted messages, course announcements, payment reminders, or promotional content directly to students and prospects. The platform simplifies campaign creation, management, and performance monitoring, ensuring stronger engagement and deeper connections with your audience."},{title:"Payment Collection",subtitle:"Collect fees on time with automated payment reminders and tracking",description:"Streamline fee collection with flexible payment options, including multiple currencies, installment plans, and Razorpay integration. The platform automates reminders for pending and overdue payments, offering transparency for both academies and students. These tools not only simplify financial operations but also enhance the student experience by making payments seamless and hassle-free."}]}]},pricing:{title:"Pricing",description:"Free forever for unlimited learners and instructors. Pay only when you want more leads.",data:{activeTab:0,tabs:[{label:"Annual (20% off)",isActive:!0,data:[{title:"Basic",subtitle:"Everything you need to run an academy",price:null,perInstructorPrice:"0",priceStatement:"free\nforever",total:"0/month",items:[{label:"Unlimited learners",tooltip:{hasTooltip:!1,label:""}},{label:"Unlimited instructors",tooltip:{hasTooltip:!1,label:""}},{label:"All platform features",tooltip:{hasTooltip:!1,label:""}},{label:"Payment collection charges: on actuals",tooltip:{hasTooltip:!0,label:"Only pay the exact fees charged by the payment gateway—no additional markup by Yuno"}},{label:"Developer API Access",tooltip:{hasTooltip:!0,label:"Complete API access to integrate Yuno data into your own apps or custom workflows"}},{label:"White labeled app",tooltip:{hasTooltip:!0,label:"Quickly deploy web and Android apps with your domain & brand"}}],cta:"Subscribe on Razorpay"},{title:"Plus",subtitle:"Generate new leads for your academy",price:null,perInstructorPrice:"4900",priceStatement:"per month",total:"4900/month Billed monthly",items:[{label:"Unlimited learners",tooltip:{hasTooltip:!1,label:""}},{label:"Unlimited instructors",tooltip:{hasTooltip:!1,label:""}},{label:"All platform features",tooltip:{hasTooltip:!1,label:""}},{label:"Payment collection charges: on actuals",tooltip:{hasTooltip:!0,label:"Only pay the exact fees charged by the payment gateway—no additional markup by Yuno"}},{label:"Developer API Access",tooltip:{hasTooltip:!0,label:"Complete API access to integrate Yuno data into your own apps or custom workflows"}},{label:"White labeled app",tooltip:{hasTooltip:!0,label:"Quickly deploy web and Android apps with your domain & brand"}},{label:"3x search visibility",tooltip:{hasTooltip:!0,label:"Courses published by Pro academies get 3x search visibility"}}],cta:"Subscribe on Razorpay"}]}]}},faqOptions:{type:"faq",hasColumn:"col-md-10 offset-md-1"},faq:{title:"FAQs",items:[{question:" Can I track the total hours put in by instructors across different courses and batches?",answer:"Yes, you can track the total hours spent by your instructors in a specific class, in all classes of a batch, and in all classes of various batches of a course."},{question:"Is there a feature to calculate and manage instructor payroll based on hours worked?",answer:"Yes, Yuno provides a downloadable CSV file (compatible with Excel and Google Sheets) containing detailed records of each instructor, their classes, hours spent per class, and more, allowing you to calculate instructor payroll with ease."},{question:"Can I monitor instructor attendance and identify trends in their engagement?",answer:"Yes, you can view both student and instructor attendance. While reviewing student attendance, you can apply filters based on the instructor teaching them, the course they are enrolled in, and other criteria."},{question:"Are there tools to analyze performance trends using charts and visual reports?",answer:"We collect all the necessary data to generate visualizations for performance analysis but this feature is not yet available - it's under development. However, we can provide the data in a CSV format, which you can utilize for your analysis."},{question:"Can the platform generate rankings for students based on test scores and participation?",answer:"Yes, we generate rankings for students based on their test scores. Rankings based on participation will be introduced in an upcoming update."},{question:"Can instructors create and use mock tests or quizzes  within the curriculum?",answer:"Yes, that's possible. Instructors have full access to Yuno's extensive resource library, which includes thousands of articles, videos, PDFs, and practice tests. They can seamlessly share these resources with students, either with an entire batch or a selected group of students, as needed."},{question:" Are detailed performance reports generated for each test, including time taken and areas of improvement?",answer:"You can check the actual response of a student of each question in the test and also their overall score. However, you cannot yet see how long did a student take to answer a specific question. That feature will soon be releases."},{question:"Can the platform identify students with low attendance or poor performance?",answer:"Yes, attendance insights allow you to view students categorized by their attendance levels. For instance, you can generate lists of students with attendance below 50%, below 30%, and other specified thresholds."},{question:" Is there a way to automatically send notifications to red-flagged students?",answer:"Each academy defines red-flag students differently, so we don’t provide automated notifications. However, you can generate a list of red-flag students based on your criteria and send them notifications manually."}]},heroFeatureList:[{title:"Live classes, in a group or 1-on-1",description:"Integrated with Google Meet and Zoom; but does a lot more!"},{title:"Go global. Instantly",description:"Yuno has everything you need to build a global business"},{title:"Teach alone or bring a team of instructors",description:"Free forever for unlimited learners and instructors"}],interested:{title:"Ready to start your own academy?",cta:{label:"Create Academy",url:"/login/?type=signup",target:"_self",disabled:!0}}}},computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","moduleWithoutTab"]),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},isPageReady(){return this.user.isLoggedin?this.header.success&&this.footer.success&&this.userInfo.success:this.header.success&&this.footer.success},isUserLoading(){return this.userInfo.loading},isUserReady(){return!this.user.isLoggedin||this.userInfo.success},searchBar(){return this.user.isLoggedin&&"Learner"===this.userRole.data}},async created(){this.fetchFooter(),this.emitEvents()},methods:{activeOrg(){const e=this.userInfo.data.current_state.org_id;return e||0},gotOrgInfo(e){const{response:{data:{code:t,data:n}={}}={}}=e;if(200===t){n.yuno_academy_subscription.academies.length>0?(this.createAcademyDisabled=!0,this.interested.cta.disabled=this.createAcademyDisabled):(this.createAcademyDisabled=!1,this.createAcademyURL="/create-new-academy/",this.interested.cta.url=this.createAcademyURL,this.interested.cta.disabled=this.createAcademyDisabled)}},fetchOrgInfo(){const e={apiURL:YUNOCommon.config.generic("org",!1,!1,this.activeOrg()),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:this.gotOrgInfo};this.$store.dispatch("fetchData",e)},applyForAccess(){sessionStorage.clear();const e="academyOwner";if(!this.user.isLoggedin){const t={url:"",pageTitle:"",category:"",role:{academyOwner:"org-admin",instructor:"instructor"}[e]||"",hasAcademy:!0};sessionStorage.setItem("landingPage",JSON.stringify(t))}},manageImgURL(e){return`${this.$store.state.themeURL}/assets/images/${e}`},fetchModules(){this.user.isLoggedin&&"org-admin"===this.userRole.data&&this.fetchOrgInfo()},onUserInfo(e){this.fetchModules()},fetchFooter(){const e={apiURL:YUNOCommon.config.footerAPI(),module:"gotData",store:"footer",callback:!1};this.$store.dispatch("fetchData",e)},onMini(e){this.isMiniSidebar=e},emitEvents(){Event.$on("checkLoggedInState",(e=>{e||(this.createAcademyDisabled=!1,this.interested.cta.disabled=this.createAcademyDisabled,this.fetchModules())}))}}});