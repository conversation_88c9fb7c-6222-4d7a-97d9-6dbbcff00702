/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,r){for(var i=-1,a=null==e?0:e.length;++i<a;){var s=e[i];t(r,s,n(s),e)}return r}function n(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function r(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function i(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function a(e,t){for(var n=-1,r=null==e?0:e.length,i=0,a=[];++n<r;){var s=e[n];t(s,n,e)&&(a[i++]=s)}return a}function s(e,t){return!(null==e||!e.length)&&g(e,t,0)>-1}function o(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}function l(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}function u(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}function c(e,t,n,r){var i=-1,a=null==e?0:e.length;for(r&&a&&(n=e[++i]);++i<a;)n=t(n,e[i],i,e);return n}function d(e,t,n,r){var i=null==e?0:e.length;for(r&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function f(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function h(e){return e.match(Ke)||[]}function p(e,t,n){var r;return n(e,(function(e,n,i){if(t(e,n,i))return r=n,!1})),r}function m(e,t,n,r){for(var i=e.length,a=n+(r?1:-1);r?a--:++a<i;)if(t(e[a],a,e))return a;return-1}function g(e,t,n){return t==t?function(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}(e,t,n):m(e,_,n)}function v(e,t,n,r){for(var i=n-1,a=e.length;++i<a;)if(r(e[i],t))return i;return-1}function _(e){return e!=e}function y(e,t){var n=null==e?0:e.length;return n?S(e,t)/n:X}function b(e){return function(t){return null==t?H:t[e]}}function w(e){return function(t){return null==e?H:e[t]}}function k(e,t,n,r,i){return i(e,(function(e,i,a){n=r?(r=!1,e):t(n,e,i,a)})),n}function S(e,t){for(var n,r=-1,i=e.length;++r<i;){var a=t(e[r]);a!==H&&(n=n===H?a:n+a)}return n}function C(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function M(e){return e?e.slice(0,W(e)+1).replace(Be,""):e}function D(e){return function(t){return e(t)}}function R(e,t){return l(t,(function(t){return e[t]}))}function O(e,t){return e.has(t)}function T(e,t){for(var n=-1,r=e.length;++n<r&&g(t,e[n],0)>-1;);return n}function x(e,t){for(var n=e.length;n--&&g(t,e[n],0)>-1;);return n}function Y(e){return"\\"+Jt[e]}function I(e){return zt.test(e)}function L(e){return Vt.test(e)}function U(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function N(e,t){return function(n){return e(t(n))}}function A(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var s=e[n];s!==t&&s!==V||(e[n]=V,a[i++]=n)}return a}function P(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function E(e){return I(e)?function(e){for(var t=Ht.lastIndex=0;Ht.test(e);)++t;return t}(e):pn(e)}function F(e){return I(e)?function(e){return e.match(Ht)||[]}(e):function(e){return e.split("")}(e)}function W(e){for(var t=e.length;t--&&Ge.test(e.charAt(t)););return t}function j(e){return e.match($t)||[]}var H,$="Expected a function",z="__lodash_hash_undefined__",V="__lodash_placeholder__",B=16,G=32,q=64,Z=128,J=256,K=1/0,Q=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,re=[["ary",Z],["bind",1],["bindKey",2],["curry",8],["curryRight",B],["flip",512],["partial",G],["partialRight",q],["rearg",J]],ie="[object Arguments]",ae="[object Array]",se="[object Boolean]",oe="[object Date]",le="[object Error]",ue="[object Function]",ce="[object GeneratorFunction]",de="[object Map]",fe="[object Number]",he="[object Object]",pe="[object Promise]",me="[object RegExp]",ge="[object Set]",ve="[object String]",_e="[object Symbol]",ye="[object WeakMap]",be="[object ArrayBuffer]",we="[object DataView]",ke="[object Float32Array]",Se="[object Float64Array]",Ce="[object Int8Array]",Me="[object Int16Array]",De="[object Int32Array]",Re="[object Uint8Array]",Oe="[object Uint8ClampedArray]",Te="[object Uint16Array]",xe="[object Uint32Array]",Ye=/\b__p \+= '';/g,Ie=/\b(__p \+=) '' \+/g,Le=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ue=/&(?:amp|lt|gt|quot|#39);/g,Ne=/[&<>"']/g,Ae=RegExp(Ue.source),Pe=RegExp(Ne.source),Ee=/<%-([\s\S]+?)%>/g,Fe=/<%([\s\S]+?)%>/g,We=/<%=([\s\S]+?)%>/g,je=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,He=/^\w*$/,$e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ze=/[\\^$.*+?()[\]{}|]/g,Ve=RegExp(ze.source),Be=/^\s+/,Ge=/\s/,qe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ze=/\{\n\/\* \[wrapped with (.+)\] \*/,Je=/,? & /,Ke=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Qe=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,rt=/^0b[01]+$/i,it=/^\[object .+?Constructor\]$/,at=/^0o[0-7]+$/i,st=/^(?:0|[1-9]\d*)$/,ot=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ut=/['\n\r\u2028\u2029\\]/g,ct="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",ft="\\u2700-\\u27bf",ht="a-z\\xdf-\\xf6\\xf8-\\xff",pt="A-Z\\xc0-\\xd6\\xd8-\\xde",mt="\\ufe0e\\ufe0f",gt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",_t="["+ct+"]",yt="["+gt+"]",bt="["+dt+"]",wt="\\d+",kt="["+ft+"]",St="["+ht+"]",Ct="[^"+ct+gt+wt+ft+ht+pt+"]",Mt="\\ud83c[\\udffb-\\udfff]",Dt="[^"+ct+"]",Rt="(?:\\ud83c[\\udde6-\\uddff]){2}",Ot="[\\ud800-\\udbff][\\udc00-\\udfff]",Tt="["+pt+"]",xt="\\u200d",Yt="(?:"+St+"|"+Ct+")",It="(?:"+Tt+"|"+Ct+")",Lt="(?:['’](?:d|ll|m|re|s|t|ve))?",Ut="(?:['’](?:D|LL|M|RE|S|T|VE))?",Nt="(?:"+bt+"|"+Mt+")"+"?",At="["+mt+"]?",Pt=At+Nt+("(?:"+xt+"(?:"+[Dt,Rt,Ot].join("|")+")"+At+Nt+")*"),Et="(?:"+[kt,Rt,Ot].join("|")+")"+Pt,Ft="(?:"+[Dt+bt+"?",bt,Rt,Ot,_t].join("|")+")",Wt=RegExp(vt,"g"),jt=RegExp(bt,"g"),Ht=RegExp(Mt+"(?="+Mt+")|"+Ft+Pt,"g"),$t=RegExp([Tt+"?"+St+"+"+Lt+"(?="+[yt,Tt,"$"].join("|")+")",It+"+"+Ut+"(?="+[yt,Tt+Yt,"$"].join("|")+")",Tt+"?"+Yt+"+"+Lt,Tt+"+"+Ut,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",wt,Et].join("|"),"g"),zt=RegExp("["+xt+ct+dt+mt+"]"),Vt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Bt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Gt=-1,qt={};qt[ke]=qt[Se]=qt[Ce]=qt[Me]=qt[De]=qt[Re]=qt[Oe]=qt[Te]=qt[xe]=!0,qt[ie]=qt[ae]=qt[be]=qt[se]=qt[we]=qt[oe]=qt[le]=qt[ue]=qt[de]=qt[fe]=qt[he]=qt[me]=qt[ge]=qt[ve]=qt[ye]=!1;var Zt={};Zt[ie]=Zt[ae]=Zt[be]=Zt[we]=Zt[se]=Zt[oe]=Zt[ke]=Zt[Se]=Zt[Ce]=Zt[Me]=Zt[De]=Zt[de]=Zt[fe]=Zt[he]=Zt[me]=Zt[ge]=Zt[ve]=Zt[_e]=Zt[Re]=Zt[Oe]=Zt[Te]=Zt[xe]=!0,Zt[le]=Zt[ue]=Zt[ye]=!1;var Jt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Kt=parseFloat,Qt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,rn=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=rn&&rn.exports===nn,sn=an&&Xt.process,on=function(){try{var e=rn&&rn.require&&rn.require("util").types;return e||sn&&sn.binding&&sn.binding("util")}catch(e){}}(),ln=on&&on.isArrayBuffer,un=on&&on.isDate,cn=on&&on.isMap,dn=on&&on.isRegExp,fn=on&&on.isSet,hn=on&&on.isTypedArray,pn=b("length"),mn=w({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),gn=w({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=w({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),_n=function w(Ge){function Ke(e){if(Wi(e)&&!xo(e)&&!(e instanceof ft)){if(e instanceof dt)return e;if(Ta.call(e,"__wrapped__"))return fi(e)}return new dt(e)}function ct(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=H}function ft(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function pt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new mt;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new pt(e)).size}function _t(e,t){var n=xo(e),r=!n&&To(e),i=!n&&!r&&Io(e),a=!n&&!r&&!i&&Po(e),s=n||r||i||a,o=s?C(e.length,ka):[],l=o.length;for(var u in e)!t&&!Ta.call(e,u)||s&&("length"==u||i&&("offset"==u||"parent"==u)||a&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Jr(u,l))||o.push(u);return o}function yt(e){var t=e.length;return t?e[Ln(0,t-1)]:H}function bt(e,t){return li(cr(e),Tt(t,0,e.length))}function wt(e){return li(cr(e))}function kt(e,t,n){(n===H||Ii(e[t],n))&&(n!==H||t in e)||Rt(e,t,n)}function St(e,t,n){var r=e[t];Ta.call(e,t)&&Ii(r,n)&&(n!==H||t in e)||Rt(e,t,n)}function Ct(e,t){for(var n=e.length;n--;)if(Ii(e[n][0],t))return n;return-1}function Mt(e,t,n,r){return Rs(e,(function(e,i,a){t(r,e,n(e),a)})),r}function Dt(e,t){return e&&dr(t,ea(t),e)}function Rt(e,t,n){"__proto__"==t&&Ga?Ga(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function Ot(e,t){for(var n=-1,r=t.length,i=ma(r),a=null==e;++n<r;)i[n]=a?H:Qi(e,t[n]);return i}function Tt(e,t,n){return e==e&&(n!==H&&(e=e<=n?e:n),t!==H&&(e=e>=t?e:t)),e}function xt(e,t,r,i,a,s){var o,l=1&t,u=2&t,c=4&t;if(r&&(o=a?r(e,i,a,s):r(e)),o!==H)return o;if(!Fi(e))return e;var d=xo(e);if(d){if(o=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Ta.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return cr(e,o)}else{var f=Fs(e),h=f==ue||f==ce;if(Io(e))return ir(e,l);if(f==he||f==ie||h&&!a){if(o=u||h?{}:qr(e),!l)return u?function(e,t){return dr(e,Es(e),t)}(e,function(e,t){return e&&dr(t,ta(t),e)}(o,e)):function(e,t){return dr(e,Ps(e),t)}(e,Dt(o,e))}else{if(!Zt[f])return a?e:{};o=function(e,t,n){var r=e.constructor;switch(t){case be:return ar(e);case se:case oe:return new r(+e);case we:return function(e,t){return new e.constructor(t?ar(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case ke:case Se:case Ce:case Me:case De:case Re:case Oe:case Te:case xe:return sr(e,n);case de:return new r;case fe:case ve:return new r(e);case me:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case ge:return new r;case _e:return function(e){return Cs?ba(Cs.call(e)):{}}(e)}}(e,f,l)}}s||(s=new vt);var p=s.get(e);if(p)return p;s.set(e,o),Ao(e)?e.forEach((function(n){o.add(xt(n,t,r,n,e,s))})):Uo(e)&&e.forEach((function(n,i){o.set(i,xt(n,t,r,i,e,s))}));var m=d?H:(c?u?Wr:Fr:u?ta:ea)(e);return n(m||e,(function(n,i){m&&(n=e[i=n]),St(o,i,xt(n,t,r,i,e,s))})),o}function Yt(e,t,n){var r=n.length;if(null==e)return!r;for(e=ba(e);r--;){var i=n[r],a=t[i],s=e[i];if(s===H&&!(i in e)||!a(s))return!1}return!0}function It(e,t,n){if("function"!=typeof e)throw new Sa($);return Hs((function(){e.apply(H,n)}),t)}function Lt(e,t,n,r){var i=-1,a=s,u=!0,c=e.length,d=[],f=t.length;if(!c)return d;n&&(t=l(t,D(n))),r?(a=o,u=!1):t.length>=200&&(a=O,u=!1,t=new gt(t));e:for(;++i<c;){var h=e[i],p=null==n?h:n(h);if(h=r||0!==h?h:0,u&&p==p){for(var m=f;m--;)if(t[m]===p)continue e;d.push(h)}else a(t,p,r)||d.push(h)}return d}function Ut(e,t){var n=!0;return Rs(e,(function(e,r,i){return n=!!t(e,r,i)})),n}function Nt(e,t,n){for(var r=-1,i=e.length;++r<i;){var a=e[r],s=t(a);if(null!=s&&(o===H?s==s&&!zi(s):n(s,o)))var o=s,l=a}return l}function At(e,t){var n=[];return Rs(e,(function(e,r,i){t(e,r,i)&&n.push(e)})),n}function Pt(e,t,n,r,i){var a=-1,s=e.length;for(n||(n=Zr),i||(i=[]);++a<s;){var o=e[a];t>0&&n(o)?t>1?Pt(o,t-1,n,r,i):u(i,o):r||(i[i.length]=o)}return i}function Et(e,t){return e&&Ts(e,t,ea)}function Ft(e,t){return e&&xs(e,t,ea)}function Ht(e,t){return a(t,(function(t){return Ai(e[t])}))}function $t(e,t){for(var n=0,r=(t=nr(t,e)).length;null!=e&&n<r;)e=e[ui(t[n++])];return n&&n==r?e:H}function zt(e,t,n){var r=t(e);return xo(e)?r:u(r,n(e))}function Vt(e){return null==e?e===H?"[object Undefined]":"[object Null]":Ba&&Ba in ba(e)?function(e){var t=Ta.call(e,Ba),n=e[Ba];try{e[Ba]=H;var r=!0}catch(e){}var i=Ia.call(e);return r&&(t?e[Ba]=n:delete e[Ba]),i}(e):function(e){return Ia.call(e)}(e)}function Jt(e,t){return e>t}function Xt(e,t){return null!=e&&Ta.call(e,t)}function en(e,t){return null!=e&&t in ba(e)}function nn(e,t,n){for(var r=n?o:s,i=e[0].length,a=e.length,u=a,c=ma(a),d=1/0,f=[];u--;){var h=e[u];u&&t&&(h=l(h,D(t))),d=as(h.length,d),c[u]=!n&&(t||i>=120&&h.length>=120)?new gt(u&&h):H}h=e[0];var p=-1,m=c[0];e:for(;++p<i&&f.length<d;){var g=h[p],v=t?t(g):g;if(g=n||0!==g?g:0,!(m?O(m,v):r(f,v,n))){for(u=a;--u;){var _=c[u];if(!(_?O(_,v):r(e[u],v,n)))continue e}m&&m.push(v),f.push(g)}}return f}function rn(t,n,r){var i=null==(t=ii(t,n=nr(n,t)))?t:t[ui(vi(n))];return null==i?H:e(i,t,r)}function sn(e){return Wi(e)&&Vt(e)==ie}function on(e,t,n,r,i){return e===t||(null==e||null==t||!Wi(e)&&!Wi(t)?e!=e&&t!=t:function(e,t,n,r,i,a){var s=xo(e),o=xo(t),l=s?ae:Fs(e),u=o?ae:Fs(t);l=l==ie?he:l,u=u==ie?he:u;var c=l==he,d=u==he,f=l==u;if(f&&Io(e)){if(!Io(t))return!1;s=!0,c=!1}if(f&&!c)return a||(a=new vt),s||Po(e)?Pr(e,t,n,r,i,a):function(e,t,n,r,i,a,s){switch(n){case we:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case be:return!(e.byteLength!=t.byteLength||!a(new Ea(e),new Ea(t)));case se:case oe:case fe:return Ii(+e,+t);case le:return e.name==t.name&&e.message==t.message;case me:case ve:return e==t+"";case de:var o=U;case ge:var l=1&r;if(o||(o=P),e.size!=t.size&&!l)return!1;var u=s.get(e);if(u)return u==t;r|=2,s.set(e,t);var c=Pr(o(e),o(t),r,i,a,s);return s.delete(e),c;case _e:if(Cs)return Cs.call(e)==Cs.call(t)}return!1}(e,t,l,n,r,i,a);if(!(1&n)){var h=c&&Ta.call(e,"__wrapped__"),p=d&&Ta.call(t,"__wrapped__");if(h||p){var m=h?e.value():e,g=p?t.value():t;return a||(a=new vt),i(m,g,n,r,a)}}return!!f&&(a||(a=new vt),function(e,t,n,r,i,a){var s=1&n,o=Fr(e),l=o.length;if(l!=Fr(t).length&&!s)return!1;for(var u=l;u--;){var c=o[u];if(!(s?c in t:Ta.call(t,c)))return!1}var d=a.get(e),f=a.get(t);if(d&&f)return d==t&&f==e;var h=!0;a.set(e,t),a.set(t,e);for(var p=s;++u<l;){var m=e[c=o[u]],g=t[c];if(r)var v=s?r(g,m,c,t,e,a):r(m,g,c,e,t,a);if(!(v===H?m===g||i(m,g,n,r,a):v)){h=!1;break}p||(p="constructor"==c)}if(h&&!p){var _=e.constructor,y=t.constructor;_!=y&&"constructor"in e&&"constructor"in t&&!("function"==typeof _&&_ instanceof _&&"function"==typeof y&&y instanceof y)&&(h=!1)}return a.delete(e),a.delete(t),h}(e,t,n,r,i,a))}(e,t,n,r,on,i))}function pn(e,t,n,r){var i=n.length,a=i,s=!r;if(null==e)return!a;for(e=ba(e);i--;){var o=n[i];if(s&&o[2]?o[1]!==e[o[0]]:!(o[0]in e))return!1}for(;++i<a;){var l=(o=n[i])[0],u=e[l],c=o[1];if(s&&o[2]){if(u===H&&!(l in e))return!1}else{var d=new vt;if(r)var f=r(u,c,l,e,t,d);if(!(f===H?on(c,u,3,r,d):f))return!1}}return!0}function yn(e){return!(!Fi(e)||function(e){return!!Ya&&Ya in e}(e))&&(Ai(e)?Na:it).test(ci(e))}function bn(e){return"function"==typeof e?e:null==e?la:"object"==typeof e?xo(e)?Dn(e[0],e[1]):Mn(e):fa(e)}function wn(e){if(!ei(e))return rs(e);var t=[];for(var n in ba(e))Ta.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kn(e){if(!Fi(e))return function(e){var t=[];if(null!=e)for(var n in ba(e))t.push(n);return t}(e);var t=ei(e),n=[];for(var r in e)("constructor"!=r||!t&&Ta.call(e,r))&&n.push(r);return n}function Sn(e,t){return e<t}function Cn(e,t){var n=-1,r=Li(e)?ma(e.length):[];return Rs(e,(function(e,i,a){r[++n]=t(e,i,a)})),r}function Mn(e){var t=Vr(e);return 1==t.length&&t[0][2]?ni(t[0][0],t[0][1]):function(n){return n===e||pn(n,e,t)}}function Dn(e,t){return Qr(e)&&ti(t)?ni(ui(e),t):function(n){var r=Qi(n,e);return r===H&&r===t?Xi(n,e):on(t,r,3)}}function Rn(e,t,n,r,i){e!==t&&Ts(t,(function(a,s){if(i||(i=new vt),Fi(a))!function(e,t,n,r,i,a,s){var o=ai(e,n),l=ai(t,n),u=s.get(l);if(u)return kt(e,n,u),H;var c=a?a(o,l,n+"",e,t,s):H,d=c===H;if(d){var f=xo(l),h=!f&&Io(l),p=!f&&!h&&Po(l);c=l,f||h||p?xo(o)?c=o:Ui(o)?c=cr(o):h?(d=!1,c=ir(l,!0)):p?(d=!1,c=sr(l,!0)):c=[]:Hi(l)||To(l)?(c=o,To(o)?c=Ji(o):Fi(o)&&!Ai(o)||(c=qr(l))):d=!1}d&&(s.set(l,c),i(c,l,r,a,s),s.delete(l)),kt(e,n,c)}(e,t,s,n,Rn,r,i);else{var o=r?r(ai(e,s),a,s+"",e,t,i):H;o===H&&(o=a),kt(e,s,o)}}),ta)}function On(e,t){var n=e.length;if(n)return Jr(t+=t<0?n:0,n)?e[t]:H}function Tn(e,t,n){t=t.length?l(t,(function(e){return xo(e)?function(t){return $t(t,1===e.length?e[0]:e)}:e})):[la];var r=-1;return t=l(t,D($r())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Cn(e,(function(e,n,i){return{criteria:l(t,(function(t){return t(e)})),index:++r,value:e}})),(function(e,t){return function(e,t,n){for(var r=-1,i=e.criteria,a=t.criteria,s=i.length,o=n.length;++r<s;){var l=or(i[r],a[r]);if(l)return r>=o?l:l*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}))}function xn(e,t,n){for(var r=-1,i=t.length,a={};++r<i;){var s=t[r],o=$t(e,s);n(o,s)&&En(a,nr(s,e),o)}return a}function Yn(e,t,n,r){var i=r?v:g,a=-1,s=t.length,o=e;for(e===t&&(t=cr(t)),n&&(o=l(e,D(n)));++a<s;)for(var u=0,c=t[a],d=n?n(c):c;(u=i(o,d,u,r))>-1;)o!==e&&$a.call(o,u,1),$a.call(e,u,1);return e}function In(e,t){for(var n=e?t.length:0,r=n-1;n--;){var i=t[n];if(n==r||i!==a){var a=i;Jr(i)?$a.call(e,i,1):qn(e,i)}}return e}function Ln(e,t){return e+Qa(ls()*(t-e+1))}function Un(e,t){var n="";if(!e||t<1||t>Q)return n;do{t%2&&(n+=e),(t=Qa(t/2))&&(e+=e)}while(t);return n}function Nn(e,t){return $s(ri(e,t,la),e+"")}function An(e){return yt(ra(e))}function Pn(e,t){var n=ra(e);return li(n,Tt(t,0,n.length))}function En(e,t,n,r){if(!Fi(e))return e;for(var i=-1,a=(t=nr(t,e)).length,s=a-1,o=e;null!=o&&++i<a;){var l=ui(t[i]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(i!=s){var c=o[l];(u=r?r(c,l,o):H)===H&&(u=Fi(c)?c:Jr(t[i+1])?[]:{})}St(o,l,u),o=o[l]}return e}function Fn(e){return li(ra(e))}function Wn(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=ma(i);++r<i;)a[r]=e[r+t];return a}function jn(e,t){var n;return Rs(e,(function(e,r,i){return!(n=t(e,r,i))})),!!n}function Hn(e,t,n){var r=0,i=null==e?r:e.length;if("number"==typeof t&&t==t&&i<=ne){for(;r<i;){var a=r+i>>>1,s=e[a];null!==s&&!zi(s)&&(n?s<=t:s<t)?r=a+1:i=a}return i}return $n(e,t,la,n)}function $n(e,t,n,r){var i=0,a=null==e?0:e.length;if(0===a)return 0;for(var s=(t=n(t))!=t,o=null===t,l=zi(t),u=t===H;i<a;){var c=Qa((i+a)/2),d=n(e[c]),f=d!==H,h=null===d,p=d==d,m=zi(d);if(s)var g=r||p;else g=u?p&&(r||f):o?p&&f&&(r||!h):l?p&&f&&!h&&(r||!m):!h&&!m&&(r?d<=t:d<t);g?i=c+1:a=c}return as(a,te)}function zn(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var s=e[n],o=t?t(s):s;if(!n||!Ii(o,l)){var l=o;a[i++]=0===s?0:s}}return a}function Vn(e){return"number"==typeof e?e:zi(e)?X:+e}function Bn(e){if("string"==typeof e)return e;if(xo(e))return l(e,Bn)+"";if(zi(e))return Ms?Ms.call(e):"";var t=e+"";return"0"==t&&1/e==-K?"-0":t}function Gn(e,t,n){var r=-1,i=s,a=e.length,l=!0,u=[],c=u;if(n)l=!1,i=o;else if(a>=200){var d=t?null:Ns(e);if(d)return P(d);l=!1,i=O,c=new gt}else c=t?[]:u;e:for(;++r<a;){var f=e[r],h=t?t(f):f;if(f=n||0!==f?f:0,l&&h==h){for(var p=c.length;p--;)if(c[p]===h)continue e;t&&c.push(h),u.push(f)}else i(c,h,n)||(c!==u&&c.push(h),u.push(f))}return u}function qn(e,t){return null==(e=ii(e,t=nr(t,e)))||delete e[ui(vi(t))]}function Zn(e,t,n,r){return En(e,t,n($t(e,t)),r)}function Jn(e,t,n,r){for(var i=e.length,a=r?i:-1;(r?a--:++a<i)&&t(e[a],a,e););return n?Wn(e,r?0:a,r?a+1:i):Wn(e,r?a+1:0,r?i:a)}function Kn(e,t){var n=e;return n instanceof ft&&(n=n.value()),c(t,(function(e,t){return t.func.apply(t.thisArg,u([e],t.args))}),n)}function Qn(e,t,n){var r=e.length;if(r<2)return r?Gn(e[0]):[];for(var i=-1,a=ma(r);++i<r;)for(var s=e[i],o=-1;++o<r;)o!=i&&(a[i]=Lt(a[i]||s,e[o],t,n));return Gn(Pt(a,1),t,n)}function Xn(e,t,n){for(var r=-1,i=e.length,a=t.length,s={};++r<i;)n(s,e[r],r<a?t[r]:H);return s}function er(e){return Ui(e)?e:[]}function tr(e){return"function"==typeof e?e:la}function nr(e,t){return xo(e)?e:Qr(e,t)?[e]:zs(Ki(e))}function rr(e,t,n){var r=e.length;return n=n===H?r:n,!t&&n>=r?e:Wn(e,t,n)}function ir(e,t){if(t)return e.slice();var n=e.length,r=Fa?Fa(n):new e.constructor(n);return e.copy(r),r}function ar(e){var t=new e.constructor(e.byteLength);return new Ea(t).set(new Ea(e)),t}function sr(e,t){return new e.constructor(t?ar(e.buffer):e.buffer,e.byteOffset,e.length)}function or(e,t){if(e!==t){var n=e!==H,r=null===e,i=e==e,a=zi(e),s=t!==H,o=null===t,l=t==t,u=zi(t);if(!o&&!u&&!a&&e>t||a&&s&&l&&!o&&!u||r&&s&&l||!n&&l||!i)return 1;if(!r&&!a&&!u&&e<t||u&&n&&i&&!r&&!a||o&&n&&i||!s&&i||!l)return-1}return 0}function lr(e,t,n,r){for(var i=-1,a=e.length,s=n.length,o=-1,l=t.length,u=is(a-s,0),c=ma(l+u),d=!r;++o<l;)c[o]=t[o];for(;++i<s;)(d||i<a)&&(c[n[i]]=e[i]);for(;u--;)c[o++]=e[i++];return c}function ur(e,t,n,r){for(var i=-1,a=e.length,s=-1,o=n.length,l=-1,u=t.length,c=is(a-o,0),d=ma(c+u),f=!r;++i<c;)d[i]=e[i];for(var h=i;++l<u;)d[h+l]=t[l];for(;++s<o;)(f||i<a)&&(d[h+n[s]]=e[i++]);return d}function cr(e,t){var n=-1,r=e.length;for(t||(t=ma(r));++n<r;)t[n]=e[n];return t}function dr(e,t,n,r){var i=!n;n||(n={});for(var a=-1,s=t.length;++a<s;){var o=t[a],l=r?r(n[o],e[o],o,n,e):H;l===H&&(l=e[o]),i?Rt(n,o,l):St(n,o,l)}return n}function fr(e,n){return function(r,i){var a=xo(r)?t:Mt,s=n?n():{};return a(r,e,$r(i,2),s)}}function hr(e){return Nn((function(t,n){var r=-1,i=n.length,a=i>1?n[i-1]:H,s=i>2?n[2]:H;for(a=e.length>3&&"function"==typeof a?(i--,a):H,s&&Kr(n[0],n[1],s)&&(a=i<3?H:a,i=1),t=ba(t);++r<i;){var o=n[r];o&&e(t,o,r,a)}return t}))}function pr(e,t){return function(n,r){if(null==n)return n;if(!Li(n))return e(n,r);for(var i=n.length,a=t?i:-1,s=ba(n);(t?a--:++a<i)&&!1!==r(s[a],a,s););return n}}function mr(e){return function(t,n,r){for(var i=-1,a=ba(t),s=r(t),o=s.length;o--;){var l=s[e?o:++i];if(!1===n(a[l],l,a))break}return t}}function gr(e){return function(t){var n=I(t=Ki(t))?F(t):H,r=n?n[0]:t.charAt(0),i=n?rr(n,1).join(""):t.slice(1);return r[e]()+i}}function vr(e){return function(t){return c(sa(aa(t).replace(Wt,"")),e,"")}}function _r(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Ds(e.prototype),r=e.apply(n,t);return Fi(r)?r:n}}function yr(t,n,r){var i=_r(t);return function a(){for(var s=arguments.length,o=ma(s),l=s,u=Hr(a);l--;)o[l]=arguments[l];var c=s<3&&o[0]!==u&&o[s-1]!==u?[]:A(o,u);return(s-=c.length)<r?xr(t,n,kr,a.placeholder,H,o,c,H,H,r-s):e(this&&this!==tn&&this instanceof a?i:t,this,o)}}function br(e){return function(t,n,r){var i=ba(t);if(!Li(t)){var a=$r(n,3);t=ea(t),n=function(e){return a(i[e],e,i)}}var s=e(t,n,r);return s>-1?i[a?t[s]:s]:H}}function wr(e){return Er((function(t){var n=t.length,r=n,i=dt.prototype.thru;for(e&&t.reverse();r--;){var a=t[r];if("function"!=typeof a)throw new Sa($);if(i&&!s&&"wrapper"==jr(a))var s=new dt([],!0)}for(r=s?r:n;++r<n;){var o=jr(a=t[r]),l="wrapper"==o?As(a):H;s=l&&Xr(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?s[jr(l[0])].apply(s,l[3]):1==a.length&&Xr(a)?s[o]():s.thru(a)}return function(){var e=arguments,r=e[0];if(s&&1==e.length&&xo(r))return s.plant(r).value();for(var i=0,a=n?t[i].apply(this,e):r;++i<n;)a=t[i].call(this,a);return a}}))}function kr(e,t,n,r,i,a,s,o,l,u){var c=t&Z,d=1&t,f=2&t,h=24&t,p=512&t,m=f?H:_r(e);return function g(){for(var v=arguments.length,_=ma(v),y=v;y--;)_[y]=arguments[y];if(h)var b=Hr(g),w=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(_,b);if(r&&(_=lr(_,r,i,h)),a&&(_=ur(_,a,s,h)),v-=w,h&&v<u)return xr(e,t,kr,g.placeholder,n,_,A(_,b),o,l,u-v);var k=d?n:this,S=f?k[e]:e;return v=_.length,o?_=function(e,t){for(var n=e.length,r=as(t.length,n),i=cr(e);r--;){var a=t[r];e[r]=Jr(a,n)?i[a]:H}return e}(_,o):p&&v>1&&_.reverse(),c&&l<v&&(_.length=l),this&&this!==tn&&this instanceof g&&(S=m||_r(S)),S.apply(k,_)}}function Sr(e,t){return function(n,r){return function(e,t,n,r){return Et(e,(function(e,i,a){t(r,n(e),i,a)})),r}(n,e,t(r),{})}}function Cr(e,t){return function(n,r){var i;if(n===H&&r===H)return t;if(n!==H&&(i=n),r!==H){if(i===H)return r;"string"==typeof n||"string"==typeof r?(n=Bn(n),r=Bn(r)):(n=Vn(n),r=Vn(r)),i=e(n,r)}return i}}function Mr(t){return Er((function(n){return n=l(n,D($r())),Nn((function(r){var i=this;return t(n,(function(t){return e(t,i,r)}))}))}))}function Dr(e,t){var n=(t=t===H?" ":Bn(t)).length;if(n<2)return n?Un(t,e):t;var r=Un(t,Ka(e/E(t)));return I(t)?rr(F(r),0,e).join(""):r.slice(0,e)}function Rr(t,n,r,i){var a=1&n,s=_r(t);return function n(){for(var o=-1,l=arguments.length,u=-1,c=i.length,d=ma(c+l),f=this&&this!==tn&&this instanceof n?s:t;++u<c;)d[u]=i[u];for(;l--;)d[u++]=arguments[++o];return e(f,a?r:this,d)}}function Or(e){return function(t,n,r){return r&&"number"!=typeof r&&Kr(t,n,r)&&(n=r=H),t=Bi(t),n===H?(n=t,t=0):n=Bi(n),function(e,t,n,r){for(var i=-1,a=is(Ka((t-e)/(n||1)),0),s=ma(a);a--;)s[r?a:++i]=e,e+=n;return s}(t,n,r=r===H?t<n?1:-1:Bi(r),e)}}function Tr(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Zi(t),n=Zi(n)),e(t,n)}}function xr(e,t,n,r,i,a,s,o,l,u){var c=8&t;t|=c?G:q,4&(t&=~(c?q:G))||(t&=-4);var d=[e,t,i,c?a:H,c?s:H,c?H:a,c?H:s,o,l,u],f=n.apply(H,d);return Xr(e)&&js(f,d),f.placeholder=r,si(f,e,t)}function Yr(e){var t=ya[e];return function(e,n){if(e=Zi(e),(n=null==n?0:as(Gi(n),292))&&ts(e)){var r=(Ki(e)+"e").split("e");return+((r=(Ki(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}function Ir(e){return function(t){var n=Fs(t);return n==de?U(t):n==ge?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return l(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Lr(e,t,n,r,i,a,s,o){var l=2&t;if(!l&&"function"!=typeof e)throw new Sa($);var u=r?r.length:0;if(u||(t&=-97,r=i=H),s=s===H?s:is(Gi(s),0),o=o===H?o:Gi(o),u-=i?i.length:0,t&q){var c=r,d=i;r=i=H}var f=l?H:As(e),h=[e,t,n,r,i,c,d,a,s,o];if(f&&function(e,t){var n=e[1],r=t[1],i=n|r,a=i<131,s=r==Z&&8==n||r==Z&&n==J&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!a&&!s)return e;1&r&&(e[2]=t[2],i|=1&n?0:4);var o=t[3];if(o){var l=e[3];e[3]=l?lr(l,o,t[4]):o,e[4]=l?A(e[3],V):t[4]}o=t[5],o&&(l=e[5],e[5]=l?ur(l,o,t[6]):o,e[6]=l?A(e[5],V):t[6]),o=t[7],o&&(e[7]=o),r&Z&&(e[8]=null==e[8]?t[8]:as(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=i}(h,f),e=h[0],t=h[1],n=h[2],r=h[3],i=h[4],!(o=h[9]=h[9]===H?l?0:e.length:is(h[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)p=8==t||t==B?yr(e,t,o):t!=G&&33!=t||i.length?kr.apply(H,h):Rr(e,t,n,r);else var p=function(e,t,n){var r=1&t,i=_r(e);return function t(){return(this&&this!==tn&&this instanceof t?i:e).apply(r?n:this,arguments)}}(e,t,n);return si((f?Ys:js)(p,h),e,t)}function Ur(e,t,n,r){return e===H||Ii(e,Da[n])&&!Ta.call(r,n)?t:e}function Nr(e,t,n,r,i,a){return Fi(e)&&Fi(t)&&(a.set(t,e),Rn(e,t,H,Nr,a),a.delete(t)),e}function Ar(e){return Hi(e)?H:e}function Pr(e,t,n,r,i,a){var s=1&n,o=e.length,l=t.length;if(o!=l&&!(s&&l>o))return!1;var u=a.get(e),c=a.get(t);if(u&&c)return u==t&&c==e;var d=-1,h=!0,p=2&n?new gt:H;for(a.set(e,t),a.set(t,e);++d<o;){var m=e[d],g=t[d];if(r)var v=s?r(g,m,d,t,e,a):r(m,g,d,e,t,a);if(v!==H){if(v)continue;h=!1;break}if(p){if(!f(t,(function(e,t){if(!O(p,t)&&(m===e||i(m,e,n,r,a)))return p.push(t)}))){h=!1;break}}else if(m!==g&&!i(m,g,n,r,a)){h=!1;break}}return a.delete(e),a.delete(t),h}function Er(e){return $s(ri(e,H,mi),e+"")}function Fr(e){return zt(e,ea,Ps)}function Wr(e){return zt(e,ta,Es)}function jr(e){for(var t=e.name+"",n=vs[t],r=Ta.call(vs,t)?n.length:0;r--;){var i=n[r],a=i.func;if(null==a||a==e)return i.name}return t}function Hr(e){return(Ta.call(Ke,"placeholder")?Ke:e).placeholder}function $r(){var e=Ke.iteratee||ua;return e=e===ua?bn:e,arguments.length?e(arguments[0],arguments[1]):e}function zr(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function Vr(e){for(var t=ea(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,ti(i)]}return t}function Br(e,t){var n=function(e,t){return null==e?H:e[t]}(e,t);return yn(n)?n:H}function Gr(e,t,n){for(var r=-1,i=(t=nr(t,e)).length,a=!1;++r<i;){var s=ui(t[r]);if(!(a=null!=e&&n(e,s)))break;e=e[s]}return a||++r!=i?a:!!(i=null==e?0:e.length)&&Ei(i)&&Jr(s,i)&&(xo(e)||To(e))}function qr(e){return"function"!=typeof e.constructor||ei(e)?{}:Ds(Wa(e))}function Zr(e){return xo(e)||To(e)||!!(za&&e&&e[za])}function Jr(e,t){var n=typeof e;return!!(t=null==t?Q:t)&&("number"==n||"symbol"!=n&&st.test(e))&&e>-1&&e%1==0&&e<t}function Kr(e,t,n){if(!Fi(n))return!1;var r=typeof t;return!!("number"==r?Li(n)&&Jr(t,n.length):"string"==r&&t in n)&&Ii(n[t],e)}function Qr(e,t){if(xo(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!zi(e))||He.test(e)||!je.test(e)||null!=t&&e in ba(t)}function Xr(e){var t=jr(e),n=Ke[t];if("function"!=typeof n||!(t in ft.prototype))return!1;if(e===n)return!0;var r=As(n);return!!r&&e===r[0]}function ei(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Da)}function ti(e){return e==e&&!Fi(e)}function ni(e,t){return function(n){return null!=n&&n[e]===t&&(t!==H||e in ba(n))}}function ri(t,n,r){return n=is(n===H?t.length-1:n,0),function(){for(var i=arguments,a=-1,s=is(i.length-n,0),o=ma(s);++a<s;)o[a]=i[n+a];a=-1;for(var l=ma(n+1);++a<n;)l[a]=i[a];return l[n]=r(o),e(t,this,l)}}function ii(e,t){return t.length<2?e:$t(e,Wn(t,0,-1))}function ai(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function si(e,t,n){var r=t+"";return $s(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(qe,"{\n/* [wrapped with "+t+"] */\n")}(r,di(function(e){var t=e.match(Ze);return t?t[1].split(Je):[]}(r),n)))}function oi(e){var t=0,n=0;return function(){var r=ss(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(H,arguments)}}function li(e,t){var n=-1,r=e.length,i=r-1;for(t=t===H?r:t;++n<t;){var a=Ln(n,i),s=e[a];e[a]=e[n],e[n]=s}return e.length=t,e}function ui(e){if("string"==typeof e||zi(e))return e;var t=e+"";return"0"==t&&1/e==-K?"-0":t}function ci(e){if(null!=e){try{return Oa.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function di(e,t){return n(re,(function(n){var r="_."+n[0];t&n[1]&&!s(e,r)&&e.push(r)})),e.sort()}function fi(e){if(e instanceof ft)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=cr(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function hi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:Gi(n);return i<0&&(i=is(r+i,0)),m(e,$r(t,3),i)}function pi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r-1;return n!==H&&(i=Gi(n),i=n<0?is(r+i,0):as(i,r-1)),m(e,$r(t,3),i,!0)}function mi(e){return null!=e&&e.length?Pt(e,1):[]}function gi(e){return e&&e.length?e[0]:H}function vi(e){var t=null==e?0:e.length;return t?e[t-1]:H}function _i(e,t){return e&&e.length&&t&&t.length?Yn(e,t):e}function yi(e){return null==e?e:us.call(e)}function bi(e){if(!e||!e.length)return[];var t=0;return e=a(e,(function(e){if(Ui(e))return t=is(e.length,t),!0})),C(t,(function(t){return l(e,b(t))}))}function wi(t,n){if(!t||!t.length)return[];var r=bi(t);return null==n?r:l(r,(function(t){return e(n,H,t)}))}function ki(e){var t=Ke(e);return t.__chain__=!0,t}function Si(e,t){return t(e)}function Ci(e,t){return(xo(e)?n:Rs)(e,$r(t,3))}function Mi(e,t){return(xo(e)?r:Os)(e,$r(t,3))}function Di(e,t){return(xo(e)?l:Cn)(e,$r(t,3))}function Ri(e,t,n){return t=n?H:t,t=e&&null==t?e.length:t,Lr(e,Z,H,H,H,H,t)}function Oi(e,t){var n;if("function"!=typeof t)throw new Sa($);return e=Gi(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=H),n}}function Ti(e,t,n){function r(t){var n=l,r=u;return l=u=H,p=t,d=e.apply(r,n)}function i(e){var n=e-h;return h===H||n>=t||n<0||g&&e-p>=c}function a(){var e=_o();return i(e)?s(e):(f=Hs(a,function(e){var n=t-(e-h);return g?as(n,c-(e-p)):n}(e)),H)}function s(e){return f=H,v&&l?r(e):(l=u=H,d)}function o(){var e=_o(),n=i(e);if(l=arguments,u=this,h=e,n){if(f===H)return function(e){return p=e,f=Hs(a,t),m?r(e):d}(h);if(g)return Us(f),f=Hs(a,t),r(h)}return f===H&&(f=Hs(a,t)),d}var l,u,c,d,f,h,p=0,m=!1,g=!1,v=!0;if("function"!=typeof e)throw new Sa($);return t=Zi(t)||0,Fi(n)&&(m=!!n.leading,c=(g="maxWait"in n)?is(Zi(n.maxWait)||0,t):c,v="trailing"in n?!!n.trailing:v),o.cancel=function(){f!==H&&Us(f),p=0,l=h=u=f=H},o.flush=function(){return f===H?d:s(_o())},o}function xi(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Sa($);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var s=e.apply(this,r);return n.cache=a.set(i,s)||a,s};return n.cache=new(xi.Cache||mt),n}function Yi(e){if("function"!=typeof e)throw new Sa($);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Ii(e,t){return e===t||e!=e&&t!=t}function Li(e){return null!=e&&Ei(e.length)&&!Ai(e)}function Ui(e){return Wi(e)&&Li(e)}function Ni(e){if(!Wi(e))return!1;var t=Vt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Hi(e)}function Ai(e){if(!Fi(e))return!1;var t=Vt(e);return t==ue||t==ce||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Pi(e){return"number"==typeof e&&e==Gi(e)}function Ei(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Q}function Fi(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Wi(e){return null!=e&&"object"==typeof e}function ji(e){return"number"==typeof e||Wi(e)&&Vt(e)==fe}function Hi(e){if(!Wi(e)||Vt(e)!=he)return!1;var t=Wa(e);if(null===t)return!0;var n=Ta.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Oa.call(n)==La}function $i(e){return"string"==typeof e||!xo(e)&&Wi(e)&&Vt(e)==ve}function zi(e){return"symbol"==typeof e||Wi(e)&&Vt(e)==_e}function Vi(e){if(!e)return[];if(Li(e))return $i(e)?F(e):cr(e);if(Va&&e[Va])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Va]());var t=Fs(e);return(t==de?U:t==ge?P:ra)(e)}function Bi(e){return e?(e=Zi(e))===K||e===-K?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function Gi(e){var t=Bi(e),n=t%1;return t==t?n?t-n:t:0}function qi(e){return e?Tt(Gi(e),0,ee):0}function Zi(e){if("number"==typeof e)return e;if(zi(e))return X;if(Fi(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Fi(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=M(e);var n=rt.test(e);return n||at.test(e)?Qt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Ji(e){return dr(e,ta(e))}function Ki(e){return null==e?"":Bn(e)}function Qi(e,t,n){var r=null==e?H:$t(e,t);return r===H?n:r}function Xi(e,t){return null!=e&&Gr(e,t,en)}function ea(e){return Li(e)?_t(e):wn(e)}function ta(e){return Li(e)?_t(e,!0):kn(e)}function na(e,t){if(null==e)return{};var n=l(Wr(e),(function(e){return[e]}));return t=$r(t),xn(e,n,(function(e,n){return t(e,n[0])}))}function ra(e){return null==e?[]:R(e,ea(e))}function ia(e){return ul(Ki(e).toLowerCase())}function aa(e){return(e=Ki(e))&&e.replace(ot,mn).replace(jt,"")}function sa(e,t,n){return e=Ki(e),(t=n?H:t)===H?L(e)?j(e):h(e):e.match(t)||[]}function oa(e){return function(){return e}}function la(e){return e}function ua(e){return bn("function"==typeof e?e:xt(e,1))}function ca(e,t,r){var i=ea(t),a=Ht(t,i);null!=r||Fi(t)&&(a.length||!i.length)||(r=t,t=e,e=this,a=Ht(t,ea(t)));var s=!(Fi(r)&&"chain"in r&&!r.chain),o=Ai(e);return n(a,(function(n){var r=t[n];e[n]=r,o&&(e.prototype[n]=function(){var t=this.__chain__;if(s||t){var n=e(this.__wrapped__);return(n.__actions__=cr(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,u([this.value()],arguments))})})),e}function da(){}function fa(e){return Qr(e)?b(ui(e)):function(e){return function(t){return $t(t,e)}}(e)}function ha(){return[]}function pa(){return!1}var ma=(Ge=null==Ge?tn:_n.defaults(tn.Object(),Ge,_n.pick(tn,Bt))).Array,ga=Ge.Date,va=Ge.Error,_a=Ge.Function,ya=Ge.Math,ba=Ge.Object,wa=Ge.RegExp,ka=Ge.String,Sa=Ge.TypeError,Ca=ma.prototype,Ma=_a.prototype,Da=ba.prototype,Ra=Ge["__core-js_shared__"],Oa=Ma.toString,Ta=Da.hasOwnProperty,xa=0,Ya=function(){var e=/[^.]+$/.exec(Ra&&Ra.keys&&Ra.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ia=Da.toString,La=Oa.call(ba),Ua=tn._,Na=wa("^"+Oa.call(Ta).replace(ze,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Aa=an?Ge.Buffer:H,Pa=Ge.Symbol,Ea=Ge.Uint8Array,Fa=Aa?Aa.allocUnsafe:H,Wa=N(ba.getPrototypeOf,ba),ja=ba.create,Ha=Da.propertyIsEnumerable,$a=Ca.splice,za=Pa?Pa.isConcatSpreadable:H,Va=Pa?Pa.iterator:H,Ba=Pa?Pa.toStringTag:H,Ga=function(){try{var e=Br(ba,"defineProperty");return e({},"",{}),e}catch(e){}}(),qa=Ge.clearTimeout!==tn.clearTimeout&&Ge.clearTimeout,Za=ga&&ga.now!==tn.Date.now&&ga.now,Ja=Ge.setTimeout!==tn.setTimeout&&Ge.setTimeout,Ka=ya.ceil,Qa=ya.floor,Xa=ba.getOwnPropertySymbols,es=Aa?Aa.isBuffer:H,ts=Ge.isFinite,ns=Ca.join,rs=N(ba.keys,ba),is=ya.max,as=ya.min,ss=ga.now,os=Ge.parseInt,ls=ya.random,us=Ca.reverse,cs=Br(Ge,"DataView"),ds=Br(Ge,"Map"),fs=Br(Ge,"Promise"),hs=Br(Ge,"Set"),ps=Br(Ge,"WeakMap"),ms=Br(ba,"create"),gs=ps&&new ps,vs={},_s=ci(cs),ys=ci(ds),bs=ci(fs),ws=ci(hs),ks=ci(ps),Ss=Pa?Pa.prototype:H,Cs=Ss?Ss.valueOf:H,Ms=Ss?Ss.toString:H,Ds=function(){function e(){}return function(t){if(!Fi(t))return{};if(ja)return ja(t);e.prototype=t;var n=new e;return e.prototype=H,n}}();Ke.templateSettings={escape:Ee,evaluate:Fe,interpolate:We,variable:"",imports:{_:Ke}},Ke.prototype=ct.prototype,Ke.prototype.constructor=Ke,dt.prototype=Ds(ct.prototype),dt.prototype.constructor=dt,ft.prototype=Ds(ct.prototype),ft.prototype.constructor=ft,ht.prototype.clear=function(){this.__data__=ms?ms(null):{},this.size=0},ht.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ht.prototype.get=function(e){var t=this.__data__;if(ms){var n=t[e];return n===z?H:n}return Ta.call(t,e)?t[e]:H},ht.prototype.has=function(e){var t=this.__data__;return ms?t[e]!==H:Ta.call(t,e)},ht.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=ms&&t===H?z:t,this},pt.prototype.clear=function(){this.__data__=[],this.size=0},pt.prototype.delete=function(e){var t=this.__data__,n=Ct(t,e);return!(n<0||(n==t.length-1?t.pop():$a.call(t,n,1),--this.size,0))},pt.prototype.get=function(e){var t=this.__data__,n=Ct(t,e);return n<0?H:t[n][1]},pt.prototype.has=function(e){return Ct(this.__data__,e)>-1},pt.prototype.set=function(e,t){var n=this.__data__,r=Ct(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},mt.prototype.clear=function(){this.size=0,this.__data__={hash:new ht,map:new(ds||pt),string:new ht}},mt.prototype.delete=function(e){var t=zr(this,e).delete(e);return this.size-=t?1:0,t},mt.prototype.get=function(e){return zr(this,e).get(e)},mt.prototype.has=function(e){return zr(this,e).has(e)},mt.prototype.set=function(e,t){var n=zr(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},gt.prototype.add=gt.prototype.push=function(e){return this.__data__.set(e,z),this},gt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new pt,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof pt){var r=n.__data__;if(!ds||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new mt(r)}return n.set(e,t),this.size=n.size,this};var Rs=pr(Et),Os=pr(Ft,!0),Ts=mr(),xs=mr(!0),Ys=gs?function(e,t){return gs.set(e,t),e}:la,Is=Ga?function(e,t){return Ga(e,"toString",{configurable:!0,enumerable:!1,value:oa(t),writable:!0})}:la,Ls=Nn,Us=qa||function(e){return tn.clearTimeout(e)},Ns=hs&&1/P(new hs([,-0]))[1]==K?function(e){return new hs(e)}:da,As=gs?function(e){return gs.get(e)}:da,Ps=Xa?function(e){return null==e?[]:(e=ba(e),a(Xa(e),(function(t){return Ha.call(e,t)})))}:ha,Es=Xa?function(e){for(var t=[];e;)u(t,Ps(e)),e=Wa(e);return t}:ha,Fs=Vt;(cs&&Fs(new cs(new ArrayBuffer(1)))!=we||ds&&Fs(new ds)!=de||fs&&Fs(fs.resolve())!=pe||hs&&Fs(new hs)!=ge||ps&&Fs(new ps)!=ye)&&(Fs=function(e){var t=Vt(e),n=t==he?e.constructor:H,r=n?ci(n):"";if(r)switch(r){case _s:return we;case ys:return de;case bs:return pe;case ws:return ge;case ks:return ye}return t});var Ws=Ra?Ai:pa,js=oi(Ys),Hs=Ja||function(e,t){return tn.setTimeout(e,t)},$s=oi(Is),zs=function(e){var t=xi(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace($e,(function(e,n,r,i){t.push(r?i.replace(Xe,"$1"):n||e)})),t})),Vs=Nn((function(e,t){return Ui(e)?Lt(e,Pt(t,1,Ui,!0)):[]})),Bs=Nn((function(e,t){var n=vi(t);return Ui(n)&&(n=H),Ui(e)?Lt(e,Pt(t,1,Ui,!0),$r(n,2)):[]})),Gs=Nn((function(e,t){var n=vi(t);return Ui(n)&&(n=H),Ui(e)?Lt(e,Pt(t,1,Ui,!0),H,n):[]})),qs=Nn((function(e){var t=l(e,er);return t.length&&t[0]===e[0]?nn(t):[]})),Zs=Nn((function(e){var t=vi(e),n=l(e,er);return t===vi(n)?t=H:n.pop(),n.length&&n[0]===e[0]?nn(n,$r(t,2)):[]})),Js=Nn((function(e){var t=vi(e),n=l(e,er);return(t="function"==typeof t?t:H)&&n.pop(),n.length&&n[0]===e[0]?nn(n,H,t):[]})),Ks=Nn(_i),Qs=Er((function(e,t){var n=null==e?0:e.length,r=Ot(e,t);return In(e,l(t,(function(e){return Jr(e,n)?+e:e})).sort(or)),r})),Xs=Nn((function(e){return Gn(Pt(e,1,Ui,!0))})),eo=Nn((function(e){var t=vi(e);return Ui(t)&&(t=H),Gn(Pt(e,1,Ui,!0),$r(t,2))})),to=Nn((function(e){var t=vi(e);return t="function"==typeof t?t:H,Gn(Pt(e,1,Ui,!0),H,t)})),no=Nn((function(e,t){return Ui(e)?Lt(e,t):[]})),ro=Nn((function(e){return Qn(a(e,Ui))})),io=Nn((function(e){var t=vi(e);return Ui(t)&&(t=H),Qn(a(e,Ui),$r(t,2))})),ao=Nn((function(e){var t=vi(e);return t="function"==typeof t?t:H,Qn(a(e,Ui),H,t)})),so=Nn(bi),oo=Nn((function(e){var t=e.length,n=t>1?e[t-1]:H;return n="function"==typeof n?(e.pop(),n):H,wi(e,n)})),lo=Er((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,i=function(t){return Ot(t,e)};return!(t>1||this.__actions__.length)&&r instanceof ft&&Jr(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:Si,args:[i],thisArg:H}),new dt(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(H),e}))):this.thru(i)})),uo=fr((function(e,t,n){Ta.call(e,n)?++e[n]:Rt(e,n,1)})),co=br(hi),fo=br(pi),ho=fr((function(e,t,n){Ta.call(e,n)?e[n].push(t):Rt(e,n,[t])})),po=Nn((function(t,n,r){var i=-1,a="function"==typeof n,s=Li(t)?ma(t.length):[];return Rs(t,(function(t){s[++i]=a?e(n,t,r):rn(t,n,r)})),s})),mo=fr((function(e,t,n){Rt(e,n,t)})),go=fr((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),vo=Nn((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Kr(e,t[0],t[1])?t=[]:n>2&&Kr(t[0],t[1],t[2])&&(t=[t[0]]),Tn(e,Pt(t,1),[])})),_o=Za||function(){return tn.Date.now()},yo=Nn((function(e,t,n){var r=1;if(n.length){var i=A(n,Hr(yo));r|=G}return Lr(e,r,t,n,i)})),bo=Nn((function(e,t,n){var r=3;if(n.length){var i=A(n,Hr(bo));r|=G}return Lr(t,r,e,n,i)})),wo=Nn((function(e,t){return It(e,1,t)})),ko=Nn((function(e,t,n){return It(e,Zi(t)||0,n)}));xi.Cache=mt;var So=Ls((function(t,n){var r=(n=1==n.length&&xo(n[0])?l(n[0],D($r())):l(Pt(n,1),D($r()))).length;return Nn((function(i){for(var a=-1,s=as(i.length,r);++a<s;)i[a]=n[a].call(this,i[a]);return e(t,this,i)}))})),Co=Nn((function(e,t){return Lr(e,G,H,t,A(t,Hr(Co)))})),Mo=Nn((function(e,t){return Lr(e,q,H,t,A(t,Hr(Mo)))})),Do=Er((function(e,t){return Lr(e,J,H,H,H,t)})),Ro=Tr(Jt),Oo=Tr((function(e,t){return e>=t})),To=sn(function(){return arguments}())?sn:function(e){return Wi(e)&&Ta.call(e,"callee")&&!Ha.call(e,"callee")},xo=ma.isArray,Yo=ln?D(ln):function(e){return Wi(e)&&Vt(e)==be},Io=es||pa,Lo=un?D(un):function(e){return Wi(e)&&Vt(e)==oe},Uo=cn?D(cn):function(e){return Wi(e)&&Fs(e)==de},No=dn?D(dn):function(e){return Wi(e)&&Vt(e)==me},Ao=fn?D(fn):function(e){return Wi(e)&&Fs(e)==ge},Po=hn?D(hn):function(e){return Wi(e)&&Ei(e.length)&&!!qt[Vt(e)]},Eo=Tr(Sn),Fo=Tr((function(e,t){return e<=t})),Wo=hr((function(e,t){if(ei(t)||Li(t))return dr(t,ea(t),e),H;for(var n in t)Ta.call(t,n)&&St(e,n,t[n])})),jo=hr((function(e,t){dr(t,ta(t),e)})),Ho=hr((function(e,t,n,r){dr(t,ta(t),e,r)})),$o=hr((function(e,t,n,r){dr(t,ea(t),e,r)})),zo=Er(Ot),Vo=Nn((function(e,t){e=ba(e);var n=-1,r=t.length,i=r>2?t[2]:H;for(i&&Kr(t[0],t[1],i)&&(r=1);++n<r;)for(var a=t[n],s=ta(a),o=-1,l=s.length;++o<l;){var u=s[o],c=e[u];(c===H||Ii(c,Da[u])&&!Ta.call(e,u))&&(e[u]=a[u])}return e})),Bo=Nn((function(t){return t.push(H,Nr),e(Ko,H,t)})),Go=Sr((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ia.call(t)),e[t]=n}),oa(la)),qo=Sr((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ia.call(t)),Ta.call(e,t)?e[t].push(n):e[t]=[n]}),$r),Zo=Nn(rn),Jo=hr((function(e,t,n){Rn(e,t,n)})),Ko=hr((function(e,t,n,r){Rn(e,t,n,r)})),Qo=Er((function(e,t){var n={};if(null==e)return n;var r=!1;t=l(t,(function(t){return t=nr(t,e),r||(r=t.length>1),t})),dr(e,Wr(e),n),r&&(n=xt(n,7,Ar));for(var i=t.length;i--;)qn(n,t[i]);return n})),Xo=Er((function(e,t){return null==e?{}:function(e,t){return xn(e,t,(function(t,n){return Xi(e,n)}))}(e,t)})),el=Ir(ea),tl=Ir(ta),nl=vr((function(e,t,n){return t=t.toLowerCase(),e+(n?ia(t):t)})),rl=vr((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),il=vr((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),al=gr("toLowerCase"),sl=vr((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),ol=vr((function(e,t,n){return e+(n?" ":"")+ul(t)})),ll=vr((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),ul=gr("toUpperCase"),cl=Nn((function(t,n){try{return e(t,H,n)}catch(e){return Ni(e)?e:new va(e)}})),dl=Er((function(e,t){return n(t,(function(t){t=ui(t),Rt(e,t,yo(e[t],e))})),e})),fl=wr(),hl=wr(!0),pl=Nn((function(e,t){return function(n){return rn(n,e,t)}})),ml=Nn((function(e,t){return function(n){return rn(e,n,t)}})),gl=Mr(l),vl=Mr(i),_l=Mr(f),yl=Or(),bl=Or(!0),wl=Cr((function(e,t){return e+t}),0),kl=Yr("ceil"),Sl=Cr((function(e,t){return e/t}),1),Cl=Yr("floor"),Ml=Cr((function(e,t){return e*t}),1),Dl=Yr("round"),Rl=Cr((function(e,t){return e-t}),0);return Ke.after=function(e,t){if("function"!=typeof t)throw new Sa($);return e=Gi(e),function(){if(--e<1)return t.apply(this,arguments)}},Ke.ary=Ri,Ke.assign=Wo,Ke.assignIn=jo,Ke.assignInWith=Ho,Ke.assignWith=$o,Ke.at=zo,Ke.before=Oi,Ke.bind=yo,Ke.bindAll=dl,Ke.bindKey=bo,Ke.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return xo(e)?e:[e]},Ke.chain=ki,Ke.chunk=function(e,t,n){t=(n?Kr(e,t,n):t===H)?1:is(Gi(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var i=0,a=0,s=ma(Ka(r/t));i<r;)s[a++]=Wn(e,i,i+=t);return s},Ke.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var a=e[t];a&&(i[r++]=a)}return i},Ke.concat=function(){var e=arguments.length;if(!e)return[];for(var t=ma(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return u(xo(n)?cr(n):[n],Pt(t,1))},Ke.cond=function(t){var n=null==t?0:t.length,r=$r();return t=n?l(t,(function(e){if("function"!=typeof e[1])throw new Sa($);return[r(e[0]),e[1]]})):[],Nn((function(r){for(var i=-1;++i<n;){var a=t[i];if(e(a[0],this,r))return e(a[1],this,r)}}))},Ke.conforms=function(e){return function(e){var t=ea(e);return function(n){return Yt(n,e,t)}}(xt(e,1))},Ke.constant=oa,Ke.countBy=uo,Ke.create=function(e,t){var n=Ds(e);return null==t?n:Dt(n,t)},Ke.curry=function e(t,n,r){var i=Lr(t,8,H,H,H,H,H,n=r?H:n);return i.placeholder=e.placeholder,i},Ke.curryRight=function e(t,n,r){var i=Lr(t,B,H,H,H,H,H,n=r?H:n);return i.placeholder=e.placeholder,i},Ke.debounce=Ti,Ke.defaults=Vo,Ke.defaultsDeep=Bo,Ke.defer=wo,Ke.delay=ko,Ke.difference=Vs,Ke.differenceBy=Bs,Ke.differenceWith=Gs,Ke.drop=function(e,t,n){var r=null==e?0:e.length;return r?Wn(e,(t=n||t===H?1:Gi(t))<0?0:t,r):[]},Ke.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?Wn(e,0,(t=r-(t=n||t===H?1:Gi(t)))<0?0:t):[]},Ke.dropRightWhile=function(e,t){return e&&e.length?Jn(e,$r(t,3),!0,!0):[]},Ke.dropWhile=function(e,t){return e&&e.length?Jn(e,$r(t,3),!0):[]},Ke.fill=function(e,t,n,r){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&Kr(e,t,n)&&(n=0,r=i),function(e,t,n,r){var i=e.length;for((n=Gi(n))<0&&(n=-n>i?0:i+n),(r=r===H||r>i?i:Gi(r))<0&&(r+=i),r=n>r?0:qi(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},Ke.filter=function(e,t){return(xo(e)?a:At)(e,$r(t,3))},Ke.flatMap=function(e,t){return Pt(Di(e,t),1)},Ke.flatMapDeep=function(e,t){return Pt(Di(e,t),K)},Ke.flatMapDepth=function(e,t,n){return n=n===H?1:Gi(n),Pt(Di(e,t),n)},Ke.flatten=mi,Ke.flattenDeep=function(e){return null!=e&&e.length?Pt(e,K):[]},Ke.flattenDepth=function(e,t){return null!=e&&e.length?Pt(e,t=t===H?1:Gi(t)):[]},Ke.flip=function(e){return Lr(e,512)},Ke.flow=fl,Ke.flowRight=hl,Ke.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var i=e[t];r[i[0]]=i[1]}return r},Ke.functions=function(e){return null==e?[]:Ht(e,ea(e))},Ke.functionsIn=function(e){return null==e?[]:Ht(e,ta(e))},Ke.groupBy=ho,Ke.initial=function(e){return null!=e&&e.length?Wn(e,0,-1):[]},Ke.intersection=qs,Ke.intersectionBy=Zs,Ke.intersectionWith=Js,Ke.invert=Go,Ke.invertBy=qo,Ke.invokeMap=po,Ke.iteratee=ua,Ke.keyBy=mo,Ke.keys=ea,Ke.keysIn=ta,Ke.map=Di,Ke.mapKeys=function(e,t){var n={};return t=$r(t,3),Et(e,(function(e,r,i){Rt(n,t(e,r,i),e)})),n},Ke.mapValues=function(e,t){var n={};return t=$r(t,3),Et(e,(function(e,r,i){Rt(n,r,t(e,r,i))})),n},Ke.matches=function(e){return Mn(xt(e,1))},Ke.matchesProperty=function(e,t){return Dn(e,xt(t,1))},Ke.memoize=xi,Ke.merge=Jo,Ke.mergeWith=Ko,Ke.method=pl,Ke.methodOf=ml,Ke.mixin=ca,Ke.negate=Yi,Ke.nthArg=function(e){return e=Gi(e),Nn((function(t){return On(t,e)}))},Ke.omit=Qo,Ke.omitBy=function(e,t){return na(e,Yi($r(t)))},Ke.once=function(e){return Oi(2,e)},Ke.orderBy=function(e,t,n,r){return null==e?[]:(xo(t)||(t=null==t?[]:[t]),xo(n=r?H:n)||(n=null==n?[]:[n]),Tn(e,t,n))},Ke.over=gl,Ke.overArgs=So,Ke.overEvery=vl,Ke.overSome=_l,Ke.partial=Co,Ke.partialRight=Mo,Ke.partition=go,Ke.pick=Xo,Ke.pickBy=na,Ke.property=fa,Ke.propertyOf=function(e){return function(t){return null==e?H:$t(e,t)}},Ke.pull=Ks,Ke.pullAll=_i,Ke.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Yn(e,t,$r(n,2)):e},Ke.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Yn(e,t,H,n):e},Ke.pullAt=Qs,Ke.range=yl,Ke.rangeRight=bl,Ke.rearg=Do,Ke.reject=function(e,t){return(xo(e)?a:At)(e,Yi($r(t,3)))},Ke.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,i=[],a=e.length;for(t=$r(t,3);++r<a;){var s=e[r];t(s,r,e)&&(n.push(s),i.push(r))}return In(e,i),n},Ke.rest=function(e,t){if("function"!=typeof e)throw new Sa($);return Nn(e,t=t===H?t:Gi(t))},Ke.reverse=yi,Ke.sampleSize=function(e,t,n){return t=(n?Kr(e,t,n):t===H)?1:Gi(t),(xo(e)?bt:Pn)(e,t)},Ke.set=function(e,t,n){return null==e?e:En(e,t,n)},Ke.setWith=function(e,t,n,r){return r="function"==typeof r?r:H,null==e?e:En(e,t,n,r)},Ke.shuffle=function(e){return(xo(e)?wt:Fn)(e)},Ke.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&Kr(e,t,n)?(t=0,n=r):(t=null==t?0:Gi(t),n=n===H?r:Gi(n)),Wn(e,t,n)):[]},Ke.sortBy=vo,Ke.sortedUniq=function(e){return e&&e.length?zn(e):[]},Ke.sortedUniqBy=function(e,t){return e&&e.length?zn(e,$r(t,2)):[]},Ke.split=function(e,t,n){return n&&"number"!=typeof n&&Kr(e,t,n)&&(t=n=H),(n=n===H?ee:n>>>0)?(e=Ki(e))&&("string"==typeof t||null!=t&&!No(t))&&(!(t=Bn(t))&&I(e))?rr(F(e),0,n):e.split(t,n):[]},Ke.spread=function(t,n){if("function"!=typeof t)throw new Sa($);return n=null==n?0:is(Gi(n),0),Nn((function(r){var i=r[n],a=rr(r,0,n);return i&&u(a,i),e(t,this,a)}))},Ke.tail=function(e){var t=null==e?0:e.length;return t?Wn(e,1,t):[]},Ke.take=function(e,t,n){return e&&e.length?Wn(e,0,(t=n||t===H?1:Gi(t))<0?0:t):[]},Ke.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?Wn(e,(t=r-(t=n||t===H?1:Gi(t)))<0?0:t,r):[]},Ke.takeRightWhile=function(e,t){return e&&e.length?Jn(e,$r(t,3),!1,!0):[]},Ke.takeWhile=function(e,t){return e&&e.length?Jn(e,$r(t,3)):[]},Ke.tap=function(e,t){return t(e),e},Ke.throttle=function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new Sa($);return Fi(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),Ti(e,t,{leading:r,maxWait:t,trailing:i})},Ke.thru=Si,Ke.toArray=Vi,Ke.toPairs=el,Ke.toPairsIn=tl,Ke.toPath=function(e){return xo(e)?l(e,ui):zi(e)?[e]:cr(zs(Ki(e)))},Ke.toPlainObject=Ji,Ke.transform=function(e,t,r){var i=xo(e),a=i||Io(e)||Po(e);if(t=$r(t,4),null==r){var s=e&&e.constructor;r=a?i?new s:[]:Fi(e)&&Ai(s)?Ds(Wa(e)):{}}return(a?n:Et)(e,(function(e,n,i){return t(r,e,n,i)})),r},Ke.unary=function(e){return Ri(e,1)},Ke.union=Xs,Ke.unionBy=eo,Ke.unionWith=to,Ke.uniq=function(e){return e&&e.length?Gn(e):[]},Ke.uniqBy=function(e,t){return e&&e.length?Gn(e,$r(t,2)):[]},Ke.uniqWith=function(e,t){return t="function"==typeof t?t:H,e&&e.length?Gn(e,H,t):[]},Ke.unset=function(e,t){return null==e||qn(e,t)},Ke.unzip=bi,Ke.unzipWith=wi,Ke.update=function(e,t,n){return null==e?e:Zn(e,t,tr(n))},Ke.updateWith=function(e,t,n,r){return r="function"==typeof r?r:H,null==e?e:Zn(e,t,tr(n),r)},Ke.values=ra,Ke.valuesIn=function(e){return null==e?[]:R(e,ta(e))},Ke.without=no,Ke.words=sa,Ke.wrap=function(e,t){return Co(tr(t),e)},Ke.xor=ro,Ke.xorBy=io,Ke.xorWith=ao,Ke.zip=so,Ke.zipObject=function(e,t){return Xn(e||[],t||[],St)},Ke.zipObjectDeep=function(e,t){return Xn(e||[],t||[],En)},Ke.zipWith=oo,Ke.entries=el,Ke.entriesIn=tl,Ke.extend=jo,Ke.extendWith=Ho,ca(Ke,Ke),Ke.add=wl,Ke.attempt=cl,Ke.camelCase=nl,Ke.capitalize=ia,Ke.ceil=kl,Ke.clamp=function(e,t,n){return n===H&&(n=t,t=H),n!==H&&(n=(n=Zi(n))==n?n:0),t!==H&&(t=(t=Zi(t))==t?t:0),Tt(Zi(e),t,n)},Ke.clone=function(e){return xt(e,4)},Ke.cloneDeep=function(e){return xt(e,5)},Ke.cloneDeepWith=function(e,t){return xt(e,5,t="function"==typeof t?t:H)},Ke.cloneWith=function(e,t){return xt(e,4,t="function"==typeof t?t:H)},Ke.conformsTo=function(e,t){return null==t||Yt(e,t,ea(t))},Ke.deburr=aa,Ke.defaultTo=function(e,t){return null==e||e!=e?t:e},Ke.divide=Sl,Ke.endsWith=function(e,t,n){e=Ki(e),t=Bn(t);var r=e.length,i=n=n===H?r:Tt(Gi(n),0,r);return(n-=t.length)>=0&&e.slice(n,i)==t},Ke.eq=Ii,Ke.escape=function(e){return(e=Ki(e))&&Pe.test(e)?e.replace(Ne,gn):e},Ke.escapeRegExp=function(e){return(e=Ki(e))&&Ve.test(e)?e.replace(ze,"\\$&"):e},Ke.every=function(e,t,n){var r=xo(e)?i:Ut;return n&&Kr(e,t,n)&&(t=H),r(e,$r(t,3))},Ke.find=co,Ke.findIndex=hi,Ke.findKey=function(e,t){return p(e,$r(t,3),Et)},Ke.findLast=fo,Ke.findLastIndex=pi,Ke.findLastKey=function(e,t){return p(e,$r(t,3),Ft)},Ke.floor=Cl,Ke.forEach=Ci,Ke.forEachRight=Mi,Ke.forIn=function(e,t){return null==e?e:Ts(e,$r(t,3),ta)},Ke.forInRight=function(e,t){return null==e?e:xs(e,$r(t,3),ta)},Ke.forOwn=function(e,t){return e&&Et(e,$r(t,3))},Ke.forOwnRight=function(e,t){return e&&Ft(e,$r(t,3))},Ke.get=Qi,Ke.gt=Ro,Ke.gte=Oo,Ke.has=function(e,t){return null!=e&&Gr(e,t,Xt)},Ke.hasIn=Xi,Ke.head=gi,Ke.identity=la,Ke.includes=function(e,t,n,r){e=Li(e)?e:ra(e),n=n&&!r?Gi(n):0;var i=e.length;return n<0&&(n=is(i+n,0)),$i(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&g(e,t,n)>-1},Ke.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:Gi(n);return i<0&&(i=is(r+i,0)),g(e,t,i)},Ke.inRange=function(e,t,n){return t=Bi(t),n===H?(n=t,t=0):n=Bi(n),function(e,t,n){return e>=as(t,n)&&e<is(t,n)}(e=Zi(e),t,n)},Ke.invoke=Zo,Ke.isArguments=To,Ke.isArray=xo,Ke.isArrayBuffer=Yo,Ke.isArrayLike=Li,Ke.isArrayLikeObject=Ui,Ke.isBoolean=function(e){return!0===e||!1===e||Wi(e)&&Vt(e)==se},Ke.isBuffer=Io,Ke.isDate=Lo,Ke.isElement=function(e){return Wi(e)&&1===e.nodeType&&!Hi(e)},Ke.isEmpty=function(e){if(null==e)return!0;if(Li(e)&&(xo(e)||"string"==typeof e||"function"==typeof e.splice||Io(e)||Po(e)||To(e)))return!e.length;var t=Fs(e);if(t==de||t==ge)return!e.size;if(ei(e))return!wn(e).length;for(var n in e)if(Ta.call(e,n))return!1;return!0},Ke.isEqual=function(e,t){return on(e,t)},Ke.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:H)?n(e,t):H;return r===H?on(e,t,H,n):!!r},Ke.isError=Ni,Ke.isFinite=function(e){return"number"==typeof e&&ts(e)},Ke.isFunction=Ai,Ke.isInteger=Pi,Ke.isLength=Ei,Ke.isMap=Uo,Ke.isMatch=function(e,t){return e===t||pn(e,t,Vr(t))},Ke.isMatchWith=function(e,t,n){return n="function"==typeof n?n:H,pn(e,t,Vr(t),n)},Ke.isNaN=function(e){return ji(e)&&e!=+e},Ke.isNative=function(e){if(Ws(e))throw new va("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return yn(e)},Ke.isNil=function(e){return null==e},Ke.isNull=function(e){return null===e},Ke.isNumber=ji,Ke.isObject=Fi,Ke.isObjectLike=Wi,Ke.isPlainObject=Hi,Ke.isRegExp=No,Ke.isSafeInteger=function(e){return Pi(e)&&e>=-Q&&e<=Q},Ke.isSet=Ao,Ke.isString=$i,Ke.isSymbol=zi,Ke.isTypedArray=Po,Ke.isUndefined=function(e){return e===H},Ke.isWeakMap=function(e){return Wi(e)&&Fs(e)==ye},Ke.isWeakSet=function(e){return Wi(e)&&"[object WeakSet]"==Vt(e)},Ke.join=function(e,t){return null==e?"":ns.call(e,t)},Ke.kebabCase=rl,Ke.last=vi,Ke.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r;return n!==H&&(i=(i=Gi(n))<0?is(r+i,0):as(i,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,i):m(e,_,i,!0)},Ke.lowerCase=il,Ke.lowerFirst=al,Ke.lt=Eo,Ke.lte=Fo,Ke.max=function(e){return e&&e.length?Nt(e,la,Jt):H},Ke.maxBy=function(e,t){return e&&e.length?Nt(e,$r(t,2),Jt):H},Ke.mean=function(e){return y(e,la)},Ke.meanBy=function(e,t){return y(e,$r(t,2))},Ke.min=function(e){return e&&e.length?Nt(e,la,Sn):H},Ke.minBy=function(e,t){return e&&e.length?Nt(e,$r(t,2),Sn):H},Ke.stubArray=ha,Ke.stubFalse=pa,Ke.stubObject=function(){return{}},Ke.stubString=function(){return""},Ke.stubTrue=function(){return!0},Ke.multiply=Ml,Ke.nth=function(e,t){return e&&e.length?On(e,Gi(t)):H},Ke.noConflict=function(){return tn._===this&&(tn._=Ua),this},Ke.noop=da,Ke.now=_o,Ke.pad=function(e,t,n){e=Ki(e);var r=(t=Gi(t))?E(e):0;if(!t||r>=t)return e;var i=(t-r)/2;return Dr(Qa(i),n)+e+Dr(Ka(i),n)},Ke.padEnd=function(e,t,n){e=Ki(e);var r=(t=Gi(t))?E(e):0;return t&&r<t?e+Dr(t-r,n):e},Ke.padStart=function(e,t,n){e=Ki(e);var r=(t=Gi(t))?E(e):0;return t&&r<t?Dr(t-r,n)+e:e},Ke.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),os(Ki(e).replace(Be,""),t||0)},Ke.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Kr(e,t,n)&&(t=n=H),n===H&&("boolean"==typeof t?(n=t,t=H):"boolean"==typeof e&&(n=e,e=H)),e===H&&t===H?(e=0,t=1):(e=Bi(e),t===H?(t=e,e=0):t=Bi(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var i=ls();return as(e+i*(t-e+Kt("1e-"+((i+"").length-1))),t)}return Ln(e,t)},Ke.reduce=function(e,t,n){var r=xo(e)?c:k,i=arguments.length<3;return r(e,$r(t,4),n,i,Rs)},Ke.reduceRight=function(e,t,n){var r=xo(e)?d:k,i=arguments.length<3;return r(e,$r(t,4),n,i,Os)},Ke.repeat=function(e,t,n){return t=(n?Kr(e,t,n):t===H)?1:Gi(t),Un(Ki(e),t)},Ke.replace=function(){var e=arguments,t=Ki(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Ke.result=function(e,t,n){var r=-1,i=(t=nr(t,e)).length;for(i||(i=1,e=H);++r<i;){var a=null==e?H:e[ui(t[r])];a===H&&(r=i,a=n),e=Ai(a)?a.call(e):a}return e},Ke.round=Dl,Ke.runInContext=w,Ke.sample=function(e){return(xo(e)?yt:An)(e)},Ke.size=function(e){if(null==e)return 0;if(Li(e))return $i(e)?E(e):e.length;var t=Fs(e);return t==de||t==ge?e.size:wn(e).length},Ke.snakeCase=sl,Ke.some=function(e,t,n){var r=xo(e)?f:jn;return n&&Kr(e,t,n)&&(t=H),r(e,$r(t,3))},Ke.sortedIndex=function(e,t){return Hn(e,t)},Ke.sortedIndexBy=function(e,t,n){return $n(e,t,$r(n,2))},Ke.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=Hn(e,t);if(r<n&&Ii(e[r],t))return r}return-1},Ke.sortedLastIndex=function(e,t){return Hn(e,t,!0)},Ke.sortedLastIndexBy=function(e,t,n){return $n(e,t,$r(n,2),!0)},Ke.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Hn(e,t,!0)-1;if(Ii(e[n],t))return n}return-1},Ke.startCase=ol,Ke.startsWith=function(e,t,n){return e=Ki(e),n=null==n?0:Tt(Gi(n),0,e.length),t=Bn(t),e.slice(n,n+t.length)==t},Ke.subtract=Rl,Ke.sum=function(e){return e&&e.length?S(e,la):0},Ke.sumBy=function(e,t){return e&&e.length?S(e,$r(t,2)):0},Ke.template=function(e,t,n){var r=Ke.templateSettings;n&&Kr(e,t,n)&&(t=H),e=Ki(e),t=Ho({},t,r,Ur);var i,a,s=Ho({},t.imports,r.imports,Ur),o=ea(s),l=R(s,o),u=0,c=t.interpolate||lt,d="__p += '",f=wa((t.escape||lt).source+"|"+c.source+"|"+(c===We?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),h="//# sourceURL="+(Ta.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Gt+"]")+"\n";e.replace(f,(function(t,n,r,s,o,l){return r||(r=s),d+=e.slice(u,l).replace(ut,Y),n&&(i=!0,d+="' +\n__e("+n+") +\n'"),o&&(a=!0,d+="';\n"+o+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),u=l+t.length,t})),d+="';\n";var p=Ta.call(t,"variable")&&t.variable;if(p){if(Qe.test(p))throw new va("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(a?d.replace(Ye,""):d).replace(Ie,"$1").replace(Le,"$1;"),d="function("+(p||"obj")+") {\n"+(p?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var m=cl((function(){return _a(o,h+"return "+d).apply(H,l)}));if(m.source=d,Ni(m))throw m;return m},Ke.times=function(e,t){if((e=Gi(e))<1||e>Q)return[];var n=ee,r=as(e,ee);t=$r(t),e-=ee;for(var i=C(r,t);++n<e;)t(n);return i},Ke.toFinite=Bi,Ke.toInteger=Gi,Ke.toLength=qi,Ke.toLower=function(e){return Ki(e).toLowerCase()},Ke.toNumber=Zi,Ke.toSafeInteger=function(e){return e?Tt(Gi(e),-Q,Q):0===e?e:0},Ke.toString=Ki,Ke.toUpper=function(e){return Ki(e).toUpperCase()},Ke.trim=function(e,t,n){if((e=Ki(e))&&(n||t===H))return M(e);if(!e||!(t=Bn(t)))return e;var r=F(e),i=F(t);return rr(r,T(r,i),x(r,i)+1).join("")},Ke.trimEnd=function(e,t,n){if((e=Ki(e))&&(n||t===H))return e.slice(0,W(e)+1);if(!e||!(t=Bn(t)))return e;var r=F(e);return rr(r,0,x(r,F(t))+1).join("")},Ke.trimStart=function(e,t,n){if((e=Ki(e))&&(n||t===H))return e.replace(Be,"");if(!e||!(t=Bn(t)))return e;var r=F(e);return rr(r,T(r,F(t))).join("")},Ke.truncate=function(e,t){var n=30,r="...";if(Fi(t)){var i="separator"in t?t.separator:i;n="length"in t?Gi(t.length):n,r="omission"in t?Bn(t.omission):r}var a=(e=Ki(e)).length;if(I(e)){var s=F(e);a=s.length}if(n>=a)return e;var o=n-E(r);if(o<1)return r;var l=s?rr(s,0,o).join(""):e.slice(0,o);if(i===H)return l+r;if(s&&(o+=l.length-o),No(i)){if(e.slice(o).search(i)){var u,c=l;for(i.global||(i=wa(i.source,Ki(tt.exec(i))+"g")),i.lastIndex=0;u=i.exec(c);)var d=u.index;l=l.slice(0,d===H?o:d)}}else if(e.indexOf(Bn(i),o)!=o){var f=l.lastIndexOf(i);f>-1&&(l=l.slice(0,f))}return l+r},Ke.unescape=function(e){return(e=Ki(e))&&Ae.test(e)?e.replace(Ue,vn):e},Ke.uniqueId=function(e){var t=++xa;return Ki(e)+t},Ke.upperCase=ll,Ke.upperFirst=ul,Ke.each=Ci,Ke.eachRight=Mi,Ke.first=gi,ca(Ke,function(){var e={};return Et(Ke,(function(t,n){Ta.call(Ke.prototype,n)||(e[n]=t)})),e}(),{chain:!1}),Ke.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Ke[e].placeholder=Ke})),n(["drop","take"],(function(e,t){ft.prototype[e]=function(n){n=n===H?1:is(Gi(n),0);var r=this.__filtered__&&!t?new ft(this):this.clone();return r.__filtered__?r.__takeCount__=as(n,r.__takeCount__):r.__views__.push({size:as(n,ee),type:e+(r.__dir__<0?"Right":"")}),r},ft.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),n(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;ft.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:$r(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),n(["head","last"],(function(e,t){var n="take"+(t?"Right":"");ft.prototype[e]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");ft.prototype[e]=function(){return this.__filtered__?new ft(this):this[n](1)}})),ft.prototype.compact=function(){return this.filter(la)},ft.prototype.find=function(e){return this.filter(e).head()},ft.prototype.findLast=function(e){return this.reverse().find(e)},ft.prototype.invokeMap=Nn((function(e,t){return"function"==typeof e?new ft(this):this.map((function(n){return rn(n,e,t)}))})),ft.prototype.reject=function(e){return this.filter(Yi($r(e)))},ft.prototype.slice=function(e,t){e=Gi(e);var n=this;return n.__filtered__&&(e>0||t<0)?new ft(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==H&&(n=(t=Gi(t))<0?n.dropRight(-t):n.take(t-e)),n)},ft.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},ft.prototype.toArray=function(){return this.take(ee)},Et(ft.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),i=Ke[r?"take"+("last"==t?"Right":""):t],a=r||/^find/.test(t);i&&(Ke.prototype[t]=function(){var t=this.__wrapped__,s=r?[1]:arguments,o=t instanceof ft,l=s[0],c=o||xo(t),d=function(e){var t=i.apply(Ke,u([e],s));return r&&f?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(o=c=!1);var f=this.__chain__,h=!!this.__actions__.length,p=a&&!f,m=o&&!h;if(!a&&c){t=m?t:new ft(this);var g=e.apply(t,s);return g.__actions__.push({func:Si,args:[d],thisArg:H}),new dt(g,f)}return p&&m?e.apply(this,s):(g=this.thru(d),p?r?g.value()[0]:g.value():g)})})),n(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Ca[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Ke.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var i=this.value();return t.apply(xo(i)?i:[],e)}return this[n]((function(n){return t.apply(xo(n)?n:[],e)}))}})),Et(ft.prototype,(function(e,t){var n=Ke[t];if(n){var r=n.name+"";Ta.call(vs,r)||(vs[r]=[]),vs[r].push({name:t,func:n})}})),vs[kr(H,2).name]=[{name:"wrapper",func:H}],ft.prototype.clone=function(){var e=new ft(this.__wrapped__);return e.__actions__=cr(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=cr(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=cr(this.__views__),e},ft.prototype.reverse=function(){if(this.__filtered__){var e=new ft(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},ft.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=xo(e),r=t<0,i=n?e.length:0,a=function(e,t,n){for(var r=-1,i=n.length;++r<i;){var a=n[r],s=a.size;switch(a.type){case"drop":e+=s;break;case"dropRight":t-=s;break;case"take":t=as(t,e+s);break;case"takeRight":e=is(e,t-s)}}return{start:e,end:t}}(0,i,this.__views__),s=a.start,o=a.end,l=o-s,u=r?o:s-1,c=this.__iteratees__,d=c.length,f=0,h=as(l,this.__takeCount__);if(!n||!r&&i==l&&h==l)return Kn(e,this.__actions__);var p=[];e:for(;l--&&f<h;){for(var m=-1,g=e[u+=t];++m<d;){var v=c[m],_=v.iteratee,y=v.type,b=_(g);if(2==y)g=b;else if(!b){if(1==y)continue e;break e}}p[f++]=g}return p},Ke.prototype.at=lo,Ke.prototype.chain=function(){return ki(this)},Ke.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Ke.prototype.next=function(){this.__values__===H&&(this.__values__=Vi(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?H:this.__values__[this.__index__++]}},Ke.prototype.plant=function(e){for(var t,n=this;n instanceof ct;){var r=fi(n);r.__index__=0,r.__values__=H,t?i.__wrapped__=r:t=r;var i=r;n=n.__wrapped__}return i.__wrapped__=e,t},Ke.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof ft){var t=e;return this.__actions__.length&&(t=new ft(this)),(t=t.reverse()).__actions__.push({func:Si,args:[yi],thisArg:H}),new dt(t,this.__chain__)}return this.thru(yi)},Ke.prototype.toJSON=Ke.prototype.valueOf=Ke.prototype.value=function(){return Kn(this.__wrapped__,this.__actions__)},Ke.prototype.first=Ke.prototype.head,Va&&(Ke.prototype[Va]=function(){return this}),Ke}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=_n,define((function(){return _n}))):rn?((rn.exports=_n)._=_n,nn._=_n):tn._=_n}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid">\n                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach((e=>{e.items.forEach((e=>{e.is_active=n===t(e.url);let r=!1;e.sub_items.forEach((i=>{i.is_active=n===t(i.url),i.is_active&&i.parent_id===e.id&&(r=!0)})),e.is_expended=!!r}))}))},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,r={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",r)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:r}=e;null===sessionStorage.getItem("activeOrg")&&(n?r.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(r[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,(function(){"use strict";var e;function t(){return e.apply(null,arguments)}function n(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function r(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function a(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(i(e,t))return;return 1}function s(e){return void 0===e}function o(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function l(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function u(e,t){for(var n=[],r=e.length,i=0;i<r;++i)n.push(t(e[i],i));return n}function c(e,t){for(var n in t)i(t,n)&&(e[n]=t[n]);return i(t,"toString")&&(e.toString=t.toString),i(t,"valueOf")&&(e.valueOf=t.valueOf),e}function d(e,t,n,r){return Dt(e,t,n,r,!0).utc()}function f(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function h(e){var t,n,r=e._d&&!isNaN(e._d.getTime());return r&&(t=f(e),n=m.call(t.parsedDateParts,(function(e){return null!=e})),r=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?r:(e._isValid=r,e._isValid)}function p(e){var t=d(NaN);return null!=e?c(f(t),e):f(t).userInvalidated=!0,t}var m=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,r=0;r<n;r++)if(r in t&&e.call(this,t[r],r,t))return!0;return!1},g=t.momentProperties=[],v=!1;function _(e,t){var n,r,i,a=g.length;if(s(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),s(t._i)||(e._i=t._i),s(t._f)||(e._f=t._f),s(t._l)||(e._l=t._l),s(t._strict)||(e._strict=t._strict),s(t._tzm)||(e._tzm=t._tzm),s(t._isUTC)||(e._isUTC=t._isUTC),s(t._offset)||(e._offset=t._offset),s(t._pf)||(e._pf=f(t)),s(t._locale)||(e._locale=t._locale),0<a)for(n=0;n<a;n++)s(i=t[r=g[n]])||(e[r]=i);return e}function y(e){_(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===v&&(v=!0,t.updateOffset(this),v=!1)}function b(e){return e instanceof y||null!=e&&null!=e._isAMomentObject}function w(e){!1===t.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function k(e,n){var r=!0;return c((function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),r){for(var a,s,o=[],l=arguments.length,u=0;u<l;u++){if(a="","object"==typeof arguments[u]){for(s in a+="\n["+u+"] ",arguments[0])i(arguments[0],s)&&(a+=s+": "+arguments[0][s]+", ");a=a.slice(0,-2)}else a=arguments[u];o.push(a)}w(e+"\nArguments: "+Array.prototype.slice.call(o).join("")+"\n"+(new Error).stack),r=!1}return n.apply(this,arguments)}),n)}var S={};function C(e,n){null!=t.deprecationHandler&&t.deprecationHandler(e,n),S[e]||(w(n),S[e]=!0)}function M(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function D(e,t){var n,a=c({},e);for(n in t)i(t,n)&&(r(e[n])&&r(t[n])?(a[n]={},c(a[n],e[n]),c(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in e)i(e,n)&&!i(t,n)&&r(e[n])&&(a[n]=c({},a[n]));return a}function R(e){null!=e&&this.set(e)}t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var O=Object.keys||function(e){var t,n=[];for(t in e)i(e,t)&&n.push(t);return n};function T(e,t,n){var r=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-r.length)).toString().substr(1)+r}var x=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,Y=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,I={},L={};function U(e,t,n,r){var i="string"==typeof r?function(){return this[r]()}:r;e&&(L[e]=i),t&&(L[t[0]]=function(){return T(i.apply(this,arguments),t[1],t[2])}),n&&(L[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function N(e,t){return e.isValid()?(t=A(t,e.localeData()),I[t]=I[t]||function(e){for(var t,n=e.match(x),r=0,i=n.length;r<i;r++)L[n[r]]?n[r]=L[n[r]]:n[r]=(t=n[r]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var r="",a=0;a<i;a++)r+=M(n[a])?n[a].call(t,e):n[a];return r}}(t),I[t](e)):e.localeData().invalidDate()}function A(e,t){var n=5;function r(e){return t.longDateFormat(e)||e}for(Y.lastIndex=0;0<=n&&Y.test(e);)e=e.replace(Y,r),Y.lastIndex=0,--n;return e}var P={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function E(e){return"string"==typeof e?P[e]||P[e.toLowerCase()]:void 0}function F(e){var t,n,r={};for(n in e)i(e,n)&&(t=E(n))&&(r[t]=e[n]);return r}var W={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},j=/\d/,H=/\d\d/,$=/\d{3}/,z=/\d{4}/,V=/[+-]?\d{6}/,B=/\d\d?/,G=/\d\d\d\d?/,q=/\d\d\d\d\d\d?/,Z=/\d{1,3}/,J=/\d{1,4}/,K=/[+-]?\d{1,6}/,Q=/\d+/,X=/[+-]?\d+/,ee=/Z|[+-]\d\d:?\d\d/gi,te=/Z|[+-]\d\d(?::?\d\d)?/gi,ne=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,re=/^[1-9]\d?/,ie=/^([1-9]\d|\d)/;function ae(e,t,n){ce[e]=M(t)?t:function(e,r){return e&&n?n:t}}function se(e,t){return i(ce,e)?ce[e](t._strict,t._locale):new RegExp(oe(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,n,r,i){return t||n||r||i}))))}function oe(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function le(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function ue(e){var t=0;return 0!=(e=+e)&&isFinite(e)?le(e):t}var ce={},de={};function fe(e,t){var n,r,i=t;for("string"==typeof e&&(e=[e]),o(t)&&(i=function(e,n){n[t]=ue(e)}),r=e.length,n=0;n<r;n++)de[e[n]]=i}function he(e,t){fe(e,(function(e,n,r,i){r._w=r._w||{},t(e,r._w,r,i)}))}function pe(e){return e%4==0&&e%100!=0||e%400==0}var me=0,ge=1,ve=2,_e=3,ye=4,be=5,we=6,ke=7,Se=8;function Ce(e){return pe(e)?366:365}U("Y",0,0,(function(){var e=this.year();return e<=9999?T(e,4):"+"+e})),U(0,["YY",2],0,(function(){return this.year()%100})),U(0,["YYYY",4],0,"year"),U(0,["YYYYY",5],0,"year"),U(0,["YYYYYY",6,!0],0,"year"),ae("Y",X),ae("YY",B,H),ae("YYYY",J,z),ae("YYYYY",K,V),ae("YYYYYY",K,V),fe(["YYYYY","YYYYYY"],me),fe("YYYY",(function(e,n){n[me]=2===e.length?t.parseTwoDigitYear(e):ue(e)})),fe("YY",(function(e,n){n[me]=t.parseTwoDigitYear(e)})),fe("Y",(function(e,t){t[me]=parseInt(e,10)})),t.parseTwoDigitYear=function(e){return ue(e)+(68<ue(e)?1900:2e3)};var Me,De=Re("FullYear",!0);function Re(e,n){return function(r){return null!=r?(Te(this,e,r),t.updateOffset(this,n),this):Oe(this,e)}}function Oe(e,t){if(!e.isValid())return NaN;var n=e._d,r=e._isUTC;switch(t){case"Milliseconds":return r?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return r?n.getUTCSeconds():n.getSeconds();case"Minutes":return r?n.getUTCMinutes():n.getMinutes();case"Hours":return r?n.getUTCHours():n.getHours();case"Date":return r?n.getUTCDate():n.getDate();case"Day":return r?n.getUTCDay():n.getDay();case"Month":return r?n.getUTCMonth():n.getMonth();case"FullYear":return r?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Te(e,t,n){var r,i,a;if(e.isValid()&&!isNaN(n)){switch(r=e._d,i=e._isUTC,t){case"Milliseconds":return i?r.setUTCMilliseconds(n):r.setMilliseconds(n);case"Seconds":return i?r.setUTCSeconds(n):r.setSeconds(n);case"Minutes":return i?r.setUTCMinutes(n):r.setMinutes(n);case"Hours":return i?r.setUTCHours(n):r.setHours(n);case"Date":return i?r.setUTCDate(n):r.setDate(n);case"FullYear":break;default:return}t=n,a=e.month(),e=29!==(e=e.date())||1!==a||pe(t)?e:28,i?r.setUTCFullYear(t,a,e):r.setFullYear(t,a,e)}}function xe(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(e+=(t-(n=(t%(n=12)+n)%n))/12,1==n?pe(e)?29:28:31-n%7%2)}Me=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},U("M",["MM",2],"Mo",(function(){return this.month()+1})),U("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),U("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),ae("M",B,re),ae("MM",B,H),ae("MMM",(function(e,t){return t.monthsShortRegex(e)})),ae("MMMM",(function(e,t){return t.monthsRegex(e)})),fe(["M","MM"],(function(e,t){t[ge]=ue(e)-1})),fe(["MMM","MMMM"],(function(e,t,n,r){null!=(r=n._locale.monthsParse(e,r,n._strict))?t[ge]=r:f(n).invalidMonth=e}));var Ye="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Ie="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Le=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ue=ne,Ne=ne;function Ae(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=ue(t);else if(!o(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,xe(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Pe(e){return null!=e?(Ae(this,e),t.updateOffset(this,!0),this):Oe(this,"Month")}function Ee(){function e(e,t){return t.length-e.length}for(var t,n,r=[],i=[],a=[],s=0;s<12;s++)n=d([2e3,s]),t=oe(this.monthsShort(n,"")),n=oe(this.months(n,"")),r.push(t),i.push(n),a.push(n),a.push(t);r.sort(e),i.sort(e),a.sort(e),this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Fe(e,t,n,r,i,a,s){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,r,i,a,s),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,r,i,a,s),o}function We(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function je(e,t,n){return(n=7+t-n)-(7+We(e,0,n).getUTCDay()-t)%7-1}function He(e,t,n,r,i){var a;n=(t=1+7*(t-1)+(7+n-r)%7+je(e,r,i))<=0?Ce(a=e-1)+t:t>Ce(e)?(a=e+1,t-Ce(e)):(a=e,t);return{year:a,dayOfYear:n}}function $e(e,t,n){var r,i,a=je(e.year(),t,n);return(a=Math.floor((e.dayOfYear()-a-1)/7)+1)<1?r=a+ze(i=e.year()-1,t,n):a>ze(e.year(),t,n)?(r=a-ze(e.year(),t,n),i=e.year()+1):(i=e.year(),r=a),{week:r,year:i}}function ze(e,t,n){var r=je(e,t,n);t=je(e+1,t,n);return(Ce(e)-r+t)/7}function Ve(e,t){return e.slice(t,7).concat(e.slice(0,t))}U("w",["ww",2],"wo","week"),U("W",["WW",2],"Wo","isoWeek"),ae("w",B,re),ae("ww",B,H),ae("W",B,re),ae("WW",B,H),he(["w","ww","W","WW"],(function(e,t,n,r){t[r.substr(0,1)]=ue(e)})),U("d",0,"do","day"),U("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),U("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),U("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),U("e",0,0,"weekday"),U("E",0,0,"isoWeekday"),ae("d",B),ae("e",B),ae("E",B),ae("dd",(function(e,t){return t.weekdaysMinRegex(e)})),ae("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),ae("dddd",(function(e,t){return t.weekdaysRegex(e)})),he(["dd","ddd","dddd"],(function(e,t,n,r){null!=(r=n._locale.weekdaysParse(e,r,n._strict))?t.d=r:f(n).invalidWeekday=e})),he(["d","e","E"],(function(e,t,n,r){t[r]=ue(e)}));var Be="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ge="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),qe="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Ze=ne,Je=ne,Ke=ne;function Qe(){function e(e,t){return t.length-e.length}for(var t,n,r,i=[],a=[],s=[],o=[],l=0;l<7;l++)r=d([2e3,1]).day(l),t=oe(this.weekdaysMin(r,"")),n=oe(this.weekdaysShort(r,"")),r=oe(this.weekdays(r,"")),i.push(t),a.push(n),s.push(r),o.push(t),o.push(n),o.push(r);i.sort(e),a.sort(e),s.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function Xe(){return this.hours()%12||12}function et(e,t){U(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function tt(e,t){return t._meridiemParse}U("H",["HH",2],0,"hour"),U("h",["hh",2],0,Xe),U("k",["kk",2],0,(function(){return this.hours()||24})),U("hmm",0,0,(function(){return""+Xe.apply(this)+T(this.minutes(),2)})),U("hmmss",0,0,(function(){return""+Xe.apply(this)+T(this.minutes(),2)+T(this.seconds(),2)})),U("Hmm",0,0,(function(){return""+this.hours()+T(this.minutes(),2)})),U("Hmmss",0,0,(function(){return""+this.hours()+T(this.minutes(),2)+T(this.seconds(),2)})),et("a",!0),et("A",!1),ae("a",tt),ae("A",tt),ae("H",B,ie),ae("h",B,re),ae("k",B,re),ae("HH",B,H),ae("hh",B,H),ae("kk",B,H),ae("hmm",G),ae("hmmss",q),ae("Hmm",G),ae("Hmmss",q),fe(["H","HH"],_e),fe(["k","kk"],(function(e,t,n){e=ue(e),t[_e]=24===e?0:e})),fe(["a","A"],(function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e})),fe(["h","hh"],(function(e,t,n){t[_e]=ue(e),f(n).bigHour=!0})),fe("hmm",(function(e,t,n){var r=e.length-2;t[_e]=ue(e.substr(0,r)),t[ye]=ue(e.substr(r)),f(n).bigHour=!0})),fe("hmmss",(function(e,t,n){var r=e.length-4,i=e.length-2;t[_e]=ue(e.substr(0,r)),t[ye]=ue(e.substr(r,2)),t[be]=ue(e.substr(i)),f(n).bigHour=!0})),fe("Hmm",(function(e,t,n){var r=e.length-2;t[_e]=ue(e.substr(0,r)),t[ye]=ue(e.substr(r))})),fe("Hmmss",(function(e,t,n){var r=e.length-4,i=e.length-2;t[_e]=ue(e.substr(0,r)),t[ye]=ue(e.substr(r,2)),t[be]=ue(e.substr(i))})),ne=Re("Hours",!0);var nt,rt={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Ye,monthsShort:Ie,week:{dow:0,doy:6},weekdays:Be,weekdaysMin:qe,weekdaysShort:Ge,meridiemParse:/[ap]\.?m?\.?/i},it={},at={};function st(e){return e&&e.toLowerCase().replace("_","-")}function ot(e){var t,n;if(void 0===it[e]&&"undefined"!=typeof module&&module&&module.exports&&(n=e)&&n.match("^[^/\\\\]*$"))try{t=nt._abbr,require("./locale/"+e),lt(t)}catch(t){it[e]=null}return it[e]}function lt(e,t){return e&&((t=s(t)?ct(e):ut(e,t))?nt=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),nt._abbr}function ut(e,t){if(null===t)return delete it[e],null;var n,r=rt;if(t.abbr=e,null!=it[e])C("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=it[e]._config;else if(null!=t.parentLocale)if(null!=it[t.parentLocale])r=it[t.parentLocale]._config;else{if(null==(n=ot(t.parentLocale)))return at[t.parentLocale]||(at[t.parentLocale]=[]),at[t.parentLocale].push({name:e,config:t}),null;r=n._config}return it[e]=new R(D(r,t)),at[e]&&at[e].forEach((function(e){ut(e.name,e.config)})),lt(e),it[e]}function ct(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return nt;if(!n(e)){if(t=ot(e))return t;e=[e]}return function(e){for(var t,n,r,i,a=0;a<e.length;){for(t=(i=st(e[a]).split("-")).length,n=(n=st(e[a+1]))?n.split("-"):null;0<t;){if(r=ot(i.slice(0,t).join("-")))return r;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),r=0;r<n;r+=1)if(e[r]!==t[r])return r;return n}(i,n)>=t-1)break;t--}a++}return nt}(e)}function dt(e){var t=e._a;return t&&-2===f(e).overflow&&(t=t[ge]<0||11<t[ge]?ge:t[ve]<1||t[ve]>xe(t[me],t[ge])?ve:t[_e]<0||24<t[_e]||24===t[_e]&&(0!==t[ye]||0!==t[be]||0!==t[we])?_e:t[ye]<0||59<t[ye]?ye:t[be]<0||59<t[be]?be:t[we]<0||999<t[we]?we:-1,f(e)._overflowDayOfYear&&(t<me||ve<t)&&(t=ve),f(e)._overflowWeeks&&-1===t&&(t=ke),f(e)._overflowWeekday&&-1===t&&(t=Se),f(e).overflow=t),e}var ft=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ht=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pt=/Z|[+-]\d\d(?::?\d\d)?/,mt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],gt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],vt=/^\/?Date\((-?\d+)/i,_t=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,yt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function bt(e){var t,n,r,i,a,s,o=e._i,l=ft.exec(o)||ht.exec(o),u=(o=mt.length,gt.length);if(l){for(f(e).iso=!0,t=0,n=o;t<n;t++)if(mt[t][1].exec(l[1])){i=mt[t][0],r=!1!==mt[t][2];break}if(null==i)e._isValid=!1;else{if(l[3]){for(t=0,n=u;t<n;t++)if(gt[t][1].exec(l[3])){a=(l[2]||" ")+gt[t][0];break}if(null==a)return void(e._isValid=!1)}if(r||null==a){if(l[4]){if(!pt.exec(l[4]))return void(e._isValid=!1);s="Z"}e._f=i+(a||"")+(s||""),Ct(e)}else e._isValid=!1}}else e._isValid=!1}function wt(e){var t,n,r=_t.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));r?(t=function(e,t,n,r,i,a){return e=[function(e){return(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e}(e),Ie.indexOf(t),parseInt(n,10),parseInt(r,10),parseInt(i,10)],a&&e.push(parseInt(a,10)),e}(r[4],r[3],r[2],r[5],r[6],r[7]),function(e,t,n){if(!e||Ge.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;f(n).weekdayMismatch=!0,n._isValid=!1}(r[1],t,e)&&(e._a=t,e._tzm=(t=r[8],n=r[9],r=r[10],t?yt[t]:n?0:((t=parseInt(r,10))-(n=t%100))/100*60+n),e._d=We.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),f(e).rfc2822=!0)):e._isValid=!1}function kt(e,t,n){return null!=e?e:null!=t?t:n}function St(e){var n,r,i,a,s,o,l,u,c,d,h,p=[];if(!e._d){for(i=e,a=new Date(t.now()),r=i._useUTC?[a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate()]:[a.getFullYear(),a.getMonth(),a.getDate()],e._w&&null==e._a[ve]&&null==e._a[ge]&&(null!=(a=(i=e)._w).GG||null!=a.W||null!=a.E?(u=1,c=4,s=kt(a.GG,i._a[me],$e(Rt(),1,4).year),o=kt(a.W,1),((l=kt(a.E,1))<1||7<l)&&(d=!0)):(u=i._locale._week.dow,c=i._locale._week.doy,h=$e(Rt(),u,c),s=kt(a.gg,i._a[me],h.year),o=kt(a.w,h.week),null!=a.d?((l=a.d)<0||6<l)&&(d=!0):null!=a.e?(l=a.e+u,(a.e<0||6<a.e)&&(d=!0)):l=u),o<1||o>ze(s,u,c)?f(i)._overflowWeeks=!0:null!=d?f(i)._overflowWeekday=!0:(h=He(s,o,l,u,c),i._a[me]=h.year,i._dayOfYear=h.dayOfYear)),null!=e._dayOfYear&&(a=kt(e._a[me],r[me]),(e._dayOfYear>Ce(a)||0===e._dayOfYear)&&(f(e)._overflowDayOfYear=!0),d=We(a,0,e._dayOfYear),e._a[ge]=d.getUTCMonth(),e._a[ve]=d.getUTCDate()),n=0;n<3&&null==e._a[n];++n)e._a[n]=p[n]=r[n];for(;n<7;n++)e._a[n]=p[n]=null==e._a[n]?2===n?1:0:e._a[n];24===e._a[_e]&&0===e._a[ye]&&0===e._a[be]&&0===e._a[we]&&(e._nextDay=!0,e._a[_e]=0),e._d=(e._useUTC?We:Fe).apply(null,p),s=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[_e]=24),e._w&&void 0!==e._w.d&&e._w.d!==s&&(f(e).weekdayMismatch=!0)}}function Ct(e){if(e._f===t.ISO_8601)bt(e);else if(e._f===t.RFC_2822)wt(e);else{e._a=[],f(e).empty=!0;for(var n,r,a,s,o,l=""+e._i,u=l.length,c=0,d=A(e._f,e._locale).match(x)||[],h=d.length,p=0;p<h;p++)r=d[p],(n=(l.match(se(r,e))||[])[0])&&(0<(a=l.substr(0,l.indexOf(n))).length&&f(e).unusedInput.push(a),l=l.slice(l.indexOf(n)+n.length),c+=n.length),L[r]?(n?f(e).empty=!1:f(e).unusedTokens.push(r),a=r,o=e,null!=(s=n)&&i(de,a)&&de[a](s,o._a,o,a)):e._strict&&!n&&f(e).unusedTokens.push(r);f(e).charsLeftOver=u-c,0<l.length&&f(e).unusedInput.push(l),e._a[_e]<=12&&!0===f(e).bigHour&&0<e._a[_e]&&(f(e).bigHour=void 0),f(e).parsedDateParts=e._a.slice(0),f(e).meridiem=e._meridiem,e._a[_e]=function(e,t,n){return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[_e],e._meridiem),null!==(u=f(e).era)&&(e._a[me]=e._locale.erasConvertYear(u,e._a[me])),St(e),dt(e)}}function Mt(e){var i,a,d,m=e._i,g=e._f;if(e._locale=e._locale||ct(e._l),null===m||void 0===g&&""===m)return p({nullInput:!0});if("string"==typeof m&&(e._i=m=e._locale.preparse(m)),b(m))return new y(dt(m));if(l(m))e._d=m;else if(n(g)){var v,w,k,S,C,M,D=e,R=!1,O=D._f.length;if(0===O)f(D).invalidFormat=!0,D._d=new Date(NaN);else{for(S=0;S<O;S++)C=0,M=!1,v=_({},D),null!=D._useUTC&&(v._useUTC=D._useUTC),v._f=D._f[S],Ct(v),h(v)&&(M=!0),C=(C+=f(v).charsLeftOver)+10*f(v).unusedTokens.length,f(v).score=C,R?C<k&&(k=C,w=v):(null==k||C<k||M)&&(k=C,w=v,M)&&(R=!0);c(D,w||v)}}else g?Ct(e):s(g=(m=e)._i)?m._d=new Date(t.now()):l(g)?m._d=new Date(g.valueOf()):"string"==typeof g?(a=m,null!==(i=vt.exec(a._i))?a._d=new Date(+i[1]):(bt(a),!1===a._isValid&&(delete a._isValid,wt(a),!1===a._isValid)&&(delete a._isValid,a._strict?a._isValid=!1:t.createFromInputFallback(a)))):n(g)?(m._a=u(g.slice(0),(function(e){return parseInt(e,10)})),St(m)):r(g)?(i=m)._d||(d=void 0===(a=F(i._i)).day?a.date:a.day,i._a=u([a.year,a.month,d,a.hour,a.minute,a.second,a.millisecond],(function(e){return e&&parseInt(e,10)})),St(i)):o(g)?m._d=new Date(g):t.createFromInputFallback(m);return h(e)||(e._d=null),e}function Dt(e,t,i,s,o){var l={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==i&&!1!==i||(s=i,i=void 0),(r(e)&&a(e)||n(e)&&0===e.length)&&(e=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=o,l._l=i,l._i=e,l._f=t,l._strict=s,(o=new y(dt(Mt(o=l))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Rt(e,t,n,r){return Dt(e,t,n,r,!1)}function Ot(e,t){var r,i;if(!(t=1===t.length&&n(t[0])?t[0]:t).length)return Rt();for(r=t[0],i=1;i<t.length;++i)t[i].isValid()&&!t[i][e](r)||(r=t[i]);return r}t.createFromInputFallback=k("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),t.ISO_8601=function(){},t.RFC_2822=function(){},G=k("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Rt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:p()})),q=k("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Rt.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:p()}));var Tt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function xt(e){var t=(e=F(e)).year||0,n=e.quarter||0,r=e.month||0,a=e.week||e.isoWeek||0,s=e.day||0,o=e.hour||0,l=e.minute||0,u=e.second||0,c=e.millisecond||0;this._isValid=function(e){var t,n,r=!1,a=Tt.length;for(t in e)if(i(e,t)&&(-1===Me.call(Tt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<a;++n)if(e[Tt[n]]){if(r)return!1;parseFloat(e[Tt[n]])!==ue(e[Tt[n]])&&(r=!0)}return!0}(e),this._milliseconds=+c+1e3*u+6e4*l+1e3*o*60*60,this._days=+s+7*a,this._months=+r+3*n+12*t,this._data={},this._locale=ct(),this._bubble()}function Yt(e){return e instanceof xt}function It(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Lt(e,t){U(e,0,0,(function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+T(~~(e/60),2)+t+T(~~e%60,2)}))}Lt("Z",":"),Lt("ZZ",""),ae("Z",te),ae("ZZ",te),fe(["Z","ZZ"],(function(e,t,n){n._useUTC=!0,n._tzm=Nt(te,e)}));var Ut=/([\+\-]|\d\d)/gi;function Nt(e,t){return null===(t=(t||"").match(e))?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Ut)||["-",0,0])[1]+ue(e[2]))?0:"+"===e[0]?t:-t}function At(e,n){var r;return n._isUTC?(n=n.clone(),r=(b(e)||l(e)?e:Rt(e)).valueOf()-n.valueOf(),n._d.setTime(n._d.valueOf()+r),t.updateOffset(n,!1),n):Rt(e).local()}function Pt(e){return-Math.round(e._d.getTimezoneOffset())}function Et(){return!!this.isValid()&&this._isUTC&&0===this._offset}t.updateOffset=function(){};var Ft=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Wt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function jt(e,t){var n,r=e;return Yt(e)?r={ms:e._milliseconds,d:e._days,M:e._months}:o(e)||!isNaN(+e)?(r={},t?r[t]=+e:r.milliseconds=+e):(t=Ft.exec(e))?(n="-"===t[1]?-1:1,r={y:0,d:ue(t[ve])*n,h:ue(t[_e])*n,m:ue(t[ye])*n,s:ue(t[be])*n,ms:ue(It(1e3*t[we]))*n}):(t=Wt.exec(e))?(n="-"===t[1]?-1:1,r={y:Ht(t[2],n),M:Ht(t[3],n),w:Ht(t[4],n),d:Ht(t[5],n),h:Ht(t[6],n),m:Ht(t[7],n),s:Ht(t[8],n)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(t=function(e,t){var n;return e.isValid()&&t.isValid()?(t=At(t,e),e.isBefore(t)?n=$t(e,t):((n=$t(t,e)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Rt(r.from),Rt(r.to)),(r={}).ms=t.milliseconds,r.M=t.months),n=new xt(r),Yt(e)&&i(e,"_locale")&&(n._locale=e._locale),Yt(e)&&i(e,"_isValid")&&(n._isValid=e._isValid),n}function Ht(e,t){return e=e&&parseFloat(e.replace(",",".")),(isNaN(e)?0:e)*t}function $t(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function zt(e,t){return function(n,r){var i;return null===r||isNaN(+r)||(C(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),i=n,n=r,r=i),Vt(this,jt(n,r),e),this}}function Vt(e,n,r,i){var a=n._milliseconds,s=It(n._days);n=It(n._months);e.isValid()&&(i=null==i||i,n&&Ae(e,Oe(e,"Month")+n*r),s&&Te(e,"Date",Oe(e,"Date")+s*r),a&&e._d.setTime(e._d.valueOf()+a*r),i)&&t.updateOffset(e,s||n)}function Bt(e){return"string"==typeof e||e instanceof String}function Gt(e,t){var n,r;return e.date()<t.date()?-Gt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(r=e.clone().add(n,"months"))<0?(t-r)/(r-e.clone().add(n-1,"months")):(t-r)/(e.clone().add(1+n,"months")-r)))||0}function qt(e){return void 0===e?this._locale._abbr:(null!=(e=ct(e))&&(this._locale=e),this)}function Zt(){return this._locale}jt.fn=xt.prototype,jt.invalid=function(){return jt(NaN)},Ye=zt(1,"add"),Be=zt(-1,"subtract"),t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",qe=k("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));var Jt=126227808e5;function Kt(e,t){return(e%t+t)%t}function Qt(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-Jt:new Date(e,t,n).valueOf()}function Xt(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-Jt:Date.UTC(e,t,n)}function en(e,t){return t.erasAbbrRegex(e)}function tn(){for(var e,t,n,r=[],i=[],a=[],s=[],o=this.eras(),l=0,u=o.length;l<u;++l)e=oe(o[l].name),t=oe(o[l].abbr),n=oe(o[l].narrow),i.push(e),r.push(t),a.push(n),s.push(e),s.push(t),s.push(n);this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+r.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+a.join("|")+")","i")}function nn(e,t){U(0,[e,e.length],0,t)}function rn(e,t,n,r,i){var a;return null==e?$e(this,r,i).year:(a=ze(e,r,i),function(e,t,n,r,i){return t=We((e=He(e,t,n,r,i)).year,0,e.dayOfYear),this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=a<t?a:t,n,r,i))}U("N",0,0,"eraAbbr"),U("NN",0,0,"eraAbbr"),U("NNN",0,0,"eraAbbr"),U("NNNN",0,0,"eraName"),U("NNNNN",0,0,"eraNarrow"),U("y",["y",1],"yo","eraYear"),U("y",["yy",2],0,"eraYear"),U("y",["yyy",3],0,"eraYear"),U("y",["yyyy",4],0,"eraYear"),ae("N",en),ae("NN",en),ae("NNN",en),ae("NNNN",(function(e,t){return t.erasNameRegex(e)})),ae("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),fe(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,n,r){(r=n._locale.erasParse(e,r,n._strict))?f(n).era=r:f(n).invalidEra=e})),ae("y",Q),ae("yy",Q),ae("yyy",Q),ae("yyyy",Q),ae("yo",(function(e,t){return t._eraYearOrdinalRegex||Q})),fe(["y","yy","yyy","yyyy"],me),fe(["yo"],(function(e,t,n,r){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[me]=n._locale.eraYearOrdinalParse(e,i):t[me]=parseInt(e,10)})),U(0,["gg",2],0,(function(){return this.weekYear()%100})),U(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),nn("gggg","weekYear"),nn("ggggg","weekYear"),nn("GGGG","isoWeekYear"),nn("GGGGG","isoWeekYear"),ae("G",X),ae("g",X),ae("GG",B,H),ae("gg",B,H),ae("GGGG",J,z),ae("gggg",J,z),ae("GGGGG",K,V),ae("ggggg",K,V),he(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,n,r){t[r.substr(0,2)]=ue(e)})),he(["gg","GG"],(function(e,n,r,i){n[i]=t.parseTwoDigitYear(e)})),U("Q",0,"Qo","quarter"),ae("Q",j),fe("Q",(function(e,t){t[ge]=3*(ue(e)-1)})),U("D",["DD",2],"Do","date"),ae("D",B,re),ae("DD",B,H),ae("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),fe(["D","DD"],ve),fe("Do",(function(e,t){t[ve]=ue(e.match(B)[0])})),J=Re("Date",!0),U("DDD",["DDDD",3],"DDDo","dayOfYear"),ae("DDD",Z),ae("DDDD",$),fe(["DDD","DDDD"],(function(e,t,n){n._dayOfYear=ue(e)})),U("m",["mm",2],0,"minute"),ae("m",B,ie),ae("mm",B,H),fe(["m","mm"],ye);var an;z=Re("Minutes",!1),U("s",["ss",2],0,"second"),ae("s",B,ie),ae("ss",B,H),fe(["s","ss"],be),K=Re("Seconds",!1);for(U("S",0,0,(function(){return~~(this.millisecond()/100)})),U(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),U(0,["SSS",3],0,"millisecond"),U(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),U(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),U(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),U(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),U(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),U(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),ae("S",Z,j),ae("SS",Z,H),ae("SSS",Z,$),an="SSSS";an.length<=9;an+="S")ae(an,Q);function sn(e,t){t[we]=ue(1e3*("0."+e))}for(an="S";an.length<=9;an+="S")fe(an,sn);function on(e){return e}function ln(e,t,n,r){var i=ct();r=d().set(r,t);return i[n](r,e)}function un(e,t,n){if(o(e)&&(t=e,e=void 0),e=e||"",null!=t)return ln(e,t,n,"month");for(var r=[],i=0;i<12;i++)r[i]=ln(e,i,n,"month");return r}function cn(e,t,n,r){"boolean"==typeof e?o(t)&&(n=t,t=void 0):(t=e,e=!1,o(n=t)&&(n=t,t=void 0)),t=t||"";var i,a=ct(),s=e?a._week.dow:0,l=[];if(null!=n)return ln(t,(n+s)%7,r,"day");for(i=0;i<7;i++)l[i]=ln(t,(i+s)%7,r,"day");return l}V=Re("Milliseconds",!1),U("z",0,0,"zoneAbbr"),U("zz",0,0,"zoneName"),(re=y.prototype).add=Ye,re.calendar=function(e,s){1===arguments.length&&(arguments[0]?function(e){return b(e)||l(e)||Bt(e)||o(e)||function(e){var t=n(e),r=!1;return t&&(r=0===e.filter((function(t){return!o(t)&&Bt(e)})).length),t&&r}(e)||function(e){var t,n=r(e)&&!a(e),s=!1,o=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],l=o.length;for(t=0;t<l;t+=1)s=s||i(e,o[t]);return n&&s}(e)||null==e}(arguments[0])?(e=arguments[0],s=void 0):function(e){for(var t=r(e)&&!a(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],o=0;o<s.length;o+=1)n=n||i(e,s[o]);return t&&n}(arguments[0])&&(s=arguments[0],e=void 0):s=e=void 0);var u=At(e=e||Rt(),this).startOf("day");u=t.calendarFormat(this,u)||"sameElse",s=s&&(M(s[u])?s[u].call(this,e):s[u]);return this.format(s||this.localeData().calendar(u,this,Rt(e)))},re.clone=function(){return new y(this)},re.diff=function(e,t,n){var r,i,a;if(!this.isValid())return NaN;if(!(r=At(e,this)).isValid())return NaN;switch(i=6e4*(r.utcOffset()-this.utcOffset()),t=E(t)){case"year":a=Gt(this,r)/12;break;case"month":a=Gt(this,r);break;case"quarter":a=Gt(this,r)/3;break;case"second":a=(this-r)/1e3;break;case"minute":a=(this-r)/6e4;break;case"hour":a=(this-r)/36e5;break;case"day":a=(this-r-i)/864e5;break;case"week":a=(this-r-i)/6048e5;break;default:a=this-r}return n?a:le(a)},re.endOf=function(e){var n,r;if(void 0!==(e=E(e))&&"millisecond"!==e&&this.isValid()){switch(r=this._isUTC?Xt:Qt,e){case"year":n=r(this.year()+1,0,1)-1;break;case"quarter":n=r(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":n=r(this.year(),this.month()+1,1)-1;break;case"week":n=r(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":n=r(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":n=r(this.year(),this.month(),this.date()+1)-1;break;case"hour":n=this._d.valueOf(),n+=36e5-Kt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":n=this._d.valueOf(),n+=6e4-Kt(n,6e4)-1;break;case"second":n=this._d.valueOf(),n+=1e3-Kt(n,1e3)-1}this._d.setTime(n),t.updateOffset(this,!0)}return this},re.format=function(e){return e=N(this,e=e||(this.isUtc()?t.defaultFormatUtc:t.defaultFormat)),this.localeData().postformat(e)},re.from=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Rt(e).isValid())?jt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},re.fromNow=function(e){return this.from(Rt(),e)},re.to=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Rt(e).isValid())?jt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},re.toNow=function(e){return this.to(Rt(),e)},re.get=function(e){return M(this[e=E(e)])?this[e]():this},re.invalidAt=function(){return f(this).overflow},re.isAfter=function(e,t){return e=b(e)?e:Rt(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=E(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},re.isBefore=function(e,t){return e=b(e)?e:Rt(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=E(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},re.isBetween=function(e,t,n,r){return e=b(e)?e:Rt(e),t=b(t)?t:Rt(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(r=r||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===r[1]?this.isBefore(t,n):!this.isAfter(t,n))},re.isSame=function(e,t){e=b(e)?e:Rt(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=E(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},re.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},re.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},re.isValid=function(){return h(this)},re.lang=qe,re.locale=qt,re.localeData=Zt,re.max=q,re.min=G,re.parsingFlags=function(){return c({},f(this))},re.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)i(e,t)&&n.push({unit:t,priority:W[t]});return n.sort((function(e,t){return e.priority-t.priority})),n}(e=F(e)),r=n.length,a=0;a<r;a++)this[n[a].unit](e[n[a].unit]);else if(M(this[e=E(e)]))return this[e](t);return this},re.startOf=function(e){var n,r;if(void 0!==(e=E(e))&&"millisecond"!==e&&this.isValid()){switch(r=this._isUTC?Xt:Qt,e){case"year":n=r(this.year(),0,1);break;case"quarter":n=r(this.year(),this.month()-this.month()%3,1);break;case"month":n=r(this.year(),this.month(),1);break;case"week":n=r(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":n=r(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":n=r(this.year(),this.month(),this.date());break;case"hour":n=this._d.valueOf(),n-=Kt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":n=this._d.valueOf(),n-=Kt(n,6e4);break;case"second":n=this._d.valueOf(),n-=Kt(n,1e3)}this._d.setTime(n),t.updateOffset(this,!0)}return this},re.subtract=Be,re.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},re.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},re.toDate=function(){return new Date(this.valueOf())},re.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?N(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):M(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",N(t,"Z")):N(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},re.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+e+'[")]')):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(re[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),re.toJSON=function(){return this.isValid()?this.toISOString():null},re.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},re.unix=function(){return Math.floor(this.valueOf()/1e3)},re.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},re.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},re.eraName=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},re.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},re.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},re.eraYear=function(){for(var e,n,r=this.localeData().eras(),i=0,a=r.length;i<a;++i)if(e=r[i].since<=r[i].until?1:-1,n=this.clone().startOf("day").valueOf(),r[i].since<=n&&n<=r[i].until||r[i].until<=n&&n<=r[i].since)return(this.year()-t(r[i].since).year())*e+r[i].offset;return this.year()},re.year=De,re.isLeapYear=function(){return pe(this.year())},re.weekYear=function(e){return rn.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},re.isoWeekYear=function(e){return rn.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},re.quarter=re.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},re.month=Pe,re.daysInMonth=function(){return xe(this.year(),this.month())},re.week=re.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},re.isoWeek=re.isoWeeks=function(e){var t=$e(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},re.weeksInYear=function(){var e=this.localeData()._week;return ze(this.year(),e.dow,e.doy)},re.weeksInWeekYear=function(){var e=this.localeData()._week;return ze(this.weekYear(),e.dow,e.doy)},re.isoWeeksInYear=function(){return ze(this.year(),1,4)},re.isoWeeksInISOWeekYear=function(){return ze(this.isoWeekYear(),1,4)},re.date=J,re.day=re.days=function(e){var t,n,r;return this.isValid()?(t=Oe(this,"Day"),null!=e?(n=e,r=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=r.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},re.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},re.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},re.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},re.hour=re.hours=ne,re.minute=re.minutes=z,re.second=re.seconds=K,re.millisecond=re.milliseconds=V,re.utcOffset=function(e,n,r){var i,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?a:Pt(this);if("string"==typeof e){if(null===(e=Nt(te,e)))return this}else Math.abs(e)<16&&!r&&(e*=60);return!this._isUTC&&n&&(i=Pt(this)),this._offset=e,this._isUTC=!0,null!=i&&this.add(i,"m"),a!==e&&(!n||this._changeInProgress?Vt(this,jt(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this},re.utc=function(e){return this.utcOffset(0,e)},re.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Pt(this),"m"),this},re.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Nt(ee,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},re.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Rt(e).utcOffset():0,(this.utcOffset()-e)%60==0)},re.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},re.isLocal=function(){return!!this.isValid()&&!this._isUTC},re.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},re.isUtc=Et,re.isUTC=Et,re.zoneAbbr=function(){return this._isUTC?"UTC":""},re.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},re.dates=k("dates accessor is deprecated. Use date instead.",J),re.months=k("months accessor is deprecated. Use month instead",Pe),re.years=k("years accessor is deprecated. Use year instead",De),re.zone=k("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()})),re.isDSTShifted=k("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){var e,t;return s(this._isDSTShifted)&&(_(e={},this),(e=Mt(e))._a?(t=(e._isUTC?d:Rt)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var r=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),a=0,s=0;s<r;s++)ue(e[s])!==ue(t[s])&&a++;return a+i}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted})),(ie=R.prototype).calendar=function(e,t,n){return M(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},ie.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(x).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},ie.invalidDate=function(){return this._invalidDate},ie.ordinal=function(e){return this._ordinal.replace("%d",e)},ie.preparse=on,ie.postformat=on,ie.relativeTime=function(e,t,n,r){var i=this._relativeTime[n];return M(i)?i(e,t,n,r):i.replace(/%d/i,e)},ie.pastFuture=function(e,t){return M(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},ie.set=function(e){var t,n;for(n in e)i(e,n)&&(M(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},ie.eras=function(e,n){for(var r,i=this._eras||ct("en")._eras,a=0,s=i.length;a<s;++a){if("string"==typeof i[a].since)r=t(i[a].since).startOf("day"),i[a].since=r.valueOf();switch(typeof i[a].until){case"undefined":i[a].until=1/0;break;case"string":r=t(i[a].until).startOf("day").valueOf(),i[a].until=r.valueOf()}}return i},ie.erasParse=function(e,t,n){var r,i,a,s,o,l=this.eras();for(e=e.toUpperCase(),r=0,i=l.length;r<i;++r)if(a=l[r].name.toUpperCase(),s=l[r].abbr.toUpperCase(),o=l[r].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(s===e)return l[r];break;case"NNNN":if(a===e)return l[r];break;case"NNNNN":if(o===e)return l[r]}else if(0<=[a,s,o].indexOf(e))return l[r]},ie.erasConvertYear=function(e,n){var r=e.since<=e.until?1:-1;return void 0===n?t(e.since).year():t(e.since).year()+(n-e.offset)*r},ie.erasAbbrRegex=function(e){return i(this,"_erasAbbrRegex")||tn.call(this),e?this._erasAbbrRegex:this._erasRegex},ie.erasNameRegex=function(e){return i(this,"_erasNameRegex")||tn.call(this),e?this._erasNameRegex:this._erasRegex},ie.erasNarrowRegex=function(e){return i(this,"_erasNarrowRegex")||tn.call(this),e?this._erasNarrowRegex:this._erasRegex},ie.months=function(e,t){return e?(n(this._months)?this._months:this._months[(this._months.isFormat||Le).test(t)?"format":"standalone"])[e.month()]:n(this._months)?this._months:this._months.standalone},ie.monthsShort=function(e,t){return e?(n(this._monthsShort)?this._monthsShort:this._monthsShort[Le.test(t)?"format":"standalone"])[e.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},ie.monthsParse=function(e,t,n){var r,i;if(this._monthsParseExact)return function(e,t,n){var r,i,a;e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)a=d([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(a,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=Me.call(this._shortMonthsParse,e))?i:null:-1!==(i=Me.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=Me.call(this._shortMonthsParse,e))||-1!==(i=Me.call(this._longMonthsParse,e))?i:null:-1!==(i=Me.call(this._longMonthsParse,e))||-1!==(i=Me.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(i=d([2e3,r]),n&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[r]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[r]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[r].test(e))return r;if(n&&"MMM"===t&&this._shortMonthsParse[r].test(e))return r;if(!n&&this._monthsParse[r].test(e))return r}},ie.monthsRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||Ee.call(this),e?this._monthsStrictRegex:this._monthsRegex):(i(this,"_monthsRegex")||(this._monthsRegex=Ne),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},ie.monthsShortRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||Ee.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(i(this,"_monthsShortRegex")||(this._monthsShortRegex=Ue),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},ie.week=function(e){return $e(e,this._week.dow,this._week.doy).week},ie.firstDayOfYear=function(){return this._week.doy},ie.firstDayOfWeek=function(){return this._week.dow},ie.weekdays=function(e,t){return t=n(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?Ve(t,this._week.dow):e?t[e.day()]:t},ie.weekdaysMin=function(e){return!0===e?Ve(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},ie.weekdaysShort=function(e){return!0===e?Ve(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},ie.weekdaysParse=function(e,t,n){var r,i;if(this._weekdaysParseExact)return function(e,t,n){var r,i,a;e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)a=d([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(a,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=Me.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=Me.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=Me.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=Me.call(this._weekdaysParse,e))||-1!==(i=Me.call(this._shortWeekdaysParse,e))||-1!==(i=Me.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=Me.call(this._shortWeekdaysParse,e))||-1!==(i=Me.call(this._weekdaysParse,e))||-1!==(i=Me.call(this._minWeekdaysParse,e))?i:null:-1!==(i=Me.call(this._minWeekdaysParse,e))||-1!==(i=Me.call(this._weekdaysParse,e))||-1!==(i=Me.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(i=d([2e3,1]).day(r),n&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[r]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[r]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[r].test(e))return r;if(n&&"ddd"===t&&this._shortWeekdaysParse[r].test(e))return r;if(n&&"dd"===t&&this._minWeekdaysParse[r].test(e))return r;if(!n&&this._weekdaysParse[r].test(e))return r}},ie.weekdaysRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Qe.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(i(this,"_weekdaysRegex")||(this._weekdaysRegex=Ze),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},ie.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Qe.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(i(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Je),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},ie.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Qe.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(i(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Ke),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},ie.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},ie.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},lt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===ue(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),t.lang=k("moment.lang is deprecated. Use moment.locale instead.",lt),t.langData=k("moment.langData is deprecated. Use moment.localeData instead.",ct);var dn=Math.abs;function fn(e,t,n,r){return t=jt(t,n),e._milliseconds+=r*t._milliseconds,e._days+=r*t._days,e._months+=r*t._months,e._bubble()}function hn(e){return e<0?Math.floor(e):Math.ceil(e)}function pn(e){return 4800*e/146097}function mn(e){return 146097*e/4800}function gn(e){return function(){return this.as(e)}}function vn(e){return function(){return this.isValid()?this._data[e]:NaN}}j=gn("ms"),H=gn("s"),Z=gn("m"),$=gn("h"),Ye=gn("d"),q=gn("w"),G=gn("M"),Be=gn("Q"),ne=gn("y"),z=j;K=vn("milliseconds"),V=vn("seconds"),J=vn("minutes"),De=vn("hours"),ie=vn("days");var _n=vn("months"),yn=vn("years"),bn=Math.round,wn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var kn=Math.abs;function Sn(e){return(0<e)-(e<0)||+e}function Cn(){var e,t,n,r,i,a,s,o,l,u,c;return this.isValid()?(e=kn(this._milliseconds)/1e3,t=kn(this._days),n=kn(this._months),(o=this.asSeconds())?(r=le(e/60),i=le(r/60),e%=60,r%=60,a=le(n/12),n%=12,s=e?e.toFixed(3).replace(/\.?0+$/,""):"",l=Sn(this._months)!==Sn(o)?"-":"",u=Sn(this._days)!==Sn(o)?"-":"",c=Sn(this._milliseconds)!==Sn(o)?"-":"",(o<0?"-":"")+"P"+(a?l+a+"Y":"")+(n?l+n+"M":"")+(t?u+t+"D":"")+(i||r||e?"T":"")+(i?c+i+"H":"")+(r?c+r+"M":"")+(e?c+s+"S":"")):"P0D"):this.localeData().invalidDate()}var Mn=xt.prototype;return Mn.isValid=function(){return this._isValid},Mn.abs=function(){var e=this._data;return this._milliseconds=dn(this._milliseconds),this._days=dn(this._days),this._months=dn(this._months),e.milliseconds=dn(e.milliseconds),e.seconds=dn(e.seconds),e.minutes=dn(e.minutes),e.hours=dn(e.hours),e.months=dn(e.months),e.years=dn(e.years),this},Mn.add=function(e,t){return fn(this,e,t,1)},Mn.subtract=function(e,t){return fn(this,e,t,-1)},Mn.as=function(e){if(!this.isValid())return NaN;var t,n,r=this._milliseconds;if("month"===(e=E(e))||"quarter"===e||"year"===e)switch(t=this._days+r/864e5,n=this._months+pn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(mn(this._months)),e){case"week":return t/7+r/6048e5;case"day":return t+r/864e5;case"hour":return 24*t+r/36e5;case"minute":return 1440*t+r/6e4;case"second":return 86400*t+r/1e3;case"millisecond":return Math.floor(864e5*t)+r;default:throw new Error("Unknown unit "+e)}},Mn.asMilliseconds=j,Mn.asSeconds=H,Mn.asMinutes=Z,Mn.asHours=$,Mn.asDays=Ye,Mn.asWeeks=q,Mn.asMonths=G,Mn.asQuarters=Be,Mn.asYears=ne,Mn.valueOf=z,Mn._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,r=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*hn(mn(n)+t),n=t=0),r.milliseconds=e%1e3,e=le(e/1e3),r.seconds=e%60,e=le(e/60),r.minutes=e%60,e=le(e/60),r.hours=e%24,t+=le(e/24),n+=e=le(pn(t)),t-=hn(mn(e)),e=le(n/12),n%=12,r.days=t,r.months=n,r.years=e,this},Mn.clone=function(){return jt(this)},Mn.get=function(e){return e=E(e),this.isValid()?this[e+"s"]():NaN},Mn.milliseconds=K,Mn.seconds=V,Mn.minutes=J,Mn.hours=De,Mn.days=ie,Mn.weeks=function(){return le(this.days()/7)},Mn.months=_n,Mn.years=yn,Mn.humanize=function(e,t){var n,r;return this.isValid()?(n=!1,r=wn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(r=Object.assign({},wn,t),null!=t.s)&&null==t.ss&&(r.ss=t.s-1),t=function(e,t,n,r){var i=jt(e).abs(),a=bn(i.as("s")),s=bn(i.as("m")),o=bn(i.as("h")),l=bn(i.as("d")),u=bn(i.as("M")),c=bn(i.as("w"));return i=bn(i.as("y")),a=(a<=n.ss?["s",a]:a<n.s&&["ss",a])||(s<=1?["m"]:s<n.m&&["mm",s])||(o<=1?["h"]:o<n.h&&["hh",o])||(l<=1?["d"]:l<n.d&&["dd",l]),(a=(a=null!=n.w?a||(c<=1?["w"]:c<n.w&&["ww",c]):a)||(u<=1?["M"]:u<n.M&&["MM",u])||(i<=1?["y"]:["yy",i]))[2]=t,a[3]=0<+e,a[4]=r,function(e,t,n,r,i){return i.relativeTime(t||1,!!n,e,r)}.apply(null,a)}(this,!n,r,e=this.localeData()),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},Mn.toISOString=Cn,Mn.toString=Cn,Mn.toJSON=Cn,Mn.locale=qt,Mn.localeData=Zt,Mn.toIsoString=k("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Cn),Mn.lang=qe,U("X",0,0,"unix"),U("x",0,0,"valueOf"),ae("x",X),ae("X",/[+-]?\d+(\.\d{1,3})?/),fe("X",(function(e,t,n){n._d=new Date(1e3*parseFloat(e))})),fe("x",(function(e,t,n){n._d=new Date(ue(e))})),t.version="2.30.1",e=Rt,t.fn=re,t.min=function(){return Ot("isBefore",[].slice.call(arguments,0))},t.max=function(){return Ot("isAfter",[].slice.call(arguments,0))},t.now=function(){return Date.now?Date.now():+new Date},t.utc=d,t.unix=function(e){return Rt(1e3*e)},t.months=function(e,t){return un(e,t,"months")},t.isDate=l,t.locale=lt,t.invalid=p,t.duration=jt,t.isMoment=b,t.weekdays=function(e,t,n){return cn(e,t,n,"weekdays")},t.parseZone=function(){return Rt.apply(null,arguments).parseZone()},t.localeData=ct,t.isDuration=Yt,t.monthsShort=function(e,t){return un(e,t,"monthsShort")},t.weekdaysMin=function(e,t,n){return cn(e,t,n,"weekdaysMin")},t.defineLocale=ut,t.updateLocale=function(e,t){var n,r;return null!=t?(r=rt,null!=it[e]&&null!=it[e].parentLocale?it[e].set(D(it[e]._config,t)):(t=D(r=null!=(n=ot(e))?n._config:r,t),null==n&&(t.abbr=e),(r=new R(t)).parentLocale=it[e],it[e]=r),lt(e)):null!=it[e]&&(null!=it[e].parentLocale?(it[e]=it[e].parentLocale,e===lt()&&lt(e)):null!=it[e]&&delete it[e]),it[e]},t.locales=function(){return O(it)},t.weekdaysShort=function(e,t,n){return cn(e,t,n,"weekdaysShort")},t.normalizeUnits=E,t.relativeTimeRounding=function(e){return void 0===e?bn:"function"==typeof e&&(bn=e,!0)},t.relativeTimeThreshold=function(e,t){return void 0!==wn[e]&&(void 0===t?wn[e]:(wn[e]=t,"s"===e&&(wn.ss=t-1),!0))},t.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},t.prototype=re,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t})),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minium 10 numbers required",max:"Maxium 15 numbers required",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-batch-insights",{template:'\n        <yuno-page-grid\n            :authorizedRoles="authorizedRoles"\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="false"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <div class="mainHeader">\n                        <div class="block">\n                            <h1 class="pageTitle">{{ pageHeader.title }}</h1>\n                            <div class="action" v-if="isElementVisible(userRole.data, pageHeader.button.role)">\n                                <template v-if="pageHeader.button.type === \'custom\'">\n                                    <b-button \n                                        @click="pageCTA()"\n                                        class="yunoSecondaryCTA wired">\n                                        {{pageHeader.button.label}}\n                                    </b-button>\n                                </template>\n                                <template v-else>\n                                    <b-button tag="a"\n                                        :href="pageHeader.button.url"\n                                        class="yunoSecondaryCTA wired">\n                                        {{pageHeader.button.label}}\n                                    </b-button>\n                                </template>\n                            </div> \n                        </div>\n                        <div class="filtersWrapper" v-if="!enabledFilters">\n                            <b-skeleton width="100%" height="40px"></b-skeleton>\n                        </div>\n                        <yuno-filters v-if="enabledFilters" :data="filters"></yuno-filters>\n                    </div>\n                    <div class="filtersWrapper" v-if="!enabledFilters">\n                        <ul>\n                            <li>\n                                <b-skeleton width="100" height="40px"></b-skeleton>\n                            </li>\n                            <li>\n                                <b-skeleton width="100" height="40px"></b-skeleton>\n                            </li>\n                        </ul>\n                    </div>\n                    <yuno-tabs v-if="enabledTabs" :data="nestedTabs" :options="{ \'date\':  dateRange, \'payload\': payload}"></yuno-tabs>\n                </div>\n                <b-modal \n                    :active.sync="classSize.modal" \n                    :width="500" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="classSizeModalClose"\n                    class="yunoModal lightTheme">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Choose Class Size</h2>\n                        </div>\n                        <validation-observer tag="div" ref="classSizeObserver" v-slot="{ handleSubmit }">\n                            <form id="selectCourseForm" @submit.prevent="handleSubmit(initClassSize)">\n                                <div class="modalBody">\n                                    <div class="radioList groupElement">\n                                        <small class="helper">Please choose one option</small>\n                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                            <template v-for="(item, i) in classSize.items">\n                                                <b-field class="colorGrey">\n                                                    <b-radio v-model="classSize.selected"\n                                                        name="classSize"\n                                                        :native-value="item.slug">\n                                                        {{item.label}}\n                                                    </b-radio>\n                                                </b-field>\n                                            </template>\n                                            <p class="error">{{errors[0]}}</p>\n                                        </validation-provider>\n                                    </div>\n                                    <div class="ctaWrapper alignLeft">\n                                        <b-button\n                                            native-type="submit"\n                                            class="yunoSecondaryCTA">\n                                            Continue\n                                        </b-button>\n                                    </div>        \n                                </div>\n                            </form>\n                        </validation-observer>\n                </b-modal>\n                <b-modal \n                    :active.sync="drawerModal.modal" \n                    :width="500" \n                    animation="slide-out"\n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="closeDrawer"\n                    class="yunoModal drawerModal">\n                        <template v-if="drawer.loading">\n                            <div class="loaderWrapper">\n                                <div class="smallLoader"></div>\n                            </div>\n                        </template>\n                        <template v-if="drawer.success && drawerModal.modal">\n                            <template v-if="drawer.error">\n                                <h2 class="drawerTitle">Active Learners</h2>\n                                <yuno-empty-states :options="{\'state\': \'dataNotFound\', \'description\': drawer.errorData}"></yuno-empty-states>\n                            </template>\n                            <template v-else>\n                                <div class="scrollable">\n                                    <h2 class="drawerTitle">Active Learners ({{ drawer.data.length }})</h2>\n                                    <div class="learners">\n                                        <template v-for="(user, u) in drawer.data">\n                                            <figure :key="u" class="userImg">\n                                                <img :src="user.image_url" :alt="user.full_name">\n                                                <figcaption>\n                                                    <h3 class="primaryTitle">{{ user.full_name }}</h3>\n                                                </figcaption>\n                                            </figure>\n                                        </template>\n                                    </div>\n                                </div>\n                            </template>\n                        </template>\n                </b-modal>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({isMiniSidebar:!1,enabledFilters:!1,enabledTabs:!1,classSize:{modal:!1,selected:"",items:[{slug:"one_to_one",label:"1-to-1"},{slug:"one_to_many",label:"Group (10 max.)"}]},drawerModal:{modal:!1,data:[]},pageHeader:{title:"Batches",button:{label:"Create Batch",type:"custom",url:"/create-batch",role:["yuno-admin","org-admin"]}},hideHeader:!1,isScroll:!1,authorizedRoles:["yuno-admin","Counselor","org-admin"],filters:[{type:"search",slug:"course_search",items:[],is_multiple:!1,is_highlighted:!1,is_active:!0,is_disabled:!1,is_focus:!1,selected:null,current:"",label:"Course",placeholder:"Search Course"},{type:"search",slug:"search_instructor",items:[],is_multiple:!1,is_highlighted:!1,is_active:!1,is_disabled:!0,is_focus:!1,selected:null,current:"",label:"Instructor",placeholder:"Search Instructor"},{type:"groupMultiSelect",slug:"class_days_time",is_multiple:!0,items:[{slug:"class_days",label:"Class Days",items:[{label:"Sun",filter:"class_days_time",slug:"sun",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Mon",filter:"class_days_time",slug:"mon",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Tue",filter:"class_days_time",slug:"tue",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Wed",filter:"class_days_time",slug:"wed",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Thu",filter:"class_days_time",slug:"thu",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Fri",filter:"class_days_time",slug:"fri",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Sat",filter:"class_days_time",slug:"sat",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!1}]},{slug:"class_time",label:"Class Time",items:[{label:"Morning",filter:"class_days_time",slug:"morning",parentSlug:"class_time",interval:"6 - 12",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Afternoon",filter:"class_days_time",slug:"afternoon",parentSlug:"class_time",interval:"12 - 4",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!1},{label:"Evening",filter:"class_days_time",slug:"evening",parentSlug:"class_time",interval:"4 - 8",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Night",filter:"class_days_time",slug:"night",parentSlug:"class_time",interval:"8 - 11",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0}]}],is_highlighted:!1,is_active:!0,is_disabled:!0,is_focus:!1,selected:[],current:[],label:"",placeholder:"Class Days & Time"},{type:"dropdown",slug:"personalisation",is_multiple:!1,items:[{label:"All",slug:"all"},{label:"1-to-1",slug:"one_to_one"},{label:"Group",slug:"one_to_many"}],is_highlighted:!1,is_active:!0,is_disabled:!0,is_focus:!1,selected:"",current:"",label:"Personalisation",placeholder:"Personalisation"},{type:"singleCheck",slug:"showEnrollable",items:[],is_multiple:!1,is_highlighted:!1,is_active:!0,is_disabled:!0,is_focus:!1,selected:!0,current:"",label:"",placeholder:"Show only enrollable",tooltip:{message:"A batch is enrollable only if: There is at least one vacancy. The batch is unlocked and there are enough days left to take a new enrolment (i.e. before the batch end day approaches)",multilined:!0}},{type:"singleCheck",slug:"showLocked",items:[],is_multiple:!1,is_highlighted:!1,is_active:!0,is_disabled:!0,is_focus:!1,selected:!1,current:"",label:"",placeholder:"Show only locked",tooltip:{message:"New enrollments cannot be made in a locked batch",multilined:!1}},{type:"dropdown",slug:"hideColumns",items:[],is_multiple:!0,is_highlighted:!1,is_active:!1,is_disabled:!1,is_focus:!1,selected:"",current:"",label:"Hide Columns",placeholder:"Hide Columns"}],nestedTabs:{activeTab:0,items:[{label:"Upcoming Batches",slug:"ongoing",isActive:!0,data:[],hasData:!1,visible:!0},{label:"Past",slug:"past",isActive:!1,data:[],hasData:!1,visible:!0}]},dateRange:[],payload:{batch_days:["sun","mon","tue","wed","thu","fri","sat"],batch_time:["morning","afternoon","evening","night"],enrollment_request_date:"",course_id:"",enrollable_status:"enrollable",instructor_id:0,locked_batch:"all",personalisation:"all",role_id:Number(isLoggedIn),view:"grid-view",org_id:"",user_id:""}}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","allCourses","instructorList","filterResult","drawer","loader"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data)}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){let e="";return e=!this.user.isLoggedin||this.userInfo.success,e}}},async created(){this.whichPage(),this.emitEvents(),window.addEventListener("resize",this.manageOnResize)},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.manageOnResize()},methods:{isElementVisible:(e,t)=>YUNOCommon.findInArray(t,e),setPayload(){YUNOCommon.getQueryParameter("courseid");"org-admin"===this.userInfo.data.role&&(this.payload.org_id=this.activeOrg(),this.payload.user_id=isLoggedIn)},onLogin(e){},onUserInfo(e){YUNOCommon.findInArray(this.authorizedRoles,e.role)&&this.fetchModules()},onMini(e){this.isMiniSidebar=e},onMenuLoaded(){},classSizeModalClose(){this.$refs.classSizeObserver.reset(),this.classSize.selected=""},initClassSize(){let e=this.pageHeader.button.url+"/?classSize="+this.classSize.selected;window.location.href=YUNOCommon.config.host()+e},pageCTA(){this.classSize.modal=!0},manageOnResize(){let e=window.outerWidth;(e>1920||e<=1920&&e>1366||e<=1366&&e>1280||e<=1280&&e>1024)&&(this.filterResult.limit=20)},stickyObserver(e,t){new IntersectionObserver((function(t){0===t[0].intersectionRatio?document.querySelector("#"+e).classList.add("isStickyEle"):1===t[0].intersectionRatio&&document.querySelector("#"+e).classList.remove("isStickyEle")}),{threshold:[0,1]}).observe(document.querySelector("#"+t))},initScroll(){void 0!==window?this.onScroll(window,!0):setTimeout((()=>{this.onScroll(window,!0)}),200)},onScroll(e,t){const n=this;let r="";e.addEventListener("scroll",(function(e){let i="";if(i=t?window.pageYOffset||document.documentElement.scrollTop:e.target.scrollTop,-1===Math.sign(i))return n.isScroll=!1,n.hideHeader=!1,!1;i>=r?n.isScroll||(n.isScroll=!0,n.hideHeader=!0):!n.isTabClicked&&n.isScroll&&(n.isScroll=!1,n.hideHeader=!1),r=i<=0?0:i}))},managePastColumns(e){for(let t=0;t<e.length;t++){const n=e[t];n.isActive=!0,"batch_instructor"===n.field?n.type="instructor":"class_days"===n.field?n.type="days":n.type=""}},manageColumns(e){for(let t=0;t<e.length;t++){const n=e[t];n.isActive=!0,"enrollments"===n.field?n.type="highlight":"batch_instructor"===n.field?n.type="instructor":"class_days"===n.field?n.type="days":"batch_id"===n.field?n.type="hasDrawer":"batch_url"===n.field?n.type="link_copyToClipboard":n.type=""}e.push({field:"action",label:"Action",sortable:!1,type:"action",isActive:!0})},manageRows(e){for(let t=0;t<e.length;t++){const n=e[t];let r="";n.action=[{label:"Edit Batch",isActive:this.isFilterAvailable(["yuno-admin","org-admin"]),slug:"editBatch",activeClass:"material-icons-outlined",url:"/create-batch/?classSize="+n.batch_personalisation+"&isEdit="+n.batch_id,linkTarget:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"edit"}},{label:"New enrollment",isActive:this.isFilterAvailable(["yuno-admin","Counselor","org-admin"],n.batch_locked),slug:"newEnrollment",activeClass:"material-icons-outlined",url:"/generate-link?courseID="+this.payload.course_id+"-"+n.batch_id,linkTarget:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"person_add_alt_1"}},{label:"End batch",isActive:this.isFilterAvailable(["yuno-admin"]),slug:"endBatch",activeClass:"material-icons-outlined",url:!1,linkTarget:"",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"unpublished"}}],"Not Enrollable"!==n.enrollments?(r=n.enrollments.split(" "),n.hightlight=Number(r[0])>=Number(r[2])?"isRed":"isGreen"):n.hightlight="isRed",void 0!==n.batch_url&&""!==n.batch_url&&(n.batch_url={label:"Click to copy",slug:"batch_url",hasData:!1,activeClass:"material-icons-outlined",url:n.batch_url,linkTarget:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"file_copy"}})}},isFilterAvailable(e,t){return!!YUNOCommon.findInArray(e,this.userRole.data)&&(void 0===t||!t)},gotPastBatches(e){if(this.filterResult.loading=!1,Event.$emit("dataLoaded"),void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data;this.filterResult.count=t.count,this.managePastColumns(t.data.columns),this.filterResult.data=t.data,this.manageHideColumns(this.filterResult.data),this.getStorage(!1)}},filterResultReset(e){this.filterResult.success=!1,this.filterResult.error=null,this.filterResult.data=[],e&&(this.filterResult.offset=0),this.filterResult.additional=[]},fetchPastBatches(e,t){e||this.filterResultReset(t);const n=this,r={apiURL:this.getPastBatchesURL(),module:"gotData",store:"filterResult",moduleLoading:!e,addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPastBatches(e)}};this.$store.dispatch("fetchData",r)},getPastBatchesURL(){const e=this.userInfo.data.role;this.payload.id;return{"org-admin":YUNOCommon.config.org("batchesPast",this.payload.course_id,this.payload.personalisation,isLoggedIn,this.activeOrg(),!1,!1,!1,!1,!1,!1,"grid-view",this.filterResult.limit,this.filterResult.offset),"yuno-admin":YUNOCommon.config.pastBatchesList(this.payload.course_id,this.payload.personalisation,isLoggedIn,"grid-view",this.filterResult.limit,this.filterResult.offset),Counselor:YUNOCommon.config.pastBatchesList(this.payload.course_id,this.payload.personalisation,isLoggedIn,"grid-view",this.filterResult.limit,this.filterResult.offset)}[e]||null},manageHideColumns(e){const t=YUNOCommon.findObjectByKey(this.filters,"slug","hideColumns"),n=e.columns;t.items=[];for(let e=0;e<n.length;e++){const r=n[e];if("enrollments"===r.field){const e={label:"Enrollments",slug:r.field,isActive:!0};t.items.push(e)}else{const e={label:r.label,slug:r.field,isActive:!0};t.items.push(e)}}t.is_active=!0},gotUpcomingOngoingBatches(e,t){if(this.filterResult.loading=!1,Event.$emit("dataLoaded"),void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data;this.filterResult.additional={batches_vacancies:t.batches_vacancies},this.filterResult.count=t.count,this.manageColumns(t.data.columns),this.manageRows(t.data.rows),this.filterResult.data=t.data,this.manageHideColumns(this.filterResult.data),this.getStorage(!1)}},fetchUpcomingOngoingBatches(e,t){e||(this.filterResult.loading=!0,this.filterResultReset(t));"org-admin"===this.userInfo.data.role&&(this.payload.limit=this.filterResult.limit,this.payload.offset=this.filterResult.offset);const n=this;let r={apiURL:this.getUpcomingOngoingBatchesURL(),module:"gotData",store:"filterResult",addToModule:!1,payload:this.payload,headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:function(e){return n.gotUpcomingOngoingBatches(e)}};this.$store.dispatch("postData",r)},getUpcomingOngoingBatchesURL(){const e=this.userInfo.data.role;this.payload.id;return{"org-admin":YUNOCommon.config.org("batchesUpcomingOngoing"),"yuno-admin":YUNOCommon.config.availableBatches(this.payload.course_id,this.filterResult.limit,this.filterResult.offset),Counselor:YUNOCommon.config.availableBatches(this.payload.course_id,this.filterResult.limit,this.filterResult.offset)}[e]||null},gotInstructorList(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchInstructorList(){const e=this,t={apiURL:YUNOCommon.config.instructorListAPI(isLoggedIn),module:"gotData",store:"instructorList",callback:!0,callbackFunc:function(t){return e.gotInstructorList(t)}};this.$store.dispatch("fetchData",t)},gotCourses(e){const{response:t}=e;if(200===t?.data?.code){const{data:e}=t.data,n=YUNOCommon.getQueryParameter("courseid");if(n){const t=YUNOCommon.findObjectByKey(this.filters,"slug","course_search"),r=YUNOCommon.findObjectByKey(e,"course_id",Number(n));t.current=r.title,t.selected=r,this.manageCourseSearch(t)}}},fetchCourses(){const e={apiURL:this.getCoursesURL(),module:"gotData",store:"allCourses",callback:!0,callbackFunc:e=>this.gotCourses(e)};this.$store.dispatch("fetchData",e)},getCoursesURL(){const e=this.userInfo.data.role;return{"org-admin":YUNOCommon.config.org("courses",this.activeOrg(),isLoggedIn,0,"all",!1,!1,!1,!1,"all","all","list-view",100,0),"yuno-admin":YUNOCommon.config.coursesList(),Counselor:YUNOCommon.config.coursesList()}[e]||null},addDays(e,t){const n=new Date(Number(e));return n.setDate(e.getDate()+t),n},manageDateRange(){let e=this.addDays(new Date,0),t=this.addDays(e,10),n=new moment(e),r=new moment(t);for(n=new moment(e),dates=[],startTimer=new Date;n<r;)this.dateRange.push({label:n.format("ddd MMM DD"),slug:n.format("YYYY-MM-DD")}),n.add(1,"days");endTimer=new Date,this.payload.enrollment_request_date=this.dateRange[1].slug},manageFilterVisibility(){YUNOCommon.findObjectByKey(this.filters,"slug","search_instructor").is_active=this.isElementVisible(this.userRole.data,["yuno-admin"])},fetchModules(){this.manageFilterVisibility(),this.setPayload(),this.manageDateRange(),this.fetchCourses(),this.fetchInstructorList(),setTimeout((()=>{this.getStorage(!0)}),500)},manageFilters(e){if(this.filterResultReset(),0===e)for(let e=0;e<this.filters.length;e++){const t=this.filters[e];t.is_active=!0,"hideColumns"===t.slug&&(t.current="",t.selected="",t.items=[])}else if(1===e){for(let e=0;e<this.filters.length;e++){const t=this.filters[e];t.is_active=!1,"course_search"!==t.slug&&("search_instructor"===t.slug?(t.current="",t.selected=null):"class_days_time"===t.slug?(t.current=[],t.selected=[]):"personalisation"===t.slug?(t.current="",t.selected=""):"showEnrollable"===t.slug?(t.current="",t.selected=!0):"showLocked"===t.slug?(t.current="",t.selected=!1):"hideColumns"===t.slug&&(t.current="",t.selected="",t.items=[]))}YUNOCommon.findObjectByKey(this.filters,"slug","course_search").is_active=!0,YUNOCommon.findObjectByKey(this.filters,"slug","personalisation").is_active=!0,this.resetPayload()}this.setStorage(!0),""!==this.payload.course_id&&this.manageState(!1,!0)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},getStorage(e){sessionStorage.removeItem("batchesStateV6");const t=sessionStorage.getItem("batchesStateV7"),n=this.userRole.data;if(null!==t){const r=JSON.parse(t);e&&(this.nestedTabs.activeTab=r.activeTab,this.filters=r.filters,this.filterResult.currentPage=r.results.currentPage,this.filterResult.offset=r.results.offset,"org-admin"===n&&(r.payload.org_id=sessionStorage.getItem("activeOrg")),this.payload=r.payload,this.enabledFilters=!0,this.enabledTabs=!0,this.manageState(!1)),void 0!==r.columns&&(this.filterResult.data.columns=r.columns)}else this.enabledFilters=!0,this.enabledTabs=!0},whichPage(){if(window.location.href.indexOf("active")>-1){this.nestedTabs.activeTab=0;for(let e=0;e<this.filters.length;e++){const t=this.filters[e];t.is_active=!0,"hideColumns"===t.slug&&(t.current="",t.selected="",t.items=[])}}else if(window.location.href.indexOf("past")>-1){this.nestedTabs.activeTab=1;for(let e=0;e<this.filters.length;e++){const t=this.filters[e];t.is_active=!1,"course_search"!==t.slug&&("search_instructor"===t.slug?(t.current="",t.selected=null):"class_days_time"===t.slug?(t.current=[],t.selected=[]):"personalisation"===t.slug?(t.current="",t.selected=""):"showEnrollable"===t.slug?(t.current="",t.selected=!0):"showLocked"===t.slug?(t.current="",t.selected=!1):"hideColumns"===t.slug&&(t.current="",t.selected="",t.items=[]))}YUNOCommon.findObjectByKey(this.filters,"slug","course_search").is_active=!0,YUNOCommon.findObjectByKey(this.filters,"slug","personalisation").is_active=!0}},setStorage(e){const t={activeTab:this.nestedTabs.activeTab,filters:this.filters,results:this.filterResult,payload:this.payload,columns:this.filterResult.data.columns};e&&delete t.columns,sessionStorage.setItem("batchesStateV7",JSON.stringify(t))},manageState(e,t){switch(this.nestedTabs.activeTab){case 0:this.fetchUpcomingOngoingBatches(e,t);break;case 1:this.fetchPastBatches(e,t)}setTimeout((()=>{this.setStorage(!1)}),200)},resetPayload(){const e=this.payload,t=this.dateRange[1].slug;for(let n in e)e.hasOwnProperty(n)&&"course_id"!==n&&(e[n]="batch_days"===n?["sun","mon","tue","wed","thu","fri","sat"]:"batch_time"===n?["morning","afternoon","evening","night"]:"enrollable_status"===n?"enrollable":"enrollment_request_date"===n?t:"locked_batch"===n||"personalisation"===n?"all":"role_id"===n?Number(isLoggedIn):"view"===n?"grid-view":"")},rearrangeFilters(e){for(let t=0;t<this.filters.length;t++){const n=this.filters[t];"course_search"!==n.slug&&(null!==e.selected?n.is_disabled=!1:(n.is_disabled=!0,"search_instructor"===n.slug?(n.current="",n.selected=null):"class_days_time"===n.slug?(n.current=[],n.selected=[]):"personalisation"===n.slug?(n.current="",n.selected=""):"showEnrollable"===n.slug?(n.current="",n.selected=!0):"showLocked"===n.slug&&(n.current="",n.selected=!1)))}},manageCourseSearch(e){null!==e.selected?(this.payload.course_id=e.selected.course_id,this.manageState(!1),this.rearrangeFilters(e),this.filterResultReset()):(this.payload.course_id="",this.resetPayload(),this.rearrangeFilters(e),this.filterResultReset(),this.setStorage(!1))},manageInstructorSearch(e){null!==e.selected?this.payload.instructor_id=e.selected.id:this.payload.instructor_id=0,this.manageState(!1)},manageEnrollable(e,t){this.payload.enrollable_status=e?"enrollable":"not_enrollable",this.manageState(!1)},manageLocked(e,t){this.payload.locked_batch=e?"locked":"unlocked",this.manageState(!1)},managePersonalisation(e){""!==e.selected?this.payload.personalisation=e.slug:this.payload.personalisation="all",this.manageState(!1)},manageClassDaysTime(e,t){},clearClassDaysTime(e,t){YUNOCommon.removeObjInArr(e.current,"slug",t.slug),YUNOCommon.removeValInArr(e.selected,t.slug),"class_days"===t.parentSlug?YUNOCommon.removeValInArr(this.payload.batch_days,t.slug):YUNOCommon.removeValInArr(this.payload.batch_time,t.slug),0===this.payload.batch_days.length&&(this.payload.batch_days=["sun","mon","tue","wed","thu","fri","sat"]),0===this.payload.batch_time.length&&(this.payload.batch_time=["morning","afternoon","evening","night"]),this.manageState(!1)},closeDrawer(){this.drawerModal.modal=!1,this.drawerModal.data=[]},gotLearners(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchLearners(e){this.drawer.data=[],this.drawer.error=null,this.drawer.success=!1;const t=this,n={apiURL:YUNOCommon.config.batch("learners",{batchID:e}),module:"gotData",store:"drawer",callback:!0,callbackFunc:function(e){return t.gotLearners(e)}};this.$store.dispatch("fetchData",n)},showHideColumns(e,t){const n=YUNOCommon.findObjectByKey(this.filterResult.data.columns,"field",e.slug);null!==n&&(n.isActive=!n.isActive,e.isActive=!e.isActive,this.setStorage(!1))},endBatchDone(e,t,n){if(this.manageLoader(!1),void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:t.message,position:"is-bottom"})}else{const t=e.response.data.message;this.$buefy.toast.open({duration:5e3,message:t,position:"is-bottom",type:"is-danger"})}},initEndBatch(e,t){this.manageLoader(!0);const n=this,r={apiURL:YUNOCommon.config.endBatch(e.batch_id),module:"gotData",store:"updateLink",callback:!0,callbackFunc:function(r){return n.endBatchDone(r,e,t)}};this.$store.dispatch("putData",r)},manageLoader(e){this.loader.isActive=e,this.loader.overlay=e},manageQueryParams(){const e=new URL(window.location.href);["courseid"].forEach((t=>{e.searchParams.has(t)&&e.searchParams.delete(t)})),window.history.pushState({path:e.href},"",e.href)},emitEvents(){Event.$on("refreshTable",(()=>{this.manageState(!1)})),Event.$on("initAction",((e,t)=>{if("endBatch"===t.slug){let n="End Batch";this.$buefy.dialog.confirm({title:n,message:"Are you sure you want to end this batch",cancelText:"Cancel",confirmText:"Yes",type:"is-danger",onConfirm:()=>this.initEndBatch(e,t)})}})),Event.$on("openDrawer",(e=>{this.drawerModal.modal=!0,this.fetchLearners(e.batch_id)})),Event.$on("onFilterClear",((e,t)=>{switch(e.slug){case"course_search":e.current="",e.selected=null,this.manageCourseSearch(e);break;case"search_instructor":e.current="",e.selected=null,this.manageInstructorSearch(e);break;case"class_days_time":this.clearClassDaysTime(e,t);break;case"personalisation":e.current="",e.selected="",this.managePersonalisation(e)}this.manageQueryParams()})),Event.$on("tabChange",(e=>{this.manageFilters(e)})),Event.$on("onDateChange",(e=>{this.payload.enrollment_request_date=e.slug,this.manageState(!1)})),Event.$on("onApplyFilter",(e=>{this.filterResult.currentPage=1,this.filterResult.offset=0;const t=["morning","afternoon","evening","night"],n=["sun","mon","tue","wed","thu","fri","sat"];if(0!==e.current.length){this.payload.batch_days=null!==YUNOCommon.findObjectByKey(e.current,"parentSlug","class_days")?[]:n,this.payload.batch_time=null!==YUNOCommon.findObjectByKey(e.current,"parentSlug","class_time")?[]:t;for(let t=0;t<e.current.length;t++){const n=e.current[t];"class_time"===n.parentSlug&&this.payload.batch_time.push(n.slug),"class_days"===n.parentSlug&&this.payload.batch_days.push(n.slug)}}else this.payload.batch_days=n,this.payload.batch_time=t;this.manageState(!1)})),Event.$on("onFilterItemSelect",((e,t)=>{switch(this.filterResult.currentPage=1,this.filterResult.offset=0,t.slug){case"personalisation":this.managePersonalisation(e);break;case"class_days_time":this.manageClassDaysTime(e,t);break;case"hideColumns":this.showHideColumns(e,t)}})),Event.$on("onTablePageChange",((e,t)=>{this.filterResult.offset=Math.floor(this.filterResult.limit*e-this.filterResult.limit),this.manageState(t)})),Event.$on("onFilterChange",((e,t)=>{switch(this.filterResult.currentPage=1,this.filterResult.offset=0,t.slug){case"showEnrollable":this.manageEnrollable(e,t);break;case"showLocked":this.manageLocked(e,t)}})),Event.$on("onSearchItemSelect",(e=>{switch(this.filterResult.currentPage=1,this.filterResult.offset=0,e.slug){case"course_search":this.manageCourseSearch(e);break;case"search_instructor":this.manageInstructorSearch(e)}})),Event.$on("checkLoggedInState",(e=>{e&&Event.$on("gotUserRole",((e,t)=>{Event.$on("gotUserMenu",(()=>{YUNOCommon.findInArray(this.authorizedRoles,e)&&this.fetchModules()}))}))}))},fetchFooter(){const e={apiURL:YUNOCommon.config.footerAPI(),module:"gotData",store:"footer",callback:!1};this.$store.dispatch("fetchData",e)}}}),Vue.component("yuno-date",{props:["data","options"],template:'\n        <section class="dateOptions">\n            <b-field>\n                <template v-for="(date, i) in data">\n                    <b-radio-button \n                        v-model="options.enrollment_request_date"\n                        @input="onDateChange(date)"\n                        :key="i"\n                        :native-value="date.slug">\n                        <b-tooltip label="Enrollments start date"\n                            type="is-dark"\n                            position="is-right">\n                            <span>{{ date.label }}</span>    \n                        </b-tooltip>\n                    </b-radio-button>\n                </template>\n            </b-field>\n        </section>\n    ',data:()=>({radioButton:""}),computed:{},async created(){},mounted(){},methods:{onDateChange(e){Event.$emit("onDateChange",e)}}}),Vue.component("yuno-empty-state-v2",{props:["data","options"],template:'\n        <section class="emptyStateV2" :class="[options.type]">\n            <span v-if="options.type === \'noInput\'" class="arrowUp"></span>\n            <figure>\n                <img width="80" height="59" :src="options.image !== undefined ? wpThemeURL + options.image : wpThemeURL + \'/assets/images/noBatch.png\'" alt="Yuno Learning">\n                <figcaption>\n                    {{ options.message }}\n                </figcaption>\n                <b-button v-if="options.type === \'course\'" @click="noResult()" class="yunoSecondaryCTA">Clear Filters</b-button>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{noResult(){Event.$emit("defaultFilters")}}}),Vue.component("yuno-filters",{props:["data","options"],template:'\n        <section class="filtersWrapper">\n            <ul>\n                <template v-for="(filter, i) in data">\n                    <template v-if="filter.type === \'search\' && filter.slug === \'course_search\'">\n                        <li \n                            :key="i"\n                            v-if="filter.is_active"\n                            class="filter"\n                            :class="[filter.slug, filter.type, filter.is_focus ? \'isFocus\' : \'\']">\n                            <template v-if="allCourses.loading">\n                                <b-skeleton height="40px"></b-skeleton>\n                            </template>\n                            <template v-if="allCourses.success">\n                                <b-field>\n                                    <b-autocomplete\n                                        v-model="filter.current"\n                                        :data="filteredAutocomplete(filter, \'title\', allCourses)"\n                                        :placeholder="filter.placeholder"\n                                        autocomplete="courseSearch"\n                                        :disabled="filter.is_disabled"\n                                        field="title"\n                                        open-on-focus\n                                        @select="onSearchItemSelect($event, filter)"\n                                        @focus="searchFocusToggle(filter, \'focus\')"\n                                        @blur="searchFocusToggle(filter, \'blur\')"\n                                        :clearable="true">\n                                        <template slot="empty">No results for {{filter.current}}</template>\n                                        <template slot-scope="props">\n                                            {{props.option.title}}\n                                            <div class="subtitle">\n                                                {{userRole.data === "org-admin" ? props.option.duration_weeks + \' weeks\' : props.option.duration_weeks}}\n                                            </div>\n                                        </template>\n                                    </b-autocomplete>  \n                                </b-field>\n                            </template>\n                        </li>\n                    </template>\n                    <template v-if="filter.type === \'search\' && filter.slug === \'search_instructor\'">\n                        <li \n                            :key="i"\n                            v-if="filter.is_active"\n                            class="filter"\n                            :class="[filter.slug, filter.type, filter.is_focus ? \'isFocus\' : \'\']">\n                            <template v-if="instructorList.loading">\n                                <b-skeleton height="40px"></b-skeleton>\n                            </template>\n                            <template v-if="instructorList.success">\n                                <b-field>\n                                    <b-autocomplete\n                                        v-model="filter.current"\n                                        :data="filteredAutocomplete(filter, \'name_email\', instructorList)"\n                                        :placeholder="filter.placeholder"\n                                        autocomplete="instructorSearch"\n                                        :disabled="filter.is_disabled"\n                                        field="name_email"\n                                        open-on-focus\n                                        @select="onSearchItemSelect($event, filter)"\n                                        @focus="searchFocusToggle(filter, \'focus\')"\n                                        @blur="searchFocusToggle(filter, \'blur\')"\n                                        :clearable="true">\n                                        <template slot="empty">No results for {{filter.current}}</template>\n                                        <template slot-scope="props">\n                                            {{props.option.name_email}}\n                                        </template>\n                                    </b-autocomplete>  \n                                </b-field>\n                            </template>\n                        </li>\n                    </template>\n                    <template v-if="filter.type === \'dropdown\'">\n                        <li \n                            :key="i"\n                            v-if="filter.is_active"\n                            :class="[filter.slug, filter.type]"\n                            class="filter">\n                            <b-dropdown\n                                :key="i"\n                                v-model="filter.selected"\n                                :multiple="filter.is_multiple"\n                                aria-role="list"\n                                :disabled="filter.is_disabled"\n                                @change="onFilterChange($event, filter)"\n                                class="filterMenu"\n                                :class="[filter.is_highlighted ? \'active\' : \'\']">\n                                <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">\n                                    <template v-if="filter.slug === \'hideColumns\'">\n                                    <div class="material-icons-outlined">\n                                    settings\n                                    </div>\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="filter.selected === \'\'">\n                                            <span>{{filter.placeholder}}</span>\n                                        </template>\n                                        <template v-else>\n                                            <span>{{filter.selected.label}}</span>\n                                        </template>\n                                    </template>\n                                    <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                </button>\n                                <template v-if="filter.slug === \'hideColumns\'">\n                                    <p class="listCaption">Hide Columns</p>\n                                    <template v-for="(item, j) in filter.items">\n                                        <b-dropdown-item \n                                            @click="onFilterItemSelect(item, filter)"\n                                            :value="item.slug"\n                                            :key="item.slug + \'-\' + j"\n                                            aria-role="listitem">\n                                            <span class="itemLabel">{{item.label}}</span>\n                                        </b-dropdown-item>\n                                    </template>\n                                </template>\n                                <template v-else>\n                                    <template v-for="(item, j) in filter.items">\n                                        <b-dropdown-item \n                                            @click="onFilterItemSelect(item, filter)"\n                                            :value="item"\n                                            :key="item.slug + \'-\' + j"\n                                            aria-role="listitem">\n                                            <span class="itemLabel">{{item.label}}</span>\n                                        </b-dropdown-item>\n                                    </template>\n                                </template>\n                            </b-dropdown>\n                        </li>\n                    </template>\n                    <template v-if="filter.type === \'groupMultiSelect\'">\n                        <li \n                            :key="i"\n                            v-if="filter.is_active"\n                            class="filter">\n                            <b-dropdown\n                                :key="i"\n                                v-model="filter.selected"\n                                ref="groupMultiSelect"\n                                :multiple="filter.is_multiple"\n                                aria-role="list"\n                                :disabled="filter.is_disabled"\n                                @change="onFilterChange($event, filter)"\n                                class="filterMenu"\n                                :class="[filter.is_highlighted ? \'active\' : \'\']">\n                                <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">\n                                    <span>{{filter.placeholder}}</span>\n                                    <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                </button>\n                                <template v-for="(item, j) in filter.items">\n                                    <div class="filterSet" :class="[item.slug]">\n                                        <p :key="item.slug + \'-\' + j" class="listCaption">\n                                            {{item.label}}\n                                        </p>\n                                        <div class="wrapper">\n                                            <template v-for="(subItem, l) in item.items">\n                                                <b-dropdown-item \n                                                    @click="onFilterItemSelect(subItem, filter)"\n                                                    :class="[subItem.slug]"\n                                                    :value="subItem.slug"\n                                                    :key="item.slug + \'-sub-\' + l"\n                                                    aria-role="listitem">\n                                                    <span class="itemLabel">{{subItem.label}}</span>\n                                                </b-dropdown-item>\n                                            </template>\n                                        </div>\n                                    </div>\n                                </template>\n                                <div class="ctaWrapper">\n                                    <b-button class="noBorder" @click="clearMe(filter)">Clear All</b-button>\n                                    <b-button class="yunoSecondaryCTA" @click="applyFilter(filter)">Apply</b-button>\n                                </div>\n                            </b-dropdown>\n                        </li>\n                    </template>\n                    <template v-if="filter.type === \'singleCheck\'">\n                        <li \n                            :key="i"\n                            v-if="filter.is_active"\n                            :class="[filter.slug, filter.type]"\n                            class="filter">\n                            <div class="field">\n                                <b-checkbox\n                                    :native-value="filter.selected"\n                                    @input="onFilterChange($event, filter)"\n                                    :disabled="filter.is_disabled"\n                                    v-model="filter.selected">\n                                    <b-tooltip \n                                        type="is-dark"\n                                        :label="filter.tooltip.message"\n                                        :multilined="filter.tooltip.multilined"\n                                        position="is-bottom">\n                                        {{ filter.placeholder }}\n                                    </b-tooltip>\n                                </b-checkbox>\n                            </div>\n                        </li>\n                    </template>\n                </template>\n            </ul>\n            <div class="clearFilters">\n                <template v-for="(item, i) in data">\n                    <template v-if="item.slug !== \'class_days_time\' && item.slug !== \'showEnrollable\' && item.slug !== \'showLocked\' && item.slug !== \'personalisation\'">\n                        <template v-if="item.current !== \'\'">\n                            <b-tag\n                                :key="\'active-\' + i"\n                                attached\n                                closable\n                                rounded\n                                @close="onFilterClear(item, false)"\n                                aria-close-label="Close tag">\n                                <span><strong>{{ item.label + \': \' }}</strong>{{ item.current }}</span>\n                            </b-tag>\n                        </template>\n                    </template>\n                    <template v-if="item.current.length !== 0 && item.slug === \'class_days_time\'">\n                        <template v-for="(dayTime, l) in item.current">\n                            <b-tag\n                                :key="\'dayTime-\' + l"\n                                attached\n                                closable\n                                rounded\n                                aria-close-label="Close tag"\n                                @close="onFilterClear(item, dayTime)">\n                                <span>\n                                    <strong>\n                                        <template v-if="dayTime.parentSlug === \'class_days\'">\n                                            {{ \'Class Days\' + \': \' }}\n                                        </template>\n                                        <template v-else>\n                                            {{ \'Class Time\' + \': \' }}\n                                        </template>\n                                    </strong> {{ dayTime.label }}\n                                </span>\n                            </b-tag>\n                        </template>\n                    </template>\n                    <template v-if="item.selected !== \'\' && item.slug === \'personalisation\'">\n                        <b-tag\n                            :key="\'active-\' + i"\n                            attached\n                            closable\n                            rounded\n                            @close="onFilterClear(item, false)"\n                            aria-close-label="Close tag">\n                            <span><strong>{{ item.label + \': \' }}</strong>{{ item.selected.label }}</span>\n                        </b-tag>\n                    </template>\n                </template>\n            </div>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","user","allCourses","instructorList"])},async created(){},mounted(){},methods:{onFilterClear(e,t){Event.$emit("onFilterClear",e,t)},clearMe(e){e.selected=[],e.current=[]},applyFilter(e){this.$refs.groupMultiSelect[0].isActive=!1,Event.$emit("onApplyFilter",e)},searchFocusToggle(e,t){"focus"===t?(e.is_focus=!0,""===e.current&&null!==e.selected&&(e.selected=null,Event.$emit("onSearchItemSelect",e))):e.is_focus=!1},manageQueryParams(){const e=new URL(window.location.href);["courseid"].forEach((t=>{e.searchParams.has(t)&&e.searchParams.delete(t)})),window.history.pushState({path:e.href},"",e.href)},onSearchItemSelect(e,t){null!==e?(t.selected=e,Event.$emit("onSearchItemSelect",t)):(t.selected=null,this.manageQueryParams(),Event.$emit("onSearchItemSelect",t))},filteredAutocomplete(e,t,n){if(n.error)return[];{e.items=n.data;const r=e.current.toLowerCase();return e.items.filter((e=>e[t].toString().toLowerCase().includes(r)))}},onFilterChange(e,t){Event.$emit("onFilterChange",e,t)},onFilterItemSelect(e,t){if("class_days_time"===e.filter){YUNOCommon.findInArray(t.selected,e.slug)?t.current.push(e):YUNOCommon.removeObjInArr(t.current,"slug",e.slug)}Event.$emit("onFilterItemSelect",e,t)}}}),Vue.component("yuno-table",{props:["data","options"],template:'\n        <section class="yunoTable">\n            <template v-if="filterResult.loading">\n                <b-skeleton height="500px"></b-skeleton>\n            </template>\n            <template v-if="filterResult.success">\n                <template v-if="filterResult.error">\n                    <yuno-empty-state-v2 :options="{\'type\': \'noDataFound\', \'message\': filterResult.errorData}"></yuno-empty-state-v2>\n                </template>    \n                <template v-else>\n                    <b-table\n                        :data="filterResult.data.rows"\n                        :loading="tableLoading"\n                        :paginated="true"\n                        :backend-pagination="true"\n                        :total="filterResult.count"\n                        :per-page="filterResult.limit"\n                        :current-page="filterResult.currentPage"\n                        :striped="true"\n                        @page-change="onPageChange($event)"\n                        ref="table">\n                        <template slot-scope="props">\n                            <b-table-column\n                                v-for="(column, i) in filterResult.data.columns" \n                                :key="i" \n                                :field="column.field" \n                                :label="column.label"\n                                :visible="column.isActive"\n                                :sortable="column.sortable">\n                                <template v-if="column.type === \'highlight\'">\n                                    <div \n                                        class="grid"\n                                        :class="[column.field]">\n                                        <span class="material-icons item isEnrollable" :class="[props.row.is_enrollable ? \'isActive\' : \'\']">\n                                            <template v-if="props.row.is_enrollable">\n                                                check_circle\n                                            </template>\n                                            <template v-else>\n                                                check_circle_outline\n                                            </template>\n                                        </span>\n                                        <span class="item iconWithLabel">\n                                            <span class="material-icons">\n                                                <template v-if="props.row.batch_time === \'Morning\'">\n                                                    wb_twilight\n                                                </template>\n                                                <template v-else-if="props.row.batch_time === \'Afternoon\'">\n                                                    wb_sunny\n                                                </template>\n                                                <template v-else-if="props.row.batch_time === \'Evening\'">\n                                                    wb_twilight\n                                                </template>\n                                                <template v-else-if="props.row.batch_time === \'Night\'">\n                                                    dark_mode\n                                                </template>\n                                            </span>\n                                            <span class="itemLabel">\n                                                {{ props.row.batch_time }}\n                                            </span>\n                                        </span>\n                                        <span class="material-icons-outlined item">\n                                            <template v-if="props.row.batch_locked">\n                                                lock\n                                            </template>\n                                            <template v-else>\n                                                lock_open\n                                            </template>\n                                        </span>\n                                        <span :class="[props.row.hightlight]" class="hightlight item">\n                                            {{ props.row[column.field] }}\n                                        </span>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.type === \'days\'">\n                                    <ul :class="[column.field]" class="classDays">\n                                        <li \n                                            v-for="(day, d) in props.row.class_days" \n                                            :key="d"\n                                            :class="[day.is_available ? \'\' : \'disabled\']">\n                                            {{day.label}}\n                                        </li>\n                                    </ul>\n                                </template>\n                                <template v-else-if="column.type === \'instructor\'">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto">\n                                        <img :src="props.row.instructor.image" :alt="props.row.instructor.name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row.instructor.name }}\n                                        </figcaption>\n                                    </figure>    \n                                </template>\n                                <template v-else-if="column.type === \'link_copyToClipboard\'">\n                                    <template v-if="props.row[column.field].hasData !== undefined">\n                                        <ul class="actions copyToClipboard" :class="[column.field]">\n                                            <li>\n                                                <a :href="props.row[column.field].url" :target="props.row[column.field].linkTarget">\n                                                    <span class="itemLabel">{{ props.row[column.field].slug }}</span>\n                                                    <span class="itemIcon material-icons">\n                                                        open_in_new\n                                                    </span>\n                                                </a>\n                                            </li>\n                                            <li>\n                                                <b-tooltip :label="props.row[column.field].label"\n                                                    type="is-dark"\n                                                    position="is-left">\n                                                    <div class="wrapper" @click="copyToClipboard(props.row, generateID(props.row, props.row[column.field].slug))">\n                                                        <span class="itemLabel">\n                                                            <b-input :id="generateID(props.row, props.row[column.field].slug)" :value="props.row[column.field].url" readonly></b-input>\n                                                        </span>\n                                                        <span class="itemIcon" :class="[props.row[column.field].activeClass]" @mouseover="manageMouse(props.row[column.field], \'over\')" @mouseout="manageMouse(props.row[column.field], \'out\')">\n                                                            {{ props.row[column.field].icon.font }}\n                                                        </span>\n                                                    </div>\n                                                </b-tooltip>\n                                            </li>\n                                        </ul> \n                                    </template>\n                                    <template v-else>\n                                        <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                                    </template>\n                                </template>\n                                <template v-else-if="column.type === \'hasDrawer\'">\n                                    <div :class="[column.field]"><a href="#" @click.prevent="openDrawer(props.row)">{{ props.row[column.field] }}</a></div>    \n                                </template>\n                                <template v-else-if="column.type === \'action\'">\n                                    <ul class="actions" :class="[column.field]">\n                                        <li \n                                            v-for="(item, k) in props.row[column.field]"\n                                            v-if="item.isActive"\n                                            :key="\'action-\' + k">\n                                            <b-tooltip :label="item.label"\n                                                type="is-dark"\n                                                position="is-left">\n                                                <a :href="item.url !== false ? item.url : \'#\'" :target="item.linkTarget" @click="initAction(props.row, item, $event)">\n                                                    <span class="itemLabel">{{ item.label }}</span>\n                                                    <span class="itemIcon" :class="[item.activeClass]" @mouseover="manageMouse(item, \'over\')" @mouseout="manageMouse(item, \'out\')">\n                                                        {{ item.icon.font }}\n                                                    </span>\n                                                </a>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul>    \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                                </template>\n                            </b-table-column>\n                        </template>\n                    </b-table>    \n                </template>    \n            </template>\n        </section>\n    ',data:()=>({tableLoading:!1}),computed:{...Vuex.mapState(["filterResult"])},async created(){this.emitEvents()},destroyed(){},mounted(){},methods:{generateID(e,t){if("batch_url"===t)return t+"-"+e.batch_id},copyToClipboard(e,t){let n=document.getElementById(t);n.select(),n.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})},manageMouse(e,t){e.activeClass="over"===t?e.icon.hover:e.icon.class},initAction(e,t,n){!1===t.url&&n.preventDefault(),Event.$emit("initAction",e,t)},emitEvents(){Event.$on("dataLoaded",(()=>{this.tableLoading=!1}))},openDrawer(e){Event.$emit("openDrawer",e)},onPageChange(e,t){this.filterResult.currentPage=e,this.tableLoading=!0,Event.$emit("onTablePageChange",e,this.tableLoading)}}}),Vue.component("yuno-tabs",{props:["data","options"],template:'\n        <b-tabs class="yunoTabsV2" v-model="data.activeTab" @input="tabChange" :animated="false">\n            <b-tab-item \n                v-for="(tab, i) in data.items"\n                :key="i"\n                :label="tab.label">\n                <template v-if="tab.slug === \'ongoing\'">\n                    <yuno-date v-if="data.activeTab === 0 && options.payload.course_id !== \'\'" :data="options.date" :options="options.payload"></yuno-date>\n                    <yuno-empty-state-v2 \n                        v-if="options.payload.course_id === \'\'" \n                        :options="{\n                        \'type\': \'noInput\',\n                        \'image\': \'/assets/images/choose.png\', \n                        \'message\': \'Choose a course above to see its batches\'}"\n                    >\n                    </yuno-empty-state-v2>\n                    <p class="note" \n                        v-if="filterResult.success && filterResult.additional.length !== 0">\n                        {{ filterResult.additional.batches_vacancies }}\n                        <a \n                            class="refresh" \n                            @click.prevent="refreshTable()"\n                            href="#">\n                            <b-tooltip label="Refresh"\n                                type="is-dark"\n                                position="is-right">\n                                <span class="material-icons">refresh</span>\n                            </b-tooltip>\n                        </a>\n                    </p>\n                    <yuno-table v-if="options.payload.course_id !== \'\'"></yuno-table>\n                </template>\n                <template v-if="tab.slug === \'past\'">\n                    <yuno-empty-state-v2 v-if="options.payload.course_id === \'\'" :options="{\'type\': \'noInput\', \'message\': \'Choose a course above to see its batches\'}"></yuno-empty-state-v2>\n                    <yuno-table v-if="options.payload.course_id !== \'\'"></yuno-table>\n                </template>\n            </b-tab-item>\n        </b-tabs>\n    ',data:()=>({}),computed:{...Vuex.mapState(["filterResult"])},async created(){},mounted(){},methods:{refreshTable(){Event.$emit("refreshTable")},tabChange(e){Event.$emit("tabChange",e)}}});