Vue.component("yuno-courses", {
  props: ["data", "options", "hasLearnMore", "seeAvailability"],
  template: `
        <section class="courses">
            <template v-if="course.loading" >
                <div class="course" v-for="i in 3" :key="i">
                    <div class="greyBG">
                        <div class="courseHeader" style="margin-bottom: 15px">
                            <h2 class="largerTitle"><b-skeleton width="200px" active></b-skeleton></h2>
                            <div class="price">
                                <b-skeleton width="80px" active></b-skeleton>
                            </div>
                        </div>
                        <ul class="features">
                            <li>
                                <b-skeleton size="is-large" width="80px" :active="true"></b-skeleton> 
                            </li>
                            <li>
                                <b-skeleton size="is-large" width="80px" :active="true"></b-skeleton> 
                            </li>
                            <li>
                                <b-skeleton size="is-large" width="80px" :active="true"></b-skeleton> 
                            </li>
                        </ul>
                        <ul class="studentsInfo">
                            <li>
                                <b-skeleton size="is-large" width="80px" :active="true"></b-skeleton> 
                            </li>
                            <li>
                                <b-skeleton size="is-large" width="80px" :active="true"></b-skeleton> 
                            </li>
                        </ul>
                    </div>
                    <div class="noBg hasFlex">
                            <div class="col-12 col-md-4 pl-0">
                                <div 
                                    class="description pb-2"
                                >
                                    <b-skeleton width="100%" height="20px" :active="true" :count="2"></b-skeleton>
                                </div>
                            </div>
                            <div class="col-12 col-md-4 pl-0">
                                <b-skeleton height="40px" width="100%"></b-skeleton>
                            </div>
                        </div>
                </div>
            </template>
            <template v-if="course.success">
                <template v-if="course.error">
                    <yuno-empty-state-v2 
                        :options="{'message': course.errorData, 'type': 'course'}"
                        @defaultFilters="defaultFilters"
                    >
                    </yuno-empty-state-v2>
                </template>    
                <template v-else>
                    <p class="count">{{ course.data.course_count + " live courses and " + course.data.instructors_count + " instructors to choose from"}}</p>
                    <template v-for="(course, i) in courses">
                        <yuno-course
                            :key="i + '-course-' + course.id"
                            :data="course"
                            :hasLearnMore="hasLearnMore"
                            :seeAvailability="seeAvailability"
                        >
                        </yuno-course>
                    </template>
                    <yuno-paging :total-pages="totalPages()" :total="course.count" :per-page="course.limit" :current-page="currentPage" @pagechanged="onPageChange"></yuno-paging>
                </template>    
            </template>
        </section>
    `,
  data() {
    return {
      currentPage: 1,
    };
  },
  computed: {
    ...Vuex.mapState(["user", "course"]),
    courses() {
      return this.course.data.courses;
    },
  },
  async created() {},
  mounted() {},
  methods: {
    defaultFilters() {
      this.$emit("defaultFilters");
    },
    init(page) {
      this.$emit("initCourses", page);
    },
    totalPages() {
      let count = this.course.count / this.course.limit;
      return Math.ceil(count);
    },
    onPageChange(page) {
      this.currentPage = page;
      this.init(page);
    },
  },
});
