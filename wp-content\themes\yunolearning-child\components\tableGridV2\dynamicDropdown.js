Vue.component('yuno-dynamic-dropdown', {
    props: {
        data: {
            type: Object,
            required: true
        },
        defaultFilters: {
            type: Object,
            required: true
        },
        payload: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoDropdown" :class="[data.filter]">
            <template v-if="data.loading">
                <b-skeleton width="150px" height="43px"></b-skeleton>
            </template>
            <template v-if="data.success">
                <b-dropdown 
                    v-model="selectedOption" 
                    aria-role="list"
                    :mobile-modal="false"
                    @change="onChange($event, data)"
                >
                    <template #trigger>
                        <div class="labelWrapper">
                            <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>
                            <span class="selectedItem">{{ showSelected(data.selected, data) }}</span>
                            <a 
                                href="#" 
                                class="clearFilter" 
                                v-if="manageClearFilterCTA(data)"
                                @click="clearFilter($event, data.selected, data)"
                            >
                                <span class="material-icons">cancel</span>
                            </a>
                            <span class="material-icons icon">expand_more</span>
                        </div>
                    </template>
                    <template v-for="(option, i) in data.items">
                        <b-dropdown-item 
                            :value="option" 
                            aria-role="listitem"
                        >
                            {{ option.label }}   
                        </b-dropdown-item>
                    </template>
                </b-dropdown>
            </template>
        </div>
    `,
    data() {
        return {
            selectedOption: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageClearFilterCTA(data) {
            if (this.payload[data.filter] === this.$props.defaultFilters[data.filter]) {
                return false
            } else {
                return true
            }
        },
        clearFilter(e, selected, data) {
            e.preventDefault();
            this.selectedOption = ""
            this.$emit("clearFilter", selected, data)
        },
        showSelected(e, data) {
            let placeHolder = "";

            if (this.payload[data.filter] === this.$props.defaultFilters[data.filter]) {
                placeHolder = data.placeholder
            } else {
                placeHolder = YUNOCommon.findObjectByKey(data.items, "slug", this.payload[data.filter]).label;
            }

            return placeHolder;
        },
        onChange(e, data) {
            const payload = this.$props.payload; 
            payload[e.filter] = e.slug;
            data.selected = e;
            this.$emit("onDropdownChange", e, data)
        }
    }
});