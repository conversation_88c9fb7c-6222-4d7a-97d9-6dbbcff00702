/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,o){for(var a=-1,r=null==e?0:e.length;++a<r;){var s=e[a];t(o,s,n(s),e)}return o}function n(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}function r(e,t){for(var n=-1,o=null==e?0:e.length,a=0,r=[];++n<o;){var s=e[n];t(s,n,e)&&(r[a++]=s)}return r}function s(e,t){return!(null==e||!e.length)&&h(e,t,0)>-1}function i(e,t,n){for(var o=-1,a=null==e?0:e.length;++o<a;)if(n(t,e[o]))return!0;return!1}function l(e,t){for(var n=-1,o=null==e?0:e.length,a=Array(o);++n<o;)a[n]=t(e[n],n,e);return a}function u(e,t){for(var n=-1,o=t.length,a=e.length;++n<o;)e[a+n]=t[n];return e}function c(e,t,n,o){var a=-1,r=null==e?0:e.length;for(o&&r&&(n=e[++a]);++a<r;)n=t(n,e[a],a,e);return n}function d(e,t,n,o){var a=null==e?0:e.length;for(o&&a&&(n=e[--a]);a--;)n=t(n,e[a],a,e);return n}function p(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}function m(e){return e.match(Qe)||[]}function f(e,t,n){var o;return n(e,(function(e,n,a){if(t(e,n,a))return o=n,!1})),o}function g(e,t,n,o){for(var a=e.length,r=n+(o?1:-1);o?r--:++r<a;)if(t(e[r],r,e))return r;return-1}function h(e,t,n){return t==t?function(e,t,n){for(var o=n-1,a=e.length;++o<a;)if(e[o]===t)return o;return-1}(e,t,n):g(e,y,n)}function v(e,t,n,o){for(var a=n-1,r=e.length;++a<r;)if(o(e[a],t))return a;return-1}function y(e){return e!=e}function b(e,t){var n=null==e?0:e.length;return n?I(e,t)/n:X}function w(e){return function(t){return null==t?B:t[e]}}function k(e){return function(t){return null==e?B:e[t]}}function _(e,t,n,o,a){return a(e,(function(e,a,r){n=o?(o=!1,e):t(n,e,a,r)})),n}function I(e,t){for(var n,o=-1,a=e.length;++o<a;){var r=t(e[o]);r!==B&&(n=n===B?r:n+r)}return n}function A(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function L(e){return e?e.slice(0,N(e)+1).replace(We,""):e}function C(e){return function(t){return e(t)}}function S(e,t){return l(t,(function(t){return e[t]}))}function j(e,t){return e.has(t)}function D(e,t){for(var n=-1,o=e.length;++n<o&&h(t,e[n],0)>-1;);return n}function T(e,t){for(var n=e.length;n--&&h(t,e[n],0)>-1;);return n}function P(e){return"\\"+Kt[e]}function x(e){return zt.test(e)}function U(e){return Vt.test(e)}function R(e){var t=-1,n=Array(e.size);return e.forEach((function(e,o){n[++t]=[o,e]})),n}function M(e,t){return function(n){return e(t(n))}}function H(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var s=e[n];s!==t&&s!==V||(e[n]=V,r[a++]=n)}return r}function O(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function $(e){return x(e)?function(e){for(var t=Bt.lastIndex=0;Bt.test(e);)++t;return t}(e):fn(e)}function E(e){return x(e)?function(e){return e.match(Bt)||[]}(e):function(e){return e.split("")}(e)}function N(e){for(var t=e.length;t--&&Ge.test(e.charAt(t)););return t}function F(e){return e.match(Yt)||[]}var B,Y="Expected a function",z="__lodash_hash_undefined__",V="__lodash_placeholder__",W=16,G=32,q=64,J=128,K=256,Q=1/0,Z=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,oe=[["ary",J],["bind",1],["bindKey",2],["curry",8],["curryRight",W],["flip",512],["partial",G],["partialRight",q],["rearg",K]],ae="[object Arguments]",re="[object Array]",se="[object Boolean]",ie="[object Date]",le="[object Error]",ue="[object Function]",ce="[object GeneratorFunction]",de="[object Map]",pe="[object Number]",me="[object Object]",fe="[object Promise]",ge="[object RegExp]",he="[object Set]",ve="[object String]",ye="[object Symbol]",be="[object WeakMap]",we="[object ArrayBuffer]",ke="[object DataView]",_e="[object Float32Array]",Ie="[object Float64Array]",Ae="[object Int8Array]",Le="[object Int16Array]",Ce="[object Int32Array]",Se="[object Uint8Array]",je="[object Uint8ClampedArray]",De="[object Uint16Array]",Te="[object Uint32Array]",Pe=/\b__p \+= '';/g,xe=/\b(__p \+=) '' \+/g,Ue=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Re=/&(?:amp|lt|gt|quot|#39);/g,Me=/[&<>"']/g,He=RegExp(Re.source),Oe=RegExp(Me.source),$e=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,Ne=/<%=([\s\S]+?)%>/g,Fe=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Be=/^\w*$/,Ye=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ze=/[\\^$.*+?()[\]{}|]/g,Ve=RegExp(ze.source),We=/^\s+/,Ge=/\s/,qe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Je=/\{\n\/\* \[wrapped with (.+)\] \*/,Ke=/,? & /,Qe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ze=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,ot=/^0b[01]+$/i,at=/^\[object .+?Constructor\]$/,rt=/^0o[0-7]+$/i,st=/^(?:0|[1-9]\d*)$/,it=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ut=/['\n\r\u2028\u2029\\]/g,ct="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pt="\\u2700-\\u27bf",mt="a-z\\xdf-\\xf6\\xf8-\\xff",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",gt="\\ufe0e\\ufe0f",ht="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",yt="["+ct+"]",bt="["+ht+"]",wt="["+dt+"]",kt="\\d+",_t="["+pt+"]",It="["+mt+"]",At="[^"+ct+ht+kt+pt+mt+ft+"]",Lt="\\ud83c[\\udffb-\\udfff]",Ct="[^"+ct+"]",St="(?:\\ud83c[\\udde6-\\uddff]){2}",jt="[\\ud800-\\udbff][\\udc00-\\udfff]",Dt="["+ft+"]",Tt="\\u200d",Pt="(?:"+It+"|"+At+")",xt="(?:"+Dt+"|"+At+")",Ut="(?:['’](?:d|ll|m|re|s|t|ve))?",Rt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Mt="(?:"+wt+"|"+Lt+")"+"?",Ht="["+gt+"]?",Ot=Ht+Mt+("(?:"+Tt+"(?:"+[Ct,St,jt].join("|")+")"+Ht+Mt+")*"),$t="(?:"+[_t,St,jt].join("|")+")"+Ot,Et="(?:"+[Ct+wt+"?",wt,St,jt,yt].join("|")+")",Nt=RegExp(vt,"g"),Ft=RegExp(wt,"g"),Bt=RegExp(Lt+"(?="+Lt+")|"+Et+Ot,"g"),Yt=RegExp([Dt+"?"+It+"+"+Ut+"(?="+[bt,Dt,"$"].join("|")+")",xt+"+"+Rt+"(?="+[bt,Dt+Pt,"$"].join("|")+")",Dt+"?"+Pt+"+"+Ut,Dt+"+"+Rt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",kt,$t].join("|"),"g"),zt=RegExp("["+Tt+ct+dt+gt+"]"),Vt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Wt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Gt=-1,qt={};qt[_e]=qt[Ie]=qt[Ae]=qt[Le]=qt[Ce]=qt[Se]=qt[je]=qt[De]=qt[Te]=!0,qt[ae]=qt[re]=qt[we]=qt[se]=qt[ke]=qt[ie]=qt[le]=qt[ue]=qt[de]=qt[pe]=qt[me]=qt[ge]=qt[he]=qt[ve]=qt[be]=!1;var Jt={};Jt[ae]=Jt[re]=Jt[we]=Jt[ke]=Jt[se]=Jt[ie]=Jt[_e]=Jt[Ie]=Jt[Ae]=Jt[Le]=Jt[Ce]=Jt[de]=Jt[pe]=Jt[me]=Jt[ge]=Jt[he]=Jt[ve]=Jt[ye]=Jt[Se]=Jt[je]=Jt[De]=Jt[Te]=!0,Jt[le]=Jt[ue]=Jt[be]=!1;var Kt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qt=parseFloat,Zt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,on=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=on&&on.exports===nn,rn=an&&Xt.process,sn=function(){try{var e=on&&on.require&&on.require("util").types;return e||rn&&rn.binding&&rn.binding("util")}catch(e){}}(),ln=sn&&sn.isArrayBuffer,un=sn&&sn.isDate,cn=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,mn=sn&&sn.isTypedArray,fn=w("length"),gn=k({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),hn=k({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=k({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function k(Ge){function Qe(e){if(Fa(e)&&!Ti(e)&&!(e instanceof pt)){if(e instanceof dt)return e;if(Tr.call(e,"__wrapped__"))return ma(e)}return new dt(e)}function ct(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=B}function pt(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new gt;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new ft(e)).size}function yt(e,t){var n=Ti(e),o=!n&&Di(e),a=!n&&!o&&xi(e),r=!n&&!o&&!a&&Oi(e),s=n||o||a||r,i=s?A(e.length,Ir):[],l=i.length;for(var u in e)!t&&!Tr.call(e,u)||s&&("length"==u||a&&("offset"==u||"parent"==u)||r&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Qo(u,l))||i.push(u);return i}function bt(e){var t=e.length;return t?e[Un(0,t-1)]:B}function wt(e,t){return ua(co(e),Dt(t,0,e.length))}function kt(e){return ua(co(e))}function _t(e,t,n){(n===B||Ua(e[t],n))&&(n!==B||t in e)||St(e,t,n)}function It(e,t,n){var o=e[t];Tr.call(e,t)&&Ua(o,n)&&(n!==B||t in e)||St(e,t,n)}function At(e,t){for(var n=e.length;n--;)if(Ua(e[n][0],t))return n;return-1}function Lt(e,t,n,o){return js(e,(function(e,a,r){t(o,e,n(e),r)})),o}function Ct(e,t){return e&&po(t,tr(t),e)}function St(e,t,n){"__proto__"==t&&qr?qr(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function jt(e,t){for(var n=-1,o=t.length,a=hr(o),r=null==e;++n<o;)a[n]=r?B:Xa(e,t[n]);return a}function Dt(e,t,n){return e==e&&(n!==B&&(e=e<=n?e:n),t!==B&&(e=e>=t?e:t)),e}function Tt(e,t,o,a,r,s){var i,l=1&t,u=2&t,c=4&t;if(o&&(i=r?o(e,a,r,s):o(e)),i!==B)return i;if(!Na(e))return e;var d=Ti(e);if(d){if(i=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Tr.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return co(e,i)}else{var p=Ns(e),m=p==ue||p==ce;if(xi(e))return ao(e,l);if(p==me||p==ae||m&&!r){if(i=u||m?{}:Jo(e),!l)return u?function(e,t){return po(e,Es(e),t)}(e,function(e,t){return e&&po(t,nr(t),e)}(i,e)):function(e,t){return po(e,$s(e),t)}(e,Ct(i,e))}else{if(!Jt[p])return r?e:{};i=function(e,t,n){var o=e.constructor;switch(t){case we:return ro(e);case se:case ie:return new o(+e);case ke:return function(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case _e:case Ie:case Ae:case Le:case Ce:case Se:case je:case De:case Te:return so(e,n);case de:return new o;case pe:case ve:return new o(e);case ge:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case he:return new o;case ye:return function(e){return Ls?kr(Ls.call(e)):{}}(e)}}(e,p,l)}}s||(s=new vt);var f=s.get(e);if(f)return f;s.set(e,i),Hi(e)?e.forEach((function(n){i.add(Tt(n,t,o,n,e,s))})):Ri(e)&&e.forEach((function(n,a){i.set(a,Tt(n,t,o,a,e,s))}));var g=d?B:(c?u?Fo:No:u?nr:tr)(e);return n(g||e,(function(n,a){g&&(n=e[a=n]),It(i,a,Tt(n,t,o,a,e,s))})),i}function Pt(e,t,n){var o=n.length;if(null==e)return!o;for(e=kr(e);o--;){var a=n[o],r=t[a],s=e[a];if(s===B&&!(a in e)||!r(s))return!1}return!0}function xt(e,t,n){if("function"!=typeof e)throw new Ar(Y);return Ys((function(){e.apply(B,n)}),t)}function Ut(e,t,n,o){var a=-1,r=s,u=!0,c=e.length,d=[],p=t.length;if(!c)return d;n&&(t=l(t,C(n))),o?(r=i,u=!1):t.length>=200&&(r=j,u=!1,t=new ht(t));e:for(;++a<c;){var m=e[a],f=null==n?m:n(m);if(m=o||0!==m?m:0,u&&f==f){for(var g=p;g--;)if(t[g]===f)continue e;d.push(m)}else r(t,f,o)||d.push(m)}return d}function Rt(e,t){var n=!0;return js(e,(function(e,o,a){return n=!!t(e,o,a)})),n}function Mt(e,t,n){for(var o=-1,a=e.length;++o<a;){var r=e[o],s=t(r);if(null!=s&&(i===B?s==s&&!Va(s):n(s,i)))var i=s,l=r}return l}function Ht(e,t){var n=[];return js(e,(function(e,o,a){t(e,o,a)&&n.push(e)})),n}function Ot(e,t,n,o,a){var r=-1,s=e.length;for(n||(n=Ko),a||(a=[]);++r<s;){var i=e[r];t>0&&n(i)?t>1?Ot(i,t-1,n,o,a):u(a,i):o||(a[a.length]=i)}return a}function $t(e,t){return e&&Ts(e,t,tr)}function Et(e,t){return e&&Ps(e,t,tr)}function Bt(e,t){return r(t,(function(t){return Oa(e[t])}))}function Yt(e,t){for(var n=0,o=(t=no(t,e)).length;null!=e&&n<o;)e=e[ca(t[n++])];return n&&n==o?e:B}function zt(e,t,n){var o=t(e);return Ti(e)?o:u(o,n(e))}function Vt(e){return null==e?e===B?"[object Undefined]":"[object Null]":Gr&&Gr in kr(e)?function(e){var t=Tr.call(e,Gr),n=e[Gr];try{e[Gr]=B;var o=!0}catch(e){}var a=Ur.call(e);return o&&(t?e[Gr]=n:delete e[Gr]),a}(e):function(e){return Ur.call(e)}(e)}function Kt(e,t){return e>t}function Xt(e,t){return null!=e&&Tr.call(e,t)}function en(e,t){return null!=e&&t in kr(e)}function nn(e,t,n){for(var o=n?i:s,a=e[0].length,r=e.length,u=r,c=hr(r),d=1/0,p=[];u--;){var m=e[u];u&&t&&(m=l(m,C(t))),d=ss(m.length,d),c[u]=!n&&(t||a>=120&&m.length>=120)?new ht(u&&m):B}m=e[0];var f=-1,g=c[0];e:for(;++f<a&&p.length<d;){var h=m[f],v=t?t(h):h;if(h=n||0!==h?h:0,!(g?j(g,v):o(p,v,n))){for(u=r;--u;){var y=c[u];if(!(y?j(y,v):o(e[u],v,n)))continue e}g&&g.push(v),p.push(h)}}return p}function on(t,n,o){var a=null==(t=ra(t,n=no(n,t)))?t:t[ca(ya(n))];return null==a?B:e(a,t,o)}function rn(e){return Fa(e)&&Vt(e)==ae}function sn(e,t,n,o,a){return e===t||(null==e||null==t||!Fa(e)&&!Fa(t)?e!=e&&t!=t:function(e,t,n,o,a,r){var s=Ti(e),i=Ti(t),l=s?re:Ns(e),u=i?re:Ns(t);l=l==ae?me:l,u=u==ae?me:u;var c=l==me,d=u==me,p=l==u;if(p&&xi(e)){if(!xi(t))return!1;s=!0,c=!1}if(p&&!c)return r||(r=new vt),s||Oi(e)?$o(e,t,n,o,a,r):function(e,t,n,o,a,r,s){switch(n){case ke:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case we:return!(e.byteLength!=t.byteLength||!r(new Er(e),new Er(t)));case se:case ie:case pe:return Ua(+e,+t);case le:return e.name==t.name&&e.message==t.message;case ge:case ve:return e==t+"";case de:var i=R;case he:var l=1&o;if(i||(i=O),e.size!=t.size&&!l)return!1;var u=s.get(e);if(u)return u==t;o|=2,s.set(e,t);var c=$o(i(e),i(t),o,a,r,s);return s.delete(e),c;case ye:if(Ls)return Ls.call(e)==Ls.call(t)}return!1}(e,t,l,n,o,a,r);if(!(1&n)){var m=c&&Tr.call(e,"__wrapped__"),f=d&&Tr.call(t,"__wrapped__");if(m||f){var g=m?e.value():e,h=f?t.value():t;return r||(r=new vt),a(g,h,n,o,r)}}return!!p&&(r||(r=new vt),function(e,t,n,o,a,r){var s=1&n,i=No(e),l=i.length;if(l!=No(t).length&&!s)return!1;for(var u=l;u--;){var c=i[u];if(!(s?c in t:Tr.call(t,c)))return!1}var d=r.get(e),p=r.get(t);if(d&&p)return d==t&&p==e;var m=!0;r.set(e,t),r.set(t,e);for(var f=s;++u<l;){var g=e[c=i[u]],h=t[c];if(o)var v=s?o(h,g,c,t,e,r):o(g,h,c,e,t,r);if(!(v===B?g===h||a(g,h,n,o,r):v)){m=!1;break}f||(f="constructor"==c)}if(m&&!f){var y=e.constructor,b=t.constructor;y!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return r.delete(e),r.delete(t),m}(e,t,n,o,a,r))}(e,t,n,o,sn,a))}function fn(e,t,n,o){var a=n.length,r=a,s=!o;if(null==e)return!r;for(e=kr(e);a--;){var i=n[a];if(s&&i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++a<r;){var l=(i=n[a])[0],u=e[l],c=i[1];if(s&&i[2]){if(u===B&&!(l in e))return!1}else{var d=new vt;if(o)var p=o(u,c,l,e,t,d);if(!(p===B?sn(c,u,3,o,d):p))return!1}}return!0}function bn(e){return!(!Na(e)||function(e){return!!xr&&xr in e}(e))&&(Oa(e)?Hr:at).test(da(e))}function wn(e){return"function"==typeof e?e:null==e?ur:"object"==typeof e?Ti(e)?Cn(e[0],e[1]):Ln(e):mr(e)}function kn(e){if(!ta(e))return as(e);var t=[];for(var n in kr(e))Tr.call(e,n)&&"constructor"!=n&&t.push(n);return t}function _n(e){if(!Na(e))return function(e){var t=[];if(null!=e)for(var n in kr(e))t.push(n);return t}(e);var t=ta(e),n=[];for(var o in e)("constructor"!=o||!t&&Tr.call(e,o))&&n.push(o);return n}function In(e,t){return e<t}function An(e,t){var n=-1,o=Ra(e)?hr(e.length):[];return js(e,(function(e,a,r){o[++n]=t(e,a,r)})),o}function Ln(e){var t=Wo(e);return 1==t.length&&t[0][2]?oa(t[0][0],t[0][1]):function(n){return n===e||fn(n,e,t)}}function Cn(e,t){return Xo(e)&&na(t)?oa(ca(e),t):function(n){var o=Xa(n,e);return o===B&&o===t?er(n,e):sn(t,o,3)}}function Sn(e,t,n,o,a){e!==t&&Ts(t,(function(r,s){if(a||(a=new vt),Na(r))!function(e,t,n,o,a,r,s){var i=sa(e,n),l=sa(t,n),u=s.get(l);if(u)return _t(e,n,u),B;var c=r?r(i,l,n+"",e,t,s):B,d=c===B;if(d){var p=Ti(l),m=!p&&xi(l),f=!p&&!m&&Oi(l);c=l,p||m||f?Ti(i)?c=i:Ma(i)?c=co(i):m?(d=!1,c=ao(l,!0)):f?(d=!1,c=so(l,!0)):c=[]:Ya(l)||Di(l)?(c=i,Di(i)?c=Qa(i):Na(i)&&!Oa(i)||(c=Jo(l))):d=!1}d&&(s.set(l,c),a(c,l,o,r,s),s.delete(l)),_t(e,n,c)}(e,t,s,n,Sn,o,a);else{var i=o?o(sa(e,s),r,s+"",e,t,a):B;i===B&&(i=r),_t(e,s,i)}}),nr)}function jn(e,t){var n=e.length;if(n)return Qo(t+=t<0?n:0,n)?e[t]:B}function Dn(e,t,n){t=t.length?l(t,(function(e){return Ti(e)?function(t){return Yt(t,1===e.length?e[0]:e)}:e})):[ur];var o=-1;return t=l(t,C(zo())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(An(e,(function(e,n,a){return{criteria:l(t,(function(t){return t(e)})),index:++o,value:e}})),(function(e,t){return function(e,t,n){for(var o=-1,a=e.criteria,r=t.criteria,s=a.length,i=n.length;++o<s;){var l=io(a[o],r[o]);if(l)return o>=i?l:l*("desc"==n[o]?-1:1)}return e.index-t.index}(e,t,n)}))}function Tn(e,t,n){for(var o=-1,a=t.length,r={};++o<a;){var s=t[o],i=Yt(e,s);n(i,s)&&$n(r,no(s,e),i)}return r}function Pn(e,t,n,o){var a=o?v:h,r=-1,s=t.length,i=e;for(e===t&&(t=co(t)),n&&(i=l(e,C(n)));++r<s;)for(var u=0,c=t[r],d=n?n(c):c;(u=a(i,d,u,o))>-1;)i!==e&&zr.call(i,u,1),zr.call(e,u,1);return e}function xn(e,t){for(var n=e?t.length:0,o=n-1;n--;){var a=t[n];if(n==o||a!==r){var r=a;Qo(a)?zr.call(e,a,1):qn(e,a)}}return e}function Un(e,t){return e+Xr(us()*(t-e+1))}function Rn(e,t){var n="";if(!e||t<1||t>Z)return n;do{t%2&&(n+=e),(t=Xr(t/2))&&(e+=e)}while(t);return n}function Mn(e,t){return zs(aa(e,t,ur),e+"")}function Hn(e){return bt(ar(e))}function On(e,t){var n=ar(e);return ua(n,Dt(t,0,n.length))}function $n(e,t,n,o){if(!Na(e))return e;for(var a=-1,r=(t=no(t,e)).length,s=r-1,i=e;null!=i&&++a<r;){var l=ca(t[a]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(a!=s){var c=i[l];(u=o?o(c,l,i):B)===B&&(u=Na(c)?c:Qo(t[a+1])?[]:{})}It(i,l,u),i=i[l]}return e}function En(e){return ua(ar(e))}function Nn(e,t,n){var o=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(n=n>a?a:n)<0&&(n+=a),a=t>n?0:n-t>>>0,t>>>=0;for(var r=hr(a);++o<a;)r[o]=e[o+t];return r}function Fn(e,t){var n;return js(e,(function(e,o,a){return!(n=t(e,o,a))})),!!n}function Bn(e,t,n){var o=0,a=null==e?o:e.length;if("number"==typeof t&&t==t&&a<=ne){for(;o<a;){var r=o+a>>>1,s=e[r];null!==s&&!Va(s)&&(n?s<=t:s<t)?o=r+1:a=r}return a}return Yn(e,t,ur,n)}function Yn(e,t,n,o){var a=0,r=null==e?0:e.length;if(0===r)return 0;for(var s=(t=n(t))!=t,i=null===t,l=Va(t),u=t===B;a<r;){var c=Xr((a+r)/2),d=n(e[c]),p=d!==B,m=null===d,f=d==d,g=Va(d);if(s)var h=o||f;else h=u?f&&(o||p):i?f&&p&&(o||!m):l?f&&p&&!m&&(o||!g):!m&&!g&&(o?d<=t:d<t);h?a=c+1:r=c}return ss(r,te)}function zn(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var s=e[n],i=t?t(s):s;if(!n||!Ua(i,l)){var l=i;r[a++]=0===s?0:s}}return r}function Vn(e){return"number"==typeof e?e:Va(e)?X:+e}function Wn(e){if("string"==typeof e)return e;if(Ti(e))return l(e,Wn)+"";if(Va(e))return Cs?Cs.call(e):"";var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function Gn(e,t,n){var o=-1,a=s,r=e.length,l=!0,u=[],c=u;if(n)l=!1,a=i;else if(r>=200){var d=t?null:Hs(e);if(d)return O(d);l=!1,a=j,c=new ht}else c=t?[]:u;e:for(;++o<r;){var p=e[o],m=t?t(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=c.length;f--;)if(c[f]===m)continue e;t&&c.push(m),u.push(p)}else a(c,m,n)||(c!==u&&c.push(m),u.push(p))}return u}function qn(e,t){return null==(e=ra(e,t=no(t,e)))||delete e[ca(ya(t))]}function Jn(e,t,n,o){return $n(e,t,n(Yt(e,t)),o)}function Kn(e,t,n,o){for(var a=e.length,r=o?a:-1;(o?r--:++r<a)&&t(e[r],r,e););return n?Nn(e,o?0:r,o?r+1:a):Nn(e,o?r+1:0,o?a:r)}function Qn(e,t){var n=e;return n instanceof pt&&(n=n.value()),c(t,(function(e,t){return t.func.apply(t.thisArg,u([e],t.args))}),n)}function Zn(e,t,n){var o=e.length;if(o<2)return o?Gn(e[0]):[];for(var a=-1,r=hr(o);++a<o;)for(var s=e[a],i=-1;++i<o;)i!=a&&(r[a]=Ut(r[a]||s,e[i],t,n));return Gn(Ot(r,1),t,n)}function Xn(e,t,n){for(var o=-1,a=e.length,r=t.length,s={};++o<a;)n(s,e[o],o<r?t[o]:B);return s}function eo(e){return Ma(e)?e:[]}function to(e){return"function"==typeof e?e:ur}function no(e,t){return Ti(e)?e:Xo(e,t)?[e]:Vs(Za(e))}function oo(e,t,n){var o=e.length;return n=n===B?o:n,!t&&n>=o?e:Nn(e,t,n)}function ao(e,t){if(t)return e.slice();var n=e.length,o=Nr?Nr(n):new e.constructor(n);return e.copy(o),o}function ro(e){var t=new e.constructor(e.byteLength);return new Er(t).set(new Er(e)),t}function so(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.length)}function io(e,t){if(e!==t){var n=e!==B,o=null===e,a=e==e,r=Va(e),s=t!==B,i=null===t,l=t==t,u=Va(t);if(!i&&!u&&!r&&e>t||r&&s&&l&&!i&&!u||o&&s&&l||!n&&l||!a)return 1;if(!o&&!r&&!u&&e<t||u&&n&&a&&!o&&!r||i&&n&&a||!s&&a||!l)return-1}return 0}function lo(e,t,n,o){for(var a=-1,r=e.length,s=n.length,i=-1,l=t.length,u=rs(r-s,0),c=hr(l+u),d=!o;++i<l;)c[i]=t[i];for(;++a<s;)(d||a<r)&&(c[n[a]]=e[a]);for(;u--;)c[i++]=e[a++];return c}function uo(e,t,n,o){for(var a=-1,r=e.length,s=-1,i=n.length,l=-1,u=t.length,c=rs(r-i,0),d=hr(c+u),p=!o;++a<c;)d[a]=e[a];for(var m=a;++l<u;)d[m+l]=t[l];for(;++s<i;)(p||a<r)&&(d[m+n[s]]=e[a++]);return d}function co(e,t){var n=-1,o=e.length;for(t||(t=hr(o));++n<o;)t[n]=e[n];return t}function po(e,t,n,o){var a=!n;n||(n={});for(var r=-1,s=t.length;++r<s;){var i=t[r],l=o?o(n[i],e[i],i,n,e):B;l===B&&(l=e[i]),a?St(n,i,l):It(n,i,l)}return n}function mo(e,n){return function(o,a){var r=Ti(o)?t:Lt,s=n?n():{};return r(o,e,zo(a,2),s)}}function fo(e){return Mn((function(t,n){var o=-1,a=n.length,r=a>1?n[a-1]:B,s=a>2?n[2]:B;for(r=e.length>3&&"function"==typeof r?(a--,r):B,s&&Zo(n[0],n[1],s)&&(r=a<3?B:r,a=1),t=kr(t);++o<a;){var i=n[o];i&&e(t,i,o,r)}return t}))}function go(e,t){return function(n,o){if(null==n)return n;if(!Ra(n))return e(n,o);for(var a=n.length,r=t?a:-1,s=kr(n);(t?r--:++r<a)&&!1!==o(s[r],r,s););return n}}function ho(e){return function(t,n,o){for(var a=-1,r=kr(t),s=o(t),i=s.length;i--;){var l=s[e?i:++a];if(!1===n(r[l],l,r))break}return t}}function vo(e){return function(t){var n=x(t=Za(t))?E(t):B,o=n?n[0]:t.charAt(0),a=n?oo(n,1).join(""):t.slice(1);return o[e]()+a}}function yo(e){return function(t){return c(ir(sr(t).replace(Nt,"")),e,"")}}function bo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Ss(e.prototype),o=e.apply(n,t);return Na(o)?o:n}}function wo(t,n,o){var a=bo(t);return function r(){for(var s=arguments.length,i=hr(s),l=s,u=Yo(r);l--;)i[l]=arguments[l];var c=s<3&&i[0]!==u&&i[s-1]!==u?[]:H(i,u);return(s-=c.length)<o?Po(t,n,Io,r.placeholder,B,i,c,B,B,o-s):e(this&&this!==tn&&this instanceof r?a:t,this,i)}}function ko(e){return function(t,n,o){var a=kr(t);if(!Ra(t)){var r=zo(n,3);t=tr(t),n=function(e){return r(a[e],e,a)}}var s=e(t,n,o);return s>-1?a[r?t[s]:s]:B}}function _o(e){return Eo((function(t){var n=t.length,o=n,a=dt.prototype.thru;for(e&&t.reverse();o--;){var r=t[o];if("function"!=typeof r)throw new Ar(Y);if(a&&!s&&"wrapper"==Bo(r))var s=new dt([],!0)}for(o=s?o:n;++o<n;){var i=Bo(r=t[o]),l="wrapper"==i?Os(r):B;s=l&&ea(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?s[Bo(l[0])].apply(s,l[3]):1==r.length&&ea(r)?s[i]():s.thru(r)}return function(){var e=arguments,o=e[0];if(s&&1==e.length&&Ti(o))return s.plant(o).value();for(var a=0,r=n?t[a].apply(this,e):o;++a<n;)r=t[a].call(this,r);return r}}))}function Io(e,t,n,o,a,r,s,i,l,u){var c=t&J,d=1&t,p=2&t,m=24&t,f=512&t,g=p?B:bo(e);return function h(){for(var v=arguments.length,y=hr(v),b=v;b--;)y[b]=arguments[b];if(m)var w=Yo(h),k=function(e,t){for(var n=e.length,o=0;n--;)e[n]===t&&++o;return o}(y,w);if(o&&(y=lo(y,o,a,m)),r&&(y=uo(y,r,s,m)),v-=k,m&&v<u)return Po(e,t,Io,h.placeholder,n,y,H(y,w),i,l,u-v);var _=d?n:this,I=p?_[e]:e;return v=y.length,i?y=function(e,t){for(var n=e.length,o=ss(t.length,n),a=co(e);o--;){var r=t[o];e[o]=Qo(r,n)?a[r]:B}return e}(y,i):f&&v>1&&y.reverse(),c&&l<v&&(y.length=l),this&&this!==tn&&this instanceof h&&(I=g||bo(I)),I.apply(_,y)}}function Ao(e,t){return function(n,o){return function(e,t,n,o){return $t(e,(function(e,a,r){t(o,n(e),a,r)})),o}(n,e,t(o),{})}}function Lo(e,t){return function(n,o){var a;if(n===B&&o===B)return t;if(n!==B&&(a=n),o!==B){if(a===B)return o;"string"==typeof n||"string"==typeof o?(n=Wn(n),o=Wn(o)):(n=Vn(n),o=Vn(o)),a=e(n,o)}return a}}function Co(t){return Eo((function(n){return n=l(n,C(zo())),Mn((function(o){var a=this;return t(n,(function(t){return e(t,a,o)}))}))}))}function So(e,t){var n=(t=t===B?" ":Wn(t)).length;if(n<2)return n?Rn(t,e):t;var o=Rn(t,Zr(e/$(t)));return x(t)?oo(E(o),0,e).join(""):o.slice(0,e)}function jo(t,n,o,a){var r=1&n,s=bo(t);return function n(){for(var i=-1,l=arguments.length,u=-1,c=a.length,d=hr(c+l),p=this&&this!==tn&&this instanceof n?s:t;++u<c;)d[u]=a[u];for(;l--;)d[u++]=arguments[++i];return e(p,r?o:this,d)}}function Do(e){return function(t,n,o){return o&&"number"!=typeof o&&Zo(t,n,o)&&(n=o=B),t=Ga(t),n===B?(n=t,t=0):n=Ga(n),function(e,t,n,o){for(var a=-1,r=rs(Zr((t-e)/(n||1)),0),s=hr(r);r--;)s[o?r:++a]=e,e+=n;return s}(t,n,o=o===B?t<n?1:-1:Ga(o),e)}}function To(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Ka(t),n=Ka(n)),e(t,n)}}function Po(e,t,n,o,a,r,s,i,l,u){var c=8&t;t|=c?G:q,4&(t&=~(c?q:G))||(t&=-4);var d=[e,t,a,c?r:B,c?s:B,c?B:r,c?B:s,i,l,u],p=n.apply(B,d);return ea(e)&&Bs(p,d),p.placeholder=o,ia(p,e,t)}function xo(e){var t=wr[e];return function(e,n){if(e=Ka(e),(n=null==n?0:ss(qa(n),292))&&ns(e)){var o=(Za(e)+"e").split("e");return+((o=(Za(t(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return t(e)}}function Uo(e){return function(t){var n=Ns(t);return n==de?R(t):n==he?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return l(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Ro(e,t,n,o,a,r,s,i){var l=2&t;if(!l&&"function"!=typeof e)throw new Ar(Y);var u=o?o.length:0;if(u||(t&=-97,o=a=B),s=s===B?s:rs(qa(s),0),i=i===B?i:qa(i),u-=a?a.length:0,t&q){var c=o,d=a;o=a=B}var p=l?B:Os(e),m=[e,t,n,o,a,c,d,r,s,i];if(p&&function(e,t){var n=e[1],o=t[1],a=n|o,r=a<131,s=o==J&&8==n||o==J&&n==K&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!r&&!s)return e;1&o&&(e[2]=t[2],a|=1&n?0:4);var i=t[3];if(i){var l=e[3];e[3]=l?lo(l,i,t[4]):i,e[4]=l?H(e[3],V):t[4]}i=t[5],i&&(l=e[5],e[5]=l?uo(l,i,t[6]):i,e[6]=l?H(e[5],V):t[6]),i=t[7],i&&(e[7]=i),o&J&&(e[8]=null==e[8]?t[8]:ss(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=a}(m,p),e=m[0],t=m[1],n=m[2],o=m[3],a=m[4],!(i=m[9]=m[9]===B?l?0:e.length:rs(m[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)f=8==t||t==W?wo(e,t,i):t!=G&&33!=t||a.length?Io.apply(B,m):jo(e,t,n,o);else var f=function(e,t,n){var o=1&t,a=bo(e);return function t(){return(this&&this!==tn&&this instanceof t?a:e).apply(o?n:this,arguments)}}(e,t,n);return ia((p?xs:Bs)(f,m),e,t)}function Mo(e,t,n,o){return e===B||Ua(e,Sr[n])&&!Tr.call(o,n)?t:e}function Ho(e,t,n,o,a,r){return Na(e)&&Na(t)&&(r.set(t,e),Sn(e,t,B,Ho,r),r.delete(t)),e}function Oo(e){return Ya(e)?B:e}function $o(e,t,n,o,a,r){var s=1&n,i=e.length,l=t.length;if(i!=l&&!(s&&l>i))return!1;var u=r.get(e),c=r.get(t);if(u&&c)return u==t&&c==e;var d=-1,m=!0,f=2&n?new ht:B;for(r.set(e,t),r.set(t,e);++d<i;){var g=e[d],h=t[d];if(o)var v=s?o(h,g,d,t,e,r):o(g,h,d,e,t,r);if(v!==B){if(v)continue;m=!1;break}if(f){if(!p(t,(function(e,t){if(!j(f,t)&&(g===e||a(g,e,n,o,r)))return f.push(t)}))){m=!1;break}}else if(g!==h&&!a(g,h,n,o,r)){m=!1;break}}return r.delete(e),r.delete(t),m}function Eo(e){return zs(aa(e,B,ha),e+"")}function No(e){return zt(e,tr,$s)}function Fo(e){return zt(e,nr,Es)}function Bo(e){for(var t=e.name+"",n=ys[t],o=Tr.call(ys,t)?n.length:0;o--;){var a=n[o],r=a.func;if(null==r||r==e)return a.name}return t}function Yo(e){return(Tr.call(Qe,"placeholder")?Qe:e).placeholder}function zo(){var e=Qe.iteratee||cr;return e=e===cr?wn:e,arguments.length?e(arguments[0],arguments[1]):e}function Vo(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function Wo(e){for(var t=tr(e),n=t.length;n--;){var o=t[n],a=e[o];t[n]=[o,a,na(a)]}return t}function Go(e,t){var n=function(e,t){return null==e?B:e[t]}(e,t);return bn(n)?n:B}function qo(e,t,n){for(var o=-1,a=(t=no(t,e)).length,r=!1;++o<a;){var s=ca(t[o]);if(!(r=null!=e&&n(e,s)))break;e=e[s]}return r||++o!=a?r:!!(a=null==e?0:e.length)&&Ea(a)&&Qo(s,a)&&(Ti(e)||Di(e))}function Jo(e){return"function"!=typeof e.constructor||ta(e)?{}:Ss(Fr(e))}function Ko(e){return Ti(e)||Di(e)||!!(Vr&&e&&e[Vr])}function Qo(e,t){var n=typeof e;return!!(t=null==t?Z:t)&&("number"==n||"symbol"!=n&&st.test(e))&&e>-1&&e%1==0&&e<t}function Zo(e,t,n){if(!Na(n))return!1;var o=typeof t;return!!("number"==o?Ra(n)&&Qo(t,n.length):"string"==o&&t in n)&&Ua(n[t],e)}function Xo(e,t){if(Ti(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Va(e))||Be.test(e)||!Fe.test(e)||null!=t&&e in kr(t)}function ea(e){var t=Bo(e),n=Qe[t];if("function"!=typeof n||!(t in pt.prototype))return!1;if(e===n)return!0;var o=Os(n);return!!o&&e===o[0]}function ta(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Sr)}function na(e){return e==e&&!Na(e)}function oa(e,t){return function(n){return null!=n&&n[e]===t&&(t!==B||e in kr(n))}}function aa(t,n,o){return n=rs(n===B?t.length-1:n,0),function(){for(var a=arguments,r=-1,s=rs(a.length-n,0),i=hr(s);++r<s;)i[r]=a[n+r];r=-1;for(var l=hr(n+1);++r<n;)l[r]=a[r];return l[n]=o(i),e(t,this,l)}}function ra(e,t){return t.length<2?e:Yt(e,Nn(t,0,-1))}function sa(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function ia(e,t,n){var o=t+"";return zs(e,function(e,t){var n=t.length;if(!n)return e;var o=n-1;return t[o]=(n>1?"& ":"")+t[o],t=t.join(n>2?", ":" "),e.replace(qe,"{\n/* [wrapped with "+t+"] */\n")}(o,pa(function(e){var t=e.match(Je);return t?t[1].split(Ke):[]}(o),n)))}function la(e){var t=0,n=0;return function(){var o=is(),a=16-(o-n);if(n=o,a>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(B,arguments)}}function ua(e,t){var n=-1,o=e.length,a=o-1;for(t=t===B?o:t;++n<t;){var r=Un(n,a),s=e[r];e[r]=e[n],e[n]=s}return e.length=t,e}function ca(e){if("string"==typeof e||Va(e))return e;var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function da(e){if(null!=e){try{return Dr.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function pa(e,t){return n(oe,(function(n){var o="_."+n[0];t&n[1]&&!s(e,o)&&e.push(o)})),e.sort()}function ma(e){if(e instanceof pt)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=co(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function fa(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=rs(o+a,0)),g(e,zo(t,3),a)}function ga(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o-1;return n!==B&&(a=qa(n),a=n<0?rs(o+a,0):ss(a,o-1)),g(e,zo(t,3),a,!0)}function ha(e){return null!=e&&e.length?Ot(e,1):[]}function va(e){return e&&e.length?e[0]:B}function ya(e){var t=null==e?0:e.length;return t?e[t-1]:B}function ba(e,t){return e&&e.length&&t&&t.length?Pn(e,t):e}function wa(e){return null==e?e:cs.call(e)}function ka(e){if(!e||!e.length)return[];var t=0;return e=r(e,(function(e){if(Ma(e))return t=rs(e.length,t),!0})),A(t,(function(t){return l(e,w(t))}))}function _a(t,n){if(!t||!t.length)return[];var o=ka(t);return null==n?o:l(o,(function(t){return e(n,B,t)}))}function Ia(e){var t=Qe(e);return t.__chain__=!0,t}function Aa(e,t){return t(e)}function La(e,t){return(Ti(e)?n:js)(e,zo(t,3))}function Ca(e,t){return(Ti(e)?o:Ds)(e,zo(t,3))}function Sa(e,t){return(Ti(e)?l:An)(e,zo(t,3))}function ja(e,t,n){return t=n?B:t,t=e&&null==t?e.length:t,Ro(e,J,B,B,B,B,t)}function Da(e,t){var n;if("function"!=typeof t)throw new Ar(Y);return e=qa(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=B),n}}function Ta(e,t,n){function o(t){var n=l,o=u;return l=u=B,f=t,d=e.apply(o,n)}function a(e){var n=e-m;return m===B||n>=t||n<0||h&&e-f>=c}function r(){var e=yi();return a(e)?s(e):(p=Ys(r,function(e){var n=t-(e-m);return h?ss(n,c-(e-f)):n}(e)),B)}function s(e){return p=B,v&&l?o(e):(l=u=B,d)}function i(){var e=yi(),n=a(e);if(l=arguments,u=this,m=e,n){if(p===B)return function(e){return f=e,p=Ys(r,t),g?o(e):d}(m);if(h)return Ms(p),p=Ys(r,t),o(m)}return p===B&&(p=Ys(r,t)),d}var l,u,c,d,p,m,f=0,g=!1,h=!1,v=!0;if("function"!=typeof e)throw new Ar(Y);return t=Ka(t)||0,Na(n)&&(g=!!n.leading,c=(h="maxWait"in n)?rs(Ka(n.maxWait)||0,t):c,v="trailing"in n?!!n.trailing:v),i.cancel=function(){p!==B&&Ms(p),f=0,l=m=u=p=B},i.flush=function(){return p===B?d:s(yi())},i}function Pa(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Ar(Y);var n=function(){var o=arguments,a=t?t.apply(this,o):o[0],r=n.cache;if(r.has(a))return r.get(a);var s=e.apply(this,o);return n.cache=r.set(a,s)||r,s};return n.cache=new(Pa.Cache||gt),n}function xa(e){if("function"!=typeof e)throw new Ar(Y);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Ua(e,t){return e===t||e!=e&&t!=t}function Ra(e){return null!=e&&Ea(e.length)&&!Oa(e)}function Ma(e){return Fa(e)&&Ra(e)}function Ha(e){if(!Fa(e))return!1;var t=Vt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Ya(e)}function Oa(e){if(!Na(e))return!1;var t=Vt(e);return t==ue||t==ce||"[object AsyncFunction]"==t||"[object Proxy]"==t}function $a(e){return"number"==typeof e&&e==qa(e)}function Ea(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Z}function Na(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Fa(e){return null!=e&&"object"==typeof e}function Ba(e){return"number"==typeof e||Fa(e)&&Vt(e)==pe}function Ya(e){if(!Fa(e)||Vt(e)!=me)return!1;var t=Fr(e);if(null===t)return!0;var n=Tr.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Dr.call(n)==Rr}function za(e){return"string"==typeof e||!Ti(e)&&Fa(e)&&Vt(e)==ve}function Va(e){return"symbol"==typeof e||Fa(e)&&Vt(e)==ye}function Wa(e){if(!e)return[];if(Ra(e))return za(e)?E(e):co(e);if(Wr&&e[Wr])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Wr]());var t=Ns(e);return(t==de?R:t==he?O:ar)(e)}function Ga(e){return e?(e=Ka(e))===Q||e===-Q?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function qa(e){var t=Ga(e),n=t%1;return t==t?n?t-n:t:0}function Ja(e){return e?Dt(qa(e),0,ee):0}function Ka(e){if("number"==typeof e)return e;if(Va(e))return X;if(Na(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Na(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=L(e);var n=ot.test(e);return n||rt.test(e)?Zt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Qa(e){return po(e,nr(e))}function Za(e){return null==e?"":Wn(e)}function Xa(e,t,n){var o=null==e?B:Yt(e,t);return o===B?n:o}function er(e,t){return null!=e&&qo(e,t,en)}function tr(e){return Ra(e)?yt(e):kn(e)}function nr(e){return Ra(e)?yt(e,!0):_n(e)}function or(e,t){if(null==e)return{};var n=l(Fo(e),(function(e){return[e]}));return t=zo(t),Tn(e,n,(function(e,n){return t(e,n[0])}))}function ar(e){return null==e?[]:S(e,tr(e))}function rr(e){return ul(Za(e).toLowerCase())}function sr(e){return(e=Za(e))&&e.replace(it,gn).replace(Ft,"")}function ir(e,t,n){return e=Za(e),(t=n?B:t)===B?U(e)?F(e):m(e):e.match(t)||[]}function lr(e){return function(){return e}}function ur(e){return e}function cr(e){return wn("function"==typeof e?e:Tt(e,1))}function dr(e,t,o){var a=tr(t),r=Bt(t,a);null!=o||Na(t)&&(r.length||!a.length)||(o=t,t=e,e=this,r=Bt(t,tr(t)));var s=!(Na(o)&&"chain"in o&&!o.chain),i=Oa(e);return n(r,(function(n){var o=t[n];e[n]=o,i&&(e.prototype[n]=function(){var t=this.__chain__;if(s||t){var n=e(this.__wrapped__);return(n.__actions__=co(this.__actions__)).push({func:o,args:arguments,thisArg:e}),n.__chain__=t,n}return o.apply(e,u([this.value()],arguments))})})),e}function pr(){}function mr(e){return Xo(e)?w(ca(e)):function(e){return function(t){return Yt(t,e)}}(e)}function fr(){return[]}function gr(){return!1}var hr=(Ge=null==Ge?tn:yn.defaults(tn.Object(),Ge,yn.pick(tn,Wt))).Array,vr=Ge.Date,yr=Ge.Error,br=Ge.Function,wr=Ge.Math,kr=Ge.Object,_r=Ge.RegExp,Ir=Ge.String,Ar=Ge.TypeError,Lr=hr.prototype,Cr=br.prototype,Sr=kr.prototype,jr=Ge["__core-js_shared__"],Dr=Cr.toString,Tr=Sr.hasOwnProperty,Pr=0,xr=function(){var e=/[^.]+$/.exec(jr&&jr.keys&&jr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ur=Sr.toString,Rr=Dr.call(kr),Mr=tn._,Hr=_r("^"+Dr.call(Tr).replace(ze,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Or=an?Ge.Buffer:B,$r=Ge.Symbol,Er=Ge.Uint8Array,Nr=Or?Or.allocUnsafe:B,Fr=M(kr.getPrototypeOf,kr),Br=kr.create,Yr=Sr.propertyIsEnumerable,zr=Lr.splice,Vr=$r?$r.isConcatSpreadable:B,Wr=$r?$r.iterator:B,Gr=$r?$r.toStringTag:B,qr=function(){try{var e=Go(kr,"defineProperty");return e({},"",{}),e}catch(e){}}(),Jr=Ge.clearTimeout!==tn.clearTimeout&&Ge.clearTimeout,Kr=vr&&vr.now!==tn.Date.now&&vr.now,Qr=Ge.setTimeout!==tn.setTimeout&&Ge.setTimeout,Zr=wr.ceil,Xr=wr.floor,es=kr.getOwnPropertySymbols,ts=Or?Or.isBuffer:B,ns=Ge.isFinite,os=Lr.join,as=M(kr.keys,kr),rs=wr.max,ss=wr.min,is=vr.now,ls=Ge.parseInt,us=wr.random,cs=Lr.reverse,ds=Go(Ge,"DataView"),ps=Go(Ge,"Map"),ms=Go(Ge,"Promise"),fs=Go(Ge,"Set"),gs=Go(Ge,"WeakMap"),hs=Go(kr,"create"),vs=gs&&new gs,ys={},bs=da(ds),ws=da(ps),ks=da(ms),_s=da(fs),Is=da(gs),As=$r?$r.prototype:B,Ls=As?As.valueOf:B,Cs=As?As.toString:B,Ss=function(){function e(){}return function(t){if(!Na(t))return{};if(Br)return Br(t);e.prototype=t;var n=new e;return e.prototype=B,n}}();Qe.templateSettings={escape:$e,evaluate:Ee,interpolate:Ne,variable:"",imports:{_:Qe}},Qe.prototype=ct.prototype,Qe.prototype.constructor=Qe,dt.prototype=Ss(ct.prototype),dt.prototype.constructor=dt,pt.prototype=Ss(ct.prototype),pt.prototype.constructor=pt,mt.prototype.clear=function(){this.__data__=hs?hs(null):{},this.size=0},mt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},mt.prototype.get=function(e){var t=this.__data__;if(hs){var n=t[e];return n===z?B:n}return Tr.call(t,e)?t[e]:B},mt.prototype.has=function(e){var t=this.__data__;return hs?t[e]!==B:Tr.call(t,e)},mt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=hs&&t===B?z:t,this},ft.prototype.clear=function(){this.__data__=[],this.size=0},ft.prototype.delete=function(e){var t=this.__data__,n=At(t,e);return!(n<0||(n==t.length-1?t.pop():zr.call(t,n,1),--this.size,0))},ft.prototype.get=function(e){var t=this.__data__,n=At(t,e);return n<0?B:t[n][1]},ft.prototype.has=function(e){return At(this.__data__,e)>-1},ft.prototype.set=function(e,t){var n=this.__data__,o=At(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this},gt.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(ps||ft),string:new mt}},gt.prototype.delete=function(e){var t=Vo(this,e).delete(e);return this.size-=t?1:0,t},gt.prototype.get=function(e){return Vo(this,e).get(e)},gt.prototype.has=function(e){return Vo(this,e).has(e)},gt.prototype.set=function(e,t){var n=Vo(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this},ht.prototype.add=ht.prototype.push=function(e){return this.__data__.set(e,z),this},ht.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new ft,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ft){var o=n.__data__;if(!ps||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new gt(o)}return n.set(e,t),this.size=n.size,this};var js=go($t),Ds=go(Et,!0),Ts=ho(),Ps=ho(!0),xs=vs?function(e,t){return vs.set(e,t),e}:ur,Us=qr?function(e,t){return qr(e,"toString",{configurable:!0,enumerable:!1,value:lr(t),writable:!0})}:ur,Rs=Mn,Ms=Jr||function(e){return tn.clearTimeout(e)},Hs=fs&&1/O(new fs([,-0]))[1]==Q?function(e){return new fs(e)}:pr,Os=vs?function(e){return vs.get(e)}:pr,$s=es?function(e){return null==e?[]:(e=kr(e),r(es(e),(function(t){return Yr.call(e,t)})))}:fr,Es=es?function(e){for(var t=[];e;)u(t,$s(e)),e=Fr(e);return t}:fr,Ns=Vt;(ds&&Ns(new ds(new ArrayBuffer(1)))!=ke||ps&&Ns(new ps)!=de||ms&&Ns(ms.resolve())!=fe||fs&&Ns(new fs)!=he||gs&&Ns(new gs)!=be)&&(Ns=function(e){var t=Vt(e),n=t==me?e.constructor:B,o=n?da(n):"";if(o)switch(o){case bs:return ke;case ws:return de;case ks:return fe;case _s:return he;case Is:return be}return t});var Fs=jr?Oa:gr,Bs=la(xs),Ys=Qr||function(e,t){return tn.setTimeout(e,t)},zs=la(Us),Vs=function(e){var t=Pa(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ye,(function(e,n,o,a){t.push(o?a.replace(Xe,"$1"):n||e)})),t})),Ws=Mn((function(e,t){return Ma(e)?Ut(e,Ot(t,1,Ma,!0)):[]})),Gs=Mn((function(e,t){var n=ya(t);return Ma(n)&&(n=B),Ma(e)?Ut(e,Ot(t,1,Ma,!0),zo(n,2)):[]})),qs=Mn((function(e,t){var n=ya(t);return Ma(n)&&(n=B),Ma(e)?Ut(e,Ot(t,1,Ma,!0),B,n):[]})),Js=Mn((function(e){var t=l(e,eo);return t.length&&t[0]===e[0]?nn(t):[]})),Ks=Mn((function(e){var t=ya(e),n=l(e,eo);return t===ya(n)?t=B:n.pop(),n.length&&n[0]===e[0]?nn(n,zo(t,2)):[]})),Qs=Mn((function(e){var t=ya(e),n=l(e,eo);return(t="function"==typeof t?t:B)&&n.pop(),n.length&&n[0]===e[0]?nn(n,B,t):[]})),Zs=Mn(ba),Xs=Eo((function(e,t){var n=null==e?0:e.length,o=jt(e,t);return xn(e,l(t,(function(e){return Qo(e,n)?+e:e})).sort(io)),o})),ei=Mn((function(e){return Gn(Ot(e,1,Ma,!0))})),ti=Mn((function(e){var t=ya(e);return Ma(t)&&(t=B),Gn(Ot(e,1,Ma,!0),zo(t,2))})),ni=Mn((function(e){var t=ya(e);return t="function"==typeof t?t:B,Gn(Ot(e,1,Ma,!0),B,t)})),oi=Mn((function(e,t){return Ma(e)?Ut(e,t):[]})),ai=Mn((function(e){return Zn(r(e,Ma))})),ri=Mn((function(e){var t=ya(e);return Ma(t)&&(t=B),Zn(r(e,Ma),zo(t,2))})),si=Mn((function(e){var t=ya(e);return t="function"==typeof t?t:B,Zn(r(e,Ma),B,t)})),ii=Mn(ka),li=Mn((function(e){var t=e.length,n=t>1?e[t-1]:B;return n="function"==typeof n?(e.pop(),n):B,_a(e,n)})),ui=Eo((function(e){var t=e.length,n=t?e[0]:0,o=this.__wrapped__,a=function(t){return jt(t,e)};return!(t>1||this.__actions__.length)&&o instanceof pt&&Qo(n)?((o=o.slice(n,+n+(t?1:0))).__actions__.push({func:Aa,args:[a],thisArg:B}),new dt(o,this.__chain__).thru((function(e){return t&&!e.length&&e.push(B),e}))):this.thru(a)})),ci=mo((function(e,t,n){Tr.call(e,n)?++e[n]:St(e,n,1)})),di=ko(fa),pi=ko(ga),mi=mo((function(e,t,n){Tr.call(e,n)?e[n].push(t):St(e,n,[t])})),fi=Mn((function(t,n,o){var a=-1,r="function"==typeof n,s=Ra(t)?hr(t.length):[];return js(t,(function(t){s[++a]=r?e(n,t,o):on(t,n,o)})),s})),gi=mo((function(e,t,n){St(e,n,t)})),hi=mo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),vi=Mn((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Zo(e,t[0],t[1])?t=[]:n>2&&Zo(t[0],t[1],t[2])&&(t=[t[0]]),Dn(e,Ot(t,1),[])})),yi=Kr||function(){return tn.Date.now()},bi=Mn((function(e,t,n){var o=1;if(n.length){var a=H(n,Yo(bi));o|=G}return Ro(e,o,t,n,a)})),wi=Mn((function(e,t,n){var o=3;if(n.length){var a=H(n,Yo(wi));o|=G}return Ro(t,o,e,n,a)})),ki=Mn((function(e,t){return xt(e,1,t)})),_i=Mn((function(e,t,n){return xt(e,Ka(t)||0,n)}));Pa.Cache=gt;var Ii=Rs((function(t,n){var o=(n=1==n.length&&Ti(n[0])?l(n[0],C(zo())):l(Ot(n,1),C(zo()))).length;return Mn((function(a){for(var r=-1,s=ss(a.length,o);++r<s;)a[r]=n[r].call(this,a[r]);return e(t,this,a)}))})),Ai=Mn((function(e,t){return Ro(e,G,B,t,H(t,Yo(Ai)))})),Li=Mn((function(e,t){return Ro(e,q,B,t,H(t,Yo(Li)))})),Ci=Eo((function(e,t){return Ro(e,K,B,B,B,t)})),Si=To(Kt),ji=To((function(e,t){return e>=t})),Di=rn(function(){return arguments}())?rn:function(e){return Fa(e)&&Tr.call(e,"callee")&&!Yr.call(e,"callee")},Ti=hr.isArray,Pi=ln?C(ln):function(e){return Fa(e)&&Vt(e)==we},xi=ts||gr,Ui=un?C(un):function(e){return Fa(e)&&Vt(e)==ie},Ri=cn?C(cn):function(e){return Fa(e)&&Ns(e)==de},Mi=dn?C(dn):function(e){return Fa(e)&&Vt(e)==ge},Hi=pn?C(pn):function(e){return Fa(e)&&Ns(e)==he},Oi=mn?C(mn):function(e){return Fa(e)&&Ea(e.length)&&!!qt[Vt(e)]},$i=To(In),Ei=To((function(e,t){return e<=t})),Ni=fo((function(e,t){if(ta(t)||Ra(t))return po(t,tr(t),e),B;for(var n in t)Tr.call(t,n)&&It(e,n,t[n])})),Fi=fo((function(e,t){po(t,nr(t),e)})),Bi=fo((function(e,t,n,o){po(t,nr(t),e,o)})),Yi=fo((function(e,t,n,o){po(t,tr(t),e,o)})),zi=Eo(jt),Vi=Mn((function(e,t){e=kr(e);var n=-1,o=t.length,a=o>2?t[2]:B;for(a&&Zo(t[0],t[1],a)&&(o=1);++n<o;)for(var r=t[n],s=nr(r),i=-1,l=s.length;++i<l;){var u=s[i],c=e[u];(c===B||Ua(c,Sr[u])&&!Tr.call(e,u))&&(e[u]=r[u])}return e})),Wi=Mn((function(t){return t.push(B,Ho),e(Qi,B,t)})),Gi=Ao((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ur.call(t)),e[t]=n}),lr(ur)),qi=Ao((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ur.call(t)),Tr.call(e,t)?e[t].push(n):e[t]=[n]}),zo),Ji=Mn(on),Ki=fo((function(e,t,n){Sn(e,t,n)})),Qi=fo((function(e,t,n,o){Sn(e,t,n,o)})),Zi=Eo((function(e,t){var n={};if(null==e)return n;var o=!1;t=l(t,(function(t){return t=no(t,e),o||(o=t.length>1),t})),po(e,Fo(e),n),o&&(n=Tt(n,7,Oo));for(var a=t.length;a--;)qn(n,t[a]);return n})),Xi=Eo((function(e,t){return null==e?{}:function(e,t){return Tn(e,t,(function(t,n){return er(e,n)}))}(e,t)})),el=Uo(tr),tl=Uo(nr),nl=yo((function(e,t,n){return t=t.toLowerCase(),e+(n?rr(t):t)})),ol=yo((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),al=yo((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),rl=vo("toLowerCase"),sl=yo((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),il=yo((function(e,t,n){return e+(n?" ":"")+ul(t)})),ll=yo((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),ul=vo("toUpperCase"),cl=Mn((function(t,n){try{return e(t,B,n)}catch(e){return Ha(e)?e:new yr(e)}})),dl=Eo((function(e,t){return n(t,(function(t){t=ca(t),St(e,t,bi(e[t],e))})),e})),pl=_o(),ml=_o(!0),fl=Mn((function(e,t){return function(n){return on(n,e,t)}})),gl=Mn((function(e,t){return function(n){return on(e,n,t)}})),hl=Co(l),vl=Co(a),yl=Co(p),bl=Do(),wl=Do(!0),kl=Lo((function(e,t){return e+t}),0),_l=xo("ceil"),Il=Lo((function(e,t){return e/t}),1),Al=xo("floor"),Ll=Lo((function(e,t){return e*t}),1),Cl=xo("round"),Sl=Lo((function(e,t){return e-t}),0);return Qe.after=function(e,t){if("function"!=typeof t)throw new Ar(Y);return e=qa(e),function(){if(--e<1)return t.apply(this,arguments)}},Qe.ary=ja,Qe.assign=Ni,Qe.assignIn=Fi,Qe.assignInWith=Bi,Qe.assignWith=Yi,Qe.at=zi,Qe.before=Da,Qe.bind=bi,Qe.bindAll=dl,Qe.bindKey=wi,Qe.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ti(e)?e:[e]},Qe.chain=Ia,Qe.chunk=function(e,t,n){t=(n?Zo(e,t,n):t===B)?1:rs(qa(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var a=0,r=0,s=hr(Zr(o/t));a<o;)s[r++]=Nn(e,a,a+=t);return s},Qe.compact=function(e){for(var t=-1,n=null==e?0:e.length,o=0,a=[];++t<n;){var r=e[t];r&&(a[o++]=r)}return a},Qe.concat=function(){var e=arguments.length;if(!e)return[];for(var t=hr(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return u(Ti(n)?co(n):[n],Ot(t,1))},Qe.cond=function(t){var n=null==t?0:t.length,o=zo();return t=n?l(t,(function(e){if("function"!=typeof e[1])throw new Ar(Y);return[o(e[0]),e[1]]})):[],Mn((function(o){for(var a=-1;++a<n;){var r=t[a];if(e(r[0],this,o))return e(r[1],this,o)}}))},Qe.conforms=function(e){return function(e){var t=tr(e);return function(n){return Pt(n,e,t)}}(Tt(e,1))},Qe.constant=lr,Qe.countBy=ci,Qe.create=function(e,t){var n=Ss(e);return null==t?n:Ct(n,t)},Qe.curry=function e(t,n,o){var a=Ro(t,8,B,B,B,B,B,n=o?B:n);return a.placeholder=e.placeholder,a},Qe.curryRight=function e(t,n,o){var a=Ro(t,W,B,B,B,B,B,n=o?B:n);return a.placeholder=e.placeholder,a},Qe.debounce=Ta,Qe.defaults=Vi,Qe.defaultsDeep=Wi,Qe.defer=ki,Qe.delay=_i,Qe.difference=Ws,Qe.differenceBy=Gs,Qe.differenceWith=qs,Qe.drop=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,(t=n||t===B?1:qa(t))<0?0:t,o):[]},Qe.dropRight=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,0,(t=o-(t=n||t===B?1:qa(t)))<0?0:t):[]},Qe.dropRightWhile=function(e,t){return e&&e.length?Kn(e,zo(t,3),!0,!0):[]},Qe.dropWhile=function(e,t){return e&&e.length?Kn(e,zo(t,3),!0):[]},Qe.fill=function(e,t,n,o){var a=null==e?0:e.length;return a?(n&&"number"!=typeof n&&Zo(e,t,n)&&(n=0,o=a),function(e,t,n,o){var a=e.length;for((n=qa(n))<0&&(n=-n>a?0:a+n),(o=o===B||o>a?a:qa(o))<0&&(o+=a),o=n>o?0:Ja(o);n<o;)e[n++]=t;return e}(e,t,n,o)):[]},Qe.filter=function(e,t){return(Ti(e)?r:Ht)(e,zo(t,3))},Qe.flatMap=function(e,t){return Ot(Sa(e,t),1)},Qe.flatMapDeep=function(e,t){return Ot(Sa(e,t),Q)},Qe.flatMapDepth=function(e,t,n){return n=n===B?1:qa(n),Ot(Sa(e,t),n)},Qe.flatten=ha,Qe.flattenDeep=function(e){return null!=e&&e.length?Ot(e,Q):[]},Qe.flattenDepth=function(e,t){return null!=e&&e.length?Ot(e,t=t===B?1:qa(t)):[]},Qe.flip=function(e){return Ro(e,512)},Qe.flow=pl,Qe.flowRight=ml,Qe.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,o={};++t<n;){var a=e[t];o[a[0]]=a[1]}return o},Qe.functions=function(e){return null==e?[]:Bt(e,tr(e))},Qe.functionsIn=function(e){return null==e?[]:Bt(e,nr(e))},Qe.groupBy=mi,Qe.initial=function(e){return null!=e&&e.length?Nn(e,0,-1):[]},Qe.intersection=Js,Qe.intersectionBy=Ks,Qe.intersectionWith=Qs,Qe.invert=Gi,Qe.invertBy=qi,Qe.invokeMap=fi,Qe.iteratee=cr,Qe.keyBy=gi,Qe.keys=tr,Qe.keysIn=nr,Qe.map=Sa,Qe.mapKeys=function(e,t){var n={};return t=zo(t,3),$t(e,(function(e,o,a){St(n,t(e,o,a),e)})),n},Qe.mapValues=function(e,t){var n={};return t=zo(t,3),$t(e,(function(e,o,a){St(n,o,t(e,o,a))})),n},Qe.matches=function(e){return Ln(Tt(e,1))},Qe.matchesProperty=function(e,t){return Cn(e,Tt(t,1))},Qe.memoize=Pa,Qe.merge=Ki,Qe.mergeWith=Qi,Qe.method=fl,Qe.methodOf=gl,Qe.mixin=dr,Qe.negate=xa,Qe.nthArg=function(e){return e=qa(e),Mn((function(t){return jn(t,e)}))},Qe.omit=Zi,Qe.omitBy=function(e,t){return or(e,xa(zo(t)))},Qe.once=function(e){return Da(2,e)},Qe.orderBy=function(e,t,n,o){return null==e?[]:(Ti(t)||(t=null==t?[]:[t]),Ti(n=o?B:n)||(n=null==n?[]:[n]),Dn(e,t,n))},Qe.over=hl,Qe.overArgs=Ii,Qe.overEvery=vl,Qe.overSome=yl,Qe.partial=Ai,Qe.partialRight=Li,Qe.partition=hi,Qe.pick=Xi,Qe.pickBy=or,Qe.property=mr,Qe.propertyOf=function(e){return function(t){return null==e?B:Yt(e,t)}},Qe.pull=Zs,Qe.pullAll=ba,Qe.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Pn(e,t,zo(n,2)):e},Qe.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Pn(e,t,B,n):e},Qe.pullAt=Xs,Qe.range=bl,Qe.rangeRight=wl,Qe.rearg=Ci,Qe.reject=function(e,t){return(Ti(e)?r:Ht)(e,xa(zo(t,3)))},Qe.remove=function(e,t){var n=[];if(!e||!e.length)return n;var o=-1,a=[],r=e.length;for(t=zo(t,3);++o<r;){var s=e[o];t(s,o,e)&&(n.push(s),a.push(o))}return xn(e,a),n},Qe.rest=function(e,t){if("function"!=typeof e)throw new Ar(Y);return Mn(e,t=t===B?t:qa(t))},Qe.reverse=wa,Qe.sampleSize=function(e,t,n){return t=(n?Zo(e,t,n):t===B)?1:qa(t),(Ti(e)?wt:On)(e,t)},Qe.set=function(e,t,n){return null==e?e:$n(e,t,n)},Qe.setWith=function(e,t,n,o){return o="function"==typeof o?o:B,null==e?e:$n(e,t,n,o)},Qe.shuffle=function(e){return(Ti(e)?kt:En)(e)},Qe.slice=function(e,t,n){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Zo(e,t,n)?(t=0,n=o):(t=null==t?0:qa(t),n=n===B?o:qa(n)),Nn(e,t,n)):[]},Qe.sortBy=vi,Qe.sortedUniq=function(e){return e&&e.length?zn(e):[]},Qe.sortedUniqBy=function(e,t){return e&&e.length?zn(e,zo(t,2)):[]},Qe.split=function(e,t,n){return n&&"number"!=typeof n&&Zo(e,t,n)&&(t=n=B),(n=n===B?ee:n>>>0)?(e=Za(e))&&("string"==typeof t||null!=t&&!Mi(t))&&(!(t=Wn(t))&&x(e))?oo(E(e),0,n):e.split(t,n):[]},Qe.spread=function(t,n){if("function"!=typeof t)throw new Ar(Y);return n=null==n?0:rs(qa(n),0),Mn((function(o){var a=o[n],r=oo(o,0,n);return a&&u(r,a),e(t,this,r)}))},Qe.tail=function(e){var t=null==e?0:e.length;return t?Nn(e,1,t):[]},Qe.take=function(e,t,n){return e&&e.length?Nn(e,0,(t=n||t===B?1:qa(t))<0?0:t):[]},Qe.takeRight=function(e,t,n){var o=null==e?0:e.length;return o?Nn(e,(t=o-(t=n||t===B?1:qa(t)))<0?0:t,o):[]},Qe.takeRightWhile=function(e,t){return e&&e.length?Kn(e,zo(t,3),!1,!0):[]},Qe.takeWhile=function(e,t){return e&&e.length?Kn(e,zo(t,3)):[]},Qe.tap=function(e,t){return t(e),e},Qe.throttle=function(e,t,n){var o=!0,a=!0;if("function"!=typeof e)throw new Ar(Y);return Na(n)&&(o="leading"in n?!!n.leading:o,a="trailing"in n?!!n.trailing:a),Ta(e,t,{leading:o,maxWait:t,trailing:a})},Qe.thru=Aa,Qe.toArray=Wa,Qe.toPairs=el,Qe.toPairsIn=tl,Qe.toPath=function(e){return Ti(e)?l(e,ca):Va(e)?[e]:co(Vs(Za(e)))},Qe.toPlainObject=Qa,Qe.transform=function(e,t,o){var a=Ti(e),r=a||xi(e)||Oi(e);if(t=zo(t,4),null==o){var s=e&&e.constructor;o=r?a?new s:[]:Na(e)&&Oa(s)?Ss(Fr(e)):{}}return(r?n:$t)(e,(function(e,n,a){return t(o,e,n,a)})),o},Qe.unary=function(e){return ja(e,1)},Qe.union=ei,Qe.unionBy=ti,Qe.unionWith=ni,Qe.uniq=function(e){return e&&e.length?Gn(e):[]},Qe.uniqBy=function(e,t){return e&&e.length?Gn(e,zo(t,2)):[]},Qe.uniqWith=function(e,t){return t="function"==typeof t?t:B,e&&e.length?Gn(e,B,t):[]},Qe.unset=function(e,t){return null==e||qn(e,t)},Qe.unzip=ka,Qe.unzipWith=_a,Qe.update=function(e,t,n){return null==e?e:Jn(e,t,to(n))},Qe.updateWith=function(e,t,n,o){return o="function"==typeof o?o:B,null==e?e:Jn(e,t,to(n),o)},Qe.values=ar,Qe.valuesIn=function(e){return null==e?[]:S(e,nr(e))},Qe.without=oi,Qe.words=ir,Qe.wrap=function(e,t){return Ai(to(t),e)},Qe.xor=ai,Qe.xorBy=ri,Qe.xorWith=si,Qe.zip=ii,Qe.zipObject=function(e,t){return Xn(e||[],t||[],It)},Qe.zipObjectDeep=function(e,t){return Xn(e||[],t||[],$n)},Qe.zipWith=li,Qe.entries=el,Qe.entriesIn=tl,Qe.extend=Fi,Qe.extendWith=Bi,dr(Qe,Qe),Qe.add=kl,Qe.attempt=cl,Qe.camelCase=nl,Qe.capitalize=rr,Qe.ceil=_l,Qe.clamp=function(e,t,n){return n===B&&(n=t,t=B),n!==B&&(n=(n=Ka(n))==n?n:0),t!==B&&(t=(t=Ka(t))==t?t:0),Dt(Ka(e),t,n)},Qe.clone=function(e){return Tt(e,4)},Qe.cloneDeep=function(e){return Tt(e,5)},Qe.cloneDeepWith=function(e,t){return Tt(e,5,t="function"==typeof t?t:B)},Qe.cloneWith=function(e,t){return Tt(e,4,t="function"==typeof t?t:B)},Qe.conformsTo=function(e,t){return null==t||Pt(e,t,tr(t))},Qe.deburr=sr,Qe.defaultTo=function(e,t){return null==e||e!=e?t:e},Qe.divide=Il,Qe.endsWith=function(e,t,n){e=Za(e),t=Wn(t);var o=e.length,a=n=n===B?o:Dt(qa(n),0,o);return(n-=t.length)>=0&&e.slice(n,a)==t},Qe.eq=Ua,Qe.escape=function(e){return(e=Za(e))&&Oe.test(e)?e.replace(Me,hn):e},Qe.escapeRegExp=function(e){return(e=Za(e))&&Ve.test(e)?e.replace(ze,"\\$&"):e},Qe.every=function(e,t,n){var o=Ti(e)?a:Rt;return n&&Zo(e,t,n)&&(t=B),o(e,zo(t,3))},Qe.find=di,Qe.findIndex=fa,Qe.findKey=function(e,t){return f(e,zo(t,3),$t)},Qe.findLast=pi,Qe.findLastIndex=ga,Qe.findLastKey=function(e,t){return f(e,zo(t,3),Et)},Qe.floor=Al,Qe.forEach=La,Qe.forEachRight=Ca,Qe.forIn=function(e,t){return null==e?e:Ts(e,zo(t,3),nr)},Qe.forInRight=function(e,t){return null==e?e:Ps(e,zo(t,3),nr)},Qe.forOwn=function(e,t){return e&&$t(e,zo(t,3))},Qe.forOwnRight=function(e,t){return e&&Et(e,zo(t,3))},Qe.get=Xa,Qe.gt=Si,Qe.gte=ji,Qe.has=function(e,t){return null!=e&&qo(e,t,Xt)},Qe.hasIn=er,Qe.head=va,Qe.identity=ur,Qe.includes=function(e,t,n,o){e=Ra(e)?e:ar(e),n=n&&!o?qa(n):0;var a=e.length;return n<0&&(n=rs(a+n,0)),za(e)?n<=a&&e.indexOf(t,n)>-1:!!a&&h(e,t,n)>-1},Qe.indexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=rs(o+a,0)),h(e,t,a)},Qe.inRange=function(e,t,n){return t=Ga(t),n===B?(n=t,t=0):n=Ga(n),function(e,t,n){return e>=ss(t,n)&&e<rs(t,n)}(e=Ka(e),t,n)},Qe.invoke=Ji,Qe.isArguments=Di,Qe.isArray=Ti,Qe.isArrayBuffer=Pi,Qe.isArrayLike=Ra,Qe.isArrayLikeObject=Ma,Qe.isBoolean=function(e){return!0===e||!1===e||Fa(e)&&Vt(e)==se},Qe.isBuffer=xi,Qe.isDate=Ui,Qe.isElement=function(e){return Fa(e)&&1===e.nodeType&&!Ya(e)},Qe.isEmpty=function(e){if(null==e)return!0;if(Ra(e)&&(Ti(e)||"string"==typeof e||"function"==typeof e.splice||xi(e)||Oi(e)||Di(e)))return!e.length;var t=Ns(e);if(t==de||t==he)return!e.size;if(ta(e))return!kn(e).length;for(var n in e)if(Tr.call(e,n))return!1;return!0},Qe.isEqual=function(e,t){return sn(e,t)},Qe.isEqualWith=function(e,t,n){var o=(n="function"==typeof n?n:B)?n(e,t):B;return o===B?sn(e,t,B,n):!!o},Qe.isError=Ha,Qe.isFinite=function(e){return"number"==typeof e&&ns(e)},Qe.isFunction=Oa,Qe.isInteger=$a,Qe.isLength=Ea,Qe.isMap=Ri,Qe.isMatch=function(e,t){return e===t||fn(e,t,Wo(t))},Qe.isMatchWith=function(e,t,n){return n="function"==typeof n?n:B,fn(e,t,Wo(t),n)},Qe.isNaN=function(e){return Ba(e)&&e!=+e},Qe.isNative=function(e){if(Fs(e))throw new yr("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(e)},Qe.isNil=function(e){return null==e},Qe.isNull=function(e){return null===e},Qe.isNumber=Ba,Qe.isObject=Na,Qe.isObjectLike=Fa,Qe.isPlainObject=Ya,Qe.isRegExp=Mi,Qe.isSafeInteger=function(e){return $a(e)&&e>=-Z&&e<=Z},Qe.isSet=Hi,Qe.isString=za,Qe.isSymbol=Va,Qe.isTypedArray=Oi,Qe.isUndefined=function(e){return e===B},Qe.isWeakMap=function(e){return Fa(e)&&Ns(e)==be},Qe.isWeakSet=function(e){return Fa(e)&&"[object WeakSet]"==Vt(e)},Qe.join=function(e,t){return null==e?"":os.call(e,t)},Qe.kebabCase=ol,Qe.last=ya,Qe.lastIndexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o;return n!==B&&(a=(a=qa(n))<0?rs(o+a,0):ss(a,o-1)),t==t?function(e,t,n){for(var o=n+1;o--;)if(e[o]===t)return o;return o}(e,t,a):g(e,y,a,!0)},Qe.lowerCase=al,Qe.lowerFirst=rl,Qe.lt=$i,Qe.lte=Ei,Qe.max=function(e){return e&&e.length?Mt(e,ur,Kt):B},Qe.maxBy=function(e,t){return e&&e.length?Mt(e,zo(t,2),Kt):B},Qe.mean=function(e){return b(e,ur)},Qe.meanBy=function(e,t){return b(e,zo(t,2))},Qe.min=function(e){return e&&e.length?Mt(e,ur,In):B},Qe.minBy=function(e,t){return e&&e.length?Mt(e,zo(t,2),In):B},Qe.stubArray=fr,Qe.stubFalse=gr,Qe.stubObject=function(){return{}},Qe.stubString=function(){return""},Qe.stubTrue=function(){return!0},Qe.multiply=Ll,Qe.nth=function(e,t){return e&&e.length?jn(e,qa(t)):B},Qe.noConflict=function(){return tn._===this&&(tn._=Mr),this},Qe.noop=pr,Qe.now=yi,Qe.pad=function(e,t,n){e=Za(e);var o=(t=qa(t))?$(e):0;if(!t||o>=t)return e;var a=(t-o)/2;return So(Xr(a),n)+e+So(Zr(a),n)},Qe.padEnd=function(e,t,n){e=Za(e);var o=(t=qa(t))?$(e):0;return t&&o<t?e+So(t-o,n):e},Qe.padStart=function(e,t,n){e=Za(e);var o=(t=qa(t))?$(e):0;return t&&o<t?So(t-o,n)+e:e},Qe.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),ls(Za(e).replace(We,""),t||0)},Qe.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Zo(e,t,n)&&(t=n=B),n===B&&("boolean"==typeof t?(n=t,t=B):"boolean"==typeof e&&(n=e,e=B)),e===B&&t===B?(e=0,t=1):(e=Ga(e),t===B?(t=e,e=0):t=Ga(t)),e>t){var o=e;e=t,t=o}if(n||e%1||t%1){var a=us();return ss(e+a*(t-e+Qt("1e-"+((a+"").length-1))),t)}return Un(e,t)},Qe.reduce=function(e,t,n){var o=Ti(e)?c:_,a=arguments.length<3;return o(e,zo(t,4),n,a,js)},Qe.reduceRight=function(e,t,n){var o=Ti(e)?d:_,a=arguments.length<3;return o(e,zo(t,4),n,a,Ds)},Qe.repeat=function(e,t,n){return t=(n?Zo(e,t,n):t===B)?1:qa(t),Rn(Za(e),t)},Qe.replace=function(){var e=arguments,t=Za(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Qe.result=function(e,t,n){var o=-1,a=(t=no(t,e)).length;for(a||(a=1,e=B);++o<a;){var r=null==e?B:e[ca(t[o])];r===B&&(o=a,r=n),e=Oa(r)?r.call(e):r}return e},Qe.round=Cl,Qe.runInContext=k,Qe.sample=function(e){return(Ti(e)?bt:Hn)(e)},Qe.size=function(e){if(null==e)return 0;if(Ra(e))return za(e)?$(e):e.length;var t=Ns(e);return t==de||t==he?e.size:kn(e).length},Qe.snakeCase=sl,Qe.some=function(e,t,n){var o=Ti(e)?p:Fn;return n&&Zo(e,t,n)&&(t=B),o(e,zo(t,3))},Qe.sortedIndex=function(e,t){return Bn(e,t)},Qe.sortedIndexBy=function(e,t,n){return Yn(e,t,zo(n,2))},Qe.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var o=Bn(e,t);if(o<n&&Ua(e[o],t))return o}return-1},Qe.sortedLastIndex=function(e,t){return Bn(e,t,!0)},Qe.sortedLastIndexBy=function(e,t,n){return Yn(e,t,zo(n,2),!0)},Qe.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Bn(e,t,!0)-1;if(Ua(e[n],t))return n}return-1},Qe.startCase=il,Qe.startsWith=function(e,t,n){return e=Za(e),n=null==n?0:Dt(qa(n),0,e.length),t=Wn(t),e.slice(n,n+t.length)==t},Qe.subtract=Sl,Qe.sum=function(e){return e&&e.length?I(e,ur):0},Qe.sumBy=function(e,t){return e&&e.length?I(e,zo(t,2)):0},Qe.template=function(e,t,n){var o=Qe.templateSettings;n&&Zo(e,t,n)&&(t=B),e=Za(e),t=Bi({},t,o,Mo);var a,r,s=Bi({},t.imports,o.imports,Mo),i=tr(s),l=S(s,i),u=0,c=t.interpolate||lt,d="__p += '",p=_r((t.escape||lt).source+"|"+c.source+"|"+(c===Ne?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),m="//# sourceURL="+(Tr.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Gt+"]")+"\n";e.replace(p,(function(t,n,o,s,i,l){return o||(o=s),d+=e.slice(u,l).replace(ut,P),n&&(a=!0,d+="' +\n__e("+n+") +\n'"),i&&(r=!0,d+="';\n"+i+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),u=l+t.length,t})),d+="';\n";var f=Tr.call(t,"variable")&&t.variable;if(f){if(Ze.test(f))throw new yr("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(r?d.replace(Pe,""):d).replace(xe,"$1").replace(Ue,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(r?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var g=cl((function(){return br(i,m+"return "+d).apply(B,l)}));if(g.source=d,Ha(g))throw g;return g},Qe.times=function(e,t){if((e=qa(e))<1||e>Z)return[];var n=ee,o=ss(e,ee);t=zo(t),e-=ee;for(var a=A(o,t);++n<e;)t(n);return a},Qe.toFinite=Ga,Qe.toInteger=qa,Qe.toLength=Ja,Qe.toLower=function(e){return Za(e).toLowerCase()},Qe.toNumber=Ka,Qe.toSafeInteger=function(e){return e?Dt(qa(e),-Z,Z):0===e?e:0},Qe.toString=Za,Qe.toUpper=function(e){return Za(e).toUpperCase()},Qe.trim=function(e,t,n){if((e=Za(e))&&(n||t===B))return L(e);if(!e||!(t=Wn(t)))return e;var o=E(e),a=E(t);return oo(o,D(o,a),T(o,a)+1).join("")},Qe.trimEnd=function(e,t,n){if((e=Za(e))&&(n||t===B))return e.slice(0,N(e)+1);if(!e||!(t=Wn(t)))return e;var o=E(e);return oo(o,0,T(o,E(t))+1).join("")},Qe.trimStart=function(e,t,n){if((e=Za(e))&&(n||t===B))return e.replace(We,"");if(!e||!(t=Wn(t)))return e;var o=E(e);return oo(o,D(o,E(t))).join("")},Qe.truncate=function(e,t){var n=30,o="...";if(Na(t)){var a="separator"in t?t.separator:a;n="length"in t?qa(t.length):n,o="omission"in t?Wn(t.omission):o}var r=(e=Za(e)).length;if(x(e)){var s=E(e);r=s.length}if(n>=r)return e;var i=n-$(o);if(i<1)return o;var l=s?oo(s,0,i).join(""):e.slice(0,i);if(a===B)return l+o;if(s&&(i+=l.length-i),Mi(a)){if(e.slice(i).search(a)){var u,c=l;for(a.global||(a=_r(a.source,Za(tt.exec(a))+"g")),a.lastIndex=0;u=a.exec(c);)var d=u.index;l=l.slice(0,d===B?i:d)}}else if(e.indexOf(Wn(a),i)!=i){var p=l.lastIndexOf(a);p>-1&&(l=l.slice(0,p))}return l+o},Qe.unescape=function(e){return(e=Za(e))&&He.test(e)?e.replace(Re,vn):e},Qe.uniqueId=function(e){var t=++Pr;return Za(e)+t},Qe.upperCase=ll,Qe.upperFirst=ul,Qe.each=La,Qe.eachRight=Ca,Qe.first=va,dr(Qe,function(){var e={};return $t(Qe,(function(t,n){Tr.call(Qe.prototype,n)||(e[n]=t)})),e}(),{chain:!1}),Qe.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Qe[e].placeholder=Qe})),n(["drop","take"],(function(e,t){pt.prototype[e]=function(n){n=n===B?1:rs(qa(n),0);var o=this.__filtered__&&!t?new pt(this):this.clone();return o.__filtered__?o.__takeCount__=ss(n,o.__takeCount__):o.__views__.push({size:ss(n,ee),type:e+(o.__dir__<0?"Right":"")}),o},pt.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),n(["filter","map","takeWhile"],(function(e,t){var n=t+1,o=1==n||3==n;pt.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:zo(e,3),type:n}),t.__filtered__=t.__filtered__||o,t}})),n(["head","last"],(function(e,t){var n="take"+(t?"Right":"");pt.prototype[e]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");pt.prototype[e]=function(){return this.__filtered__?new pt(this):this[n](1)}})),pt.prototype.compact=function(){return this.filter(ur)},pt.prototype.find=function(e){return this.filter(e).head()},pt.prototype.findLast=function(e){return this.reverse().find(e)},pt.prototype.invokeMap=Mn((function(e,t){return"function"==typeof e?new pt(this):this.map((function(n){return on(n,e,t)}))})),pt.prototype.reject=function(e){return this.filter(xa(zo(e)))},pt.prototype.slice=function(e,t){e=qa(e);var n=this;return n.__filtered__&&(e>0||t<0)?new pt(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==B&&(n=(t=qa(t))<0?n.dropRight(-t):n.take(t-e)),n)},pt.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},pt.prototype.toArray=function(){return this.take(ee)},$t(pt.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),a=Qe[o?"take"+("last"==t?"Right":""):t],r=o||/^find/.test(t);a&&(Qe.prototype[t]=function(){var t=this.__wrapped__,s=o?[1]:arguments,i=t instanceof pt,l=s[0],c=i||Ti(t),d=function(e){var t=a.apply(Qe,u([e],s));return o&&p?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(i=c=!1);var p=this.__chain__,m=!!this.__actions__.length,f=r&&!p,g=i&&!m;if(!r&&c){t=g?t:new pt(this);var h=e.apply(t,s);return h.__actions__.push({func:Aa,args:[d],thisArg:B}),new dt(h,p)}return f&&g?e.apply(this,s):(h=this.thru(d),f?o?h.value()[0]:h.value():h)})})),n(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Lr[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);Qe.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var a=this.value();return t.apply(Ti(a)?a:[],e)}return this[n]((function(n){return t.apply(Ti(n)?n:[],e)}))}})),$t(pt.prototype,(function(e,t){var n=Qe[t];if(n){var o=n.name+"";Tr.call(ys,o)||(ys[o]=[]),ys[o].push({name:t,func:n})}})),ys[Io(B,2).name]=[{name:"wrapper",func:B}],pt.prototype.clone=function(){var e=new pt(this.__wrapped__);return e.__actions__=co(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=co(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=co(this.__views__),e},pt.prototype.reverse=function(){if(this.__filtered__){var e=new pt(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},pt.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ti(e),o=t<0,a=n?e.length:0,r=function(e,t,n){for(var o=-1,a=n.length;++o<a;){var r=n[o],s=r.size;switch(r.type){case"drop":e+=s;break;case"dropRight":t-=s;break;case"take":t=ss(t,e+s);break;case"takeRight":e=rs(e,t-s)}}return{start:e,end:t}}(0,a,this.__views__),s=r.start,i=r.end,l=i-s,u=o?i:s-1,c=this.__iteratees__,d=c.length,p=0,m=ss(l,this.__takeCount__);if(!n||!o&&a==l&&m==l)return Qn(e,this.__actions__);var f=[];e:for(;l--&&p<m;){for(var g=-1,h=e[u+=t];++g<d;){var v=c[g],y=v.iteratee,b=v.type,w=y(h);if(2==b)h=w;else if(!w){if(1==b)continue e;break e}}f[p++]=h}return f},Qe.prototype.at=ui,Qe.prototype.chain=function(){return Ia(this)},Qe.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Qe.prototype.next=function(){this.__values__===B&&(this.__values__=Wa(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?B:this.__values__[this.__index__++]}},Qe.prototype.plant=function(e){for(var t,n=this;n instanceof ct;){var o=ma(n);o.__index__=0,o.__values__=B,t?a.__wrapped__=o:t=o;var a=o;n=n.__wrapped__}return a.__wrapped__=e,t},Qe.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof pt){var t=e;return this.__actions__.length&&(t=new pt(this)),(t=t.reverse()).__actions__.push({func:Aa,args:[wa],thisArg:B}),new dt(t,this.__chain__)}return this.thru(wa)},Qe.prototype.toJSON=Qe.prototype.valueOf=Qe.prototype.value=function(){return Qn(this.__wrapped__,this.__actions__)},Qe.prototype.first=Qe.prototype.head,Wr&&(Qe.prototype[Wr]=function(){return this}),Qe}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define((function(){return yn}))):on?((on.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid">\n                <yuno-header-v2 @userInfo="onUserInfo" @isMini="onMini" v-if="loginStatus && hasPageHeader"></yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}});const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":"https://local.yunolearning.com"===this.host()?"https://dev.yunolearning.com":this.host()},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let n="";void 0!==t&&(n="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+n},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+n},studentResultsAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+n},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+n+"/"+o+"/"+a+"/"+r},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+n+"/"+o},classAPi:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classesByViewAPi:function(e,t,n,a,r,s,i,l,u,c){let d="",p="",m="";c&&(d="?ver="+o()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==s&&!1!==s&&(m="/"+s);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+n+"/"+a+"/"+r+m+"/"+i+"/"+l+"/"+u+d},groupsAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+n+"/"+o},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,n,o,a){let r="";!1!==a&&(r="?filter="+a);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+n+"/"+o+r},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+a},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentsList:function(e,t,n,o,a,r,s,i,l,u,c,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+u+"/"+m+"/"+c+"/"+d},paymentsList:function(e,t,n,a,r,s,i,l,u,c){let d="";c&&(d="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+u+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let n="",o="";void 0!==t&&!1!==t&&(o="/"+t),void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+o},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/status/toggle"},changeBatchAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+n+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+a},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+n+"/"+o},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,n,o,a,r,s,i,l){let u="";void 0!==s&&!1!==s&&(u="/"+s);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+u+"/"+i+"/"+l},mapCoursesAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+n},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},deleteResourceAttachmentAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+n},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},videoListAPI:function(e,t){let n="",o="";void 0!==e&&(n=e),void 0===o&&!1===o||(o=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,n,o,a){let r="";if(!1!==t)r=t;else{let t="";void 0!==n&&(t=n),r=e+"/"+t+"/"+o+"/"+a}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+r},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+n},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,n,a,r,s,i){let l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==a&&!1!==a&&(d="/"+a),void 0!==r&&!1!==r&&(p="/"+r),void 0!==s&&!1!==s&&(m="/"+s),void 0!==n&&!1!==n&&(c="/"+n),i&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+t+c+d+p+m+u},courseListAPI:function(e,t,n,a){let r="",s="",i="";a&&(r="?ver="+o()),void 0!==t&&!1!==t&&(s="/"+t),void 0!==n&&!1!==n&&(i="/"+n);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+s+i+r},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+n},learnerListAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+n},instructorAvailabilityAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+r+a},createUpdateAvailabilityAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+n},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+n},instructorsByCategoryAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+n},capabilitiesAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+n},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+n},signInURLWithState(e){const t=["email","profile"],n=encodeURI(JSON.stringify(e));let o="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",n);o=e.toString()}else o="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return o},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+a},eventDetailAPI:function(e,t,n,a,r){let s="";r&&(s="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+n+"?uuid="+a+s},profileDetailAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+a},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},resourcesDetailAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o},videoTestimonialAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n},createExamResultAPI:function(e,t,n,o){let a="",r="";void 0===o&&!1===o&&(o="v1"),void 0!==t&&!1!==t&&(a="/"+t),void 0!==n&&!1!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/"+o+"/examresult/"+e+a+r},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,n){let o="",a="";void 0!==t&&(o="/"+t),void 0!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o+a},videotestimonialListAPI:function(e,t,n,o){let a="",r="";void 0!==n&&(a="/"+n),void 0!==o&&(r="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+a+r},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,n,o,a,r,s){let i="",l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(u="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==n&&!1!==n&&(c="/"+n),void 0!==o&&!1!==o&&(d="/"+o),void 0!==a&&!1!==a&&(p="/"+a),void 0!==r&&!1!==r&&(i="/"+r),void 0!==s&&!1!==s&&(l="/"+s);return this.pickHost()+"/wp-json/yuno/v1/article"+u+m+c+d+p+i+l},webinarSingleAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+a},webinarListingAPI:function(e,t,n,a,r){let s="";r&&(s="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+a+s},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+o},managelearningContentAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+n},learningContentAPI:function(e,t,n,o,a){let r="",s="",i="",l="";void 0!==t&&!1!==t&&(r="/"+t),void 0!==n&&!1!==n&&(s="/"+n),void 0!==o&&!1!==o&&(i="/"+o),void 0!==a&&!1!==a&&(l="/"+a);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+r+s+i+l},learnerInsightsAPI:function(e,t,n,a,r,s,i,l,u){let c="";u&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+c},learnerInsightsClassAPI:function(e,t,n,a){let r="",s="";s=void 0!==n&&!1!==n?"v2":"v1",a&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/"+s+"/learner/class/"+e+"/"+t+r},signupFormAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+r+a},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,n,a,r){let s="";r&&(s="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+n+"/"+a+s},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,n,o){let a="";!1!==o&&(a="?filters="+encodeURI(JSON.stringify(o)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+n+"/"+a},resourcesResultsAPI:function(e,t,n,o,a,r,s){let i="";!1!==r&&(i="?filters="+encodeURI(JSON.stringify(r))),void 0!==s&&!1!==s||(s="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+s+"/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+i},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+a},instructorInsightsAPI:function(e,t,n,a,r,s,i,l){let u="";l&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+u},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+n},dashboardListAPI:function(e,t,n,o,a){let r="",s="",i="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==t&&!1!==t&&(i="/"+t),void 0!==o&&!1!==o&&(r="/"+o),void 0!==a&&!1!==a&&(s="/"+a);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+i+l+r+s},enrollmentDashboardAPI:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+n+"/"+o+"/"+a},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+n},publishedBlogsAPI:function(e,t,n,o,a,r){let s="",i="",l="",u="",c="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(l="/"+t),void 0!==n&&!1!==n&&(u="/"+n),void 0!==o&&!1!==o&&(c="/"+o),void 0!==a&&!1!==a&&(s="/"+a),void 0!==r&&!1!==r&&(i="/"+r);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+u+c+s+i},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,n,o,a,r){r=void 0!==r&&!1!==r?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"";return this.pickHost()+"/wp-json/yuno/v1/"+r+"/"+e+t+n+o+a},instructorMyCourses:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+n},instructorCourseBatches:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+n+"/batches/"+o+"/"+a+"/"+r},manageBookmarkAPI:function(e,t,n,o,a,r){void 0!==t&&!1!==t||(t="v1"),n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"",r=void 0!==r&&!1!==r?"?filters="+encodeURI(JSON.stringify(r)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+n+o+a+r},availableCourses:function(e,t,n,o){t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+n+o},availableBatches:function(e,t,n,o){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+n},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+n+"/"+o},pastBatchesList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,n,o,a,r,s,i,l,u,c,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+u+"/"+c+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+n+"/"+o+"/"+a},instructorVideotestimonial:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+n},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+n},classReviews:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classReviewsByInstructor:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a},listOfUser:function(e,t,n){let a="";n&&(a="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+a},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,n,o,a,r,s,i,l,u,c){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+u+"/"+c},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n},courseEconomicsSummary:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n+"/"+o},csvList:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+n+"/"+o+"/"+a},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,n,o){void 0===o&&(o="v1");return this.pickHost()+"/wp-json/yuno/"+o+"/referrer/user/"+e+"/"+t+"/"+n},referralReports:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+n+"/"+o},orgToken:function(e,t){let n="";return n="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,n},webhooks:function(e,t,n,o,a,r){let s="";return"grid"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+n+"/"+o+"/"+a+"/"+r:"create"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),s},seo:function(e,t,n,o){let a="";return"status"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+o),a},quiz:function(e,t,n,o){let a="";return void 0!==o&&!1!==o||(o=""),"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+o:"quizgrid"===e?a=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?a=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+n:"delete"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),a},question:function(e,t,n,o,a){let r="";return"questions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+n:"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?r=this.pickHost()+"/wp-json/yuno/v1/question/"+o:"deleteQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"attempt"===e?r=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+o:"attemptQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+a:"questionset"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"questionsetQuestions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+a:"questionsetQuestionsList"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+a:"questionsetQuestionsReorder"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),r},enrollments:function(e,t,n,o){let a="";return"active"===e?a=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+n+"/learners":"extendDate"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),a},blog:function(e,t,n,o){let a="";return"recentSingle"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+o+"?is_list=true&category_id="+t:"categoriesList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),a},writingTask:function(e,t,n,o,a,r,s,i){let l="";return"type"===e?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+n:"create"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o+"/"+a+"/"+r+"/"+s+"/"+i:"payload"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o:"delete"===e&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+o),l},categoryLandingPage:function(e,t){let n="";return"category"===e?n=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+n+"/"+o},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,n,o,a,r,s,i,l,u,c,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${o}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${a}/${r}/${s}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${a}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${i}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${t}/${o}/${a}/${u}/${c}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${n}/${o}/${a}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,n){let o="";return"steps"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+n:"postStep"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+n:"updateMobile"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),o},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,n,o,a,r){let s="";switch(e){case"insights":s=`/wp-json/yuno/v2/users/${t}/learner/${n}/${o}/${a}`;break;case"demoRequests":s=`/wp-json/yuno/v1/demo-requests/${t}/list/${o}/${a}`;break;case"demoRequestsOrg":s=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${n}/${o}/${a}`;break;case"learnerDetailOrg":s=`/wp-json/yuno/v1/demo-requests/${t}/${r}`;break;case"learnerDetail":s=`/wp-json/yuno/v1/demo-requests/${r}`;break;case"instructorLearnerDetail":s=`/wp-json/yuno/v2/instructor/mylearner/${r}`;break;case"orgAdminLearners":s=`/wp-json/yuno/v2/orgadmin/learner/${n}/${o}/${a}`}return this.pickHost()+s},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,n,o,a){let r="";switch(e){case"googleFonts":r=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":r=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":r=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":r=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${n}/${o}`;break;case"orgBatches":r=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${a}`;break;case"org":r=`${this.pickHost()}/wp-json/yuno/v2/org/${o}`;break;case"categories":r=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return r},course:function(e,t,n,o,a,r){let s="";switch(e){case"payload":s=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":s=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${n}/${o}/${a}/${r}`;break;case"createBatchOrg":s=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":s=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return s},learner:function(e,t,n,o,a){let r="";switch(e){case"enrolledCourses":r=`/wp-json/yuno/v3/learner/${t}/enrollments/${n}`;break;case"classes":r=`/wp-json/yuno/v4/classes/${n}/${o}/${t}?limit=${a.limit}&offset=${a.offset}&course=${a.course}&batch=${a.batch}&academy=${a.academy}`;break;case"filters":r=`/wp-json/yuno/v4/classes/filter/${o}/${t}`}return this.pickHost()+r},classes:function(e,t,n,o,a,r,s,i,l,u,c){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${n}/${o}/${a}/${r}/${s}/${i}/${l}/${u}/${c}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,fetchMyLearners:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivateClass:"/wp-json/yuno/v4/classes/private",updatePrivateClass:`/wp-json/yuno/v4/classes/private/${t.classID}`,getClassDetail:`/wp-json/yuno/v4/classes/${t.classID}`,createAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/create`,updateAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/update`,getInstructorAvailability:`/wp-json/yuno/v4/working_hours/instructor/${t.id}`}[e]||"")},attendance:function(e,t,n,o,a){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${n}/${o}/${a}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots"}[e]||"")},classInsights:function(e,t,n,o,a){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${n}/${o}/${a}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${n}/${o}/${a}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",activeOrg:"/wp-json/yuno/v3/user/state",getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${t.id}/0/0/0/active/all/0/0/all/all/0/list-view/100/0`,addDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/add",getDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/${t.id}/${t.org_id}/${t.academy_id}`,updateDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/edit"}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`,detail:`/place/details/json?place_id=${t.placeID}&key=${t.key}`}[e]||"")},createPlace:function(e,t){return this.pickHost()+({create:"/wp-json/yuno/v4/places",createClassroom:"/wp-json/yuno/v4/classrooms"}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules"}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")}},n=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){e(!0)},n.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),o=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,n)=>e.find((e=>e[t]===n))||null,heightOfEle:function(e,t){let n=e.offsetHeight;if(t){let t=getComputedStyle(e);return n+=parseInt(t.marginTop)+parseInt(t.marginBottom),n}return n},assignVValidationObj:function(e){const t=window.VeeValidate,n=window.VeeValidateRules,o=t.ValidationProvider,a=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let o in e.messages)t.extend(o,n[o]);t.localize("validationMsg",e),Vue.component("ValidationProvider",o),Vue.component("ValidationObserver",a)},removeObjInArr:function(e,t,n){let o=e.length;for(;o--;)e[o]&&e[o].hasOwnProperty(t)&&arguments.length>2&&e[o][t]===n&&e.splice(o,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return o[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){n.then((function(t){e(t)}))},setCookie:function(e,t,n){let o=new Date;void 0===n&&(n=30),o.setTime(o.getTime()+24*n*60*60*1e3);let a="expires="+o.toGMTString();document.cookie=e+"="+t+";"+a+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:o,removeValInArr:function(e){let t,n,o=arguments,a=o.length;for(;a>1&&e.length;)for(t=o[--a];-1!==(n=e.indexOf(t));)e.splice(n,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,n){let o=e.match(t);return null!=o&&(!0===n?o[1].replace(/\/$/,""):o[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,n){let o=window.pageYOffset,a=(r=e,window.pageYOffset+document.querySelector(r).getBoundingClientRect().top);var r;targetY=document.body.scrollHeight-a<window.innerHeight?document.body.scrollHeight-window.innerHeight:a,customHeight=void 0!==n?n:74,diff=targetY-o-customHeight;let s="";diff&&window.requestAnimationFrame((function e(n){s||(s=n);let a=n-s,r=Math.min(a/t,1);var i;r=(i=r)<.5?4*i*i*i:(i-1)*(2*i-2)*(2*i-2)+1,window.scrollTo(0,o+diff*r),a<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var n=e.substring(1).split("&"),o=0;o<n.length;o++){var a=n[o].split("=");if(a[0]==t)return a[1]}return!1},cleanTextAndTruncate:function(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n}}}(jQuery),YUNOStore=function(e){const t=function(e,t,n,o,a){if(o){if(void 0===t.addToModule||t.addToModule)if(void 0!==a&&a){for(let t=0;t<n.length;t++)e.data.push(n[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=n}else t.moduleTabs?(e.error=n,e.loading=!1):e.error=n;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[]},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{class_title:"",class_date_time:"",class_duration:"",instructor_id:parseInt(isLoggedIn,10),category_id:0,academy_id:0,learners:[],batch_id:0,course_id:0}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,n){if(n.isError){let o=n.response.response,a=!(void 0===n.pushData||!n.pushData),r=e[n.store],s="";s=void 0!==o&&void 0!==o.data&&void 0!==o.data.message?o.data.message:YUNOCommon.config.errorMsg.common,console.log(s),console.log(n.store),n.tabs?(t(r.tabs[n.tabIndex],n,o,!1,a),r.tabs[n.tabIndex].error=!0,r.tabs[n.tabIndex].errorData=s,n.callback&&n.callbackFunc(r.tabs[n.tabIndex].errorData)):(t(r,n,o,!1,a),r.error=!0,r.errorData=s,n.callback&&n.callbackFunc(r.errorData))}else{let o=n.response.data.data,a=!(void 0===n.pushData||!n.pushData),r=n.response.data,s=e[n.store];n.tabs?(204===r.code&&(s.tabs[n.tabIndex].error=!0,s.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(s.tabs[n.tabIndex].error=!0,s.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),t(s.tabs[n.tabIndex],n,o,!0,a)):(204===r.code&&(s.error=!0,s.errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(s.error=!0,s.errorData=r.message,console.log(r.message),console.log(n.store)),t(s,n,o,!0,a)),n.callback&&n.callbackFunc(n)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,n){if(n.isError){let o=n.response;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!1),n.callback&&n.callbackFunc(e[n.module].errorData)}else{let o=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let o=n.response.data.data;n.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[n.module],n,o,!0),n.isTabAdded=!0,setTimeout((()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let a=n.response.data.data;for(var o=0;o<a.length;o++)a[o].username_email_phone=`${a[o].username_email} (${a[o].phone})`;t(e[n.module],n,a,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let n=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",t.classID),0===n.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let n=e[t.parentModule].data[t.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,n){if(n.isError){let o=n.response;"enrolled"===n.nestedTab?(e[n.module].tabs[n.tabIndex].loading=!1,e[n.module].tabs[n.tabIndex].pageLoading=!1,e[n.module].tabs[n.tabIndex].error=o):t(e[n.module],n,o,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let n=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let o=0;o<n.length;o++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(n[o]);let o=e[t.module].data[t.courseIndex].tabs[t.tabIndex];o.count=t.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount,o.isLoadMore=!1}else{for(let e=0;e<n.length;e++)n[e].isBatches=!1,n[e].isLoading=!1,n[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=n}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let o=t.response.data.data;if(void 0!==t.nested){let a=o.columns,r=o.rows,s={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&a.push(s);for(var n=0;n<r.length;n++)r[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+r[n].id};e[t.nested].tabs[0].data=o,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=o.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const n=function(n){n?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?n(!1):n(!0)},instructorLearners(e,t){const n=function(n){if(n&&void 0!==t.response.data){let n=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=n,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let o={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};e[t.module].tabs[t.index].data.unshift(o),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let n=e.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const a=e[t.module].tabs[t.index];a.hasData=!0,a.isLoadMore=!1;for(var o=0;o<n.length;o++)n[o].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[o].group_created_time)[0]),n[o].scheduleClassURL=`/class-schedule/?groupID=${n[o].group_id}`,a.data.push(n[o]);a.count=t.response.data.count,a.currentCount=a.data.length,a.offset=a.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?n(!1):(n(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)}))},postThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):console.log("not defined")},fetchData({commit:e,state:t},n){let o="",a="";a="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},o=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0===n.moduleLoading||n.moduleLoading?o.loading=!0:o.loading=!1,n.moduleTabs&&n.isTabLoader&&(o.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:a}).then((o=>{n.response=o,n.isError=!1,e(n.module,n),403===(n.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,t[n.store].loading=!1,t[n.store].success=!0;let a="";a=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),403===o.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),n.response=o,n.isError=!0,e(n.module,n)}))},putData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPutData({commit:e,state:t},n){let o={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))},postData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPostData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},deleteData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:o,data:n.payload}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOHeader=(jQuery,{header:function(){Vue.component("yuno-header",{props:["isnav","scrollenabled","logoAlignment","postsignup","hassearchbar","options"],template:' \n                <div>\n                    <header id="yunoHeader" class="yunoHeader" :class="{\'noNav\': isnav === false, \'scrollEnabled\': scrollenabled, \'logoCenter\': logoAlignment}">\n                        <div class="container-fluid noOverflow">\n                            <nav class="navbar navbar-expand-lg" :class="[hassearchbar !== undefined && hassearchbar.isActive ? \'hasSearchBar\' : \'\',]">\n                                <figure class="logo navbar-brand">\n                                    <a :href="isnav !== false ? getHomeURL : \'#\'"><img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                </figure>\n                                \n                                <button v-if="isnav !== false" class="navbar-toggler" type="button" aria-controls="navbarSupportedContent" :aria-expanded="enabledSubmenu" aria-label="Toggle navigation" @click="toggleMenu">\n                                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                </button>\n                                <div v-if="isnav !== undefined && !isnav" class="yunoLogin">\n                                    <div v-if="false" class="yunoCallUs noSpacer" :class="{\'preLogin\': getUserStatus !== true}">\n                                        <a class="whatsapp" href="https://api.whatsapp.com/send?phone=+918847251466" target="_blank"><i class="fa fa-whatsapp" aria-hidden="true"></i></a>\n                                    </div>        \n                                </div>\n                                <div v-if="isnav !== false" class="collapse navbar-collapse yunoMainNav" id="yunoMainNav" :class="[!getUserStatus || getUserStatus && userRole.data === \'Learner\' ? \'hasSearch\' : \'\', userRole.data === \'yuno-admin\' ? \'hasAdmin\' : \'\', enabledSubmenu ? \'show\' : \'collapsed\']">\n                                    <ul class="navbar-nav align-items-center w-100">\n                                        <template v-if="headerMenu.loading" v-for="(menu, menuIndex) in 3">\n                                            <li :style="{ marginLeft: \'30px\' }">\n                                                <b-skeleton width="100px" height="32px" active></b-skeleton>\n                                            </li>\n                                        </template>\n                                        <template v-if="headerMenu.success" v-for="(menu, menuIndex) in headerMenu.data">\n                                            <li class="hasCTA" v-if="detectPlatform() === \'android\'">\n                                                <b-button tag="a"\n                                                    href="https://play.google.com/store/apps/details?id=com.yunolearning.learn"\n                                                    target="_blank"\n                                                    class="yunoPrimaryCTA small">\n                                                    Get the app\n                                                </b-button>\n                                            </li>\n                                            <template v-if="menu.type === \'menu_item\'">\n                                                <li \n                                                    class="nav-item" \n                                                    :class="{\'active\': menu.is_active === true, \'dropdown\': menu.items.length, \'isCTA\': menu.section === \'Invite\'}" \n                                                    :key="menuIndex">\n                                                    <template v-if="menu.items.length">\n                                                        <a \n                                                            :id="\'submenu\' + menuIndex"\n                                                            :class="{\'dropdownToggle\': menu.items.length}" \n                                                            @click="manageSubmenu($event, menu, \'submenu\' + menuIndex)"\n                                                            :ref="\'submenu\' + menuIndex"\n                                                            :href="menu.url">\n                                                            {{menu.section}}\n                                                        </a>\n                                                    </template>\n                                                    <a \n                                                        v-else\n                                                        :class="{\'dropdown-toggle\': menu.items.length}" \n                                                        @click="manageCustomLink($event, menu)"\n                                                        :href="menu.url">\n                                                            {{menu.section}}\n                                                    </a>\n                                                </li>\n                                            </template>\n                                            <template v-if="menu.type === \'search_item\'">\n                                                <li class="hasSearchBar">\n                                                    <div class="searchBarWrapper">\n                                                        <validation-observer \n                                                            tag="div" \n                                                            ref="searchObserver" \n                                                            v-slot="{ handleSubmit, invalid }">\n                                                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                                                <b-field class="searchFieldWrapper">\n                                                                    <validation-provider \n                                                                        tag="div"\n                                                                        class="searchField"\n                                                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                                                        v-slot="{ errors, classes }">\n                                                                        <b-autocomplete\n                                                                            :class="classes"\n                                                                            v-model="categories.current"\n                                                                            :data="categories.data"\n                                                                            autocomplete="courseSearch"\n                                                                            :loading="categories.isLoading"\n                                                                            placeholder="Search..."\n                                                                            @typing="searchOnTyping"\n                                                                            @select="onSelect($event)"\n                                                                            :clearable="true"\n                                                                        >\n                                                                            <template slot-scope="props">\n                                                                                <template v-if="props.option.course_url">\n                                                                                    <div class="suggestion courseBlock">\n                                                                                        <figure>\n                                                                                            <div class="imageWrapper" v-if="false">\n                                                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                                                            </div>\n                                                                                            <figcaption>\n                                                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                                                <p class="courseDetail">\n                                                                                                    <span class="caption">Course</span>\n                                                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                                                </p>\n                                                                                            </figcaption>\n                                                                                        </figure>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                            </template>\n                                                                        </b-autocomplete>\n                                                                    </validation-provider>\n                                                                    <div class="ctaWrapper">\n                                                                        <b-button\n                                                                            native-type="submit"\n                                                                            class="doSearch">\n                                                                            <span class="material-icons-outlined">search</span>\n                                                                        </b-button>  \n                                                                    </div>\n                                                                </b-field>\n                                                            </form>\n                                                        </validation-observer>\n                                                    </div>\n                                                </li> \n                                            </template>\n                                        </template>\n                                    </ul>\n                                </div>\n                                <template v-if="isnav !== false">\n                                    <div class="yunoLogin" v-if="!getUserStatus">\n                                        <a \n                                            class="marginRight15"\n                                            @click="trackLoginLandingPage()"\n                                            href="/login/">\n                                            <span class="yuno-login-with-google-on-pages grey wired">Log in</span>\n                                        </a>\n                                        <a @click="trackLoginLandingPage()" href="/login/?type=signup">\n                                            <span class="yuno-login-with-google-on-pages">Sign up</span>\n                                        </a>\n                                        <div class="yunoCallUs" v-if="false" :class="{\'preLogin\': getUserStatus !== true}">\n                                            <template v-if="false">\n                                                <span class="caption">Call us at</span>\n                                                <a href="tel:+91-8847251466"><span class="material-icons">call</span> <span class="value">+91-8847251466</span></a>\n                                            </template>\n                                            <a href="#" @click.prevent="bookADemo()">Book a demo class</a>\n                                        </div>        \n                                    </div>\n                                </template>\n                                <template v-if="isnav !== false || hassearchbar !== undefined && hassearchbar.isActive">\n                                    <template v-if="getUserStatus">\n                                        <div class="dropdown yunoLoginDropdown" v-if="getUserProfile.success">\n                                            <a class="dropdown-toggle" href="#" role="button" id="userProfile" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                                                <figure class="profilePic">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.first_name">\n                                                </figure>\n                                            </a>\n                                            <div class="dropdown-menu" aria-labelledby="userProfile">\n                                                <figure class="menuHeader">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.yuno_display_name">\n                                                    <figcaption>\n                                                        <p class="userName">{{ getUserProfile.data.yuno_display_name }}</p>\n                                                        <p class="userEmail">{{ getUserProfile.data.email }}</p>\n                                                    </figcaption>\n                                                </figure>\n                                                <ul class="userlinks">\n                                                    <li \n                                                        v-for="(item, l) in usermenu"\n                                                        :key="l"\n                                                        v-if="item.isActive"\n                                                        :class="[item.slug === \'switchAccount\' ? \'linksFooter\' : \'\']">\n                                                        <a \n                                                            v-if="item.callbackFunc !== false" \n                                                            class="dropdown-item" \n                                                            @click="item.callbackFunc" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                        <a \n                                                            v-else \n                                                            class="dropdown-item" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                    </li>\n                                                </ul>\n                                                <div class="additionalItems" v-if="isItemAvailable([\'Instructor\'])">\n                                                    <template v-if="referralCode.loading">\n                                                        <div class="loaderWrapper">\n                                                            <div class="smallLoader"></div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-if="referralCode.success">\n                                                        <template v-if="referralCode.error === null">\n                                                            <div class="item">\n                                                                <b-field label="Referral code">\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="material-icons-outlined">content_copy</span>\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </template>\n                                </template>\n                            </nav>\n                        </div>\n                        <b-modal \n                            :active.sync="invite.modal" \n                            :width="500" \n                            :can-cancel="[\'escape\', \'x\']"\n                            class="yunoModal inviteModal">\n                                <div class="modalHeader">\n                                    <h2 class="modalTitle">Invite Link</h2>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="wrapper">\n                                        <template v-if="inviteLink.loading">\n                                            <div class="loaderWrapper">\n                                                <div class="smallLoader"></div>\n                                            </div>\n                                        </template>\n                                        <template v-if="inviteLink.success">\n                                            <template v-if="inviteLink.error === null">\n                                                <ul class="classFields">\n                                                    <li>\n                                                        <div class="clipboard">\n                                                            <b-input id="inviteLink" :value="inviteLink.data.invitation_link" readonly></b-input>\n                                                            <i @click="copyToClipboard(\'inviteLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n                                                        </div>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else>\n                                                <p>{{inviteLink.error}}</p>\n                                            </template>\n                                        </template>\n                                    </div>\n                                </div>\n                        </b-modal>\n                    </header>\n                    <div class="yunoSubmenu" \n                        :class="[submenu.isActive ? \'active\' : \'inActive\']"\n                        v-if="submenu.isActive && scrollenabled === undefined || submenu.isActive && scrollenabled !== undefined && !scrollenabled" \n                        v-closable="{exclude: [submenu.ref], handler: \'onClose\'}">\n                        <div class="container">\n                            <a href="#" class="closeSubmenu" @click.prevent="closeSubmenu()"><span class="material-icons">arrow_back_ios</span></a>\n                            <ul class="submenuList" :class="[submenu.data.items.length < 3 ? \'col2\' : \'\']">\n                                <li v-for="(nav, i) in submenu.data.items" :key="i">\n                                    <a :href="nav.url">\n                                        <figure>\n                                            <div class="imgWrapper"><img :src="nav.image" :alt="nav.label"></div>\n                                            <figcaption>\n                                                <p class="navLabel">{{ nav.label }}</p>\n                                                <p class="navDes">{{nav.excerpt}}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </a>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    <yuno-choose-account-type>\n                    </yuno-choose-account-type>\n                    <b-modal \n                        :active.sync="bookademo.modal" \n                        :width="388" \n                        :can-cancel="[\'escape\', \'x\']"\n                        :on-cancel="bookademoModalClose"\n                        class="yunoModal lightTheme">\n                            <template v-if="bookademo.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">\n                                        {{ bookademo.title }}\n                                    </h3>\n                                </div>\n                                <div class="modalBody">\n                                    <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observerWrapper"\n                                        ref="bookademoobserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form @submit.prevent="handleSubmit(initBookADemo)">\n                                            <validation-provider \n                                                tag="div" \n                                                class="categoryWithImage" \n                                                :rules="{required:true}" \n                                                v-slot="{ errors, classes }"\n                                            >\n                                                <template v-for="(option, i) in bookademo.data">\n                                                    <div class="fieldWrapper">\n                                                        <div class="inner">\n                                                            <b-field :key="i" :style="categoryImg(option)">\n                                                                <b-radio-button \n                                                                    :class="classes"\n                                                                    v-model="bookademo.selected"\n                                                                    @input="initBookADemo()"\n                                                                    name="bookademo"\n                                                                    :native-value="option">\n                                                                    {{option.label}}\n                                                                </b-radio-button>\n                                                            </b-field>\n                                                            <div class="catLabel">{{option.label}}</div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>\n                                            <div class="ctaWrapper alignLeft" v-if="false">\n                                                <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>\n                                            </div>\n                                        </form>\n                                    </validation-observer>\n                                </div>\n                            </template>\n                    </b-modal>\n                \n                </div>\n            ',data(){return{enabledSubmenu:!1,bookademo:{modal:!1,title:"Book a Demo Class",subtitle:"Choose Subject Category",selected:"",data:[]},showSubmenu:!1,invite:{modal:!1},submenu:{isActive:!1,data:[],ref:""},usermenu:[{label:"Home",slug:"learnerDashboard",icon:"home",iconType:"material-icons-outlined",url:"/learner/",isActive:!1,callbackFunc:!1},{label:"Bookmarks",slug:"myBookmarks",icon:"bookmark_border",iconType:"material-icons-outlined",url:"/my-bookmarks/",isActive:!1,callbackFunc:!1},{label:"Enrolled Courses",slug:"myEnrollments",icon:"local_library",iconType:"material-icons-outlined",url:"/learner-courses/",isActive:!1,callbackFunc:!1},{label:"Subscribed Categories",slug:"categories",icon:"category",iconType:"material-icons-outlined",url:"/subscribed-categories",isActive:!1,callbackFunc:!1},{label:"My Profile",slug:"myProfile",icon:"account_circle",iconType:"material-icons-outlined",url:"",isActive:!1,callbackFunc:!1},{label:"Settings",slug:"settings",icon:"settings",iconType:"material-icons-outlined",url:"/settings/",isActive:!0,callbackFunc:!1},{label:"Switch account",slug:"switchAccount",icon:"swap_horiz",iconType:"material-icons-outlined",url:this.$store.getters.getSwitchAccountURL,isActive:!0,callbackFunc:()=>this.chooseAccountState()},{label:"Logout",slug:"logout",icon:"logout",iconType:"material-icons-outlined",url:"/logout/",isActive:!0,callbackFunc:!1}],errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}},computed:{...Vuex.mapState(["loginWithGoogle","user","userRole","inviteLink","module","referralCode","searchSuggestions","chooseAccountTypeModal"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},getSignInURL(){return this.$store.getters.getSignInURL},switchAccountURL(){return this.$store.getters.getSwitchAccountURL},headerMenu:{get(){return this.$store.state.header}},getUserStatus:{get:()=>0!==Number(isLoggedIn)},getUserProfile:{get(){return this.$store.state.userProfile}}},async created(){this.emitEvents()},mounted(){this.logoGoogleSchema(),this.getUserStatus?this.getState():(["userState","oldUserState","paymentState","demoClassState","skipSignUp","userSignUpPage","userSignUp","noSignupRequired","isWelcomePage","isReferrer","isQuiz"].forEach((e=>localStorage.removeItem(e))),sessionStorage.removeItem("activeUserV1"))},methods:{toggleMenu(){this.enabledSubmenu?this.enabledSubmenu=!1:this.enabledSubmenu=!0},manageCustomLink(e,t){"Teach on Yuno"===t.section&&(e.preventDefault(),this.chooseAccountTypeModal.modal=!0)},isObjectBlank:e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&0===Object.keys(e).length,getQueryParams(e){const t=new URLSearchParams(e.split("?")[1]);let n={};for(const[e,o]of t)n[e]=o;return n},trackLoginLandingPage(){if(!this.user.isLoggedin){let e="",t="";e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",t=this.isObjectBlank(this.getQueryParams(window.location.origin+window.location.pathname+window.location.search))?null:this.getQueryParams(window.location.origin+window.location.pathname+window.location.search);const n={url:window.location.origin+window.location.pathname,pageTitle:document.title,category:e,urlParams:t,zohoMeta:this.$props.options.zohoMeta};sessionStorage.setItem("landingPage",JSON.stringify(n))}},categoryImg:e=>({"background-image":`url(${e.image.replace(/ /g,"%20")})`}),bookademoModalClose(){this.bookademo.selected="",this.bookademo.data=[]},initBookADemo(){window.location.href=this.bookademo.selected.url},bookADemo(){this.bookademo.modal=!0;let e=JSON.parse(JSON.stringify(YUNOCommon.findObjectByKey(this.headerMenu.data,"label","Explore").submenu));e.forEach((e=>{e.slug=e.label.toLowerCase(),"ielts"===e.slug?e.url="/for-ads/ielts/ielts-for-all-v3/":"english speaking"===e.slug?e.url="/for-ads/english-speaking/english-for-all-v3/":"pte"===e.slug?e.url="/for-ads/pte/pte-for-all-v3/":"duolingo"===e.slug?e.url="/for-ads/duolingo-for-all/":"toefl"===e.slug?e.url="/for-ads/toefl-for-all/":"french"===e.slug&&(e.url="/for-ads/french/french-for-all-v3/")})),this.bookademo.data=e},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},initForm(){const e=this.categories.selected,t={filter:"category",id:e.category_id,label:e.category,parent_id:0,slug:e.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",t,n)},onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:o}=e.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(e){e.length>2?(this.categories.isLoading=!0,this.categories.data=[],this.fetchCourseSuggestions(e)):this.categories.data=[]}),700),gotReferralCode(e){void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code&&e.response.data.data},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},emitEvents(){Event.$on("updateHeaderSearch",(()=>{})),Event.$on("checkLoggedInState",(e=>{e&&Event.$on("gotUserRole",((e,t)=>{"Learner"===e&&(this.$store.state.homeURL="/learner"),"Instructor"===e&&this.fetchReferralCode()}))}))},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},logoGoogleSchema(){let e={"@context":"https://schema.org","@type":"Organization",url:"https://www.yunolearning.com/",logo:YUNOCommon.config.host()+"/wp-content/themes/yunolearning-child/assets/images/yuno.jpeg"};this.structuredData(e)},manageLogin(e,t){e.preventDefault(),Event.$emit("manageLogin",e,t)},detectPlatform:()=>/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":!!/Android/i.test(window.navigator.userAgent)&&"android",gotInviteLink(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code)this.inviteLink.data=e.response.data.data;else{const t=void 0!==e.response.data&&void 0!==e.response.data.message?e.response.data.message:YUNOCommon.config.errorMsg.common;this.inviteLink.error=t}},fetchInviteLink(){const e=this,t={apiURL:YUNOCommon.config.getInviteURLAPI(isLoggedIn),module:"gotData",store:"inviteLink",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotInviteLink(t)}};this.$store.dispatch("fetchData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},initInviteModal(e){e.preventDefault(),this.invite.modal=!0,0===this.inviteLink.data.length&&this.fetchInviteLink()},whichPage:()=>window.location.href.indexOf("open")>-1?"open":null,submenuClasses(){const e=document.querySelectorAll(".dropdown-menu.level3");for(let t=0;t<e.length;t++)e[t].classList.remove("show")},closeSubmenu(){this.onClose()},onClose(){this.submenu.isActive=!1,this.submenu.data=[],this.submenu.ref=""},manageSubmenu(e,t,n){e.preventDefault(),this.submenu.isActive=!this.submenu.isActive,this.submenu.data=t,this.submenu.ref=n},manageSubmenuLevel3(e){let t=document.querySelectorAll(".dropdown-menu."+e)[0];this.submenuClasses(),t.classList.toggle("show")},isPageHasCategory(){"undefined"!=typeof assignedCategory&&localStorage.setItem("userSignUpPage",assignedCategory)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},getState(){if(null!==localStorage.getItem("isChooseAccountState"))return["userState","isChooseAccountState"].forEach((e=>localStorage.removeItem(e))),!1}}})}}),YUNOHeaderRevamp=(jQuery,{headerRevamp:function(){let e;Vue.directive("closable",{bind(t,n,o){e=e=>{e.stopPropagation();const{handler:a,exclude:r}=n.value;let s=!1;r.forEach((t=>{if(!s){const n=o.context.$refs[t];s=n[0].contains(e.target)}})),t.contains(e.target)||s||o.context[a]()},document.addEventListener("click",e),document.addEventListener("touchstart",e)},unbind(){document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}}),Vue.component("yuno-header-revamp",{props:["hasnav","hasscrollenabled","haslogoAlignment","postsignup","hassearchbar","fetchAPI","options"],template:'\n            <div class="isSticky">\n                <div>\n                    <section class="appPrompt" v-if="appInfo.platform === \'android\'">\n                        <div class="container">\n                            <ul class="colGrid">\n                                <li class="appIcon">\n                                    <div class="closePrompt">\n                                        <i @click="onPromptClose()" class="fa fa-times" aria-hidden="true"></i>\n                                    </div>\n                                    <figure class="appMedia">\n                                        <div class="iconWrap">\n                                            <img :src="appInfo.icon" :alt="appInfo.shortName">\n                                        </div>\n                                        <figcaption class="appInfo">\n                                            <p class="infoTitle">{{ appInfo.shortName }}</p>\n                                            <p class="infoCaption" v-if="appInfo.hasOS === \'android\'">{{ appInfo.androidCaption }}</p>\n                                        </figcaption>\n                                    </figure>\n                                </li>\n                                <li>\n                                    <b-button tag="a"\n                                        :href="appInfo.hasOS === \'android\' ? appInfo.androidURL : \'\'"\n                                        target="_blank"\n                                        rel="nofollow"\n                                        @click="onPromptView($event)"\n                                        class="yunoPrimaryCTA small">\n                                        Open\n                                    </b-button>\n                                </li>\n                            </ul>\n                        </div>\n                    </section>\n                    <template v-if="userInfo.error">\n                        <header id="yunoHeader" class="yunoHeader">\n                            <div class="container noOverflow">\n                                <nav class="navbar navbar-expand-lg">\n                                    <figure class="logo navbar-brand">\n                                        <a :href="getHomeURL"><img :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                    </figure>\n                                    <button class="navbar-toggler" data-toggle="collapse" data-target="#yunoMainNav" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">\n                                        <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                    </button>\n                                    <div class="collapse navbar-collapse yunoMainNav" id="yunoMainNav">\n                                        <ul class="navbar-nav justify-content-end align-items-center w-100">\n                                            <li class="nav-item">\n                                                <a \n                                                    href="/logout">\n                                                    Logout\n                                                </a>\n                                            </li>\n                                        </ul>\n                                    </div>\n                                </nav>\n                            </div>\n                        </header>\n                        <main id="yunoMain">\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </main>\n                    </template>\n                    <template v-else>\n                        <div class="notificationBar" :class="{ notificationHide: notification.isHidden, notificationShow: !notification.isHidden, notVisibleInDOM: !notification.isVisible }">\n                            <a :href="notification.url" target="_blank">\n                                {{ notification.label }}\n                            </a>\n                            <span class="material-icons-outlined" @click="hideNotificationBar">close</span>\n                        </div>\n                        <yuno-header \n                            :isnav="hasnav !== undefined ? hasnav : undefined" \n                            :logoAlignment="haslogoAlignment !== undefined ? haslogoAlignment : undefined" \n                            :scrollenabled="hasscrollenabled !== undefined ? hasscrollenabled : undefined"\n                            :postsignup="postsignup"\n                            :hassearchbar="hassearchbar"\n                            :options="{zohoMeta: options !== undefined ? options.zohoMeta : \'\'}">\n                        </yuno-header>\n                    </template>\n                    <b-modal \n                        :active.sync="config.unauthorizedModal" \n                        :width="450" \n                        :can-cancel="[\'escape\', \'x\']" \n                        :on-cancel="unauthorizedModalClose"\n                        class="yunoModal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">Session Expired</h2>\n                            </div>\n                            <div class="modalBody">\n                                <div class="wrapper">\n                                    <p>{{sessionExpired}}</p>\n                                </div>\n                            </div>\n                            <div class="modalFooter">\n                                <div class="unauthorizedLogin">\n                                    <a \n                                        @click.prevent="setState()"\n                                        href="#">\n                                        <span class="g_icon"></span>\n                                        <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                    </a>\n                                </div>\n                            </div>\n                    </b-modal>\n                </div>\n                <b-modal \n                    :active.sync="loginModal.modal" \n                    :width="360" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="loginModalClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="loginModal.modal">\n                            <div class="modalHeader">\n                                <h3 class="modalTitle">\n                                    <template v-if="loginModal.active === \'login\'">\n                                        Log in\n                                    </template>\n                                    <template v-else>\n                                        Sign up\n                                    </template>\n                                </h3>\n                            </div>\n                            <div class="modalBody">\n                                <template v-if="loginModal.active === \'login\'">\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <div class="ctaWrapper">\n                                        <button class="googleLogin width70" @click="setState">\n                                            <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                        </button>\n                                    </div>\n                                    <p class="footerCaption">\n                                        {{loginModal[loginModal.active].footer.label}} <a @click="updateLoginState($event, \'signup\')" :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                    </p>\n                                </template>\n                                <template v-else>\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observer"\n                                        ref="commonSignupObserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                            <b-field label="Phone number">\n                                                <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                    <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                    <p class="error">{{errors[0]}}</p>\n                                                </validation-provider>    \n                                            </b-field>\n                                            <div class="ctaWrapper">\n                                                <button class="googleLogin" type="submit">\n                                                    <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                                </button>\n                                            </div>\n                                            <p class="helperCaption">\n                                                {{loginModal[loginModal.active].helper.label}} <a @click="updateLoginState($event, \'login\')" :href="loginModal[loginModal.active].helper.actionURL">{{loginModal[loginModal.active].helper.actionLabel}}</a>\n                                            </p>\n                                            <p class="footerCaption" v-if="loginModal[loginModal.active].footer.isActive">\n                                                {{loginModal[loginModal.active].footer.label}} <a :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                            </p>\n                                        </form>\n                                    </validation-observer>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </div>\n            ',data(){return{tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,appInfo:{shortName:"Yuno Learning",name:"Yuno Learning",icon:this.$store.state.themeURL+"/assets/images/yunoLogo.svg",androidURL:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",hasOS:"android",androidCaption:"FREE - In the Google Play Store",platform:"",daysHidden:1,daysReminder:90},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}},loginModal:{modal:!1,active:"",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",login:{title:"Log into your Yuno Learning account",hasMobile:!1,cta:"Login with Google",footer:{label:"Don't have an account yet?",actionLabel:"Sign Up",actionURL:"#"}},signup:{title:"Sign up to create a free account on Yuno Learning",hasMobile:!0,cta:"Sign up with Google",helper:{label:"Already a user?",actionLabel:"Login",actionURL:"#"},footer:{label:"Are you an instructor?",isActive:!0,actionLabel:"Sign up here",actionURL:"/ielts/become-an-instructor"}}},notification:{isHidden:!1,isVisible:!1,label:"Free summer online workshop for kids and teenagers",url:"/free-summer-workshops-for-kids",hideNotificationDate:""}}},computed:{...Vuex.mapState(["user","userRole","userInfo","userProfile","header","footer","config","apiTokenExpiryTime","apiTokenRefresh"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},emptyStates:()=>({state:"dataNotFound"}),getSignInURL(){return this.$store.getters.getSignInURL}},async created(){this.emitEvents()},mounted(){this.loginStatus()},beforeDestroy(){},methods:{hideNotificationBar(){this.notification.isVisible&&(this.notification.isVisible=!1,this.notification.isHidden=!0)},setSigninProps(){localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const{zohoMeta:e={}}=this.$props.options||{},t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general";this.signIn={...this.signIn,categoryURL:`/${t}`,landing_page:{url:window.location.origin+window.location.pathname,title:document.title},productCode:e.productcode||"",leadStatus:e.leadstatus||"",utmSource:YUNOCommon.getQueryParameter("utm_source"),utmCampaign:YUNOCommon.getQueryParameter("utm_campaign"),utmMedium:YUNOCommon.getQueryParameter("utm_medium"),adGroupID:YUNOCommon.getQueryParameter("adgroupid"),adContent:YUNOCommon.getQueryParameter("ad_content"),utmTerm:YUNOCommon.getQueryParameter("utm_term"),gclid:YUNOCommon.getQueryParameter("gclid"),content:{type:e.content_type||"",id:e.content_id||""}}},isFirefoxPrivate(e){null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},emitEvents(){void 0!==Event.$emit&&(Event.$on("manageLogin",((e,t)=>{this.manageLogin(e,t)})),Event.$on("initLoginModal",((e,t,n)=>{this.manageLogin(e,"signup"),this.loginModal[this.loginModal.active].footer.isActive=!1})),Event.$on("initSignupModal",(e=>{this.manageLogin(e,"signup")})))},updateLoginState(e,t){e.preventDefault(),this.loginModal.active=t},initCommonSignup(){this.setState()},loginModalClose(){setTimeout((()=>{this.loginModal.active=""}),300),localStorage.removeItem("paymentState"),localStorage.removeItem("isQuiz")},manageLogin(e,t){this.loginModal.modal=!0,this.loginModal.active=t},onPromptClose(){const e=this.appInfo.daysHidden;YUNOCommon.setCookie("yunoAppPrompt2",!0,e),this.detectPlatform()},onPromptView(e){this.detectPlatform()},detectPlatform(){if(/iPhone|iPad|iPod/i.test(window.navigator.userAgent))this.appInfo.platform="ios";else if(/Android/i.test(window.navigator.userAgent)){const e=YUNOCommon.getCookie("yunoAppPrompt2");this.appInfo.platform=void 0===e&&"android"}else this.appInfo.platform=!1},unauthorizedModalClose(){window.location.href="/logout"},roleSpecificAPI(e,t){"Learner"===e&&this.detectPlatform(),void 0!==Event.$emit&&Event.$emit("gotUserRole",e,t)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):(this.user.isLoggedin=!1,this.detectPlatform(),void 0===this.$props.hasnav||this.$props.hasnav?(this.fetchPreLoginMenu(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin))},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.userRole.data=t.role,this.userRole.success=!0,this.userProfile.data=t,this.userProfile.success=!0,(void 0===this.$props.hasnav||this.$props.hasnav)&&0!==this.header.data.length&&this.fetchPostLoginMenu(t.role),this.$props.fetchAPI&&this.fetchPostLoginMenu(t.role);const n=localStorage.getItem("skipSignUp"),o=localStorage.getItem("paymentState");if("Instructor"===t.role&&"de-active"===t.account_status&&"/account-disabled/"!==window.location.pathname)return window.location.href=YUNOCommon.config.host()+"/account-disabled",!1;"pending"===t.is_signup_completed&&"/sign-up/"!==window.location.pathname&&"/instructor-sign-up/"!==window.location.pathname&&null===n&&null===o&&setTimeout((()=>{"Instructor"===t.role?window.location.href=YUNOCommon.config.host()+"/instructor-sign-up":window.location.href=YUNOCommon.config.host()+"/sign-up"}),50),this.roleSpecificAPI(t.role,t)}else this.userInfo.error=!0,this.header.success=!0,this.footer.success=!0},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",e)},highlightCurrentPage(e){const t=e.response.data.data.map((e=>({...e,isActive:e.url===window.location.href})));this.header.data=t},gotPostLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotUserMenu"))},fetchPostLoginMenu(e){const t={apiURL:YUNOCommon.config.headerMenuAPIV2(isLoggedIn,e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:this.gotPostLoginMenu};this.$store.dispatch("fetchData",t)},gotPreLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotPreLoginMenu"))},fetchPreLoginMenu(){const e={userID:isLoggedIn,orgID:0},t={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",callback:!0,callbackFunc:this.gotPreLoginMenu};this.$store.dispatch("fetchData",t)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <template v-if="footer.loading">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in 4">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <ul class="linkList">\n                                            <template v-for="(item, j) in 4">\n                                                <li \n                                                    :key="j">\n                                                    <b-skeleton active></b-skeleton>\n                                                </li>\n                                            </template>\n                                        </ul>  \n                                    </div>        \n                                </template>\n                            </div>\n                        </template>\n                        <template v-if="footer.success">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in footer.data.primary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="primary.type === \'withIcon\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :class="item.icon"\n                                                        :key="j">\n                                                        <a rel="nofollow" :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'basic\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'withOrderList\'">\n                                            <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        {{item.label}}\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                            <b-button tag="a"\n                                                :href="primary.cta.url"\n                                                target="_blank"\n                                                rel="nofollow noopener"\n                                                class="yunoSecondaryCTA">\n                                                {{ primary.cta.label }}\n                                            </b-button>\n                                        </template>\n                                        <template v-if="primary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <div class="col-12">\n                                    <div class="spacer"></div>\n                                </div>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(secondary, i) in footer.data.secondary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="secondary.type === \'basic\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="secondary.title !== undefined" class="listTitle">\n                                                    <h3>{{ secondary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in secondary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="secondary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                            <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row">\n                                <div class="col-12">\n                                    <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                        <li class="copy" v-if="false">© {{currentYear}} Yunolearning. All rights reserved.</li>\n                                        <li v-if="isLogo === undefined || isLogo">\n                                            <figure class="logo">\n                                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                            </figure>\n                                        </li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </template>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"916239571825"}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},methods:{}})}});Vue.component("yuno-tabs-v2",{props:{animated:{type:Boolean,default:!1},destroyOnHide:{type:Boolean,default:!1}},template:'\n        <section class="yunoTabsWrapper" :class=[filterResult.tabs.wrapperClass]>\n            <b-tabs \n                class="yunoTabsV5" \n                v-model="filterResult.tabs.activeTab" \n                @input="tabChange" \n                :animated="animated"\n                :destroy-on-hide="destroyOnHide"\n            >\n                <b-tab-item \n                    v-for="(tab, i) in filterResult.tabs.items"\n                    :visible="tab.isVisible"\n                    :key="i"\n                    :label="tab.label">\n                    <slot :name="tab.slug"></slot>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user","filterResult"])},async created(){},destroyed(){},mounted(){},methods:{tabChange(e){this.$emit("tabChange",e)}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach((e=>{e.items.forEach((e=>{e.is_active=n===t(e.url);let o=!1;e.sub_items.forEach((a=>{a.is_active=n===t(a.url),a.is_active&&a.parent_id===e.id&&(o=!0)})),e.is_expended=!!o}))}))},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,o={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",o)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:o}=e;null===sessionStorage.getItem("activeOrg")&&(n?o.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(o[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-academy",{props:{storage:{type:Object,required:!0}},template:'\n          <div class="academyForm">\n              <div class="row">\n                  <div class="col-12 col-md-6">\n                      <template v-if="academy.length > 1">\n                          <b-field label="Academy">\n                              <b-skeleton height="40px" v-if="subform.loading"></b-skeleton>\n                              <b-select\n                                  v-if="subform.success"\n                                  v-model="selectedAcademy"\n                                  placeholder="Select academy"\n                                  @input="dropdownChange($event)"\n                              >\n                                  <option\n                                      v-for="(option, j) in subform.data"\n                                      :key="j"\n                                      :value="option.value"\n                                  >\n                                      {{ option.label }}\n                                  </option>\n                              </b-select>\n                          </b-field>\n                          <template v-if="selectedAcademy">\n                              <template v-if="orgAdmin.loading">\n                                  <div class="field">\n                                      <b-skeleton height="40px"></b-skeleton>\n                                  </div>\n                                  <div class="field">\n                                      <b-skeleton height="100px"></b-skeleton>\n                                  </div>\n                                  <div class="field">\n                                      <b-skeleton height="40px"></b-skeleton>\n                                  </div>\n                                  <div class="field">\n                                      <b-skeleton height="40px"></b-skeleton>\n                                  </div>\n                              </template>\n                              <template v-if="orgAdmin.success">\n                                  <yuno-org-settings-form\n                                      :fields="form.fields"\n                                      :payload="form.payload"\n                                      :defaultTaxonomy="defaultTaxonomy"\n                                      @openModal="openModal"\n                                      @submitForm="submitForm"\n                                  >\n                                  </yuno-org-settings-form>  \n                              </template>\n                          </template>\n                      </template>\n                      <template v-else>\n                          <template v-if="orgAdmin.loading">\n                              <div class="field">\n                                  <b-skeleton height="40px"></b-skeleton>\n                              </div>\n                              <div class="field">\n                                  <b-skeleton height="100px"></b-skeleton>\n                              </div>\n                              <div class="field">\n                                  <b-skeleton height="40px"></b-skeleton>\n                              </div>\n                              <div class="field">\n                                  <b-skeleton height="40px"></b-skeleton>\n                              </div>\n                          </template>\n                          <template v-if="orgAdmin.success">\n                              <yuno-org-settings-form\n                                  :fields="form.fields"\n                                  :payload="form.payload"\n                                  :defaultTaxonomy="defaultTaxonomy"\n                                  @openModal="openModal"\n                                  @submitForm="submitForm"\n                              >\n                              </yuno-org-settings-form>  \n                          </template> \n                      </template>\n                  </div>\n              </div>\n              <b-modal\n                  :active="addDemoInstructors"\n                  :width="500"\n                  class="yunoModal demoClass lightTheme"\n                  :can-cancel="[\'escape\', \'x\']"\n                  @close="closeModal"\n                  :destroy-on-hide="false"\n              >  \n                  <div class="modalTitle">\n                      <h3 class="headline5">Demo Classes</h3>\n                  </div>\n                  <b-tabs \n                        class="yunoTabsV5 demoClass"\n                      v-model="subjectTabs.activeTab"\n                      @input="tabChange"\n                      :animated="false"\n                      :destroy-on-hide="false"\n                  >\n                      <b-tab-item\n                          v-for="(subject, index) in selectedSubjects"\n                          :key="index"\n                          :label="subject.label"\n                      >\n                          <template v-if="module.loading">\n                              <b-skeleton height="30px" width="30%"></b-skeleton>\n                              <b-skeleton height="60px" width="100%"></b-skeleton>\n                          </template>\n                          <template v-if="module.success && module.error === null && !module.loading">\n                              <yuno-add-instructor\n                                  :data="subject"\n                                  :currentTabSlug="currentTabSlug"\n                                  @closeModal="closeModal"\n                                  @submitForm="submitDemoInstructorForm"\n                              >\n                              </yuno-add-instructor>\n                          </template>\n                          <template v-else-if="module.success">\n                              {{ module.errorData }}\n                          </template>\n                      </b-tab-item>\n                  </b-tabs>\n              </b-modal>\n          </div>\n      ',data:()=>({defaultTaxonomy:0,industry:null,selectedAcademy:null,addDemoInstructors:!1,selectedSubjects:[],currentTabSlug:"",subjectTabs:{activeTab:0,wrapperClass:"subjectTbsWrapper",tabs:[]},subjectInstructors:{},filteredInstructors:[],modalDescription:"For each subject category, select the instructors who can deliver demo classes. Only these instructors can schedule and deliver demo classes."}),computed:{...Vuex.mapState(["userInfo","user","filterResult","subform","subform2","form","orgAdmin","config","moduleWithoutTab","module","updateLink","filters"]),academy(){return this.moduleWithoutTab.data.yuno_academy_subscription.academies}},async created(){},destroyed(){},mounted(){this.academy.length>1?this.fetchAcademies():(this.selectedAcademy=this.academy[0],this.manageOrg(this.userInfo.data))},methods:{tabChange(e){this.currentTabSlug=this.subjectTabs.tabs[e].slug,this.filters.payload.category.slug=this.currentTabSlug,this.setInstructorsForCurrentTab()},setupSubjectTab(){this.subjectTabs={activeTab:0,wrapperClass:"subjectTbsWrapper",tabs:this.selectedSubjects}},gotOrgInstructors(e){let t=e.response;if(200===t?.data?.code){let e=t.data.data;e.list_view&&Array.isArray(e.list_view)&&(this.filters.data=e.list_view.filter((e=>e.instructor)).map((e=>({id:e.instructor.id,name:e.instructor.name})))),this.filters.payload={org_admin:{id:isLoggedIn},org:{id:this.activeOrg()},academy:{id:this.selectedAcademy},category:[{slug:this.currentTabSlug||""}],instructors:[]}}},fetchOrgInstructors(){const e={id:isLoggedIn},t={apiURL:YUNOCommon.config.academy("getOrgInstructors",e),module:"gotData",store:"filters",callback:!0,addToModule:!1,callbackFunc:e=>this.gotOrgInstructors(e)};this.dispatchData("fetchData",t)},dropdownChange(e){this.resetModules(),this.manageOrg(this.userInfo.data)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},openModal(){this.addDemoInstructors=!0,this.initializeSelectedSubjects(),this.fetchDemoInstructorDetail()},closeModal(){this.addDemoInstructors=!1},initializeSelectedSubjects(){if(this.form.fields&&this.form.fields.length>0){const e=this.form.fields.find((e=>"category"===e.name));e&&e.selected&&(this.selectedSubjects=e.selected.map((e=>({label:e.name,value:e.slug,slug:e.slug,class:e.class||"",instructors:[]}))),this.selectedSubjects.length>0&&(this.currentTabSlug=this.selectedSubjects[0].slug),this.setupSubjectTab())}},hasSelectedSubjects(){const e=this.form.fields.find((e=>"category"===e.name)),t=this.form.fields.find((e=>"instructors"===e.name));e&&e.selected&&(e.selected.length>0?t.visible=!0:t.visible=!1)},gotAcademies(e){const{code:t,data:n}=e.response?.data||{};200===t&&(this.subform.data=n)},fetchAcademies(){const e={apiURL:YUNOCommon.config.org("orgAcademies",this.activeOrg()),module:"gotData",store:"subform",callback:!0,addToModule:!1,callbackFunc:e=>this.gotAcademies(e)};this.dispatchData("fetchData",e)},fetchData(e,t,n){const o=YUNOCommon.findObjectByKey(this.form.fields,"name",e);o.loading=!0;const a={apiURL:t,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:e=>n(e,o)};this.dispatchData("fetchData",a)},setStorage(e){const t=this.$props.storage,n={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(n))}),100)},resetModules(){this.orgAdmin.data=[],this.orgAdmin.success=!1,this.orgAdmin.error=null},onTabChanged(e){this.setStorage(),this.resetModules()},manageOrg(e){const t=this.activeOrg();t&&(this.setupForm(e,t),this.fetchModules(e,t))},dispatchData(e,t){this.$store.dispatch(e,t)},fetchData(e,t,n){const o=YUNOCommon.findObjectByKey(this.form.fields,"name",e);o.loading=!0;const a={apiURL:t,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:e=>n(e,o)};this.dispatchData("fetchData",a)},setupForm(e,t){const n={isRequired:!1,isDisabled:!1,isLoading:!1},o=[{label:"Name",placeholder:"Enter academy name",type:"text",name:"academy_name",...n},{label:"Descripition",placeholder:"Enter description here...",type:"textarea",name:"description",...n},{label:"Excerpt",placeholder:"Enter excerpt here...",type:"textarea",name:"excerpt",...n},{label:"Upload Logo",placeholder:"File format: JPG, GIF or PNG. Dimensions: 512 x 512 pixels",type:"upload",name:"logo",cta:"Click to upload",...n},{label:"Subjects",placeholder:"Select subjects",type:"multiSelectDropdown",name:"category",options:[],loading:!1,...n,isRequired:!0,selected:[]},{label:"Add or remove demo instructors",type:"addInstructors",class:"addInstructors",visible:!1,name:"instructors"}],a={id:this.academy,org_id:t,academy_name:"",user_id:isLoggedIn,excerpt:"",description:"",logo:null,banner_image:null,action:"publish",category:[],app:null};this.form.fields=o,this.form.payload=a},fetchModules(e,t){this.fetchAcademy(e,t),this.fetchOrgInstructors()},gotOrg(e){const{code:t,data:n}=e.response?.data||{};if(200===t){["logo","banner_image"].forEach((e=>{null!==n[e]&&(n[e]={name:n[e]})})),n.user_id=isLoggedIn,this.orgAdmin.data=n,this.form.payload=n,this.inlineData(["category"])}},fetchAcademy(e,t){const n={apiURL:YUNOCommon.config.org("getAcademy",this.selectedAcademy),module:"gotData",store:"orgAdmin",callback:!0,addToModule:!1,callbackFunc:e=>this.gotOrg(e)};this.dispatchData("fetchData",n)},inlineData(e){e.forEach((e=>{this.fetchInlineDataData(e)}))},fetchInlineDataData(e){if("category"===e)this.fetchSubjects(e)},gotSubjects(e,t){const{code:n,data:o}=e.response?.data||{};t.loading=!1,200===n&&(t.options=o,t.selected=o.filter((e=>this.orgAdmin.data.category.some((t=>t.slug===e.slug)))),Vue.nextTick((()=>{this.hasSelectedSubjects()})))},fetchSubjects(e){this.fetchData(e,YUNOCommon.config.generic("categories"),this.gotSubjects)},gotDemoInstructorDetail(e){const{code:t,data:n}=e.response?.data||{};if(200===t){let e=n.demo_instructors||[];if(this.subjectInstructors={},!e||0===e.length)return console.warn("No demo instructors found"),void this.setInstructorsForCurrentTab();e.forEach((e=>{if(!e)return;const t=Object.keys(e)[0],n=e[t]?.instructor;if(!n)return;let o=[];o=Array.isArray(n)?n.map((e=>e?.id)).filter(Boolean):Object.values(n).map((e=>e?.id)).filter(Boolean),this.subjectInstructors[t]={hasRecord:o.length>0,instructors:o}})),this.setInstructorsForCurrentTab()}},fetchDemoInstructorDetail(){const e={id:this.selectedAcademy,org_id:this.activeOrg(),academy_id:this.selectedAcademy},t={apiURL:YUNOCommon.config.academy("getDemoInstructors",e),module:"gotData",store:"module",callback:!0,addToModule:!1,callbackFunc:e=>this.gotDemoInstructorDetail(e)};this.dispatchData("fetchData",t)},demoInstructorFormPosted(e){this.filters.loading=!1;const t=e?.response?.data;(201===t?.code||t?.message)&&this.showToastMessage(t.message)},submitDemoInstructorForm(){this.filters.loading=!0;let e=this.filters.payload;this.filters.payload.category[0].slug=this.currentTabSlug;let t=this.subjectInstructors[this.currentTabSlug]?.hasRecord||!1?"updateDemoInstructors":"addDemoInstructors";const n={apiURL:YUNOCommon.config.academy(t,!1),module:"gotData",store:"updateLink",payload:e,callback:!0,callbackFunc:e=>this.demoInstructorFormPosted(e)};this.dispatchData("postData",n)},submitForm(e){this.form.isLoading=!0;const t={apiURL:YUNOCommon.config.org("createUpdateAcademy"),module:"gotData",store:"form",payload:e,callback:!0,callbackFunc:e=>this.formPosted(e)};this.dispatchData("postData",t)},formPosted(e){this.form.isLoading=!1;const t=e?.response?.data;(201===t?.code||t?.message)&&this.showToastMessage(t.message)},showToastMessage(e){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:"is-bottom"})},setInstructorsForCurrentTab(){if(this.currentTabSlug&&this.subjectInstructors&&this.filters.data){const e=this.subjectInstructors[this.currentTabSlug]||{instructors:[]},t=e.instructors.map((e=>this.filters.data.find((t=>t.id===e))?{id:e}:null)).filter(Boolean);this.filters.payload.instructors=t,this.filters.payload.category[0].slug=this.currentTabSlug;const n=this.selectedSubjects.find((e=>e.slug===this.currentTabSlug));n&&(n.hasRecord=e.hasRecord||!1)}}}}),Vue.component("yuno-add-instructor",{props:{data:{type:Object,required:!0},currentTabSlug:{type:String,required:!0}},template:'\n    <div class="addInstructor">\n  \t\t<div class="tagInputWrapper">\n\t\t\t<validation-observer \n\t\t\t\ttag="div" \n\t\t\t\tclass="observer"\n\t\t\t\tref="addInstructorFormObserver" \n\t\t\t\tv-slot="{ handleSubmit, invalid }"\n\t\t\t>\n\t\t\t\t<form @submit.prevent="handleSubmit(initDemoForm)">\n\t\t\t\t\t<validation-provider \n\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\tclass="fieldWrapper" \n\t\t\t\t\t\t:rules="{ required: true }" \n\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t>\n\t\t\t\t\t\t<b-field :label="data.label">\n\t\t\t\t\t\t\t<b-taginput\n\t\t\t\t\t\t\t\tv-model="selectedInstructors"\n\t\t\t\t\t\t\t\t:data="filteredInstructors"\n\t\t\t\t\t\t\t\tautocomplete\n\t\t\t\t\t\t\t\topen-on-focus\n\t\t\t\t\t\t\t\tfield="name"\n\t\t\t\t\t\t\t\tplaceholder="Add instructors"\n\t\t\t\t\t\t\t\t@typing="updateSearchText"\n\t\t\t\t\t\t\t\t@select="handleInstructorSelect"\n\t\t\t\t\t\t\t\tclass="demoInstructorTagInput">\n\t\t\t\t\t\t\t</b-taginput>\n\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t</validation-provider>\n\t\t\t\t\t<div class="ctaWrapper">\n\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\tnative-type="reset"\n\t\t\t\t\t\t\t@click="closeModal"\n\t\t\t\t\t\t\tclass="yunoPrimaryCTA wired fat">\n\t\t\t\t\t\t\tCancel\n\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\tnative-type="submit"\n\t\t\t\t\t\t\t:loading="filters.loading"\n\t\t\t\t\t\t\t:disabled="filters.isLoading"\n\t\t\t\t\t\t\tclass="yunoSecondaryCTA fat">\n\t\t\t\t\t\t\tSave\n\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t</div>\n\t\t\t\t</form>\n\t\t\t</validation-observer>\n\t\t</div>\n    </div>\n  ',computed:{...Vuex.mapState(["filters","filterResult"]),filteredInstructors(){return this.filters.data.length>0?this.filters.data.filter((e=>e.name.toString().toLowerCase().includes(this.searchText.toLowerCase()))):this.filters.data},selectedInstructors:{get(){return(Array.isArray(this.filters.payload.instructors)?this.filters.payload.instructors:[]).map((e=>this.filters.data.find((t=>t.id===e.id))||null)).filter(Boolean)},set(e){this.filters.payload.instructors=e.map((e=>({id:e.id})))}}},data:()=>({searchText:""}),methods:{updateSearchText(e){this.searchText=e},closeModal(){this.$emit("closeModal")},handleInstructorSelect(e){if(e&&e.id){Array.isArray(this.filters.payload.instructors)||(this.filters.payload.instructors=[]);this.filters.payload.instructors.some((t=>t.id===e.id))||this.filters.payload.instructors.push({id:e.id})}},initDemoForm(){this.$emit("submitForm")}}}),Vue.component("yuno-categories-accordion",{props:["options"],template:'\n        <div :class="[options.hasWrapperClass !== undefined ? options.hasWrapperClass : \'\']">\n            <b-collapse\n                class="categoryTaxonomy"\n                animation="slide"\n                v-for="(taxonomy, taxonomyIndex) of options.data"\n                :ref="\'category\' + taxonomy.id" \n                :key="taxonomyIndex"\n                :open="options.default == taxonomyIndex"\n                @open="onOpenCategoryTaxonomy(taxonomyIndex, taxonomy.id)">\n                <div\n                    class="collapseHeader field colorGrey"\n                    :class="props.open ? \'menuDown\' : \'menuUp\'"\n                    slot="trigger"\n                    slot-scope="props">\n                    <validation-provider tag="div" class="fieldWrapper" :rules="{required:true}" v-slot="{ errors, classes }">\n                        <b-radio \n                            :class="classes"\n                            :disabled="options.disabled !== undefined ? options.disabled : false"\n                            v-model="options.payload.category.id"\n                            @input="selectLevel1(taxonomy.id)"\n                            :native-value="taxonomy.id">\n                            {{taxonomy.name}}\n                        </b-radio>\n                        <p class="error">{{errors[0]}}</p>\n                        <i class="fa" :class="props.open ? \'fa-caret-down\' : \'fa-caret-up\'" aria-hidden="true"></i>\n                    </validation-provider>\n                </div>\n                <div class="collapseBody" v-if="taxonomy.sub_category !== undefined">\n                    <template v-for="(sub2, sub2Index) in taxonomy.sub_category">\n                        <template v-if="sub2.sub_category !== undefined">\n                            <b-collapse \n                                animation="slide" \n                                :open="false" \n                                :ref="\'subCategory\' + sub2.id" \n                                aria-id="subCategories3">\n                                <div class="trigger" slot="trigger" aria-controls="subCategories3" slot-scope="props">\n                                    <i class="fa" :class="props.open ? \'fa-caret-down\' : \'fa-caret-up\'" aria-hidden="true"></i>\n                                    <div class="field" :key="sub2Index">\n                                        {{sub2.name}}\n                                        <b-checkbox\n                                            v-if="false"\n                                            :disabled="options.disabled !== undefined ? options.disabled : false"\n                                            name="subCategory2"\n                                            :native-value="sub2.id"\n                                            v-model="options.payload.category.sub_category.id">\n                                            {{sub2.name}}\n                                        </b-checkbox>\n                                    </div>\n                                </div>\n                                <div class="sub2Content">\n                                    <template v-for="(sub3, sub3Index) in sub2.sub_category">\n                                        <div class="field" :key="sub3Index">\n                                            <b-checkbox\n                                                name="subCategory3"\n                                                :disabled="options.disabled !== undefined ? options.disabled : false"\n                                                :native-value="sub3"\n                                                v-model="options.payload.category.sub_category.sub_category.id">\n                                                {{sub3.name}}\n                                            </b-checkbox>\n                                        </div>\n                                    </template>\n                                </div>\n                            </b-collapse>  \n                        </template>\n                        <template v-else>\n                            <div class="field" :key="sub2Index">\n                                <b-checkbox\n                                    name="subCategory2"\n                                    :disabled="options.disabled !== undefined ? options.disabled : false"\n                                    :native-value="sub2.id"\n                                    v-model="options.payload.category.sub_category.id">\n                                    {{sub2.name}}\n                                </b-checkbox>\n                            </div>\n                        </template>\n                    </template>\n                </div>\n                <div class="collapseBody" v-else>\n                <i class="fa fa-exclamation-circle" aria-hidden="true"></i> No sub-category found\n                </div>\n            </b-collapse>\n        </div>\n    ',data:()=>({}),computed:{},created(){},mounted(){},methods:{onOpenCategoryTaxonomy(e,t){Event.$emit("onOpenCategoryTaxonomy",e,t)},selectLevel1(e){Event.$emit("selectLevel1",e)}}}),Vue.component("yuno-category-skeleton",{props:["data","options"],template:'\n        <div class="">\n            <b-collapse\n                class="categoryTaxonomy"\n                animation="slide"\n                v-for="(taxonomy, taxonomyIndex) of items"\n                :key="taxonomyIndex"\n                :open="true"\n            >\n                <div\n                    class="collapseHeader field colorGrey"\n                    :class="props.open ? \'menuDown\' : \'menuUp\'"\n                    slot="trigger"\n                    slot-scope="props">\n                    <div class="fieldWrapper">\n                        <div class="b-radio">\n                            <b-skeleton active></b-skeleton>    \n                        </div>\n                    </div>\n                </div>\n                <div class="collapseBody">\n                    <b-skeleton active></b-skeleton>\n                </div>\n            </b-collapse>\n        </div>\n    ',data:()=>({items:1}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{}}),Vue.component("yuno-org-settings-form",{props:{fields:{type:Array,required:!0},payload:{type:Object,required:!0},defaultTaxonomy:{type:Number,required:!0},industryData:{type:Object,required:!1}},template:'\n        <div class="yunoFormWrapper">\n            <validation-observer \n                tag="div" \n                class="observer"\n                ref="orgSettingsFormObserver" \n                v-slot="{ handleSubmit, invalid }"\n            >\n                <form @submit.prevent="handleSubmit(initForm)">\n                    <template v-for="(field, i) in fields">\n                        <template v-if="field.type === \'text\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-input\n                                        :class="classes"\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'textarea\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-input\n                                    type="textarea"\n                                    v-model="payload[field.name]"\n                                    :placeholder="field.placeholder"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-input>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'checkbox\'">\n                            <b-field :key="i">\n                                <b-checkbox \n                                    :value="payload[field.name]" \n                                    v-model="payload[field.name]"\n                                >\n                                    {{ field.label}}\n                                </b-checkbox>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'dropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="dropdownChange($event)"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.value"\n                                            :disabled="option.isDisabled"\n                                        >\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'googleFontFamilydropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.family"\n                                        >\n                                            {{ option.family }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'multiSelectDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <validation-provider \n                                        tag="div" \n                                        class="fieldWrapper" \n                                        :rules="{\n                                            required: field.isRequired\n                                        }" \n                                        v-slot="{ errors, classes }"\n                                    >\n                                        <b-dropdown\n                                            v-model="field.selected"\n                                            :class="classes"\n                                            multiple\n                                            :disabled="field.disabled"\n                                            aria-role="list"\n                                            @change="multiSelectDropdownChange($event, field)"\n                                        >\n                                            <template #trigger>\n                                                <b-button>\n                                                    <template v-if="field.selected.length">\n                                                        <div class="selected">\n                                                            <template v-for="(subOption, k) in field.selected">\n                                                                {{ subOption.name }}{{ k < field.selected.length - 1 ? \', \' : \'\' }}\n                                                            </template>    \n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="placeholder">{{ field.placeholder }}</div>\n                                                    </template>\n                                                    <div class="material-icons">expand_more</div>    \n                                                </b-button>\n                                            </template>\n                                            <b-dropdown-item \n                                                :value="subOption"\n                                                aria-role="listitem"\n                                                v-for="(subOption, k) in field.options"\n                                            >\n                                                <span>{{ subOption.name }}</span>\n                                            </b-dropdown-item>\n                                        </b-dropdown>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'groupDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="industry"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="groupDropdownChange($event, field)"\n                                    >\n                                        <optgroup \n                                            :label="option.label"\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                        >\n                                            <option\n                                                v-for="(subOption, k) in option.sub_industry"\n                                                :key="k"\n                                                :value="subOption"\n                                            >\n                                                {{ subOption.label }}\n                                            </option>\n                                        </optgroup>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'upload\'">\n                            <b-field :label="field.label" :key="i" class="uploadField">\n                                <p class="helper">{{ field.placeholder }}</p>\n                                <b-upload\n                                    v-model="payload[field.name]"\n                                    accept="image/*"\n                                    class="file-label"\n                                >\n                                    <span class="file-cta">\n                                        <span class="material-icons-outlined">file_upload</span>\n                                        <span class="file-label">{{ field.cta }}</span>\n                                    </span>\n                                    <span class="file-name" v-if="payload[field.name]">\n                                        {{ payload[field.name].name }}\n                                    </span>\n                                </b-upload>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'colorpicker\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-colorpicker\n                                    :color-formatter="(color) => color.toString(\'hex\')"\n                                    representation="square"\n                                    @input="updateColor($event, field.name)"\n                                    :value="payload[field.name]"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-colorpicker>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'addInstructors\' && field.visible">\n\t\t\t\t\t\t\t<div :class="field.class" @click="openModal">\n\t\t\t\t\t\t\t\t<span>{{ field.label }}</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</template>\n                    </template>\n                    <div class="ctaWrapper">\n                        <b-button\n                            native-type="reset"\n                            @click="clearForm"\n                            class="yunoPrimaryCTA wired fat">\n                            Cancel\n                        </b-button>    \n                        <b-button\n                            native-type="submit"\n                            :loading="form.isLoading"\n                            :disabled="form.isLoading"\n                            class="yunoSecondaryCTA fat">\n                            Save\n                        </b-button>    \n                    </div>\n                </form>\n            </validation-observer>\n        </div>\n    ',data:()=>({industry:null,defaultPayload:[]}),computed:{...Vuex.mapState(["user","categoryTaxonomy","form"])},watch:{industryData:{handler(e,t){e!==t&&(this.industry=e)},deep:!0}},async created(){},destroyed(){},mounted(){this.init()},methods:{updateColor(e,t){this.form.payload[t]=e.toString("hex")},init(){this.defaultPayload=JSON.parse(JSON.stringify(this.form.payload))},clearForm(){this.$refs.orgSettingsFormObserver.reset(),this.form.payload=JSON.parse(JSON.stringify(this.defaultPayload))},jsonToFormData(e){const t=new FormData;for(let n in e)Array.isArray(e[n])&&e[n].every((e=>"object"==typeof e))?t.append(n,JSON.stringify(e[n])):t.append(n,e[n]);return t},initForm(){this.$emit("submitForm",this.jsonToFormData(this.form.payload))},dropdownChange(e){},groupDropdownChange({parent:e,slug:t},n){"sub_industry"===n.name&&(this.form.payload.industry=e,this.form.payload.sub_industry=t)},multiSelectDropdownChange(e,t){this.form.payload[t.name]=e},openModal(){this.$emit("openModal")}}}),Vue.component("yuno-general",{props:{storage:{type:Object,required:!0}},template:'\n        <div class="row">\n            <div class="col-12 col-md-6">\n                <template v-if="orgAdmin.loading">\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="100px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                </template>\n                <template v-if="orgAdmin.success">\n                    <yuno-org-settings-form\n                        :fields="form.fields"\n                        :payload="form.payload"\n                        :defaultTaxonomy="defaultTaxonomy"\n                        :industryData="industry"\n                        @submitForm="submitForm"\n                    >\n                    </yuno-org-settings-form>  \n                </template>\n            </div>\n        </div>\n    ',data:()=>({defaultTaxonomy:0,industry:null}),computed:{...Vuex.mapState(["userInfo","user","filterResult","subform2","form","orgAdmin","config"])},async created(){},destroyed(){},mounted(){this.manageOrg(this.userInfo.data)},methods:{setStorage(e){const t=this.$props.storage,n={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(n))}),100)},resetModules(){this.orgAdmin.data=[],this.orgAdmin.success=!1,this.orgAdmin.error=null},onTabChanged(e){this.setStorage(),this.resetModules()},showToastMessage(e){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:"is-bottom"})},formPosted(e){this.form.isLoading=!1;const t=e?.response?.data;(201===t?.code||t?.message)&&this.showToastMessage(t.message)},dispatchData(e,t){this.$store.dispatch(e,t)},fetchData(e,t,n){const o=YUNOCommon.findObjectByKey(this.form.fields,"name",e);o.loading=!0;const a={apiURL:t,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:e=>n(e,o)};this.dispatchData("fetchData",a)},submitForm(e){this.form.isLoading=!0;const t={apiURL:YUNOCommon.config.org("detailsUpdate"),module:"gotData",store:"form",payload:e,callback:!0,callbackFunc:e=>this.formPosted(e)};this.dispatchData("postData",t)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},setupForm(e,t){const n={isRequired:!1,isDisabled:!1,isLoading:!1},o=[{label:"Name",placeholder:"Enter company name",type:"text",name:"name",...n},{label:"Descripition",placeholder:"Enter message here...",type:"textarea",name:"description",...n},{label:"Industry",placeholder:"Select industry",type:"groupDropdown",name:"sub_industry",options:[],loading:!1,...n},{label:"Upload Logo",placeholder:"File format: JPG, GIF or PNG. Dimension: 120x120 pixels",type:"upload",name:"logo_image",cta:"Click to upload",...n},{label:"Upload Fav icon",placeholder:"File format: ICO or SVG. Dimension: 32x32 pixels",type:"upload",name:"fav_icon",cta:"Click to upload",...n},{label:"Select a primary color",placeholder:"",type:"colorpicker",name:"primary_color",...n},{label:"Select a background color",placeholder:"",type:"colorpicker",name:"background_color",...n},{label:"Font family",placeholder:"Select font family",type:"googleFontFamilydropdown",name:"font_family",options:[],loading:!1,...n},{label:"Number of employees",placeholder:"Select numbers",type:"dropdown",name:"number_of_employees",options:[{label:"1 (self-employed)",value:"1 (self-employed)"},{label:"2-5",value:"2-5"},{label:"6-20",value:"6-20"},{label:"21-100",value:"21-100"},{label:"101-500",value:"101-500"},{label:"501 or more",value:"501 or more"}],...n}],a={name:"",description:"",org_id:t,user_id:isLoggedIn,number_of_employees:null,industry:"",sub_industry:null,subject_category:null,logo_image:null,fav_icon:null,font_family:null,background_color:"#FFFFFF",primary_color:"#000000"};this.form.fields=o,this.form.payload=a},manageOrg(e){const t=this.activeOrg();t&&(this.setupForm(e,t),this.fetchModules(e,t))},gotOrgIndustry(e,t){const{code:n,data:o}=e.response?.data||{};if(t.loading=!1,200===n){t.options=o;const e=this.orgAdmin.data.industry,n=this.orgAdmin.data.sub_industry;if(""!==e&&null!==n){const t=YUNOCommon.findObjectByKey(o,"slug",e),a=YUNOCommon.findObjectByKey(t.sub_industry,"slug",n);this.industry=a}}},fetchOrgIndustry(e){this.fetchData(e,YUNOCommon.config.org("industries"),this.gotOrgIndustry)},gotOrg(e){const{code:t,data:n}=e.response?.data||{};if(200===t){const e=["logo_image","fav_icon"];["sub_industry","font_family"].forEach((e=>{"null"!==n[e]&&"N/A"!==n[e]&&""!==n[e]||(n[e]=null)})),0===n.number_of_employees&&(n.number_of_employees=null),0===n.subject_category.length?n.subject_category=null:n.subject_category=n.subject_categories,e.forEach((e=>{null!==n[e]&&""!==n[e]&&"N/A"!==n[e]?n[e]={name:n[e]}:n[e]=null})),n.user_id=isLoggedIn,this.orgAdmin.data=n,this.form.payload=n,this.inlineData(["sub_industry","font_family"])}},fetchOrg(e,t){const n={apiURL:YUNOCommon.config.org("settings",t),module:"gotData",store:"orgAdmin",callback:!0,addToModule:!1,callbackFunc:e=>this.gotOrg(e)};this.dispatchData("fetchData",n)},gotGoogleFontFamily(e,t){t.loading=!1,200===e.response.status&&e.response.data&&(t.options=e.response.data.items)},fetchGoogleFontFamily(e){this.fetchData(e,YUNOCommon.config.generic("googleFonts","key="+this.config.googleAPIKey+"&sort=popularity"),this.gotGoogleFontFamily)},fetchInlineDataData(e){switch(e){case"sub_industry":this.fetchOrgIndustry(e);break;case"font_family":this.fetchGoogleFontFamily(e)}},inlineData(e){e.forEach((e=>{this.fetchInlineDataData(e)}))},fetchModules(e,t){this.fetchOrg(e,t)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-org-settings",{template:'\n        <yuno-page-grid\n            :authorizedRoles="authorizedRoles"\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="false"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <template v-if="moduleWithoutTab.loading">\n                        <figure class="infiniteSpinner">\n                            <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                        </figure>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <div class="mainHeader">\n                            <h1>{{ pageHeader.title }}</h1>\n                        </div>\n                        <yuno-tabs-v2\n                            :destroyOnHide="true"\n                            @tabChange="tabChange"\n                        >\n                            <template v-slot:general>\n                                <yuno-general\n                                    @tabChanged="onTabChanged"\n                                    ref="yunoGeneral" \n                                    :storage="storage"\n                                >\n                                </yuno-general>\n                            </template>\n                            <template v-slot:prasar>\n                                <yuno-prasar\n                                    @tabChanged="onTabChanged"\n                                    ref="yunoPrasar" \n                                    :storage="storage"\n                                >\n                                </yuno-prasar>\n                            </template>\n                            <template v-slot:academy>\n                                <yuno-academy\n                                    @tabChanged="onTabChanged"\n                                    ref="yunoAcademy" \n                                    :storage="storage"\n                                >\n                                </yuno-academy>\n                            </template>\n                        </yuno-tabs-v2>\n                    </template>\n                </div>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({apiURL:null,isMiniSidebar:!1,pageHeader:{title:"Org Settings"},authorizedRoles:["org-admin"],storage:{name:"orgSettings",version:1}}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","filterResult","moduleWithoutTab"]),wpThemeURL(){return this.$store.state.themeURL},isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data)}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){let e="";return e=!this.user.isLoggedin||this.userInfo.success,e}}},async created(){},destroyed(){},mounted(){},methods:{setupTabs(e,t,n){function o(e,t,n,o){return{label:e,slug:t,isActive:!1,isVisible:o,class:n}}const a=this.storage,r=sessionStorage.getItem(a.name+"V"+a.version);let s="";s=r?JSON.parse(r).currentTab:0,this.filterResult.tabs={activeTab:s,wrapperClass:e,items:[o("General","general","yunoGeneral",!0),o("Prasar","prasar","yunoPrasar",n),o("Academy","academy","yunoAcademy",t)]}},tabChange(e){setTimeout((()=>{const t=[this.$refs.yunoGeneral,this.$refs.yunoPrasar,this.$refs.yunoAcademy][e];t&&t.onTabChanged(e)}),50)},onTabChanged(){},onUserInfo(e){YUNOCommon.findInArray(this.authorizedRoles,e.role)&&this.fetchOrgInfo()},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotOrgInfo(e){const{response:{data:{code:t,data:n}={}}={}}=e;if(200===t){const e=n.yuno_academy_subscription.academies,t=n.yuno_academy_subscription.plan.prasar;this.setupTabs("tabsAvailable",e.length>0,t)}},fetchOrgInfo(){const e={apiURL:YUNOCommon.config.generic("org",!1,!1,this.activeOrg()),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:this.gotOrgInfo};this.$store.dispatch("fetchData",e)}}}),Vue.component("yuno-prasar",{props:{storage:{type:Object,required:!0}},template:'\n        <div class="row">\n            <div class="col-12 col-md-6">\n                <template v-if="orgAdmin.loading">\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="100px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                    <div class="field">\n                        <b-skeleton height="40px"></b-skeleton>\n                    </div>\n                </template>\n                <template v-if="orgAdmin.success">\n                    <yuno-org-settings-form\n                        :fields="form.fields"\n                        :payload="form.payload"\n                        :defaultTaxonomy="defaultTaxonomy"\n                        @submitForm="submitForm"\n                    >\n                    </yuno-org-settings-form>  \n                </template>\n            </div>\n        </div>\n    ',data:()=>({defaultTaxonomy:0,industry:null}),computed:{...Vuex.mapState(["userInfo","user","filterResult","subform2","form","orgAdmin","config"])},async created(){},destroyed(){},mounted(){this.manageOrg(this.userInfo.data)},methods:{setStorage(e){const t=this.$props.storage,n={currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(n))}),100)},resetModules(){this.orgAdmin.data=[],this.orgAdmin.success=!1,this.orgAdmin.error=null},onTabChanged(e){this.setStorage(),this.resetModules()},showToastMessage(e){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:"is-bottom"})},formPosted(e){this.form.isLoading=!1;const t=e?.response?.data;(201===t?.code||t?.message)&&this.showToastMessage(t.message)},dispatchData(e,t){this.$store.dispatch(e,t)},fetchData(e,t,n){const o=YUNOCommon.findObjectByKey(this.form.fields,"name",e);o.loading=!0;const a={apiURL:t,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:e=>n(e,o)};this.dispatchData("fetchData",a)},submitForm(e){this.form.isLoading=!0;const t={apiURL:YUNOCommon.config.org("detailsUpdate"),module:"gotData",store:"form",payload:e,callback:!0,callbackFunc:e=>this.formPosted(e)};this.dispatchData("postData",t)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},setupForm(e,t){const n={isRequired:!1,isDisabled:!1,isLoading:!1},o=[{label:"Prasar URL",placeholder:"Enter a valid URL",type:"text",name:"prasar_url",...n},{label:"App Name",placeholder:"Enter a app name",type:"text",name:"app_name",...n},{label:"Excerpt",placeholder:"Enter excerpt",type:"textarea",name:"app_short_description",...n},{label:"Description",placeholder:"Enter description",type:"textarea",name:"app_long_description",...n},{label:"Privacy Policy URL",placeholder:"Enter a valid URL",type:"text",name:"privacy_policy_url",...n},{label:"App Developer Email",placeholder:"Enter a valid email",type:"text",name:"app_dev_email",...n},{label:"App Developer Phone",placeholder:"Enter a valid phone",type:"text",name:"app_dev_phone",...n},{label:"App Developer Website",placeholder:"Enter a valid url",type:"text",name:"app_dev_website",...n},{label:"Video URL",placeholder:"Enter a valid url",type:"text",name:"video_url",...n},{label:"Live Classes Included",placeholder:"",type:"checkbox",name:"live_classes_included",...n},{label:"Study Material",placeholder:"",type:"checkbox",name:"study_material",...n},{label:"Videos",placeholder:"",type:"checkbox",name:"study_material",...n},{label:"Practice Tests",placeholder:"",type:"checkbox",name:"practice_tests",...n}],a={live_classes_included:"",study_material:"",videos:"",practice_tests:"",prasar_url:"",app_name:"",app_short_description:"",app_long_description:"",privacy_policy_url:"",app_dev_email:"",app_dev_phone:"",app_dev_website:"",video_url:"",org_id:t,user_id:isLoggedIn};this.form.fields=o,this.form.payload=a},manageOrg(e){const t=this.activeOrg();t&&(this.setupForm(e,t),this.fetchModules(e,t))},gotOrg(e,t){const{code:n,data:o}=e.response?.data||{};if(200===n){const e=this.form.payload,n=o.prasar;e.live_classes_included=n.live_classes_included,e.study_material=n.resources.study_material,e.videos=n.resources.videos,e.practice_tests=n.resources.practice_tests,e.video_url=n.mobile_app.video_url,e.privacy_policy_url=n.mobile_app.privacy_policy_url,e.prasar_url=n.prasar_url,e.app_short_description=n.mobile_app.app_short_description,e.app_name=n.mobile_app.app_name,e.app_long_description=n.mobile_app.app_long_description,e.app_dev_email=n.mobile_app.app_developer.email,e.app_dev_phone=n.mobile_app.app_developer.phone,e.app_dev_website=n.mobile_app.app_developer.website,e.user_id=isLoggedIn,e.org_id=t,this.orgAdmin.data=o,this.form.payload=e}},fetchOrg(e,t){const n={apiURL:YUNOCommon.config.org("settings",t),module:"gotData",store:"orgAdmin",callback:!0,addToModule:!1,callbackFunc:e=>this.gotOrg(e,t)};this.dispatchData("fetchData",n)},fetchModules(e,t){this.fetchOrg(e,t)}}});