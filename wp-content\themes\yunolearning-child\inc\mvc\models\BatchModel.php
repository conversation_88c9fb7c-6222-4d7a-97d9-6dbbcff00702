<?php

namespace V4;

/**
 * Batch model
 */
class BatchModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('response');
        $this->loadLibary('utility');
    }

    public function getBatch($query, $filter = [])
    {
        $query = is_array($query) ? $query : ['id' => $query];

        if (isset($query['id'])) {
            // Define the query
            $batchQuery = [
                '_source' => false,
                'query' => [
                    'nested' => [
                        'path' => 'data.details.batch_details',
                        'query' => [
                            'term' => [
                                'data.details.batch_details.batch_id' => $query['id']
                            ]
                        ],
                        'inner_hits' => (object)[]
                    ]
                ]
            ];

            $dataResponse = $this->es->customQuery($batchQuery, 'course');
            if ($dataResponse['status_code'] == 200) {
                $batchDataResponse = $dataResponse['body']['hits']['hits'][0];
            } else {
                return false;
            }
        } else {
            return false;
        }

        // Output the response (inner_hits only)
        // Iterate over the hits
        foreach ($batchDataResponse as $hit) {
            if (isset($hit['data.details.batch_details']['hits']['hits'])) {
                //foreach ($hit['inner_hits']['data.details.batch_details']['hits']['hits'] as $innerHit) {
                    $batch = $hit['data.details.batch_details']['hits']['hits'][0]['_source'];
                    $formattedResponse = [
                        'id' => $batch['batch_id'],
                        'title' => $batch['batch_title'],
                        'temporal_state' => $batch['temporal_state'] ?? '',
                        'instructor' => [
                            'id' => $batch['instructor_id'],
                            'name' => $batch['instructor_name'],
                            'profile_url' => $batch['instructor_profile_url']
                        ],
                        'checkout_url' => $batch['batch_post_url'] ?? null,
                        'created_by' => [
                            'id' => $batch['created_by']['id'] ?? null,
                            'name' => $batch['created_by']['name'] ?? null
                        ],
                        'is_active' => $batch['active_batch'],
                        'is_locked' => $batch['locked_batch'],
                        'is_deleted' => $batch['batch_deleted_status'],
                        'start_end' => [
                            'start_date' => $batch['batch_start_date'],
                            'end_date' => $batch['batch_end_date'],
                            'display_start' => $batch['batch_display_start_date'],
                            'display_end' => $batch['batch_display_end_date']
                        ],
                        'class_days' => array_map(function ($day) {
                            return [
                                'day' => $day ?? null,
                                'name' => null,
                                'available' => true
                            ];
                        }, $batch['days_of_week'] ?? []),
                        'class_time' => [
                            'name' => null,
                            'start_time' => $batch['class_start_time'],
                            'end_time' => $batch['class_end_time'],
                            'duration' => $batch['duration']
                        ],
                        'personalisation' => $batch['personalisation'],
                        'teaching_mode' => $batch['mode'] ?? null,
                        'seats' => [
                            'max' => $batch['max_seats'] ?? null,
                            'occupied' => $batch['enrollment_counter'] ?? null,
                            'vacant' => ($batch['max_seats'] ?? 0) - ($batch['enrollment_counter'] ?? 0)
                        ],
                        'in_crm' => [
                            'platform' => '',
                            'batch_id' => $batch['zoho_batch_id'] ?? null
                        ],
                        'course' => [
                            'id' => $batch['course_id'],
                            'title' => null,
                            'description' => null
                        ],
                        'active_enrollments' => array_map(function ($enrollment) {
                            return [
                                'enrollment_id' => $enrollment['id'] ?? null,
                                'learner_name' => $enrollment['learner_name'] ?? null
                            ];
                        }, $batch['active_enrollments'] ?? []),
                        'place' => [
                            'id' => $batch['place']['id'] ?? null,
                            'address' => $batch['place']['address'] ?? null
                        ],
                        'classroom' => [
                            'id' => $batch['classroom']['id'] ?? null,
                            'name' => $batch['classroom']['name'] ?? null
                        ],
                        'is_enrollable' => $batch['is_enrollable'] ?? null,
                        'has_vacancy' => $batch['vacancy'] > 0 ? true : false,
                    ];

                    // Add to the final array
                    //$formattedResponses[] = $formattedResponse;
                //}
            }
        }

        if($formattedResponse){
            return $this->schema->validate($formattedResponse, 'Batch', $filter);
        }else{
            return false;
        }
    }

    // Get batch details
    public function getBatchDetails($batch_id)
    {
        global  $wpdb;
        $wp_batches = $wpdb->get_row("SELECT * FROM wp_batches WHERE id = $batch_id", ARRAY_A);
        if (!$wp_batches) {
            // Return error if an exception occurs
            return false;
        }
        return $wp_batches;
    }

    // Check if the batch is locked and validate enrollment
    public function validateBatchLock($wpBatches)
    {
        global $wpdb;
        if (!empty($wpBatches['batch_reference_post_id'])) {
            $isBatchLocked = get_post_meta($wpBatches['batch_reference_post_id'], 'locked_batch', true);
            if ($isBatchLocked == 1) {
                return false;
            }
            return true;
        }
    }

    /**
     * Validates if the batch enrollment is allowed based on the batch end date and course duration.
     *
     * @param array $wpBatches The batch details retrieved from the database or custom post type.
     * @param array $request The request payload containing enrollment details.
     * @return bool|WP_Error Returns true if validation passes, or WP_Error if validation fails.
     */
    /**
     * Validates if batch enrollment is allowed based on the batch end date and course duration.
     *
     * @param array $batchDetails The batch details retrieved from the database or custom post type.
     * @param array $requestData The request payload containing enrollment details.
     * @return bool Returns true if validation passes, false otherwise.
     */
    public function validateBatchEnrollment($batchDetails, $batchId)
    {
        global $wpdb;

        $batchEndDate = $batchDetails['end_date'];
        $enrollmentType = $batchDetails['enrollment_type'];

        // Retrieve courses associated with the batch
        $courses = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT DISTINCT product_id FROM {$wpdb->prefix}group_relationships WHERE batch_id = %d",
                $batchId
            )
        );

        $courseDurations = array_map(function ($course) {
            $durationWeeks = (int) get_post_meta($course->product_id, 'Duration_Weeks', true);
            return $durationWeeks * 7; // Convert weeks to days
        }, $courses);

        // Determine the maximum course duration
        $maxDurationInDays = !empty($courseDurations) ? max($courseDurations) : 0;
        $calculatedDate = date('Y-m-d H:i:s', strtotime("+$maxDurationInDays days"));


        // Validate batch enrollment based on calculated end date
        if ($batchEndDate < $calculatedDate) {
            return false; // Validation failed
        }

        return true; // Validation passed
    }

    public function addBatchToCourse($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (file_exists(get_stylesheet_directory() . '/inc/classes/rest_api/v1/CSVController.php')) {
            include_once(get_stylesheet_directory() . '/inc/classes/rest_api/v1/CSVController.php');

            if (class_exists('CSVController')) {
                echo "Class CSVController found.";
                $csvController = new \CSVController();
                $csvController->course_batch($batch_id);
            } else {
            }
        } else {
        }
    }

    /**
     * Generates the batch filter for an instructor.
     *
     * Builds a filter array for batch selection by retrieving batch details if provided
     * and fetching active batches for the given instructor. The returned array is used
     * to populate a dropdown UI for selecting a batch.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $batch The batch identifier or details.
     * @param int $instructorId The instructor's ID.
     * @return array The filter array for batch selection.
     * <AUTHOR>
     */
    public function generateBatchFiltersInstructor($batch, $instructorId)
    {
        $row = [
            'filter' => 'batch',
            'title' => 'batch',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Batch',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($batch)) {
            $batch_data = $this->getBatchDetails($batch, ['schema' => 'Batch_Minimal']);

            if ($batch_data) {
                $batch_name = $batch_data['batch_name'] ?? '';
                $sel_batch_id = $batch_data['id'] ?? 0;

                $selected_batch = [
                    'id' => $sel_batch_id,
                    'label' => $batch_name,
                    'filter' => 'batch',
                ];
                $row['selected'] = (int)$selected_batch['id'];
            }
        }

        $activeBatches = $this->utility->getActiveBatchesInstructor($instructorId);

        if (!empty($activeBatches)) {
            $row['items'] = $activeBatches;
        }
        return $row;

    }

    /**
     * Generates batch filter for a learner.
     *
     * Retrieves the selected batch (if provided) and fetches available batches from enrollment events
     * based on the learner's ID, then constructs a dropdown filter for batch selection.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $batch The batch identifier or details.
     * @param int $learnerId The learner's ID.
     * @return array|false The filter array or false on failure.
     * <AUTHOR>
     */
    public function generateBatchFiltersLearner($batch, $learnerId)
    {
        $row = [
            'filter' => 'batch',
            'title' => 'batch',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Batch',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];
        if ($batch) {
            $batch_data = $this->getBatchDetails($batch, ['schema' => 'Batch_Minimal']);
            if ($batch_data) {
                $row['selected'] = (int)($batch_data['id'] ?? 0);
            }
        }
        $customQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.user_id' => $learnerId 
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        $batchRecords = $this->es->customQuery($customQuery, 'batchenrollmentevent', []);

        if ($batchRecords['status_code'] == 200) {
            $batches = $batchRecords['body']['hits']['hits'];
        } else {
            return false;
        }
        $existingBatchIds = [];
        if (!empty($batches)) {
            foreach ($batches as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $batchId = $details['batch_id'] ?? 0;
                $batchName = $details['batch_name'] ?? '';

                if ($batchId && !in_array($batchId, $existingBatchIds)) {
                    $row['items'][] = [
                        'id' => $batchId,
                        'label' => $batchName,
                        'filter' => 'batch',
                    ];
                    $existingBatchIds[] = $batchId;
                }
            }
        }

        return $row;
    }
    
    
}
