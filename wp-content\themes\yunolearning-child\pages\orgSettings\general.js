Vue.component("yuno-general", {
  props: {
    storage: {
      type: Object,
      required: true,
    },
  },
  template: `
        <div class="row">
            <div class="col-12 col-md-6">
                <template v-if="orgAdmin.loading">
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="100px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                </template>
                <template v-if="orgAdmin.success">
                    <yuno-org-settings-form
                        :fields="form.fields"
                        :payload="form.payload"
                        :defaultTaxonomy="defaultTaxonomy"
                        :industryData="industry"
                        @submitForm="submitForm"
                    >
                    </yuno-org-settings-form>  
                </template>
            </div>
        </div>
    `,
  data() {
    return {
      defaultTaxonomy: 0,
      industry: null,
    };
  },
  computed: {
    ...Vuex.mapState([
      "userInfo",
      "user",
      "filterResult",
      "subform2",
      "form",
      "orgAdmin",
      "config",
    ]),
  },
  async created() {},
  destroyed() {},
  mounted() {
    this.manageOrg(this.userInfo.data);
  },
  methods: {
    setStorage(hasColumns) {
      const storage = this.$props.storage;
      const store = {
        currentTab: this.filterResult.tabs.activeTab,
      };

      setTimeout(() => {
        sessionStorage.setItem(
          storage.name + "V" + storage.version,
          JSON.stringify(store)
        );
      }, 100);
    },
    resetModules() {
      this.orgAdmin.data = [];
      this.orgAdmin.success = false;
      this.orgAdmin.error = null;
    },
    onTabChanged(e) {
      this.setStorage();
      this.resetModules();
    },
    /**
     * Displays a toast message.
     * @param {string} message - The message to be displayed in the toast.
     */
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },
    /**
     * Handles the response after form submission.
     * @param {Object} options - The options object containing the response data.
     */
    formPosted(options) {
      this.form.isLoading = false;

      const response = options?.response?.data;

      if (response?.code === 201) {
        this.showToastMessage(response.message);
      } else if (response?.message) {
        this.showToastMessage(response.message);
      }
    },
    /**
     * Dispatches an action to the store with the specified options.
     *
     * @param {string} action - The name of the action to dispatch.
     * @param {Object} options - The options to pass to the action.
     */
    dispatchData(action, options) {
      this.$store.dispatch(action, options);
    },
    /**
     * Fetches data from the specified API URL and invokes the callback function with the fetched data.
     * @param {string} field - The name of the field to fetch data for.
     * @param {string} apiURL - The URL of the API to fetch data from.
     * @param {Function} callbackFunc - The callback function to invoke with the fetched data.
     */
    fetchData(field, apiURL, callbackFunc) {
      const formField = YUNOCommon.findObjectByKey(
        this.form.fields,
        "name",
        field
      );
      formField.loading = true;
      const options = {
        apiURL: apiURL,
        module: "gotData",
        store: "subform2",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => callbackFunc(options, formField),
      };
      this.dispatchData("fetchData", options);
    },
    /**
     * Submits the form data and updates the organization details.
     * @param {Object} payload - The payload containing the form data.
     */
    submitForm(payload) {
      this.form.isLoading = true;

      const options = {
        apiURL: YUNOCommon.config.org("detailsUpdate"),
        module: "gotData",
        store: "form",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.formPosted(options),
      };

      this.dispatchData("postData", options);
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    // Function to set up the form with default field properties and payload
    setupForm(userInfo, activeOrg) {
      const defaultFieldProperties = {
        isRequired: false,
        isDisabled: false,
        isLoading: false,
      };

      const form = [
        {
          label: "Name",
          placeholder: "Enter company name",
          type: "text",
          name: "name",
          ...defaultFieldProperties,
        },
        {
          label: "Descripition",
          placeholder: "Enter message here...",
          type: "textarea",
          name: "description",
          ...defaultFieldProperties,
        },
        {
          label: "Industry",
          placeholder: "Select industry",
          type: "groupDropdown",
          name: "sub_industry",
          options: [],
          loading: false,
          ...defaultFieldProperties,
        },
        {
          label: "Upload Logo",
          placeholder:
            "File format: JPG, GIF or PNG. Dimension: 120x120 pixels",
          type: "upload",
          name: "logo_image",
          cta: "Click to upload",
          ...defaultFieldProperties,
        },
        {
          label: "Upload Fav icon",
          placeholder: "File format: ICO or SVG. Dimension: 32x32 pixels",
          type: "upload",
          name: "fav_icon",
          cta: "Click to upload",
          ...defaultFieldProperties,
        },
        {
          label: "Select a primary color",
          placeholder: "",
          type: "colorpicker",
          name: "primary_color",
          ...defaultFieldProperties,
        },
        {
          label: "Select a background color",
          placeholder: "",
          type: "colorpicker",
          name: "background_color",
          ...defaultFieldProperties,
        },
        {
          label: "Font family",
          placeholder: "Select font family",
          type: "googleFontFamilydropdown",
          name: "font_family",
          options: [],
          loading: false,
          ...defaultFieldProperties,
        },
        {
          label: "Number of employees",
          placeholder: "Select numbers",
          type: "dropdown",
          name: "number_of_employees",
          options: [
            {
              label: "1 (self-employed)",
              value: "1 (self-employed)",
            },
            {
              label: "2-5",
              value: "2-5",
            },
            {
              label: "6-20",
              value: "6-20",
            },
            {
              label: "21-100",
              value: "21-100",
            },
            {
              label: "101-500",
              value: "101-500",
            },
            {
              label: "501 or more",
              value: "501 or more",
            },
          ],
          ...defaultFieldProperties,
        },
      ];

      const DEFAULT_BACKGROUND_COLOR = "#FFFFFF";
      const DEFAULT_PRIMARY_COLOR = "#000000";

      const payload = {
        name: "",
        description: "",
        org_id: activeOrg,
        user_id: isLoggedIn,
        number_of_employees: null,
        industry: "",
        sub_industry: null,
        subject_category: null,
        logo_image: null,
        fav_icon: null,
        font_family: null,
        background_color: DEFAULT_BACKGROUND_COLOR,
        primary_color: DEFAULT_PRIMARY_COLOR,
      };

      // Set the form fields and payload
      this.form.fields = form;
      this.form.payload = payload;
    },
    manageOrg(data) {
      const activeOrg = this.activeOrg();

      if (activeOrg) {
        // isLoggedIn = activeOrg;
        this.setupForm(data, activeOrg);
        this.fetchModules(data, activeOrg);
      }
      // else {
      //     window.location.href = "/select-an-organization";
      // }
    },
    /**
     * Handles the response of the organization industry API call.
     * @param {Object} options - The response object from the API call.
     * @param {Object} formField - The form field object.
     */
    gotOrgIndustry(options, formField) {
      const { code, data } = options.response?.data || {};
      formField.loading = false;

      if (code === 200) {
        formField.options = data;

        const industry = this.orgAdmin.data.industry,
          subIndustry = this.orgAdmin.data.sub_industry;

        if (industry !== "" && subIndustry !== null) {
          const selectedIndustry = YUNOCommon.findObjectByKey(
              data,
              "slug",
              industry
            ),
            selectedSubIndustry = YUNOCommon.findObjectByKey(
              selectedIndustry.sub_industry,
              "slug",
              subIndustry
            );

          this.industry = selectedSubIndustry;
        }
      }
    },
    /**
     * Fetches the organization industry data.
     * @param {string} field - The field to fetch data for.
     */
    fetchOrgIndustry(field) {
      this.fetchData(
        field,
        YUNOCommon.config.org("industries"),
        this.gotOrgIndustry
      );
    },
    /**
     * Handles the response of the organization data API call.
     * @param {Object} options - The response object from the API call.
     */
    gotOrg(options) {
      const { code, data } = options.response?.data || {};

      if (code === 200) {
        const fieldsToNullify = ["sub_industry", "font_family"];
        const imageFields = ["logo_image", "fav_icon"];

        // Convert "null" strings to null values
        fieldsToNullify.forEach((field) => {
          if (
            data[field] === "null" ||
            data[field] === "N/A" ||
            data[field] === ""
          ) {
            data[field] = null;
          }
        });

        // Convert 0 number of employees to null
        if (data.number_of_employees === 0) {
          data.number_of_employees = null;
        }

        // Convert empty subject categories to null
        if (data.subject_category.length === 0) {
          data.subject_category = null;
        } else {
          data.subject_category = data.subject_categories;
        }

        // Convert image fields to object format
        imageFields.forEach((field) => {
          if (
            data[field] !== null &&
            data[field] !== "" &&
            data[field] !== "N/A"
          ) {
            data[field] = { name: data[field] };
          } else {
            data[field] = null;
          }
        });

        data.user_id = isLoggedIn;

        // Update orgAdmin data and form payload
        this.orgAdmin.data = data;
        this.form.payload = data;
        this.inlineData(["sub_industry", "font_family"]);
      }
    },
    /**
     * Fetches organization data from the server.
     */
    fetchOrg(userInfo, activeOrg) {
      const options = {
        apiURL: YUNOCommon.config.org("settings", activeOrg),
        module: "gotData",
        store: "orgAdmin",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotOrg(options),
      };
      this.dispatchData("fetchData", options);
    },
    /**
     * Handles the response of the Google Font Family API call.
     * @param {Object} options - The response object from the API call.
     * @param {Object} formField - The form field object.
     */
    gotGoogleFontFamily(options, formField) {
      formField.loading = false;

      if (options.response.status === 200 && options.response.data) {
        formField.options = options.response.data.items;
      }
    },
    /**
     * Fetches the Google Font Family data.
     * @param {string} field - The field to fetch data for.
     */
    fetchGoogleFontFamily(field) {
      this.fetchData(
        field,
        YUNOCommon.config.generic(
          "googleFonts",
          "key=" + this.config.googleAPIKey + "&sort=popularity"
        ),
        this.gotGoogleFontFamily
      );
    },
    /**
     * Fetches inline data based on the given field.
     * @param {string} field - The field to fetch inline data for.
     */
    fetchInlineDataData(field) {
      switch (field) {
        case "sub_industry":
          this.fetchOrgIndustry(field);
          break;

        case "font_family":
          this.fetchGoogleFontFamily(field);
          break;
      }
    },
    /**
     * Fetches inline data for the given fields.
     * @param {Array} fields - The array of fields to fetch inline data for.
     */
    inlineData(fields) {
      fields.forEach((field) => {
        this.fetchInlineDataData(field);
      });
    },
    /**
     * Fetches the modules for the organization.
     * This function internally calls the fetchOrg function.
     */
    fetchModules(userInfo, activeOrg) {
      this.fetchOrg(userInfo, activeOrg);
    },
  },
});
