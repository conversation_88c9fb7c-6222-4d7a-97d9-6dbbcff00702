#app, #app .field .label {
  color: #201A19;
}

#app .field.uploadField .helper {
  color: #534342;
}

#app .academyForm .yunoModal.demoClass .modalTitle, #app .mainHeader h1 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .field .label {
  font-size: 16px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .yunoFormWrapper .addInstructors {
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .field.uploadField .helper {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .mainHeader {
  padding: 15px 15px 0;
  margin-bottom: 30px;
}

#app .mainHeader h1 {
  margin: 14px 0 0 20px;
}

@media (min-width: 768px) {
  #app .mainHeader h1 {
    margin: 0;
  }
}
#app .yunoTabsWrapper {
  z-index: 7 !important;
}
#app .yunoFormWrapper {
  padding-bottom: 30px;
}

#app .yunoFormWrapper .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  margin-top: 15px;
}
#app .yunoFormWrapper .addInstructors {
  color: #a62027;
  cursor: pointer;
}
#app .demoInstructorTagInput .taginput-container {
  flex-direction: row !important;
}
#app .demoInstructorTagInput .taginput-container .tag {
  margin: 5px 5px 0 !important;
}
#app .addInstructor .tagInputWrapper .ctaWrapper {
  justify-content: end !important;
  gap: 15px !important;
}
#app .demoClass .tab-content {
  padding: 1rem 0 0 0 !important;
}
#app .field .control input[type=text] {
  border-color: #e6e6e6;
  height: 40px;
}

#app .field .control textarea {
  border-color: #e6e6e6;
}

#app .field .control .select select {
  border-color: #e6e6e6;
}

#app .field .control.colorpicker .button {
  height: 40px;
  border-radius: 4px;
}

#app .field.uploadField .field.has-addons {
  display: block;
}

#app .field.uploadField .upload {
  margin-top: 15px;
}

#app .field.uploadField .upload .file-cta {
  background-color: #A81E22;
  color: white;
}

#app .field.uploadField .upload .file-cta .material-icons-outlined {
  font-size: 18px;
  margin-right: 5px;
}

#app .field .dropdown {
  width: 100%;
}

#app .field .dropdown .dropdown-trigger {
  width: 100%;
}

#app .field .dropdown .button {
  width: 100%;
  justify-content: flex-start;
  border-color: #e6e6e6;
  height: 40px;
}

#app .field .dropdown .button > span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .field .dropdown .button .selected {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#app .field .dropdown .button .selected span {
  margin-right: 5px;
}

#app .field .dropdown .button .selected span::after {
  content: ",";
}

#app .field .dropdown .button .selected span:last-child::after {
  content: "";
}

#app .field .dropdown .dropdown-menu {
  width: 100%;
}

#app .field .dropdown .dropdown-menu .dropdown-item:hover {
  text-decoration: none;
  background-color: #FFF8F7;
}

#app .field .dropdown .dropdown-menu .dropdown-item.is-active {
  background-color: #EDE0DE;
}

#app .field .dropdown .placeholder {
  color: #8080808c;
}

#app .field .dropdown.invalid .button {
  border-color: red;
}

#app .categoryTaxonomyWrapper {
  padding-top: 30px;
}

#app .categoryTaxonomy {
  background: #FFF;
  border: 1px solid #E6E6E6;
  border-top: 0;
}

#app .categoryTaxonomy:first-child {
  border-top: 1px solid #E6E6E6;
}

#app .categoryTaxonomy .collapse-trigger {
  display: block;
  padding: 0;
}

#app .categoryTaxonomy .collapse-trigger .b-radio {
  margin: 0;
}

#app .categoryTaxonomy .collapseHeader {
  position: relative;
}

#app .categoryTaxonomy .collapseHeader.menuDown {
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
          box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
}

#app .categoryTaxonomy .collapseHeader .b-radio {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: 15px 15px;
}

#app .categoryTaxonomy .collapseHeader .b-radio + .error {
  display: none;
  margin: 0;
  padding: 0 0 15px 45px;
}

#app .categoryTaxonomy .collapseHeader .b-radio.invalid + .error {
  display: block;
}

#app .categoryTaxonomy .collapseHeader .fa {
  position: absolute;
  right: 15px;
  top: calc(50% - 8px);
}

#app .categoryTaxonomy .collapse-content {
  display: block;
  padding: 15px 30px;
}

#app .categoryTaxonomy .collapse {
  margin-bottom: 15px;
}

#app .categoryTaxonomy .collapse:last-child {
  margin-bottom: 0;
}

#app .categoryTaxonomy .collapse .collapse-trigger {
  position: relative;
  padding-left: 18px;
}

#app .categoryTaxonomy .collapse .collapse-trigger .fa {
  position: absolute;
  left: 0;
  top: 3px;
}

#app .categoryTaxonomy .collapse .collapse-content {
  padding-bottom: 0;
  position: relative;
}

#app .categoryTaxonomy .collapse .collapse-content:before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #E6E6E6;
  position: absolute;
  left: 4px;
  top: 0;
}

#app .categoryTaxonomy .collapse .sub2Content .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}

#app .categoryTaxonomy .collapse .sub2Content .field:before {
  content: "";
  width: 100%;
  height: 1px;
  background-color: #E6E6E6;
  position: absolute;
  left: -25px;
  top: 10px;
  z-index: 1;
}

#app .categoryTaxonomy .collapse .sub2Content .field:after {
  content: "";
  background: #FFF;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

#app .categoryTaxonomy .collapse .sub2Content .field:last-child {
  margin-bottom: 0;
}

#app .categoryTaxonomy .collapse .sub2Content .field .b-checkbox {
  position: relative;
  z-index: 3;
}

#app .categoryTaxonomy .collapse .trigger .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryTaxonomy .collapse .trigger .b-checkbox {
  margin: 0;
}
#app .academyForm .yunoModal.demoClass {
  z-index: 100000 !important;
}
#app .academyForm .yunoModal.demoClass .modal-background {
  background-color: rgba(0, 0, 0, 0.5) !important;
}
#app .academyForm .yunoModal.demoClass .modal-content {
  border: 1px solid #e6e6e6 !important;
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 500px;
  margin: 0 auto;
}
#app .academyForm .yunoModal.demoClass .modalTitle {
  color: #201A19;
  margin-bottom: 16px;
}
#app .academyForm .yunoModal.demoClass .modal-close {
  top: 20px !important;
  right: 20px !important;
}
#app .academyForm .yunoModal.demoClass .modalBody {
  padding: 0;
}
#app .academyForm .yunoModal.demoClass .modalBody .description {
  color: #534342;
  font-size: 14px;
  margin-bottom: 24px;
}
#app .academyForm .yunoModal.demoClass .modalBody .instructorFields .field {
  margin-bottom: 20px;
}
#app .academyForm .yunoModal.demoClass .modalBody .instructorFields .field .label {
  font-weight: 500;
  margin-bottom: 8px;
}
#app .academyForm .yunoModal.demoClass .modalBody .instructorFields .field .taginput .taginput-container {
  border-color: #e6e6e6;
  border-radius: 4px;
  flex-direction: row !important;
}
#app .academyForm .yunoModal.demoClass .modalBody .instructorFields .field .taginput .taginput-container .tag {
  margin: 6px 5px 0 !important;
}
#app .academyForm .yunoModal.demoClass .modalBody .instructorFields .field .taginput .taginput-container input {
  height: 38px;
}
#app .academyForm .yunoModal.demoClass .modalFooter {
  margin-top: 24px;
}
#app .academyForm .yunoModal.demoClass .modalFooter .ctaWrapper {
  gap: 10px;
}
#app .academyForm .yunoModal.demoClass .modalFooter .ctaWrapper .primaryCTA {
  padding: 7px 16px;
  border-radius: 4px;
  border: 1px solid #A81E22;
  background-color: #A81E22;
  line-height: normal;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  min-width: 80px;
}
#app .academyForm .yunoModal.demoClass .modalFooter .ctaWrapper .primaryCTA:hover {
  background-color: #7d1619;
}
#app .academyForm .yunoModal.demoClass .modalFooter .ctaWrapper .secondaryCTA {
  padding: 7px 16px;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  background-color: #fff;
  line-height: normal;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
  color: #201A19;
}
#app .academyForm .yunoModal.demoClass .modalFooter .ctaWrapper .secondaryCTA:hover {
  border-color: #A81E22;
}/*# sourceMappingURL=orgSettings.css.map */
