Vue.component('yuno-learner-attendance', {
    props: ["data", "options"],
    template: `
        <div>
            <template v-if="data.loading">
                <div class="loaderWrapper">
                    <div class="smallLoader"></div>
                </div>
            </template>
            <template v-if="data.success">
                <template v-if="data.error">
                    {{ data.errorData }}
                </template>
                <template v-else>
                    <div class="classDate">
                        {{ data.data.attendance_detail.class_date }}
                        <a 
                            class="action" 
                            v-if="false"
                            @click.prevent="createCSV('batchenrollmentevent')"
                            href="#">
                            <b-tooltip label="Download CSV"
                                type="is-dark"
                                position="is-bottom">
                                <span class="material-icons">file_download</span>
                            </b-tooltip>
                        </a>
                    </div>
                    <div class="intro">
                        <p class="introLabel">Course</p>
                        <h2 class="primaryTitle">{{ data.data.attendance_detail.course_title }}</h2>
                        <p class="introLabel">Batch</p>
                        <h3 class="secondaryTitle">{{ data.data.attendance_detail.batch_name }}</h3>
                    </div>
                    <ul class="stats">
                        <li v-for="(item, i) in data.data.attendance_detail.stats" :key="i">
                            <div class="itemValue">{{ item.value }}</div>
                            <div class="itemLabel">{{ item.label }}</div>
                        </li>
                    </ul>
                    <div class="classDetail">
                        <template v-if="learnerInsightsClass.loading">
                            <div class="loaderWrapper">
                                <div class="smallLoader"></div>
                            </div>
                        </template>
                        <template v-if="learnerInsightsClass.success">
                            <template v-if="learnerInsightsClass.error">
                                <p class="inform">{{learnerInsightsClass.errorData}}</p>
                            </template>
                            <template v-else>
                                <p class="inform" v-if="learnerInsightsClass.data.is_absent !== undefined && learnerInsightsClass.data.is_absent">{{learnerInsightsClass.errorData}}</p>
                                <p class="classDateV2">{{ learnerInsightsClass.data.class_date }}</p>
                                <p class="classTitle">{{ learnerInsightsClass.data.class_title }}</p>
                                <ul class="classInfo">
                                    <li>
                                        <figure>
                                            <img :src="learnerInsightsClass.data.instructor.image" :alt="learnerInsightsClass.data.instructor.name">
                                            <figcaption>
                                                <p class="itemLabel">Instructor</p>
                                                <p class="itemValue">{{ learnerInsightsClass.data.instructor.name }}</p>
                                            </figcaption>
                                        </figure>
                                    </li>
                                    <li>
                                        <p class="itemLabel">Start Time</p>
                                        <p class="itemValue">{{ learnerInsightsClass.data.class_start_time }}</p>
                                    </li>
                                    <li>
                                        <p class="itemLabel">Duration</p>
                                        <p class="itemValue">{{ learnerInsightsClass.data.class_duration }}</p>
                                    </li>
                                </ul>
                                <div class="barWrapper">
                                    <div class="barHeader">
                                        <p class="classDateV2">Attendence Duration</p>
                                        <p class="barValue">{{learnerInsightsClass.data.attendence_duration.value}}</p>
                                    </div>
                                    <b-progress 
                                        :type="{
                                            'is-red': learnerInsightsClass.data.attendence_duration.percentage <= 30,
                                            'is-orange': learnerInsightsClass.data.attendence_duration.percentage > 30,
                                            'is-yellow': learnerInsightsClass.data.attendence_duration.percentage > 50,
                                            'is-lightGreen': learnerInsightsClass.data.attendence_duration.percentage > 70,
                                            'is-darkGreen': learnerInsightsClass.data.attendence_duration.percentage > 80
                                        }"  
                                        format="percent"    
                                        :value="Number(learnerInsightsClass.data.attendence_duration.percentage)">
                                    </b-progress>
                                </div>
                            </template>
                        </template>
                        <template v-else>
                            <p class="inform" v-if="!learnerInsightsClass.loading">{{ data.data.attendance_detail.message.before_class_select }}</p>
                        </template>
                    </div>
                    <div class="classes">
                        <p class="classesLabel">Classes ({{ data.data.attendance_detail.days.length }})</p>
                        <ul class="days">
                            <li 
                                v-for="(day, i) in data.data.attendance_detail.days" 
                                :class="[day.hasAttended ? 'active' : 'disable', day.hasClicked ? 'isClicked' : '']"
                                :key="i">
                                <div class="disc" @click="getClassDetail(day, data.data.attendance_detail)">{{ day.label }}</div>
                            </li>
                        </ul>
                    </div>
                </template>
            </template>
        </div>  
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'learnerInsightsClass'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        getClassDetail(day, attendanceDetail) {
            this.$emit('getClassDetail', day, attendanceDetail);
        },
    }
});