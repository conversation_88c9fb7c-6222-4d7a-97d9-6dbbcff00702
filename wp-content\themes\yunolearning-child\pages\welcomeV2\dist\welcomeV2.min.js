const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":this.host()},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let o="";void 0!==t&&(o="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+o},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+o},studentResultsAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+o},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+o+"/"+n+"/"+s+"/"+a},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+o+"/"+n},classAPi:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+o+"/"+n+"/"+s},classesByViewAPi:function(e,t,o,s,a,r,i,u,c,l){let d="",p="",m="";l&&(d="?ver="+n()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==r&&!1!==r&&(m="/"+r);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+o+"/"+s+"/"+a+m+"/"+i+"/"+u+"/"+c+d},groupsAPi:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+o+"/"+n},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,o,n,s){let a="";!1!==s&&(a="?filter="+s);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+o+"/"+n+a},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+s},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+o},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+o+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},enrollmentList:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},enrollmentsList:function(e,t,o,n,s,a,r,i,u,c,l,d,p,m){let g="";g=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+g+"/enrollments/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r+"/"+i+"/"+u+"/"+c+"/"+m+"/"+l+"/"+d},paymentsList:function(e,t,o,s,a,r,i,u,c,l){let d="";l&&(d="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+o+"/"+s+"/"+a+"/"+r+"/"+i+"/"+u+"/"+c+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let o="",n="";void 0!==t&&!1!==t&&(n="/"+t),void 0!==e&&!1!==e&&(o="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+o+n},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+o+"/status/toggle"},changeBatchAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,o){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+o+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+s},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+o+"/"+n},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,o,n,s,a,r,i,u){let c="";void 0!==r&&!1!==r&&(c="/"+r);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+c+"/"+i+"/"+u},mapCoursesAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+o},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},deleteResourceAttachmentAPI:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+o},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},videoListAPI:function(e,t){let o="",n="";void 0!==e&&(o=e),void 0===n&&!1===n||(n=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+o},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,o,n,s){let a="";if(!1!==t)a=t;else{let t="";void 0!==o&&(t=o),a=e+"/"+t+"/"+n+"/"+s}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+a},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+o},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,o,s,a,r,i){let u="",c="",l="",d="",p="",m="";void 0!==e&&!1!==e&&(u="/"+e),void 0!==s&&!1!==s&&(d="/"+s),void 0!==a&&!1!==a&&(p="/"+a),void 0!==r&&!1!==r&&(m="/"+r),void 0!==o&&!1!==o&&(l="/"+o),i&&(c="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/review"+u+"/"+t+l+d+p+m+c},courseListAPI:function(e,t,o,s){let a="",r="",i="";s&&(a="?ver="+n()),void 0!==t&&!1!==t&&(r="/"+t),void 0!==o&&!1!==o&&(i="/"+o);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+r+i+a},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+o},learnerListAPI:function(e,t){let o="";t&&(o="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+o},instructorAvailabilityAPI:function(e,t,o){let s="",a="";o&&(s="?ver="+n()),void 0!==t&&!1!==t&&(a="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+a+s},createUpdateAvailabilityAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+o},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+o},instructorsByCategoryAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+o},capabilitiesAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+o},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let o="";t&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+o},signInURLWithState(e){const t=["email","profile"],o=encodeURI(JSON.stringify(e));let n="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",o);n=e.toString()}else n="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+o+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return n},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+s},eventDetailAPI:function(e,t,o,s,a){let r="";a&&(r="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+o+"?uuid="+s+r},profileDetailAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+s},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},resourcesDetailAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+o+"/"+n},videoTestimonialAPI:function(e,t){let o="";t&&(o="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o},createExamResultAPI:function(e,t,o,n){let s="",a="";void 0===n&&!1===n&&(n="v1"),void 0!==t&&!1!==t&&(s="/"+t),void 0!==o&&!1!==o&&(a="/"+o);return this.pickHost()+"/wp-json/yuno/"+n+"/examresult/"+e+s+a},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,o){let n="",s="";void 0!==t&&(n="/"+t),void 0!==o&&(s="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n+s},videotestimonialListAPI:function(e,t,o,n){let s="",a="";void 0!==o&&(s="/"+o),void 0!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+s+a},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,o,n,s,a,r){let i="",u="",c="",l="",d="",p="",m="";void 0!==e&&!1!==e&&(c="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==o&&!1!==o&&(l="/"+o),void 0!==n&&!1!==n&&(d="/"+n),void 0!==s&&!1!==s&&(p="/"+s),void 0!==a&&!1!==a&&(i="/"+a),void 0!==r&&!1!==r&&(u="/"+r);return this.pickHost()+"/wp-json/yuno/v1/article"+c+m+l+d+p+i+u},webinarSingleAPI:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+s},webinarListingAPI:function(e,t,o,s,a){let r="";a&&(r="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+o+"/"+s+r},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,o,n,s,a,r){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+n},managelearningContentAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+o},learningContentAPI:function(e,t,o,n,s){let a="",r="",i="",u="";void 0!==t&&!1!==t&&(a="/"+t),void 0!==o&&!1!==o&&(r="/"+o),void 0!==n&&!1!==n&&(i="/"+n),void 0!==s&&!1!==s&&(u="/"+s);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+a+r+i+u},learnerInsightsAPI:function(e,t,o,s,a,r,i,u,c){let l="";c&&(l="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+o+"/"+s+"/"+a+"/"+r+"/"+i+"/"+u+l},learnerInsightsClassAPI:function(e,t,o,s){let a="",r="";r=void 0!==o&&!1!==o?"v2":"v1",s&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/"+r+"/learner/class/"+e+"/"+t+a},signupFormAPI:function(e,t,o){let s="",a="";o&&(s="?ver="+n()),void 0!==t&&!1!==t&&(a="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+a+s},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,o,s,a){let r="";a&&(r="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+o+"/"+s+r},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,o,n){let s="";!1!==n&&(s="?filters="+encodeURI(JSON.stringify(n)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+o+"/"+s},resourcesResultsAPI:function(e,t,o,n,s,a,r){let i="";!1!==a&&(i="?filters="+encodeURI(JSON.stringify(a))),void 0!==r&&!1!==r||(r="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+r+"/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+i},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,o){let s="";o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+s},instructorInsightsAPI:function(e,t,o,s,a,r,i,u){let c="";u&&(c="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+o+"/"+s+"/"+a+"/"+r+"/"+i+c},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+o},dashboardListAPI:function(e,t,o,n,s){let a="",r="",i="",u="";void 0!==o&&!1!==o&&(u="/"+o),void 0!==t&&!1!==t&&(i="/"+t),void 0!==n&&!1!==n&&(a="/"+n),void 0!==s&&!1!==s&&(r="/"+s);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+i+u+a+r},enrollmentDashboardAPI:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+o+"/"+n+"/"+s},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let o="";void 0!==t&&!1!==t&&(o="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+o},publishedBlogsAPI:function(e,t,o,n,s,a){let r="",i="",u="",c="",l="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(u="/"+t),void 0!==o&&!1!==o&&(c="/"+o),void 0!==n&&!1!==n&&(l="/"+n),void 0!==s&&!1!==s&&(r="/"+s),void 0!==a&&!1!==a&&(i="/"+a);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+u+c+l+r+i},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,o,n,s,a){a=void 0!==a&&!1!==a?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"/"+n:"",s=void 0!==s&&!1!==s?"/"+s:"";return this.pickHost()+"/wp-json/yuno/v1/"+a+"/"+e+t+o+n+s},instructorMyCourses:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+o},instructorCourseBatches:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+o+"/batches/"+n+"/"+s+"/"+a},manageBookmarkAPI:function(e,t,o,n,s,a){void 0!==t&&!1!==t||(t="v1"),o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"/"+n:"",s=void 0!==s&&!1!==s?"/"+s:"",a=void 0!==a&&!1!==a?"?filters="+encodeURI(JSON.stringify(a)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+o+n+s+a},availableCourses:function(e,t,o,n){t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"?params="+encodeURI(JSON.stringify(n)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+o+n},availableBatches:function(e,t,o,n){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",o=void 0!==o&&!1!==o?"/"+o:"",n=void 0!==n&&!1!==n?"?params="+encodeURI(JSON.stringify(n)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+o},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,o,n,s,a,r){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+o+"/"+n},pastBatchesList:function(e,t,o,n,s,a){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,o,n,s,a,r,i,u,c,l,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r+"/"+i+"/"+u+"/"+c+"/"+l+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+o+"/"+n+"/"+s},instructorVideotestimonial:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,o,n,s,a,r){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+o},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+o},classReviews:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+o+"/"+n+"/"+s},classReviewsByInstructor:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+o+"/"+n+"/"+s},listOfUser:function(e,t,o){let s="";o&&(s="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+s},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,o,n,s,a,r,i,u,c,l){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+o+"/"+n+"/"+s+"/"+a+"/"+r+"/"+i+"/"+u+"/"+c+"/"+l},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+o},courseEconomicsSummary:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+o+"/"+n},csvList:function(e,t,o,n,s){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+o+"/"+n+"/"+s},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,o,n){void 0===n&&(n="v1");return this.pickHost()+"/wp-json/yuno/"+n+"/referrer/user/"+e+"/"+t+"/"+o},referralReports:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+o+"/"+n},orgToken:function(e,t){let o="";return o="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,o},webhooks:function(e,t,o,n,s,a){let r="";return"grid"===e?r=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+o+"/"+n+"/"+s+"/"+a:"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?r=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),r},seo:function(e,t,o,n){let s="";return"status"===e?s=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?s=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+o+"/?search="+n),s},quiz:function(e,t,o,n){let s="";return void 0!==n&&!1!==n||(n=""),"create"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+n:"quizgrid"===e?s=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?s=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?s=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?s=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+o:"delete"===e?s=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),s},question:function(e,t,o,n,s){let a="";return"questions"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+o:"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?a=this.pickHost()+"/wp-json/yuno/v1/question/"+n:"deleteQuestionSet"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset/"+s:"attempt"===e?a=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+n:"attemptQuestionSet"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+s:"questionset"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset/"+s:"questionsetQuestions"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+s:"questionsetQuestionsList"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+s:"questionsetQuestionsReorder"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),a},enrollments:function(e,t,o,n){let s="";return"active"===e?s=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+o+"/learners":"extendDate"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),s},blog:function(e,t,o,n){let s="";return"recentSingle"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+o+"/"+n+"?is_list=true&category_id="+t:"categoriesList"===e?s=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),s},writingTask:function(e,t,o,n,s,a,r,i){let u="";return"type"===e?u=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+o:"create"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+n+"/"+s+"/"+a+"/"+r+"/"+i:"payload"===e?u=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+n:"delete"===e&&(u=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+n),u},categoryLandingPage:function(e,t){let o="";return"category"===e?o=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(o=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),o},examResults:function(e,t,o,n){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+o+"/"+n},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,o,n,s,a,r,i,u,c,l,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${o}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${n}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${s}/${a}/${r}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${s}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${i}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${u}/batch`,courses:`/wp-json/yuno/v3/org/course/${o}/${t}/${n}/${s}/${c}/${l}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${o}/${n}/${s}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,o){let n="";return"steps"===e?n=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+o:"postStep"===e?n=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+o:"updateMobile"===e&&(n=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),n},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,o,n,s,a){let r="";switch(e){case"insights":r=`/wp-json/yuno/v2/users/${t}/learner/${o}/${n}/${s}`;break;case"demoRequests":r=`/wp-json/yuno/v1/demo-requests/${t}/list/${n}/${s}`;break;case"demoRequestsOrg":r=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${o}/${n}/${s}`;break;case"learnerDetailOrg":r=`/wp-json/yuno/v1/demo-requests/${t}/${a}`;break;case"learnerDetail":r=`/wp-json/yuno/v1/demo-requests/${a}`;break;case"instructorLearnerDetail":r=`/wp-json/yuno/v2/instructor/mylearner/${a}`;break;case"orgAdminLearners":r=`/wp-json/yuno/v2/orgadmin/learner/${o}/${n}/${s}`}return this.pickHost()+r},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,o,n,s){let a="";switch(e){case"googleFonts":a=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":a=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":a=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":a=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${o}/${n}`;break;case"orgBatches":a=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${s}`;break;case"org":a=`${this.pickHost()}/wp-json/yuno/v2/org/${n}`;break;case"categories":a=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return a},course:function(e,t,o,n,s,a){let r="";switch(e){case"payload":r=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":r=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":r=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":r=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${o}/${n}/${s}/${a}`;break;case"createBatchOrg":r=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":r=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":r=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return r},learner:function(e,t,o,n,s,a){let r="";switch(e){case"enrolledCourses":r=`/wp-json/yuno/v3/learner/${t}/enrollments/${o}`;break;case"classes":r=`/wp-json/yuno/v4/classes/${o}/${n}/${t}?limit=${s}&offset=${a}`;break;case"filters":r=`/wp-json/yuno/v4/classes/filter/${n}/${t}`}return this.pickHost()+r},classes:function(e,t,o,n,s,a,r,i,u,c,l){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${o}/${n}/${s}/${a}/${r}/${i}/${u}/${c}/${l}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private"}[e]||"")},attendance:function(e,t,o,n,s){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${o}/${n}/${s}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots"}[e]||"")},classInsights:function(e,t,o,n,s){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${o}/${n}/${s}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${o}/${n}/${s}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${o}/${n}/${s}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${o}/${n}/${s}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",activeOrg:"/wp-json/yuno/v3/user/state"}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`,detail:`/place/details/json?place_id=${t.placeID}&key=${t.key}`}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules"}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")}},o=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var o=indexedDB.open("test");o.onerror=function(){e(!0)},o.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),n=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,o)=>e.find((e=>e[t]===o))||null,heightOfEle:function(e,t){let o=e.offsetHeight;if(t){let t=getComputedStyle(e);return o+=parseInt(t.marginTop)+parseInt(t.marginBottom),o}return o},assignVValidationObj:function(e){const t=window.VeeValidate,o=window.VeeValidateRules,n=t.ValidationProvider,s=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let n in e.messages)t.extend(n,o[n]);t.localize("validationMsg",e),Vue.component("ValidationProvider",n),Vue.component("ValidationObserver",s)},removeObjInArr:function(e,t,o){let n=e.length;for(;n--;)e[n]&&e[n].hasOwnProperty(t)&&arguments.length>2&&e[n][t]===o&&e.splice(n,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),o=0;o<t.length;o++){var n=t[o].split("=");if(n[0]==e)return n[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){o.then((function(t){e(t)}))},setCookie:function(e,t,o){let n=new Date;void 0===o&&(o=30),n.setTime(n.getTime()+24*o*60*60*1e3);let s="expires="+n.toGMTString();document.cookie=e+"="+t+";"+s+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:n,removeValInArr:function(e){let t,o,n=arguments,s=n.length;for(;s>1&&e.length;)for(t=n[--s];-1!==(o=e.indexOf(t));)e.splice(o,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,o){let n=e.match(t);return null!=n&&(!0===o?n[1].replace(/\/$/,""):n[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,o){let n=window.pageYOffset,s=(a=e,window.pageYOffset+document.querySelector(a).getBoundingClientRect().top);var a;targetY=document.body.scrollHeight-s<window.innerHeight?document.body.scrollHeight-window.innerHeight:s,customHeight=void 0!==o?o:74,diff=targetY-n-customHeight;let r="";diff&&window.requestAnimationFrame((function e(o){r||(r=o);let s=o-r,a=Math.min(s/t,1);var i;a=(i=a)<.5?4*i*i*i:(i-1)*(2*i-2)*(2*i-2)+1,window.scrollTo(0,n+diff*a),s<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var o=e.substring(1).split("&"),n=0;n<o.length;n++){var s=o[n].split("=");if(s[0]==t)return s[1]}return!1},cleanTextAndTruncate:function(e,t){let o=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(o=o.replace(/\s\s+/g," "),o.length>t){const e=o.lastIndexOf(" ",t-1);return o.substring(0,e)+"..."}return o}}}(jQuery),YUNOStore=function(e){const t=function(e,t,o,n,s){if(n){if(void 0===t.addToModule||t.addToModule)if(void 0!==s&&s){for(let t=0;t<o.length;t++)e.data.push(o[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=o}else t.moduleTabs?(e.error=o,e.loading=!1):e.error=o;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[]},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{ClassTitle:"",ClassDescription:"",ClassDate:"",ClassTime:"",ClassDuration:"",classSchedule:"",learner:[],RelatedCourses:"",BatchID:"",academy_id:""}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,o){if(o.isError){let n=o.response.response,s=!(void 0===o.pushData||!o.pushData),a=e[o.store],r="";r=void 0!==n&&void 0!==n.data&&void 0!==n.data.message?n.data.message:YUNOCommon.config.errorMsg.common,console.log(r),console.log(o.store),o.tabs?(t(a.tabs[o.tabIndex],o,n,!1,s),a.tabs[o.tabIndex].error=!0,a.tabs[o.tabIndex].errorData=r,o.callback&&o.callbackFunc(a.tabs[o.tabIndex].errorData)):(t(a,o,n,!1,s),a.error=!0,a.errorData=r,o.callback&&o.callbackFunc(a.errorData))}else{let n=o.response.data.data,s=!(void 0===o.pushData||!o.pushData),a=o.response.data,r=e[o.store];o.tabs?(204===a.code&&(r.tabs[o.tabIndex].error=!0,r.tabs[o.tabIndex].errorData=a.message,console.log(a.message),console.log(o.store)),401===a.code&&(r.tabs[o.tabIndex].error=!0,r.tabs[o.tabIndex].errorData=a.message,console.log(a.message),console.log(o.store)),t(r.tabs[o.tabIndex],o,n,!0,s)):(204===a.code&&(r.error=!0,r.errorData=a.message,console.log(a.message),console.log(o.store)),401===a.code&&(r.error=!0,r.errorData=a.message,console.log(a.message),console.log(o.store)),t(r,o,n,!0,s)),o.callback&&o.callbackFunc(o)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,o){if(o.isError){let n=o.response;(void 0===o.overrideData||o.overrideData)&&t(e[o.module],o,n,!1),o.callback&&o.callbackFunc(e[o.module].errorData)}else{let n=o.response.data.data;(void 0===o.overrideData||o.overrideData)&&t(e[o.module],o,n,!0),o.callback&&o.callbackFunc(o)}},reviewsByType(e,o){if(o.isError){let n=o.response;t(e[o.module],o,n,!1)}else{let n=o.response.data.data;o.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[o.module],o,n,!0),o.isTabAdded=!0,setTimeout((()=>{o.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,o){if(o.isError){let n=o.response;t(e[o.module],o,n,!1)}else{let s=o.response.data.data;for(var n=0;n<s.length;n++)s[n].username_email_phone=`${s[n].username_email} (${s[n].phone})`;t(e[o.module],o,s,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let o=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(o,"id",t.classID),0===o.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let o=e[t.parentModule].data[t.classIndex];void 0!==o&&(o.isLoading=!1,o.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const o=localStorage.getItem("userSignUp");null!==o&&"pending"===o&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,o){if(o.isError){let n=o.response;"enrolled"===o.nestedTab?(e[o.module].tabs[o.tabIndex].loading=!1,e[o.module].tabs[o.tabIndex].pageLoading=!1,e[o.module].tabs[o.tabIndex].error=n):t(e[o.module],o,n,!1)}else o.callback&&o.callbackFunc(o,o.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let o=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let n=0;n<o.length;n++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(o[n]);let n=e[t.module].data[t.courseIndex].tabs[t.tabIndex];n.count=t.response.data.count,n.currentCount=n.data.length,n.offset=n.currentCount,n.isLoadMore=!1}else{for(let e=0;e<o.length;e++)o[e].isBatches=!1,o[e].isLoading=!1,o[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=o}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let n=t.response.data.data;if(void 0!==t.nested){let s=n.columns,a=n.rows,r={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&s.push(r);for(var o=0;o<a.length;o++)a[o].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+a[o].id};e[t.nested].tabs[0].data=n,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=n.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const o=function(o){o?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?o(!1):o(!0)},instructorLearners(e,t){const o=function(o){if(o&&void 0!==t.response.data){let o=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=o,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let n={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(o.group_created_time)[0]),group_created_time:o.group_created_time,group_id:""+o.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${o.group_id}`};e[t.module].tabs[t.index].data.unshift(n),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let o=e.instructorLearners.addLearner;o.selectedLearner="",o.payload.group_id="",o.payload.owner_id="",o.payload.user_ids=[],o.deleteUser=[],o.newAddedUser=[],o.payload.add_user_ids=[],o.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const s=e[t.module].tabs[t.index];s.hasData=!0,s.isLoadMore=!1;for(var n=0;n<o.length;n++)o[n].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(o[n].group_created_time)[0]),o[n].scheduleClassURL=`/class-schedule/?groupID=${o[n].group_id}`,s.data.push(o[n]);s.count=t.response.data.count,s.currentCount=s.data.length,s.offset=s.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?o(!1):(o(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},o){let n=t[o.store];n.loading=!0,axios.get(o.apiURL,{headers:void 0!==o.headers?o.headers:""}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)}))},postThirdPartyData({commit:e,state:t},o){let n=t[o.store];n.loading=!0,axios.defaults.timeout=void 0===o.timeout?0:o.timeout,"post"===o.method?axios.post(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"patch"===o.method?axios.patch(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"head"===o.method?axios.head(o.apiURL,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):"put"===o.method?axios.put(o.apiURL,o.payload,{headers:o.headers}).then((t=>{n.loading=!1,o.response=t,n.error=null,e(o.module,o)})).catch((t=>{n.loading=!1,n.errorData=t,o.response=t,n.error=!0,e(o.module,o)})):console.log("not defined")},fetchData({commit:e,state:t},o){let n="",s="";s="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},n=void 0!==o.store?o.tabs?t[o.store].tabs[o.tabIndex]:t[o.store]:t[o.module],void 0===o.moduleLoading||o.moduleLoading?n.loading=!0:n.loading=!1,o.moduleTabs&&o.isTabLoader&&(n.loading=!0),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.get(o.apiURL,{headers:s}).then((n=>{o.response=n,o.isError=!1,e(o.module,o),403===(o.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===o.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,t[o.store].loading=!1,t[o.store].success=!0;let s="";s=void 0!==o.store?o.tabs?t[o.store].tabs[o.tabIndex]:t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),403===n.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===o.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),o.response=n,o.isError=!0,e(o.module,o)}))},putData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.put(o.apiURL,o.payload,{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},awsPutData({commit:e,state:t},o){let n={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization="",axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.put(o.apiURL,JSON.stringify(o.payload),{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,o.response=n,o.isError=!0,e(o.module,o)}))},postData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.post(o.apiURL,o.payload,{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},awsPostData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.post(o.apiURL,JSON.stringify(o.payload),{headers:n}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0;let s="";s=void 0!==o.store?t[o.store]:t[o.module],void 0!==s.errorData&&void 0!==n.response&&(s.errorData=n.response),o.response=n,o.isError=!0,e(o.module,o)}))},deleteData({commit:e,state:t},o){let n="";void 0!==o.headers?("0"!==isLoggedIn?o.headers.authorization=t.config.yunoAPIToken:o.headers.authorization="",n=o.headers):(n={"content-type":"text/json"},"0"!==isLoggedIn?n.authorization=t.config.yunoAPIToken:n.authorization=""),axios.defaults.timeout=void 0===o.timeout?6e4:o.timeout,axios.delete(o.apiURL,{headers:n,data:o.payload}).then((t=>{o.response=t,o.isError=!1,e(o.module,o)})).catch((n=>{console.log(o.store),console.log(n),t[o.store].error=!0,o.response=n,o.isError=!0,e(o.module,o)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}});window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-welcome-v2",{template:'\n        <div>\n            <yuno-page-loader v-if="isPageLoading"></yuno-page-loader>\n            <div class="pageGrid" v-show="isPageReady">\n                <template v-if="isUserAuthorized">\n                    <yuno-loader></yuno-loader>\n                    <header class="simpleHeader">\n                        <img width="106" height="50" :src="logoURL" alt="Yuno Learning">\n                    </header>\n                    <main id="yunoMain" class="mainBody">\n                        <section class="container welcomeWrapper">\n                            <div class="mainHeader" v-if="false">\n                                <h1 class="title">{{ "Welcome " + userInfo.data.yuno_display_name }}</h1>\n                            </div> \n                            <div class="row">\n                                <div class="col-12 col-md-8" v-if="false">\n                                    <div class="videoLPPlayer">\n                                        <iframe width="800" height="450" src="https://www.youtube.com/embed/AThKzwLsdIs?si=3npgIKLuCCPEF1bG" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>\n                                    </div>\n                                </div>    \n                                <div class="col-12 col-md-4 vMiddle offset-md-4" style="text-align: center;">\n                                    <div class="mainHeader">\n                                        <h1 class="title">{{ "Welcome " + userInfo.data.yuno_display_name }}</h1>\n                                    </div> \n                                    <div class="details">\n                                        <p>\n                                            Welcome to Yuno Learning. You’re an administrator of your organization.\n                                        </p>\n                                        <b-button\n                                            tag="a"\n                                            href="/enrollments/all"\n                                            class="yunoSecondaryCTA fat">\n                                            Go to Dashboard\n                                        </b-button>    \n                                    </div>\n                                </div>    \n                            </div>    \n                        </section>\n                    </main>\n                </template>\n                <template v-else>\n                    <div class="container">\n                        <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                    </div>\n                </template>\n            </div>\n        </div>\n    ',data:()=>({apiURL:null,logoURL:"https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",isMiniSidebar:!1,pageHeader:{title:"Org Settings"},authorizedRoles:["org-admin"],defaultTaxonomy:0,industry:null}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","loader","form"]),isUserAuthorized:{get(){return YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data)}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){let e="";return e=!this.user.isLoggedin||this.userInfo.success,e}}},async created(){this.init()},destroyed(){},mounted(){},methods:{init(){this.loginStatus()},gotUserInfo(e){const{response:{data:{code:t,data:o}={}}={}}=e;if(200===t){this.userRole.data=o.role;const{host:e}=YUNOCommon.config,{has_org:t,org_id:n}=this.userInfo.data;t&&1===n.length&&sessionStorage.setItem("activeOrg",JSON.stringify(n[0].id))}},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",e)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo()):this.user.isLoggedin=!1;const e=YUNOCommon.getQueryParameter("stepsBegin");this.user.isLoggedin&&e&&(this.stepsBegin=!0)}}});