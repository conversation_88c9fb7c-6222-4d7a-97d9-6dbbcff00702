<?php

/**
 * Academy controller is used for publish, update, delete, get academy
 * Basically we are creating a new custom post type academy named academy.
 */
class AcademyController
{
    private $logger;
    public $namespace;
    public $addDemoInstructors;
    public $editDemoInstructors;
    public $getDemoInstructors;
    public $getOrgAcademies;
    public function __construct()
    {
        $this->namespace = 'yuno/v3';
        $this->addDemoInstructors = '/academy/demo-instructors/add';
        $this->editDemoInstructors = '/academy/demo-instructors/edit';
        $this->getDemoInstructors = '/academy/demo-instructors/(?P<org_admin_id>\d+)/(?P<org_id>\d+)/(?P<academy_id>\d+)';
        $this->getOrgAcademies = '/org/academies';
        $this->logger = WP_Structured_Logger::get_instance();
    }

    public function registerRoutes()
    {
        register_rest_route(
            $this->namespace,
            $this->addDemoInstructors,
            array(
                array(
                    'methods' => WP_REST_Server::CREATABLE,
                    'callback' => array($this, 'add_demo_instructors'),
                    //'permission_callback' => array($this, 'check_access_permissions_check'),
                    'permission_callback' => array($this, 'add_demo_instructors_permissions_check'),
                    'args' => array(),
                ),
            )
        );

        register_rest_route(
            $this->namespace,
            $this->editDemoInstructors,
            array(
                array(
                    'methods' => WP_REST_Server::EDITABLE,
                    'callback' => array($this, 'add_demo_instructors'),
                    //'permission_callback' => array($this, 'check_access_permissions_check'),
                    'permission_callback' => array($this, 'add_demo_instructors_permissions_check'),
                    'args' => array(),
                ),
            )
        );

        register_rest_route(
            $this->namespace,
            $this->getDemoInstructors,
            array(
                array(
                    'methods' => WP_REST_Server::READABLE,
                    'callback' => array($this, 'get_demo_instructors'),
                    'permission_callback' => array($this, 'check_access_permissions_check'),
                    'args' => array(),
                ),
            )
        );

        register_rest_route(
            $this->namespace,
            $this->getOrgAcademies,
            array(
                array(
                    'methods' => WP_REST_Server::CREATABLE,
                    'callback' => array($this, 'get_org_academies'),
                    'permission_callback' => array($this, 'check_access_permissions_check'),
                    'args' => array(),
                ),
            )
        );
    }

    /**
     * Token authorization check
     * This function common for all post login apis
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {
        return true;
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            return is_user_logged_in(); // this is for postman
        }
        $codes = error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }

    /**
     * Add demo instructors academy
     */
    public function add_demo_instructors($request)
    {
        global $wpdb;
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);

        // Required data from the payload
        $org_id = (int) $data['org']['id'];
        $org_admin_id = $data['org_admin']['id'];
        $academy_id = $data['academy']['id'];
        $categories = []; // Array of categories (slugs)
        $instructors = [];

        //$instructors = $data['instructors']; // Array of instructors

        foreach ($data['category'] as $cat) {
            $categories[] = $cat['slug'];
        }

        // Output the array as a JSON string
        //$categories = json_encode($category);

        foreach ($data['instructors'] as $instructor_data) {
            $instructors[] = $instructor_data['id'];
        }

        //$instructors = json_encode($instructor);

        // Validations for required fields
        if (empty($org_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose ORG', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate org_id exists and is of custom post type 'org'
        $org_post = get_post($org_id);
        if (!$org_post || $org_post->post_type !== 'org') {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG ID or not a org', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($org_admin_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose valid ORG Admin', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate org_admin_id exists and is linked with org_id
        $org_admin_user = get_user_by('ID', $org_admin_id);
        if (!$org_admin_user || !in_array('um_org-admin', $org_admin_user->roles)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG Admin assigned', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Check if org_admin_id is linked to the org_id
        $org_admin_organisations = maybe_unserialize(get_user_meta($org_admin_id, 'organisation', true));

        if (!is_array($org_admin_organisations) || !in_array($org_id, $org_admin_organisations)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'ORG Admin is not linked to the specified ORG ID', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($academy_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Academy ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $academy_post = get_post($academy_id);
        if (!$academy_post || $academy_post->post_type !== 'academy') {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid Academy ID or not a academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Fetch the serialized Category_Tree_Structure meta value
        $serialized_category_structure = get_post_meta($academy_id, 'Category_Tree_Structure', true);
        $category_structure = maybe_unserialize($serialized_category_structure);

        // Ensure that we have a valid unserialized structure
        if (empty($category_structure)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category tree structure not found for this academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Call the function to search only main categories (categories with parent_id = 0)
        if (!$this->find_main_categories_in_structure($category_structure, $categories)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'None of the main categories are linked with the academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate that instructors is a non-empty array of integers
        if (!is_array($instructors) || empty($instructors)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructors should be a non-empty array of integers', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        } else {

            foreach ($instructors as $instructor_id) {
                // Check if the user exists and has the 'instructor' role
                $user = get_user_by('ID', $instructor_id);
                if ($user && in_array('um_instructor', $user->roles)) {
                    $valid_instructors[] = $instructor_id; // Add to valid instructors list
                } else {
                    // If an instructor is invalid, throw an exception
                    //throw new Exception("Instructor with ID {$instructor_id} is not valid or does not have the instructor role", $this->codes["POST_INSERT_FAIL"]["code"]);
                    return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructor with ID is not valid or does not have the instructor role', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
                }
            }
        }

        // Fetch existing demo instructors from post meta
        // Fetch existing demo instructors from post meta
        $existing_data = get_post_meta($academy_id, 'demo_instructors', true);

        // If no existing data (first time), initialize the structure
        if (empty($existing_data)) {
            $existing_data = [];
        }

        // Initialize the structure to hold the category and instructors mapping
        $demo_instructors_structure = $existing_data; // Start with existing data

        // Loop through each category in the payload
        foreach ($categories as $category) {
            // Ensure the category is a valid slug
            $category_slug = sanitize_title($category); // Converts to a slug if not already
            // Find if the category already exists in demo_instructors
            $category_found = false;

            foreach ($demo_instructors_structure as &$existing_category_data) {
                // If the category exists, update its instructors
                if (isset($existing_category_data[$category_slug])) {
                    $existing_instructors = $existing_category_data[$category_slug]['instructor'];
                    $category_found = true;

                    // Merge new instructors with existing ones, avoiding duplicates
                    $merged_instructors = array_unique(array_merge(
                        array_column($existing_instructors, 'id'), // Existing instructor IDs
                        $instructors // New instructor IDs
                    ));

                    // Update the instructors for the category
                    $existing_category_data[$category_slug]['instructor'] = array_map(function ($id) {
                        return ['id' => $id];
                    }, $merged_instructors);
                }
            }

            // If the category is not found, add it as a new category with instructors
            if (!$category_found) {
                $demo_instructors_structure[] = [
                    $category_slug => [
                        'instructor' => array_map(function ($id) {
                            return ['id' => $id];
                        }, array_unique($instructors)), // Ensure unique instructor IDs
                    ],
                ];
            }
        }

        // Update the post meta with the new structure
        update_post_meta($academy_id, 'demo_instructors', $demo_instructors_structure);

        $post = get_post($academy_id);

        // Get all categories attached to this academy under 'course_category'
        $categories = wp_get_post_terms($academy_id, 'course_category');

        $categoryArray = array_map(function ($cat) {
            $featuredImageUrl = get_term_meta($cat->term_id, 'featured_image', true); // stored as URL
            return [
                "name" => $cat->name,
                "slug" => $cat->slug,
                "featured_image" => $featuredImageUrl,
                "id" => $cat->term_id,
            ];
        }, $categories);

        // Get logo and banner image URLs
        $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($academy_id), 'full');
        $bannerAttachmentId = get_post_meta($academy_id, "bannerImage_id", true);
        $banner_image = wp_get_attachment_image_src($bannerAttachmentId, 'full');

        // Elasticsearch data update (optional if needed)
        $curlPost = [
            "data" => [
                "details" => [
                    "record_id" => $academy_id,
                    "update_event_type" => "academies",
                    "id" => $academy_id,
                    "org_id" => $org_id,
                    "academy_name"   => get_the_title($academy_id),
                    "excerpt"        => $post->post_excerpt,
                    "description"    => $post->post_content,
                    "logo"           => $featured_image[0] ?? '',
                    "banner_image"   => $banner_image[0] ?? '',
                    "category"       => $categoryArray,
                    "open_schedule"  => get_post_meta($academy_id, 'open_schedule', true),
                    "url"            => get_permalink($academy_id),
                    "status"         => $post->post_status,
                    "demo_instructors" => $demo_instructors_structure,
                    "published_at" => get_the_time('M j, Y g:i A', $academy_id),
                ],
                "@timestamp" => date("Y-m-d H:i:s"),
            ],
        ];

        // Send data to Elasticsearch (if you have an update function)
        $this->update_in_academies('academies', $academy_id, $curlPost);

        // Return a success response
        $result = array(
            'code' => $codes["PUT_UPDATE"]["code"],
            'message' => str_replace("[Module_Name]", "Academy Demo Instructors", $codes["PUT_UPDATE"]["message"]),
            'status' => $codes["PUT_UPDATE"]["code"],
            //'data' => array('status' => $codes["PUT_UPDATE"]["code"], 'id' => $academy_id)
        );
        return new WP_REST_Response($result, 200);
    }
    public function add_demo_instructors_old($request)
    {
        global $wpdb;
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);

        // Required data from the payload
        $org_id = (int) $data['org_id'];
        $org_admin_id = $data['org_admin_id'];
        $academy_id = $data['academy_id'];
        $categories = $data['category']; // Array of categories (slugs)
        $instructors = $data['instructors']; // Array of instructors

        // Validations for required fields
        if (empty($org_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose ORG', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate org_id exists and is of custom post type 'org'
        $org_post = get_post($org_id);
        if (!$org_post || $org_post->post_type !== 'org') {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG ID or not a org', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($org_admin_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Choose valid ORG Admin', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate org_admin_id exists and is linked with org_id
        $org_admin_user = get_user_by('ID', $org_admin_id);
        if (!$org_admin_user || !in_array('um_org-admin', $org_admin_user->roles)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid ORG Admin assigned', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Check if org_admin_id is linked to the org_id
        $org_admin_organisations = maybe_unserialize(get_user_meta($org_admin_id, 'organisation', true));

        if (!is_array($org_admin_organisations) || !in_array($org_id, $org_admin_organisations)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'ORG Admin is not linked to the specified ORG ID', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($academy_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Academy ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $academy_post = get_post($academy_id);
        if (!$academy_post || $academy_post->post_type !== 'academy') {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Invalid Academy ID or not a academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Fetch the serialized Category_Tree_Structure meta value
        $serialized_category_structure = get_post_meta($academy_id, 'Category_Tree_Structure', true);
        $category_structure = maybe_unserialize($serialized_category_structure);

        // Ensure that we have a valid unserialized structure
        if (empty($category_structure)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category tree structure not found for this academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Call the function to search only main categories (categories with parent_id = 0)
        if (!$this->find_main_categories_in_structure($category_structure, $categories)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'None of the main categories are linked with the academy', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate that instructors is a non-empty array of integers
        if (!is_array($instructors) || empty($instructors) || !array_filter($instructors, 'is_int')) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructors should be a non-empty array of integers', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        } else {

            foreach ($instructors as $instructor_id) {
                // Check if the user exists and has the 'instructor' role
                $user = get_user_by('ID', $instructor_id);
                if ($user && in_array('um_instructor', $user->roles)) {
                    $valid_instructors[] = $instructor_id; // Add to valid instructors list
                } else {
                    // If an instructor is invalid, throw an exception
                    //throw new Exception("Instructor with ID {$instructor_id} is not valid or does not have the instructor role", $codes["POST_INSERT_FAIL"]["code"]);
                    return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Instructor with ID is not valid or does not have the instructor role', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
                }
            }
        }

        // Fetch existing demo instructors from post meta
        $existing_data = get_post_meta($academy_id, 'demo_instructors', true);

        // If no existing data (first time), initialize the structure
        if (empty($existing_data)) {
            $existing_data = [];
        }

        // Initialize the structure to hold the category and instructors mapping
        $demo_instructors_structure = $existing_data; // Start with existing data

        // Loop through each category in the payload
        foreach ($categories as $category) {
            // Ensure the category is a valid slug
            $category_slug = sanitize_title($category); // Converts to a slug if not already

            // Find if the category already exists in demo_instructors
            $existing_instructors_for_category = [];
            $category_found = false;

            foreach ($demo_instructors_structure as &$existing_category_data) {
                if (isset($existing_category_data[$category_slug])) {
                    $existing_instructors_for_category = $existing_category_data[$category_slug];
                    $category_found = true;

                    // Merge new instructors with existing ones
                    $merged_instructors = array_unique(array_merge($existing_instructors_for_category, $instructors));

                    // Update the instructors for the category
                    $existing_category_data[$category_slug] = $merged_instructors;
                }
            }

            // If the category is not found, add it as new
            if (!$category_found) {
                $demo_instructors_structure[] = [
                    $category_slug => array_unique($instructors),
                ];
            }
        }

        // Update the post meta with the new structure
        update_post_meta($academy_id, 'demo_instructors', $demo_instructors_structure);

        // Elasticsearch data update (optional if needed)
        $curlPost = [
            "data" => [
                "details" => [
                    "record_id" => $academy_id,
                    "update_event_type" => "academies",
                    "id" => $academy_id,
                    "org_id" => $org_id,
                    "demo_instructors" => $demo_instructors_structure,
                    "published_at" => get_the_time('M j, Y g:i A', $academy_id),
                ],
                "@timestamp" => date("Y-m-d H:i:s"),
            ],
        ];

        // Send data to Elasticsearch (if you have an update function)
        $this->update_in_academies('academies', $academy_id, $curlPost);

        // Return a success response
        $result = array(
            'code' => $codes["PUT_UPDATE"]["code"],
            'message' => str_replace("[Module_Name]", "Academy Demo Instructors", $codes["PUT_UPDATE"]["message"]),
            'status' => $codes["PUT_UPDATE"]["code"],
            //'data' => array('status' => $codes["PUT_UPDATE"]["code"], 'id' => $academy_id)
        );
        return new WP_REST_Response($result, 200);
    }

    // update academies for particular academy_id
    public function update_in_academies($event_type, $id, $data)
    {
        // Ensure the ID is not empty.
        date_default_timezone_set('Asia/Calcutta');
        if (!empty($id)) {
            $param = $event_type . "/_doc/" . $event_type . "-" . $id; // Assuming the ID is unique and no need to prefix with event_type
            //echo $param;
        } else {
            //echo "ID is empty, cannot update document.";
            return;
        }

        $es_url = ELASTIC_SEARCH_END_URL . "/" . $param;

        // Initialize cURL
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $es_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'PUT', // This method will replace the entire document at the ID
            CURLOPT_POSTFIELDS => json_encode($data), // Encode data array to JSON
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json', // Correct header format
                'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION, // Correct header format
            ),
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($response === false) {
            error_log('Curl error: ' . curl_error($curl));
            return false;
        }
        error_log('Elasticsearch data to be updated: ' . json_encode($data), 3, ABSPATH . "error-logs/ScriptController.log");

        $responseArray = json_decode($response, true);
        error_log('Elasticsearch response: ' . json_encode($responseArray), 3, ABSPATH . "error-logs/ScriptController.log");
        if (isset($responseArray['error'])) {
            error_log('Elasticsearch error: ' . json_encode($responseArray['error']));
            error_log('Elasticsearch error: ' . json_encode($responseArray['error']), 3, ABSPATH . "error-logs/ScriptController.log");
            return false;
        }

        if ($httpCode >= 200 && $httpCode <= 299 && ($responseArray['result'] == 'updated' || $responseArray['result'] == 'created')) {
            return true; // Success, the document was either updated or created
        }

        return false; // Failure or no update needed
    }

    public function add_demo_instructors_permissions_check()
    {
        return true;
    }

    // Function to find categories with parent_id = 0
    public function find_main_categories_in_structure($category_tree, $slugs_to_find)
    {
        $matched_slugs = [];

        foreach ($category_tree as $category) {
            if (isset($category['slug']) && in_array($category['slug'], $slugs_to_find)) {
                $matched_slugs[] = $category['slug'];
            }
        }

        return count($matched_slugs) === count($slugs_to_find);
    }

    public function find_main_categories_in_structureOLD($category_tree, $slugs_to_find)
    {
        $main_categories = [];

        // Traverse the category structure and collect main categories (parent_id = 0)
        foreach ($category_tree as $category) {
            if (isset($category['parent_id']) && $category['parent_id'] == 0) {
                if (isset($category['slug']) && in_array($category['slug'], $slugs_to_find)) {
                    $main_categories[] = $category['slug'];
                }
            }
        }

        // Return true if all slugs were found in main categories, otherwise false
        return count($main_categories) === count($slugs_to_find);
    }

    public function get_demo_instructors($request)
    {
        global $wpdb;
        $codes = error_code_setting();

        // Get required fields from the request
        $org_id = (int) $request['org_id'];
        $org_admin_id = $request['org_admin_id'];
        $academy_id = $request['academy_id'];

        // Validate required fields
        if (empty($org_id) || empty($org_admin_id) || empty($academy_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Required fields missing', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Fetch academy data from Elasticsearch using the cURL method
        $academy_data = $this->fetchDocumentById('academies', $academy_id); // Assume 'academies' is the index name

        if (!$academy_data) {
            return new WP_Error($codes["API_FAIL"]["code"], 'Academy not found in Elasticsearch', array('status' => $codes["API_FAIL"]["status"]));
        }

        // Initialize arrays for categories and instructors
        $categories = [];
        $instructors = [];

        // Loop through the demo instructors from the Elasticsearch data
        foreach ($academy_data['demo_instructors'] as $category_data) {
            foreach ($category_data as $category_slug => $instructors_array) {
                // Add the category slug to the $categories array
                $categories[] = ['slug' => $category_slug];

                // Extract instructor IDs and add them to the $instructors array
                foreach ($instructors_array['instructor'] as $instructor_data) {
                    $instructors[] = ['id' => $instructor_data['id']];
                }
            }
        }

        // Remove any duplicate instructors by their IDs
        $instructors = array_map("unserialize", array_unique(array_map("serialize", $instructors)));

        // Output the final result

        // Return the formatted response
        $result = array(
            'code' => $codes["GET_SUCCESS"]["code"],
            //'message' => $codes["GET_SUCCESS"]["message"],
            'message' => str_replace("[Module_Name]", "Demo Instructors", $codes["GET_SUCCESS"]["message"]),
            'data' => array(
                'org_admin' => ["id" => (int) $org_admin_id],
                'org' => ["id" => (int) $org_id],
                'academy' => ["id" => (int) $academy_id],
               // 'category' => $categories,
                //'instructors' => $instructors,
                'demo_instructors' => $academy_data['demo_instructors'], // Include demo instructors
            ),
        );

        return new WP_REST_Response($result, 200);
    }

    // Fetch document by ID from Elasticsearch
    public function fetchDocumentById($indexName, $documentId)
    {
        // Elasticsearch URL configuration
        $es_url = ELASTIC_SEARCH_END_URL . "/" . $indexName . "/_doc/" . $indexName . "-" . $documentId;

        // Initialize cURL session
        $curl = curl_init($es_url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache",
            'Content-Type: application/json',
        ));

        // Execute the cURL session
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        // Check for errors and handle the response
        if ($err) {
            echo "cURL Error #:" . $err;
            return null;
        } else {
            $decodedResponse = json_decode($response, true);

            // Check if the document is found
            if (isset($decodedResponse['found']) && $decodedResponse['found'] === true) {
                $sourceData = $decodedResponse['_source']; // Get source of the document
                $details = $sourceData['data']['details']; // Extract 'details' section of the data

                // Prepare and return the results
                $results = [];
                $results['academy_id'] = (int) $details['record_id'];
                $results['org_admin_id'] = (int) $details['user_id'];
                $results['org_id'] = (int) $details['org_id'];
                $results['demo_instructors'] = $details['demo_instructors']; // Fetch demo instructors

                return $results; // Return the necessary details
            } else {
                return null; // Document not found
            }
        }
    }

    public static function get_enrollments_for_academy($org_id)
    {

        try {
            // Query Elasticsearch to retrieve the plan
            $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search?size=" . ELASTIC_RECORDS_COUNT;
            $headers = [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
            ];
            $curlPost = [
                "query" => [
                    "bool" => [
                        "must" => [
                            [
                                "match_phrase" => [
                                    "data.details.org_admin.id" => $org_id,
                                ]
                            ]
                        ],
                        "must_not" => [
                            [
                                "match" => [
                                    "data.details.course_name" => false,
                                ],
                            ],
                        ],
                    ],
                ],
                "aggs" => [
                    "enrollment_status_count" => [
                        "terms" => [
                            "field" => "data.details.enrollment_status.keyword"
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.enrollment_status",
                ],
            ];

            $return = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);

            if ($return['success'] === true) {
                $records = json_decode($return['response']);
                return ["inactive_enrollments" => $records->aggregations->enrollment_status_count->buckets[1]->doc_count, "active_enrollments" => $records->aggregations->enrollment_status_count->buckets[0]->doc_count];
            }
        } catch (Exception $e) {
            // Handle the exception and return an error response
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            self::logger->custom_log('info', "Org Academies", 'get_enrollments_for_academy', $message, [], []);
            $response = array(
                'code' => $codes["POST_INSERT_FAIL"]["code"],
                'message' => $message,
                'status' => $codes["POST_INSERT_FAIL"]["status"],
            );
            return rest_ensure_response($response);
        }
    }
    /**
     * Retrieves the academies for a given org id or ids.
     * This function validates the request parameters and retrieves the academies for the specified
     * orgs.It queries *elasticsearch to retrieve the data and returns a response indicating
     * the success or failure of the operation.
     * @param WP_REST_Request $request The REST request object.
     * @throws WP_Error If the request validation fails or an exception occurs.
     * @return WP_REST_Response|WP_Error The response object or error object indicating the success or failure of the operation.
     */
    public function get_org_academies($request)
    {
        $codes = error_code_setting();
        // Decode JSON
        $request_data = json_decode($request->get_body(), true);
        // Retrieve request parameters
        $org_ids = $request_data['org_ids'];
        $offset = (int) $request_data['offset'];
        $limit = (int) $request_data['limit'];
        $category = $request_data['category'] ?? 0;
        $data = ["org_ids" => $org_ids];

        // Define validation checks
        $validation_checks = [
            'org_ids' => 'post_id',
        ];

        // Validate each field using the common functions
        $errors = [];
        foreach ($validation_checks as $key => $type) {
            $result = Utility::validate_required($data, $key, $type, false);
            if (is_wp_error($result)) {
                $errors[] = $result;
            }
        }

        try {
            if (!empty($errors)) {
                // Return error response if validation fails
                foreach ($errors as $error) {
                    $error_message = $error->get_error_message() . "\n";
                    $response = [
                        'code' => $codes["POST_INSERT_FAIL"]["code"],
                        'message' => $error_message,
                        'status' => $codes["POST_INSERT_FAIL"]["status"],
                    ];
                    return rest_ensure_response($response);
                }
            } else {
                $first_body['_source'] = [
                    "data.details.academy_name",
                    "data.details.org_id",
                    "data.details.record_id",
                    "data.details.logo",
                    "data.details.banner_image",
                    "data.details.excerpt",
                    "data.details.description",
                ];
                if (!empty($org_ids)) {
                    $first_body["query"] = [
                        "terms" => [
                            "data.details.org_id" => $org_ids,
                        ],
                    ];
                    if ($category > 0) {
                        $first_body["query"] = [
                            "bool" => [
                                "must" => [
                                    [
                                        "terms" => [
                                            "data.details.org_id" => $org_ids,
                                        ],
                                    ],
                                    [
                                        "script" => [
                                            "script" => [
                                                "source" => "if (doc.containsKey('data.details.category.id') && doc['data.details.category.id'].size() > 0) { for (cat in doc['data.details.category.id']) { if (cat == params.category_id) { return true; } } } return false;",
                                                "params" => ["category_id" => $category],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ];
                    }
                }
                $first_body['_source'] = [
                    "data.details.academy_name",
                    "data.details.org_id",
                    "data.details.record_id",
                    "data.details.logo",
                    "data.details.banner_image",
                    "data.details.excerpt",
                    "data.details.description",
                ];
                $first_body["size"] = $limit;
                $first_body["from"] = $offset;
                $first_body["sort"] = [
                    [
                        "_script" => [
                            "type"   => "string",
                            "script" => [
                                "lang"   => "painless",
                                "source" => "doc['data.details.academy_name.keyword'].size() != 0 ? doc['data.details.academy_name.keyword'].value.toLowerCase() : ''"
                            ],
                            "order"  => "asc"
                        ]
                    ]
                ];
                if (!empty($org_ids)) {
                    $second_body["query"] = [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "terms" => [
                                    "data.details.record_id" => $org_ids,
                                ],
                            ],
                        ],
                    ];
                }
                $second_body['_source'] = [
                    "data.details.record_id",
                    "data.details.organisation_name",
                    "data.details.logo_image",
                    "data.details.fav_icon",
                ];
                $second_body["size"] = ELASTIC_RECORDS_COUNT;
                if (!empty($org_ids)) {
                    $third_body["query"] = [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "terms" => [
                                    "data.details.org_id" => $org_ids,
                                ],
                            ],
                        ],
                    ];
                }
                $third_body['_source'] = [
                    "data.details.record_id",
                    "data.details.org_id",
                    "data.details.mapped_instructor_ids",
                    "data.details.active_enrollments",
                ];
                $third_body["size"] = ELASTIC_RECORDS_COUNT;
                // Prepare _msearch queries
                $msearch_queries = [
                    // Query 1: Search in academies index
                    [
                        'header' => ['index' => 'academies'],
                        'body' => $first_body,
                    ],
                    // Query 2: Search in org index with user_id
                    [
                        'header' => ['index' => 'org'],
                        'body' => $second_body,
                    ],
                    // Query 3: Search in course index with user_id
                    [
                        'header' => ['index' => 'course'],
                        'body' => $third_body,
                    ],
                ];
                // Convert queries array into msearch format
                $msearch_query = "";
                foreach ($msearch_queries as $query) {
                    $msearch_query .= json_encode($query['header']) . "\n" . json_encode($query['body']) . "\n";
                }
                $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/_msearch";
                $headers = [
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                ];

                $return = Utility::curl_request($elasticsearch_url, 'POST', $msearch_query, $headers, ELASTIC_SEARCH_PORT);
                if ($return['success'] === true) {
                    $results = json_decode($return['response']);
                    // Parsing the _msearch results
                    $totals = $results->responses[0]->hits->total->value;
                    $academies_data = $results->responses[0]->hits->hits; // Data from academies index
                    $org_data = $results->responses[1]->hits->hits; // Data from org index
                    $course_data = $results->responses[2]->hits->hits; // Data from course index
                    $details = [];
                    foreach ($academies_data as $result) {
                        $academy_name = $result->_source->data->details->academy_name;
                        $org_id = (int) $result->_source->data->details->org_id;
                        $academy_id = (int) $result->_source->data->details->record_id;
                        $academy_logo = $result->_source->data->details->logo;
                        $excerpt = $result->_source->data->details->excerpt;
                        $description = $result->_source->data->details->description;
                        $org = [
                            "id" => 0,
                            "name" => "",
                            "logo" => "",
                            "fav_icon" => "",
                        ];
                        // Find the matching org from the second index
                        foreach ($org_data as $org_detail) {
                            if ($org_detail->_source->data->details->record_id == $org_id) {
                                $org = [
                                    "id" => (int) $org_detail->_source->data->details->record_id,
                                    "name" => $org_detail->_source->data->details->organisation_name ?? "",
                                    "logo" => $org_detail->_source->data->details->logo_image ?? "",
                                    "fav_icon" => $org_detail->_source->data->details->fav_icon ?? "",
                                ];
                            }
                        }
                        // Find the matching course from the third index
                        $courses_count = 0;
                        $mapped_instructors_counter = [];
                        $course_ids = [];
                        foreach ($course_data as $course_detail) {
                            if ($course_detail->_source->data->details->org_id == $org_id) {
                                $courses_count++;
                                if (is_array($course_detail->_source->data->details->mapped_instructor_ids) || $course_detail->_source->data->details->mapped_instructor_ids instanceof Countable) {
                                    $mapped_instructors = $course_detail->_source->data->details->mapped_instructor_ids;
                                    foreach ($mapped_instructors as $mapped_instructor) {
                                        $mapped_instructors_counter[] = $mapped_instructor;
                                    }
                                }

                                $active_learners_count = $course_detail->_source->data->details->active_enrollments ?? 0;
                                $course_ids[] = $course_detail->_source->data->details->record_id;
                            }
                        }
                        $learners_count = self::get_enrollments_for_academy($org_id);
                        $mapped_instructors_count = count(array_unique($mapped_instructors_counter));
                        $details[] = [
                            "name" => $academy_name ?? "",
                            "id" => $academy_id,
                            "logo_url" => $academy_logo ?? "",
                            "fav_icon_url" => $fav_icon_url ?? "",
                            "excerpt" => $excerpt ?? "",
                            "description" => $description ?? "",
                            "is_active" => false,
                            "org" => $org,
                            "courses_count" => (int) $courses_count ?? 0,
                            "mapped_instructors_count" => (int) $mapped_instructors_count ?? 0,
                            "active_learners_count" => (int) $learners_count['inactive_enrollments'] ?? 0,
                            "past_learners_count" => (int) $learners_count['active_enrollments'] ?? 0,
                        ];
                    }
                    if (empty($details)) {
                        $response = [
                            'code' => $codes["POST_INSERT_FAIL"]["code"],
                            'message' => $codes["SEARCH_FAIL"]["message"],
                            'status' => $codes["POST_INSERT_FAIL"]["status"],
                        ];
                        return rest_ensure_response($response);
                    }

                    $result = array(
                        'code' => $codes["POST_INSERT"]["code"],
                        'message' => str_replace("[Module_Name]", "Academies", $codes["GET_SUCCESS"]["message"]),
                        "status" => $codes["POST_INSERT"]["status"],
                        "count" => $totals,
                        'data' => $details,
                    );
                    return new WP_REST_Response($result, 200);
                }
                $response = array(
                    'code' => $codes["POST_INSERT_FAIL"]["code"],
                    'message' => $return['error'],
                    'status' => $codes["POST_INSERT_FAIL"]["status"],
                );
                return rest_ensure_response($response);
            }
        } catch (Exception $e) {
            // Handle the exception and return an error response
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $this->logger->custom_log('info', "Org Academies", 'get_org_academies', $message, [], []);
            $response = array(
                'code' => $codes["POST_INSERT_FAIL"]["code"],
                'message' => $message,
                'status' => $codes["POST_INSERT_FAIL"]["status"],
            );
            return rest_ensure_response($response);
        }
    }
    public function get_academy_from_elasticsearch() {}
}
