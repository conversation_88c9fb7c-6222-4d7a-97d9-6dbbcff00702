Vue.component('yuno-login-step', {
    props: ["data", "options", "isOrg"],
    template: `
    
        <section class="loginStep">
            <div class="container">
                <div class="columns is-mobile is-centered">
                    <div class="column is-three-fifths-desktop is-full-mobile">
                        <div class="card">
                            <template v-if="isOrg && orgAdmin.loading">
                                <b-skeleton height="355px"></b-skeleton>
                            </template>
                            <template v-else>
                                <validation-observer 
                                    tag="div" 
                                    class="observer loginObserver"
                                    ref="commonSignupObserver" 
                                    v-slot="{ handleSubmit, invalid }">
                                    <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">
                                        <div class="card-content">
                                            <div class="content">
                                                <h1 class="largestTitle">{{ data.title }}</h1>
                                                <h2 class="largeTitle">{{ data.subTitle }}</h2>
                                                <div class="columns loginFieldWrapper">
                                                    <div class="column is-full-mobile alignC">
                                                        <div class="ratingWrapper">
                                                            <figure>
                                                                <img :src="googleIcnURL" alt="google"></img>
                                                            </figure>
                                                            <div class="ratingInfo">
                                                                <div class="stars">
                                                                    <span v-for="(star, i) in 5" class="material-icons">star_rate</span>
                                                                </div>
                                                                <h3 class="smallCaption dark60">
                                                                    {{ ratingCaption }}
                                                                    <span class="body1">{{ ratingSubCaption }}</span>
                                                                </h3>
                                                            </div>
                                                        </div>
                                                        <div class="instrutorList" v-if="false">
                                                            <div class="list">
                                                                <span v-for="(item, i) in 7" class="material-icons">
                                                                    account_circle
                                                                </span>
                                                            </div>
                                                            <div class="numbers">{{ instructorCaption }}</div>
                                                        </div>
                                                    </div>
                                                    <div class="column is-three-fifths-desktop is-full-mobile loginWrapper">
                                                        <b-field label="Phone Number">
                                                            <validation-provider 
                                                                :customMessages="{ required: 'Phone number is required'}" 
                                                                tag="div" 
                                                                :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" 
                                                                v-slot="{ errors, classes }"
                                                                class="fieldWrapper"
                                                            >
                                                                <b-input 
                                                                    placeholder="Enter your phone number" 
                                                                    :class="classes" 
                                                                    inputmode="numeric"
                                                                    pattern="[0-9]*"
                                                                    type="text"
                                                                    v-model="signIn.mobile"
                                                                >
                                                                </b-input>
                                                                <b-button 
                                                                    v-if="user.isLoggedin" 
                                                                    class="updateNumber"
                                                                >
                                                                    <span class="material-icons">edit</span>
                                                                </b-button>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>    
                                                        </b-field>
                                                        <footer class="card-footer googleWrapper">
                                                            <b-button v-if="!user.isLoggedin" class="card-footer-item hasGoogle" native-type="submit">
                                                                <img :src="googleIcnURL" alt="google"></img> Sign up with Google
                                                            </b-button>
                                                            <b-button v-else class="card-footer-item primary" native-type="submit" :loading="form.loading" :disabled="form.loading">
                                                                Next
                                                            </b-button>
                                                        </footer>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </validation-observer>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    
    `,
    data() {
        return {
            transitionName: 'slide-left',
            ratingCaption: "Rated 5-star on Google",
            ratingSubCaption: "10,000 successful students",
            instructorCaption: "Choose from 216 instructors",
            googleIcnURL: this.$store.state.themeURL + "/assets/images/google.svg",
            signIn: {
                mobile: "",
                categoryURL: "",
                productCode: "",
                leadStatus: "",
                variant: "",
                utmSource: "",
                utmCampaign: "",
                utmMedium: "",
                adGroupID: "",
                adContent: "",
                utmTerm: "",
                gclid: "",
                content: {
                    type: "",
                    id: ""
                },
                landing_page: {
                    url: "",
                    title: ""
                },
                org_details: {
                    type: "login",
                    org_id: "",
                    org_url: "",
                    phone: ""
                },
                login_details: {
                    role: ""
                }
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'subform2',
            'form',
            'categoryList',
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    mounted() {
        this.preFillForm()
    },
    methods: {
        nextStep() {
            if (this.form.data.form_fields.length !== 0) {
                Event.$emit('stepsBegins');    
            } else {
                const category = YUNOCommon.findObjectByKey(this.categoryList.data, "categorySlug", this.$props.options.category),
                    courseSearchURL = "/search/?state=%7B%22category%22:%5B"+ category.category_id +"%5D,%22category_level_1%22:%5B%5D,%22category_level_2%22:%5B%5D,%22class_days_time%22:%5B%7B%22items%22:%5B%5D,%22slug%22:%22class_days%22%7D,%7B%22items%22:%5B%5D,%22slug%22:%22class_time%22%7D%5D%7D";

                setTimeout(() => {
                    window.location.href = courseSearchURL;    
                }, 50);
            };
        },
        preFillForm() {
            if (this.user.isLoggedin) {
                this.signIn.mobile = this.userInfo.data.mobile;
            }

            if (this.$props.isOrg) {
                const orgID = YUNOCommon.getQueryParameter("org_id");
                const currentDateTime = this.formatDateTime(new Date());
                const orgId = "" + orgID + "@@@" + currentDateTime + "";
                this.signIn.org_details.org_id = this.utf8ToBase64(orgId);
            };
        },
        mobileUpdated(options, debug) {
            this.subform2.isLoading = false;

            const response = options?.response?.data;

            if (response?.code === 201) {
                this.showNotification(response.message, 'is-bottom', 'is-black');
                debug ? console.log(response.message) : "";
                
            } else if (response?.message) { 
                debug ? console.log(response.message) : "";
            }
        },
        showNotification(message, position, type = '') { 
            this.$buefy.toast.open({
                duration: 5000, // Notification duration in milliseconds
                message: `${message}`, // Notification message
                position: position, // Position of the notification
                type: type // Type of the notification (e.g., 'is-danger' for errors)
            });
        },
        updateMobile(debug) {
            this.subform2.isLoading = true;

            const payload = {
                "phone": this.signIn.mobile,
                "cta": "book_a_demo",
                "course": YUNOCommon.getQueryParameter("course_id") !== false ? YUNOCommon.getQueryParameter("course_id") : 0
            }

            const options = {
                apiURL: YUNOCommon.config.leadForm("postStep", isLoggedIn, this.$props.options.category), 
                module: "gotData", 
                store: "subform2", 
                payload: payload, 
                callback: true, 
                callbackFunc: (options) => this.mobileUpdated(options, debug)
            };

            this.$store.dispatch('postData', options);
        },
        initCommonSignup() {
            if (this.user.isLoggedin) {
                this.updateMobile();
                setTimeout(() => {
                    this.nextStep();    
                }, 50);
                
            } else {
                this.setState();
            }
        },
        addQueryParamUsingURLSearchParams(url, key, value) {
            let urlObj = new URL(url);
            // Append or overwrite the query parameter
            urlObj.searchParams.set(key, value);
            // Return the updated URL string
            return urlObj.toString();
        },
        setSigninProps() {
            let currentURL = window.location.origin + window.location.pathname + window.location.search,
                updatedURL = this.addQueryParamUsingURLSearchParams(currentURL, "stepsBegin", true);

            localStorage.setItem('userState', updatedURL);
            localStorage.setItem("isQuiz", true);
            localStorage.setItem('skipSignUp', true);
        },
        setPayload() {
            let payload = this.signIn;

            payload.categoryURL = this.$props.options.category;
            payload.landing_page.url = window.location.origin + window.location.pathname;
            payload.landing_page.title = document.title;
            payload.productCode = "";
            payload.leadStatus = "";
            payload.utmSource = YUNOCommon.getQueryParameter("utm_source");
            payload.utmCampaign = YUNOCommon.getQueryParameter("utm_campaign");
            payload.utmMedium = YUNOCommon.getQueryParameter("utm_medium");
            payload.adGroupID = YUNOCommon.getQueryParameter("adgroupid");
            payload.adContent = YUNOCommon.getQueryParameter("ad_content");
            payload.utmTerm = YUNOCommon.getQueryParameter("utm_term");
            payload.gclid = YUNOCommon.getQueryParameter("gclid");
            payload.content.type = "book_a_demo";
            payload.content.id = "";
        },
        isFirefoxPrivate(is_private) {
            if (is_private !== null && is_private) {
                setTimeout(() => {  
                    this.setSigninProps();
                    this.setPayload();
                    window.location.href = YUNOCommon.config.signInURLWithState(this.signIn)
                }, 5000); 
            } else {
                this.setSigninProps();
                this.setPayload();
                setTimeout(() => { window.location.href = YUNOCommon.config.signInURLWithState(this.signIn) }, 50); 
            }
        },
        setState() {
            YUNOCommon.isPrivateWindow(this.isFirefoxPrivate);
        },
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
        
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        utf8ToBase64(str) {
            // First, encode the string as UTF-8
            const utf8EncodedString = encodeURIComponent(str);
        
            // Then, convert each character to a binary representation
            const binaryString = utf8EncodedString.replace(/%([0-9A-F]{2})/g, function(match, p1) {
                return String.fromCharCode('0x' + p1);
            });
        
            // Finally, convert to Base64
            return btoa(binaryString);
        },
    }
});