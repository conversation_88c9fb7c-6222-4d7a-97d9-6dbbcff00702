window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minium 10 numbers required",
        "max": "Maxium 15 numbers required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-batch-insights', {
    template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
        >
            <template v-slot:main>
                <div class="container-fluid">
                    <div class="mainHeader">
                        <div class="block">
                            <h1 class="pageTitle">{{ pageHeader.title }}</h1>
                            <div class="action" v-if="isElementVisible(userRole.data, pageHeader.button.role)">
                                <template v-if="pageHeader.button.type === 'custom'">
                                    <b-button 
                                        @click="pageCTA()"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                                <template v-else>
                                    <b-button tag="a"
                                        :href="pageHeader.button.url"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                            </div> 
                        </div>
                        <div class="filtersWrapper" v-if="!enabledFilters">
                            <b-skeleton width="100%" height="40px"></b-skeleton>
                        </div>
                        <yuno-filters v-if="enabledFilters" :data="filters"></yuno-filters>
                    </div>
                    <div class="filtersWrapper" v-if="!enabledFilters">
                        <ul>
                            <li>
                                <b-skeleton width="100" height="40px"></b-skeleton>
                            </li>
                            <li>
                                <b-skeleton width="100" height="40px"></b-skeleton>
                            </li>
                        </ul>
                    </div>
                    <yuno-tabs v-if="enabledTabs" :data="nestedTabs" :options="{ 'date':  dateRange, 'payload': payload}"></yuno-tabs>
                </div>
                <b-modal 
                    :active.sync="classSize.modal" 
                    :width="500" 
                    :can-cancel="['escape', 'x']"
                    :on-cancel="classSizeModalClose"
                    class="yunoModal lightTheme">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Choose Class Size</h2>
                        </div>
                        <validation-observer tag="div" ref="classSizeObserver" v-slot="{ handleSubmit }">
                            <form id="selectCourseForm" @submit.prevent="handleSubmit(initClassSize)">
                                <div class="modalBody">
                                    <div class="radioList groupElement">
                                        <small class="helper">Please choose one option</small>
                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                            <template v-for="(item, i) in classSize.items">
                                                <b-field class="colorGrey">
                                                    <b-radio v-model="classSize.selected"
                                                        name="classSize"
                                                        :native-value="item.slug">
                                                        {{item.label}}
                                                    </b-radio>
                                                </b-field>
                                            </template>
                                            <p class="error">{{errors[0]}}</p>
                                        </validation-provider>
                                    </div>
                                    <div class="ctaWrapper alignLeft">
                                        <b-button
                                            native-type="submit"
                                            class="yunoSecondaryCTA">
                                            Continue
                                        </b-button>
                                    </div>        
                                </div>
                            </form>
                        </validation-observer>
                </b-modal>
                <b-modal 
                    :active.sync="drawerModal.modal" 
                    :width="500" 
                    animation="slide-out"
                    :can-cancel="['escape', 'x']"
                    :on-cancel="closeDrawer"
                    class="yunoModal drawerModal">
                        <template v-if="drawer.loading">
                            <div class="loaderWrapper">
                                <div class="smallLoader"></div>
                            </div>
                        </template>
                        <template v-if="drawer.success && drawerModal.modal">
                            <template v-if="drawer.error">
                                <h2 class="drawerTitle">Active Learners</h2>
                                <yuno-empty-states :options="{'state': 'dataNotFound', 'description': drawer.errorData}"></yuno-empty-states>
                            </template>
                            <template v-else>
                                <div class="scrollable">
                                    <h2 class="drawerTitle">Active Learners ({{ drawer.data.length }})</h2>
                                    <div class="learners">
                                        <template v-for="(user, u) in drawer.data">
                                            <figure :key="u" class="userImg">
                                                <img :src="user.image_url" :alt="user.full_name">
                                                <figcaption>
                                                    <h3 class="primaryTitle">{{ user.full_name }}</h3>
                                                </figcaption>
                                            </figure>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </template>
                </b-modal>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            isMiniSidebar: false,
            enabledFilters: false,
            enabledTabs: false,
            classSize: {
                modal: false,
                selected: "",
                items: [
                    {
                        slug: "one_to_one",
                        label: "1-to-1"
                    },
                    {
                        slug: "one_to_many",
                        label: "Group (10 max.)"
                    }
                ]
            },
            drawerModal: {
                modal: false,
                data: []
            },
            pageHeader: {
                title: "Batches",
                button: {
                    label: "Create Batch",
                    type: "custom",
                    url: "/create-batch",
                    role: ["yuno-admin", "org-admin"]
                }
            },
            hideHeader: false,
            isScroll: false,
            authorizedRoles: [
                "yuno-admin",
                "Counselor",
                "org-admin"
            ],
            filters: [
                {
                    type: "search",
                    slug: "course_search",
                    items: [],
                    is_multiple: false,
                    is_highlighted: false,
                    is_active: true,
                    is_disabled: false,
                    is_focus: false,
                    selected: null,
                    current: "",
                    label: "Course",
                    placeholder: "Search Course"
                },
                {
                    type: "search",
                    slug: "search_instructor",
                    items: [],
                    is_multiple: false,
                    is_highlighted: false,
                    is_active: false,
                    is_disabled: true,
                    is_focus: false,
                    selected: null,
                    current: "",
                    label: "Instructor",
                    placeholder: "Search Instructor"
                },
                {
                    type: "groupMultiSelect",
                    slug: "class_days_time",
                    is_multiple: true,
                    items: [
                        {
                            slug: "class_days",
                            label: "Class Days",
                            "items": [
                                {
                                    "label": "Sun",
                                    "filter": "class_days_time",
                                    "slug": "sun",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Mon",
                                    "filter": "class_days_time",
                                    "slug": "mon",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Tue",
                                    "filter": "class_days_time",
                                    "slug": "tue",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Wed",
                                    "filter": "class_days_time",
                                    "slug": "wed",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Thu",
                                    "filter": "class_days_time",
                                    "slug": "thu",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Fri",
                                    "filter": "class_days_time",
                                    "slug": "fri",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Sat",
                                    "filter": "class_days_time",
                                    "slug": "sat",
                                    "parentSlug": "class_days",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": false
                                }
                            ]
                        },
                        {
                            "slug": "class_time",
                            "label": "Class Time",
                            "items": [
                                {
                                    "label": "Morning",
                                    "filter": "class_days_time",
                                    "slug": "morning",
                                    "parentSlug": "class_time",
                                    "interval": "6 - 12",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Afternoon",
                                    "filter": "class_days_time",
                                    "slug": "afternoon",
                                    "parentSlug": "class_time",
                                    "interval": "12 - 4",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": false
                                },
                                {
                                    "label": "Evening",
                                    "filter": "class_days_time",
                                    "slug": "evening",
                                    "parentSlug": "class_time",
                                    "interval": "4 - 8",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                },
                                {
                                    "label": "Night",
                                    "filter": "class_days_time",
                                    "slug": "night",
                                    "parentSlug": "class_time",
                                    "interval": "8 - 11",
                                    "isActive": true,
                                    "isChecked": false,
                                    "placeHolder": "Class Days & Time",
                                    "displayStatus": true
                                }
                            ]
                        }
                    ],
                    is_highlighted: false,
                    is_active: true,
                    is_disabled: true,
                    is_focus: false,
                    selected: [],
                    current: [],
                    label: "",
                    placeholder: "Class Days & Time"
                },
                {
                    type: "dropdown",
                    slug: "personalisation",
                    is_multiple: false,
                    items: [
                        {
                            label: "All",
                            slug: "all"
                        },
                        {
                            label: "1-to-1",
                            slug: "one_to_one"
                        },
                        {
                            label: "Group",
                            slug: "one_to_many"
                        }
                    ],
                    is_highlighted: false,
                    is_active: true,
                    is_disabled: true,
                    is_focus: false,
                    selected: "",
                    current: "",
                    label: "Personalisation",
                    placeholder: "Personalisation"
                },
                {
                    type: "singleCheck",
                    slug: "showEnrollable",
                    items: [],
                    is_multiple: false,
                    is_highlighted: false,
                    is_active: true,
                    is_disabled: true,
                    is_focus: false,
                    selected: true,
                    current: "",
                    label: "",
                    placeholder: "Show only enrollable",
                    tooltip: {
                        message: "A batch is enrollable only if: There is at least one vacancy. The batch is unlocked and there are enough days left to take a new enrolment (i.e. before the batch end day approaches)",
                        multilined: true
                    }
                },
                {
                    type: "singleCheck",
                    slug: "showLocked",
                    items: [],
                    is_multiple: false,
                    is_highlighted: false,
                    is_active: true,
                    is_disabled: true,
                    is_focus: false,
                    selected: false,
                    current: "",
                    label: "",
                    placeholder: "Show only locked",
                    tooltip: {
                        message: "New enrollments cannot be made in a locked batch",
                        multilined: false
                    }
                },
                {
                    type: "dropdown",
                    slug: "hideColumns",
                    items: [],
                    is_multiple: true,
                    is_highlighted: false,
                    is_active: false,
                    is_disabled: false,
                    is_focus: false,
                    selected: "",
                    current: "",
                    label: "Hide Columns",
                    placeholder: "Hide Columns"
                },
            ],
            nestedTabs: {
                activeTab: 0,
                items: [
                    {
                        label: "Upcoming Batches",
                        slug: "ongoing",
                        isActive: true,
                        data: [],
                        hasData: false,
                        visible: true
                    },
                    {
                        label: "Past",
                        slug: "past",
                        isActive: false,
                        data: [],
                        hasData: false,
                        visible: true
                    }
                ]
            },
            dateRange: [],
            payload: {
                batch_days: ["sun","mon","tue","wed","thu","fri","sat"],
                batch_time: ["morning","afternoon","evening","night"],
                enrollment_request_date: "",
                course_id: "",
                enrollable_status: "enrollable",
                instructor_id: 0,
                locked_batch: "all",
                personalisation: "all",
                role_id: Number(isLoggedIn),
                view: "grid-view",
                org_id: "",
                user_id: ""
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'allCourses',
            'instructorList',
            'filterResult',
            'drawer',
            'loader'
        ]),
        isUserAuthorized : {
            get() {
                if (YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
                    return true;
                } else {
                    return false;
                }
            }
        },
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading: {
            get() {
                const module =  this.userInfo.loading
                                
                return module
            }
        },
        isPageReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module = this.userInfo.success
                } else {
                    module = true
                }

                return module
            }
        }
    },
    async created() {
        this.whichPage();
        this.emitEvents();
        window.addEventListener("resize", this.manageOnResize);
    },
    destroyed() {
        window.removeEventListener("resize", this.manageOnResize);
    },
    mounted() {
        this.manageOnResize();
    },
    methods: {
        isElementVisible(role, allowedRoles) {
            return YUNOCommon.findInArray(allowedRoles, role)
        },
        setPayload() {
            const courseID = YUNOCommon.getQueryParameter("courseid");
            const role = this.userInfo.data.role;

            if (role === "org-admin") {
                this.payload.org_id = this.activeOrg();
                this.payload.user_id = isLoggedIn
                // this.setStorage(true);
            }
        },
        onLogin(staus) {

        },
        onUserInfo(data) {
            if ( YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
                this.fetchModules();
            }
        },
        onMini(data) {
            this.isMiniSidebar = data;
        },
        onMenuLoaded() {
            
        },
        classSizeModalClose() {
            this.$refs.classSizeObserver.reset();
            this.classSize.selected = "";
        },
        initClassSize() {
            let url = this.pageHeader.button.url + "/?classSize=" + this.classSize.selected;
            window.location.href = YUNOCommon.config.host() + url;
        },
        pageCTA() {
            this.classSize.modal = true;
        },
        manageOnResize() {
            let windowWidth = window.outerWidth;

            if (windowWidth > 1920) {
                this.filterResult.limit = 20;
            } else if (windowWidth <= 1920 && windowWidth > 1366) {
                this.filterResult.limit = 20;
            } else if (windowWidth <= 1366 && windowWidth > 1280) {
                this.filterResult.limit = 20;
            } else if (windowWidth <= 1280 && windowWidth > 1024) {
                this.filterResult.limit = 20;
            };
        },
        stickyObserver(stickyEle, topEle) {
            const observer = new IntersectionObserver(function(entries) {
                if(entries[0].intersectionRatio === 0)
                   document.querySelector("#" + stickyEle).classList.add("isStickyEle");
                else if(entries[0].intersectionRatio === 1)
                    document.querySelector("#" + stickyEle).classList.remove("isStickyEle");
            }, { threshold: [0,1] })

            observer.observe(document.querySelector("#" + topEle));
        },
        initScroll() {
            if (window !== undefined) {
                this.onScroll(window, true);
            } else {
                setTimeout(() => {
                    this.onScroll(window, true);    
                }, 200);
            }
        },
        onScroll(element, isWindow) {
            const instance = this;
            let lastScrollTop = "";
            
            element.addEventListener('scroll', function(event) {
                let getScrollTop = "";

                if (isWindow) {
                    getScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                } else {
                    getScrollTop = event.target.scrollTop;
                }

                if (Math.sign(getScrollTop) === -1) {
                    instance.isScroll = false;
                    instance.hideHeader = false;
                    return false;
                };

                if (getScrollTop >= lastScrollTop) {
                    if (!instance.isScroll) {
                        instance.isScroll = true;
                        instance.hideHeader = true;
                    }
                } else {
                    if (!instance.isTabClicked && instance.isScroll) {
                        instance.isScroll = false;
                        instance.hideHeader = false;
                    }
                }

                lastScrollTop = getScrollTop <= 0 ? 0 : getScrollTop;
            });
        },
        managePastColumns(data) {
            for (let i = 0; i < data.length; i++) {
                const col = data[i];
                col.isActive = true;

                if (col.field === "batch_instructor") {
                    col.type = "instructor"
                } else if (col.field === "class_days") {
                    col.type = "days"
                } else {
                    col.type = ""
                };
            };
        },
        manageColumns(data) {
            for (let i = 0; i < data.length; i++) {
                const col = data[i];
                col.isActive = true;

                if (col.field === "enrollments") {
                    col.type = "highlight"
                } else if (col.field === "batch_instructor") {
                    col.type = "instructor"
                } else if (col.field === "class_days") {
                    col.type = "days"
                } else if (col.field === "batch_id") {
                    col.type = "hasDrawer"
                } else if (col.field === "batch_url") {
                    col.type = "link_copyToClipboard"
                } else {
                    col.type = ""
                };
            };

            const action = {
                "field":"action",
                "label":"Action",
                "sortable":false,
                "type":"action",
                "isActive": true
            };

            data.push(action)
        },
        manageRows(data) {
            for (let i = 0; i < data.length; i++) {
                const row = data[i];
                let toArray = ""
                    row.action = [
                        {
                            label: "Edit Batch",
                            isActive: this.isFilterAvailable(["yuno-admin", "org-admin"]),
                            slug: "editBatch",
                            activeClass: "material-icons-outlined",
                            url: "/create-batch/?classSize="+ row.batch_personalisation +"&isEdit="+ row.batch_id +"",
                            linkTarget: "_blank",
                            icon: {
                                type: "mdl",
                                class: "material-icons-outlined",
                                hover: "material-icons",
                                font: "edit"
                            }
                        },
                        {
                            label: "New enrollment",
                            isActive: this.isFilterAvailable(["yuno-admin", "Counselor", "org-admin"], row.batch_locked),
                            slug: "newEnrollment",
                            activeClass: "material-icons-outlined",
                            url: "/generate-link?courseID="+ this.payload.course_id +"-"+ row.batch_id +"",
                            linkTarget: "_blank",
                            icon: {
                                type: "mdl",
                                class: "material-icons-outlined",
                                hover: "material-icons",
                                font: "person_add_alt_1"
                            }
                        },
                        {
                            label: "End batch",
                            isActive: this.isFilterAvailable(["yuno-admin"]),
                            slug: "endBatch",
                            activeClass: "material-icons-outlined",
                            url: false,
                            linkTarget: "",
                            icon: {
                                type: "mdl",
                                class: "material-icons-outlined",
                                hover: "material-icons",
                                font: "unpublished"
                            }
                        }
                    ];

                if (row.enrollments !== "Not Enrollable") {
                    toArray = row.enrollments.split(" ");
                    row.hightlight = Number(toArray[0]) >= Number(toArray[2]) ? "isRed" : "isGreen";
                } else {
                    row.hightlight = "isRed";
                }

                if (row.batch_url !== undefined && row.batch_url !== "") {
                    row.batch_url = {
                        label: "Click to copy",
                        slug: "batch_url",
                        hasData: false,
                        activeClass: "material-icons-outlined",
                        url: row.batch_url,
                        linkTarget: "_blank",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: "file_copy"
                        }
                    }
                };
            };
        },
        isFilterAvailable(data, isBatchLocked) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                if (isBatchLocked !== undefined && isBatchLocked) {
                    return false
                } else {
                    return true;
                }
            } else {
                return false;
            }
        },
        gotPastBatches(options) {
            this.filterResult.loading = false;
            Event.$emit('dataLoaded');

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const response = options.response.data;
                this.filterResult.count = response.count;
                this.managePastColumns(response.data.columns);
                this.filterResult.data = response.data;
                this.manageHideColumns(this.filterResult.data);
                this.getStorage(false);    
            };
        },
        filterResultReset(isTabChanged) {
            this.filterResult.success = false;
            this.filterResult.error = null;
            this.filterResult.data = [];
            if (isTabChanged) {
                this.filterResult.offset = 0;       
            }
            this.filterResult.additional = []
        },
        fetchPastBatches(tableLoading, isTabChanged) {
            if (!tableLoading) {
                this.filterResultReset(isTabChanged);
            };

            const instance = this;
            const options = {
                apiURL: this.getPastBatchesURL(),
                module: "gotData",
                store: "filterResult",
                moduleLoading: tableLoading ? false : true,
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotPastBatches(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        getPastBatchesURL() {
            const role = this.userInfo.data.role;
            const isCreate = this.payload.id === 0;
        
            const urlMapping = {
                "org-admin": YUNOCommon.config.org("batchesPast", this.payload.course_id, this.payload.personalisation, isLoggedIn, this.activeOrg(), false, false, false, false, false, false, "grid-view", this.filterResult.limit, this.filterResult.offset),
                "yuno-admin": YUNOCommon.config.pastBatchesList(this.payload.course_id, this.payload.personalisation, isLoggedIn, "grid-view", this.filterResult.limit, this.filterResult.offset),
                "Counselor": YUNOCommon.config.pastBatchesList(this.payload.course_id, this.payload.personalisation, isLoggedIn, "grid-view", this.filterResult.limit, this.filterResult.offset)
            };
        
            return urlMapping[role] || null;
        },
        manageHideColumns(data) {
            const hideColumns = YUNOCommon.findObjectByKey(this.filters, "slug", "hideColumns"),
                cols = data.columns;

            hideColumns.items = [];

            for (let i = 0; i < cols.length; i++) {
                const col = cols[i];
                
                if (col.field === "enrollments") {
                    const item = {
                        label: "Enrollments",
                        slug: col.field,
                        isActive: true
                    };
                    hideColumns.items.push(item)
                } else {
                    const item = {
                        label: col.label,
                        slug: col.field,
                        isActive: true
                    };

                    hideColumns.items.push(item)
                }
            }

            hideColumns.is_active = true;
        },
        gotUpcomingOngoingBatches(options, tableLoading) {
            this.filterResult.loading = false;
            Event.$emit('dataLoaded');

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const response = options.response.data;

                this.filterResult.additional = {
                    batches_vacancies: response.batches_vacancies
                }
                this.filterResult.count = response.count;
                this.manageColumns(response.data.columns);
                this.manageRows(response.data.rows);
                this.filterResult.data = response.data;
                this.manageHideColumns(this.filterResult.data);
                this.getStorage(false);    
            };
        },
        fetchUpcomingOngoingBatches(tableLoading, isTabChanged) {
            if (!tableLoading) {
                this.filterResult.loading = true;
                this.filterResultReset(isTabChanged);
            };

            const role = this.userInfo.data.role;

            if (role === "org-admin") {
                this.payload.limit = this.filterResult.limit;
                this.payload.offset = this.filterResult.offset;
            }

            const instance = this;
            let options = {
                apiURL: this.getUpcomingOngoingBatchesURL(),
                module: "gotData",
                store: "filterResult",
                addToModule: false,
                payload: this.payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotUpcomingOngoingBatches(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        getUpcomingOngoingBatchesURL() {
            const role = this.userInfo.data.role;
            const isCreate = this.payload.id === 0;
        
            const urlMapping = {
                "org-admin": YUNOCommon.config.org("batchesUpcomingOngoing"),
                "yuno-admin": YUNOCommon.config.availableBatches(this.payload.course_id, this.filterResult.limit, this.filterResult.offset),
                "Counselor": YUNOCommon.config.availableBatches(this.payload.course_id, this.filterResult.limit, this.filterResult.offset)
            };
        
            return urlMapping[role] || null;
        },
        gotInstructorList(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            };
        },
        fetchInstructorList() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.instructorListAPI(isLoggedIn),
                module: "gotData",
                store: "instructorList",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotInstructorList(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotCourses(options) {
            const { response } = options;
            if (response?.data?.code === 200) {
                const { data } = response.data;
                const courseID = YUNOCommon.getQueryParameter("courseid");
        
                if (courseID) {
                    const courseFilter = YUNOCommon.findObjectByKey(this.filters, "slug", "course_search");
                    const selectedCourse = YUNOCommon.findObjectByKey(data, "course_id", Number(courseID));

                    courseFilter.current = selectedCourse.title;
                    courseFilter.selected = selectedCourse;
                    this.manageCourseSearch(courseFilter)
                }
            }
        },
        fetchCourses() {
            const options = {
                apiURL: this.getCoursesURL(),
                module: "gotData",
                store: "allCourses",
                callback: true,
                callbackFunc: (options) => this.gotCourses(options)
            };
        
            this.$store.dispatch('fetchData', options);
        },
        getCoursesURL() {
            const role = this.userInfo.data.role;
        
            const urlMapping = {
                "org-admin": YUNOCommon.config.org("courses", this.activeOrg(), isLoggedIn, 0, "all", false, false, false, false, "all", "all", "list-view", 100, 0),
                "yuno-admin": YUNOCommon.config.coursesList(),
                "Counselor": YUNOCommon.config.coursesList()
            };
        
            return urlMapping[role] || null;
        },
        addDays(date, days) {
            const copy = new Date(Number(date))
            copy.setDate(date.getDate() + days)
            return copy
        },
        manageDateRange() {
            let startDate = this.addDays(new Date(), 0),
                endDate = this.addDays(startDate, 10),
                current = new moment(startDate),
                end = new moment(endDate);
            
            current = new moment(startDate);
            dates = [];
            startTimer = new Date();
            while (current < end) {
                this.dateRange.push({
                    label: current.format('ddd MMM DD'),
                    slug: current.format('YYYY-MM-DD')
                });
                current.add(1, 'days');
            };

            endTimer = new Date();

            this.payload.enrollment_request_date = this.dateRange[1].slug;
        },
        manageFilterVisibility() {
            const instructorFilter = YUNOCommon.findObjectByKey(this.filters, "slug", "search_instructor");
            instructorFilter.is_active = this.isElementVisible(this.userRole.data, ["yuno-admin"]);
        },
        fetchModules() {
            this.manageFilterVisibility();
            this.setPayload();
            this.manageDateRange();
            this.fetchCourses();
            this.fetchInstructorList();
            setTimeout(() => {
                this.getStorage(true);    
            }, 500);
        },
        manageFilters(tabIndex) {
            this.filterResultReset();

            if (tabIndex === 0) {
                for (let i = 0; i < this.filters.length; i++) {
                    const filter = this.filters[i];
                    filter.is_active = true;

                    if (filter.slug === "hideColumns") {
                        filter.current = "";
                        filter.selected = "";
                        filter.items = [];
                    };
                };
            } else if (tabIndex === 1) {
                for (let i = 0; i < this.filters.length; i++) {
                    const filter = this.filters[i];
                    filter.is_active = false;

                    if (filter.slug !== "course_search") {
                        if (filter.slug === "search_instructor") {
                            filter.current = "";
                            filter.selected = null;
                        } else if (filter.slug === "class_days_time") {
                            filter.current = [];
                            filter.selected = [];
                        } else if (filter.slug === "personalisation") {
                            filter.current = "";
                            filter.selected = "";
                        } else if (filter.slug === "showEnrollable") {
                            filter.current = "";
                            filter.selected = true;
                        } else if (filter.slug === "showLocked") {
                            filter.current = "";
                            filter.selected = false;
                        } else if (filter.slug === "hideColumns") {
                            filter.current = "";
                            filter.selected = "";
                            filter.items = [];
                        };
                    }
                };

                YUNOCommon.findObjectByKey(this.filters, "slug", "course_search").is_active = true;
                YUNOCommon.findObjectByKey(this.filters, "slug", "personalisation").is_active = true;
                this.resetPayload();
            };

            this.setStorage(true);

            if (this.payload.course_id !== "") {
                this.manageState(false, true);
            }
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        },
        getStorage(reInitCall) {
            sessionStorage.removeItem('batchesStateV6');
            
            const store = sessionStorage.getItem('batchesStateV7');
            const role = this.userRole.data;

            if (store !== null) {
                const data = JSON.parse(store);
                
                if (reInitCall) {
                    this.nestedTabs.activeTab = data.activeTab;
                    this.filters = data.filters;
                    this.filterResult.currentPage = data.results.currentPage;
                    this.filterResult.offset = data.results.offset;

                    if (role === "org-admin") {
                        data.payload.org_id = sessionStorage.getItem("activeOrg");
                    };

                    this.payload = data.payload;    

                    this.enabledFilters = true;
                    this.enabledTabs = true;
                    this.manageState(false);
                }

                if (data.columns !== undefined) {
                    this.filterResult.data.columns = data.columns;    
                }
            } else {
                this.enabledFilters = true;
                this.enabledTabs = true;
            }
        },
        whichPage() {
            if (window.location.href.indexOf("active") > -1) {
                this.nestedTabs.activeTab = 0;
                for (let i = 0; i < this.filters.length; i++) {
                    const filter = this.filters[i];
                    filter.is_active = true;

                    if (filter.slug === "hideColumns") {
                        filter.current = "";
                        filter.selected = "";
                        filter.items = [];
                    };
                };
            } else if (window.location.href.indexOf("past") > -1) {
                this.nestedTabs.activeTab = 1;

                for (let i = 0; i < this.filters.length; i++) {
                    const filter = this.filters[i];
                    filter.is_active = false;

                    if (filter.slug !== "course_search") {
                        if (filter.slug === "search_instructor") {
                            filter.current = "";
                            filter.selected = null;
                        } else if (filter.slug === "class_days_time") {
                            filter.current = [];
                            filter.selected = [];
                        } else if (filter.slug === "personalisation") {
                            filter.current = "";
                            filter.selected = "";
                        } else if (filter.slug === "showEnrollable") {
                            filter.current = "";
                            filter.selected = true;
                        } else if (filter.slug === "showLocked") {
                            filter.current = "";
                            filter.selected = false;
                        } else if (filter.slug === "hideColumns") {
                            filter.current = "";
                            filter.selected = "";
                            filter.items = [];
                        };
                    }
                };

                YUNOCommon.findObjectByKey(this.filters, "slug", "course_search").is_active = true;
                YUNOCommon.findObjectByKey(this.filters, "slug", "personalisation").is_active = true;
            };
        },
        setStorage(emptyColumns) {
            const store = {
                activeTab: this.nestedTabs.activeTab,
                filters: this.filters,
                results: this.filterResult,
                payload: this.payload,
                columns: this.filterResult.data.columns
            };

            if (emptyColumns) {
                delete store.columns
            };

            sessionStorage.setItem("batchesStateV7", JSON.stringify(store));
        },
        manageState(tableLoading, isTabChanged) {
            switch (this.nestedTabs.activeTab) {
                case 0:
                    this.fetchUpcomingOngoingBatches(tableLoading, isTabChanged);
                    break;

                case 1:
                    this.fetchPastBatches(tableLoading, isTabChanged);
                    break;
            }

            setTimeout(() => {
                this.setStorage(false);    
            }, 200);
        },
        resetPayload() {
            const payload = this.payload,
                defaultDate = this.dateRange[1].slug;

            for (let key in payload) {
                if (payload.hasOwnProperty(key)) {
                    if (key !== "course_id") {
                        if (key === "batch_days") {
                            payload[key] = ["sun","mon","tue","wed","thu","fri","sat"]
                        } else if (key === "batch_time") {
                            payload[key] = ["morning","afternoon","evening","night"]
                        } else if (key === "enrollable_status") {
                            payload[key] = "enrollable"
                        } else if (key === "enrollment_request_date") {
                            payload[key] = defaultDate
                        } else if (key === "locked_batch") {
                            payload[key] = "all"
                        } else if (key === "personalisation") {
                            payload[key] = "all"
                        } else if (key === "role_id") {
                            payload[key] = Number(isLoggedIn)
                        } else if (key === "view") {
                            payload[key] = "grid-view"
                        } else {
                            payload[key] = ""
                        }
                    };
                }
            }
        },
        rearrangeFilters(data) {
            for (let i = 0; i < this.filters.length; i++) {
                const filter = this.filters[i];

                if (filter.slug !== "course_search") {
                    if (data.selected !== null) {
                        filter.is_disabled = false;
                    } else {
                        filter.is_disabled = true;

                        if (filter.slug === "search_instructor") {
                            filter.current = "";
                            filter.selected = null;
                        } else if (filter.slug === "class_days_time") {
                            filter.current = [];
                            filter.selected = [];
                        } else if (filter.slug === "personalisation") {
                            filter.current = "";
                            filter.selected = "";
                        } else if (filter.slug === "showEnrollable") {
                            filter.current = "";
                            filter.selected = true;
                        } else if (filter.slug === "showLocked") {
                            filter.current = "";
                            filter.selected = false;
                        };
                    }    
                }
            };
        },
        manageCourseSearch(data) {
            if (data.selected !== null) {
                this.payload.course_id = data.selected.course_id;
                this.manageState(false)
                this.rearrangeFilters(data);
                this.filterResultReset();
            } else {
                this.payload.course_id = "";
                this.resetPayload();
                this.rearrangeFilters(data);
                this.filterResultReset();
                this.setStorage(false);    
            }
        },
        manageInstructorSearch(data) {
            if (data.selected !== null) {
                this.payload.instructor_id = data.selected.id;
            } else {
                this.payload.instructor_id = 0;
            }

            this.manageState(false)
        },
        manageEnrollable(e, data) {
            if (e) {
                this.payload.enrollable_status = "enrollable"
            } else {
                this.payload.enrollable_status = "not_enrollable"
            }

            this.manageState(false)
        },
        manageLocked(e, data) {
            if (e) {
                this.payload.locked_batch = "locked"
            } else {
                this.payload.locked_batch = "unlocked"
            }
            this.manageState(false)
        },
        managePersonalisation(data) {
            if (data.selected !== "") {
                this.payload.personalisation = data.slug;
            } else {
                this.payload.personalisation = "all";
            }

            this.manageState(false)
        },
        manageClassDaysTime(data, filter) {

        },
        clearClassDaysTime(filter, subItem) {
            YUNOCommon.removeObjInArr(filter.current, "slug", subItem.slug);
            YUNOCommon.removeValInArr(filter.selected, subItem.slug);

            if (subItem.parentSlug === "class_days") {
                YUNOCommon.removeValInArr(this.payload.batch_days, subItem.slug);
            } else {
                YUNOCommon.removeValInArr(this.payload.batch_time, subItem.slug);
            };

            if (this.payload.batch_days.length === 0) {
                this.payload.batch_days = ["sun","mon","tue","wed","thu","fri","sat"]
            };

            if (this.payload.batch_time.length === 0) {
                this.payload.batch_time = ["morning","afternoon","evening","night"]
            };

            this.manageState(false)
        },
        closeDrawer() {
            this.drawerModal.modal = false;
            this.drawerModal.data = [];
        },
        gotLearners(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            };
        },
        fetchLearners(batchID) {
            this.drawer.data = [];
            this.drawer.error = null;
            this.drawer.success = false;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.batch("learners", {batchID: batchID}),
                module: "gotData",
                store: "drawer",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotLearners(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        showHideColumns(data, filter) {
            const selectedCol = YUNOCommon.findObjectByKey(this.filterResult.data.columns, "field", data.slug);

            if (selectedCol !== null) {
                selectedCol.isActive = selectedCol.isActive ? false : true;
                data.isActive = data.isActive ? false : true;
                this.setStorage(false);
            };
        },
        endBatchDone(options, row, action) {
            this.manageLoader(false);
            
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: data.message,
                    position: 'is-bottom'
                });
            } else {
                const msg = options.response.data.message;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: msg,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },            
        initEndBatch(row, action) {
            this.manageLoader(true);

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.endBatch(row.batch_id),
                module: "gotData",
                store: "updateLink",
                callback: true,
                callbackFunc: function(options) {
                    return instance.endBatchDone(options, row, action)
                }
            };

            this.$store.dispatch('putData', options);
        },
        manageLoader(state) {
            this.loader.isActive = state;
            this.loader.overlay = state;  
        },
        manageQueryParams() {
            const url = new URL(window.location.href);
            const paramsToRemove = ['courseid'];
        
            paramsToRemove.forEach(param => {
                if (url.searchParams.has(param)) {
                    url.searchParams.delete(param);
                }
            });
        
            window.history.pushState({ path: url.href }, '', url.href);
        },        
        emitEvents() {
            Event.$on('refreshTable', () => {
                this.manageState(false)
            });

            Event.$on('initAction', (row, action) => {
                switch (action.slug) {
                    case "endBatch":
                        let title = "End Batch",
                            msg = "Are you sure you want to end this batch";

                        this.$buefy.dialog.confirm({
                            title: title,
                            message: "Are you sure you want to end this batch",
                            cancelText: 'Cancel',
                            confirmText: 'Yes',
                            type: 'is-danger',
                            onConfirm: () => this.initEndBatch(row, action)
                        });    
                        break;
                }
            });

            Event.$on('openDrawer', (row) => {
                this.drawerModal.modal = true;
                this.fetchLearners(row.batch_id);
            });

            Event.$on('onFilterClear', (data, subItem) => {
                switch (data.slug) {
                    case "course_search":
                        data.current = "";
                        data.selected = null;
                        this.manageCourseSearch(data);
                        break;
                    
                    case "search_instructor":
                        data.current = "";
                        data.selected = null;
                        this.manageInstructorSearch(data);
                        break;

                    case "class_days_time":
                        this.clearClassDaysTime(data, subItem);
                        break;

                    case "personalisation":
                        data.current = "";
                        data.selected = "";
                        this.managePersonalisation(data);
                        break;
                }

                this.manageQueryParams();
            });

            Event.$on('tabChange', (e) => {
                this.manageFilters(e);
            });

            Event.$on('onDateChange', (date) => {
                this.payload.enrollment_request_date = date.slug;
                this.manageState(false)
            });

            Event.$on('onApplyFilter', (filter) => {
                this.filterResult.currentPage = 1;
                this.filterResult.offset = 0;
                
                const defaultTime = ["morning","afternoon","evening","night"],
                    defaultDays = ["sun","mon","tue","wed","thu","fri","sat"];

                if (filter.current.length !== 0) {
                    this.payload.batch_days = YUNOCommon.findObjectByKey(filter.current, "parentSlug", "class_days") !== null ? [] : defaultDays;
                    this.payload.batch_time = YUNOCommon.findObjectByKey(filter.current, "parentSlug", "class_time") !== null ? [] : defaultTime;

                    for (let i = 0; i < filter.current.length; i++) {
                        const item = filter.current[i];
                        
                        if (item.parentSlug === "class_time") {
                            this.payload.batch_time.push(item.slug);
                        }
                        
                        if (item.parentSlug === "class_days") {
                            this.payload.batch_days.push(item.slug);
                        }
                    }
                } else {
                    this.payload.batch_days = defaultDays;
                    this.payload.batch_time = defaultTime;
                } 

                this.manageState(false)
            });

            Event.$on('onFilterItemSelect', (data, filter) => {
                this.filterResult.currentPage = 1;
                this.filterResult.offset = 0;

                switch (filter.slug) {
                    case "personalisation":
                        this.managePersonalisation(data);                        
                        break;
                    case "class_days_time":
                        this.manageClassDaysTime(data, filter);                        
                        break;
                    case "hideColumns":
                        this.showHideColumns(data, filter);                        
                        break;
                };
            });

            Event.$on('onTablePageChange', (e, tableLoading) => {
                this.filterResult.offset = Math.floor(this.filterResult.limit * e - this.filterResult.limit);
                this.manageState(tableLoading)
            });

            Event.$on('onFilterChange', (e, filter) => {
                this.filterResult.currentPage = 1;
                this.filterResult.offset = 0;

                switch (filter.slug) {
                    case "showEnrollable":
                        this.manageEnrollable(e, filter);                        
                        break;
                    case "showLocked":
                        this.manageLocked(e, filter);                        
                        break;
                };
            });

            Event.$on('onSearchItemSelect', (filter) => {
                this.filterResult.currentPage = 1;
                this.filterResult.offset = 0;

                switch (filter.slug) {
                    case "course_search":
                        this.manageCourseSearch(filter);                        
                        break;
                    case "search_instructor":
                        this.manageInstructorSearch(filter);                        
                        break;
                };
            });

            Event.$on('checkLoggedInState', (status) => {
                if (status) {
                    Event.$on('gotUserRole', (role, userData) => {
                        Event.$on('gotUserMenu', () => {
                            if ( YUNOCommon.findInArray(this.authorizedRoles, role)) {
                                this.fetchModules();
                            }
                        }); 
                    });
                };
            });
        },
        fetchFooter() {
            const options = {
                apiURL: YUNOCommon.config.footerAPI(),
                module: "gotData",
                store: "footer",
                callback: false
            };

            this.$store.dispatch('fetchData', options);
        }
    }
});