window.Event = new Vue();

const validationMsg = {
  messages: {
    required: "This field is required",
    numeric: "Numbers only",
    min: "Minimum 10 numbers required",
    max: "Maximum 15 numbers required",
    is_not: "New batch shouldn't be same as current batch",
  },
};

YUNOCommon.assignVValidationObj(validationMsg);
YUNOAccordion.accordion();

Vue.component("yuno-academy", {
  template: `
        <yuno-page-grid
            @onUserInfo="onUserInfo"
        >
            <template v-slot:main>
                <template v-if="moduleWithoutTab.loading">
                    <div class="container hasTopGap">
                        <figure class="infiniteSpinner">
                            <img width="150" height="75" :src="manageImgURL('infinite-spinner.svg')" alt="Yuno Learning">
                        </figure>
                    </div>
                </template>
                <template v-else>
                    <yuno-grid-detail-view
                        type="heroBanner"
                        h1="Instructor-led live learning, powered by AI"
                        description="Turn underutilized seats into revenue streams, and dramatically improve learning quality"
                        :img="manageImgURL('academy.png')"
                        :imgOrder="1"
                        :featureList="heroFeatureList"
                        :featureListOrder="2"
                        @ctaClick="applyForAccess"
                        :cta="{
                            'title': 'Create Academy',
                            'url': createAcademyURL,
                            'target': '_self',
                            'disabled': createAcademyDisabled    
                        }"
                    >
                    </yuno-grid-detail-view>
                    <yuno-section-scroller
                        :data="sectionsTabs"
                    >
                    </yuno-section-scroller>
                    <yuno-carousel-slides
                      :data="carouselItems"
					  :options="{
						'columns': 'one',
					  }"
                    >
                    </yuno-carousel-slides>
					<yuno-accordion-media
						:data="accordionMedia"
					>
					</yuno-accordion-media>
					<yuno-content-table
						:data="contentTable"
					>
					</yuno-content-table>
                  <yuno-tab-with-cards
                      :data="pricing"
                      :hideTabs="true"
                    >
                  </yuno-tab-with-cards>
                  <yuno-two-column-faq
                      :data="faq" 
                    >
                  </yuno-two-column-faq>
                  <yuno-banner-v2
                      :data="interested"
                      @onPrimaryCTAClick="applyForAccess"
                  >
                  </yuno-banner-v2> 
                </template>
            </template>
        </yuno-page-grid>
    `,
  data() {
    return {
      createAcademyDisabled: true,
      createAcademyURL: "/login/?type=signup",
      isMiniSidebar: false,
      authorizedRoles: [
        "Instructor",
        "Learner",
        "yuno-admin",
        "org-admin",
        "Counselor",
      ],
      sectionsTabs: [
        { label: "Features", targetClass: "contentTable" },
        { label: "Pricing", targetClass: "tabsWithCards" },
        { label: "FAQs", targetClass: "twoColumnFAQ" },
      ],
      carouselItems: {
        items: [
          {
            title: "Turn Visitors into Students—Automatically",
            description:
              "Don’t have your students fill lead forms. Have them book demo classes",
            name: "Book A Demo Class",
            excerpt:
              "Students book demos instantly, Yuno schedules automatically. No manual forms or follow-ups—just seamless scheduling that boosts your conversions.",
            video_url:
              this.$store.state.themeURL +
              "/assets/images/Book_a_Demo_class.mp4",
            cta: {
              label: "Call To Action",
            },
          },
          {
            title: "Find the Perfect Instructor Instantly",
            description:
              "Match instructors to upcoming batches - powered by AI",
            name: "Find an instructor",
            excerpt:
              "Stop juggling instructor skills and schedules. Yuno AI instantly finds the right instructor available to teach your next batch—saving you time and stress.",
            video_url:
              this.$store.state.themeURL + "/assets/images/Find_instructor.mp4",
            cta: {
              label: "Call To Action",
            },
          },
          {
            title: "Build Courses in Minutes, Not Days",
            description: "Effortless course creation with AI",
            name: "AI Course Creation",
            excerpt:
              "Tell Yuno your course goals, duration, and format—our AI handles the rest. Create complete, engaging courses effortlessly and reclaim your valuable time.",
            video_url:
              this.$store.state.themeURL + "/assets/images/Course_creation.mp4",
            cta: {
              label: "Call To Action",
            },
          },
          {
            title: "Batch Management Made Simple",
            description:
              "Increase bottom line - visualize, optimize, and maximize your batch utilization",
            name: "Batch Management",
            excerpt:
              "Easily track batch timings, seat availability, and capacity at a glance. Yuno helps you maximize enrollment and boost your academy’s profitability.",
            video_url:
              this.$store.state.themeURL + "/assets/images/Batch_insights.mp4",
            cta: {
              label: "Call To Action",
            },
          },
          {
            title: "Attendance Insights that Drive Results",
            description:
              "Understand attendance to improve learning outcomes and instructor quality",
            name: "Attendance Insights",
            excerpt:
              "Spot attendance patterns instantly. Yuno highlights student progress, instructor impact, and curriculum effectiveness—empowering you to take action fast.",
            video_url:
              this.$store.state.themeURL +
              "/assets/images/Attendance_insights.mp4",
            cta: {
              label: "Call To Action",
            },
          },
        ],
      },
      accordionMedia: {
        title: "Your Branded App. Free!",
        media: {
          image:
            this.$store.state.themeURL + "/assets/images/your_brand_app.png",
        },
        items: [
          {
            title: "Your academy. Your app. Your brand",
            description:
              "Get your personalized mobile app, custom domain, and branding for free. Offer students access exclusively to your courses—or showcase the entire Yuno catalog. Customize freely, grow confidently.",
          },
          {
            title: "Live Classes. Instant Recordings",
            description:
              "Let your students easily view upcoming classes, join live sessions, and revisit recordings anytime. A complete learning experience—seamlessly integrated into your branded app.",
          },
          {
            title: "Unlimited Study Material",
            description:
              "Offer your students instant access to an extensive library of study resources—all seamlessly integrated into your own branded app.",
          },
          {
            title: "Hundreds of Tests. Unlimited Practice",
            description:
              "Empower your students with hundreds of ready-to-use practice tests, directly accessible from your branded app.",
          },
        ],
      },
      contentTable: {
        title: "Features",
        table: [
          {
            title: "Live Teaching, in Group or 1-on-1",
            description:
              "All the tools you need to teach the way you want: in group classes or 1-on-1",
            items: [
              {
                title: "Course Creation",
                subtitle:
                  "Create detailed courses with customizable curriculums and pricing",
                description:
                  "A course on Yuno serves as the core offering that students enroll in.Each course is built around live classes, designed to cover a specific subject area over a defined duration, typically spanning several weeks. Courses are structured with a set number of live classes and follow a fixed daily or weekly schedule. They also come with a pre-determined enrollment fee, ensuring clarity and consistency for both students and academies. This structured approach allows academies to deliver focused and high-quality learning experiences.",
              },
              {
                title: "Batch Creation",
                subtitle:
                  "Manage batch schedules, batch capacity and instructor assignments effortlessly",
                description:
                  "A batch represents a specific group of students enrolled in a particular course, assigned to an instructor. Each batch is designed to match the course’s duration and structure while defining key parameters such as start and end dates, class days, and timing. For example, a batch may accommodate up to 10 students, starting on October 12th, with classes scheduled on Tuesdays, Thursdays, and Saturdays at 6 PM. Batches provide the flexibility to manage multiple groups under the same course, enabling academies to cater to varied student availability while maintaining a consistent learning experience.",
              },
              {
                title: "Enrollment Management",
                subtitle:
                  "Track student enrollment timelines with automated start and end date allocation",
                description:
                  "An enrollment refers to a student’s participation in a specific batch of a course. If a student joins two different batches, each is treated as a separate enrollment. The platform automatically assigns start and end dates to each enrollment, ensuring structured timelines. It also tracks ongoing registrations, overdue enrollments, and pending payments, providing a real-time overview for smooth operations, making it easier to handle your student database efficiently.",
              },
              {
                title: "Live Classes with Zoom or Google Meet",
                subtitle:
                  "Easily integrate your own license of Zoom or Google Meet to deliver virtual classes",
                description:
                  "Live classes are an integral part of a course, conducted in real time to facilitate interactive learning. With integrated Zoom and Google Meet, scheduling and managing live sessions becomes straightforward. Students receive automatic reminders and can join sessions seamlessly, while instructors can monitor attendance and engagement, making the learning experience dynamic and engaging.",
              },
            ],
          },
          {
            title: "Teach alone or bring a team of instructors",
            description:
              "Everything you need to manage a large number of instructors who work for your academy",
            items: [
              {
                title: "Instructor Management",
                subtitle:
                  "Map courses to instructors, track availability, and manage workloads",
                description:
                  "Whether your academy has a single instructor or a team of hundreds, managing them is seamless. You can add or remove instructors, assign them to specific courses or batches, and monitor their workload efficiently. This flexibility ensures that instructors are aligned with the academy’s needs, whether for a single batch or multiple concurrent sessions, maintaining smooth operations at all scales.",
              },
              {
                title: "Batch Management",
                subtitle:
                  "Manage batch schedules, batch capacity and instructor assignments effortlessly",
                description:
                  "Once a batch is created, managing it involves tracking enrollment capacity, monitoring attendance, and balancing instructor workloads. Batches can be locked once full to maintain consistency or adjusted to accommodate changes like transferring students or extending enrollment durations. These tools ensure efficient batch operations and an optimal learning experience for students.",
              },
              {
                title: "Instructor Reviews",
                subtitle: "Track instructor performance and feedback",
                description:
                  "Instructor reviews provide a comprehensive overview of instructor performance, including attendance, engagement, and feedback from students. This data helps academies evaluate instructor effectiveness and make informed decisions about instructor assignments and training.",
              },
            ],
          },
          {
            title: "Enable Students to Self-Study",
            description:
              "Accompanied by live classes, your students need guided self-study to achieve learning outcomes. We help you achieve that",
            items: [
              {
                title: "Access to content on Yuno",
                subtitle:
                  "Leverage Yuno’s library of expert-reviewed content for your courses",
                description:
                  "With Yuno’s vast repository of 2000+ expert-reviewed study materials, academies can enhance their courses by incorporating high-quality content. The platform provides access to videos, articles, PDFs, and e-books, offering a diverse range of learning resources. These materials are designed to complement live sessions, making learning more effective and engaging for students.",
              },
              {
                title: "Publish Content by Type",
                subtitle:
                  "Upload videos, PDFs, and e-books to create a diverse learning experience",
                description:
                  "Academies can create and publish custom content in multiple formats, including videos, articles, PDFs, and e-books, directly on Yuno. Upload videos to the platform, where storage and seamless streaming are handled automatically. Use AI-powered tools to write and share articles quickly and effectively, ensuring professional quality. Upload PDF documents to allow students to download study materials effortlessly, while gaining insights into who accessed them.",
              },
              {
                title: "Practice Tests",
                subtitle:
                  "Design mock exams and track results to prepare students for real tests",
                description:
                  "A practice test is a mock exam designed to simulate real test conditions. Leveraging AI-powered features, the test creation process is simplified, enabling you to design and distribute tests efficiently. These tests help students evaluate their readiness and identify areas for improvement. Instructors can create practice tests on the platform and analyze performance data to provide targeted feedback. This feature ensures students are well-prepared and confident for their exams.",
              },
            ],
          },
          {
            title: "Data Insights",
            description:
              "You'll teach better if you have insights from data. Know which students are not attending your classes regularly, which instructors get top-level feedback and much more",
            items: [
              {
                title: "Enrollment Insights",
                subtitle:
                  "Monitor batch capacity, pending enrollments, and dropout trends effortlessly",
                description:
                  "Gain a deeper understanding of your students with detailed enrollment insights. Track student engagement levels, analyze course popularity, and review student demographics to make informed decisions. These insights enable you to optimize course offerings, adjust batch sizes, and refine schedules to better align with student preferences and needs.",
              },
              {
                title: "Attendance Insights",
                subtitle:
                  "Monitor and analyze attendance rates for every student and batch",
                description:
                  "Tracking attendance is effortless with Yuno, which records not just student presence but also the duration spent in each class. These insights provide a deeper understanding of student engagement and help instructors identify attendance patterns. The data allows academies to intervene early with students who are falling behind, ensuring no learner is left out.",
              },
              {
                title: "Instructor Insights",
                subtitle:
                  "Gather feedback and track engagement through attendance",
                description:
                  "Instructor insights offer data on teaching hours, attendance rates, and feedback from students. These metrics help academies assess instructor effectiveness and plan schedules to balance workloads. By identifying top-performing instructors, academies can also build mentorship programs or expand high-demand courses.",
              },
              {
                title: "Content Insights",
                subtitle:
                  "Track engagement with study materials to optimize your content strategy",
                description:
                  "Content insights analyze how students engage with study materials. Metrics such as completion rates, time spent on videos, and feedback scores help academies refine their content strategy. This ensures that the materials provided are effective, relevant, and aligned with learning objectives.",
              },
            ],
          },
          {
            title: "Money on the Table",
            description:
              "To sell, you need student leads. For leads, you need to promote your academy and for that, you need tools",
            items: [
              {
                title: "Lead generation through demo requests",
                subtitle:
                  "Capture leads with demo requests and track their enrollment journey",
                description:
                  "Publicly listing your courses allows students to easily discover and explore your offerings. Interested learners can submit demo requests, providing them with a firsthand experience of your teaching quality. These demos not only showcase your academy's courses but also serve as a powerful lead generation tool, attracting potential students at no additional cost. This direct interaction with prospective students creates opportunities to increase enrollments and drive revenue growth.",
              },
              {
                title: "E-mail & Whatsapp Campaigns",
                subtitle:
                  "Run automated email and WhatsApp campaigns to convert leads faster",
                description:
                  "Create impactful communication strategies with user-friendly tools for email and WhatsApp campaigns. These features enable academies to deliver targeted messages, course announcements, payment reminders, or promotional content directly to students and prospects. The platform simplifies campaign creation, management, and performance monitoring, ensuring stronger engagement and deeper connections with your audience.",
              },
              {
                title: "Payment Collection",
                subtitle:
                  "Collect fees on time with automated payment reminders and tracking",
                description:
                  "Streamline fee collection with flexible payment options, including multiple currencies, installment plans, and Razorpay integration. The platform automates reminders for pending and overdue payments, offering transparency for both academies and students. These tools not only simplify financial operations but also enhance the student experience by making payments seamless and hassle-free.",
              },
            ],
          },
        ],
      },
      pricing: {
        title: "Pricing",
        description:
          "Free forever for unlimited learners and instructors. Pay only when you want more leads.",
        data: {
          activeTab: 0,
          tabs: [
            {
              label: "Annual (20% off)",
              isActive: true,
              data: [
                {
                  title: "Basic",
                  subtitle: "Everything you need to run an academy",
                  price: null,
                  perInstructorPrice: "0",
                  priceStatement: "free\nforever",
                  total: "0/month",
                  items: [
                    {
                      label: "Unlimited learners",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "Unlimited instructors",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "All platform features",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "Payment collection charges: on actuals",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Only pay the exact fees charged by the payment gateway—no additional markup by Yuno",
                      },
                    },
                    {
                      label: "Developer API Access",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Complete API access to integrate Yuno data into your own apps or custom workflows",
                      },
                    },
                    {
                      label: "White labeled app",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Quickly deploy web and Android apps with your domain & brand",
                      },
                    },
                  ],
                  cta: "Subscribe on Razorpay",
                },
                {
                  title: "Plus",
                  subtitle: "Generate new leads for your academy",
                  price: null,
                  perInstructorPrice: "4900",
                  priceStatement: "per month",
                  total: "4900/month Billed monthly",
                  items: [
                    {
                      label: "Unlimited learners",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "Unlimited instructors",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "All platform features",
                      tooltip: {
                        hasTooltip: false,
                        label: "",
                      },
                    },
                    {
                      label: "Payment collection charges: on actuals",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Only pay the exact fees charged by the payment gateway—no additional markup by Yuno",
                      },
                    },
                    {
                      label: "Developer API Access",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Complete API access to integrate Yuno data into your own apps or custom workflows",
                      },
                    },
                    {
                      label: "White labeled app",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Quickly deploy web and Android apps with your domain & brand",
                      },
                    },
                    {
                      label: "3x search visibility",
                      tooltip: {
                        hasTooltip: true,
                        label:
                          "Courses published by Pro academies get 3x search visibility",
                      },
                    },
                  ],
                  cta: "Subscribe on Razorpay",
                },
              ],
            },
          ],
        },
      },
      faqOptions: {
        type: "faq",
        hasColumn: "col-md-10 offset-md-1",
      },
      faq: {
        title: "FAQs",
        items: [
          {
            question:
              " Can I track the total hours put in by instructors across different courses and batches?",
            answer:
              "Yes, you can track the total hours spent by your instructors in a specific class, in all classes of a batch, and in all classes of various batches of a course.",
          },
          {
            question:
              "Is there a feature to calculate and manage instructor payroll based on hours worked?",
            answer:
              "Yes, Yuno provides a downloadable CSV file (compatible with Excel and Google Sheets) containing detailed records of each instructor, their classes, hours spent per class, and more, allowing you to calculate instructor payroll with ease.",
          },
          {
            question:
              "Can I monitor instructor attendance and identify trends in their engagement?",
            answer:
              "Yes, you can view both student and instructor attendance. While reviewing student attendance, you can apply filters based on the instructor teaching them, the course they are enrolled in, and other criteria.",
          },
          {
            question:
              "Are there tools to analyze performance trends using charts and visual reports?",
            answer:
              "We collect all the necessary data to generate visualizations for performance analysis but this feature is not yet available - it's under development. However, we can provide the data in a CSV format, which you can utilize for your analysis.",
          },
          {
            question:
              "Can the platform generate rankings for students based on test scores and participation?",
            answer:
              "Yes, we generate rankings for students based on their test scores. Rankings based on participation will be introduced in an upcoming update.",
          },
          {
            question:
              "Can instructors create and use mock tests or quizzes  within the curriculum?",
            answer:
              "Yes, that's possible. Instructors have full access to Yuno's extensive resource library, which includes thousands of articles, videos, PDFs, and practice tests. They can seamlessly share these resources with students, either with an entire batch or a selected group of students, as needed.",
          },
          {
            question:
              " Are detailed performance reports generated for each test, including time taken and areas of improvement?",
            answer:
              "You can check the actual response of a student of each question in the test and also their overall score. However, you cannot yet see how long did a student take to answer a specific question. That feature will soon be releases.",
          },
          {
            question:
              "Can the platform identify students with low attendance or poor performance?",
            answer:
              "Yes, attendance insights allow you to view students categorized by their attendance levels. For instance, you can generate lists of students with attendance below 50%, below 30%, and other specified thresholds.",
          },
          {
            question:
              " Is there a way to automatically send notifications to red-flagged students?",
            answer:
              "Each academy defines red-flag students differently, so we don’t provide automated notifications. However, you can generate a list of red-flag students based on your criteria and send them notifications manually.",
          },
        ],
      },
      heroFeatureList: [
        {
          title: "Live classes, in a group or 1-on-1",
          description:
            "Integrated with Google Meet and Zoom; but does a lot more!",
        },
        {
          title: "Go global. Instantly",
          description:
            "Yuno has everything you need to build a global business",
        },
        {
          title: "Teach alone or bring a team of instructors",
          description: "Free forever for unlimited learners and instructors",
        },
      ],
      interested: {
        title: "Ready to start your own academy?",
        cta: {
          label: "Create Academy",
          url: "/login/?type=signup",
          target: "_self",
          disabled: true,
        },
      },
    };
  },
  computed: {
    ...Vuex.mapState([
      "user",
      "userInfo",
      "header",
      "userProfile",
      "userRole",
      "footer",
      "moduleWithoutTab",
    ]),
    isPageLoading() {
      return (
        this.userInfo.loading || this.header.loading || this.footer.loading
      );
    },
    isPageReady() {
      return this.user.isLoggedin
        ? this.header.success && this.footer.success && this.userInfo.success
        : this.header.success && this.footer.success;
    },
    isUserLoading() {
      return this.userInfo.loading;
    },
    isUserReady() {
      return this.user.isLoggedin ? this.userInfo.success : true;
    },
    searchBar() {
      return this.user.isLoggedin && this.userRole.data === "Learner";
    },
  },
  async created() {
    this.fetchFooter();
    this.emitEvents();
  },
  methods: {
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      } else {
        return 0;
      }
    },
    gotOrgInfo(options) {
      const { response: { data: { code, data } = {} } = {} } = options;
      if (code === 200) {
        const academies = data.yuno_academy_subscription.academies;
        if (academies.length > 0) {
          this.createAcademyDisabled = true;
          this.interested.cta.disabled = this.createAcademyDisabled;
        } else {
          this.createAcademyDisabled = false;
          this.createAcademyURL = "/create-new-academy/";
          this.interested.cta.url = this.createAcademyURL;
          this.interested.cta.disabled = this.createAcademyDisabled;
        }
      }
    },
    fetchOrgInfo() {
      const options = {
        apiURL: YUNOCommon.config.generic(
          "org",
          false,
          false,
          this.activeOrg()
        ),
        module: "gotData",
        store: "moduleWithoutTab",
        callback: true,
        callbackFunc: this.gotOrgInfo,
      };

      this.$store.dispatch("fetchData", options);
    },
    applyForAccess() {
      sessionStorage.clear();

      const cta = {
        label: "Sign Up as Academy Owner",
        action: "academyOwner",
        disabled: true,
      };

      if (!this.user.isLoggedin) {
        const roleMap = {
          academyOwner: "org-admin",
          instructor: "instructor",
        };

        const page = {
          url: "",
          pageTitle: "",
          category: "",
          role: roleMap[cta.action] || "",
          hasAcademy: true,
        };

        sessionStorage.setItem("landingPage", JSON.stringify(page));
      }
    },
    manageImgURL(fileName) {
      return `${this.$store.state.themeURL}/assets/images/${fileName}`;
    },
    fetchModules() {
      if (this.user.isLoggedin && this.userRole.data === "org-admin") {
        this.fetchOrgInfo();
      }
    },
    onUserInfo(data) {
      this.fetchModules();
    },
    /**
     * Fetches the footer data from the server and stores it in the Vuex store.
     */
    fetchFooter() {
      const options = {
        apiURL: YUNOCommon.config.footerAPI(),
        module: "gotData",
        store: "footer",
        callback: false,
      };

      this.$store.dispatch("fetchData", options);
    },
    onMini(data) {
      this.isMiniSidebar = data;
    },
    /**
     * Emits the checkLoggedInState event and calls the fetchModules method when the status is false.
     * @param {boolean} status - The logged in status.
     */
    emitEvents() {
      Event.$on("checkLoggedInState", (status) => {
        if (!status) {
          this.createAcademyDisabled = false;
          this.interested.cta.disabled = this.createAcademyDisabled;
          this.fetchModules();
        }
      });
    },
  },
});
