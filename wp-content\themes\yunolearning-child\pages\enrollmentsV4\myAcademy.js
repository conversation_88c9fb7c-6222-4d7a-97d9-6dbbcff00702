Vue.component('yuno-my-academy', {
    props: {
        storage: {
            type: Object,
            required: true
        },
        apiURL: {
            type: Object,
            required: true,
        },
    },
    template: `
        <div>   
            hhhh
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userInfo',
            'filters',
            'filterResult',
            'userRole',
            'drawer'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onTabChanged(e) {
            
        },
    }
});