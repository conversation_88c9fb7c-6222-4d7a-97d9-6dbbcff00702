<?php

namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class CourseModel extends Model
{

    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
        $this->loadModel('locale');
        $this->loadModel('invoice');
    }

    /**
     * Retrieves a list of courses based on the provided query and optional filters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns a list of formatted courses or false if no courses are found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourses($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if ($query['custom']) {
            $coursesCntResponse = $this->es->count('course', $query['custom']);
            if ($coursesCntResponse['status_code'] == 200) {
                //$query['qryStr'] = ['_source' => 'data.details.record_id,data.details.title,data.details.url,data.details.short_description,data.details.post_description,data.details.successful_students,data.details.active_enrollments,data.details.parent_taxonomy,data.details.academies,data.details.group_type,data.details.availability_summary,data.details.batch_details,data.details.mapped_instructors,data.details.duration_weeks'];
                $coursesDataResponse = $this->es->customQuery($query['custom'], 'course', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($coursesDataResponse['status_code'] == 200) {
            $responseCount = $coursesCntResponse['body']['count'];
            $courses = $coursesDataResponse['body']['hits']['hits'];
            if ($responseCount > 0 && is_countable($courses) && count($courses) > 0) {
                foreach ($courses as $courseDataResponse) {
                    $course = $courseDataResponse['_source']['data']['details'];

                    // Build the structured response
                    $responseData[] = array(
                        'id' =>  $course['record_id'] ?? '',  // Unique number of course
                        'title' => $course['title'] ?? '',  // Label of course
                        'url' => $course['url'] ?? '',  // URL of course (assuming format URI)
                        'short_description' => $course['short_description'] ?? '',  // Short description
                        'long_description' => $course['post_description'] ?? '',  // Long description
                        'past_learners' => $course['successful_students'] ?? [],  // Learners who've completed the course
                        'active_learners' => $course['active_enrollments'] ?? [],  // Active enrollments
                        'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal']),  // Category object
                        'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0], ['schema' => 'Academy_Minimal']),  // Academy object
                        'personalization' => array_map(function ($type) {
                            return [
                                'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                                'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                                'slug' => $type, // Use the value directly as the slug
                                'description' => $type === "1-1"
                                    ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                                    : "Classes will be taught in a group setting, consisting of multiple learners.",
                                'value' => true // Set true or false as needed for personalization
                            ];
                        }, $course['group_type'] ?? []),  // Personalization details
                        'availability' => $this->load->subData('course', 'getCourseAvailability', ['id' => $course['record_id']]),
                        'duration' => array(
                            'label' => 'WEEKS',  // Duration unit: WEEKS or DAYS
                            'value' => $course['duration_weeks'] ?? 0  // Numeric value of duration
                        ),
                        'teaching_mode' => 'online',  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                        'schedule' => $this->load->subData('course', 'getCourseSchedule', ['id' => $course['record_id']]),  // Schedule object
                        'economics' => $this->load->subData('course', 'getCourseEconomics', ['id' => $course['record_id']]),  // Economics/financial details
                        "in_crm" => [
                            "platform" => "Zoho",
                            "id" => get_post_meta($course['record_id'] ?? '', 'id', true) ?? ''
                        ]
                    );
                    
                } //foreach end courses

                
                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }
                
                return $this->schema->validate(['count' => $responseCount, 'data' => $responseData], ['count' => 'integer', 'data' => ['Refer#Course']], $filter);
            } //if end
        }
        return false;
    }

    /**
     * Retrieves detailed course data based on the provided course ID or custom query.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID.
     * @param array $filter Optional filters for schema validation and formatting.
     * @return array|false Returns formatted course data or false if the course is not found.
     * @throws \InvalidArgumentException If the input query is invalid.
     * <AUTHOR>
     */
    public function getCourse($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            // Fetch course data from Elasticsearch (or any other data source)
            //$query['qryStr'] = ['_source' => 'data.details.record_id,data.details.title,data.details.url,data.details.short_description,data.details.post_description,data.details.successful_students,data.details.active_enrollments,data.details.parent_taxonomy,data.details.academies,data.details.group_type,data.details.availability_summary,data.details.batch_details,data.details.mapped_instructors,data.details.duration_weeks'];
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } elseif (isset($query['custom'])) {
            $courseDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }



        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            
            // Build the structured response
            $courseResponse = array(
                'id' =>  $course['record_id'] ?? '',  // Unique number of course
                'title' => $course['title'] ?? '',  // Label of course
                'url' => $course['url'] ?? '',  // URL of course (assuming format URI)
                'short_description' => $course['short_description'] ?? '',  // Short description
                'long_description' => $course['post_description'] ?? '',  // Long description
                'past_learners' => $course['successful_students'] ?? [],  // Learners who've completed the course
                'active_learners' => $course['active_enrollments'] ?? [],  // Active enrollments
                'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal']),  // Category object
                'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0], ['schema' => 'Academy_Minimal']),  // Academy object
                'personalization' => array_map(function ($type) {
                    return [
                        'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                        'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                        'slug' => $type, // Use the value directly as the slug
                        'description' => $type === "1-1"
                            ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                            : "Classes will be taught in a group setting, consisting of multiple learners.",
                        'value' => true // Set true or false as needed for personalization
                    ];
                }, $course['group_type'] ?? []),  // Personalization details
                'availability' => $this->load->subData('course', 'getCourseAvailability', ['id' => $course['record_id']]),
                'duration' => array(
                    'label' => 'WEEKS',  // Duration unit: WEEKS or DAYS
                    'value' => $course['duration_weeks'] ?? 0  // Numeric value of duration
                ),
                'teaching_mode' => 'online',  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                'schedule' => $this->load->subData('course', 'getCourseSchedule', ['id' => $course['record_id']]),  // Schedule object
                'economics' => $this->load->subData('course', 'getCourseEconomics', ['id' => $course['record_id']]),  // Economics/financial details
                "in_crm" => [
                    "platform" => "Zoho",
                    "id" => get_post_meta($course['record_id'] ?? '', 'id', true) ?? ''
                ]
            );
            // Validate the formatted response against the 'Course' schema
            return $this->schema->validate($courseResponse, 'Course', $filter);
        } else {
            return false;
        }
    }

    /**
     * Retrieves course availability information including group and 1-to-1 availability details.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch availability information.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns formatted course availability data including:
     *                     - summary: Overall availability summary
     *                     - group: Group availability details with personalization and batch information
     *                     - 1_to_1: 1-to-1 availability details with personalization and instructor information
     *                     Returns false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourseAvailability($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        }

        $schema = [
            'summary' => 'string',  // Summary of the course availability
            'group' => [
                'personalization' => 'Refer#Personalization',
                'batch' => ['Refer#Batch_Minimal'],
            ],  // Group availability details (array of objects)
            '1_to_1' => [
               'personalization' => 'Refer#Personalization',
               'instructor' => ['Refer#User_Minimal'],
            ]  // 1-to-1 availability details (array of objects)
        ];

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];

            $availability = [
                'summary' => $course['availability_summary'] ?? '',
                'group' => [
                    'personalization' => [
                        'type' => 'GROUP',  // Assuming GROUP type for example
                        'name' => 'Group',
                        'description' => 'Group-based availability with personalization',
                        'value' => true
                    ],
                    'batch' => array_map(function ($batch) {
                        return [
                            'id' => $batch['batch_id'],  // Use the unique batch ID
                            'title' => $batch['batch_title'],  // Title of the batch
                            'temporal_state' => $batch['temporal_state'] ?? ''  // Whether the batch is in the past or upcoming/ongoing enum ['PAST', 'UPCOMINGONGOING'] example xor-batch
                        ];
                    }, $course['batch_details'] ?? []),
                ],
                '1_to_1' => [
                    'personalization' => [
                        'type' => '1TO1',  // Assuming 1TO1 type for example
                        'name' => '1-to-1',
                        'description' => '1-to-1 availability with personalization',
                        'value' => true
                    ],
                    'instructor' => array_map(function ($instructor) {
                        return [
                                'id' => $instructor['id'],
                                'role' => ["instructor"],
                                'full_name' => $instructor['name'],
                                'image_url' => $instructor['image_url']
                            ];
                    }, $course['mapped_instructors'] ?? []),
                ],
            ];
            
            return $this->schema->validate($availability, $schema, $filter);
        } else {
            return false;
        }
    }

    /**
     * Retrieves the schedule of a course from Elasticsearch based on the provided query parameters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to retrieve the course schedule.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns the formatted course schedule or false if no schedule is found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourseSchedule($query, $filter = [])
    {
        // Ensure query is an array
        $query = is_array($query) ? $query : ['id' => $query];

        // Fetch course schedule data
        if (isset($query['id'])) {
            $scheduleDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } else {
            return false;
        }

        // Check if the response from Elasticsearch is valid
        if ($scheduleDataResponse['status_code'] == 200) {

            // Extract the course schedule from the response
            $courseSchedule = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule'] ?? [];
            $courseScheduleId = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule_id'] ?? [];

            // Initialize an empty array for the formatted response
            $formattedResponse = [];
            $formattedResponse['id'] = $courseScheduleId;
            $formattedResponse['activity'] = []; // Initialize 'activity' as an array

            // Process each schedule item
            foreach ($courseSchedule as $schedule) {
                $activity = [
                    'type' => $schedule['activity']['label'] ?? '', // Extract 'label' as 'type'
                    'slug' => $schedule['activity']['slug'] ?? '', // Extract 'slug'
                    'id' => $schedule['id'] ?? '', // Unique ID for the activity
                    'order' => $schedule['order'] ?? '', // The order of the activity
                    'title' => $schedule['title'] ?? '', // Title of the activity
                    'icon_url' => $schedule['icon_url'] ?? '', // URL to the activity icon
                    'short_description' => $schedule['excerpt'] ?? '', // Short description of the activity
                    'long_description' => $schedule['description'] ?? '', // Long description of the activity
                    'duration_in_minutes' => $schedule['duration'] ?? '', // Duration in minutes
                    'sub_cat' => array_map(function ($subCat) {
                        return [
                            'id' => $subCat['id'], // ID of the sub-category
                            'name' => $subCat['name'], // Name of the sub-category
                            'slug' => $subCat['slug'], // Slug of the sub-category
                            'sub_cat' => array_map(function ($nestedSubCat) {
                                return [
                                    'id' => $nestedSubCat['id'], // ID of the nested sub-category
                                    'name' => $nestedSubCat['name'], // Name of the nested sub-category
                                    'slug' => $nestedSubCat['slug'] // Slug of the nested sub-category
                                ];
                            }, $subCat['sub_cat'] ?? []) // Nested sub-categories
                        ];
                    }, $schedule['sub_cat'] ?? []) // Sub-categories for the activity
                ];

                // Append each activity to 'activity'
                $formattedResponse['activity'][] = $activity;
            }


            // Return the formatted response
            return $this->schema->validate($formattedResponse, 'Course_Schedule', $filter);
        }
        return false;
    }

    /**
     * Retrieves course economics information including pricing, personalization options, and time investment details.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch economics information.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns formatted course economics data including:
     *                     - personalization: Course type (GROUP or 1-1) with description
     *                     - price: Pricing information with variants (list_price and selling_price)
     *                     - what_you_get: Course features (diagnostic test, live classes, assignments, etc.)
     *                     - expected_time_investment: Time investment details for various activities
     *                     Returns false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourseEconomics($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        }
        
        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            $courseEconomics = [];

            $currencyCode = $this->locale->activeCurrency('code');
            $oneToOnePrice = $course['one_to_one_price'] ?? 0;
            $groupPrice = $course['group_price'] ?? 0;

            if(isset($course['prices']) && !empty($course['prices'])){
                foreach($course['prices'] as $currency){
                    if($currency['code'] == $currencyCode){
                       
                        $oneToOnePriceOC = $currency['1_to_1_price'] ?? $currency['1_to_1_Price'];
                        $groupPriceOC = $currency['group_price'] ?? $currency['Group_Price'];

                        break;
                    }
                }
            }


            $course = $courseDataResponse['body']['_source']['data']['details'];
            //for group course economics
            if (in_array("1-Many", $course['group_type'])) {
                
                //for group price
                if(isset($groupPriceOC) && !empty($groupPriceOC)){
                    $groupPrice = $groupPriceOC;
                }else{
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $groupPrice = $groupPrice / $exchangeRate;
                }

                //copy course economics one to many
                $ceOnetoMany = $course['course_economics'][0];
                // Prepare "what_you_get"
                $whatYouGetGroupClasses = [];

                //what we get for group course economics
                //diagnostic test
                $diagnosticTest = isset($ceOnetoMany['is_diagnostic_test']) && ($ceOnetoMany['is_diagnostic_test'] === '1' || $ceOnetoMany['is_diagnostic_test'] === 1 || $ceOnetoMany['is_diagnostic_test'] === true);

                //post test
                $postTest = isset($ceOnetoMany['is_there_a_post_test']) && ($ceOnetoMany['is_there_a_post_test'] === '1' || $ceOnetoMany['is_there_a_post_test'] === 1 || $ceOnetoMany['is_there_a_post_test'] === true);

                //assignments
                $assignments = $ceOnetoMany['number_of_assignments_per_student_that_require_correction'] + $ceOnetoMany['number_of_assignments_per_student_that_not_require_correction'];

                //mock tests
                $mockTests = $ceOnetoMany['number_of_mock_exams'] ?? 0;

                //group classes duration
                $groupClassesDurationMins = (30 * $ceOnetoMany['group_class_30_min_duartion']) +
                                            (45 * $ceOnetoMany['group_class_45_min_duartion']) +
                                            (60 * $ceOnetoMany['group_class_60_min_duartion']);
                //diagnostic test duration
                $diagnosticTestDurationMins = $ceOnetoMany['duration_diagnostic_test'] ?? 0;
                //post test duration
                $postTestDurationMins = $ceOnetoMany['time_investment_per_student_on_post_test'] ?? 0;

                //total time duration
                $totalTimeMins = $groupClassesDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                $totalTimeHoursFloat = $totalTimeMins / 60;

                // Format durations in hours and minutes
                $groupClassesDuration = intdiv($groupClassesDurationMins, 60) . " hours " . ($groupClassesDurationMins % 60) . " mins";
                $diagnosticTestDuration = intdiv($diagnosticTestDurationMins, 60) . " hours " . ($diagnosticTestDurationMins % 60) . " mins";
                $postTestDuration = intdiv($postTestDurationMins, 60) . " hours " . ($postTestDurationMins % 60) . " mins";
                $totalTimeDuration = intdiv($totalTimeMins, 60) . " hours " . ($totalTimeMins % 60) . " mins";

                //diagnostic test
                if ($diagnosticTest) {
                    $whatYouGetGroupClasses[] = [
                        "label" => "Diagnostic Test",
                        "slug" => "diagnostic_test",
                        "value" => $diagnosticTest,
                        "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas"
                    ];
                }

                //group classes
                $numberGroupOfClasses = $ceOnetoMany['group_class_30_min_duartion'] + $ceOnetoMany['group_class_45_min_duartion'] + $ceOnetoMany['group_class_60_min_duartion'];
                if ($groupClassesDurationMins > 0) {
                    $whatYouGetGroupClasses[] = [
                        "label" => "of live classes",
                        "subtitle" => "$numberGroupOfClasses group classes",
                        "slug" => "live_classes",
                        "value" => $groupClassesDuration,
                        "value_float" => $groupClassesDurationMins / 60,
                        "message" => "",
                        "items" => [
                            [
                                "label" => "of group classes",
                                "subtitle" => "$numberGroupOfClasses group classes",
                                "slug" => "group_classes",
                                "value" => $groupClassesDuration,
                                "value_float" => $groupClassesDurationMins / 60,
                                "message" => "",
                            ]
                        ],
                    ];
                }

                //assignments
                if ($assignments > 0) {
                    $whatYouGetGroupClasses[] = [
                        "label" => "Assignments",
                        "subtitle" => "",
                        "slug" => "assignments",
                        "value" => $assignments,
                        "message" => ""
                    ];
                }

                //post test
                if ($postTest) {
                    $whatYouGetGroupClasses[] = [
                        "label" => "Post test",
                        "subtitle" => "",
                        "slug" => "post_test",
                        "value" => $postTest,
                        "message" => "",
                    ];
                }

                //mock tests
                if ($mockTests > 0) {
                    $whatYouGetGroupClasses[] = [
                        "label" => "Mock tests",
                        "subtitle" => "",
                        "slug" => "mock_tests",
                        "value" => $mockTests,
                        "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                    ];
                }

                // Prepare "expected_time_investment"
                $expectedTimeInvestmentGroupClasses[] = [
                    "title" => "$totalTimeDuration of time investment",
                    "title_float" => $totalTimeHoursFloat,
                    "subtitle" => " (less than 1 hour per week)",
                    "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                    "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                    "items" => [
                        [
                            "label" => "in diagnostic test",
                            "slug" => "diagnostic_test",
                            "hours" => $diagnosticTestDuration,
                            "hours_float" => $diagnosticTestDurationMins / 60,
                        ],
                        [
                            "label" => "in live classes",
                            "slug" => "live_classes",
                            "hours" => $groupClassesDuration,
                            "hours_float" => $groupClassesDurationMins / 60,
                            "items" => [
                                [
                                    "label" => "in group classes",
                                    "slug" => "group_classes",
                                    "value" => $groupClassesDuration,
                                    "value_float" => $groupClassesDurationMins / 60,
                                    "message" => "",
                                ]
                            ],
                        ],
                        [
                            "label" => "in post test",
                            "slug" => "post_test",
                            "hours" => $postTestDuration,
                            "hours_float" => $postTestDurationMins / 60,
                        ],
                    ],
                ];

                //prepare course economics one to many
                $courseEconomics[] = [
                    "id" => $ceOnetoMany['id'],
                    "personalization" => [
                        'type' => 'GROUP',
                        'name' => 'Group',
                        'description' => 'Classes will be taught in a group setting, consisting of multiple learners.',
                        'value' => true,    //Pending logic
                    ],
                    "price" => [
                        'variants' => [
                            [
                                'type' => [
                                    'based_on' => 'Personalization',
                                    'value' => '1-Many'
                                ],
                                'name' => 'Course',
                                'list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => $groupPrice]),
                                'selling_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $groupPrice]),
                            ]
                        ]
                    ],
                    "what_you_get" => $whatYouGetGroupClasses,
                    "expected_time_investment" => $expectedTimeInvestmentGroupClasses,
                ];
            }
            //for one to one course economics
            if (in_array("1-1", $course['group_type'])) {

                //for one to one price
                if(isset($oneToOnePriceOC) && !empty($oneToOnePriceOC)){
                    $oneToOnePrice = $oneToOnePriceOC;
                }else{
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $oneToOnePrice = $oneToOnePrice / $exchangeRate;
                }

                //copy course economics one to one
                $ceOnetoOne = $course['course_economics_one_to_one'][0];
                // Prepare "what_you_get"
                $whatYouGetOneToOne = [];

                //what we get for one to one course economics
                //diagnostic test
                $diagnosticTest = isset($ceOnetoOne['is_diagnostic_test']) && ($ceOnetoOne['is_diagnostic_test'] === '1' || $ceOnetoOne['is_diagnostic_test'] === 1 || $ceOnetoOne['is_diagnostic_test'] === true);

                //post test
                $postTest = isset($ceOnetoOne['is_there_a_post_test']) && ($ceOnetoOne['is_there_a_post_test'] === '1' || $ceOnetoOne['is_there_a_post_test'] === 1 || $ceOnetoOne['is_there_a_post_test'] === true);       

                //assignments
                $assignments = $ceOnetoOne['number_of_assignments_per_student_that_require_correction'] + $ceOnetoOne['number_of_assignments_per_student_that_not_require_correction'];

                //mock tests
                $mockTests = $ceOnetoOne['number_of_mock_exams'] ?? 0;
                
                //one to one classes duration
                $oneToOneClassesDurationMins =  (30 * $ceOnetoOne['one_to_one_class_30_min_duartion']) + 
                                                (45 * $ceOnetoOne['one_to_one_class_45_min_duartion']) +
                                                (60 * $ceOnetoOne['one_to_one_class_60_min_duartion']) +
                                                (75 * $ceOnetoOne['one_to_one_class_75_min_duartion']) +
                                                (90 * $ceOnetoOne['one_to_one_class_90_min_duartion']) +
                                                (120 * $ceOnetoOne['one_to_one_class_120_min_duartion']) +
                                                (150 * $ceOnetoOne['one_to_one_class_150_min_duartion']) +
                                                (180 * $ceOnetoOne['one_to_one_class_180_min_duartion']);

                //total assignments
                $totalAssignmentsOnetoOne = $ceOnetoOne['number_of_assignments_per_student_that_not_require_correction'] + $ceOnetoOne['number_of_assignments_per_student_that_require_correction'];
                //assignments duration
                $assignmentsDurationMins = $ceOnetoOne['time_investment_per_student_on_assignment_that_require_correction'] * $ceOnetoOne['number_of_assignments_per_student_that_require_correction'] +
                                            $ceOnetoOne['time_investment_per_student_on_assignment_that_not_require_correction'];
                //diagnostic test duration
                $diagnosticTestDurationMins = $ceOnetoOne['duration_diagnostic_test'] ?? 0;
                //post test duration
                $postTestDurationMins = $ceOnetoOne['time_investment_per_student_on_post_test'] ?? 0;
    
                $totalTimeMins = $oneToOneClassesDurationMins + $assignmentsDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                $totalTimeHoursFloat = $totalTimeMins / 60;

                // Format durations in hours and minutes
                $oneToOneClassesDuration = intdiv($oneToOneClassesDurationMins, 60) . " hours " . ($oneToOneClassesDurationMins % 60) . " mins";
                $assignmentsDuration = intdiv($assignmentsDurationMins, 60) . " hours " . ($assignmentsDurationMins % 60) . " mins";
                $diagnosticTestDuration = intdiv($diagnosticTestDurationMins, 60) . " hours " . ($diagnosticTestDurationMins % 60) . " mins";
                $postTestDuration = intdiv($postTestDurationMins, 60) . " hours " . ($postTestDurationMins % 60) . " mins";
                $totalTimeDuration = intdiv($totalTimeMins, 60) . " hours " . ($totalTimeMins % 60) . " mins";

                //diagnostic test
                if ($diagnosticTest) {
                    $whatYouGetOneToOne[] = [
                        "label" => "Diagnostic Test",
                        "slug" => "diagnostic_test",
                        "value" => $diagnosticTest,
                        "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas"
                    ];
                }

                $numberOnetoOneOfClasses =  $ceOnetoOne['one_to_one_class_30_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_45_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_60_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_75_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_90_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_120_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_150_min_duartion'] +
                                    $ceOnetoOne['one_to_one_class_180_min_duartion'];

                if ($oneToOneClassesDurationMins > 0) {
                    $whatYouGetOneToOne[] = [
                        "label" => "of live classes",
                        "subtitle" => "$numberOnetoOneOfClasses live classes",
                        "slug" => "live_classes",
                        "value" => $oneToOneClassesDuration,
                        "value_float" => $oneToOneClassesDurationMins / 60,
                        "message" => "",
                        "items" => [
                            [
                                "label" => "of 1-to-1 classes",
                                "subtitle" => "$numberOnetoOneOfClasses 1-to-1 classes",
                                "slug" => "one_to_one_classes",
                                "value" => $oneToOneClassesDuration,
                                "value_float" => $oneToOneClassesDurationMins / 60,
                                "message" => "",
                            ],
                        ],
                    ];
                }

                //assignments
                if ($assignments > 0) {
                    $whatYouGetOneToOne[] = [
                        "label" => "Assignments",
                        "subtitle" => "",
                        "slug" => "assignments",
                        "value" => $totalAssignmentsOnetoOne,
                        "message" => ""
                    ];
                }

                //post test
                if ($postTest) {
                    $whatYouGetOneToOne[] = [
                        "label" => "Post test",
                        "subtitle" => "",
                        "slug" => "post_test",
                        "value" => $postTest,
                        "message" => "",
                    ];
                }   

                //mock tests
                if ($mockTests > 0) {
                    $whatYouGetOneToOne[] = [
                        "label" => "Mock tests",
                        "subtitle" => "",   
                        "slug" => "mock_tests",
                        "value" => $mockTests,
                        "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                    ];
                }

                // Prepare "expected_time_investment"
                $expectedTimeInvestmentOneToOne[] = [
                    "title" => "$totalTimeDuration of time investment",
                    "title_float" => $totalTimeHoursFloat,
                    "subtitle" => " (less than 1 hour per week)",
                    "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                    "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                    "items" => [
                        [
                            "label" => "in diagnostic test",
                            "slug" => "diagnostic_test",
                            "hours" => $diagnosticTestDuration,
                            "hours_float" => $diagnosticTestDurationMins / 60,
                        ],
                        [
                            "label" => "in live classes",
                            "slug" => "live_classes",
                            "hours" => $oneToOneClassesDuration,
                            "hours_float" => $oneToOneClassesDurationMins / 60,
                            "items" => [
                                [
                                    "label" => "in 1-to-1 classes",
                                    "slug" => "one_to_one_classes",
                                    "value" => $oneToOneClassesDuration,
                                    "value_float" => $oneToOneClassesDurationMins / 60,
                                    "message" => "",
                                ],
                            ],
                        ],
                        [
                            "label" => "in assignments",
                            "slug" => "assignments",
                            "hours" => $assignmentsDuration,
                            "hours_float" => $assignmentsDurationMins / 60,
                        ],
                        [
                            "label" => "in post test",
                            "slug" => "post_test",
                            "hours" => $postTestDuration,
                            "hours_float" => $postTestDurationMins / 60,
                        ],
                    ],
                ];
                
                //prepare course economics one to one
                $courseEconomics[] = [
                    "id" => $ceOnetoOne['id'],
                    "personalization" => [
                        'type' => '1-1',
                        'name' => '1-1',
                        'description' => 'Classes will be taught in a 1-to-1 setting, consisting of only one learner.',
                        'value' => true,    //Pending logic
                    ],
                    "price" => [
                        'variants' => [
                            [
                                'type' => [
                                    'based_on' => 'Personalization',
                                    'value' => '1-1'
                                ],
                                'name' => 'Course',
                                'list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => $oneToOnePrice]),
                                'selling_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $oneToOnePrice]),
                            ]
                        ]
                    ],
                    "what_you_get" => $whatYouGetOneToOne,
                    "expected_time_investment" => $expectedTimeInvestmentOneToOne,
                ];
            }
            
            return $this->schema->validate($courseEconomics, ['Refer#Course_Economics'], $filter);
        }

        return false;
        
        
    }


    /**
     * Retrieves the academy ID associated with a specific course.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the academy.
     * @param array $filter Additional filters for response formatting (optional).
     * @return mixed Returns the academy ID or false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getAcademyId($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch batch data from your data source (like a database or Elasticsearch)
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } else {
            return false;
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            return $course['academies'][0];
        }
    }

    

    /**
     * Retrieves a list of courses taught by a specific instructor.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param int $instructorId The ID of the instructor whose courses are to be fetched.
     * @return array|false Returns an array of courses associated with the instructor or false if no courses are found.
     * <AUTHOR>
     */
    public function getInstructorCourses($query, $instructorId)
    {
        if (!is_array($query)) {
            $query = ['id' => $query];
        }

        if (isset($query['id'])) {
            $courseSearchResponse = $this->es->read('course', 'course-' . $query['id']);
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body'];
                $res = [$res];
            } else {
                return false;
            }
        } elseif (!empty($query['custom'])) {
            $courseSearchResponse = $this->es->customQuery($query['custom'], 'course');
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body']['hits']['hits'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        $courseResponse = [];
        $row = ['items' => []];

        if ($res) {
            foreach ($res as $course) {
                $source = isset($course['_source']) ? $course['_source'] : $course;

                if (isset($source['data'])) {
                    $data = $source['data'];
                    $courseResponse[] = $data;

                    if (
                        isset($data['details']) &&
                        isset($data['details']['batch_details']) &&
                        is_array($data['details']['batch_details'])
                    ) {
                        foreach ($data['details']['batch_details'] as $batch) {
                            if (
                                isset($batch['instructor_id'], $batch['active_batch']) &&
                                $batch['instructor_id'] == $instructorId
                            ) {
                                $batch_name = isset($batch['batch_name']) ? $batch['batch_name'] : '';
                                $batch_id   = isset($batch['batch_id']) ? $batch['batch_id'] : 0;

                                // Ensure uniqueness in the items array.
                                $exists = array_filter($row['items'], function ($item) use ($batch_id) {
                                    return $item['id'] == $batch_id;
                                });
                                if (empty($exists)) {
                                    $row['items'][] = [
                                        'id'     => $batch_id,
                                        'label'  => $batch_name,
                                        'filter' => 'batch',
                                    ];
                                }
                            }
                        }
                    }
                }
            }

            return $row;
        }

        return false;
    }

    /**
     * Generates filters for a course based on the course ID and instructor ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $instructorId The ID of the instructor.
     * @return array Returns a filter structure for courses based on the instructor.
     * <AUTHOR>
     */
    public function generateCourseFiltersInstructor($courseId, $instructorId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'custom' => [
                "query" => [
                    "nested" => [
                        "path"  => "data.details",
                        "query" => [
                            "constant_score" => [
                                "filter" => [
                                    "bool" => [
                                        "must" => [
                                            "terms" => [
                                                "data.details.mapped_instructor_ids" => [$instructorId]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courses = $this->getCourses($customQuery, ['key'=>'data']);

        if (!empty($courses)) {
            foreach ($courses as $course) {
                $courseId = $course['id'] ?? 0;

                if (!empty($courseId)) {
                    $courseTitle = $course['title'] ?? '';
                    $row['items'][] = [
                        'id' => $courseId,
                        'label' => $courseTitle,
                        'filter' => 'course',
                    ];
                }
            }
        }
        return $row;
    }

    /**
     * Generates filters for a course based on the course ID and learner ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $learnerId The ID of the learner.
     * @return array Returns a filter structure for courses based on the learner.
     * <AUTHOR>
     */
    public function generateCourseFiltersLearner($courseId, $learnerId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.user_id' => $learnerId
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courseRecords = $this->es->customQuery($customQuery, 'batchenrollmentevent', []);

        if ($courseRecords['status_code'] == 200) {
            $courses = $courseRecords['body']['hits']['hits'];
        } else {
            return false;
        }
        $existingBatchIds = [];
        if (!empty($courses)) {
            foreach ($courses as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $course_id = $details['course_id'] ?? 0;
                $course_name = $details['course_name'] ?? '';

                if ($course_id && !in_array($course_id, $existingBatchIds)) {
                    $row['items'][] = [
                        'id' => $course_id,
                        'label' => $course_name,
                        'filter' => 'course',
                    ];
                    $existingBatchIds[] = $course_id;
                }
            }
        }
        return $row;
    }

    /**
     * Generates course filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The ID of the instructor (for filtering instructor-specific courses).
     * @param int $learnerId The ID of the learner (for filtering learner-specific courses).
     * @param int $counselorId The ID of the counselor.
     * @param int $courseId The selected course ID (if any).
     * @return array Returns an array containing course filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId)
    {

        return [
            'filter' => 'course_id',
            'title' => 'Course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'query_suggestion',
            'selected' => $courseId,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []  // Ensure `items` remains empty
        ];
    }
    /**
     * Retrieves course records associated with one or more academies.
     *
     * This method accepts an academy ID (or an array of IDs) and constructs an Elasticsearch query
     * to find courses that belong to the specified academies. It then extracts the course record IDs
     * from the Elasticsearch response.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID, or an array/associative array with an 'id' key containing the academy IDs.
     * @return array|bool Returns an array of course record IDs on success, an empty array if no courses are found,
     * or false if the query fails or input is invalid.
     * <AUTHOR>
     */
    public function getCoursesByAcademyId($query)
    {
        $query = is_array($query) ? $query : ['id' => $query];

        if (is_array($query)) {
            if (isset($query['id'])) {
                $academyIds = is_array($query['id']) ? $query['id'] : [$query['id']];
            } else {
                $academyIds = $query;
            }
        } else {
            $academyIds = [$query];
        }

        if (!empty($academyIds)) {
            $academyQuery = [
                'query' => [
                    'nested' => [
                        'path' => 'data.details',
                        'query' => [
                            'terms' => [
                                'data.details.academies' => $academyIds
                            ]
                        ]
                    ]
                ]
            ];

            $courseDataResponse = $this->es->customQuery($academyQuery, 'course', ['size' => 100]);
            if ($courseDataResponse['status_code'] != 200) {
                return false;
            }
        } else {
            return false;
        }
        $courseData = [];

        if (isset($courseDataResponse['status_code']) && $courseDataResponse['status_code'] == 200) {
            if (
                isset($courseDataResponse['body']['hits']) &&
                isset($courseDataResponse['body']['hits']['hits']) &&
                is_array($courseDataResponse['body']['hits']['hits'])
            ) {
                $hits = $courseDataResponse['body']['hits']['hits'];

                foreach ($hits as $hit) {
                    if (
                        isset($hit['_source']['data']['details']['record_id']) &&
                        !empty($hit['_source']['data']['details']['record_id'])
                    ) {
                        $courseData[] = $hit['_source']['data']['details']['record_id'];
                    }
                }
                return $courseData;
            } else {
                return [];
            }
        } else {
            return false;
        }
    }
}
