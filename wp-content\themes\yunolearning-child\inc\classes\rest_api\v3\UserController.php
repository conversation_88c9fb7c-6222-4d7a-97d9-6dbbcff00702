<?php
namespace V3;

/**
 * User controller
 */
use UserElasticSearch;
use Utility;
use WP_Error;
use WP_REST_Controller;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;
use WP_Structured_Logger;

define('V3\\MODULE_TITLE', 'User Info');
class UserController extends WP_REST_Controller
{
    const USER_REGION_ROUTE = '/user/region/(?P<userId>\d+)';
    const MODULE_NAME = '[Module_Name]';
    private $logger;
    public $namespace;
    public $yunoUserState;

    public function __construct()
    {
        $this->namespace = 'yuno/v3';
        $this->resource_name_1 = '/user/info/(?P<userId>\d+)';
        //for getting user info details
        $this->yunoUserRegionFetch = UserController::USER_REGION_ROUTE;
        //for adding/updating user info details
        $this->yunoUserRegionUpdate = UserController::USER_REGION_ROUTE;
        $this->yunoUserLangs = 'user/languages';
        $this->yunoUserTzones = 'user/timezones';
        $this->yunoUserCurrcis = 'user/currencies';
        $this->yunoUserState = 'user/state';
        $this->logger = WP_Structured_Logger::get_instance();
    }

    public function register_routes()
    {

        register_rest_route($this->namespace, $this->resource_name_1, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_info'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserRegionFetch, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_region_fetch'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserRegionUpdate, array(
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'user_region_update'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserLangs, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_languages_fetch'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserTzones, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_timezones_fetch'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserCurrcis, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_currencies_fetch'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->yunoUserState, array(
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'user_state_put'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));
    }

    /**
     * Token authorization check
     * This function common for all post login apis
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {return false;}
        list($bearer, $token) = explode(" ", $authToken);
        /*error_log("111jwt permission check 62: ".date('Y-m-d H:i:s')." === ". CURRENT_LOGGED_IN_USER_ID. "\n\n", 3, ABSPATH.'error-logs/jwt.log');*/
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token); // this is for postman
            if ($return) {return true;} else {return false;}

        }
        $codes = error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }

    /**
     * Getting user info
     * Current using for signup page for optimize performance speed up of login
     * call only 1 api instead of 3 apis
     */
    public function user_info($request)
    {
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        global $wpdb;
        error_log("Request object " . date("Y-m-d H:i:s") . " === " . json_encode($request) . "\n\n", 3, ABSPATH . "error-logs/Usercontrolllerv3.log");
        $userId = (int) $request['userId'];
        if ($userId > 0) {
        } else {
            return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));
        }

        $userdata = get_userdata($userId);
        if (empty($userdata)) {
            return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
        }
        $user_email = $userdata->data->user_email;
        $profileUserIdReference = get_user_meta($userId, 'profile_user_id_reference', true);
        $profileURL = get_permalink($profileUserIdReference);
        $profileURLArray = explode("/", $profileURL);
        $userProfileArray = array_filter($profileURLArray);
        $userProfileName = end($userProfileArray);
        $user_nicename = $userProfileName ?? "";
        $isHomePageSignup = get_user_meta($userId, 'Home_Page_Signup_Form', true);

        $roles = $userdata->roles ? $userdata->roles : '';
        if ($userdata->roles) {
            if (in_array('um_instructor', $userdata->roles)) {
                $current_role = 'Instructor';
            } else if (in_array('um_counselor', $userdata->roles)) {
                $current_role = 'Counselor';
            } else if (in_array('um_yuno-admin', $userdata->roles)) {
                $current_role = 'yuno-admin';
            } else if (in_array('um_content-admin', $userdata->roles)) {
                $current_role = 'content-admin';
            } else if (in_array('um_yuno-category-admin', $userdata->roles)) {
                $current_role = 'yuno-category-admin';
            } else if (in_array('administrator', $userdata->roles)) {
                $current_role = 'administrator';
            } else if (in_array('um_dashboard-viewer', $userdata->roles)) {
                $current_role = 'dashboard-viewer';
            } else if (in_array('um_org-admin', $userdata->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'Learner';
            }
        } else {
            return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
        }
        $signupDetail = get_user_meta($userId, 'is_signup_complete', true);
        if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
            $isSignup = "completed";
            $isInvited = false;
            $withoutCategory = false;
        } else {
            if (!empty($isHomePageSignup)) {
                $withoutCategory = true;
            } else {
                $withoutCategory = false;
            }
            $isInvited = get_user_meta($userId, 'Yuno_Is_Invited', true);
            if ($isInvited == 1) {
                $isInvited = true;
            } else {
                $isInvited = false;
            }
            $isSignup = "pending";
            $classEnrollmentStatus = get_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_STATUS', true);
            if ($classEnrollmentStatus == true) {
                $hasWebinarEnrolled = true;
            }
        }
        $l_category = get_user_meta($userId, 'Category_URL_For_Signup', true);
        if (is_array($l_category) && count($l_category) > 0) {
            $learnerCategory = $l_category;
        } else {
            if (empty($l_category)) {
                $learnerCategory = [];
            } else {
                if (strpos($l_category, '/') !== false) {
                    $cat = strtolower(ltrim($l_category, '/'));
                } else {
                    $cat = strtolower($l_category);
                }

                if ($cat == trim($cat) && strpos($cat, ' ') !== false) {
                    $learner_cat = str_replace(' ', '-', $cat);
                    $learnerCategory = explode(" ", $learner_cat);
                } else {
                    $learnerCategory = explode(" ", $cat);
                }
            }
        }

        $orgs_id = [
            'id' => 0,
            'name' => '',
            'image' => '',
        ];
        $org_id[] = $orgs_id;
        // get active category
        $active_category = get_user_meta($userId, 'active_category', true);

        // Initialize $active_category_obj with default values
        $active_category_obj = [
            'id' => 0,
            'slug' => '',
            'label' => '',
        ];

        if (!empty($active_category)) {
            // Assume $taxonomy is your custom taxonomy name, and $slug is the slug of the category you're interested in
            $taxonomy = 'course_category';

            // Get the term object by slug
            $term = get_term_by('slug', $active_category, $taxonomy);

            // Check if the term exists and there are no errors
            if ($term !== false && !is_wp_error($term)) {
                // Update $active_category_obj with the term's details
                $active_category_obj = [
                    'id' => (int) $term->term_id,
                    'slug' => $term->slug,
                    'label' => $term->name,
                ];
            }
        }
        $mobile = get_user_meta($userId, 'yuno_gplus_mobile', true);
        $has_phone_number = false;
        if (!empty($mobile)) {
            $has_phone_number = true;
        }
        $category_slug =  $term->slug ?? "";
        $defined_org = 0;
        $organisations = get_user_meta($userId, 'organisation', true);
        if (!empty($organisations) && is_array($organisations) && count($organisations) == 1) {
            $defined_org = $organisations[0]; 
        }
        // get active org
        $active_org = get_user_meta($userId, 'active_org', true);
        $active_org = !empty($active_org) ? (int)$active_org : $defined_org;
        if (get_user_meta($userId, 'active_org', true) <= 0) {
            update_user_meta($userId, 'active_org', $defined_org);
        }
        $redirect_url = get_user_meta($userId, 'redirect_url', true) ?? "";
        $current_state = ["has_phone_number" => $has_phone_number, "category_slug" => $category_slug, "org_id" => (int)$active_org, "redirect_url" => $redirect_url];
        if ($current_role == "Learner") {
            $classEnrolledUser = $wpdb->get_results("SELECT * FROM wp_postmeta WHERE meta_key = 'YunoClassPrivateLearners' and FIND_IN_SET('" . $userId . "', meta_value) > 0", ARRAY_A);
            if (count($classEnrolledUser) > 0) {
                $hasEnrolledInClassBefore = true;
            } else {
                $hasEnrolledInClassBefore = false;
            }
            date_default_timezone_set('Asia/Kolkata');
            $currentDate = date("Y-m-d H:i:s");
            $enrollmentData = [];
            $enrollmentFalseStatusData = [];
            $data = $wpdb->get_results("SELECT id,product_db_id, enrollment_end_date, enrollment_status FROM wp_enrollment WHERE user_id='" . $userId . "'", ARRAY_A);
            foreach ($data as $key => $value) {
                if ($currentDate < $value['enrollment_end_date']) {
                    $enrollmentData[] = [
                        "course_id" => $value['product_db_id'],
                        "id" => $value['id'],
                    ];
                }
            }

            // get category belongs to logged in learner
            if (count($enrollmentData) > 0) {
                $category = [];
                foreach ($enrollmentData as $key => $value) {
                    $categories = wp_get_post_terms((int) $value['course_id'], 'course_category');
                    foreach ($categories as $k => $v) {
                        if ($v->parent == 0) {
                            $category[] = strtolower($v->slug);
                        }
                    }
                }

                $userData = [
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                    'profile_name' => $user_nicename,
                    "mobile" => $mobile,
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => true,
                    'isInvited' => $isInvited,
                    'current_state' => $current_state,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj,
                ];
            } else {
                $enrollmentStatus = "INACTIVE";
                $update_status = [
                    'enrollment_status' => $enrollmentStatus,
                ];
                foreach ($data as $key => $value) {
                    if ($currentDate > $value['enrollment_end_date']) {
                        $enrollmentFalseStatusData[] = [
                            "id" => $value['id'],
                        ];
                    }
                }
                if (count($enrollmentFalseStatusData) > 0) {
                    foreach ($enrollmentFalseStatusData as $key => $value) {
                        $wpdb->update('wp_enrollment', $update_status, ['id' => $value['id']]);
                    }
                }

                $userData = [
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                    'profile_name' => $user_nicename,
                    "mobile" => $mobile,
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => false,
                    'isInvited' => $isInvited,
                    'current_state' => $current_state,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj,
                ];
            }
        } else {
            if (in_array('um_instructor', $userdata->roles)) {
                $is_completed_step_3 = get_user_meta($userId, 'is_completed_step_3', true);
                if ($is_completed_step_3) {
                    if ($is_completed_step_3 == "yes") {
                        $isSignup = "completed";
                    } else {
                        $isSignup = "pending";
                    }
                } else {
                    $isSignup = "pending";
                }
                $courseToMap = get_user_meta($userId, 'course_to_be_map', true);
                if (empty($courseToMap)) {
                    $courseToMap = [];
                }
                 // Elasticsearch query to fetch unique academies
                 $curlPost = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => [
                                        "value" => $userId  // Use the appropriate instructor_id here
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "unique_academies" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "unique_academy_values" => [
                                    "terms" => [
                                        "field" => "data.details.academies",
                                        "size" => 10000  // Adjust size based on expected results
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false  // Exclude source fields
                ];
                $elasticsearch_url = ELASTIC_SEARCH_END_URL . '/course/_search';
                $headers = [
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache"
                  ];
                
                $response = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);
                // Decode the response from Elasticsearch
                $esResponse = json_decode($response['response'], true);
                

                // Extract the unique academies from the response
                $academyIds = [];
                if (isset($esResponse['aggregations']['unique_academies']['unique_academy_values']['buckets'])) {
                    foreach ($esResponse['aggregations']['unique_academies']['unique_academy_values']['buckets'] as $bucket) {
                        $academyIds[] = $bucket['key'];  // The unique academy ID
                    }
                }

                $academyQuery = [
                    "query" => [
                        "terms" => [
                            "data.details.record_id" => $academyIds  // Assuming the academy ID is stored in the _id field of the academy index
                        ]
                    ]  // Adjust this field name to match the name of the field in your academy index
                ];

                $elasticSearchUrl = ELASTIC_SEARCH_END_URL . '/academies/_search';


                $academyResponse = Utility::curl_request($elasticSearchUrl, 'GET', $academyQuery, $headers, ELASTIC_SEARCH_PORT);


                // Decode the academy response
                $academyResponseData = json_decode($academyResponse['response'], true);

                // Step 3: Combine Academy IDs and Names
                $academies = [];
                if (isset($academyResponseData['hits']['hits'])) {
                    foreach ($academyResponseData['hits']['hits'] as $hit) {
                        $academy = [
                            'id' => $hit['_source']['data']['details']['record_id'],  // Academy ID
                            'name' => $hit['_source']['data']['details']['academy_name'] ?? ""   // Academy name (adjust the field name if necessary)
                        ];
                        $academies[] = $academy;
                    }
                }
                $course_ids = $wpdb->get_results("SELECT course_id from wp_course_instructor_relationships where instructor_id=$userId", ARRAY_A);
                $vc_flag = false;
                foreach ($course_ids as $key => $value) {
                    $course_id = (int)$value['course_id'];
                    $organization_id = get_post_meta($course_id, 'organization', true);
                    if ($organization_id > 0) {
                        $org_vc_app = get_post_meta($organization_id, 'org_vc_app', true);
                        if ($org_vc_app == "gmeet" || $org_vc_app == "zoom") {
                            $vc_flag = true;
                            break;
                        }
                    }
                }
                update_user_meta($userId, 'vc_permission', $vc_flag);
            }
            if (in_array('um_org-admin', $userdata->roles)) {
                if (!empty(get_user_meta($userId, 'organisation', true))) {
                    $org_id = [];
                    $orgs_id = get_user_meta($userId, 'organisation', true); # code...
                    foreach ($orgs_id as $id) {
                        $logo_image = get_field('logo_image', $id);
                        $org_id[] = [
                            'id' => (int) $id,
                            'name' => get_post_meta($id, 'organisation_name', true), //organisation_name
                            'image' => $logo_image['url'] ?? "", //logo_image
                        ];
                    }
                }
            }

            $has_org = false;
            foreach ($org_id as $id) {
                if ($id['id'] != 0) {
                    $has_org = true;
                }
            }

            $userData = [
                "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                'email' => $user_email,
                'role' => $current_role,
                'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                'profile_name' => $user_nicename,
                "mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
                "signup_category" => array_values($learnerCategory),
                "is_signup_completed" => $isSignup,
                "is_lead_created" => true,
                'isInvited' => $isInvited,
                'org_id' => $org_id,
                'has_org' => $has_org, //organisation
                'account_status' => get_user_meta($userId, 'account_login_status', true),
                'current_state' => $current_state,
                'withoutCategory' => $withoutCategory,
                'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                'courses_to_be_map' => $courseToMap,
            ];

            if (in_array('um_instructor', $userdata->roles)) {
                if(!empty($academies)){ // Check if $academies is not empty
                    $userData['academies'] = $academies;
                }
            } 
        }

        if (!empty($hasWebinarEnrolled)) {
            $userData = array_merge($userData, ["hasWebinarEnrolled" => $hasWebinarEnrolled]);
        }

        $response = array(
            'code' => $codes["GET_SUCCESS"]["code"],
            'message' => 'User info found',
            "data" => $userData,
        );
        track_logs(time(), "user_info", '', time(), "end", 'user');
        return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    }

    /**
     * Getting user region detail
     * Update user information on user setting page
     * Save country, timezone, currency and language detail
     */
    public function user_region_fetch($request)
    {
        $userId = (int) $request['userId'];

        // Get error codes
        $codes = error_code_setting();

        // Validate each field using the common functions
        $validation_checks = [
            'userId' => 'numeric',
        ];

        $errors = [];

        foreach ($validation_checks as $key => $type) {
            $result = Utility::validate_required(['userId' => $userId], $key, $type);
            if (is_wp_error($result)) {
                $errors[] = $result;
            }
        }

        try {
            if (!empty($errors)) {
                // Return error if validation fails
                foreach ($errors as $error) {
                    $error_message = $error->get_error_message() . "\n";
                    return new WP_Error(
                        $codes["GET_FAIL"]["code"],
                        $error_message,
                        array('status' => $codes["GET_FAIL"]["status"])
                    );
                }
            } else {
                if ($userId <= 0) {
                    return new WP_Error(
                        $codes["USER_ID_FAIL"]["code"],
                        $codes["USER_ID_FAIL"]["message"],
                        array('status' => $codes["USER_ID_FAIL"]["status"])
                    );
                }

                $userdata = get_userdata($userId);

                if (!$userdata) {
                    return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
                }

                $roles = $userdata->roles ? $userdata->roles : '';
                if ($userdata->roles) {
                    if (in_array('um_instructor', $userdata->roles)) {
                        $current_role = 'Instructor';
                    } else if (in_array('um_counselor', $userdata->roles)) {
                        $current_role = 'Counselor';
                    } else if (in_array('um_yuno-admin', $userdata->roles)) {
                        $current_role = 'yuno-admin';
                    } else if (in_array('um_content-admin', $userdata->roles)) {
                        $current_role = 'content-admin';
                    } else if (in_array('um_yuno-category-admin', $userdata->roles)) {
                        $current_role = 'yuno-category-admin';
                    } else if (in_array('administrator', $userdata->roles)) {
                        $current_role = 'administrator';
                    } else if (in_array('um_dashboard-viewer', $userdata->roles)) {
                        $current_role = 'dashboard-viewer';
                    } else if (in_array('um_org-admin', $userdata->roles)) {
                        $current_role = 'org-admin';
                    } else {
                        $current_role = 'Learner';
                    }
                } else {
                    return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
                }

                // GET database with new payload

                $userInfoSettings = [
                    "country" => [
                        "id" => (int) get_user_meta($userId, 'yuno_user_info_country_id', true),
                        "name" => get_user_meta($userId, 'yuno_user_info_country_name', true),
                        "country_code" => get_user_meta($userId, 'yuno_user_info_country_code', true),
                    ],
                    "timezone" => get_user_meta($userId, 'yuno_user_info_timezone', true),
                    "currency" => [
                        "code" => get_user_meta($userId, 'yuno_user_info_currency_code', true),
                        "name" => get_user_meta($userId, 'yuno_user_info_currency_name', true),
                        "symbol" => get_user_meta($userId, 'yuno_user_info_currency_symbol', true),
                        "symbol_html" => get_user_meta($userId, 'yuno_user_info_currency_symbol_html', true),
                    ],
                    "language" => [
                        "name_in_english" => get_user_meta($userId, 'yuno_user_info_language_name', true),
                        "native_lang_name" => get_user_meta($userId, 'yuno_user_info_language_native', true),
                        "code" => get_user_meta($userId, 'yuno_user_info_language_code', true),
                    ],
                ];

                $result = array(
                    'code' => $codes["GET_SUCCESS"]["code"],
                    'message' => str_replace(UserController::MODULE_NAME, \V3\MODULE_TITLE, $codes["GET_SUCCESS"]["message"]),
                    'status' => $codes["GET_SUCCESS"]["status"],
                    'data' => $userInfoSettings,
                );
                return new WP_REST_Response($result, 200);
            }
        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                $message,
                array('status' => $codes["GET_FAIL"]["status"])
            );
        }
    }

    public function user_region_update($request)
    {
        $userId = (int) $request['userId'];

        // Get error codes
        $codes = error_code_setting();
        // Get request data
        $jbody = $payload = json_decode($request->get_body(), true);

        // Validate payload
        if (empty($jbody)) {
            // Return error if payload is empty
            return new WP_Error(
                $codes["PUT_UPDATE_FAIL"]["code"],
                str_replace(UserController::MODULE_NAME, \V3\MODULE_TITLE, $codes["PUT_UPDATE_FAIL"]["message"]),
                array('status' => $codes["PUT_UPDATE_FAIL"]["status"])
            );
        }

        $jbody = Utility::flatten_array($jbody);

        // Validate each field using the common functions

        $validation_checks = [
            "country" => [
                "id" => '/^[0-9]{1,5}$/',
                "name" => '/^[a-zA-Z_\s-]{1,255}$/',
                "country_code" => '/^[A-Z]{2}$/',
            ],
            "timezone" => '/^[a-zA-Z-_\/]{1,255}$/',
            "currency" => [
                "code" => '/^(INR|USD|AED)$/',
                "name" => '/^[a-zA-Z_\s-]{1,255}$/',
                "symbol" => '/^(₹|\$|د\.إ)$/',
                "symbol_html" => 'string',
            ],
            "language" => [
                "name_in_english" => '/^[a-zA-Z_\s-]{1,255}$/',
                "native_lang_name" => '/^[a-zA-Z_\s-]{1,255}$/',
                "code" => '/^[a-zA-Z]{2}$/',
            ],
        ];

        $validation_checks = Utility::flatten_array($validation_checks);

        $errors = [];

        foreach ($validation_checks as $key => $type) {
            $result = Utility::validate_required($jbody, $key, $type);
            if (is_wp_error($result)) {
                $errors[] = $result;
            }
        }

        try {
            if (!empty($errors)) {
                // Return error if validation fails
                foreach ($errors as $error) {
                    $error_message = $error->get_error_message() . "\n";
                    return new WP_Error(
                        $codes["PUT_UPDATE_FAIL"]["code"],
                        $error_message,
                        array('status' => $codes["PUT_UPDATE_FAIL"]["status"])
                    );
                }
            } else {
                if ($userId <= 0) {
                    return new WP_Error(
                        $codes["USER_ID_FAIL"]["code"],
                        $codes["USER_ID_FAIL"]["message"],
                        array('status' => $codes["USER_ID_FAIL"]["status"])
                    );
                }

                $userdata = get_userdata($userId);

                if (!$userdata) {
                    return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
                }

                $roles = $userdata->roles ? $userdata->roles : '';
                if ($userdata->roles) {
                    if (in_array('um_instructor', $userdata->roles)) {
                        $current_role = 'Instructor';
                    } else if (in_array('um_counselor', $userdata->roles)) {
                        $current_role = 'Counselor';
                    } else if (in_array('um_yuno-admin', $userdata->roles)) {
                        $current_role = 'yuno-admin';
                    } else if (in_array('um_content-admin', $userdata->roles)) {
                        $current_role = 'content-admin';
                    } else if (in_array('um_yuno-category-admin', $userdata->roles)) {
                        $current_role = 'yuno-category-admin';
                    } else if (in_array('administrator', $userdata->roles)) {
                        $current_role = 'administrator';
                    } else if (in_array('um_dashboard-viewer', $userdata->roles)) {
                        $current_role = 'dashboard-viewer';
                    } else if (in_array('um_org-admin', $userdata->roles)) {
                        $current_role = 'org-admin';
                    } else {
                        $current_role = 'Learner';
                    }
                } else {
                    return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
                }

                // Update database with new payload

                update_user_meta($userId, 'yuno_user_info_country_id', $payload['country']['id']);
                update_user_meta($userId, 'yuno_user_info_country_name', $payload['country']['name']);
                update_user_meta($userId, 'yuno_user_info_country_code', $payload['country']['country_code']);
                update_user_meta($userId, 'yuno_user_info_timezone', $payload['timezone']);
                update_user_meta($userId, 'yuno_user_info_currency_code', $payload['currency']['code']);
                update_user_meta($userId, 'yuno_user_info_currency_name', $payload['currency']['name']);
                update_user_meta($userId, 'yuno_user_info_currency_symbol', $payload['currency']['symbol']);
                update_user_meta($userId, 'yuno_user_info_currency_symbol_html', $payload['currency']['symbol_html']);
                update_user_meta($userId, 'yuno_user_info_language_name', $payload['language']['name_in_english']);
                update_user_meta($userId, 'yuno_user_info_language_native', $payload['language']['native_lang_name']);
                update_user_meta($userId, 'yuno_user_info_language_code', $payload['language']['code']);

                /*$region_obj = [
                "country_id" => $payload['country']['id'],
                "country" => $payload['country']['name'],
                "country_code" => $payload['country']['code'],
                "timezone" => $payload['timezone'],
                "currency" => $payload['currency']['code'],
                "currency_name" => $payload['currency']['name'],
                "currency_symbol" => $payload['currency']['symbol'],
                "currency_symbol_html" => $payload['currency']['symbol_html'],
                "language" => $payload['language']['name_in_english'],
                "language_native" => $payload['language']['native_lang_name'],
                "language_code" => $payload['language']['code']
                ];*/
                $region_obj = [
                    "country" => [
                        "id" => (int) $payload['country']['id'],
                        "name" => $payload['country']['name'],
                        "code" => $payload['country']['country_code'],
                    ],
                    "timezone" => $payload['timezone'],
                    "currency" => [
                        "code" => $payload['currency']['code'],
                        "name" => $payload['currency']['name'],
                        "symbol" => $payload['currency']['symbol'],
                        "symbol_html" => $payload['currency']['symbol_html'],
                    ],
                    "language" => [
                        "name_in_english" => $payload['language']['name_in_english'],
                        "native_lang_name" => $payload['language']['native_lang_name'],
                        "code" => $payload['language']['code'],
                    ],
                ];

                $curlPost['data'] = [
                    "data" => [
                        "details" => [
                            "user_id" => $userId,
                            "role" => $current_role,
                            "region" => $region_obj,
                        ],
                    ],
                ];

                UserElasticSearch::update_signedup("region_details", $curlPost);

                $result = array(
                    'code' => $codes["PUT_UPDATE"]["code"],
                    'message' => str_replace(UserController::MODULE_NAME, \V3\MODULE_TITLE, $codes["PUT_UPDATE"]["message"]),
                    'status' => $codes["PUT_UPDATE"]["status"],
                );
                return new WP_REST_Response($result, 200);
            }
        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $this->logger->custom_log('info', \V3\MODULE_TITLE, 'user_state_put', $message, [], []);
            return new WP_Error(
                $codes["PUT_UPDATE_FAIL"]["code"],
                $message,
                array('status' => $codes["PUT_UPDATE_FAIL"]["status"])
            );
        }

    }

    public function user_languages_fetch()
    {
        $codes = error_code_setting();

        try {

            $languages = array(
                array(
                    "name_in_english" => "English",
                    "native_lang_name" => "English",
                    "code" => "en",
                ),
            );

            $result = array(
                'code' => $codes["GET_SUCCESS"]["code"],
                'message' => str_replace(UserController::MODULE_NAME, "Languages", $codes["GET_SUCCESS"]["message"]),
                'status' => $codes["GET_SUCCESS"]["status"],
                'data' => $languages,
            );
            return new WP_REST_Response($result, 200);

        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                $message,
                array('status' => $codes["GET_FAIL"]["status"])
            );
        }
    }

    public function user_timezones_fetch()
    {
        $codes = error_code_setting();

        try {

            $timezones = array();
            foreach (timezone_identifiers_list() as $tKey => $tVal) {
                $timezones[] = $tVal;
            }
            $result = array(
                'code' => $codes["GET_SUCCESS"]["code"],
                'message' => str_replace(UserController::MODULE_NAME, "Timezones", $codes["GET_SUCCESS"]["message"]),
                'status' => $codes["GET_SUCCESS"]["status"],
                'data' => $timezones,
            );
            return new WP_REST_Response($result, 200);

        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                $message,
                array('status' => $codes["GET_FAIL"]["status"])
            );
        }
    }

    public function user_currencies_fetch()
    {
        $codes = error_code_setting();

        try {

            $currencies = array(
                array(
                    "code" => "INR",
                    "name" => "Indian Rupee",
                    "symbol" => "₹",
                    "symbol_html" => "&#8377;",
                ),
                array(
                    "code" => "USD",
                    "name" => "US Dollar",
                    "symbol" => "$",
                    "symbol_html" => "&#36;",
                ),
                array(
                    "code" => "AED",
                    "name" => "UAE Dirham",
                    "symbol" => "د.إ",
                    "symbol_html" => "&#x62f;&#x2e;&#x625;",
                ),
            );

            $result = array(
                'code' => $codes["GET_SUCCESS"]["code"],
                'message' => str_replace(UserController::MODULE_NAME, "Currencies", $codes["GET_SUCCESS"]["message"]),
                'status' => $codes["GET_SUCCESS"]["status"],
                'data' => $currencies,
            );
            return new WP_REST_Response($result, 200);

        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                $message,
                array('status' => $codes["GET_FAIL"]["status"])
            );
        }
    }
    public function user_state_put($request)
    {
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);
        $user_id = (int) $data['user_id'];
        $post_id = (int) $data['org_id'];
        $category_slug = $data['category_slug']; // it should be slug
        $allowed_roles = ["SEO Manager", "um_org-admin", "um_instructor"];
        $userdata = get_userdata($user_id);
        $data = [
            'user_id' => $user_id
        ];
        // Validate each field using the common functions
        $validation_checks = [
            'user_id' => 'integer'
        ];
        $errors = [];

        foreach ($validation_checks as $key => $type) {
        $result = Utility::validate_required($data, $key, $type);
            if (is_wp_error($result)) {
                $errors[] = $result;
            }
        }
        try {
            // Output validation results
            if (!empty($errors)) {
                foreach ($errors as $error) {
                    $error_message = $error->get_error_message() . "\n";
                    return new WP_Error($codes["PUT_UPDATE_FAIL"]["code"], $error_message, array('status' => $codes["PUT_UPDATE_FAIL"]["status"]));                
                }	
            } 
            else {
                if (!$userdata) {
                    $response = [
                        'code' => $codes["USER_FAIL"]["code"],
                        'message' => $codes["USER_FAIL"]["message"],
                        'status' => $codes["USER_FAIL"]["status"],
                      ];
                    return rest_ensure_response($response);	
                } 
                if (!in_array($userdata->roles[0], $allowed_roles)) {
                        $response = [
                            'code' => $codes["PUT_UPDATE_FAIL"]["code"],
                            'message' => "Invalid user role",
                            'status' => $codes["PUT_UPDATE_FAIL"]["status"],
                          ];
                      return rest_ensure_response($response); 
                }
                if (!empty($post_id)) {
                    $current_post_status = get_post_status($post_id);
                    if ($current_post_status != "publish") {
                        $response = [
                            'code' => $codes["PUT_UPDATE_FAIL"]["code"],
                            'message' => "Invalid org id",
                            'status' => $codes["PUT_UPDATE_FAIL"]["status"],
                          ];
                      return rest_ensure_response($response);                        
                    }
                }
                if (!empty($category_slug)) {
                    $terms = get_terms(array(
                        'taxonomy'  => 'course_category',
                        'hide_empty' => false,
                        'parent'    => 0,
                        'order'     => 'ASC',
                    ));
                    $slugs = [];
                    foreach ($terms as $key => $value) {
                        $slugs[] = $value->slug;
                    }
                    if (!in_array($category_slug, $slugs)) {
                        $response = [
                            'code' => $codes["PUT_UPDATE_FAIL"]["code"],
                            'message' => "Invalid category slug",
                            'status' => $codes["PUT_UPDATE_FAIL"]["status"],
                          ];
                      return rest_ensure_response($response); 
                    }
                    update_user_meta($user_id, 'active_category', $category_slug);
                }
                if (!empty($post_id)) {
                    update_user_meta($user_id, 'active_org', $post_id);
                }
                $result = array(
                    'code' => $codes["PUT_UPDATE"]["code"],
                    'message' => str_replace(UserController::MODULE_NAME, "User State", $codes["PUT_UPDATE"]["message"]),
                    'status' => $codes["PUT_UPDATE"]["status"]
                );
                return new WP_REST_Response($result, 200);
            }
        } catch (\Exception $e) {
            // Return error if an exception occurs
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $this->logger->custom_log('info', \V3\MODULE_TITLE, 'user_state_put', $message, [], []);
            return new WP_Error(
                $codes["PUT_UPDATE_FAIL"]["code"],
                $message,
                array('status' => $codes["PUT_UPDATE_FAIL"]["status"])
            );
        }
    }
}
