<?php 

/**
 * Template Name: <PERSON><PERSON> Single Batch Page
 * Template Post Type: batch
 *
 * @package WordPress
 * @subpackage Yuno
 */
// Get the 'created_batch_id' meta field for the post.
$batch_post_id = get_the_ID();
$batch_id_meta = get_post_meta($batch_post_id, 'created_batch_id', true);
//If get_post_meta() fails or 'created_batch_id' is not set, it returns an empty string.
// Use strict comparison to check for this case.
$batch_id = ($batch_id_meta === '') ? 0 : $batch_id_meta;
//apply_filters( 'get_batch_course_id', '' );

    get_header();

?>
    <yuno-batch-page-v2></yuno-batch-page-v2>
<?php get_footer();?>

<script>
    const batchID = <?php echo $batch_id; ?>; 
    let yunoCategory = "";
</script>

