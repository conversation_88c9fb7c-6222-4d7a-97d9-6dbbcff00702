<?php
return [
    'type' => 'string', // Type of address: office, home, other enum ['HOME', 'OFFICE', 'OTHER']
    'title' => 'string', // Title of the address: Home, Office, Other
    'formatted_address' => 'string', // Google formatted address
    'address_1' => 'string', // User's complete address input text
    'address_2' => 'string', // Area/Sector/Locality
    'floor' => [
        'type' => 'string', // GROUNDPLUS or ONEPLUS
        'number' => 'string', // floor number 1st, 2nd or 3rd so on
    ],
    'landmark' => 'string',
    'city' => 'string',
    'state' => 'Refer#State',
    'country' => 'Refer#Country',
    'pin_zip' => 'string', // PIN code or zip code
    'coordinates' => 'Refer#Place_Coordinates',
    'google_maps' => 'Refer#Google_Maps'
];
