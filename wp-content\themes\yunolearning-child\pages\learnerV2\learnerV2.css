#app .yunoTabs .yunoTabNav {
  top: 0;
}

#app .yunoTabs .tabsContent {
  padding-left: 15px;
  padding-right: 15px;
}

#app .yunoTabs .ctaWrapper {
  margin-top: 30px;
}

#app .yunoFormWrapper {
  padding-bottom: 30px;
}

#app .yunoFormWrapper .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

#app .pageGrid .mainBody {
  position: relative;
  z-index: 7;
}

#app .yunoDropdown {
  min-width: 0;
  width: 100%;
}

#app .yunoDropdown .labelWrapper {
  padding: 5px 35px 4px 15px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}

#app .yunoDropdown .labelWrapper:hover {
  border-color: #A81E22;
}

#app .yunoDropdown .labelWrapper .placeHolder {
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
}

#app .yunoDropdown .labelWrapper .icon {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}

#app .yunoDropdown .labelWrapper .clearFilter {
  position: absolute;
  right: 35px;
  bottom: -2px;
}

#app .yunoDropdown .labelWrapper .selectedItem {
  font-weight: 400;
  padding-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  display: block;
  padding-right: 27px;
  font-size: 14px;
}

#app .yunoDropdown .labelWrapper .selectedItem span {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem span:last-child::after {
  display: none;
}

#app .yunoDropdown .labelWrapper .selectedItem span::after {
  content: ",";
  position: relative;
  left: -2px;
}

#app .yunoDropdown .dropdown-menu {
  width: 100%;
  padding: 0;
}

#app .yunoDropdown .dropdown-menu a {
  color: rgba(0, 0, 0, 0.6);
  padding: 5px 15px;
}

#app .yunoDropdown .dropdown-menu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
}

#app .yunoDropdown .dropdown-menu a.dropdown-item.is-active {
  background-color: #FBEEEC;
  color: #534342;
}

#app .yunoDropdown .timesDays {
  padding-bottom: 10px;
}

#app .yunoDropdown .timesDays small {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .yunoDropdown .timesDays .wrapper {
  padding: 10px 15px 0;
}

#app .yunoDropdown .timesDays ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 10px -5px 0;
}

#app .yunoDropdown .timesDays ul li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  padding: 0 5px 5px;
}

#app .yunoDropdown .timesDays ul li .inner {
  text-align: center;
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  padding: 5px;
  cursor: pointer;
}

#app .yunoDropdown .timesDays ul li .inner:hover {
  border-color: #A81E22;
  color: #A81E22;
}

#app .yunoDropdown .timesDays ul li .material-icons {
  display: block;
  margin-bottom: 5px;
}

#app .yunoDropdown .timesDays ul li .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  display: block;
  margin-bottom: 5px;
}

#app .yunoDropdown .timesDays ul li.active .inner {
  background-color: #A81E22;
  color: white;
}

#app .yunoDropdown .dropdown {
  width: 100%;
}

#app .yunoDropdown .dropdown .dropdown-menu .has-link a {
  white-space: normal !important;
}

#app .yunoDropdown .dropdown-trigger {
  width: 100%;
}

#app .yunoDropdown.hideColumn .dropdown-menu {
  left: auto;
  right: 0;
}

#app .yunoDropdown.hideColumn .dropdown-menu .listCaption {
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active {
  color: rgba(0, 0, 0, 0.6);
  background-color: #FFF;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .hasActiveInactive .dropdown-item.is-active::before {
  font-size: 20px;
  padding-right: 5px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .percentageBlock .dropdown-item.is-active::before {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item::before {
  font-size: 18px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .item.iconWithLabel .dropdown-item.is-active::before {
  font-size: 18px;
  padding-right: 5px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .item.iconWithLabel .green.dropdown-item.is-active::before {
  color: rgba(0, 128, 0, 0.728);
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .item.iconWithLabel .grey.dropdown-item.is-active::before {
  color: rgba(128, 128, 128, 0.563);
}

#app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid.recentClasses .info li .dropdown-item.is-active::before {
  margin-right: 10px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before {
  font-size: 28px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .timesDays ul li .dropdown-item.is-active::before {
  display: block;
  margin-bottom: 5px;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
  white-space: normal !important;
}

#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 6px;
  font-size: 18px;
}

#app .yunoDropdown.hideColumn .timesDays ul li .dropdown-menu .dropdown-item.is-active::before {
  display: block;
  margin-bottom: 5px;
}

#app .yunoTable .b-table .table thead {
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoTable .b-table .table .catList .dropdown-content a {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoTable .b-table .table tbody {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoTable .b-table .table tbody .title {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .info li.hasArray .caption.active {
  color: #201A19;
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .info li .tooltip-content {
  color: #201A19;
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .info li .material-icons {
  margin-right: 10px;
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .info li .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  margin-right: 10px;
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .info li .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  margin-right: 10px;
}

#app .yunoTable .b-table .table tbody .grid.recentClasses .tooltip-content {
  color: #201A19;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive .material-icons {
  font-size: 20px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  font-size: 20px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .hasActiveInactive .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  font-size: 20px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .percentageBlock .material-icons {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .yunoTable .b-table .table tbody .percentageBlock .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .yunoTable .b-table .table tbody .percentageBlock .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .yunoTable .b-table .table tbody .grid .material-icons-outlined {
  font-size: 18px;
}

#app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before {
  font-size: 18px;
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .material-icons {
  font-size: 18px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  font-size: 18px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .yunoDropdown.hideColumn .dropdown-menu .green.dropdown-item.is-active::before {
  color: rgba(0, 128, 0, 0.728);
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .yunoDropdown.hideColumn .dropdown-menu .grey.dropdown-item.is-active::before {
  color: rgba(128, 128, 128, 0.563);
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  font-size: 18px;
  padding-right: 5px;
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .filters .yunoAutocompleteSearch .field .control .icon .green.mdi::after {
  color: rgba(0, 128, 0, 0.728);
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .filters .yunoAutocompleteSearch .field .control .icon .grey.mdi::after {
  color: rgba(128, 128, 128, 0.563);
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .material-icons.green {
  color: rgba(0, 128, 0, 0.728);
}

#app .yunoTable .b-table .table tbody .grid .item.iconWithLabel .material-icons.grey {
  color: rgba(128, 128, 128, 0.563);
}

#app .yunoTable .note {
  color: rgba(0, 0, 0, 0.6);
}

#app .mainHeader .gridInfo .actions {
  color: rgba(0, 0, 0, 0.87);
}

#app .mainHeader .gridInfo .actions li a {
  color: rgba(0, 0, 0, 0.87);
}

#app .mainHeader .gridInfo .note {
  color: rgba(0, 0, 0, 0.6);
}

#app .mainHeader .gridInfo .note span {
  color: rgba(0, 0, 0, 0.87);
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .hasActiveInactive .mdi::after {
  font-size: 20px;
  padding-right: 5px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .percentageBlock .mdi::after {
  font-size: 12px;
  position: relative;
  top: 2px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .item.iconWithLabel .mdi::after {
  font-size: 18px;
  padding-right: 5px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .item.iconWithLabel .green.mdi::after {
  color: rgba(0, 128, 0, 0.728);
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .item.iconWithLabel .grey.mdi::after {
  color: rgba(128, 128, 128, 0.563);
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid.recentClasses .info li .mdi::after {
  margin-right: 10px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .filterTrigger .mdi::after {
  font-size: 28px;
}

#app .filters .yunoAutocompleteSearch .field .control .icon .yunoDropdown .timesDays ul li .mdi::after {
  display: block;
  margin-bottom: 5px;
}

#app .filters .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}

#app .filters .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 43px);
          flex: 0 0 calc(100% - 43px);
}

#app .filters .filterTrigger {
  display: block;
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
  padding-right: 15px;
}

#app .filters .filterTrigger .material-icons {
  font-size: 28px;
}

#app .filters .filterTrigger .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  font-size: 28px;
}

#app .filters .filterTrigger .yunoAutocompleteSearch .field .control .icon .mdi::after {
  font-size: 28px;
}

#app .filters.mobileView .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  display: block;
  margin-bottom: 15px;
}

#app .filters.mobileView .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .filters.mobileView .filterTrigger {
  display: none;
}

#app .searchBox {
  width: 100%;
  padding-right: 20px;
}

#app .yunoDropdown.slider .dropdown-menu {
  padding: 15px;
}

#app .yunoDropdown.slider .b-slider.is-primary .b-slider-fill {
  background: #a81e22 !important;
}

#app .yunoDropdown.slider .b-tooltip.is-primary .tooltip-content {
  background: #a81e22 !important;
}

#app .yunoDropdown.availability .dropdown-menu {
  width: 300px;
  left: auto;
  right: 0;
}

#app .field .control input[type="text"] {
  height: 44px;
}

#app .yunoDropdown.iconOnly .labelWrapper {
  border: 0;
  padding: 0 5px 0;
}

#app .yunoDropdown.iconOnly .labelWrapper .icon {
  top: calc(50% - 14px);
}

#app .filterDropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}

#app .classDetails .standard-time-est {
  padding-bottom: 7px;
  padding-top: 7px;
}

#app .classDetails .standard-time-est span {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.4000000059604645px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

#app .classDetails .skeletonWrapper .dateSkeleton .b-skeleton {
  width: 100px;
}

#app .classDetails .skeletonWrapper .cardSkeleton {
  width: 100%;
}

#app .classDetails .skeletonWrapper .cardSkeleton .b-skeleton .b-skeleton-item {
  width: 100%;
  height: 200px;
}

#app .classDetails .scheduleModal .yunoModal .modal-content {
  border: 1px solid #e6e6e6 !important;
}

#app .classDetails .scheduleModal .yunoModal .modalTitle {
  padding: 40px 20px 0 20px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

@media (max-width: 768px) {
  #app .classDetails .scheduleModal .yunoModal .modalBody {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
  }
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper {
  width: 60%;
}

@media (max-width: 768px) {
  #app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper {
    width: 100%;
  }
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
  border-bottom: 1px solid #e6e6e6;
  width: 300px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .flexDiv {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 10px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li {
  margin-left: -12px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerEnrolled {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  gap: 6px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo .academyFavIcon img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners {
  width: 40%;
}

@media (max-width: 768px) {
  #app .classDetails .scheduleModal .yunoModal .modalBody .addLearners {
    width: 100%;
  }
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners .totalLearners {
  border-bottom: 2px solid #e6e6e6;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners .mappedLearners {
  overflow-y: auto;
  height: 310px;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners .mappedLearners .myLearners .button {
  border-bottom: 1px solid #e6e6e6;
  border-top: none;
  border-left: none;
  border-right: none;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners .mappedLearners .myLearners .button.is-focused {
  border: none;
}

#app .classDetails .scheduleModal .yunoModal .modalBody .addLearners .mappedLearners .myLearners .button.is-primary {
  background-color: #A81E22 !important;
  border-bottom: none;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter {
  padding-bottom: 20px;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter .ctaWrapper {
  gap: 10px;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #a81e22;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  min-width: 80px !important;
  color: #201a19;
}

#app .classDetails .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA:hover {
  text-decoration: none;
  border-color: #a81e22;
}

#app .classWrapper {
  padding: 10px 0;
}

#app .classWrapper .classSchedule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}

#app .classWrapper .classSchedule .dayInfoBox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .classWrapper .classSchedule .dayInfoBox .day {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.4000000059604645px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #A81E22;
}

#app .card-scroller {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 8px;
  padding: 3px 3px 9px 3px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (max-width: 768px) {
  #app .card-scroller {
    overflow-x: auto;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
}

#app .card-scroller .monthBox {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 73px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #EDE0DE;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

#app .card-scroller .monthBox.selected {
  background-color: #5b1717 !important;
  color: #FFF !important;
}

#app .filterPanel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
  padding-bottom: 20px;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 9;
  background: white;
  padding-top: 10px;
}

#app .pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 -15px;
}

#app .pagination li {
  padding: 0 10px;
}

#app .pagination li.noRightGap {
  padding-right: 0;
}

#app .pagination li.page {
  padding-left: 0;
}

#app .pagination li.gapRight {
  padding-right: 15px;
}

#app .pagination li.firstLast {
  display: none;
}

@media (min-width: 768px) {
  #app .pagination li.firstLast {
    display: block;
  }
}

#app .pagination button {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 5px 15px;
  height: 36px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .pagination button.nextPrev {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .pagination button.nextPrev .material-icons {
  font-size: 24px;
}

#app .pagination button.disabled {
  cursor: not-allowed;
}

#app .pagination button.disabled.active {
  background: #A81E22;
  border-color: transparent;
  color: #ffffff;
}

#app .yunoSnackbar .snackbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding: 15px;
  background: #f2f2f2;
  max-width: 500px;
  overflow-y: auto;
}

#app .yunoSnackbar .snackbar .closeSnackbar {
  position: absolute;
  right: 10px;
  top: 10px;
}

#app .yunoSnackbar .snackbar .closeSnackbar .material-icons-outlined {
  font-size: 18px;
}

#app .yunoSnackbar .titleLarge {
  font-size: 24px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 5px;
}

#app .yunoSnackbar .titleSmall {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .subtitleSmall {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .yunoSnackbar .noticeSmall {
  margin-bottom: 10px;
}

#app .yunoSnackbar .noticeTitle {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1.5px;
}

#app .yunoSnackbar .mappedInstructor {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .yunoSnackbar .mappedInstructor.gapBtm15 {
  margin-bottom: 15px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 10px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 0;
  background-color: #fff;
}

#app .yunoSnackbar .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 60px);
          flex: 0 0 calc(100% - 60px);
}

#app .yunoSnackbar .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

#app .yunoSnackbar .ctaWrapper {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoSnackbar .ctaWrapper .button {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper {
  width: 100%;
  position: relative;
  background: #fafafa;
  padding: 10px;
}

#app .yunoSnackbar .formWrapper.noBG {
  background: none;
  padding: 0;
}

#app .yunoSnackbar .formWrapper .innerWrapper {
  background-color: #fafafa;
  padding: 10px;
  margin-bottom: 15px;
}

#app .yunoSnackbar .formWrapper .innerWrapper .groupElement {
  margin: 0;
}

#app .yunoSnackbar .formWrapper.gapTop15 {
  padding-top: 15px;
}

#app .yunoSnackbar .formWrapper .vue-star-rating {
  padding-left: 15px;
}

#app .yunoSnackbar .formWrapper .field.noGap {
  margin: 0;
}

#app .yunoSnackbar .formWrapper .alert {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoSnackbar .formWrapper .alert .material-icons-outlined {
  font-size: 40px;
  margin-bottom: 10px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper {
  -webkit-box-pack: right;
      -ms-flex-pack: right;
          justify-content: right;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading {
  position: absolute;
  right: 5px;
  bottom: 54px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading .button {
  border: 0;
}

#app .yunoSnackbar .formWrapper .ctaWrapper .button {
  margin-right: 0;
}

#app .yunoSnackbar .formWrapper .checkList .fieldLabel {
  font-size: 14px;
  margin: 0 0 10px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 600;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .field {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox {
  border-radius: 20px;
  padding: 5px 10px;
  height: auto;
  font-size: 12px;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox.is-primary {
  background-color: #A81E22;
}

#app .yunoSnackbar .starLabel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 12px;
  margin-top: 15px;
}

#app .yunoSnackbar .starLabel li {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 67px;
          flex: 0 0 67px;
  text-align: center;
}

#app .yunoSnackbar .starLabel li.active {
  visibility: visible;
}

#app .yunoSnackbar .starLabel li.notActive {
  visibility: hidden;
}

.dark87, #app .yunoSnackbar .snackbar, #app .yunoSnackbar .snackbar .closeSnackbar, #app .yunoSnackbar .mappedInstructor figcaption {
  color: rgba(0, 0, 0, 0.87);
}

.body3 {
  color: rgba(0, 0, 0, 0.87);
}

.largerTitle {
  color: rgba(0, 0, 0, 0.87);
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 10px;
}

.dark60, #app .yunoSnackbar .noticeSmall, #app .yunoSnackbar .mappedInstructor .studentCount, #app .yunoSnackbar .starLabel li {
  color: rgba(0, 0, 0, 0.6);
}

.overline {
  color: rgba(0, 0, 0, 0.6);
}

.body2 {
  color: rgba(0, 0, 0, 0.6);
}

.body1 {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

.fontColorDark {
  color: #201A19;
}

.fontColorDarkVariant {
  color: #534342;
}

.largestTitle {
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

.largeTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallerCaption {
  font-size: 16px;
  line-height: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

.smallestCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

a.dropdown-item {
  white-space: normal !important;
}

.b-tabs .tab-content {
  padding: 1rem 0 1rem 0;
}

.filterWrapper .filterHeader {
  display: block;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.filterWrapper .filter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.filterHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.filterDropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.yunoDropdown {
  min-width: 200px;
}

.filterTrigger {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  cursor: pointer;
  padding: 5px 10px;
}

.hidden {
  display: none;
}

.month-year {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.4000000059604645px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #A81E22;
}

.card-scroller::-webkit-scrollbar {
  height: 6px;
}

.card-scroller::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.card-scroller::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}

@media (min-width: 768px) {
  .largestTitle {
    font-size: 48px;
    line-height: 62px;
    font-weight: 700;
    margin-bottom: 10px;
  }
  #app .filters .yunoDropdown {
    display: block;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin: 15px 0 0;
  }
  #app .filters .yunoDropdown:first-child {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
  #app .filters .filterTrigger {
    display: none;
  }
  #app .yunoDropdown {
    width: 250px;
  }
  #app .searchBox {
    width: 250px;
  }
  #app .filterDropdown {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 15px;
  }
  .filterWrapper .filterHeader {
    display: none;
  }
}

@media (max-width: 767px) {
  #app .mainHeader {
    padding-left: 0 !important;
  }
  #app .classWrapper {
    gap: 10px;
  }
}
/*# sourceMappingURL=learnerV2.css.map */