Vue.component('yuno-user-list', {
    props: {
        data: {
            type: Object,
            required: true,
            validator: function(value) {
                return value.instructor && 
                       value.instructor.name && 
                       value.instructor.image;
            }
        },
        options: {
            type: Object,
            default: () => ({})
        }
    },
    template: `
        <div class="userList">
            <figure class="mappedInstructor">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        :src="data.instructor.image" 
                        :alt="data.instructor.name"
                        @error="handleImageError">
                </div>
                <figcaption>
                    <small class="studentCount">instructor</small>
                    <h3 class="insName">{{ data.instructor.name }}</h3>
                </figcaption>
            </figure>
            <figure class="mappedInstructor" v-if="false">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        src="https://lh3.googleusercontent.com/a-/AFdZucqaT_XL5RJsrCMlYly_jIvNtgymihOsjzVEOH2J=s96-c" 
                        alt="asd">
                </div>
                <figcaption>
                    <small class="studentCount">counselor</small>
                    <h3 class="insName">Reena Nagpal</h3>
                </figcaption>
            </figure>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        handleImageError(event) {
            // Fallback to a default image if the instructor image fails to load
            event.target.src = 'https://placehold.co/44x44';
        }
    }
});