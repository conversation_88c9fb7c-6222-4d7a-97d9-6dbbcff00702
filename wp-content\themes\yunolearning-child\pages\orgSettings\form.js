Vue.component("yuno-org-settings-form", {
  props: {
    fields: {
      type: Array,
      required: true,
    },
    payload: {
      type: Object,
      required: true,
    },
    defaultTaxonomy: {
      type: Number,
      required: true,
    },
    industryData: {
      type: Object,
      required: false,
    },
  },
  template: `
        <div class="yunoFormWrapper">
            <validation-observer 
                tag="div" 
                class="observer"
                ref="orgSettingsFormObserver" 
                v-slot="{ handleSubmit, invalid }"
            >
                <form @submit.prevent="handleSubmit(initForm)">
                    <template v-for="(field, i) in fields">
                        <template v-if="field.type === 'text'">
                            <b-field :label="field.label" :key="i">
                                <validation-provider 
                                    tag="div" 
                                    class="fieldWrapper" 
                                    :rules="{
                                        required: field.isRequired
                                    }" 
                                    v-slot="{ errors, classes }"
                                >
                                    <b-input
                                        :class="classes"
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                    >
                                    </b-input>
                                    <p class="error">{{errors[0]}}</p>
                                </validation-provider>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'textarea'">
                            <b-field :label="field.label" :key="i">
                                <b-input
                                    type="textarea"
                                    v-model="payload[field.name]"
                                    :placeholder="field.placeholder"
                                    :disabled="field.disabled"
                                    :loading="field.isLoading"
                                >
                                </b-input>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'checkbox'">
                            <b-field :key="i">
                                <b-checkbox 
                                    :value="payload[field.name]" 
                                    v-model="payload[field.name]"
                                >
                                    {{ field.label}}
                                </b-checkbox>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'dropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                        @input="dropdownChange($event)"
                                    >
                                        <option
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                            :value="option.value"
                                            :disabled="option.isDisabled"
                                        >
                                            {{ option.label }}
                                        </option>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'googleFontFamilydropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                    >
                                        <option
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                            :value="option.family"
                                        >
                                            {{ option.family }}
                                        </option>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'multiSelectDropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <validation-provider 
                                        tag="div" 
                                        class="fieldWrapper" 
                                        :rules="{
                                            required: field.isRequired
                                        }" 
                                        v-slot="{ errors, classes }"
                                    >
                                        <b-dropdown
                                            v-model="field.selected"
                                            :class="classes"
                                            multiple
                                            :disabled="field.disabled"
                                            aria-role="list"
                                            @change="multiSelectDropdownChange($event, field)"
                                        >
                                            <template #trigger>
                                                <b-button>
                                                    <template v-if="field.selected.length">
                                                        <div class="selected">
                                                            <template v-for="(subOption, k) in field.selected">
                                                                {{ subOption.name }}{{ k < field.selected.length - 1 ? ', ' : '' }}
                                                            </template>    
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="placeholder">{{ field.placeholder }}</div>
                                                    </template>
                                                    <div class="material-icons">expand_more</div>    
                                                </b-button>
                                            </template>
                                            <b-dropdown-item 
                                                :value="subOption"
                                                aria-role="listitem"
                                                v-for="(subOption, k) in field.options"
                                            >
                                                <span>{{ subOption.name }}</span>
                                            </b-dropdown-item>
                                        </b-dropdown>
                                        <p class="error">{{errors[0]}}</p>
                                    </validation-provider>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'groupDropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="industry"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                        @input="groupDropdownChange($event, field)"
                                    >
                                        <optgroup 
                                            :label="option.label"
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                        >
                                            <option
                                                v-for="(subOption, k) in option.sub_industry"
                                                :key="k"
                                                :value="subOption"
                                            >
                                                {{ subOption.label }}
                                            </option>
                                        </optgroup>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'upload'">
                            <b-field :label="field.label" :key="i" class="uploadField">
                                <p class="helper">{{ field.placeholder }}</p>
                                <b-upload
                                    v-model="payload[field.name]"
                                    accept="image/*"
                                    class="file-label"
                                >
                                    <span class="file-cta">
                                        <span class="material-icons-outlined">file_upload</span>
                                        <span class="file-label">{{ field.cta }}</span>
                                    </span>
                                    <span class="file-name" v-if="payload[field.name]">
                                        {{ payload[field.name].name }}
                                    </span>
                                </b-upload>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'colorpicker'">
                            <b-field :label="field.label" :key="i">
                                <b-colorpicker
                                    :color-formatter="(color) => color.toString('hex')"
                                    representation="square"
                                    @input="updateColor($event, field.name)"
                                    :value="payload[field.name]"
                                    :disabled="field.disabled"
                                    :loading="field.isLoading"
                                >
                                </b-colorpicker>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'addInstructors' && field.visible">
							<div :class="field.class" @click="openModal">
								<span>{{ field.label }}</span>
							</div>
						</template>
                    </template>
                    <div class="ctaWrapper">
                        <b-button
                            native-type="reset"
                            @click="clearForm"
                            class="yunoPrimaryCTA wired fat">
                            Cancel
                        </b-button>    
                        <b-button
                            native-type="submit"
                            :loading="form.isLoading"
                            :disabled="form.isLoading"
                            class="yunoSecondaryCTA fat">
                            Save
                        </b-button>    
                    </div>
                </form>
            </validation-observer>
        </div>
    `,
  data() {
    return {
      industry: null,
      defaultPayload: [],
    };
  },
  computed: {
    ...Vuex.mapState(["user", "categoryTaxonomy", "form"]),
  },
  watch: {
    industryData: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.industry = newValue;
        }
      },
      deep: true,
    },
  },
  async created() {},
  destroyed() {},
  mounted() {
    this.init();
  },
  methods: {
    /**
     * Updates the color value in the form payload.
     * @param {Color} color - The color value to be updated.
     * @param {string} field - The field in the form payload to update.
     */
    updateColor(color, field) {
      this.form.payload[field] = color.toString("hex");
    },
    /**
     * Initializes the form.
     */
    init() {
      this.defaultPayload = JSON.parse(JSON.stringify(this.form.payload));
    },
    /**
     * Clears the form by resetting the form observer and setting the payload to the default payload.
     */
    clearForm() {
      this.$refs.orgSettingsFormObserver.reset();
      this.form.payload = JSON.parse(JSON.stringify(this.defaultPayload));
    },
    /**
     * Converts a JSON object to FormData.
     *
     * @param {Object} json - The JSON object to convert.
     * @returns {FormData} The converted FormData object.
     */
    jsonToFormData(json) {
      const formData = new FormData();
      for (let key in json) {
        if (
          Array.isArray(json[key]) &&
          json[key].every((item) => typeof item === "object")
        ) {
          formData.append(key, JSON.stringify(json[key]));
        } else {
          formData.append(key, json[key]);
        }
      }
      return formData;
    },
    /**
     * Initializes the form and emits the 'submitForm' event with the form data.
     * @memberof ClassName
     * @function initForm
     */
    initForm() {
      this.$emit("submitForm", this.jsonToFormData(this.form.payload));
    },
    dropdownChange(data) {},
    /**
     * Handles the change event of the group dropdown.
     * Updates the form payload based on the selected field.
     *
     * @param {Object} parent - The parent value of the selected option.
     * @param {string} slug - The slug value of the selected option.
     * @param {Object} field - The field object representing the selected option.
     */
    groupDropdownChange({ parent, slug }, field) {
      if (field.name === "sub_industry") {
        this.form.payload.industry = parent;
        this.form.payload.sub_industry = slug;
      }
    },
    multiSelectDropdownChange(selected, field) {
      this.form.payload[field.name] = selected;
    },
    openModal() {
      this.$emit("openModal");
    },
  },
});
