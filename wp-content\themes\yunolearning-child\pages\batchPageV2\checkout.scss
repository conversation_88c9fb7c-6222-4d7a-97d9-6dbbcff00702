@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
@import "../../assets/scss/icons";

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

#app {
    .checkout {
        margin-bottom: $gapLargest;

        @media (min-width: 768px) {
            margin-bottom: $gapLargest * 2;
        }
    }

    .alignR {
        text-align: right;
    }
    .mainHeader {
        margin-top: $gapLargest;
        margin-bottom: $gapLargest;

        .block {
            display: flex;
            flex-direction: column;

            @media (min-width: 768px) {
                padding: 0;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }
    }

    .pageTitle {
        @include setFont($headline3, 40px, 500, 0);
        @extend .dark87;
    }

    .batch {
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding-bottom: $gap15;
        @extend .dark87;

        .spaceBetween {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .price {
            @include setFont($headline4, 30px, 400, $gap15);    
        }

        .batchInfo {
            li {
                @include setFont($body2, 20px, 400, $gapSmall);    
                @extend .dark60;

                &.grid {
                    display: flex;
                }

                .classDays {
                    display: flex;
                    flex-wrap: wrap;
                    flex: 0 0 calc(100% - 95px);
                    margin-left: $gap15;
            
                    li {
                        text-transform: uppercase;
                        font-size: $overline;
                        margin-right: $gapSmaller;
                        @include setBGColor($primary, 0.04);
                        border: 1px solid $primary;
                        padding: 3px $gapSmaller + 3;
                        border-radius: 100px;
                        flex: 0 0 35px;
                        text-align: center;
                        color: $primary;
                        margin-top: $gapSmaller;
            
                        @media (min-width: 1214px) {
                            margin-top: 0;    
                        }
            
                        &:last-child {
                            margin-right: 0;
                        }
            
                        &.disabled {
                            @include setBGColor($primaryCopyColor, 0.02);
                            @include setFontColor($primaryCopyColor, 0.38);
                            border-color: transparent;
                        }
                    }
                }
            }
            
        }
    }

    .mappedInstructor {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-top: $gapLargest;

        .imgWrapper {
            flex: 0 0 64px;
            margin-right: $gapSmall;

            img {
                width: 64px;
                height: 64px;
                border-radius: 50%;
                font-size: 0;
            }
        }

        figcaption {
            flex: 0 0 calc(100% - 79px);
        }

        .insName {
            @include setFont($subtitle1, 20px, 500, 0);
        }

        .insRating {
            display: flex;
            align-items: center;
            position: relative;
            left: -3px;

            .material-icons {
                color: #F9B600;
                margin-right: $gapSmaller;
                font-size: 18px;
            }

            .caption {
                @include setFont($caption2, normal, 500, 0);
                @extend .dark60;
            }
        }

        .studentCount {
            flex: 0 0 100%;
            @include setFont($overline, 16px, 400, 0);
            letter-spacing: 1.5px;
            margin-top: $gapSmall;
            @extend .dark60;
            text-transform: uppercase;
        }
    }

    .course {
        @extend .dark87;
        background: #FFFFFF;
        box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: $gapLargest $gapLargest * 2;
        margin-top: $gapLargest;

        @media (min-width: 768px) {
            margin-top: 0;
        }

        .name {
            @include setFont($headline5, 28px, 400, $gapSmall);
        }

        .description {
            @extend .dark60;
            @include setFont($body2, 20px, 400, $gap15);
        }

        .subtitle {
            @extend .dark87;
            @include setFont($subtitle1, 24px, 500, $gap15);
        }

        .orderDetail {
            li {
                @include setFont($body2, 20px, 400, $gapSmall);
                display: flex;
                justify-content: space-between;

                &.primary {
                    border-top: 1px solid rgba(0, 0, 0, 0.08);
                    margin-top: $gap15;
                    padding-top: $gap15;
                    @include setFont($subtitle1, 24px, 500, $gapLargest);
                }

                .withGrid {
                    display: flex;

                    a {
                        margin-left: $gapSmaller;
                        
                        .material-icons-outlined {
                            font-size: 18px;
                        }
                    }
                }
            }
        }

        .yunoSecondaryCTA {
            width: 100%;
            margin-bottom: $gapLargest;
        }

        .caption {
            @include setFont($subtitle2, 24px, 500, $gapSmaller);
        }

        .note {
            @include setFont($caption1, 16px, 400, 0);
        }

        .formWrapper {
            margin-bottom: $gapLargest;

            #yunoForm {
                display: flex;
                align-items: flex-end;
            }

            .field {
                flex: 0 0 calc(40% - 15px);
                margin-right: $gap15;
                margin-bottom: 0;

                .label {
                    .b-tooltip {
                        top: 6px;
                    }
                }
            }

            .button {
                height: 36px;
                flex: 0 0 30%;
            }

            .msg {
                flex: 0 0 60%;
                @include setFont($caption1, 16px, 400, 6px);
                display: flex;
                align-items: center;

                &.success {
                    color: green;
                }

                &.error {
                    color: red;
                }

                .material-icons {
                    margin-right: $gapSmaller;
                }
            }
        }
    }

    .yunoModal {
        &.hasMinHeight {
            .modalBody {
                min-height: 500px;
            }
        }
        .successMsg {
            h3 {
                font-size: $headline6;
                margin-bottom: $gap15;
            }

            .subTitle {
                margin-bottom: $gap15;
            }

            .alertGrid {
                border: 1px solid rgba(0,0,0,0.08);
                border-radius: 4px;
                padding: $gap15;
                margin-bottom: $gap15;
                font-size: $subtitle2;

                .alertGridLabel {
                    @extend .dark60;
                }

                .alertGridVal {
                    margin-bottom: $gapSmall;
                }
            }

            p {
                font-size: $caption1;
            }
        }

        .subscriptionList {
            margin-bottom: $gapSmall;
            background: #FCFCFC;
            border: 1px solid #eee;
            border-radius: 4px;
            
            &:last-child {
                margin-bottom: 0;	
            }	
        }

        .hasScroll {
            overflow-y: auto;
        }

        .modalBody {
            &.noOverFlow {
                overflow: visible;
            }
        }
    }

    .batchesWrapper {
        // @media (min-width: 768px) {
        //     position: sticky;
        //     top: 179px;
        // }
    }

    .batchesCount {
        @include setFont($caption2, normal, 500, $gap15);
        @extend .dark60;
    }

    .batches {
        display: flex;
        flex-wrap: wrap;
        margin: 0 (-$gap15);

        .batchCard {
            flex: 0 0 100%;
            padding: 0 $gap15;
            margin-bottom: $gapLargest;
            @extend .dark87;

            @media (min-width: 768px) {
                flex: 0 0 33.3%;    
            }

            .wrapper {
                background: #FFFFFF;
                box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                border-radius: 4px;
                padding: $gap15;
                border: 1px solid transparent;

                &:hover {
                    border: 1px solid rgba(0, 0, 0, 0.12);

                    .ctaWrapper, .full {
                        visibility: visible
                    }
                }
            }

            .batchdate {
                @include setFont($subtitle1, 24px, 500, $gapSmall);
                display: flex;
                align-items: center;

                .material-icons {
                    margin-right: $gapSmaller;
                }
            }

            .days {
                display: flex;
                justify-content: space-between;
                margin-bottom: $gapSmall;

                li {
                    @include setFont($overline, 16px, 500, 0);
                    letter-spacing: 1.5px;
                    @include setFontColor($primaryCopyColor, 0.38);
                    text-transform: uppercase;

                    &.isActive {
                        color: $primary;
                    }
                }
            }

            .scheduleInfo {
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                margin-bottom: $gapSmall;
                padding-bottom: $gapSmall;

                li {
                    display: flex;
                    @extend .dark60;
                    align-items: center;
                    margin-top: $gapSmaller;

                    .material-icons-outlined {
                        font-size: 16px;
                        
                    }

                    .itemCaption {
                        @include setFont($caption2, 16px, 500, 0);
                        margin-left: $gapSmaller;
                    }

                    .hasBG {
                        @include setFont($caption2, 16px, 500, 0);
                        background-color: rgba(168, 30, 34, 0.04);
                        border-radius: 100px;
                        display: flex;
                        align-items: center;
                        padding: 4px 10px;

                        .material-icons-outlined {
                            margin-right: $gapSmaller;
                        }
                    }
                }
            }

            .mappedInstructor {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                margin-bottom: $gapSmall;
                padding-bottom: $gapSmall;

                .imgWrapper {
                    flex: 0 0 64px;
                    margin-right: $gapSmall;

                    img {
                        width: 64px;
                        height: 64px;
                        border-radius: 50%;
                        font-size: 0;
                    }
                }

                figcaption {
                    flex: 0 0 calc(100% - 79px);
                }

                .insName {
                    @include setFont($subtitle1, 20px, 500, 0);

                }

                .studentCount {
                    flex: 0 0 100%;
                    @include setFont($caption1, 16px, 400, 0);
                    margin-top: $gapSmall;
                    @extend .dark60;
                }
            }

            .cardFooter {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                align-items: center;

                .price {
                    @include setFont($subtitle1, 20px, 500, 0);
                }

                .ctaWrapper {
                    @media (min-width: 768px) {
                        visibility: hidden;
                    }
                }

                .full {
                    flex: 0 0 100%;
                    margin-top: $gapSmaller;
                    @include setFont($caption1, 16px, 400, 0);
                    @extend .dark60;
                    visibility: hidden;
                }
            }
        }
    }

    .filtersWrapper {
        padding: 0;
        margin: 0 0 $gap15;

        @media (min-width: 768px) {
            padding: 0;
        }

        > ul {
            display: flex;
            flex-wrap: wrap;
            margin: 0 (-$gap15);

            li {
                padding: 0 $gap15;

                @media (min-width: 768px) {
                    padding: 0 0 0 $gap15;
                }

                &.course_search, &.search_instructor {
                    flex: 0 0 100%;
                    margin-bottom: $gapSmall;

                    @media (min-width: 768px) {
                        flex: 0 0 400px;
                        margin-bottom: 0;
                    }
                }
                
                &.isFocus {
                    &.search {
                        .field .control input[type="text"] {
                            @include setBorderColor($primaryCopyColor, 1);
                        }
                    }
                }

                &.search {
                    .field .control input[type="text"] {
                        height: 40px;
                        @include setBorderColor($primaryCopyColor, 0.12);
                    }

                    .dropdown-menu {
                        .dropdown-item {
                            .subtitle {
                                @include setFont($overline, auto, 500, 0);
                            }
                        }
                        
                    }
                }

                &.singleCheck {
                    display: flex;
                    align-items: center;

                    @media (min-width: 1280px) {
                        margin-top: $gap15;
                    }

                    @media (min-width: 1800px) {
                        margin-top: 0;
                    }

                    .b-checkbox {
                        margin-bottom: 0;

                        &.is-disabled {
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }

        

        &.loading {
            .b-skeleton {
                width: auto;
                margin: 0 0 0 $gap15;
            }
        }

        .fa-filter {
            font-size: $fontSizeLarger + 6;
            padding-right: $gap15;
        }

        .button {
            &.is-primary {
                background-color: $whiteBG;
                color: $primaryCopyColor;
                font-size: $body2;
                @extend .dark87;
                @include setBorderColor($primaryCopyColor, 0.12);
                font-size: $fontSizeSmall;
                border-radius: 4px;
                padding: 8px $gap15 9px;

                &:active, &:focus {
                    box-shadow: none;
                }
            }

            .icon {
                margin: 0 0 0 $gapSmaller;
                position: relative;
                top: 1px;

                .mdi {
                    &.mdi-menu-down, &.mdi-menu-up {
                        &:after {
                            content: "\e5c5";
                            @extend .material-icons;
                        }
                    }

                    &.mdi-menu-up {
                        &:after {
                            content: "\e5c7";
                        }
                    }
                }
            }
            
        }

        .filterMenu {
            flex: 0 0 100%;
            margin-bottom: $gap15;
            margin-left: 0;

            @media (min-width: 768px) {
                flex: 0 0 auto;
                margin-bottom: 0;
                margin: 0; 
            }

            &.active {
                button.filter {
                    @include setBorderColor($primary, 1);
                    @include setBGColor($primary, 0.04);
                    color: $primary;

                    .icon {
                        color: $primary;
                    }
                }
            }

            &.is-active {
                button {
                    @include setBorderColor($primaryCopyColor, 0.87);
                }
            }

            &.multiSelect:not(.class_days_time) {
                .dropdown-menu {
                    padding: 0;
                }

                .dropdown-content {
                    padding: 0;

                    
                    a:not(.dropdownTitle) {
                        padding: 6px 15px 6px 40px;
                        position: relative;

                        &:before {
                            content: "unCheck";
                            @extend .ylIcon;
                            position: absolute;
                            left: 15px;
                            top: 10px;
                        }

                        &.is-active {
                            &:before {
                                content: "checked";
                            }
                        }
                    }
                }

                .ctaWrapper {
                    display: flex;
                    justify-content: space-between;
                    margin: $gapSmall $gap15 0;
                    padding: $gap15 0;
                    border-top: 1px solid;
                    @include setBorderColor($primaryCopyColor, 0.12);

                    .yunoSecondaryCTA {
                        border-color: $primary;
                    }
                }

                &.class_time {
                    .dropdown-content {
                        display: flex;
                        padding: $gapSmall $gap15 $gapSmall $gapSmall;
                        flex-wrap: wrap;

                        @media (min-width: 768px) {
                            width: 440px;
                        }

                        .dropdown-item {
                            flex: 0 0 25%;
                            display: flex;
                            flex-direction: column;
                            padding: $gapSmall $gap15;
                            align-items: center;
                            @extend .dark87;
                            border: 1px solid transparent;

                            &.is-active {
                                @include setBorderColor($primary, 1);
                                @include setBGColor($primary, 0.04);    
                            }

                            &:before {
                                font-family: 'Material Icons' !important;
                                @extend .material-icons;
                                position: static;
                            }

                            &.morning {
                                &:before {
                                    content: "\e1c6";
                                }
                            }

                            &.afternoon {
                                &:before {
                                    content: "\e518";
                                }
                            }

                            &.evening {
                                &:before {
                                    content: "\e1c6";
                                }
                            }

                            &.night {
                                &:before {
                                    content: "\e51c";
                                }
                            }

                            .itemCaption {
                                padding: $gapSmaller 0;
                                font-size: $subtitle1;
                                font-weight: 500;
                            }

                            .itemLabel {
                                @include setFont($overline, normal, 400, 0);
                                letter-spacing: 1.5px;
                                @extend .dark60;
                                text-transform: uppercase;
                            }

                        }
                    }

                    .ctaWrapper {
                        flex: 0 0 100%;
                        margin: 0;
                        padding-bottom: $gapSmaller;
                    }
                }

                &.class_days {
                    .dropdown-content {
                        display: flex;
                        padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                        flex-wrap: wrap;

                        @media (min-width: 768px) {
                            width: 440px;
                        }

                        .dropdown-item {
                            flex: 0 0 59px;
                            padding: $gapSmall $gap15 0 0;
                            box-sizing: border-box;

                            &:hover {
                                background: none;
                            }

                            &:before {
                                display: none;
                            }

                            .itemLabel {
                                display: block;
                                background: rgba(0, 0, 0, 0.02);
                                border-radius: 100px;
                                @include setFont($overline, 16px, 500, 0);
                                text-transform: uppercase;
                                text-align: center;
                                border: 1px solid transparent;
                                padding: 6px $gapSmaller;

                                &:hover {
                                    @include setBorderColor($primaryCopyColor, 0.60);
                                }
                            }

                            &.is-active {
                                .itemLabel {
                                    @include setBorderColor($primary, 1);
                                    @include setBGColor($primary, 0.04);    
                                    color: $primary;
                                }
                            }
                        }
                    }

                    .ctaWrapper {
                        flex: 0 0 100%;
                        margin: $gap15 0 0;
                        padding-bottom: $gapSmaller;
                    }
                }
            }

            &.class_days_time {
                .ctaWrapper {
                    display: flex;
                    justify-content: space-between;
                    margin: $gapSmall $gapSmall 0;
                    padding-top: $gapSmall;
                    border-top: 1px solid;
                    @include setBorderColor($primaryCopyColor, 0.12);
                    
                    .button {
                        border-color: $primary;
                    }
                }
            }

            .filterSet {
                .listCaption {
                    @include setFont($subtitle2, 24px, 500, 0);
                    @extend .dark87;
                    padding: 0 $gapSmall;
                }

                &.class_days {
                    .innerWrapper {
                        display: flex;
                        padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                        flex-wrap: wrap;

                        @media (min-width: 768px) {
                            width: 440px;
                        }
                    }

                    .dropdown-item {
                        flex: 0 0 59px;
                        padding: $gapSmall $gap15 0 0;
                        box-sizing: border-box;

                        &:hover {
                            background: none;
                        }

                        &:before {
                            display: none;
                        }

                        .itemLabel {
                            display: block;
                            background: rgba(0, 0, 0, 0.02);
                            border-radius: 100px;
                            @include setFont($overline, 16px, 500, 0);
                            text-transform: uppercase;
                            text-align: center;
                            border: 1px solid transparent;
                            padding: 6px $gapSmaller;

                            &:hover {
                                @include setBorderColor($primaryCopyColor, 0.60);
                            }
                        }

                        &.is-active {
                            .itemLabel {
                                @include setBorderColor($primary, 1);
                                @include setBGColor($primary, 0.04);    
                                color: $primary;
                            }
                        }
                    }
                }

                &.class_time {
                    .innerWrapper {
                        display: flex;
                        padding: $gapSmall $gap15 $gapSmall $gapSmall;
                        flex-wrap: wrap;

                        @media (min-width: 768px) {
                            width: 440px;
                        }

                        .dropdown-item {
                            flex: 0 0 25%;
                            display: flex;
                            flex-direction: column;
                            padding: $gapSmall $gap15;
                            align-items: center;
                            @extend .dark87;
                            border: 1px solid transparent;

                            &.is-active {
                                @include setBorderColor($primary, 1);
                                @include setBGColor($primary, 0.04);    
                            }

                            &:before {
                                font-family: 'Material Icons' !important;
                                @extend .material-icons;
                                position: static;
                            }

                            &.morning {
                                &:before {
                                    content: "\e1c6";
                                }
                            }

                            &.afternoon {
                                &:before {
                                    content: "\e518";
                                }
                            }

                            &.evening {
                                &:before {
                                    content: "\e1c6";
                                }
                            }

                            &.night {
                                &:before {
                                    content: "\e51c";
                                }
                            }

                            .itemCaption {
                                padding: $gapSmaller 0;
                                font-size: $subtitle1;
                                font-weight: 500;
                            }

                            .itemLabel {
                                @include setFont($overline, normal, 400, 0);
                                letter-spacing: 1.5px;
                                @extend .dark60;
                                text-transform: uppercase;
                            }

                        }
                    }

                    .ctaWrapper {
                        border: 0;
                        margin-bottom: 0;
                        padding-bottom: 0;
                    }
                }
            }

            .ctaWrapper {
                display: flex;
                justify-content: space-between;
                margin: $gapSmall $gapSmall 0;
                padding-top: $gapSmall;
                border-top: 1px solid;
                @include setBorderColor($primaryCopyColor, 0.12);
                
                .button {
                    border-color: $primary;
                }
            }

            .dropdown-trigger {
                width: 100%;
            }

            

            button {
                width: 100%;
                justify-content: space-between;

                @media (min-width: 768px) {
                    width: auto;
                    justify-content: center;
                }

                > span {
                    text-transform: capitalize;
                }
            }
            .dropdown-content {
                box-shadow: none;
                border:0;
                max-height: 300px;
                overflow: hidden;
                overflow-y: auto;

                a {
                    @include setFontColor($primaryCopyColor, 0.5);

                    &.is-active {
                        background: none;
                        @include setFontColor($primaryCopyColor, 1);
                    }

                    &:active, &:focus {
                        background: none;
                        outline: none;
                    }
                }
            }

            
        }

        
    }
}