<?php
/**
 * Courses Controller
 */
include_once('ProductController.php');
class CoursesController extends WP_REST_Controller
{
    
    function __construct()
    {
        $this->namespace = 'yuno/v1';
        $this->resource_name = '/coursesinsights/(?P<role_id>\d+)/(?P<course_id>[a-zA-Z0-9-_-]+)/(?P<category_id>[a-zA-Z0-9-_-]+)/(?P<instructor_format>[a-zA-Z0-9-_-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name1 = '/course/(?P<course_id>\d+)';
        $this->create_course = '/course/create';
        $this->resource_name2 = '/course/edit/(?P<course_id>\d+)';
        $this->resource_name3 = '/instructorsvideotestimonials/(?P<resource_type>[a-zA-Z0-9-]+)/(?P<role>[a-zA-Z0-9-]+)';
        $this->resource_name4 = '/course/suggestions/(?P<text>[^/]+)';
        $this->resource_name5 = '/course/instructors/invited/(?P<course_id>\d+)/(?P<user_id>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name6 = '/course/instructors/map';
        $this->resource_name7 = '/course/user/assignment/(?P<course_id>\d+)/(?P<user_id>\d+)';
        $this->bulk_map_courses = '/course/instructor/map/bulk';
    }

    public function register_routes() {
        register_rest_route( $this->namespace, $this->resource_name, array(
          array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_list_courses' ),
            'permission_callback' => array( $this, 'check_access_permissions_check' ),
            'args'                => array(
     
            ),
          ),
        ) );

        register_rest_route( $this->namespace, $this->resource_name1, array(
          array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_course_details' ),
            'permission_callback' => array( $this, 'get_course_details_permissions_check' ),
            'args'                => array(
     
            ),
          ),
        ) );

        register_rest_route( $this->namespace, $this->create_course, array(
          array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'create_or_update_course_details' ),
            'permission_callback' => array( $this, 'update_course_details_permissions_check' ),
            'args'                => array(
     
            ),
          ),
        ) );

        register_rest_route( $this->namespace, $this->resource_name2, array(
            array(
              'methods'             => WP_REST_Server::EDITABLE,
              'callback'            => array( $this, 'create_or_update_course_details' ),
              'permission_callback' => array( $this, 'update_course_details_permissions_check' ),
              'args'                => array(
       
              ),
            ),
          ) );

        register_rest_route( $this->namespace, $this->resource_name3, array(
          array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_testimonials_instructors' ),
            'permission_callback' => array( $this, 'get_testimonials_instructors_permissions_check' ),
            'args'                => array(
     
            ),
          ),
        ) );

        register_rest_route( $this->namespace, $this->resource_name4, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_course_suggestions' ),
                'permission_callback' => array( $this, 'get_course_details_permissions_check' ),
                'args'                => array(
        
                ),
            ),
            ) );

            register_rest_route( $this->namespace, $this->resource_name5, array(
                array(
                    'methods'             => WP_REST_Server::READABLE,
                    'callback'            => array( $this, 'get_invited_instructors' ),
                    'permission_callback' => array( $this, 'check_access_permissions_check' ),
                    'args'                => array(
                  ),
                ),
              ) );
              register_rest_route( $this->namespace, $this->resource_name6, array(
                array(
                    'methods'             => WP_REST_Server::EDITABLE,
                    'callback'            => array( $this, 'assign_instructor_and_notify_admin' ),
                    'permission_callback' => array( $this, 'check_access_permissions_check' ),
                    'args'                => array(
                  ),
                ),
              ) );
              register_rest_route( $this->namespace, $this->resource_name7, array(
                array(
                    'methods'             => WP_REST_Server::READABLE,
                    'callback'            => array( $this, 'check_instructor_course' ),
                    'permission_callback' => array( $this, 'check_access_permissions_check' ),
                    'args'                => array(
                  ),
                ),
              ) );

              register_rest_route( $this->namespace, $this->bulk_map_courses, array(
                array(
                    'methods'             => WP_REST_Server::EDITABLE,
                    'callback'            => array( $this, 'bulk_map_courses_instructor' ),
                    'permission_callback' => array( $this, 'check_access_permissions_check' ),
                    'args'                => array(
                  ),
                ),
              ) );
    }
    /**
     * Token authorization check
     * This function common for all post login apis  
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) { return false; }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) { return true; } else { return false; }

        }
        $codes=error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true){
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData); 
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData); 
        }
    }
    /**
     * Get list of Courses
     */
    public function get_list_courses($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $date = date_default_timezone_set('Asia/Kolkata');
        $currentNewDateTime = date('Y-m-d H:i:s');
        $course_id = $request['course_id'];
        $category_id = $request['category_id'];
        $instructor_format = $request['instructor_format'];
        $role_id = (int)$request['role_id'];
        $limit = $request['limit'];
        $offset = $request['offset'];
        $userdata = get_userdata($role_id);
        $role = $userdata->roles[0];

        if ($role_id > 0) {} else {
        return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));    
        }

        if (empty($userdata)) {
        return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));                 
        }
        if($course_id == "all" || $category_id == "all" || $instructor_format == "all") {
            $curlPost = [
                "query" => [
                    "match_all" => [
                        "boost" => 1.0
                    ]
                ]
            ];
        } else {
            $curlPost = ["query"=> [
               "bool"=> [
                   "must"=> [
                       ["nested"=> [
                           "path"=> "data.details",
                           "query"=> [
                               "bool"=> [
                                   "must"=> [["match"=> ["data.details.record_id"=> $course_id]]]
                               ]  
                           ]
                       ]],
                       ["nested"=> [
                           "path"=> "data.details",
                           "query"=> [
                               "bool"=> [
                                   "must"=> [["match"=> ["data.details.taxonomy"=> $category_id]]]
                               ]  
                           ]
                       ]],
                       ["nested"=> [
                           "path"=> "data.details.batch_details",
                           "query"=> [
                               "bool"=> [
                                   "must"=> [["match"=> ["data.details.batch_details.personalisation"=> $instructor_format]]]
                               ]  
                           ]
                       ]]
                   ]
               ]
            ]];
        }
        $curl = curl_init();         
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/course/_search?size=".ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
            "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache",
            "content-type: application/json"
            ),
        )); 
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $res=json_decode($response);
        //return "xxgggggggggggxx ".json_decode($response);
        //return new WP_REST_Response($res, 200); 
        $collections = [];
        foreach ($res->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $course_title = $result->_source->data->details->title;
            $course_url = $result->_source->data->details->url;
            $course_excerpt = $result->_source->data->details->excerpt;
            $course_description = $result->_source->data->details->description;
            $course_price = $result->_source->data->details->unit_price;
            $live_classes = $result->_source->data->details->live_classes;
            $instructor_hours = $result->_source->data->details->instructor_hours;
            $event_date = $result->_source->data->details->event_date;
            //Mapped instructors
            //Active batches
            //Active enrollments
            //Personalization: 1-to-1, Group
            $collections[]=[
                "course_id"=>$record_id,
                "course_title"=>$course_title,
                "course_url"=> $course_url,
                "course_excerpt"=>$course_excerpt,
                "description"=>$course_description,
                "price"=>$course_price,
                "live_classes"=>$live_classes,
                "instructor_hours"=> $instructor_hours,
                "time"=>strtotime($event_date)
            ]; 
        }
        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"],'Courses Not Found' ,array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]","Course insights",$codes["GET_SUCCESS"]["message"]),'status' => $codes["GET_SUCCESS"]["status"],  'data' => $collections); 
            return new WP_REST_Response($result, 200); 
        }
    }
    public function get_course_details($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $date = date_default_timezone_set('Asia/Kolkata');
        $currentNewDateTime = date('Y-m-d H:i:s');
        $course_id = (int)$request['course_id'];
        $curlPost = [
            "query" =>[
                "nested" =>[
                    "path"  => "data.details",
                    "query"=> [
                        "bool"=> [
                            "must"=> [
                                "match" => [
                                        "data.details.record_id"=> $course_id
                                    ]
                                ]
                            ]                    
                        ]
                    ]
                ] 
            ];
        $curl = curl_init();         
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/course/_search?size=".ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
            "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache",
            "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $res=json_decode($response);
        $collections = [];
        foreach ($res->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $course_title = $result->_source->data->details->title;
            $course_url = $result->_source->data->details->url;
            $course_excerpt = $result->_source->data->details->excerpt;
            $course_description = $result->_source->data->details->description;
            $course_price = $result->_source->data->details->unit_price;
            $live_classes = $result->_source->data->details->live_classes;
            $instructor_hours = $result->_source->data->details->instructor_hours;
            $event_date = $result->_source->data->details->event_date;
            $collections[]=[
                "course_id"=>$record_id,
                "course_title"=>$course_title,
                "course_url"=> $course_url,
                "course_excerpt"=>$course_excerpt,
                "description"=>$course_description,
                "price"=>$course_price,
                "live_classes"=>$live_classes,
                "instructor_hours"=> $instructor_hours,
                "time"=>strtotime($event_date)
            ]; 
        }
        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"],'Course Details Not Found' ,array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]","Course insights",$codes["GET_SUCCESS"]["message"]),'status' => $codes["GET_SUCCESS"]["status"],  'data' => $collections); 
            return new WP_REST_Response($result, 200); 
        }
        
    }
    public function get_course_details_permissions_check(){
        return true;
    }
    // public function update_course_details($request){
    //     global $wpdb;
    //     $codes = error_code_setting();
    //     $date = date_default_timezone_set('Asia/Kolkata');
    //     $currentNewDateTime = date('Y-m-d H:i:s');
    //     $course_id = (int)$request['course_id'];
    //     $course_title = $request['course_title'];
    //     $course_excerpt = $request['course_excerpt'];
    //     $course_description = $request['description'];
    //     $price = (int)$request['price'];
    //     $live_classes = (int)$request['live_classes'];
    //     $instructor_hours = (int)$request['instructor_hours'];

    //     if(empty($course_id) || empty($course_title) || empty($course_excerpt)) {
    //         return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course ID/Title/Excerpt should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    //     }

    //     if (strlen($course_excerpt) > 145) {
    //       return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Excerpt length upto 145 character', array('status' => $codes["POST_INSERT_FAIL"]["status"]));          
    //     }

    //     $post = array();
    //     $post['ID']   = $course_id;
    //     $post['post_status']   = 'publish';
    //     $post['post_type']     = 'course';
    //     $post['post_title']    = $course_title;
    //     $post['post_content']  = $course_description;
    //     $post['post_excerpt']  = $course_excerpt;
    //     //$post['post_author']   = 2;
    //     try {
    //         // Update Post
    //         wp_update_post( $post );
    //         update_post_meta($course_id, 'live_classes', $live_classes);
    //         update_post_meta($course_id, 'Instructor_Hours', $instructor_hours);
    //         update_post_meta($course_id, 'Unit_Price', $price);
    //     } catch (Exception $e) {
    //         error_log("Course Post is not updated");
    //         //push_custom_errors_on_sentry("Course Post is not updated - update_course_details");
    //     }

    //     $curlPost = [
    //         "data" =>[
    //             "details" => [
    //                 "record_id" => $course_id,
    //                 "update_event_type" => "course",
    //                 "resource_type" => "course",  
    //                 "title" => $course_title,
    //                 "excerpt"=> $course_excerpt,                  
    //                 "description"=> $course_description,
    //                 "unit_price"=> $price,
    //                 "live_classes"=> $live_classes,
    //                 "instructor_hours"=> $instructor_hours,
    //                 "published_at"=>get_the_time( 'M j, Y g:i A', $course_id)
    //             ],
    //             "@timestamp" => date("Y-m-d H:i:s")
    //         ]
    //     ];

    //     try {
    //         update_elastic_event($curlPost);
    //     } catch (Exception $e) {
    //         error_log("Elastic Search Post Entry is not updated");
    //         //push_custom_errors_on_sentry("Elastic Search Post Entry is not updated - update_course_details"); 
    //     }
    //     $message = str_replace("[Module_Name]","Course Details",$codes["PUT_UPDATE"]["message"]);
    //     $code = $codes["PUT_UPDATE"]["code"];
    //     if($course_id > 0){
    //         $result = array(
    //             'code' => $code, 
    //             'message' => $message, 
    //             'data' => array('status' => $code, 'resource_id' => $course_id)
    //         );
    //         return new WP_REST_Response($result, 200);    
    //     } else {
    //         return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"])); 
    //     }
    // }

    // public function create_or_update_course_details($request) {
    //     global $wpdb;
    //     $codes = error_code_setting();
    //     date_default_timezone_set('Asia/Kolkata');
    //     $currentNewDateTime = date('Y-m-d H:i:s');
    
    //     // Extract data from request
    //     $course_id = isset($request['course_id']) ? (int)$request['course_id'] : 0; // Check if course_id is provided
    //     $course_title = $request['course_title'];
    //     $course_excerpt = $request['course_excerpt'];
    //     $course_description = $request['description'];
    //     $price = (int)$request['price'];
    //     $live_classes = (int)$request['live_classes'];
    //     $instructor_hours = (int)$request['instructor_hours'];
    
    //     // Validate required fields
    //     if (empty($course_title) || empty($course_excerpt)) {
    //         return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course Title/Excerpt should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    //     }
    
    //     // if (strlen($course_excerpt) > 145) {
    //     //     return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Excerpt length should be up to 145 characters', array('status' => $codes["POST_INSERT_FAIL"]["status"]));          
    //     // }
    
    //     // Prepare post data
    //     $post = array();
    //     $post['post_status']   = 'publish';
    //     $post['post_type']     = 'course';
    //     $post['post_title']    = $course_title;
    //     $post['post_content']  = $course_description;
    //     $post['post_excerpt']  = $course_excerpt;
    
    //     // If course_id is provided, update the post
    //     if ($course_id > 0) {
    //         $post['ID'] = $course_id; // Add course ID to update the post
    //         try {
    //             wp_update_post($post); // Update the post
    //             $message = str_replace("[Module_Name]", "Course Details", $codes["PUT_UPDATE"]["message"]);
    //         } catch (Exception $e) {
    //             error_log("Course Post is not updated");
    //             return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error updating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    //         }
    //     } else {
    //         // If no course_id is provided, create a new course
    //         try {
    //             $course_id = wp_insert_post($post); // Create a new post
    //             if (is_wp_error($course_id)) {
    //                 return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error creating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    //             }else{
    //                 $message = str_replace("[Module_Name]", "Course", $codes["POST_INSERT"]["message"]);
    //             }
    //         } catch (Exception $e) {
    //             error_log("Course Post is not created");
    //             return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error creating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
    //         }
    //     }
    
    //     // Add/update custom meta fields
    //     update_post_meta($course_id, 'live_classes', $live_classes);
    //     update_post_meta($course_id, 'Instructor_Hours', $instructor_hours);
    //     update_post_meta($course_id, 'Unit_Price', $price);
    
    //     // Send data to Elasticsearch (if needed)
    //     $curlPost = [
    //         "data" => [
    //             "details" => [
    //                 "record_id" => $course_id,
    //                 "update_event_type" => "course",
    //                 "resource_type" => "course",
    //                 "title" => $course_title,
    //                 "excerpt" => $course_excerpt,
    //                 "description" => $course_description,
    //                 "unit_price" => $price,
    //                 "live_classes" => $live_classes,
    //                 "instructor_hours" => $instructor_hours,
    //                 "published_at" => get_the_time('M j, Y g:i A', $course_id)
    //             ],
    //             "@timestamp" => date("Y-m-d H:i:s")
    //         ]
    //     ];
    
    //     try {
    //         update_elastic_event($curlPost);
    //     } catch (Exception $e) {
    //         error_log("Elasticsearch Post Entry is not updated");
    //     }
    
    //     // Prepare success message
       
    //     $code = $codes["PUT_UPDATE"]["code"];
    
    //     // Return success response
    //     $result = array(
    //         'code' => $code,
    //         'message' => $message,
    //         'data' => array('status' => $code, 'resource_id' => $course_id)
    //     );
    
    //     return new WP_REST_Response($result, 200);
    // }


    function insert_elastic_event($data, $course_id) {
        // Construct the Elasticsearch URL with the custom _id as course-{course_id}
        $elasticUrl = ELASTIC_SEARCH_END_URL . "/course/_doc/course-{$course_id}";
    
        // Set up the cURL request
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $elasticUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "PUT",  // Use PUT to create or update the document with the specific _id
            CURLOPT_POSTFIELDS => json_encode($data, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "Content-Type: application/json",
                "Authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, // Ensure you're using your authentication
            ),
        ));
    
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
    
        if ($err) {
            error_log("cURL Error #:" . $err);
            error_log(" --- 1633  Elasticsearch Insert errorr:" . date("Y-m-d H:i:s") . json_encode($err) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

            return false;  // Handle error
        } else {
            error_log("Elasticsearch Insert Response: " . $response);  // Log the response
            error_log(" --- 1633  Elasticsearch Insert Response:" . date("Y-m-d H:i:s") . json_encode($response) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

            return json_decode($response, true);  // Return the Elasticsearch response
        }
    }
    
    
    public function create_or_update_course_details($request) {
        global $wpdb;
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Kolkata');
        $currentNewDateTime = date('Y-m-d H:i:s');
    
        // Extract data from request
        $course_id = isset($request['course_id']) ? (int)$request['course_id'] : 0; // Check if course_id is provided
        $course_title = $request['course_title'];
        $course_excerpt = $request['course_excerpt'];
        $course_description = $request['description'];
        $price = (int)$request['price'];
        $live_classes = (int)$request['live_classes'];
        $instructor_hours = (int)$request['instructor_hours'];
        $personalization = $request['personalization'];
        $category = $request['category'];
        if (!is_array($personalization)) {
            // Make it an array
            $personalization = array($personalization);
        }
        $enrollment_type = $request['enrollment_type'];
        
        // Define template path - make sure this matches your theme structure
        $template = "templates/course.php";
    
        // Validate required fields
        if (empty($course_title) || empty($course_excerpt)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course Title/Excerpt should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
    
        // Prepare post data
        $post = array();
        $post['post_status']   = 'publish';
        $post['post_type']     = 'course';
        $post['post_title']    = $course_title;
        $post['post_content']  = $course_description;
        $post['post_excerpt']  = $course_excerpt;
    
        // If course_id is provided, update the post
        if ($course_id > 0) {
            $post['ID'] = $course_id; // Add course ID to update the post
            try {
                wp_update_post($post); // Update the post
                $message = str_replace("[Module_Name]", "Course Details", $codes["PUT_UPDATE"]["message"]);
                
                
                // Call Elasticsearch update function
                $curlPost = [
                    "data" => [
                        "details" => [
                            "record_id" => $course_id,
                            "update_event_type" => "course",
                            "resource_type" => "course",
                            "title" => $course_title,
                            "excerpt" => $course_excerpt,
                            "description" => $course_description,
                            "unit_price" => $price,
                            "live_classes" => $live_classes,
                            "instructor_hours" => $instructor_hours,
                            "published_at" => get_the_time('M j, Y g:i A', $course_id)
                        ],
                        "@timestamp" => date("Y-m-d H:i:s")
                    ]
                ];
                update_elastic_event($curlPost); // Use the existing update function
    
            } catch (Exception $e) {
                error_log("Course Post is not updated");
                return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error updating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
            }
        } else {
            // If no course_id is provided, create a new course
            try {
                $course_id = wp_insert_post($post); // Create a new post
                if (is_wp_error($course_id)) {
                    return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error creating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
                } else {
                    $message = str_replace("[Module_Name]", "Course", $codes["POST_INSERT"]["message"]);
                    $CategoryObj = get_term_by('slug', $category, 'course_category');
                    $term_id = is_object($CategoryObj) ? $CategoryObj->term_id : $CategoryObj['term_id'];
                    $cancellationPolicy = "Full refund: get back 100% of your money within the first 3 days of starting your classes";
                    
                    wp_set_post_terms($course_id, [$term_id], 'course_category');
                    update_post_meta($course_id, 'Group_Type', $personalization);
                    update_post_meta($course_id, 'enrollment_type', $enrollment_type);
                    update_post_meta($course_id, 'Product_Name', $course_title);
                    update_post_meta($course_id, 'Description', $course_description);
                    update_post_meta($course_id, 'Short_Description', $course_excerpt);
                    update_post_meta($course_id, 'is_course_enable', true);
                    update_post_meta($course_id, 'cancellation_policy_data', $cancellationPolicy);
                    update_post_meta($course_id, '_wp_page_template', $template);

                    // Update price based on personalization type
                    if (in_array("1-Many", $personalization)) {
                        update_post_meta($course_id, 'Group_Price', $price);
                    }
                    if (in_array("1-1", $personalization)) {
                        update_post_meta($course_id, '1_to_1_Price', $price);
                    }

                    // Get featured image URL
                    $featured_image_url = get_the_post_thumbnail_url($course_id, 'full');
                    $default_image = "";
                    
                    // Get category details
                    $category_details = [];
                    if($term_id) {
                        $term = get_term($term_id, 'course_category');
                        if(!is_wp_error($term)) {
                            $category_details[] = [
                                'id' => $term->term_id,
                                'name' => $term->name,
                                'slug' => $term->slug,
                                'parent_id' => $term->parent,
                                'sub_category' => []
                            ];
                        }
                    }

                    // Get post permalink
                    $course_url = get_permalink($course_id);
                    
                    // Call Elasticsearch insert function
                    $curlPost = [
                        "data" => [
                            "details" => [
                                "record_id" => $course_id,
                                "update_event_type" => "course",
                                "resource_type" => "course",
                                "title" => $course_title,
                                "excerpt" => $course_excerpt,
                                "description" => $course_description,
                                "unit_price" => $price,
                                "live_classes" => $live_classes,
                                "instructor_hours" => $instructor_hours,
                                "enrollment_type" => $enrollment_type,
                                "group_type" => $personalization,
                                "published_at" => get_the_time('M j, Y g:i A', $course_id),
                                "active_enrollments" => "0",
                                "active_batches" => "0",
                                "ongoing_batches" => "0",
                                "successful_students" => "0",
                                "parent_taxonomy" => $category,
                                "is_enable" => true,
                                "group_price" => (string)$price,
                                "taxonomy" => [$term_id],
                                "product_description" => $course_description,
                                "product_name" => $course_title,
                                "featuredImage" => $featured_image_url ?: $default_image,
                                "cancellation_policy" => $cancellationPolicy,
                                "course_category" => $category_details,
                                "type" => "Image",
                                "media_url" => $featured_image_url ?: $default_image,
                                "url" => $course_url,
                                "highlight_1" => "",
                                "highlight_2" => "",
                                "highlight_3" => "",
                                "highlight_4" => "",
                                "highlight_5" => "",
                                "highlight_6" => "",
                                "highlight_7" => "",
                                "highlight_8" => "",
                                "highlight_9" => "",
                                "highlight_10" => "",
                                "duration_weeks" => 6,
                                "academies" => [],
                                "event_date" => date('Y-m-d H:i:s', strtotime('+1 year')),
                                "max_seats" => "10"
                            ],
                            "@timestamp" => date("Y-m-d H:i:s")
                        ]
                    ];
                    self::insert_elastic_event($curlPost, $course_id);  // Use the new insert function
                }
            } catch (Exception $e) {
                error_log("Course Post is not created");
                return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Error creating course', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
            }
        }
    
        // Add/update custom meta fields
        update_post_meta($course_id, 'Unit_Price', $price);
    
        // Prepare success message
        $code = $codes["PUT_UPDATE"]["code"];
    
        // Return success response
        $result = array(
            'code' => $code,
            'message' => $message,
            'data' => array('status' => $code, 'resource_id' => $course_id)
        );
    
        return new WP_REST_Response($result, 200);
    }
    
    
    public function update_course_details_permissions_check(){
        return true;
    }
    /**
     * To get the list of testimonials on the basis of instructor
     * Get Data from ES Document named as resourceevent
     * */
    public function get_testimonials_instructors($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $date = date_default_timezone_set('Asia/Kolkata');
        $currentNewDateTime = date('Y-m-d H:i:s');
        $resource_type = $request['resource_type'];
        $role = $request['role'];
        $curlPost = [
            "query"=> [
                "bool"=> [
                    "must"=> [[
                        "match"=> [
                                "data.details.role"=> $role
                        ]
                    ],
                    [
                        "match"=> [
                                "data.details.resource_type"=> $resource_type
                        ]
                    ]
                    ]                    
                ]
            ]
        ];
        $curl = curl_init();         
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL."/resourceevent/_search?size=".ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
            "authorization: Basic ".ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache",
            "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $res=json_decode($response);
        $collections = [];
        foreach ($res->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $title = $result->_source->data->details->title;
            $instructor_id = $result->_source->data->details->instructor_id;
            $excerpt = $result->_source->data->details->description;
            $youtube_embed_url = $result->_source->data->details->youtube_embed_url;
            $instructor_first_name = get_user_meta($instructor_id, 'yuno_first_name', true);
            $instructor_last_name = get_user_meta($instructor_id, 'yuno_last_name', true);
            $instructor_profile_url=get_permalink(get_user_meta($instructor_id, 'profile_user_id_reference', true));
            if(!$instructor_profile_url){
                $profile_url=site_url()."/profile/".strtolower(get_user_meta($instructor_id, 'yuno_first_name', true))."-".strtolower(get_user_meta($instructor_id, 'yuno_last_name', true));
            } else {
             $profile_url=$instructor_profile_url;   
            }
            $collections[]=[
                "id"=>$record_id,
                "title"=>$title,
                "instructor_id"=>$instructor_id,
                "instructor_first_name"=>$instructor_first_name,
                "instructor_last_name" => $instructor_last_name,
                "video_url" => $youtube_embed_url,
                "excerpt" => $excerpt,
                "profile_url" => $profile_url
            ]; 
        }
        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"],'Video Testimonials Not Found' ,array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $results = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]","Video Testimonials",$codes["GET_SUCCESS"]["message"]),'status' => $codes["GET_SUCCESS"]["status"],  'data' => $collections); 
            return new WP_REST_Response($results, 200); 
        }
    }
    public function get_testimonials_instructors_permissions_check(){
        return true;
    }
    
    public function get_course_suggestions($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $searchText = $request['text'];
        $searchTerm = urldecode($searchText);
        $searchFields = [
            "data.details.title" => ["weight" => 3],
            "data.details.course_category.sub_category.sub_category.name" => ["weight" => 1],
            "data.details.parent_taxonomy" => ["weight" => 2]
        ];
        $fields = [];
        foreach ($searchFields as $fieldName => $fieldConfig) {
            $weight = isset($fieldConfig['weight']) ? $fieldConfig['weight'] : 1;
            $fields[] = "{$fieldName}^{$weight}";
        }

        $curlPost = [
            "_source" => [
                "data.details.record_id",
                "data.details.title",
                "data.details.course_category",
                "data.details.parent_taxonomy",
                "data.details.url",
                "data.details.duration_weeks",
                "data.details.featuredImage",
                "data.details.media_url",
                "data.details.type"
            ],
            "query" => [
                "bool" => [
                    "must" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "multi_match" => [
                                    "query" => $searchTerm,
                                    "fields" => $fields,
                                    "type" => "phrase_prefix"
                                ]
                            ]
                        ]
                    ],
                    "filter" => [
                        [
                            "nested" => [
                                "path" => "data.details",
                                "query" => [
                                    "term" => [
                                        "data.details.is_enable" => true
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            "highlight" => [
                "fields" => [
                    "data.details.title" => new \stdClass(),
                    "data.details.course_category.sub_category.sub_category.name" => new \stdClass(),
                    "data.details.parent_taxonomy" => new \stdClass()
                ]
            ]
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $res = json_decode($response);
        
        $courses = [];
        $categories = [];
        $sub_categories = [];

        foreach ($res->hits->hits as $result) {
            $highlight = $result->highlight;
            $record_id = $result->_source->data->details->record_id;
            $course_title = $result->_source->data->details->title;
            $parent_taxonomy = $result->_source->data->details->parent_taxonomy;
            $course_category = $result->_source->data->details->course_category;

            if ($course_title) {
                $media_url = $result->_source->data->details->media_url;
                $featured_image = $result->_source->data->details->featuredImage;
                $media_type = $result->_source->data->details->type;
                $image_url = ($media_url && $media_type == "Image") ? $media_url : $featured_image;
                $default_img_url = get_stylesheet_directory_uri().'/inc/img/videoDefault.jpg';
                if (empty($image_url)) {
                    $image_url = $default_img_url;
                }
                
                $courses[] = [
                    "id" => $record_id,
                    "title" => $course_title,
                    "imageurl" => $image_url,
                    "duration_weeks" => $result->_source->data->details->duration_weeks,
                    "course_url" => $result->_source->data->details->url,
                ];
            }

            if(!empty($course_category)){
                foreach ($course_category as $category) {
                    if ($parent_taxonomy) {
                        if (is_object($category) && mb_stripos($category->name, $searchTerm) !== false) {
                            $categories[$category->id]['id'] = $category->id;
                            $categories[$category->id]['name'] = $category->name;
                            $categories[$category->id]['slug'] = $category->slug;
                            $categories[$category->id]['course_count'] = ($categories[$category->id]['course_count'] ?? 0) + 1;
                        }else{
                            foreach ($category as $cat) {
                                if (is_object($cat) && mb_stripos($cat->name, $searchTerm) !== false) {
                                    $categories[$cat->id]['id'] = $cat->id;
                                    $categories[$cat->id]['name'] = $cat->name;
                                    $categories[$cat->id]['slug'] = $cat->slug;
                                    $categories[$cat->id]['course_count'] = ($categories[$cat->id]['course_count'] ?? 0) + 1;
                                }
                            }
                        }
                    }

                    if (isset($highlight->{'data.details.course_category.sub_category.sub_category.name'})) {
                        foreach ($category as $cat) {
                            if (is_object($cat)) {    
                                continue;
                            } else if (is_array($cat)) {
                                foreach ($cat as $sub_category) {
                                    if (is_array($sub_category->sub_category) || is_object($sub_category->sub_category)) {
                                        foreach ($sub_category->sub_category as $sub_sub_category) {
                                            if ($sub_sub_category && is_object($sub_sub_category) && isset($sub_sub_category->name) && mb_stripos($sub_sub_category->name, $searchTerm) !== false) {
                                                $sub_categories[$sub_sub_category->id]['id'] = $sub_sub_category->id;
                                                $sub_categories[$sub_sub_category->id]['name'] = $sub_sub_category->name;
                                                $sub_categories[$sub_sub_category->id]['parent_cat_name'] = $category->name;
                                                $sub_categories[$sub_sub_category->id]['category_level_1'] = $sub_category->id;
                                                $sub_categories[$sub_sub_category->id]['parent_cat_id'] = $sub_category->parent_id;
                                                $sub_categories[$sub_sub_category->id]['parent_cat_slug'] = $category->slug;
                                                $sub_categories[$sub_sub_category->id]['course_count'] = ($sub_sub_category->id['course_count'] ?? 0) + 1;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                }
            }
        }
        $response = [
            "course" => $courses,
            "category" => array_values($categories),
            "sub_category" => array_values($sub_categories),
        ];

        if (empty($courses) && empty($categories) && empty($sub_categories)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Courses Not Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Courses", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'data' => $response);
            return new WP_REST_Response($result, 200);
        }
    }

    public function check_instructor_course($request)
    {
      global $wpdb;
      $codes = error_code_setting();
      $parms = json_decode($request->get_body(), true);
      $course_id = (int)$parms['course_id'];
      $user_id = (int)$parms['user_id'];
      $isFeatured = (int) get_post_meta($course_id, 'Featured_Course', true);
      if ($isFeatured === 0) {
        return new WP_Error($codes["GET_FAIL"]["code"], 'Course not available.', array('status' => $codes["GET_FAIL"]["status"]));
      } else {
        if (!(CURRENT_LOGGED_IN_USER_ID)) {
          return new WP_Error($codes["GET_FAIL"]["code"], 'You need to be logged in as an instructor to access this page.', array('status' => $codes["GET_FAIL"]["status"]));
        }else{
          $userdata = get_userdata($user_id);
          $user_email = $userdata->user_email;
          if($userdata->roles){
            if(in_array('um_instructor', $userdata->roles)){
                $records = $wpdb->get_results("SELECT * FROM wp_course_instructor_relationships where instructor_id=$user_id and course_id=$course_id");
                if(count($records) > 0) {
                    $message = "course already assigned.";
                    return new WP_Error($codes["GET_FAIL"]["code"], $message, array('status' => $codes["GET_FAIL"]["status"]));
                }else{
                    $code = $codes["GET_SUCCESS"]["code"];
                    $response = array(
                        'code' => $code,
                        'message' => "start teaching this course",
                        'data' => $userdata
                    );
                    return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
                }
            
            }else{
                $role = $userdata->roles[0];
                $message = "You are logged in as $user_email (role: $role). You need to be logged in as an instructor to access this page.";
                return new WP_Error($codes["GET_FAIL"]["code"], $message, array('status' => $codes["GET_FAIL"]["status"]));
            }
            }else{
            return new WP_Error($codes["GET_FAIL"]["code"], 'Not an instructor.', array('status' => $codes["GET_FAIL"]["status"]));
            }
            
        }
        
        }
    }

    private function get_org_admins_by_org($org_id) {
        $curl = curl_init();
        $searchQuery = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => [
                        'term' => [
                            'data.details.record_id' => $org_id
                        ]
                    ]
                ]
            ]
        ];
    
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/org/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($searchQuery, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
    
        $response = curl_exec($curl);
        $err = curl_error($curl);
    
        curl_close($curl);
    
        if ($err) {
            echo "cURL Error #:" . $err;
            return null;
        } else {
            $res = json_decode($response, true);
            if (isset($res['hits']['hits'][0]['_source']['data']['details']['org-admin'])) {
                return $res['hits']['hits'][0]['_source']['data']['details']['org-admin'];
            } else {
                return null;
            }
        }
    }
    

    private function get_org_id_by_academy($academy_id) {
        $curl = curl_init();
    
        $searchQuery = [
            'query' => [
                'term' => [
                    'data.details.record_id' => $academy_id
                ]
            ]
        ];
    
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/academies/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($searchQuery, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
    
        $response = curl_exec($curl);
        $err = curl_error($curl);
    
        curl_close($curl);
    
        if ($err) {
            echo "cURL Error #:" . $err;
            return null;
        } else {
            $res = json_decode($response, true);
            if (isset($res['hits']['hits'][0]['_source']['data']['details']['org_id'])) {
                return $res['hits']['hits'][0]['_source']['data']['details']['org_id'];
            } else {
                return null;
            }
        }
    }

    public function assign_instructor_and_notify_admin($request){
        global $wpdb;
        $codes = error_code_setting();
        $request = json_decode($request->get_body(), true);
        $course_id = (int)$request['course_id'];
        $user_id = (int) $request['user_id'];
        $group_type_meta = get_post_meta($course_id, 'Group_Type', true);

        $group_types = [
            'one_to_many' => false,
            'one_to_one' => false,
        ];
        $meta_mapping = [
            '1-Many' => 'one_to_many',
            '1-1' => 'one_to_one'
        ];
        
        foreach ($group_type_meta as $group_type) {
            if (array_key_exists($group_type, $meta_mapping)) {
                $mapped_key = $meta_mapping[$group_type];
                $group_types[$mapped_key] = true;
            }
        }
        $userdata = get_userdata($user_id);
        $user_email = $userdata->user_email;
        if($userdata->roles){
            if(in_array('um_instructor', $userdata->roles)){
                $productController = new ProductController();
                $request = new WP_REST_Request();
                $request->set_body(json_encode([
                    'instructor_id' => $user_id,
                    'course_detail' => [
                        [
                            'course_id' => $course_id,
                            'group_type' => $group_types
                        ]
                    ]
                ]));
                $assignCourse = $productController->map_course_instructor($request);
               
                if (isset($assignCourse->errors) && !empty($assignCourse->errors)) {
                    if(isset($assignCourse->error_data['204']['message'])){
                        return new WP_Error($codes["GET_FAIL"]["code"], $assignCourse->error_data['204']['message'], array('status' => $codes["GET_FAIL"]["status"]));

                    }else{
                        return new WP_Error($codes["GET_FAIL"]["code"], 'There was an error in processing your request', array('status' => $codes["GET_FAIL"]["status"]));
                    }
                }else{
                    $invitation_link = get_user_meta($user_id, 'invitation_link_instructor', true);
                    $encoded_value = basename($invitation_link);
                    $decoded_value = base64_decode($encoded_value);
                    $decoded_parts = explode('@@@', $decoded_value);
                    $decoded_mapping = [
                        'user_email' => isset($decoded_parts[0]) ? $decoded_parts[0] : null,
                        'user_id' => isset($decoded_parts[1]) ? $decoded_parts[1] : null,
                        'course_id' => isset($decoded_parts[2]) ? $decoded_parts[2] : null,
                        'academy_id' => isset($decoded_parts[3]) ? $decoded_parts[3] : 0,
                        'org_admin_id' => isset($decoded_parts[4]) ? $decoded_parts[4] : 0
                    ];
                    if ($decoded_mapping['academy_id']) {
                        $org_id = $this->get_org_id_by_academy($decoded_mapping['academy_id']);
                    } else {
                        $org_id = 0;
                    }
                    $userData = ['course_id' => $course_id,'instructor_id' => $user_id];
                    if ($org_id) {
                        $org_admins = $this->get_org_admins_by_org($org_id);
                        foreach ($org_admins as $admin) {
                            email_notification('INSTRUCTOR_MAPPED_TO_COURSE', $admin, $userData);
                        }
                    }
                    $date = date_default_timezone_set('Asia/Kolkata');
                    $current_date = date("Y-m-d H:i:s"); 
                    $notification_data = array(
                        'course_id' => $course_id,
                        'instructor_id' => $user_id,
                        'email' => $user_email,
                        'invitation_date' => '',
                        'is_accepted' => 'true',
                        'when_accepted' => $current_date
                    );
                    
                    add_post_meta($course_id, 'instructor_invitations', json_encode($notification_data));
                    $result = ['code' => $codes["POST_INSERT"]["code"], 'message' => "Success! You can now start teaching this course", 'status' => $codes["POST_INSERT"]["status"]];
                    return new WP_REST_Response($result, 200);
                    
                }
            }
            else{
                return new WP_Error($codes["GET_FAIL"]["code"], 'Not an instructor.', array('status' => $codes["GET_FAIL"]["status"]));
            }
        }
    }

    public function get_invited_instructors($request) {
        global $wpdb;
        $codes = error_code_setting();
        $courseId = $request['course_id'];
        $userId = $request['user_id'];
        $userdata = get_userdata($userId);
        $view = $request['view'];
        $limit = $request['limit'];
        $offset = $request['offset'];
        $typeCollectionArray = array("list-view", "grid-view");

        // get active org
        $active_org = get_user_meta($userId, 'active_org', true);
        $org_id = get_post_meta($courseId, 'organization',true);

        // Check if both are not equal
        if (in_array('um_org-admin', $userdata->roles)) {
            if ($org_id !== $active_org) {
                return new WP_Error($codes["GET_FAIL"]["code"], 'The data does not belong to your current organization. Switch to a different organization and try again.', array('status' => $codes["GET_FAIL"]["status"]));
            }
        }
    
        if (!in_array($view, $typeCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the view type', array('status' => $codes["GET_FAIL"]["status"]));
        }
        if (isset($userId) && empty($userdata)) {
            return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));     
        }
        if (in_array('um_org-admin', $userdata->roles) || in_array('um_yuno-admin', $userdata->roles)) {
            // $findInstructors = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_postmeta WHERE meta_key = 'instructor_invitations' AND meta_value LIKE %s", '%"course_id":' . $courseId . '%'), ARRAY_A);

            $findInstructors = $wpdb->get_results($wpdb->prepare("SELECT DISTINCT instructor_id FROM {$wpdb->prefix}course_instructor_relationships WHERE course_id = %d", $courseId));

            if (empty($findInstructors)) {
                return new WP_Error($codes["GET_FAIL"]["code"], 'No instructor found', array('status' => $codes["GET_FAIL"]["status"]));
            } else {
                
                $instructors = [];
                foreach ($findInstructors as $instructor) {
                    // $user_info = $instructor['meta_value'];
                    // $user_data = json_decode($user_info, true);
                    $user_id = $instructor->instructor_id;
                    $usermeta = $wpdb->get_results($wpdb->prepare("SELECT meta_key, meta_value FROM wp_usermeta WHERE user_id = %d", $user_id), ARRAY_A);
                    $userDetails = [];
                    foreach ($usermeta as $meta) {
                        $userDetails[$meta['meta_key']] = $meta['meta_value'];
                    }
                    
                    $curl = curl_init();
                    $searchQuery = [
                        'query' => [
                            'term' => [
                                'data.details.user_id' => $user_id
                            ]
                        ]
                    ];
                    curl_setopt_array($curl, array(
                        CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                        CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/instructorsignedup/_search?size=" . ELASTIC_RECORDS_COUNT,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($searchQuery, JSON_UNESCAPED_SLASHES),
                        CURLOPT_HTTPHEADER => array(
                            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                            "cache-control: no-cache",
                            "content-type: application/json"
                        ),
                    ));
    
                    $response = curl_exec($curl);
                    $err = curl_error($curl);
                    curl_close($curl);
    
                    if ($err) {
                        return new WP_Error($codes["GET_FAIL"]["code"], 'Elasticsearch query error: ' . $err, array('status' => $codes["GET_FAIL"]["status"]));
                    } else {
                        $esData = json_decode($response, true);
                        if (isset($esData['hits']['hits'][0]['_source'])) {
                            $details = $esData['hits']['hits'][0]['_source']['data']['details'];
                            $instructors[] = [
                                'email' => $userDetails['yuno_gplus_email'],
                                'name' => $userDetails['yuno_display_name'],
                                'profile_image' => $userDetails['googleplus_profile_img'],
                                'rating' => $details['avg_rating'],
                                'active_students' => $details['active_enrolments'],
                                'past_students' => $details['completed_enrolments']
                            ];
                        }
                    }
                }
                $columns = [
                    [
                      "field" => "email",
                      "label" => "email",
                    ],
                    [
                      "field" => "name",
                      "label" => "name",
                    ]
                ];
                $totalInstructors = count($instructors);

                if ($totalInstructors === 0) {
                    return new WP_Error($codes["GET_FAIL"]["code"], 'No instructor found', array('status' => $codes["GET_FAIL"]["status"]));
                }
                $instructors = array_slice($instructors, $offset, $limit);
                $instructorData = [
                    "rows" => $instructors,
                    "columns" => $columns
                ];
                if ($view == 'list-view') {
                    $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Instructors", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $totalInstructors, 'data' => $instructors);
                } else {
                    $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Instructors", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $totalInstructors, 'data' => $instructorData);
                } 
                return new WP_REST_Response($result, 200);
            }
        } else {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Role not allowed', array('status' => $codes["GET_FAIL"]["status"]));
        }
    }

    public function bulk_map_courses_instructor($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);
        $instructor_id = (int)$data['instructor_id'];
        $course_ids = $data['course_ids'];

        // Validate instructor existence and type
        $isInstructorExist = $wpdb->get_results($wpdb->prepare(
            "SELECT ID FROM wp_users WHERE ID = %d",
            $instructor_id
        ), ARRAY_A);

        if (count($isInstructorExist) == 0) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Please check instructor id', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $instructor_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT user_id FROM wp_usermeta WHERE meta_key = 'wp_capabilities' AND meta_value LIKE %s AND user_id = %d", '%um_instructor%', $instructor_id
            )
        );

        if (empty($instructor_exists)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Please check instructor id', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $course_data_array = [];
        $course_titles = []; 

        foreach ($course_ids as $course_id) {
            $course_id = (int)$course_id;

            // Check if the instructor is already assigned to the course
            $alreadyExist = $wpdb->get_results($wpdb->prepare(
                "SELECT * from wp_course_instructor_relationships where instructor_id=%d and course_id=%d",
                $instructor_id, $course_id
            ), ARRAY_A);

            if (count($alreadyExist) > 0) {
                continue;
            }

            $group_type_meta = get_post_meta($course_id, 'Group_Type', true);

            $group_types = [
                'one_to_many' => false,
                'one_to_one' => false,
            ];
            $meta_mapping = [
                '1-Many' => 'one_to_many',
                '1-1' => 'one_to_one'
            ];

            foreach ($group_type_meta as $group_type) {
                if (array_key_exists($group_type, $meta_mapping)) {
                    $mapped_key = $meta_mapping[$group_type];
                    $group_types[$mapped_key] = true;
                }
            }

            $new_group_type = serialize($group_types);

            // Insert new record if it doesn't exist
            $wpdb->insert(
                'wp_course_instructor_relationships',
                array(
                    'instructor_id' => $instructor_id,
                    'course_id' => $course_id,
                    'group_type' => $new_group_type
                )
            );
            $course_data_array[] = [
                'course_id' => $course_id,
                'group_type' => $group_types
            ];
            $course_titles[] = get_the_title($course_id);
            $date = date_default_timezone_set('Asia/Kolkata');
            $current_date = date("Y-m-d H:i:s"); 
            $userdata = get_userdata($instructor_id);
            $user_email = $userdata->user_email;
            $notification_data = array(
                'course_id' => $course_id,
                'instructor_id' => $instructor_id,
                'email' => $user_email,
                'invitation_date' => '',
                'is_accepted' => 'true',
                'when_accepted' => $current_date
            );
            
            add_post_meta($course_id, 'instructor_invitations', json_encode($notification_data));
        }

        // Schedule event for updating instructor courses
        $wpdb->insert(
            'wp_class_pending_data',
            array(
                'data' => json_encode([
                    'instructor_id' => $instructor_id,
                    'course_data' => $course_data_array
                ]),
                'start_datetime' => date("Y-m-d H:i:s", strtotime("+2 minutes")),
                'event_type' => 'instructor_update_course'
            )
        );

        if (!empty($course_titles)) {
            $classData = [
                'instructor_id' => $instructor_id,
                'course_titles' => $course_titles
            ];

            $email_type = 'INSTRUCTOR_MAPPED_TO_COURSE';
            email_notification($email_type, $instructor_id, $classData);
        }

        $result = array(
            'code' => $codes["POST_INSERT"]["code"],
            'message' => 'Courses assigned to instructor',
            'data' => array('status' => $codes["POST_INSERT"]["code"])
        );
        delete_cache("get_instructor_products_detail");
        $product_obj = new ProductController();
        $product_obj->instructor_coures($instructor_id, "second");
        return new WP_REST_Response($result, $codes["POST_INSERT"]["code"]);
    }

}
?>