/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,o){for(var a=-1,r=null==e?0:e.length;++a<r;){var s=e[a];t(o,s,n(s),e)}return o}function n(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}function r(e,t){for(var n=-1,o=null==e?0:e.length,a=0,r=[];++n<o;){var s=e[n];t(s,n,e)&&(r[a++]=s)}return r}function s(e,t){return!(null==e||!e.length)&&g(e,t,0)>-1}function i(e,t,n){for(var o=-1,a=null==e?0:e.length;++o<a;)if(n(t,e[o]))return!0;return!1}function l(e,t){for(var n=-1,o=null==e?0:e.length,a=Array(o);++n<o;)a[n]=t(e[n],n,e);return a}function c(e,t){for(var n=-1,o=t.length,a=e.length;++n<o;)e[a+n]=t[n];return e}function u(e,t,n,o){var a=-1,r=null==e?0:e.length;for(o&&r&&(n=e[++a]);++a<r;)n=t(n,e[a],a,e);return n}function d(e,t,n,o){var a=null==e?0:e.length;for(o&&a&&(n=e[--a]);a--;)n=t(n,e[a],a,e);return n}function p(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}function m(e){return e.match(Ke)||[]}function f(e,t,n){var o;return n(e,(function(e,n,a){if(t(e,n,a))return o=n,!1})),o}function h(e,t,n,o){for(var a=e.length,r=n+(o?1:-1);o?r--:++r<a;)if(t(e[r],r,e))return r;return-1}function g(e,t,n){return t==t?function(e,t,n){for(var o=n-1,a=e.length;++o<a;)if(e[o]===t)return o;return-1}(e,t,n):h(e,y,n)}function v(e,t,n,o){for(var a=n-1,r=e.length;++a<r;)if(o(e[a],t))return a;return-1}function y(e){return e!=e}function b(e,t){var n=null==e?0:e.length;return n?I(e,t)/n:X}function w(e){return function(t){return null==t?B:t[e]}}function _(e){return function(t){return null==e?B:e[t]}}function k(e,t,n,o,a){return a(e,(function(e,a,r){n=o?(o=!1,e):t(n,e,a,r)})),n}function I(e,t){for(var n,o=-1,a=e.length;++o<a;){var r=t(e[o]);r!==B&&(n=n===B?r:n+r)}return n}function C(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function L(e){return e?e.slice(0,F(e)+1).replace(Ge,""):e}function S(e){return function(t){return e(t)}}function A(e,t){return l(t,(function(t){return e[t]}))}function j(e,t){return e.has(t)}function R(e,t){for(var n=-1,o=e.length;++n<o&&g(t,e[n],0)>-1;);return n}function D(e,t){for(var n=e.length;n--&&g(t,e[n],0)>-1;);return n}function P(e){return"\\"+Qt[e]}function T(e){return Yt.test(e)}function U(e){return zt.test(e)}function M(e){var t=-1,n=Array(e.size);return e.forEach((function(e,o){n[++t]=[o,e]})),n}function H(e,t){return function(n){return e(t(n))}}function $(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var s=e[n];s!==t&&s!==z||(e[n]=z,r[a++]=n)}return r}function O(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function x(e){return T(e)?function(e){for(var t=Bt.lastIndex=0;Bt.test(e);)++t;return t}(e):fn(e)}function E(e){return T(e)?function(e){return e.match(Bt)||[]}(e):function(e){return e.split("")}(e)}function F(e){for(var t=e.length;t--&&We.test(e.charAt(t)););return t}function N(e){return e.match(Vt)||[]}var B,V="Expected a function",Y="__lodash_hash_undefined__",z="__lodash_placeholder__",G=16,W=32,q=64,J=128,Q=256,K=1/0,Z=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,oe=[["ary",J],["bind",1],["bindKey",2],["curry",8],["curryRight",G],["flip",512],["partial",W],["partialRight",q],["rearg",Q]],ae="[object Arguments]",re="[object Array]",se="[object Boolean]",ie="[object Date]",le="[object Error]",ce="[object Function]",ue="[object GeneratorFunction]",de="[object Map]",pe="[object Number]",me="[object Object]",fe="[object Promise]",he="[object RegExp]",ge="[object Set]",ve="[object String]",ye="[object Symbol]",be="[object WeakMap]",we="[object ArrayBuffer]",_e="[object DataView]",ke="[object Float32Array]",Ie="[object Float64Array]",Ce="[object Int8Array]",Le="[object Int16Array]",Se="[object Int32Array]",Ae="[object Uint8Array]",je="[object Uint8ClampedArray]",Re="[object Uint16Array]",De="[object Uint32Array]",Pe=/\b__p \+= '';/g,Te=/\b(__p \+=) '' \+/g,Ue=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Me=/&(?:amp|lt|gt|quot|#39);/g,He=/[&<>"']/g,$e=RegExp(Me.source),Oe=RegExp(He.source),xe=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,Fe=/<%=([\s\S]+?)%>/g,Ne=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Be=/^\w*$/,Ve=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ye=/[\\^$.*+?()[\]{}|]/g,ze=RegExp(Ye.source),Ge=/^\s+/,We=/\s/,qe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Je=/\{\n\/\* \[wrapped with (.+)\] \*/,Qe=/,? & /,Ke=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ze=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,ot=/^0b[01]+$/i,at=/^\[object .+?Constructor\]$/,rt=/^0o[0-7]+$/i,st=/^(?:0|[1-9]\d*)$/,it=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ct=/['\n\r\u2028\u2029\\]/g,ut="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pt="\\u2700-\\u27bf",mt="a-z\\xdf-\\xf6\\xf8-\\xff",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",ht="\\ufe0e\\ufe0f",gt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",yt="["+ut+"]",bt="["+gt+"]",wt="["+dt+"]",_t="\\d+",kt="["+pt+"]",It="["+mt+"]",Ct="[^"+ut+gt+_t+pt+mt+ft+"]",Lt="\\ud83c[\\udffb-\\udfff]",St="[^"+ut+"]",At="(?:\\ud83c[\\udde6-\\uddff]){2}",jt="[\\ud800-\\udbff][\\udc00-\\udfff]",Rt="["+ft+"]",Dt="\\u200d",Pt="(?:"+It+"|"+Ct+")",Tt="(?:"+Rt+"|"+Ct+")",Ut="(?:['’](?:d|ll|m|re|s|t|ve))?",Mt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ht="(?:"+wt+"|"+Lt+")"+"?",$t="["+ht+"]?",Ot=$t+Ht+("(?:"+Dt+"(?:"+[St,At,jt].join("|")+")"+$t+Ht+")*"),xt="(?:"+[kt,At,jt].join("|")+")"+Ot,Et="(?:"+[St+wt+"?",wt,At,jt,yt].join("|")+")",Ft=RegExp(vt,"g"),Nt=RegExp(wt,"g"),Bt=RegExp(Lt+"(?="+Lt+")|"+Et+Ot,"g"),Vt=RegExp([Rt+"?"+It+"+"+Ut+"(?="+[bt,Rt,"$"].join("|")+")",Tt+"+"+Mt+"(?="+[bt,Rt+Pt,"$"].join("|")+")",Rt+"?"+Pt+"+"+Ut,Rt+"+"+Mt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_t,xt].join("|"),"g"),Yt=RegExp("["+Dt+ut+dt+ht+"]"),zt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Gt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Wt=-1,qt={};qt[ke]=qt[Ie]=qt[Ce]=qt[Le]=qt[Se]=qt[Ae]=qt[je]=qt[Re]=qt[De]=!0,qt[ae]=qt[re]=qt[we]=qt[se]=qt[_e]=qt[ie]=qt[le]=qt[ce]=qt[de]=qt[pe]=qt[me]=qt[he]=qt[ge]=qt[ve]=qt[be]=!1;var Jt={};Jt[ae]=Jt[re]=Jt[we]=Jt[_e]=Jt[se]=Jt[ie]=Jt[ke]=Jt[Ie]=Jt[Ce]=Jt[Le]=Jt[Se]=Jt[de]=Jt[pe]=Jt[me]=Jt[he]=Jt[ge]=Jt[ve]=Jt[ye]=Jt[Ae]=Jt[je]=Jt[Re]=Jt[De]=!0,Jt[le]=Jt[ce]=Jt[be]=!1;var Qt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Kt=parseFloat,Zt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,on=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=on&&on.exports===nn,rn=an&&Xt.process,sn=function(){try{var e=on&&on.require&&on.require("util").types;return e||rn&&rn.binding&&rn.binding("util")}catch(e){}}(),ln=sn&&sn.isArrayBuffer,cn=sn&&sn.isDate,un=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,mn=sn&&sn.isTypedArray,fn=w("length"),hn=_({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),gn=_({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=_({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function _(We){function Ke(e){if(Na(e)&&!Di(e)&&!(e instanceof pt)){if(e instanceof dt)return e;if(Dr.call(e,"__wrapped__"))return ma(e)}return new dt(e)}function ut(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=B}function pt(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new ht;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new ft(e)).size}function yt(e,t){var n=Di(e),o=!n&&Ri(e),a=!n&&!o&&Ti(e),r=!n&&!o&&!a&&Oi(e),s=n||o||a||r,i=s?C(e.length,Ir):[],l=i.length;for(var c in e)!t&&!Dr.call(e,c)||s&&("length"==c||a&&("offset"==c||"parent"==c)||r&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ko(c,l))||i.push(c);return i}function bt(e){var t=e.length;return t?e[Un(0,t-1)]:B}function wt(e,t){return ca(uo(e),Rt(t,0,e.length))}function _t(e){return ca(uo(e))}function kt(e,t,n){(n===B||Ua(e[t],n))&&(n!==B||t in e)||At(e,t,n)}function It(e,t,n){var o=e[t];Dr.call(e,t)&&Ua(o,n)&&(n!==B||t in e)||At(e,t,n)}function Ct(e,t){for(var n=e.length;n--;)if(Ua(e[n][0],t))return n;return-1}function Lt(e,t,n,o){return js(e,(function(e,a,r){t(o,e,n(e),r)})),o}function St(e,t){return e&&po(t,tr(t),e)}function At(e,t,n){"__proto__"==t&&qr?qr(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function jt(e,t){for(var n=-1,o=t.length,a=gr(o),r=null==e;++n<o;)a[n]=r?B:Xa(e,t[n]);return a}function Rt(e,t,n){return e==e&&(n!==B&&(e=e<=n?e:n),t!==B&&(e=e>=t?e:t)),e}function Dt(e,t,o,a,r,s){var i,l=1&t,c=2&t,u=4&t;if(o&&(i=r?o(e,a,r,s):o(e)),i!==B)return i;if(!Fa(e))return e;var d=Di(e);if(d){if(i=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Dr.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return uo(e,i)}else{var p=Fs(e),m=p==ce||p==ue;if(Ti(e))return ao(e,l);if(p==me||p==ae||m&&!r){if(i=c||m?{}:Jo(e),!l)return c?function(e,t){return po(e,Es(e),t)}(e,function(e,t){return e&&po(t,nr(t),e)}(i,e)):function(e,t){return po(e,xs(e),t)}(e,St(i,e))}else{if(!Jt[p])return r?e:{};i=function(e,t,n){var o=e.constructor;switch(t){case we:return ro(e);case se:case ie:return new o(+e);case _e:return function(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case ke:case Ie:case Ce:case Le:case Se:case Ae:case je:case Re:case De:return so(e,n);case de:return new o;case pe:case ve:return new o(e);case he:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case ge:return new o;case ye:return function(e){return Ls?_r(Ls.call(e)):{}}(e)}}(e,p,l)}}s||(s=new vt);var f=s.get(e);if(f)return f;s.set(e,i),$i(e)?e.forEach((function(n){i.add(Dt(n,t,o,n,e,s))})):Mi(e)&&e.forEach((function(n,a){i.set(a,Dt(n,t,o,a,e,s))}));var h=d?B:(u?c?No:Fo:c?nr:tr)(e);return n(h||e,(function(n,a){h&&(n=e[a=n]),It(i,a,Dt(n,t,o,a,e,s))})),i}function Pt(e,t,n){var o=n.length;if(null==e)return!o;for(e=_r(e);o--;){var a=n[o],r=t[a],s=e[a];if(s===B&&!(a in e)||!r(s))return!1}return!0}function Tt(e,t,n){if("function"!=typeof e)throw new Cr(V);return Vs((function(){e.apply(B,n)}),t)}function Ut(e,t,n,o){var a=-1,r=s,c=!0,u=e.length,d=[],p=t.length;if(!u)return d;n&&(t=l(t,S(n))),o?(r=i,c=!1):t.length>=200&&(r=j,c=!1,t=new gt(t));e:for(;++a<u;){var m=e[a],f=null==n?m:n(m);if(m=o||0!==m?m:0,c&&f==f){for(var h=p;h--;)if(t[h]===f)continue e;d.push(m)}else r(t,f,o)||d.push(m)}return d}function Mt(e,t){var n=!0;return js(e,(function(e,o,a){return n=!!t(e,o,a)})),n}function Ht(e,t,n){for(var o=-1,a=e.length;++o<a;){var r=e[o],s=t(r);if(null!=s&&(i===B?s==s&&!za(s):n(s,i)))var i=s,l=r}return l}function $t(e,t){var n=[];return js(e,(function(e,o,a){t(e,o,a)&&n.push(e)})),n}function Ot(e,t,n,o,a){var r=-1,s=e.length;for(n||(n=Qo),a||(a=[]);++r<s;){var i=e[r];t>0&&n(i)?t>1?Ot(i,t-1,n,o,a):c(a,i):o||(a[a.length]=i)}return a}function xt(e,t){return e&&Ds(e,t,tr)}function Et(e,t){return e&&Ps(e,t,tr)}function Bt(e,t){return r(t,(function(t){return Oa(e[t])}))}function Vt(e,t){for(var n=0,o=(t=no(t,e)).length;null!=e&&n<o;)e=e[ua(t[n++])];return n&&n==o?e:B}function Yt(e,t,n){var o=t(e);return Di(e)?o:c(o,n(e))}function zt(e){return null==e?e===B?"[object Undefined]":"[object Null]":Wr&&Wr in _r(e)?function(e){var t=Dr.call(e,Wr),n=e[Wr];try{e[Wr]=B;var o=!0}catch(e){}var a=Ur.call(e);return o&&(t?e[Wr]=n:delete e[Wr]),a}(e):function(e){return Ur.call(e)}(e)}function Qt(e,t){return e>t}function Xt(e,t){return null!=e&&Dr.call(e,t)}function en(e,t){return null!=e&&t in _r(e)}function nn(e,t,n){for(var o=n?i:s,a=e[0].length,r=e.length,c=r,u=gr(r),d=1/0,p=[];c--;){var m=e[c];c&&t&&(m=l(m,S(t))),d=ss(m.length,d),u[c]=!n&&(t||a>=120&&m.length>=120)?new gt(c&&m):B}m=e[0];var f=-1,h=u[0];e:for(;++f<a&&p.length<d;){var g=m[f],v=t?t(g):g;if(g=n||0!==g?g:0,!(h?j(h,v):o(p,v,n))){for(c=r;--c;){var y=u[c];if(!(y?j(y,v):o(e[c],v,n)))continue e}h&&h.push(v),p.push(g)}}return p}function on(t,n,o){var a=null==(t=ra(t,n=no(n,t)))?t:t[ua(ya(n))];return null==a?B:e(a,t,o)}function rn(e){return Na(e)&&zt(e)==ae}function sn(e,t,n,o,a){return e===t||(null==e||null==t||!Na(e)&&!Na(t)?e!=e&&t!=t:function(e,t,n,o,a,r){var s=Di(e),i=Di(t),l=s?re:Fs(e),c=i?re:Fs(t);l=l==ae?me:l,c=c==ae?me:c;var u=l==me,d=c==me,p=l==c;if(p&&Ti(e)){if(!Ti(t))return!1;s=!0,u=!1}if(p&&!u)return r||(r=new vt),s||Oi(e)?xo(e,t,n,o,a,r):function(e,t,n,o,a,r,s){switch(n){case _e:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case we:return!(e.byteLength!=t.byteLength||!r(new Er(e),new Er(t)));case se:case ie:case pe:return Ua(+e,+t);case le:return e.name==t.name&&e.message==t.message;case he:case ve:return e==t+"";case de:var i=M;case ge:var l=1&o;if(i||(i=O),e.size!=t.size&&!l)return!1;var c=s.get(e);if(c)return c==t;o|=2,s.set(e,t);var u=xo(i(e),i(t),o,a,r,s);return s.delete(e),u;case ye:if(Ls)return Ls.call(e)==Ls.call(t)}return!1}(e,t,l,n,o,a,r);if(!(1&n)){var m=u&&Dr.call(e,"__wrapped__"),f=d&&Dr.call(t,"__wrapped__");if(m||f){var h=m?e.value():e,g=f?t.value():t;return r||(r=new vt),a(h,g,n,o,r)}}return!!p&&(r||(r=new vt),function(e,t,n,o,a,r){var s=1&n,i=Fo(e),l=i.length;if(l!=Fo(t).length&&!s)return!1;for(var c=l;c--;){var u=i[c];if(!(s?u in t:Dr.call(t,u)))return!1}var d=r.get(e),p=r.get(t);if(d&&p)return d==t&&p==e;var m=!0;r.set(e,t),r.set(t,e);for(var f=s;++c<l;){var h=e[u=i[c]],g=t[u];if(o)var v=s?o(g,h,u,t,e,r):o(h,g,u,e,t,r);if(!(v===B?h===g||a(h,g,n,o,r):v)){m=!1;break}f||(f="constructor"==u)}if(m&&!f){var y=e.constructor,b=t.constructor;y!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return r.delete(e),r.delete(t),m}(e,t,n,o,a,r))}(e,t,n,o,sn,a))}function fn(e,t,n,o){var a=n.length,r=a,s=!o;if(null==e)return!r;for(e=_r(e);a--;){var i=n[a];if(s&&i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++a<r;){var l=(i=n[a])[0],c=e[l],u=i[1];if(s&&i[2]){if(c===B&&!(l in e))return!1}else{var d=new vt;if(o)var p=o(c,u,l,e,t,d);if(!(p===B?sn(u,c,3,o,d):p))return!1}}return!0}function bn(e){return!(!Fa(e)||function(e){return!!Tr&&Tr in e}(e))&&(Oa(e)?$r:at).test(da(e))}function wn(e){return"function"==typeof e?e:null==e?cr:"object"==typeof e?Di(e)?Sn(e[0],e[1]):Ln(e):mr(e)}function _n(e){if(!ta(e))return as(e);var t=[];for(var n in _r(e))Dr.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kn(e){if(!Fa(e))return function(e){var t=[];if(null!=e)for(var n in _r(e))t.push(n);return t}(e);var t=ta(e),n=[];for(var o in e)("constructor"!=o||!t&&Dr.call(e,o))&&n.push(o);return n}function In(e,t){return e<t}function Cn(e,t){var n=-1,o=Ma(e)?gr(e.length):[];return js(e,(function(e,a,r){o[++n]=t(e,a,r)})),o}function Ln(e){var t=Go(e);return 1==t.length&&t[0][2]?oa(t[0][0],t[0][1]):function(n){return n===e||fn(n,e,t)}}function Sn(e,t){return Xo(e)&&na(t)?oa(ua(e),t):function(n){var o=Xa(n,e);return o===B&&o===t?er(n,e):sn(t,o,3)}}function An(e,t,n,o,a){e!==t&&Ds(t,(function(r,s){if(a||(a=new vt),Fa(r))!function(e,t,n,o,a,r,s){var i=sa(e,n),l=sa(t,n),c=s.get(l);if(c)return kt(e,n,c),B;var u=r?r(i,l,n+"",e,t,s):B,d=u===B;if(d){var p=Di(l),m=!p&&Ti(l),f=!p&&!m&&Oi(l);u=l,p||m||f?Di(i)?u=i:Ha(i)?u=uo(i):m?(d=!1,u=ao(l,!0)):f?(d=!1,u=so(l,!0)):u=[]:Va(l)||Ri(l)?(u=i,Ri(i)?u=Ka(i):Fa(i)&&!Oa(i)||(u=Jo(l))):d=!1}d&&(s.set(l,u),a(u,l,o,r,s),s.delete(l)),kt(e,n,u)}(e,t,s,n,An,o,a);else{var i=o?o(sa(e,s),r,s+"",e,t,a):B;i===B&&(i=r),kt(e,s,i)}}),nr)}function jn(e,t){var n=e.length;if(n)return Ko(t+=t<0?n:0,n)?e[t]:B}function Rn(e,t,n){t=t.length?l(t,(function(e){return Di(e)?function(t){return Vt(t,1===e.length?e[0]:e)}:e})):[cr];var o=-1;return t=l(t,S(Yo())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Cn(e,(function(e,n,a){return{criteria:l(t,(function(t){return t(e)})),index:++o,value:e}})),(function(e,t){return function(e,t,n){for(var o=-1,a=e.criteria,r=t.criteria,s=a.length,i=n.length;++o<s;){var l=io(a[o],r[o]);if(l)return o>=i?l:l*("desc"==n[o]?-1:1)}return e.index-t.index}(e,t,n)}))}function Dn(e,t,n){for(var o=-1,a=t.length,r={};++o<a;){var s=t[o],i=Vt(e,s);n(i,s)&&xn(r,no(s,e),i)}return r}function Pn(e,t,n,o){var a=o?v:g,r=-1,s=t.length,i=e;for(e===t&&(t=uo(t)),n&&(i=l(e,S(n)));++r<s;)for(var c=0,u=t[r],d=n?n(u):u;(c=a(i,d,c,o))>-1;)i!==e&&Yr.call(i,c,1),Yr.call(e,c,1);return e}function Tn(e,t){for(var n=e?t.length:0,o=n-1;n--;){var a=t[n];if(n==o||a!==r){var r=a;Ko(a)?Yr.call(e,a,1):qn(e,a)}}return e}function Un(e,t){return e+Xr(cs()*(t-e+1))}function Mn(e,t){var n="";if(!e||t<1||t>Z)return n;do{t%2&&(n+=e),(t=Xr(t/2))&&(e+=e)}while(t);return n}function Hn(e,t){return Ys(aa(e,t,cr),e+"")}function $n(e){return bt(ar(e))}function On(e,t){var n=ar(e);return ca(n,Rt(t,0,n.length))}function xn(e,t,n,o){if(!Fa(e))return e;for(var a=-1,r=(t=no(t,e)).length,s=r-1,i=e;null!=i&&++a<r;){var l=ua(t[a]),c=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(a!=s){var u=i[l];(c=o?o(u,l,i):B)===B&&(c=Fa(u)?u:Ko(t[a+1])?[]:{})}It(i,l,c),i=i[l]}return e}function En(e){return ca(ar(e))}function Fn(e,t,n){var o=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(n=n>a?a:n)<0&&(n+=a),a=t>n?0:n-t>>>0,t>>>=0;for(var r=gr(a);++o<a;)r[o]=e[o+t];return r}function Nn(e,t){var n;return js(e,(function(e,o,a){return!(n=t(e,o,a))})),!!n}function Bn(e,t,n){var o=0,a=null==e?o:e.length;if("number"==typeof t&&t==t&&a<=ne){for(;o<a;){var r=o+a>>>1,s=e[r];null!==s&&!za(s)&&(n?s<=t:s<t)?o=r+1:a=r}return a}return Vn(e,t,cr,n)}function Vn(e,t,n,o){var a=0,r=null==e?0:e.length;if(0===r)return 0;for(var s=(t=n(t))!=t,i=null===t,l=za(t),c=t===B;a<r;){var u=Xr((a+r)/2),d=n(e[u]),p=d!==B,m=null===d,f=d==d,h=za(d);if(s)var g=o||f;else g=c?f&&(o||p):i?f&&p&&(o||!m):l?f&&p&&!m&&(o||!h):!m&&!h&&(o?d<=t:d<t);g?a=u+1:r=u}return ss(r,te)}function Yn(e,t){for(var n=-1,o=e.length,a=0,r=[];++n<o;){var s=e[n],i=t?t(s):s;if(!n||!Ua(i,l)){var l=i;r[a++]=0===s?0:s}}return r}function zn(e){return"number"==typeof e?e:za(e)?X:+e}function Gn(e){if("string"==typeof e)return e;if(Di(e))return l(e,Gn)+"";if(za(e))return Ss?Ss.call(e):"";var t=e+"";return"0"==t&&1/e==-K?"-0":t}function Wn(e,t,n){var o=-1,a=s,r=e.length,l=!0,c=[],u=c;if(n)l=!1,a=i;else if(r>=200){var d=t?null:$s(e);if(d)return O(d);l=!1,a=j,u=new gt}else u=t?[]:c;e:for(;++o<r;){var p=e[o],m=t?t(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=u.length;f--;)if(u[f]===m)continue e;t&&u.push(m),c.push(p)}else a(u,m,n)||(u!==c&&u.push(m),c.push(p))}return c}function qn(e,t){return null==(e=ra(e,t=no(t,e)))||delete e[ua(ya(t))]}function Jn(e,t,n,o){return xn(e,t,n(Vt(e,t)),o)}function Qn(e,t,n,o){for(var a=e.length,r=o?a:-1;(o?r--:++r<a)&&t(e[r],r,e););return n?Fn(e,o?0:r,o?r+1:a):Fn(e,o?r+1:0,o?a:r)}function Kn(e,t){var n=e;return n instanceof pt&&(n=n.value()),u(t,(function(e,t){return t.func.apply(t.thisArg,c([e],t.args))}),n)}function Zn(e,t,n){var o=e.length;if(o<2)return o?Wn(e[0]):[];for(var a=-1,r=gr(o);++a<o;)for(var s=e[a],i=-1;++i<o;)i!=a&&(r[a]=Ut(r[a]||s,e[i],t,n));return Wn(Ot(r,1),t,n)}function Xn(e,t,n){for(var o=-1,a=e.length,r=t.length,s={};++o<a;)n(s,e[o],o<r?t[o]:B);return s}function eo(e){return Ha(e)?e:[]}function to(e){return"function"==typeof e?e:cr}function no(e,t){return Di(e)?e:Xo(e,t)?[e]:zs(Za(e))}function oo(e,t,n){var o=e.length;return n=n===B?o:n,!t&&n>=o?e:Fn(e,t,n)}function ao(e,t){if(t)return e.slice();var n=e.length,o=Fr?Fr(n):new e.constructor(n);return e.copy(o),o}function ro(e){var t=new e.constructor(e.byteLength);return new Er(t).set(new Er(e)),t}function so(e,t){return new e.constructor(t?ro(e.buffer):e.buffer,e.byteOffset,e.length)}function io(e,t){if(e!==t){var n=e!==B,o=null===e,a=e==e,r=za(e),s=t!==B,i=null===t,l=t==t,c=za(t);if(!i&&!c&&!r&&e>t||r&&s&&l&&!i&&!c||o&&s&&l||!n&&l||!a)return 1;if(!o&&!r&&!c&&e<t||c&&n&&a&&!o&&!r||i&&n&&a||!s&&a||!l)return-1}return 0}function lo(e,t,n,o){for(var a=-1,r=e.length,s=n.length,i=-1,l=t.length,c=rs(r-s,0),u=gr(l+c),d=!o;++i<l;)u[i]=t[i];for(;++a<s;)(d||a<r)&&(u[n[a]]=e[a]);for(;c--;)u[i++]=e[a++];return u}function co(e,t,n,o){for(var a=-1,r=e.length,s=-1,i=n.length,l=-1,c=t.length,u=rs(r-i,0),d=gr(u+c),p=!o;++a<u;)d[a]=e[a];for(var m=a;++l<c;)d[m+l]=t[l];for(;++s<i;)(p||a<r)&&(d[m+n[s]]=e[a++]);return d}function uo(e,t){var n=-1,o=e.length;for(t||(t=gr(o));++n<o;)t[n]=e[n];return t}function po(e,t,n,o){var a=!n;n||(n={});for(var r=-1,s=t.length;++r<s;){var i=t[r],l=o?o(n[i],e[i],i,n,e):B;l===B&&(l=e[i]),a?At(n,i,l):It(n,i,l)}return n}function mo(e,n){return function(o,a){var r=Di(o)?t:Lt,s=n?n():{};return r(o,e,Yo(a,2),s)}}function fo(e){return Hn((function(t,n){var o=-1,a=n.length,r=a>1?n[a-1]:B,s=a>2?n[2]:B;for(r=e.length>3&&"function"==typeof r?(a--,r):B,s&&Zo(n[0],n[1],s)&&(r=a<3?B:r,a=1),t=_r(t);++o<a;){var i=n[o];i&&e(t,i,o,r)}return t}))}function ho(e,t){return function(n,o){if(null==n)return n;if(!Ma(n))return e(n,o);for(var a=n.length,r=t?a:-1,s=_r(n);(t?r--:++r<a)&&!1!==o(s[r],r,s););return n}}function go(e){return function(t,n,o){for(var a=-1,r=_r(t),s=o(t),i=s.length;i--;){var l=s[e?i:++a];if(!1===n(r[l],l,r))break}return t}}function vo(e){return function(t){var n=T(t=Za(t))?E(t):B,o=n?n[0]:t.charAt(0),a=n?oo(n,1).join(""):t.slice(1);return o[e]()+a}}function yo(e){return function(t){return u(ir(sr(t).replace(Ft,"")),e,"")}}function bo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=As(e.prototype),o=e.apply(n,t);return Fa(o)?o:n}}function wo(t,n,o){var a=bo(t);return function r(){for(var s=arguments.length,i=gr(s),l=s,c=Vo(r);l--;)i[l]=arguments[l];var u=s<3&&i[0]!==c&&i[s-1]!==c?[]:$(i,c);return(s-=u.length)<o?Po(t,n,Io,r.placeholder,B,i,u,B,B,o-s):e(this&&this!==tn&&this instanceof r?a:t,this,i)}}function _o(e){return function(t,n,o){var a=_r(t);if(!Ma(t)){var r=Yo(n,3);t=tr(t),n=function(e){return r(a[e],e,a)}}var s=e(t,n,o);return s>-1?a[r?t[s]:s]:B}}function ko(e){return Eo((function(t){var n=t.length,o=n,a=dt.prototype.thru;for(e&&t.reverse();o--;){var r=t[o];if("function"!=typeof r)throw new Cr(V);if(a&&!s&&"wrapper"==Bo(r))var s=new dt([],!0)}for(o=s?o:n;++o<n;){var i=Bo(r=t[o]),l="wrapper"==i?Os(r):B;s=l&&ea(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?s[Bo(l[0])].apply(s,l[3]):1==r.length&&ea(r)?s[i]():s.thru(r)}return function(){var e=arguments,o=e[0];if(s&&1==e.length&&Di(o))return s.plant(o).value();for(var a=0,r=n?t[a].apply(this,e):o;++a<n;)r=t[a].call(this,r);return r}}))}function Io(e,t,n,o,a,r,s,i,l,c){var u=t&J,d=1&t,p=2&t,m=24&t,f=512&t,h=p?B:bo(e);return function g(){for(var v=arguments.length,y=gr(v),b=v;b--;)y[b]=arguments[b];if(m)var w=Vo(g),_=function(e,t){for(var n=e.length,o=0;n--;)e[n]===t&&++o;return o}(y,w);if(o&&(y=lo(y,o,a,m)),r&&(y=co(y,r,s,m)),v-=_,m&&v<c)return Po(e,t,Io,g.placeholder,n,y,$(y,w),i,l,c-v);var k=d?n:this,I=p?k[e]:e;return v=y.length,i?y=function(e,t){for(var n=e.length,o=ss(t.length,n),a=uo(e);o--;){var r=t[o];e[o]=Ko(r,n)?a[r]:B}return e}(y,i):f&&v>1&&y.reverse(),u&&l<v&&(y.length=l),this&&this!==tn&&this instanceof g&&(I=h||bo(I)),I.apply(k,y)}}function Co(e,t){return function(n,o){return function(e,t,n,o){return xt(e,(function(e,a,r){t(o,n(e),a,r)})),o}(n,e,t(o),{})}}function Lo(e,t){return function(n,o){var a;if(n===B&&o===B)return t;if(n!==B&&(a=n),o!==B){if(a===B)return o;"string"==typeof n||"string"==typeof o?(n=Gn(n),o=Gn(o)):(n=zn(n),o=zn(o)),a=e(n,o)}return a}}function So(t){return Eo((function(n){return n=l(n,S(Yo())),Hn((function(o){var a=this;return t(n,(function(t){return e(t,a,o)}))}))}))}function Ao(e,t){var n=(t=t===B?" ":Gn(t)).length;if(n<2)return n?Mn(t,e):t;var o=Mn(t,Zr(e/x(t)));return T(t)?oo(E(o),0,e).join(""):o.slice(0,e)}function jo(t,n,o,a){var r=1&n,s=bo(t);return function n(){for(var i=-1,l=arguments.length,c=-1,u=a.length,d=gr(u+l),p=this&&this!==tn&&this instanceof n?s:t;++c<u;)d[c]=a[c];for(;l--;)d[c++]=arguments[++i];return e(p,r?o:this,d)}}function Ro(e){return function(t,n,o){return o&&"number"!=typeof o&&Zo(t,n,o)&&(n=o=B),t=Wa(t),n===B?(n=t,t=0):n=Wa(n),function(e,t,n,o){for(var a=-1,r=rs(Zr((t-e)/(n||1)),0),s=gr(r);r--;)s[o?r:++a]=e,e+=n;return s}(t,n,o=o===B?t<n?1:-1:Wa(o),e)}}function Do(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Qa(t),n=Qa(n)),e(t,n)}}function Po(e,t,n,o,a,r,s,i,l,c){var u=8&t;t|=u?W:q,4&(t&=~(u?q:W))||(t&=-4);var d=[e,t,a,u?r:B,u?s:B,u?B:r,u?B:s,i,l,c],p=n.apply(B,d);return ea(e)&&Bs(p,d),p.placeholder=o,ia(p,e,t)}function To(e){var t=wr[e];return function(e,n){if(e=Qa(e),(n=null==n?0:ss(qa(n),292))&&ns(e)){var o=(Za(e)+"e").split("e");return+((o=(Za(t(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return t(e)}}function Uo(e){return function(t){var n=Fs(t);return n==de?M(t):n==ge?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return l(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Mo(e,t,n,o,a,r,s,i){var l=2&t;if(!l&&"function"!=typeof e)throw new Cr(V);var c=o?o.length:0;if(c||(t&=-97,o=a=B),s=s===B?s:rs(qa(s),0),i=i===B?i:qa(i),c-=a?a.length:0,t&q){var u=o,d=a;o=a=B}var p=l?B:Os(e),m=[e,t,n,o,a,u,d,r,s,i];if(p&&function(e,t){var n=e[1],o=t[1],a=n|o,r=a<131,s=o==J&&8==n||o==J&&n==Q&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!r&&!s)return e;1&o&&(e[2]=t[2],a|=1&n?0:4);var i=t[3];if(i){var l=e[3];e[3]=l?lo(l,i,t[4]):i,e[4]=l?$(e[3],z):t[4]}i=t[5],i&&(l=e[5],e[5]=l?co(l,i,t[6]):i,e[6]=l?$(e[5],z):t[6]),i=t[7],i&&(e[7]=i),o&J&&(e[8]=null==e[8]?t[8]:ss(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=a}(m,p),e=m[0],t=m[1],n=m[2],o=m[3],a=m[4],!(i=m[9]=m[9]===B?l?0:e.length:rs(m[9]-c,0))&&24&t&&(t&=-25),t&&1!=t)f=8==t||t==G?wo(e,t,i):t!=W&&33!=t||a.length?Io.apply(B,m):jo(e,t,n,o);else var f=function(e,t,n){var o=1&t,a=bo(e);return function t(){return(this&&this!==tn&&this instanceof t?a:e).apply(o?n:this,arguments)}}(e,t,n);return ia((p?Ts:Bs)(f,m),e,t)}function Ho(e,t,n,o){return e===B||Ua(e,Ar[n])&&!Dr.call(o,n)?t:e}function $o(e,t,n,o,a,r){return Fa(e)&&Fa(t)&&(r.set(t,e),An(e,t,B,$o,r),r.delete(t)),e}function Oo(e){return Va(e)?B:e}function xo(e,t,n,o,a,r){var s=1&n,i=e.length,l=t.length;if(i!=l&&!(s&&l>i))return!1;var c=r.get(e),u=r.get(t);if(c&&u)return c==t&&u==e;var d=-1,m=!0,f=2&n?new gt:B;for(r.set(e,t),r.set(t,e);++d<i;){var h=e[d],g=t[d];if(o)var v=s?o(g,h,d,t,e,r):o(h,g,d,e,t,r);if(v!==B){if(v)continue;m=!1;break}if(f){if(!p(t,(function(e,t){if(!j(f,t)&&(h===e||a(h,e,n,o,r)))return f.push(t)}))){m=!1;break}}else if(h!==g&&!a(h,g,n,o,r)){m=!1;break}}return r.delete(e),r.delete(t),m}function Eo(e){return Ys(aa(e,B,ga),e+"")}function Fo(e){return Yt(e,tr,xs)}function No(e){return Yt(e,nr,Es)}function Bo(e){for(var t=e.name+"",n=ys[t],o=Dr.call(ys,t)?n.length:0;o--;){var a=n[o],r=a.func;if(null==r||r==e)return a.name}return t}function Vo(e){return(Dr.call(Ke,"placeholder")?Ke:e).placeholder}function Yo(){var e=Ke.iteratee||ur;return e=e===ur?wn:e,arguments.length?e(arguments[0],arguments[1]):e}function zo(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function Go(e){for(var t=tr(e),n=t.length;n--;){var o=t[n],a=e[o];t[n]=[o,a,na(a)]}return t}function Wo(e,t){var n=function(e,t){return null==e?B:e[t]}(e,t);return bn(n)?n:B}function qo(e,t,n){for(var o=-1,a=(t=no(t,e)).length,r=!1;++o<a;){var s=ua(t[o]);if(!(r=null!=e&&n(e,s)))break;e=e[s]}return r||++o!=a?r:!!(a=null==e?0:e.length)&&Ea(a)&&Ko(s,a)&&(Di(e)||Ri(e))}function Jo(e){return"function"!=typeof e.constructor||ta(e)?{}:As(Nr(e))}function Qo(e){return Di(e)||Ri(e)||!!(zr&&e&&e[zr])}function Ko(e,t){var n=typeof e;return!!(t=null==t?Z:t)&&("number"==n||"symbol"!=n&&st.test(e))&&e>-1&&e%1==0&&e<t}function Zo(e,t,n){if(!Fa(n))return!1;var o=typeof t;return!!("number"==o?Ma(n)&&Ko(t,n.length):"string"==o&&t in n)&&Ua(n[t],e)}function Xo(e,t){if(Di(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!za(e))||Be.test(e)||!Ne.test(e)||null!=t&&e in _r(t)}function ea(e){var t=Bo(e),n=Ke[t];if("function"!=typeof n||!(t in pt.prototype))return!1;if(e===n)return!0;var o=Os(n);return!!o&&e===o[0]}function ta(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ar)}function na(e){return e==e&&!Fa(e)}function oa(e,t){return function(n){return null!=n&&n[e]===t&&(t!==B||e in _r(n))}}function aa(t,n,o){return n=rs(n===B?t.length-1:n,0),function(){for(var a=arguments,r=-1,s=rs(a.length-n,0),i=gr(s);++r<s;)i[r]=a[n+r];r=-1;for(var l=gr(n+1);++r<n;)l[r]=a[r];return l[n]=o(i),e(t,this,l)}}function ra(e,t){return t.length<2?e:Vt(e,Fn(t,0,-1))}function sa(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function ia(e,t,n){var o=t+"";return Ys(e,function(e,t){var n=t.length;if(!n)return e;var o=n-1;return t[o]=(n>1?"& ":"")+t[o],t=t.join(n>2?", ":" "),e.replace(qe,"{\n/* [wrapped with "+t+"] */\n")}(o,pa(function(e){var t=e.match(Je);return t?t[1].split(Qe):[]}(o),n)))}function la(e){var t=0,n=0;return function(){var o=is(),a=16-(o-n);if(n=o,a>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(B,arguments)}}function ca(e,t){var n=-1,o=e.length,a=o-1;for(t=t===B?o:t;++n<t;){var r=Un(n,a),s=e[r];e[r]=e[n],e[n]=s}return e.length=t,e}function ua(e){if("string"==typeof e||za(e))return e;var t=e+"";return"0"==t&&1/e==-K?"-0":t}function da(e){if(null!=e){try{return Rr.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function pa(e,t){return n(oe,(function(n){var o="_."+n[0];t&n[1]&&!s(e,o)&&e.push(o)})),e.sort()}function ma(e){if(e instanceof pt)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=uo(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function fa(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=rs(o+a,0)),h(e,Yo(t,3),a)}function ha(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o-1;return n!==B&&(a=qa(n),a=n<0?rs(o+a,0):ss(a,o-1)),h(e,Yo(t,3),a,!0)}function ga(e){return null!=e&&e.length?Ot(e,1):[]}function va(e){return e&&e.length?e[0]:B}function ya(e){var t=null==e?0:e.length;return t?e[t-1]:B}function ba(e,t){return e&&e.length&&t&&t.length?Pn(e,t):e}function wa(e){return null==e?e:us.call(e)}function _a(e){if(!e||!e.length)return[];var t=0;return e=r(e,(function(e){if(Ha(e))return t=rs(e.length,t),!0})),C(t,(function(t){return l(e,w(t))}))}function ka(t,n){if(!t||!t.length)return[];var o=_a(t);return null==n?o:l(o,(function(t){return e(n,B,t)}))}function Ia(e){var t=Ke(e);return t.__chain__=!0,t}function Ca(e,t){return t(e)}function La(e,t){return(Di(e)?n:js)(e,Yo(t,3))}function Sa(e,t){return(Di(e)?o:Rs)(e,Yo(t,3))}function Aa(e,t){return(Di(e)?l:Cn)(e,Yo(t,3))}function ja(e,t,n){return t=n?B:t,t=e&&null==t?e.length:t,Mo(e,J,B,B,B,B,t)}function Ra(e,t){var n;if("function"!=typeof t)throw new Cr(V);return e=qa(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=B),n}}function Da(e,t,n){function o(t){var n=l,o=c;return l=c=B,f=t,d=e.apply(o,n)}function a(e){var n=e-m;return m===B||n>=t||n<0||g&&e-f>=u}function r(){var e=yi();return a(e)?s(e):(p=Vs(r,function(e){var n=t-(e-m);return g?ss(n,u-(e-f)):n}(e)),B)}function s(e){return p=B,v&&l?o(e):(l=c=B,d)}function i(){var e=yi(),n=a(e);if(l=arguments,c=this,m=e,n){if(p===B)return function(e){return f=e,p=Vs(r,t),h?o(e):d}(m);if(g)return Hs(p),p=Vs(r,t),o(m)}return p===B&&(p=Vs(r,t)),d}var l,c,u,d,p,m,f=0,h=!1,g=!1,v=!0;if("function"!=typeof e)throw new Cr(V);return t=Qa(t)||0,Fa(n)&&(h=!!n.leading,u=(g="maxWait"in n)?rs(Qa(n.maxWait)||0,t):u,v="trailing"in n?!!n.trailing:v),i.cancel=function(){p!==B&&Hs(p),f=0,l=m=c=p=B},i.flush=function(){return p===B?d:s(yi())},i}function Pa(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Cr(V);var n=function(){var o=arguments,a=t?t.apply(this,o):o[0],r=n.cache;if(r.has(a))return r.get(a);var s=e.apply(this,o);return n.cache=r.set(a,s)||r,s};return n.cache=new(Pa.Cache||ht),n}function Ta(e){if("function"!=typeof e)throw new Cr(V);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Ua(e,t){return e===t||e!=e&&t!=t}function Ma(e){return null!=e&&Ea(e.length)&&!Oa(e)}function Ha(e){return Na(e)&&Ma(e)}function $a(e){if(!Na(e))return!1;var t=zt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Va(e)}function Oa(e){if(!Fa(e))return!1;var t=zt(e);return t==ce||t==ue||"[object AsyncFunction]"==t||"[object Proxy]"==t}function xa(e){return"number"==typeof e&&e==qa(e)}function Ea(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Z}function Fa(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Na(e){return null!=e&&"object"==typeof e}function Ba(e){return"number"==typeof e||Na(e)&&zt(e)==pe}function Va(e){if(!Na(e)||zt(e)!=me)return!1;var t=Nr(e);if(null===t)return!0;var n=Dr.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Rr.call(n)==Mr}function Ya(e){return"string"==typeof e||!Di(e)&&Na(e)&&zt(e)==ve}function za(e){return"symbol"==typeof e||Na(e)&&zt(e)==ye}function Ga(e){if(!e)return[];if(Ma(e))return Ya(e)?E(e):uo(e);if(Gr&&e[Gr])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Gr]());var t=Fs(e);return(t==de?M:t==ge?O:ar)(e)}function Wa(e){return e?(e=Qa(e))===K||e===-K?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function qa(e){var t=Wa(e),n=t%1;return t==t?n?t-n:t:0}function Ja(e){return e?Rt(qa(e),0,ee):0}function Qa(e){if("number"==typeof e)return e;if(za(e))return X;if(Fa(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Fa(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=L(e);var n=ot.test(e);return n||rt.test(e)?Zt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Ka(e){return po(e,nr(e))}function Za(e){return null==e?"":Gn(e)}function Xa(e,t,n){var o=null==e?B:Vt(e,t);return o===B?n:o}function er(e,t){return null!=e&&qo(e,t,en)}function tr(e){return Ma(e)?yt(e):_n(e)}function nr(e){return Ma(e)?yt(e,!0):kn(e)}function or(e,t){if(null==e)return{};var n=l(No(e),(function(e){return[e]}));return t=Yo(t),Dn(e,n,(function(e,n){return t(e,n[0])}))}function ar(e){return null==e?[]:A(e,tr(e))}function rr(e){return cl(Za(e).toLowerCase())}function sr(e){return(e=Za(e))&&e.replace(it,hn).replace(Nt,"")}function ir(e,t,n){return e=Za(e),(t=n?B:t)===B?U(e)?N(e):m(e):e.match(t)||[]}function lr(e){return function(){return e}}function cr(e){return e}function ur(e){return wn("function"==typeof e?e:Dt(e,1))}function dr(e,t,o){var a=tr(t),r=Bt(t,a);null!=o||Fa(t)&&(r.length||!a.length)||(o=t,t=e,e=this,r=Bt(t,tr(t)));var s=!(Fa(o)&&"chain"in o&&!o.chain),i=Oa(e);return n(r,(function(n){var o=t[n];e[n]=o,i&&(e.prototype[n]=function(){var t=this.__chain__;if(s||t){var n=e(this.__wrapped__);return(n.__actions__=uo(this.__actions__)).push({func:o,args:arguments,thisArg:e}),n.__chain__=t,n}return o.apply(e,c([this.value()],arguments))})})),e}function pr(){}function mr(e){return Xo(e)?w(ua(e)):function(e){return function(t){return Vt(t,e)}}(e)}function fr(){return[]}function hr(){return!1}var gr=(We=null==We?tn:yn.defaults(tn.Object(),We,yn.pick(tn,Gt))).Array,vr=We.Date,yr=We.Error,br=We.Function,wr=We.Math,_r=We.Object,kr=We.RegExp,Ir=We.String,Cr=We.TypeError,Lr=gr.prototype,Sr=br.prototype,Ar=_r.prototype,jr=We["__core-js_shared__"],Rr=Sr.toString,Dr=Ar.hasOwnProperty,Pr=0,Tr=function(){var e=/[^.]+$/.exec(jr&&jr.keys&&jr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ur=Ar.toString,Mr=Rr.call(_r),Hr=tn._,$r=kr("^"+Rr.call(Dr).replace(Ye,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Or=an?We.Buffer:B,xr=We.Symbol,Er=We.Uint8Array,Fr=Or?Or.allocUnsafe:B,Nr=H(_r.getPrototypeOf,_r),Br=_r.create,Vr=Ar.propertyIsEnumerable,Yr=Lr.splice,zr=xr?xr.isConcatSpreadable:B,Gr=xr?xr.iterator:B,Wr=xr?xr.toStringTag:B,qr=function(){try{var e=Wo(_r,"defineProperty");return e({},"",{}),e}catch(e){}}(),Jr=We.clearTimeout!==tn.clearTimeout&&We.clearTimeout,Qr=vr&&vr.now!==tn.Date.now&&vr.now,Kr=We.setTimeout!==tn.setTimeout&&We.setTimeout,Zr=wr.ceil,Xr=wr.floor,es=_r.getOwnPropertySymbols,ts=Or?Or.isBuffer:B,ns=We.isFinite,os=Lr.join,as=H(_r.keys,_r),rs=wr.max,ss=wr.min,is=vr.now,ls=We.parseInt,cs=wr.random,us=Lr.reverse,ds=Wo(We,"DataView"),ps=Wo(We,"Map"),ms=Wo(We,"Promise"),fs=Wo(We,"Set"),hs=Wo(We,"WeakMap"),gs=Wo(_r,"create"),vs=hs&&new hs,ys={},bs=da(ds),ws=da(ps),_s=da(ms),ks=da(fs),Is=da(hs),Cs=xr?xr.prototype:B,Ls=Cs?Cs.valueOf:B,Ss=Cs?Cs.toString:B,As=function(){function e(){}return function(t){if(!Fa(t))return{};if(Br)return Br(t);e.prototype=t;var n=new e;return e.prototype=B,n}}();Ke.templateSettings={escape:xe,evaluate:Ee,interpolate:Fe,variable:"",imports:{_:Ke}},Ke.prototype=ut.prototype,Ke.prototype.constructor=Ke,dt.prototype=As(ut.prototype),dt.prototype.constructor=dt,pt.prototype=As(ut.prototype),pt.prototype.constructor=pt,mt.prototype.clear=function(){this.__data__=gs?gs(null):{},this.size=0},mt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},mt.prototype.get=function(e){var t=this.__data__;if(gs){var n=t[e];return n===Y?B:n}return Dr.call(t,e)?t[e]:B},mt.prototype.has=function(e){var t=this.__data__;return gs?t[e]!==B:Dr.call(t,e)},mt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=gs&&t===B?Y:t,this},ft.prototype.clear=function(){this.__data__=[],this.size=0},ft.prototype.delete=function(e){var t=this.__data__,n=Ct(t,e);return!(n<0||(n==t.length-1?t.pop():Yr.call(t,n,1),--this.size,0))},ft.prototype.get=function(e){var t=this.__data__,n=Ct(t,e);return n<0?B:t[n][1]},ft.prototype.has=function(e){return Ct(this.__data__,e)>-1},ft.prototype.set=function(e,t){var n=this.__data__,o=Ct(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this},ht.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(ps||ft),string:new mt}},ht.prototype.delete=function(e){var t=zo(this,e).delete(e);return this.size-=t?1:0,t},ht.prototype.get=function(e){return zo(this,e).get(e)},ht.prototype.has=function(e){return zo(this,e).has(e)},ht.prototype.set=function(e,t){var n=zo(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this},gt.prototype.add=gt.prototype.push=function(e){return this.__data__.set(e,Y),this},gt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new ft,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ft){var o=n.__data__;if(!ps||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new ht(o)}return n.set(e,t),this.size=n.size,this};var js=ho(xt),Rs=ho(Et,!0),Ds=go(),Ps=go(!0),Ts=vs?function(e,t){return vs.set(e,t),e}:cr,Us=qr?function(e,t){return qr(e,"toString",{configurable:!0,enumerable:!1,value:lr(t),writable:!0})}:cr,Ms=Hn,Hs=Jr||function(e){return tn.clearTimeout(e)},$s=fs&&1/O(new fs([,-0]))[1]==K?function(e){return new fs(e)}:pr,Os=vs?function(e){return vs.get(e)}:pr,xs=es?function(e){return null==e?[]:(e=_r(e),r(es(e),(function(t){return Vr.call(e,t)})))}:fr,Es=es?function(e){for(var t=[];e;)c(t,xs(e)),e=Nr(e);return t}:fr,Fs=zt;(ds&&Fs(new ds(new ArrayBuffer(1)))!=_e||ps&&Fs(new ps)!=de||ms&&Fs(ms.resolve())!=fe||fs&&Fs(new fs)!=ge||hs&&Fs(new hs)!=be)&&(Fs=function(e){var t=zt(e),n=t==me?e.constructor:B,o=n?da(n):"";if(o)switch(o){case bs:return _e;case ws:return de;case _s:return fe;case ks:return ge;case Is:return be}return t});var Ns=jr?Oa:hr,Bs=la(Ts),Vs=Kr||function(e,t){return tn.setTimeout(e,t)},Ys=la(Us),zs=function(e){var t=Pa(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ve,(function(e,n,o,a){t.push(o?a.replace(Xe,"$1"):n||e)})),t})),Gs=Hn((function(e,t){return Ha(e)?Ut(e,Ot(t,1,Ha,!0)):[]})),Ws=Hn((function(e,t){var n=ya(t);return Ha(n)&&(n=B),Ha(e)?Ut(e,Ot(t,1,Ha,!0),Yo(n,2)):[]})),qs=Hn((function(e,t){var n=ya(t);return Ha(n)&&(n=B),Ha(e)?Ut(e,Ot(t,1,Ha,!0),B,n):[]})),Js=Hn((function(e){var t=l(e,eo);return t.length&&t[0]===e[0]?nn(t):[]})),Qs=Hn((function(e){var t=ya(e),n=l(e,eo);return t===ya(n)?t=B:n.pop(),n.length&&n[0]===e[0]?nn(n,Yo(t,2)):[]})),Ks=Hn((function(e){var t=ya(e),n=l(e,eo);return(t="function"==typeof t?t:B)&&n.pop(),n.length&&n[0]===e[0]?nn(n,B,t):[]})),Zs=Hn(ba),Xs=Eo((function(e,t){var n=null==e?0:e.length,o=jt(e,t);return Tn(e,l(t,(function(e){return Ko(e,n)?+e:e})).sort(io)),o})),ei=Hn((function(e){return Wn(Ot(e,1,Ha,!0))})),ti=Hn((function(e){var t=ya(e);return Ha(t)&&(t=B),Wn(Ot(e,1,Ha,!0),Yo(t,2))})),ni=Hn((function(e){var t=ya(e);return t="function"==typeof t?t:B,Wn(Ot(e,1,Ha,!0),B,t)})),oi=Hn((function(e,t){return Ha(e)?Ut(e,t):[]})),ai=Hn((function(e){return Zn(r(e,Ha))})),ri=Hn((function(e){var t=ya(e);return Ha(t)&&(t=B),Zn(r(e,Ha),Yo(t,2))})),si=Hn((function(e){var t=ya(e);return t="function"==typeof t?t:B,Zn(r(e,Ha),B,t)})),ii=Hn(_a),li=Hn((function(e){var t=e.length,n=t>1?e[t-1]:B;return n="function"==typeof n?(e.pop(),n):B,ka(e,n)})),ci=Eo((function(e){var t=e.length,n=t?e[0]:0,o=this.__wrapped__,a=function(t){return jt(t,e)};return!(t>1||this.__actions__.length)&&o instanceof pt&&Ko(n)?((o=o.slice(n,+n+(t?1:0))).__actions__.push({func:Ca,args:[a],thisArg:B}),new dt(o,this.__chain__).thru((function(e){return t&&!e.length&&e.push(B),e}))):this.thru(a)})),ui=mo((function(e,t,n){Dr.call(e,n)?++e[n]:At(e,n,1)})),di=_o(fa),pi=_o(ha),mi=mo((function(e,t,n){Dr.call(e,n)?e[n].push(t):At(e,n,[t])})),fi=Hn((function(t,n,o){var a=-1,r="function"==typeof n,s=Ma(t)?gr(t.length):[];return js(t,(function(t){s[++a]=r?e(n,t,o):on(t,n,o)})),s})),hi=mo((function(e,t,n){At(e,n,t)})),gi=mo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),vi=Hn((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Zo(e,t[0],t[1])?t=[]:n>2&&Zo(t[0],t[1],t[2])&&(t=[t[0]]),Rn(e,Ot(t,1),[])})),yi=Qr||function(){return tn.Date.now()},bi=Hn((function(e,t,n){var o=1;if(n.length){var a=$(n,Vo(bi));o|=W}return Mo(e,o,t,n,a)})),wi=Hn((function(e,t,n){var o=3;if(n.length){var a=$(n,Vo(wi));o|=W}return Mo(t,o,e,n,a)})),_i=Hn((function(e,t){return Tt(e,1,t)})),ki=Hn((function(e,t,n){return Tt(e,Qa(t)||0,n)}));Pa.Cache=ht;var Ii=Ms((function(t,n){var o=(n=1==n.length&&Di(n[0])?l(n[0],S(Yo())):l(Ot(n,1),S(Yo()))).length;return Hn((function(a){for(var r=-1,s=ss(a.length,o);++r<s;)a[r]=n[r].call(this,a[r]);return e(t,this,a)}))})),Ci=Hn((function(e,t){return Mo(e,W,B,t,$(t,Vo(Ci)))})),Li=Hn((function(e,t){return Mo(e,q,B,t,$(t,Vo(Li)))})),Si=Eo((function(e,t){return Mo(e,Q,B,B,B,t)})),Ai=Do(Qt),ji=Do((function(e,t){return e>=t})),Ri=rn(function(){return arguments}())?rn:function(e){return Na(e)&&Dr.call(e,"callee")&&!Vr.call(e,"callee")},Di=gr.isArray,Pi=ln?S(ln):function(e){return Na(e)&&zt(e)==we},Ti=ts||hr,Ui=cn?S(cn):function(e){return Na(e)&&zt(e)==ie},Mi=un?S(un):function(e){return Na(e)&&Fs(e)==de},Hi=dn?S(dn):function(e){return Na(e)&&zt(e)==he},$i=pn?S(pn):function(e){return Na(e)&&Fs(e)==ge},Oi=mn?S(mn):function(e){return Na(e)&&Ea(e.length)&&!!qt[zt(e)]},xi=Do(In),Ei=Do((function(e,t){return e<=t})),Fi=fo((function(e,t){if(ta(t)||Ma(t))return po(t,tr(t),e),B;for(var n in t)Dr.call(t,n)&&It(e,n,t[n])})),Ni=fo((function(e,t){po(t,nr(t),e)})),Bi=fo((function(e,t,n,o){po(t,nr(t),e,o)})),Vi=fo((function(e,t,n,o){po(t,tr(t),e,o)})),Yi=Eo(jt),zi=Hn((function(e,t){e=_r(e);var n=-1,o=t.length,a=o>2?t[2]:B;for(a&&Zo(t[0],t[1],a)&&(o=1);++n<o;)for(var r=t[n],s=nr(r),i=-1,l=s.length;++i<l;){var c=s[i],u=e[c];(u===B||Ua(u,Ar[c])&&!Dr.call(e,c))&&(e[c]=r[c])}return e})),Gi=Hn((function(t){return t.push(B,$o),e(Ki,B,t)})),Wi=Co((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ur.call(t)),e[t]=n}),lr(cr)),qi=Co((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ur.call(t)),Dr.call(e,t)?e[t].push(n):e[t]=[n]}),Yo),Ji=Hn(on),Qi=fo((function(e,t,n){An(e,t,n)})),Ki=fo((function(e,t,n,o){An(e,t,n,o)})),Zi=Eo((function(e,t){var n={};if(null==e)return n;var o=!1;t=l(t,(function(t){return t=no(t,e),o||(o=t.length>1),t})),po(e,No(e),n),o&&(n=Dt(n,7,Oo));for(var a=t.length;a--;)qn(n,t[a]);return n})),Xi=Eo((function(e,t){return null==e?{}:function(e,t){return Dn(e,t,(function(t,n){return er(e,n)}))}(e,t)})),el=Uo(tr),tl=Uo(nr),nl=yo((function(e,t,n){return t=t.toLowerCase(),e+(n?rr(t):t)})),ol=yo((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),al=yo((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),rl=vo("toLowerCase"),sl=yo((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),il=yo((function(e,t,n){return e+(n?" ":"")+cl(t)})),ll=yo((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),cl=vo("toUpperCase"),ul=Hn((function(t,n){try{return e(t,B,n)}catch(e){return $a(e)?e:new yr(e)}})),dl=Eo((function(e,t){return n(t,(function(t){t=ua(t),At(e,t,bi(e[t],e))})),e})),pl=ko(),ml=ko(!0),fl=Hn((function(e,t){return function(n){return on(n,e,t)}})),hl=Hn((function(e,t){return function(n){return on(e,n,t)}})),gl=So(l),vl=So(a),yl=So(p),bl=Ro(),wl=Ro(!0),_l=Lo((function(e,t){return e+t}),0),kl=To("ceil"),Il=Lo((function(e,t){return e/t}),1),Cl=To("floor"),Ll=Lo((function(e,t){return e*t}),1),Sl=To("round"),Al=Lo((function(e,t){return e-t}),0);return Ke.after=function(e,t){if("function"!=typeof t)throw new Cr(V);return e=qa(e),function(){if(--e<1)return t.apply(this,arguments)}},Ke.ary=ja,Ke.assign=Fi,Ke.assignIn=Ni,Ke.assignInWith=Bi,Ke.assignWith=Vi,Ke.at=Yi,Ke.before=Ra,Ke.bind=bi,Ke.bindAll=dl,Ke.bindKey=wi,Ke.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Di(e)?e:[e]},Ke.chain=Ia,Ke.chunk=function(e,t,n){t=(n?Zo(e,t,n):t===B)?1:rs(qa(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var a=0,r=0,s=gr(Zr(o/t));a<o;)s[r++]=Fn(e,a,a+=t);return s},Ke.compact=function(e){for(var t=-1,n=null==e?0:e.length,o=0,a=[];++t<n;){var r=e[t];r&&(a[o++]=r)}return a},Ke.concat=function(){var e=arguments.length;if(!e)return[];for(var t=gr(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return c(Di(n)?uo(n):[n],Ot(t,1))},Ke.cond=function(t){var n=null==t?0:t.length,o=Yo();return t=n?l(t,(function(e){if("function"!=typeof e[1])throw new Cr(V);return[o(e[0]),e[1]]})):[],Hn((function(o){for(var a=-1;++a<n;){var r=t[a];if(e(r[0],this,o))return e(r[1],this,o)}}))},Ke.conforms=function(e){return function(e){var t=tr(e);return function(n){return Pt(n,e,t)}}(Dt(e,1))},Ke.constant=lr,Ke.countBy=ui,Ke.create=function(e,t){var n=As(e);return null==t?n:St(n,t)},Ke.curry=function e(t,n,o){var a=Mo(t,8,B,B,B,B,B,n=o?B:n);return a.placeholder=e.placeholder,a},Ke.curryRight=function e(t,n,o){var a=Mo(t,G,B,B,B,B,B,n=o?B:n);return a.placeholder=e.placeholder,a},Ke.debounce=Da,Ke.defaults=zi,Ke.defaultsDeep=Gi,Ke.defer=_i,Ke.delay=ki,Ke.difference=Gs,Ke.differenceBy=Ws,Ke.differenceWith=qs,Ke.drop=function(e,t,n){var o=null==e?0:e.length;return o?Fn(e,(t=n||t===B?1:qa(t))<0?0:t,o):[]},Ke.dropRight=function(e,t,n){var o=null==e?0:e.length;return o?Fn(e,0,(t=o-(t=n||t===B?1:qa(t)))<0?0:t):[]},Ke.dropRightWhile=function(e,t){return e&&e.length?Qn(e,Yo(t,3),!0,!0):[]},Ke.dropWhile=function(e,t){return e&&e.length?Qn(e,Yo(t,3),!0):[]},Ke.fill=function(e,t,n,o){var a=null==e?0:e.length;return a?(n&&"number"!=typeof n&&Zo(e,t,n)&&(n=0,o=a),function(e,t,n,o){var a=e.length;for((n=qa(n))<0&&(n=-n>a?0:a+n),(o=o===B||o>a?a:qa(o))<0&&(o+=a),o=n>o?0:Ja(o);n<o;)e[n++]=t;return e}(e,t,n,o)):[]},Ke.filter=function(e,t){return(Di(e)?r:$t)(e,Yo(t,3))},Ke.flatMap=function(e,t){return Ot(Aa(e,t),1)},Ke.flatMapDeep=function(e,t){return Ot(Aa(e,t),K)},Ke.flatMapDepth=function(e,t,n){return n=n===B?1:qa(n),Ot(Aa(e,t),n)},Ke.flatten=ga,Ke.flattenDeep=function(e){return null!=e&&e.length?Ot(e,K):[]},Ke.flattenDepth=function(e,t){return null!=e&&e.length?Ot(e,t=t===B?1:qa(t)):[]},Ke.flip=function(e){return Mo(e,512)},Ke.flow=pl,Ke.flowRight=ml,Ke.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,o={};++t<n;){var a=e[t];o[a[0]]=a[1]}return o},Ke.functions=function(e){return null==e?[]:Bt(e,tr(e))},Ke.functionsIn=function(e){return null==e?[]:Bt(e,nr(e))},Ke.groupBy=mi,Ke.initial=function(e){return null!=e&&e.length?Fn(e,0,-1):[]},Ke.intersection=Js,Ke.intersectionBy=Qs,Ke.intersectionWith=Ks,Ke.invert=Wi,Ke.invertBy=qi,Ke.invokeMap=fi,Ke.iteratee=ur,Ke.keyBy=hi,Ke.keys=tr,Ke.keysIn=nr,Ke.map=Aa,Ke.mapKeys=function(e,t){var n={};return t=Yo(t,3),xt(e,(function(e,o,a){At(n,t(e,o,a),e)})),n},Ke.mapValues=function(e,t){var n={};return t=Yo(t,3),xt(e,(function(e,o,a){At(n,o,t(e,o,a))})),n},Ke.matches=function(e){return Ln(Dt(e,1))},Ke.matchesProperty=function(e,t){return Sn(e,Dt(t,1))},Ke.memoize=Pa,Ke.merge=Qi,Ke.mergeWith=Ki,Ke.method=fl,Ke.methodOf=hl,Ke.mixin=dr,Ke.negate=Ta,Ke.nthArg=function(e){return e=qa(e),Hn((function(t){return jn(t,e)}))},Ke.omit=Zi,Ke.omitBy=function(e,t){return or(e,Ta(Yo(t)))},Ke.once=function(e){return Ra(2,e)},Ke.orderBy=function(e,t,n,o){return null==e?[]:(Di(t)||(t=null==t?[]:[t]),Di(n=o?B:n)||(n=null==n?[]:[n]),Rn(e,t,n))},Ke.over=gl,Ke.overArgs=Ii,Ke.overEvery=vl,Ke.overSome=yl,Ke.partial=Ci,Ke.partialRight=Li,Ke.partition=gi,Ke.pick=Xi,Ke.pickBy=or,Ke.property=mr,Ke.propertyOf=function(e){return function(t){return null==e?B:Vt(e,t)}},Ke.pull=Zs,Ke.pullAll=ba,Ke.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Pn(e,t,Yo(n,2)):e},Ke.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Pn(e,t,B,n):e},Ke.pullAt=Xs,Ke.range=bl,Ke.rangeRight=wl,Ke.rearg=Si,Ke.reject=function(e,t){return(Di(e)?r:$t)(e,Ta(Yo(t,3)))},Ke.remove=function(e,t){var n=[];if(!e||!e.length)return n;var o=-1,a=[],r=e.length;for(t=Yo(t,3);++o<r;){var s=e[o];t(s,o,e)&&(n.push(s),a.push(o))}return Tn(e,a),n},Ke.rest=function(e,t){if("function"!=typeof e)throw new Cr(V);return Hn(e,t=t===B?t:qa(t))},Ke.reverse=wa,Ke.sampleSize=function(e,t,n){return t=(n?Zo(e,t,n):t===B)?1:qa(t),(Di(e)?wt:On)(e,t)},Ke.set=function(e,t,n){return null==e?e:xn(e,t,n)},Ke.setWith=function(e,t,n,o){return o="function"==typeof o?o:B,null==e?e:xn(e,t,n,o)},Ke.shuffle=function(e){return(Di(e)?_t:En)(e)},Ke.slice=function(e,t,n){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Zo(e,t,n)?(t=0,n=o):(t=null==t?0:qa(t),n=n===B?o:qa(n)),Fn(e,t,n)):[]},Ke.sortBy=vi,Ke.sortedUniq=function(e){return e&&e.length?Yn(e):[]},Ke.sortedUniqBy=function(e,t){return e&&e.length?Yn(e,Yo(t,2)):[]},Ke.split=function(e,t,n){return n&&"number"!=typeof n&&Zo(e,t,n)&&(t=n=B),(n=n===B?ee:n>>>0)?(e=Za(e))&&("string"==typeof t||null!=t&&!Hi(t))&&(!(t=Gn(t))&&T(e))?oo(E(e),0,n):e.split(t,n):[]},Ke.spread=function(t,n){if("function"!=typeof t)throw new Cr(V);return n=null==n?0:rs(qa(n),0),Hn((function(o){var a=o[n],r=oo(o,0,n);return a&&c(r,a),e(t,this,r)}))},Ke.tail=function(e){var t=null==e?0:e.length;return t?Fn(e,1,t):[]},Ke.take=function(e,t,n){return e&&e.length?Fn(e,0,(t=n||t===B?1:qa(t))<0?0:t):[]},Ke.takeRight=function(e,t,n){var o=null==e?0:e.length;return o?Fn(e,(t=o-(t=n||t===B?1:qa(t)))<0?0:t,o):[]},Ke.takeRightWhile=function(e,t){return e&&e.length?Qn(e,Yo(t,3),!1,!0):[]},Ke.takeWhile=function(e,t){return e&&e.length?Qn(e,Yo(t,3)):[]},Ke.tap=function(e,t){return t(e),e},Ke.throttle=function(e,t,n){var o=!0,a=!0;if("function"!=typeof e)throw new Cr(V);return Fa(n)&&(o="leading"in n?!!n.leading:o,a="trailing"in n?!!n.trailing:a),Da(e,t,{leading:o,maxWait:t,trailing:a})},Ke.thru=Ca,Ke.toArray=Ga,Ke.toPairs=el,Ke.toPairsIn=tl,Ke.toPath=function(e){return Di(e)?l(e,ua):za(e)?[e]:uo(zs(Za(e)))},Ke.toPlainObject=Ka,Ke.transform=function(e,t,o){var a=Di(e),r=a||Ti(e)||Oi(e);if(t=Yo(t,4),null==o){var s=e&&e.constructor;o=r?a?new s:[]:Fa(e)&&Oa(s)?As(Nr(e)):{}}return(r?n:xt)(e,(function(e,n,a){return t(o,e,n,a)})),o},Ke.unary=function(e){return ja(e,1)},Ke.union=ei,Ke.unionBy=ti,Ke.unionWith=ni,Ke.uniq=function(e){return e&&e.length?Wn(e):[]},Ke.uniqBy=function(e,t){return e&&e.length?Wn(e,Yo(t,2)):[]},Ke.uniqWith=function(e,t){return t="function"==typeof t?t:B,e&&e.length?Wn(e,B,t):[]},Ke.unset=function(e,t){return null==e||qn(e,t)},Ke.unzip=_a,Ke.unzipWith=ka,Ke.update=function(e,t,n){return null==e?e:Jn(e,t,to(n))},Ke.updateWith=function(e,t,n,o){return o="function"==typeof o?o:B,null==e?e:Jn(e,t,to(n),o)},Ke.values=ar,Ke.valuesIn=function(e){return null==e?[]:A(e,nr(e))},Ke.without=oi,Ke.words=ir,Ke.wrap=function(e,t){return Ci(to(t),e)},Ke.xor=ai,Ke.xorBy=ri,Ke.xorWith=si,Ke.zip=ii,Ke.zipObject=function(e,t){return Xn(e||[],t||[],It)},Ke.zipObjectDeep=function(e,t){return Xn(e||[],t||[],xn)},Ke.zipWith=li,Ke.entries=el,Ke.entriesIn=tl,Ke.extend=Ni,Ke.extendWith=Bi,dr(Ke,Ke),Ke.add=_l,Ke.attempt=ul,Ke.camelCase=nl,Ke.capitalize=rr,Ke.ceil=kl,Ke.clamp=function(e,t,n){return n===B&&(n=t,t=B),n!==B&&(n=(n=Qa(n))==n?n:0),t!==B&&(t=(t=Qa(t))==t?t:0),Rt(Qa(e),t,n)},Ke.clone=function(e){return Dt(e,4)},Ke.cloneDeep=function(e){return Dt(e,5)},Ke.cloneDeepWith=function(e,t){return Dt(e,5,t="function"==typeof t?t:B)},Ke.cloneWith=function(e,t){return Dt(e,4,t="function"==typeof t?t:B)},Ke.conformsTo=function(e,t){return null==t||Pt(e,t,tr(t))},Ke.deburr=sr,Ke.defaultTo=function(e,t){return null==e||e!=e?t:e},Ke.divide=Il,Ke.endsWith=function(e,t,n){e=Za(e),t=Gn(t);var o=e.length,a=n=n===B?o:Rt(qa(n),0,o);return(n-=t.length)>=0&&e.slice(n,a)==t},Ke.eq=Ua,Ke.escape=function(e){return(e=Za(e))&&Oe.test(e)?e.replace(He,gn):e},Ke.escapeRegExp=function(e){return(e=Za(e))&&ze.test(e)?e.replace(Ye,"\\$&"):e},Ke.every=function(e,t,n){var o=Di(e)?a:Mt;return n&&Zo(e,t,n)&&(t=B),o(e,Yo(t,3))},Ke.find=di,Ke.findIndex=fa,Ke.findKey=function(e,t){return f(e,Yo(t,3),xt)},Ke.findLast=pi,Ke.findLastIndex=ha,Ke.findLastKey=function(e,t){return f(e,Yo(t,3),Et)},Ke.floor=Cl,Ke.forEach=La,Ke.forEachRight=Sa,Ke.forIn=function(e,t){return null==e?e:Ds(e,Yo(t,3),nr)},Ke.forInRight=function(e,t){return null==e?e:Ps(e,Yo(t,3),nr)},Ke.forOwn=function(e,t){return e&&xt(e,Yo(t,3))},Ke.forOwnRight=function(e,t){return e&&Et(e,Yo(t,3))},Ke.get=Xa,Ke.gt=Ai,Ke.gte=ji,Ke.has=function(e,t){return null!=e&&qo(e,t,Xt)},Ke.hasIn=er,Ke.head=va,Ke.identity=cr,Ke.includes=function(e,t,n,o){e=Ma(e)?e:ar(e),n=n&&!o?qa(n):0;var a=e.length;return n<0&&(n=rs(a+n,0)),Ya(e)?n<=a&&e.indexOf(t,n)>-1:!!a&&g(e,t,n)>-1},Ke.indexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=rs(o+a,0)),g(e,t,a)},Ke.inRange=function(e,t,n){return t=Wa(t),n===B?(n=t,t=0):n=Wa(n),function(e,t,n){return e>=ss(t,n)&&e<rs(t,n)}(e=Qa(e),t,n)},Ke.invoke=Ji,Ke.isArguments=Ri,Ke.isArray=Di,Ke.isArrayBuffer=Pi,Ke.isArrayLike=Ma,Ke.isArrayLikeObject=Ha,Ke.isBoolean=function(e){return!0===e||!1===e||Na(e)&&zt(e)==se},Ke.isBuffer=Ti,Ke.isDate=Ui,Ke.isElement=function(e){return Na(e)&&1===e.nodeType&&!Va(e)},Ke.isEmpty=function(e){if(null==e)return!0;if(Ma(e)&&(Di(e)||"string"==typeof e||"function"==typeof e.splice||Ti(e)||Oi(e)||Ri(e)))return!e.length;var t=Fs(e);if(t==de||t==ge)return!e.size;if(ta(e))return!_n(e).length;for(var n in e)if(Dr.call(e,n))return!1;return!0},Ke.isEqual=function(e,t){return sn(e,t)},Ke.isEqualWith=function(e,t,n){var o=(n="function"==typeof n?n:B)?n(e,t):B;return o===B?sn(e,t,B,n):!!o},Ke.isError=$a,Ke.isFinite=function(e){return"number"==typeof e&&ns(e)},Ke.isFunction=Oa,Ke.isInteger=xa,Ke.isLength=Ea,Ke.isMap=Mi,Ke.isMatch=function(e,t){return e===t||fn(e,t,Go(t))},Ke.isMatchWith=function(e,t,n){return n="function"==typeof n?n:B,fn(e,t,Go(t),n)},Ke.isNaN=function(e){return Ba(e)&&e!=+e},Ke.isNative=function(e){if(Ns(e))throw new yr("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(e)},Ke.isNil=function(e){return null==e},Ke.isNull=function(e){return null===e},Ke.isNumber=Ba,Ke.isObject=Fa,Ke.isObjectLike=Na,Ke.isPlainObject=Va,Ke.isRegExp=Hi,Ke.isSafeInteger=function(e){return xa(e)&&e>=-Z&&e<=Z},Ke.isSet=$i,Ke.isString=Ya,Ke.isSymbol=za,Ke.isTypedArray=Oi,Ke.isUndefined=function(e){return e===B},Ke.isWeakMap=function(e){return Na(e)&&Fs(e)==be},Ke.isWeakSet=function(e){return Na(e)&&"[object WeakSet]"==zt(e)},Ke.join=function(e,t){return null==e?"":os.call(e,t)},Ke.kebabCase=ol,Ke.last=ya,Ke.lastIndexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var a=o;return n!==B&&(a=(a=qa(n))<0?rs(o+a,0):ss(a,o-1)),t==t?function(e,t,n){for(var o=n+1;o--;)if(e[o]===t)return o;return o}(e,t,a):h(e,y,a,!0)},Ke.lowerCase=al,Ke.lowerFirst=rl,Ke.lt=xi,Ke.lte=Ei,Ke.max=function(e){return e&&e.length?Ht(e,cr,Qt):B},Ke.maxBy=function(e,t){return e&&e.length?Ht(e,Yo(t,2),Qt):B},Ke.mean=function(e){return b(e,cr)},Ke.meanBy=function(e,t){return b(e,Yo(t,2))},Ke.min=function(e){return e&&e.length?Ht(e,cr,In):B},Ke.minBy=function(e,t){return e&&e.length?Ht(e,Yo(t,2),In):B},Ke.stubArray=fr,Ke.stubFalse=hr,Ke.stubObject=function(){return{}},Ke.stubString=function(){return""},Ke.stubTrue=function(){return!0},Ke.multiply=Ll,Ke.nth=function(e,t){return e&&e.length?jn(e,qa(t)):B},Ke.noConflict=function(){return tn._===this&&(tn._=Hr),this},Ke.noop=pr,Ke.now=yi,Ke.pad=function(e,t,n){e=Za(e);var o=(t=qa(t))?x(e):0;if(!t||o>=t)return e;var a=(t-o)/2;return Ao(Xr(a),n)+e+Ao(Zr(a),n)},Ke.padEnd=function(e,t,n){e=Za(e);var o=(t=qa(t))?x(e):0;return t&&o<t?e+Ao(t-o,n):e},Ke.padStart=function(e,t,n){e=Za(e);var o=(t=qa(t))?x(e):0;return t&&o<t?Ao(t-o,n)+e:e},Ke.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),ls(Za(e).replace(Ge,""),t||0)},Ke.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Zo(e,t,n)&&(t=n=B),n===B&&("boolean"==typeof t?(n=t,t=B):"boolean"==typeof e&&(n=e,e=B)),e===B&&t===B?(e=0,t=1):(e=Wa(e),t===B?(t=e,e=0):t=Wa(t)),e>t){var o=e;e=t,t=o}if(n||e%1||t%1){var a=cs();return ss(e+a*(t-e+Kt("1e-"+((a+"").length-1))),t)}return Un(e,t)},Ke.reduce=function(e,t,n){var o=Di(e)?u:k,a=arguments.length<3;return o(e,Yo(t,4),n,a,js)},Ke.reduceRight=function(e,t,n){var o=Di(e)?d:k,a=arguments.length<3;return o(e,Yo(t,4),n,a,Rs)},Ke.repeat=function(e,t,n){return t=(n?Zo(e,t,n):t===B)?1:qa(t),Mn(Za(e),t)},Ke.replace=function(){var e=arguments,t=Za(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Ke.result=function(e,t,n){var o=-1,a=(t=no(t,e)).length;for(a||(a=1,e=B);++o<a;){var r=null==e?B:e[ua(t[o])];r===B&&(o=a,r=n),e=Oa(r)?r.call(e):r}return e},Ke.round=Sl,Ke.runInContext=_,Ke.sample=function(e){return(Di(e)?bt:$n)(e)},Ke.size=function(e){if(null==e)return 0;if(Ma(e))return Ya(e)?x(e):e.length;var t=Fs(e);return t==de||t==ge?e.size:_n(e).length},Ke.snakeCase=sl,Ke.some=function(e,t,n){var o=Di(e)?p:Nn;return n&&Zo(e,t,n)&&(t=B),o(e,Yo(t,3))},Ke.sortedIndex=function(e,t){return Bn(e,t)},Ke.sortedIndexBy=function(e,t,n){return Vn(e,t,Yo(n,2))},Ke.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var o=Bn(e,t);if(o<n&&Ua(e[o],t))return o}return-1},Ke.sortedLastIndex=function(e,t){return Bn(e,t,!0)},Ke.sortedLastIndexBy=function(e,t,n){return Vn(e,t,Yo(n,2),!0)},Ke.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Bn(e,t,!0)-1;if(Ua(e[n],t))return n}return-1},Ke.startCase=il,Ke.startsWith=function(e,t,n){return e=Za(e),n=null==n?0:Rt(qa(n),0,e.length),t=Gn(t),e.slice(n,n+t.length)==t},Ke.subtract=Al,Ke.sum=function(e){return e&&e.length?I(e,cr):0},Ke.sumBy=function(e,t){return e&&e.length?I(e,Yo(t,2)):0},Ke.template=function(e,t,n){var o=Ke.templateSettings;n&&Zo(e,t,n)&&(t=B),e=Za(e),t=Bi({},t,o,Ho);var a,r,s=Bi({},t.imports,o.imports,Ho),i=tr(s),l=A(s,i),c=0,u=t.interpolate||lt,d="__p += '",p=kr((t.escape||lt).source+"|"+u.source+"|"+(u===Fe?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),m="//# sourceURL="+(Dr.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Wt+"]")+"\n";e.replace(p,(function(t,n,o,s,i,l){return o||(o=s),d+=e.slice(c,l).replace(ct,P),n&&(a=!0,d+="' +\n__e("+n+") +\n'"),i&&(r=!0,d+="';\n"+i+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),c=l+t.length,t})),d+="';\n";var f=Dr.call(t,"variable")&&t.variable;if(f){if(Ze.test(f))throw new yr("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(r?d.replace(Pe,""):d).replace(Te,"$1").replace(Ue,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(r?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var h=ul((function(){return br(i,m+"return "+d).apply(B,l)}));if(h.source=d,$a(h))throw h;return h},Ke.times=function(e,t){if((e=qa(e))<1||e>Z)return[];var n=ee,o=ss(e,ee);t=Yo(t),e-=ee;for(var a=C(o,t);++n<e;)t(n);return a},Ke.toFinite=Wa,Ke.toInteger=qa,Ke.toLength=Ja,Ke.toLower=function(e){return Za(e).toLowerCase()},Ke.toNumber=Qa,Ke.toSafeInteger=function(e){return e?Rt(qa(e),-Z,Z):0===e?e:0},Ke.toString=Za,Ke.toUpper=function(e){return Za(e).toUpperCase()},Ke.trim=function(e,t,n){if((e=Za(e))&&(n||t===B))return L(e);if(!e||!(t=Gn(t)))return e;var o=E(e),a=E(t);return oo(o,R(o,a),D(o,a)+1).join("")},Ke.trimEnd=function(e,t,n){if((e=Za(e))&&(n||t===B))return e.slice(0,F(e)+1);if(!e||!(t=Gn(t)))return e;var o=E(e);return oo(o,0,D(o,E(t))+1).join("")},Ke.trimStart=function(e,t,n){if((e=Za(e))&&(n||t===B))return e.replace(Ge,"");if(!e||!(t=Gn(t)))return e;var o=E(e);return oo(o,R(o,E(t))).join("")},Ke.truncate=function(e,t){var n=30,o="...";if(Fa(t)){var a="separator"in t?t.separator:a;n="length"in t?qa(t.length):n,o="omission"in t?Gn(t.omission):o}var r=(e=Za(e)).length;if(T(e)){var s=E(e);r=s.length}if(n>=r)return e;var i=n-x(o);if(i<1)return o;var l=s?oo(s,0,i).join(""):e.slice(0,i);if(a===B)return l+o;if(s&&(i+=l.length-i),Hi(a)){if(e.slice(i).search(a)){var c,u=l;for(a.global||(a=kr(a.source,Za(tt.exec(a))+"g")),a.lastIndex=0;c=a.exec(u);)var d=c.index;l=l.slice(0,d===B?i:d)}}else if(e.indexOf(Gn(a),i)!=i){var p=l.lastIndexOf(a);p>-1&&(l=l.slice(0,p))}return l+o},Ke.unescape=function(e){return(e=Za(e))&&$e.test(e)?e.replace(Me,vn):e},Ke.uniqueId=function(e){var t=++Pr;return Za(e)+t},Ke.upperCase=ll,Ke.upperFirst=cl,Ke.each=La,Ke.eachRight=Sa,Ke.first=va,dr(Ke,function(){var e={};return xt(Ke,(function(t,n){Dr.call(Ke.prototype,n)||(e[n]=t)})),e}(),{chain:!1}),Ke.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Ke[e].placeholder=Ke})),n(["drop","take"],(function(e,t){pt.prototype[e]=function(n){n=n===B?1:rs(qa(n),0);var o=this.__filtered__&&!t?new pt(this):this.clone();return o.__filtered__?o.__takeCount__=ss(n,o.__takeCount__):o.__views__.push({size:ss(n,ee),type:e+(o.__dir__<0?"Right":"")}),o},pt.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),n(["filter","map","takeWhile"],(function(e,t){var n=t+1,o=1==n||3==n;pt.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Yo(e,3),type:n}),t.__filtered__=t.__filtered__||o,t}})),n(["head","last"],(function(e,t){var n="take"+(t?"Right":"");pt.prototype[e]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");pt.prototype[e]=function(){return this.__filtered__?new pt(this):this[n](1)}})),pt.prototype.compact=function(){return this.filter(cr)},pt.prototype.find=function(e){return this.filter(e).head()},pt.prototype.findLast=function(e){return this.reverse().find(e)},pt.prototype.invokeMap=Hn((function(e,t){return"function"==typeof e?new pt(this):this.map((function(n){return on(n,e,t)}))})),pt.prototype.reject=function(e){return this.filter(Ta(Yo(e)))},pt.prototype.slice=function(e,t){e=qa(e);var n=this;return n.__filtered__&&(e>0||t<0)?new pt(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==B&&(n=(t=qa(t))<0?n.dropRight(-t):n.take(t-e)),n)},pt.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},pt.prototype.toArray=function(){return this.take(ee)},xt(pt.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),a=Ke[o?"take"+("last"==t?"Right":""):t],r=o||/^find/.test(t);a&&(Ke.prototype[t]=function(){var t=this.__wrapped__,s=o?[1]:arguments,i=t instanceof pt,l=s[0],u=i||Di(t),d=function(e){var t=a.apply(Ke,c([e],s));return o&&p?t[0]:t};u&&n&&"function"==typeof l&&1!=l.length&&(i=u=!1);var p=this.__chain__,m=!!this.__actions__.length,f=r&&!p,h=i&&!m;if(!r&&u){t=h?t:new pt(this);var g=e.apply(t,s);return g.__actions__.push({func:Ca,args:[d],thisArg:B}),new dt(g,p)}return f&&h?e.apply(this,s):(g=this.thru(d),f?o?g.value()[0]:g.value():g)})})),n(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Lr[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);Ke.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var a=this.value();return t.apply(Di(a)?a:[],e)}return this[n]((function(n){return t.apply(Di(n)?n:[],e)}))}})),xt(pt.prototype,(function(e,t){var n=Ke[t];if(n){var o=n.name+"";Dr.call(ys,o)||(ys[o]=[]),ys[o].push({name:t,func:n})}})),ys[Io(B,2).name]=[{name:"wrapper",func:B}],pt.prototype.clone=function(){var e=new pt(this.__wrapped__);return e.__actions__=uo(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=uo(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=uo(this.__views__),e},pt.prototype.reverse=function(){if(this.__filtered__){var e=new pt(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},pt.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Di(e),o=t<0,a=n?e.length:0,r=function(e,t,n){for(var o=-1,a=n.length;++o<a;){var r=n[o],s=r.size;switch(r.type){case"drop":e+=s;break;case"dropRight":t-=s;break;case"take":t=ss(t,e+s);break;case"takeRight":e=rs(e,t-s)}}return{start:e,end:t}}(0,a,this.__views__),s=r.start,i=r.end,l=i-s,c=o?i:s-1,u=this.__iteratees__,d=u.length,p=0,m=ss(l,this.__takeCount__);if(!n||!o&&a==l&&m==l)return Kn(e,this.__actions__);var f=[];e:for(;l--&&p<m;){for(var h=-1,g=e[c+=t];++h<d;){var v=u[h],y=v.iteratee,b=v.type,w=y(g);if(2==b)g=w;else if(!w){if(1==b)continue e;break e}}f[p++]=g}return f},Ke.prototype.at=ci,Ke.prototype.chain=function(){return Ia(this)},Ke.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Ke.prototype.next=function(){this.__values__===B&&(this.__values__=Ga(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?B:this.__values__[this.__index__++]}},Ke.prototype.plant=function(e){for(var t,n=this;n instanceof ut;){var o=ma(n);o.__index__=0,o.__values__=B,t?a.__wrapped__=o:t=o;var a=o;n=n.__wrapped__}return a.__wrapped__=e,t},Ke.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof pt){var t=e;return this.__actions__.length&&(t=new pt(this)),(t=t.reverse()).__actions__.push({func:Ca,args:[wa],thisArg:B}),new dt(t,this.__chain__)}return this.thru(wa)},Ke.prototype.toJSON=Ke.prototype.valueOf=Ke.prototype.value=function(){return Kn(this.__wrapped__,this.__actions__)},Ke.prototype.first=Ke.prototype.head,Gr&&(Ke.prototype[Gr]=function(){return this}),Ke}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define((function(){return yn}))):on?((on.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div>\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"switch-account"===t.slug&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(console.log(t.message),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg"),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}});const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":this.host()},laravelHost:function(){return"http://localhost"===this.host()?"https://vc.dev.yunolearning.com":"https://stage.yunolearning.com'"===this.host()?"https://ai-laravel-stage-z49vj.kinsta.app":"https://www.yunolearning.com"===this.host()?"https://ai-laravel-yxdza.kinsta.app":"https://vc.dev.yunolearning.com"},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let n="";void 0!==t&&(n="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+n},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+n},studentResultsAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+n},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+n+"/"+o+"/"+a+"/"+r},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+n+"/"+o},classAPi:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classesByViewAPi:function(e,t,n,a,r,s,i,l,c,u){let d="",p="",m="";u&&(d="?ver="+o()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==s&&!1!==s&&(m="/"+s);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+n+"/"+a+"/"+r+m+"/"+i+"/"+l+"/"+c+d},groupsAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+n+"/"+o},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,n,o,a){let r="";!1!==a&&(r="?filter="+a);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+n+"/"+o+r},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+a},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentsList:function(e,t,n,o,a,r,s,i,l,c,u,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+c+"/"+m+"/"+u+"/"+d},paymentsList:function(e,t,n,a,r,s,i,l,c,u){let d="";u&&(d="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+c+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let n="",o="";void 0!==t&&!1!==t&&(o="/"+t),void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+o},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/status/toggle"},changeBatchAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+n+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+a},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+n+"/"+o},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,n,o,a,r,s,i,l){let c="";void 0!==s&&!1!==s&&(c="/"+s);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+c+"/"+i+"/"+l},mapCoursesAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+n},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},deleteResourceAttachmentAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+n},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},videoListAPI:function(e,t){let n="",o="";void 0!==e&&(n=e),void 0===o&&!1===o||(o=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,n,o,a){let r="";if(!1!==t)r=t;else{let t="";void 0!==n&&(t=n),r=e+"/"+t+"/"+o+"/"+a}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+r},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+n},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,n,a,r,s,i){let l="",c="",u="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==a&&!1!==a&&(d="/"+a),void 0!==r&&!1!==r&&(p="/"+r),void 0!==s&&!1!==s&&(m="/"+s),void 0!==n&&!1!==n&&(u="/"+n),i&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+t+u+d+p+m+c},courseListAPI:function(e,t,n,a){let r="",s="",i="";a&&(r="?ver="+o()),void 0!==t&&!1!==t&&(s="/"+t),void 0!==n&&!1!==n&&(i="/"+n);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+s+i+r},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+n},learnerListAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+n},instructorAvailabilityAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+r+a},createUpdateAvailabilityAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+n},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+n},instructorsByCategoryAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+n},capabilitiesAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+n},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+n},signInURLWithState(e){const t=["email","profile"],n=encodeURI(JSON.stringify(e));let o="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",n);o=e.toString()}else o="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return o},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+a},eventDetailAPI:function(e,t,n,a,r){let s="";r&&(s="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+n+"?uuid="+a+s},profileDetailAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+a},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},resourcesDetailAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o},videoTestimonialAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n},createExamResultAPI:function(e,t,n,o){let a="",r="";void 0===o&&!1===o&&(o="v1"),void 0!==t&&!1!==t&&(a="/"+t),void 0!==n&&!1!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/"+o+"/examresult/"+e+a+r},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,n){let o="",a="";void 0!==t&&(o="/"+t),void 0!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o+a},videotestimonialListAPI:function(e,t,n,o){let a="",r="";void 0!==n&&(a="/"+n),void 0!==o&&(r="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+a+r},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,n,o,a,r,s){let i="",l="",c="",u="",d="",p="",m="";void 0!==e&&!1!==e&&(c="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==n&&!1!==n&&(u="/"+n),void 0!==o&&!1!==o&&(d="/"+o),void 0!==a&&!1!==a&&(p="/"+a),void 0!==r&&!1!==r&&(i="/"+r),void 0!==s&&!1!==s&&(l="/"+s);return this.pickHost()+"/wp-json/yuno/v1/article"+c+m+u+d+p+i+l},webinarSingleAPI:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+a},webinarListingAPI:function(e,t,n,a,r){let s="";r&&(s="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+a+s},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+o},managelearningContentAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+n},learningContentAPI:function(e,t,n,o,a){let r="",s="",i="",l="";void 0!==t&&!1!==t&&(r="/"+t),void 0!==n&&!1!==n&&(s="/"+n),void 0!==o&&!1!==o&&(i="/"+o),void 0!==a&&!1!==a&&(l="/"+a);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+r+s+i+l},learnerInsightsAPI:function(e,t,n,a,r,s,i,l,c){let u="";c&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+u},learnerInsightsClassAPI:function(e,t,n,a){let r="",s="";s=void 0!==n&&!1!==n?"v2":"v1",a&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/"+s+"/learner/class/"+e+"/"+t+r},signupFormAPI:function(e,t,n){let a="",r="";n&&(a="?ver="+o()),void 0!==t&&!1!==t&&(r="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+r+a},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,n,a,r){let s="";r&&(s="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+n+"/"+a+s},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,n,o){let a="";!1!==o&&(a="?filters="+encodeURI(JSON.stringify(o)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+n+"/"+a},resourcesResultsAPI:function(e,t,n,o,a,r,s){let i="";!1!==r&&(i="?filters="+encodeURI(JSON.stringify(r))),void 0!==s&&!1!==s||(s="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+s+"/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+i},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+a},instructorInsightsAPI:function(e,t,n,a,r,s,i,l){let c="";l&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+n+"/"+a+"/"+r+"/"+s+"/"+i+c},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+n},dashboardListAPI:function(e,t,n,o,a){let r="",s="",i="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==t&&!1!==t&&(i="/"+t),void 0!==o&&!1!==o&&(r="/"+o),void 0!==a&&!1!==a&&(s="/"+a);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+i+l+r+s},enrollmentDashboardAPI:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+n+"/"+o+"/"+a},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+n},publishedBlogsAPI:function(e,t,n,o,a,r){let s="",i="",l="",c="",u="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(l="/"+t),void 0!==n&&!1!==n&&(c="/"+n),void 0!==o&&!1!==o&&(u="/"+o),void 0!==a&&!1!==a&&(s="/"+a),void 0!==r&&!1!==r&&(i="/"+r);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+c+u+s+i},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,n,o,a,r){r=void 0!==r&&!1!==r?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"";return this.pickHost()+"/wp-json/yuno/v1/"+r+"/"+e+t+n+o+a},instructorMyCourses:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+n},instructorCourseBatches:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+n+"/batches/"+o+"/"+a+"/"+r},manageBookmarkAPI:function(e,t,n,o,a,r){void 0!==t&&!1!==t||(t="v1"),n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"",r=void 0!==r&&!1!==r?"?filters="+encodeURI(JSON.stringify(r)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+n+o+a+r},availableCourses:function(e,t,n,o){t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+n+o},availableBatches:function(e,t,n,o){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+n},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+n+"/"+o},pastBatchesList:function(e,t,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,n,o,a,r,s,i,l,c,u,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+c+"/"+u+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+n+"/"+o+"/"+a},instructorVideotestimonial:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,n,o,a,r,s){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+n},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+n},classReviews:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+n+"/"+o+"/"+a},classReviewsByInstructor:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+a},listOfUser:function(e,t,n){let a="";n&&(a="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+a},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,n,o,a,r,s,i,l,c,u){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+n+"/"+o+"/"+a+"/"+r+"/"+s+"/"+i+"/"+l+"/"+c+"/"+u},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n},courseEconomicsSummary:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n+"/"+o},csvList:function(e,t,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+n+"/"+o+"/"+a},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,n,o){void 0===o&&(o="v1");return this.pickHost()+"/wp-json/yuno/"+o+"/referrer/user/"+e+"/"+t+"/"+n},referralReports:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+n+"/"+o},orgToken:function(e,t){let n="";return n="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,n},webhooks:function(e,t,n,o,a,r){let s="";return"grid"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+n+"/"+o+"/"+a+"/"+r:"create"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),s},seo:function(e,t,n,o){let a="";return"status"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?a=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+o),a},quiz:function(e,t,n,o){let a="";return void 0!==o&&!1!==o||(o=""),"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+o:"quizgrid"===e?a=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?a=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?a=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+n:"delete"===e?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),a},question:function(e,t,n,o,a){let r="";return"questions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+n:"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?r=this.pickHost()+"/wp-json/yuno/v1/question/"+o:"deleteQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"attempt"===e?r=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+o:"attemptQuestionSet"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+a:"questionset"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"questionsetQuestions"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+a:"questionsetQuestionsList"===e?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+a:"questionsetQuestionsReorder"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),r},enrollments:function(e,t,n,o){let a="";return"active"===e?a=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+n+"/learners":"extendDate"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),a},blog:function(e,t,n,o){let a="";return"recentSingle"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+o+"?is_list=true&category_id="+t:"categoriesList"===e?a=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),a},writingTask:function(e,t,n,o,a,r,s,i){let l="";return"type"===e?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+n:"create"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o+"/"+a+"/"+r+"/"+s+"/"+i:"payload"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o:"delete"===e&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+o),l},categoryLandingPage:function(e,t){let n="";return"category"===e?n=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+n+"/"+o},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,n,o,a,r,s,i,l,c,u,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${o}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${a}/${r}/${s}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${a}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${i}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${t}/${o}/${a}/${c}/${u}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${n}/${o}/${a}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,n){let o="";return"steps"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+n:"postStep"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+n:"updateMobile"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),o},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,n,o,a,r){let s="";switch(e){case"insights":s=`/wp-json/yuno/v2/users/${t}/learner/${n}/${o}/${a}`;break;case"demoRequests":s=`/wp-json/yuno/v1/demo-requests/${t}/list/${o}/${a}`;break;case"demoRequestsOrg":s=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${n}/${o}/${a}`;break;case"learnerDetailOrg":s=`/wp-json/yuno/v1/demo-requests/${t}/${r}`;break;case"learnerDetail":s=`/wp-json/yuno/v1/demo-requests/${r}`;break;case"instructorLearnerDetail":s=`/wp-json/yuno/v2/instructor/mylearner/${r}`;break;case"orgAdminLearners":s=`/wp-json/yuno/v2/orgadmin/learner/${n}/${o}/${a}`}return this.pickHost()+s},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,n,o,a){let r="";switch(e){case"googleFonts":r=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":r=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":r=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":r=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${n}/${o}`;break;case"orgBatches":r=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${a}`;break;case"org":r=`${this.pickHost()}/wp-json/yuno/v2/org/${o}`;break;case"categories":r=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return r},course:function(e,t,n,o,a,r){let s="";switch(e){case"payload":s=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":s=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${n}/${o}/${a}/${r}`;break;case"createBatchOrg":s=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":s=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":s=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return s},learner:function(e,t,n,o,a,r){let s="";switch(e){case"enrolledCourses":s=`/wp-json/yuno/v3/learner/${t}/enrollments/${n}`;break;case"classes":s=`/wp-json/yuno/v4/classes/${n}/${o}/${t}?limit=${a}&offset=${r}`;break;case"filters":s=`/wp-json/yuno/v4/classes/filter/${o}/${t}`}return this.pickHost()+s},classes:function(e,t,n,o,a,r,s,i,l,c,u){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${n}/${o}/${a}/${r}/${s}/${i}/${l}/${c}/${u}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private"}[e]||"")},attendance:function(e,t,n,o,a){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${n}/${o}/${a}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots"}[e]||"")},classInsights:function(e,t,n,o,a){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${n}/${o}/${a}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${n}/${o}/${a}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${n}/${o}/${a}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",activeOrg:"/wp-json/yuno/v3/user/state"}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`,detail:`/place/details/json?place_id=${t.placeID}&key=${t.key}`}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules",status:`/api/status/${t.loggedinUserID}/all/${t.jobID}`}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")}},n=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){e(!0)},n.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),o=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,n)=>e.find((e=>e[t]===n))||null,heightOfEle:function(e,t){let n=e.offsetHeight;if(t){let t=getComputedStyle(e);return n+=parseInt(t.marginTop)+parseInt(t.marginBottom),n}return n},assignVValidationObj:function(e){const t=window.VeeValidate,n=window.VeeValidateRules,o=t.ValidationProvider,a=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let o in e.messages)t.extend(o,n[o]);t.localize("validationMsg",e),Vue.component("ValidationProvider",o),Vue.component("ValidationObserver",a)},removeObjInArr:function(e,t,n){let o=e.length;for(;o--;)e[o]&&e[o].hasOwnProperty(t)&&arguments.length>2&&e[o][t]===n&&e.splice(o,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return o[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){n.then((function(t){e(t)}))},setCookie:function(e,t,n){let o=new Date;void 0===n&&(n=30),o.setTime(o.getTime()+24*n*60*60*1e3);let a="expires="+o.toGMTString();document.cookie=e+"="+t+";"+a+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:o,removeValInArr:function(e){let t,n,o=arguments,a=o.length;for(;a>1&&e.length;)for(t=o[--a];-1!==(n=e.indexOf(t));)e.splice(n,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,n){let o=e.match(t);return null!=o&&(!0===n?o[1].replace(/\/$/,""):o[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,n){let o=window.pageYOffset,a=(r=e,window.pageYOffset+document.querySelector(r).getBoundingClientRect().top);var r;targetY=document.body.scrollHeight-a<window.innerHeight?document.body.scrollHeight-window.innerHeight:a,customHeight=void 0!==n?n:74,diff=targetY-o-customHeight;let s="";diff&&window.requestAnimationFrame((function e(n){s||(s=n);let a=n-s,r=Math.min(a/t,1);var i;r=(i=r)<.5?4*i*i*i:(i-1)*(2*i-2)*(2*i-2)+1,window.scrollTo(0,o+diff*r),a<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var n=e.substring(1).split("&"),o=0;o<n.length;o++){var a=n[o].split("=");if(a[0]==t)return a[1]}return!1},cleanTextAndTruncate:function(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n}}}(jQuery),YUNOStore=function(e){const t=function(e,t,n,o,a){if(o){if(void 0===t.addToModule||t.addToModule)if(void 0!==a&&a){for(let t=0;t<n.length;t++)e.data.push(n[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=n}else t.moduleTabs?(e.error=n,e.loading=!1):e.error=n;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[]},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{ClassTitle:"",ClassDescription:"",ClassDate:"",ClassTime:"",ClassDuration:"",classSchedule:"",learner:[],RelatedCourses:"",BatchID:"",academy_id:""}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,n){if(n.isError){let o=n.response.response,a=!(void 0===n.pushData||!n.pushData),r=e[n.store],s="";s=void 0!==o&&void 0!==o.data&&void 0!==o.data.message?o.data.message:YUNOCommon.config.errorMsg.common,console.log(s),console.log(n.store),n.tabs?(t(r.tabs[n.tabIndex],n,o,!1,a),r.tabs[n.tabIndex].error=!0,r.tabs[n.tabIndex].errorData=s,n.callback&&n.callbackFunc(r.tabs[n.tabIndex].errorData)):(t(r,n,o,!1,a),r.error=!0,r.errorData=s,n.callback&&n.callbackFunc(r.errorData))}else{let o=n.response.data.data,a=!(void 0===n.pushData||!n.pushData),r=n.response.data,s=e[n.store];n.tabs?(204===r.code&&(s.tabs[n.tabIndex].error=!0,s.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(s.tabs[n.tabIndex].error=!0,s.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),t(s.tabs[n.tabIndex],n,o,!0,a)):(204===r.code&&(s.error=!0,s.errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(s.error=!0,s.errorData=r.message,console.log(r.message),console.log(n.store)),t(s,n,o,!0,a)),n.callback&&n.callbackFunc(n)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,n){if(n.isError){let o=n.response;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!1),n.callback&&n.callbackFunc(e[n.module].errorData)}else{let o=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let o=n.response.data.data;n.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[n.module],n,o,!0),n.isTabAdded=!0,setTimeout((()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let a=n.response.data.data;for(var o=0;o<a.length;o++)a[o].username_email_phone=`${a[o].username_email} (${a[o].phone})`;t(e[n.module],n,a,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let n=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",t.classID),0===n.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let n=e[t.parentModule].data[t.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,n){if(n.isError){let o=n.response;"enrolled"===n.nestedTab?(e[n.module].tabs[n.tabIndex].loading=!1,e[n.module].tabs[n.tabIndex].pageLoading=!1,e[n.module].tabs[n.tabIndex].error=o):t(e[n.module],n,o,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let n=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let o=0;o<n.length;o++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(n[o]);let o=e[t.module].data[t.courseIndex].tabs[t.tabIndex];o.count=t.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount,o.isLoadMore=!1}else{for(let e=0;e<n.length;e++)n[e].isBatches=!1,n[e].isLoading=!1,n[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=n}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let o=t.response.data.data;if(void 0!==t.nested){let a=o.columns,r=o.rows,s={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&a.push(s);for(var n=0;n<r.length;n++)r[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+r[n].id};e[t.nested].tabs[0].data=o,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=o.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const n=function(n){n?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?n(!1):n(!0)},instructorLearners(e,t){const n=function(n){if(n&&void 0!==t.response.data){let n=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=n,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let o={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};e[t.module].tabs[t.index].data.unshift(o),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let n=e.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const a=e[t.module].tabs[t.index];a.hasData=!0,a.isLoadMore=!1;for(var o=0;o<n.length;o++)n[o].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[o].group_created_time)[0]),n[o].scheduleClassURL=`/class-schedule/?groupID=${n[o].group_id}`,a.data.push(n[o]);a.count=t.response.data.count,a.currentCount=a.data.length,a.offset=a.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?n(!1):(n(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)}))},postThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):console.log("not defined")},fetchData({commit:e,state:t},n){let o="",a="";a="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},o=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0===n.moduleLoading||n.moduleLoading?o.loading=!0:o.loading=!1,n.moduleTabs&&n.isTabLoader&&(o.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:a}).then((o=>{n.response=o,n.isError=!1,e(n.module,n),403===(n.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,t[n.store].loading=!1,t[n.store].success=!0;let a="";a=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),403===o.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),n.response=o,n.isError=!0,e(n.module,n)}))},putData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPutData({commit:e,state:t},n){let o={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))},postData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPostData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let a="";a=void 0!==n.store?t[n.store]:t[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},deleteData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:o,data:n.payload}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOHeader=(jQuery,{header:function(){Vue.component("yuno-header",{props:["isnav","scrollenabled","logoAlignment","postsignup","hassearchbar","options"],template:'\n                <div>\n                    <header id="yunoHeader" class="yunoHeader" :class="{\'noNav\': isnav === false, \'scrollEnabled\': scrollenabled, \'logoCenter\': logoAlignment}">\n                        <div class="container-fluid noOverflow">\n                            <nav class="navbar navbar-expand-lg" :class="[hassearchbar !== undefined && hassearchbar.isActive ? \'hasSearchBar\' : \'\',]">\n                                <figure class="logo navbar-brand">\n                                    <a :href="isnav !== false ? getHomeURL : \'#\'"><img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                </figure>\n                                <button v-if="isnav !== false" class="navbar-toggler" type="button" aria-controls="navbarSupportedContent" :aria-expanded="enabledSubmenu" aria-label="Toggle navigation" @click="toggleMenu">\n                                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                </button>\n                                <div v-if="isnav !== undefined && !isnav" class="yunoLogin">\n                                    <div v-if="false" class="yunoCallUs noSpacer" :class="{\'preLogin\': getUserStatus !== true}">\n                                        <a class="whatsapp" href="https://api.whatsapp.com/send?phone=+918847251466" target="_blank"><i class="fa fa-whatsapp" aria-hidden="true"></i></a>\n                                    </div>        \n                                </div>\n                                <div v-if="isnav !== false" class="collapse navbar-collapse yunoMainNav" id="yunoMainNav" :class="[!getUserStatus || getUserStatus && userRole.data === \'Learner\' ? \'hasSearch\' : \'\', userRole.data === \'yuno-admin\' ? \'hasAdmin\' : \'\', enabledSubmenu ? \'show\' : \'collapsed\']">\n                                    <ul class="navbar-nav align-items-center w-100">\n                                        <template v-if="headerMenu.loading" v-for="(menu, menuIndex) in 3">\n                                            <li :style="{ marginLeft: \'30px\' }">\n                                                <b-skeleton width="100px" height="32px" active></b-skeleton>\n                                            </li>\n                                        </template>\n                                        <template v-if="headerMenu.success" v-for="(menu, menuIndex) in headerMenu.data">\n                                            <li class="hasCTA" v-if="detectPlatform() === \'android\'">\n                                                <b-button tag="a"\n                                                    href="https://play.google.com/store/apps/details?id=com.yunolearning.learn"\n                                                    target="_blank"\n                                                    class="yunoPrimaryCTA small">\n                                                    Get the app\n                                                </b-button>\n                                            </li>\n                                            <template v-if="menu.type === \'menu_item\'">\n                                                <li \n                                                    class="nav-item" \n                                                    :class="{\'active\': menu.is_active === true, \'dropdown\': menu.items.length, \'isCTA\': menu.section === \'Invite\'}" \n                                                    :key="menuIndex">\n                                                    <template v-if="menu.items.length">\n                                                        <a \n                                                            :id="\'submenu\' + menuIndex"\n                                                            :class="{\'dropdownToggle\': menu.items.length}" \n                                                            @click="manageSubmenu($event, menu, \'submenu\' + menuIndex)"\n                                                            :ref="\'submenu\' + menuIndex"\n                                                            :href="menu.url">\n                                                            {{menu.section}}\n                                                        </a>\n                                                    </template>\n                                                    <a \n                                                        v-else\n                                                        :class="{\'dropdown-toggle\': menu.items.length}" \n                                                        @click="manageCustomLink($event, menu)"\n                                                        :href="menu.url">\n                                                            {{menu.section}}\n                                                    </a>\n                                                </li>\n                                            </template>\n                                            <template v-if="menu.type === \'search_item\'">\n                                                <li class="hasSearchBar">\n                                                    <div class="searchBarWrapper">\n                                                        <validation-observer \n                                                            tag="div" \n                                                            ref="searchObserver" \n                                                            v-slot="{ handleSubmit, invalid }">\n                                                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                                                <b-field class="searchFieldWrapper">\n                                                                    <validation-provider \n                                                                        tag="div"\n                                                                        class="searchField"\n                                                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                                                        v-slot="{ errors, classes }">\n                                                                        <b-autocomplete\n                                                                            :class="classes"\n                                                                            v-model="categories.current"\n                                                                            :data="categories.data"\n                                                                            autocomplete="courseSearch"\n                                                                            :loading="categories.isLoading"\n                                                                            placeholder="Search..."\n                                                                            @typing="searchOnTyping"\n                                                                            @select="onSelect($event)"\n                                                                            :clearable="true"\n                                                                        >\n                                                                            <template slot-scope="props">\n                                                                                <template v-if="props.option.course_url">\n                                                                                    <div class="suggestion courseBlock">\n                                                                                        <figure>\n                                                                                            <div class="imageWrapper" v-if="false">\n                                                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                                                            </div>\n                                                                                            <figcaption>\n                                                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                                                <p class="courseDetail">\n                                                                                                    <span class="caption">Course</span>\n                                                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                                                </p>\n                                                                                            </figcaption>\n                                                                                        </figure>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                            </template>\n                                                                        </b-autocomplete>\n                                                                    </validation-provider>\n                                                                    <div class="ctaWrapper">\n                                                                        <b-button\n                                                                            native-type="submit"\n                                                                            class="doSearch">\n                                                                            <span class="material-icons-outlined">search</span>\n                                                                        </b-button>  \n                                                                    </div>\n                                                                </b-field>\n                                                            </form>\n                                                        </validation-observer>\n                                                    </div>\n                                                </li> \n                                            </template>\n                                        </template>\n                                    </ul>\n                                </div>\n                                <template v-if="isnav !== false">\n                                    <div class="yunoLogin" v-if="!getUserStatus">\n                                        <a \n                                            class="marginRight15"\n                                            @click="trackLoginLandingPage()"\n                                            href="/login/">\n                                            <span class="yuno-login-with-google-on-pages grey wired">Log in</span>\n                                        </a>\n                                        <a @click="trackLoginLandingPage()" href="/login/?type=signup">\n                                            <span class="yuno-login-with-google-on-pages">Sign up</span>\n                                        </a>\n                                        <div class="yunoCallUs" v-if="false" :class="{\'preLogin\': getUserStatus !== true}">\n                                            <template v-if="false">\n                                                <span class="caption">Call us at</span>\n                                                <a href="tel:+91-8847251466"><span class="material-icons">call</span> <span class="value">+91-8847251466</span></a>\n                                            </template>\n                                            <a href="#" @click.prevent="bookADemo()">Book a demo class</a>\n                                        </div>        \n                                    </div>\n                                </template>\n                                <template v-if="isnav !== false || hassearchbar !== undefined && hassearchbar.isActive">\n                                    <template v-if="getUserStatus">\n                                        <div class="dropdown yunoLoginDropdown" v-if="getUserProfile.success">\n                                            <a class="dropdown-toggle" href="#" role="button" id="userProfile" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                                                <figure class="profilePic">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.first_name">\n                                                </figure>\n                                            </a>\n                                            <div class="dropdown-menu" aria-labelledby="userProfile">\n                                                <figure class="menuHeader">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.yuno_display_name">\n                                                    <figcaption>\n                                                        <p class="userName">{{ getUserProfile.data.yuno_display_name }}</p>\n                                                        <p class="userEmail">{{ getUserProfile.data.email }}</p>\n                                                    </figcaption>\n                                                </figure>\n                                                <ul class="userlinks">\n                                                    <li \n                                                        v-for="(item, l) in usermenu"\n                                                        :key="l"\n                                                        v-if="item.isActive"\n                                                        :class="[item.slug === \'switchAccount\' ? \'linksFooter\' : \'\']">\n                                                        <a \n                                                            v-if="item.callbackFunc !== false" \n                                                            class="dropdown-item" \n                                                            @click="item.callbackFunc" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                        <a \n                                                            v-else \n                                                            class="dropdown-item" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                    </li>\n                                                </ul>\n                                                <div class="additionalItems" v-if="isItemAvailable([\'Instructor\'])">\n                                                    <template v-if="referralCode.loading">\n                                                        <div class="loaderWrapper">\n                                                            <div class="smallLoader"></div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-if="referralCode.success">\n                                                        <template v-if="referralCode.error === null">\n                                                            <div class="item">\n                                                                <b-field label="Referral code">\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="material-icons-outlined">content_copy</span>\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </template>\n                                </template>\n                            </nav>\n                        </div>\n                        <b-modal \n                            :active.sync="invite.modal" \n                            :width="500" \n                            :can-cancel="[\'escape\', \'x\']"\n                            class="yunoModal inviteModal">\n                                <div class="modalHeader">\n                                    <h2 class="modalTitle">Invite Link</h2>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="wrapper">\n                                        <template v-if="inviteLink.loading">\n                                            <div class="loaderWrapper">\n                                                <div class="smallLoader"></div>\n                                            </div>\n                                        </template>\n                                        <template v-if="inviteLink.success">\n                                            <template v-if="inviteLink.error === null">\n                                                <ul class="classFields">\n                                                    <li>\n                                                        <div class="clipboard">\n                                                            <b-input id="inviteLink" :value="inviteLink.data.invitation_link" readonly></b-input>\n                                                            <i @click="copyToClipboard(\'inviteLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n                                                        </div>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else>\n                                                <p>{{inviteLink.error}}</p>\n                                            </template>\n                                        </template>\n                                    </div>\n                                </div>\n                        </b-modal>\n                    </header>\n                    <div class="yunoSubmenu" \n                        :class="[submenu.isActive ? \'active\' : \'inActive\']"\n                        v-if="submenu.isActive && scrollenabled === undefined || submenu.isActive && scrollenabled !== undefined && !scrollenabled" \n                        v-closable="{exclude: [submenu.ref], handler: \'onClose\'}">\n                        <div class="container">\n                            <a href="#" class="closeSubmenu" @click.prevent="closeSubmenu()"><span class="material-icons">arrow_back_ios</span></a>\n                            <ul class="submenuList" :class="[submenu.data.items.length < 3 ? \'col2\' : \'\']">\n                                <li v-for="(nav, i) in submenu.data.items" :key="i">\n                                    <a :href="nav.url">\n                                        <figure>\n                                            <div class="imgWrapper"><img :src="nav.image" :alt="nav.label"></div>\n                                            <figcaption>\n                                                <p class="navLabel">{{ nav.label }}</p>\n                                                <p class="navDes">{{nav.excerpt}}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </a>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    <yuno-choose-account-type>\n                    </yuno-choose-account-type>\n                    <b-modal \n                        :active.sync="bookademo.modal" \n                        :width="388" \n                        :can-cancel="[\'escape\', \'x\']"\n                        :on-cancel="bookademoModalClose"\n                        class="yunoModal lightTheme">\n                            <template v-if="bookademo.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">\n                                        {{ bookademo.title }}\n                                    </h3>\n                                </div>\n                                <div class="modalBody">\n                                    <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observerWrapper"\n                                        ref="bookademoobserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form @submit.prevent="handleSubmit(initBookADemo)">\n                                            <validation-provider \n                                                tag="div" \n                                                class="categoryWithImage" \n                                                :rules="{required:true}" \n                                                v-slot="{ errors, classes }"\n                                            >\n                                                <template v-for="(option, i) in bookademo.data">\n                                                    <div class="fieldWrapper">\n                                                        <div class="inner">\n                                                            <b-field :key="i" :style="categoryImg(option)">\n                                                                <b-radio-button \n                                                                    :class="classes"\n                                                                    v-model="bookademo.selected"\n                                                                    @input="initBookADemo()"\n                                                                    name="bookademo"\n                                                                    :native-value="option">\n                                                                    {{option.label}}\n                                                                </b-radio-button>\n                                                            </b-field>\n                                                            <div class="catLabel">{{option.label}}</div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>\n                                            <div class="ctaWrapper alignLeft" v-if="false">\n                                                <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>\n                                            </div>\n                                        </form>\n                                    </validation-observer>\n                                </div>\n                            </template>\n                    </b-modal>\n                </div>\n            ',data(){return{enabledSubmenu:!1,bookademo:{modal:!1,title:"Book a Demo Class",subtitle:"Choose Subject Category",selected:"",data:[]},showSubmenu:!1,invite:{modal:!1},submenu:{isActive:!1,data:[],ref:""},usermenu:[{label:"Home",slug:"learnerDashboard",icon:"home",iconType:"material-icons-outlined",url:"/learner/",isActive:!1,callbackFunc:!1},{label:"Bookmarks",slug:"myBookmarks",icon:"bookmark_border",iconType:"material-icons-outlined",url:"/my-bookmarks/",isActive:!1,callbackFunc:!1},{label:"Enrolled Courses",slug:"myEnrollments",icon:"local_library",iconType:"material-icons-outlined",url:"/learner-courses/",isActive:!1,callbackFunc:!1},{label:"Subscribed Categories",slug:"categories",icon:"category",iconType:"material-icons-outlined",url:"/subscribed-categories",isActive:!1,callbackFunc:!1},{label:"My Profile",slug:"myProfile",icon:"account_circle",iconType:"material-icons-outlined",url:"",isActive:!1,callbackFunc:!1},{label:"Settings",slug:"settings",icon:"settings",iconType:"material-icons-outlined",url:"/settings/",isActive:!0,callbackFunc:!1},{label:"Switch account",slug:"switchAccount",icon:"swap_horiz",iconType:"material-icons-outlined",url:this.$store.getters.getSwitchAccountURL,isActive:!0,callbackFunc:()=>this.chooseAccountState()},{label:"Logout",slug:"logout",icon:"logout",iconType:"material-icons-outlined",url:"/logout/",isActive:!0,callbackFunc:!1}],errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}},computed:{...Vuex.mapState(["loginWithGoogle","user","userRole","inviteLink","module","referralCode","searchSuggestions","chooseAccountTypeModal"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},getSignInURL(){return this.$store.getters.getSignInURL},switchAccountURL(){return this.$store.getters.getSwitchAccountURL},headerMenu:{get(){return this.$store.state.header}},getUserStatus:{get:()=>0!==Number(isLoggedIn)},getUserProfile:{get(){return this.$store.state.userProfile}}},async created(){this.emitEvents()},mounted(){this.logoGoogleSchema(),this.getUserStatus?this.getState():(["userState","oldUserState","paymentState","demoClassState","skipSignUp","userSignUpPage","userSignUp","noSignupRequired","isWelcomePage","isReferrer","isQuiz"].forEach((e=>localStorage.removeItem(e))),sessionStorage.removeItem("activeUserV1"))},methods:{toggleMenu(){this.enabledSubmenu?this.enabledSubmenu=!1:this.enabledSubmenu=!0},manageCustomLink(e,t){"Teach on Yuno"===t.label&&(e.preventDefault(),this.chooseAccountTypeModal.modal=!0)},isObjectBlank:e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&0===Object.keys(e).length,getQueryParams(e){const t=new URLSearchParams(e.split("?")[1]);let n={};for(const[e,o]of t)n[e]=o;return n},trackLoginLandingPage(){if(!this.user.isLoggedin){let e="",t="";e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",t=this.isObjectBlank(this.getQueryParams(window.location.origin+window.location.pathname+window.location.search))?null:this.getQueryParams(window.location.origin+window.location.pathname+window.location.search);const n={url:window.location.origin+window.location.pathname,pageTitle:document.title,category:e,urlParams:t,zohoMeta:this.$props.options.zohoMeta};sessionStorage.setItem("landingPage",JSON.stringify(n))}},categoryImg:e=>({"background-image":`url(${e.image.replace(/ /g,"%20")})`}),bookademoModalClose(){this.bookademo.selected="",this.bookademo.data=[]},initBookADemo(){window.location.href=this.bookademo.selected.url},bookADemo(){this.bookademo.modal=!0;let e=JSON.parse(JSON.stringify(YUNOCommon.findObjectByKey(this.headerMenu.data,"label","Explore").submenu));e.forEach((e=>{e.slug=e.label.toLowerCase(),"ielts"===e.slug?e.url="/for-ads/ielts/ielts-for-all-v3/":"english speaking"===e.slug?e.url="/for-ads/english-speaking/english-for-all-v3/":"pte"===e.slug?e.url="/for-ads/pte/pte-for-all-v3/":"duolingo"===e.slug?e.url="/for-ads/duolingo-for-all/":"toefl"===e.slug?e.url="/for-ads/toefl-for-all/":"french"===e.slug&&(e.url="/for-ads/french/french-for-all-v3/")})),this.bookademo.data=e},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},initForm(){const e=this.categories.selected,t={filter:"category",id:e.category_id,label:e.category,parent_id:0,slug:e.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",t,n)},onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:o}=e.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce((function(e){e.length>2?(this.categories.isLoading=!0,this.categories.data=[],this.fetchCourseSuggestions(e)):this.categories.data=[]}),700),gotReferralCode(e){void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code&&e.response.data.data},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},emitEvents(){Event.$on("updateHeaderSearch",(()=>{})),Event.$on("checkLoggedInState",(e=>{e&&Event.$on("gotUserRole",((e,t)=>{"Learner"===e&&(this.$store.state.homeURL="/learner"),"Instructor"===e&&this.fetchReferralCode()}))}))},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},logoGoogleSchema(){let e={"@context":"https://schema.org","@type":"Organization",url:"https://www.yunolearning.com/",logo:YUNOCommon.config.host()+"/wp-content/themes/yunolearning-child/assets/images/yuno.jpeg"};this.structuredData(e)},manageLogin(e,t){e.preventDefault(),Event.$emit("manageLogin",e,t)},detectPlatform:()=>/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":!!/Android/i.test(window.navigator.userAgent)&&"android",gotInviteLink(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code)this.inviteLink.data=e.response.data.data;else{const t=void 0!==e.response.data&&void 0!==e.response.data.message?e.response.data.message:YUNOCommon.config.errorMsg.common;this.inviteLink.error=t}},fetchInviteLink(){const e=this,t={apiURL:YUNOCommon.config.getInviteURLAPI(isLoggedIn),module:"gotData",store:"inviteLink",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotInviteLink(t)}};this.$store.dispatch("fetchData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},initInviteModal(e){e.preventDefault(),this.invite.modal=!0,0===this.inviteLink.data.length&&this.fetchInviteLink()},whichPage:()=>window.location.href.indexOf("open")>-1?"open":null,submenuClasses(){const e=document.querySelectorAll(".dropdown-menu.level3");for(let t=0;t<e.length;t++)e[t].classList.remove("show")},closeSubmenu(){this.onClose()},onClose(){this.submenu.isActive=!1,this.submenu.data=[],this.submenu.ref=""},manageSubmenu(e,t,n){e.preventDefault(),this.submenu.isActive=!this.submenu.isActive,this.submenu.data=t,this.submenu.ref=n},manageSubmenuLevel3(e){let t=document.querySelectorAll(".dropdown-menu."+e)[0];this.submenuClasses(),t.classList.toggle("show")},isPageHasCategory(){"undefined"!=typeof assignedCategory&&localStorage.setItem("userSignUpPage",assignedCategory)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},getState(){if(null!==localStorage.getItem("isChooseAccountState"))return["userState","isChooseAccountState"].forEach((e=>localStorage.removeItem(e))),!1}}})}}),YUNOHeaderRevamp=(jQuery,{headerRevamp:function(){let e;Vue.directive("closable",{bind(t,n,o){e=e=>{e.stopPropagation();const{handler:a,exclude:r}=n.value;let s=!1;r.forEach((t=>{if(!s){const n=o.context.$refs[t];s=n[0].contains(e.target)}})),t.contains(e.target)||s||o.context[a]()},document.addEventListener("click",e),document.addEventListener("touchstart",e)},unbind(){document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}}),Vue.component("yuno-header-revamp",{props:["hasnav","hasscrollenabled","haslogoAlignment","postsignup","hassearchbar","fetchAPI","options"],template:'\n            <div class="isSticky">\n                <div>\n                    <section class="appPrompt" v-if="appInfo.platform === \'android\'">\n                        <div class="container">\n                            <ul class="colGrid">\n                                <li class="appIcon">\n                                    <div class="closePrompt">\n                                        <i @click="onPromptClose()" class="fa fa-times" aria-hidden="true"></i>\n                                    </div>\n                                    <figure class="appMedia">\n                                        <div class="iconWrap">\n                                            <img :src="appInfo.icon" :alt="appInfo.shortName">\n                                        </div>\n                                        <figcaption class="appInfo">\n                                            <p class="infoTitle">{{ appInfo.shortName }}</p>\n                                            <p class="infoCaption" v-if="appInfo.hasOS === \'android\'">{{ appInfo.androidCaption }}</p>\n                                        </figcaption>\n                                    </figure>\n                                </li>\n                                <li>\n                                    <b-button tag="a"\n                                        :href="appInfo.hasOS === \'android\' ? appInfo.androidURL : \'\'"\n                                        target="_blank"\n                                        rel="nofollow"\n                                        @click="onPromptView($event)"\n                                        class="yunoPrimaryCTA small">\n                                        Open\n                                    </b-button>\n                                </li>\n                            </ul>\n                        </div>\n                    </section>\n                    <template v-if="userInfo.error">\n                        <header id="yunoHeader" class="yunoHeader">\n                            <div class="container noOverflow">\n                                <nav class="navbar navbar-expand-lg">\n                                    <figure class="logo navbar-brand">\n                                        <a :href="getHomeURL"><img :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                    </figure>\n                                    <button class="navbar-toggler" data-toggle="collapse" data-target="#yunoMainNav" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">\n                                        <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                    </button>\n                                    <div class="collapse navbar-collapse yunoMainNav" id="yunoMainNav">\n                                        <ul class="navbar-nav justify-content-end align-items-center w-100">\n                                            <li class="nav-item">\n                                                <a \n                                                    href="/logout">\n                                                    Logout\n                                                </a>\n                                            </li>\n                                        </ul>\n                                    </div>\n                                </nav>\n                            </div>\n                        </header>\n                        <main id="yunoMain">\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </main>\n                    </template>\n                    <template v-else>\n                        <div class="notificationBar" :class="{ notificationHide: notification.isHidden, notificationShow: !notification.isHidden, notVisibleInDOM: !notification.isVisible }">\n                            <a :href="notification.url" target="_blank">\n                                {{ notification.label }}\n                            </a>\n                            <span class="material-icons-outlined" @click="hideNotificationBar">close</span>\n                        </div>\n                        <yuno-header \n                            :isnav="hasnav !== undefined ? hasnav : undefined" \n                            :logoAlignment="haslogoAlignment !== undefined ? haslogoAlignment : undefined" \n                            :scrollenabled="hasscrollenabled !== undefined ? hasscrollenabled : undefined"\n                            :postsignup="postsignup"\n                            :hassearchbar="hassearchbar"\n                            :options="{zohoMeta: options !== undefined ? options.zohoMeta : \'\'}">\n                        </yuno-header>\n                    </template>\n                    <b-modal \n                        :active.sync="config.unauthorizedModal" \n                        :width="450" \n                        :can-cancel="[\'escape\', \'x\']" \n                        :on-cancel="unauthorizedModalClose"\n                        class="yunoModal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">Session Expired</h2>\n                            </div>\n                            <div class="modalBody">\n                                <div class="wrapper">\n                                    <p>{{sessionExpired}}</p>\n                                </div>\n                            </div>\n                            <div class="modalFooter">\n                                <div class="unauthorizedLogin">\n                                    <a \n                                        @click.prevent="setState()"\n                                        href="#">\n                                        <span class="g_icon"></span>\n                                        <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                    </a>\n                                </div>\n                            </div>\n                    </b-modal>\n                </div>\n                <b-modal \n                    :active.sync="loginModal.modal" \n                    :width="360" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="loginModalClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="loginModal.modal">\n                            <div class="modalHeader">\n                                <h3 class="modalTitle">\n                                    <template v-if="loginModal.active === \'login\'">\n                                        Log in\n                                    </template>\n                                    <template v-else>\n                                        Sign up\n                                    </template>\n                                </h3>\n                            </div>\n                            <div class="modalBody">\n                                <template v-if="loginModal.active === \'login\'">\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <div class="ctaWrapper">\n                                        <button class="googleLogin width70" @click="setState">\n                                            <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                        </button>\n                                    </div>\n                                    <p class="footerCaption">\n                                        {{loginModal[loginModal.active].footer.label}} <a @click="updateLoginState($event, \'signup\')" :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                    </p>\n                                </template>\n                                <template v-else>\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observer"\n                                        ref="commonSignupObserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                            <b-field label="Phone number">\n                                                <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                    <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                    <p class="error">{{errors[0]}}</p>\n                                                </validation-provider>    \n                                            </b-field>\n                                            <div class="ctaWrapper">\n                                                <button class="googleLogin" type="submit">\n                                                    <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                                </button>\n                                            </div>\n                                            <p class="helperCaption">\n                                                {{loginModal[loginModal.active].helper.label}} <a @click="updateLoginState($event, \'login\')" :href="loginModal[loginModal.active].helper.actionURL">{{loginModal[loginModal.active].helper.actionLabel}}</a>\n                                            </p>\n                                            <p class="footerCaption" v-if="loginModal[loginModal.active].footer.isActive">\n                                                {{loginModal[loginModal.active].footer.label}} <a :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                            </p>\n                                        </form>\n                                    </validation-observer>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </div>\n            ',data(){return{tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,appInfo:{shortName:"Yuno Learning",name:"Yuno Learning",icon:this.$store.state.themeURL+"/assets/images/yunoLogo.svg",androidURL:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",hasOS:"android",androidCaption:"FREE - In the Google Play Store",platform:"",daysHidden:1,daysReminder:90},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}},loginModal:{modal:!1,active:"",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",login:{title:"Log into your Yuno Learning account",hasMobile:!1,cta:"Login with Google",footer:{label:"Don't have an account yet?",actionLabel:"Sign Up",actionURL:"#"}},signup:{title:"Sign up to create a free account on Yuno Learning",hasMobile:!0,cta:"Sign up with Google",helper:{label:"Already a user?",actionLabel:"Login",actionURL:"#"},footer:{label:"Are you an instructor?",isActive:!0,actionLabel:"Sign up here",actionURL:"/ielts/become-an-instructor"}}},notification:{isHidden:!1,isVisible:!1,label:"Free summer online workshop for kids and teenagers",url:"/free-summer-workshops-for-kids",hideNotificationDate:""}}},computed:{...Vuex.mapState(["user","userRole","userInfo","userProfile","header","footer","config","apiTokenExpiryTime","apiTokenRefresh"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},emptyStates:()=>({state:"dataNotFound"}),getSignInURL(){return this.$store.getters.getSignInURL}},async created(){this.emitEvents()},mounted(){this.loginStatus()},beforeDestroy(){},methods:{hideNotificationBar(){this.notification.isVisible&&(this.notification.isVisible=!1,this.notification.isHidden=!0)},setSigninProps(){localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const{zohoMeta:e={}}=this.$props.options||{},t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general";this.signIn={...this.signIn,categoryURL:`/${t}`,landing_page:{url:window.location.origin+window.location.pathname,title:document.title},productCode:e.productcode||"",leadStatus:e.leadstatus||"",utmSource:YUNOCommon.getQueryParameter("utm_source"),utmCampaign:YUNOCommon.getQueryParameter("utm_campaign"),utmMedium:YUNOCommon.getQueryParameter("utm_medium"),adGroupID:YUNOCommon.getQueryParameter("adgroupid"),adContent:YUNOCommon.getQueryParameter("ad_content"),utmTerm:YUNOCommon.getQueryParameter("utm_term"),gclid:YUNOCommon.getQueryParameter("gclid"),content:{type:e.content_type||"",id:e.content_id||""}}},isFirefoxPrivate(e){null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},emitEvents(){void 0!==Event.$emit&&(Event.$on("manageLogin",((e,t)=>{this.manageLogin(e,t)})),Event.$on("initLoginModal",((e,t,n)=>{this.manageLogin(e,"signup"),this.loginModal[this.loginModal.active].footer.isActive=!1})),Event.$on("initSignupModal",(e=>{this.manageLogin(e,"signup")})))},updateLoginState(e,t){e.preventDefault(),this.loginModal.active=t},initCommonSignup(){this.setState()},loginModalClose(){setTimeout((()=>{this.loginModal.active=""}),300),localStorage.removeItem("paymentState"),localStorage.removeItem("isQuiz")},manageLogin(e,t){this.loginModal.modal=!0,this.loginModal.active=t},onPromptClose(){const e=this.appInfo.daysHidden;YUNOCommon.setCookie("yunoAppPrompt2",!0,e),this.detectPlatform()},onPromptView(e){this.detectPlatform()},detectPlatform(){if(/iPhone|iPad|iPod/i.test(window.navigator.userAgent))this.appInfo.platform="ios";else if(/Android/i.test(window.navigator.userAgent)){const e=YUNOCommon.getCookie("yunoAppPrompt2");this.appInfo.platform=void 0===e&&"android"}else this.appInfo.platform=!1},unauthorizedModalClose(){window.location.href="/logout"},roleSpecificAPI(e,t){"Learner"===e&&this.detectPlatform(),void 0!==Event.$emit&&Event.$emit("gotUserRole",e,t)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):(this.user.isLoggedin=!1,this.detectPlatform(),void 0===this.$props.hasnav||this.$props.hasnav?(this.fetchPreLoginMenu(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin))},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.userRole.data=t.role,this.userRole.success=!0,this.userProfile.data=t,this.userProfile.success=!0,(void 0===this.$props.hasnav||this.$props.hasnav)&&0!==this.header.data.length&&this.fetchPostLoginMenu(t.role),this.$props.fetchAPI&&this.fetchPostLoginMenu(t.role);const n=localStorage.getItem("skipSignUp"),o=localStorage.getItem("paymentState");if("Instructor"===t.role&&"de-active"===t.account_status&&"/account-disabled/"!==window.location.pathname)return window.location.href=YUNOCommon.config.host()+"/account-disabled",!1;"pending"===t.is_signup_completed&&"/sign-up/"!==window.location.pathname&&"/instructor-sign-up/"!==window.location.pathname&&null===n&&null===o&&setTimeout((()=>{"Instructor"===t.role?window.location.href=YUNOCommon.config.host()+"/instructor-sign-up":window.location.href=YUNOCommon.config.host()+"/sign-up"}),50),this.roleSpecificAPI(t.role,t)}else this.userInfo.error=!0,this.header.success=!0,this.footer.success=!0},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",e)},highlightCurrentPage(e){const t=e.response.data.data.map((e=>({...e,isActive:e.url===window.location.href})));this.header.data=t},gotPostLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotUserMenu"))},fetchPostLoginMenu(e){const t={apiURL:YUNOCommon.config.headerMenuAPIV2(isLoggedIn,e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:this.gotPostLoginMenu};this.$store.dispatch("fetchData",t)},gotPreLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotPreLoginMenu"))},fetchPreLoginMenu(){const e={userID:isLoggedIn,orgID:0},t={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",callback:!0,callbackFunc:this.gotPreLoginMenu};this.$store.dispatch("fetchData",t)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <template v-if="footer.loading">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in 4">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <ul class="linkList">\n                                            <template v-for="(item, j) in 4">\n                                                <li \n                                                    :key="j">\n                                                    <b-skeleton active></b-skeleton>\n                                                </li>\n                                            </template>\n                                        </ul>  \n                                    </div>        \n                                </template>\n                            </div>\n                        </template>\n                        <template v-if="footer.success">\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(primary, i) in footer.data.primary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="primary.type === \'withIcon\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :class="item.icon"\n                                                        :key="j">\n                                                        <a rel="nofollow" :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'basic\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="primary.type === \'withOrderList\'">\n                                            <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                                <li v-if="primary.title !== undefined" class="listTitle">\n                                                    <h3>{{ primary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in primary.items">\n                                                    <li \n                                                        :key="j">\n                                                        {{item.label}}\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                            <b-button tag="a"\n                                                :href="primary.cta.url"\n                                                target="_blank"\n                                                rel="nofollow noopener"\n                                                class="yunoSecondaryCTA">\n                                                {{ primary.cta.label }}\n                                            </b-button>\n                                        </template>\n                                        <template v-if="primary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="primary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <div class="col-12">\n                                    <div class="spacer"></div>\n                                </div>\n                            </div>\n                            <div class="row" v-if="isnav !== false">\n                                <template v-for="(secondary, i) in footer.data.secondary">\n                                    <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                        <template v-if="secondary.type === \'basic\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="secondary.title !== undefined" class="listTitle">\n                                                    <h3>{{ secondary.title }}</h3>\n                                                </li>\n                                                <template v-for="(item, j) in secondary.items">\n                                                    <li \n                                                        :key="j">\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>\n                                        <template v-if="secondary.type === \'stackBlock\'">\n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                                <ul class="linkList marginBtm30" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <li \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                                <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                            </a>\n                                                        </li>\n                                                    </template>\n                                                </ul>\n                                            </template>            \n                                            <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                                <ul class="linkList" :class="secondary.type">\n                                                    <li v-if="item.title !== undefined" class="listTitle">\n                                                        <h3>{{ item.title }}</h3>\n                                                    </li>\n                                                    <li class="iconsBlock">\n                                                        <template v-for="(subitem, k) in item.items">\n                                                            <div \n                                                                :class="subitem.icon"\n                                                                :key="k">\n                                                                <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template> \n                                            <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                        </template>\n                                    </div>    \n                                </template>\n                            </div>\n                            <div class="row">\n                                <div class="col-12">\n                                    <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                        <li class="copy" v-if="false">© {{currentYear}} Yunolearning. All rights reserved.</li>\n                                        <li v-if="isLogo === undefined || isLogo">\n                                            <figure class="logo">\n                                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                            </figure>\n                                        </li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </template>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"916239571825"}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},methods:{}})}});Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach((e=>{e.items.forEach((e=>{e.is_active=n===t(e.url);let o=!1;e.sub_items.forEach((a=>{a.is_active=n===t(a.url),a.is_active&&a.parent_id===e.id&&(o=!0)})),e.is_expended=!!o}))}))},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:0},n=this,o={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",o)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:o}=e;null===sessionStorage.getItem("activeOrg")&&(n?o.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(o[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"switch-account"===t.slug&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-csv-download-modal",{props:["data","options"],template:'\n        <b-modal \n            :active.sync="data.modal.isActive" \n            :width="500" \n            :can-cancel="[\'escape\', \'x\']"\n            :on-cancel="csvModalClose"\n            class="yunoModal lightTheme">\n                <template v-if="data.modal.isActive">\n                    <div class="modalHeader">\n                        <h2 class="modalTitle">Initiating request</h2>\n                    </div>\n                    <div class="modalBody">\n                        <p class="modalCaption">We have initiated the request. Go to the downloads to check the status of your CSV.</p>\n                        <div class="ctaWrapper alignLeft">\n                            <b-button\n                                tag="a"\n                                href="/downloads/"\n                                target="_blank"\n                                @click="csvModalClose"\n                                class="yunoSecondaryCTA">\n                                Go to downloads\n                            </b-button>\n                        </div>        \n                    </div>\n                </template>\n        </b-modal>\n    ',data:()=>({}),computed:{...Vuex.mapState(["filterResult","loader"])},async created(){},mounted(){},methods:{csvModalClose(){this.filterResult.modal.data=[],this.filterResult.modal.isActive=!1}}}),Vue.component("yuno-csv-download",{props:["data","options"],template:'\n        <a \n            class="action" \n            @click.prevent="createCSV()"\n            v-if="data.success && data.error === null"\n            href="#">\n            <b-tooltip label="Request CSV Download"\n                type="is-dark"\n                position="is-left">\n                <span class="material-icons-outlined">summarize</span>\n            </b-tooltip>\n        </a>\n    ',data:()=>({csvDownload:{user_id:isLoggedIn,document_type:"",document_label:"",api_endpoint:"",total_records:""}}),computed:{...Vuex.mapState(["filterResult","loader"])},async created(){},mounted(){},methods:{createCSVDone(e){if(this.manageLoader(!1),void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){let t=e.response.data;this.filterResult.modal.data=t,this.filterResult.modal.isActive=!0}else{let t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},createCSV(){this.setCSVPayload(),this.manageLoader(!0);const e=this,t={apiURL:YUNOCommon.config.createCSV(),module:"gotData",store:"updateLink",payload:this.csvDownload,callback:!0,callbackFunc:function(t){return e.createCSVDone(t)}};this.$store.dispatch("postData",t)},setCSVPayload(){this.csvDownload.total_records=this.filterResult.count,this.csvDownload.api_endpoint=this.$props.options.apiURL,this.csvDownload.document_type=this.$props.options.type,this.csvDownload.document_label=this.$props.options.label},manageLoader(e){this.loader.isActive=e,this.loader.overlay=e}}}),Vue.component("yuno-autocomplete",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoAutocompleteSearch" :class="[data.filter]">\n            <b-field>\n                <b-autocomplete\n                    v-model="data.current"\n                    :clearable="true"\n                    :placeholder="data.placeholder"\n                    :data="data.items"\n                    :loading="data.loading"\n                    :field="data.search_field"\n                    autocomplete="search"\n                    @select="onSearchItemSelect($event, data)"\n                >\n                    <template slot="empty">No results for {{selectedOption}}</template>\n                    <template slot-scope="props">\n                        {{props.option[data.search_field]}}\n                    </template>\n                </b-autocomplete>  \n            </b-field>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{onSearchType:_.debounce((function(e,t){e.length>2?(t.loading=!0,t.items=[],this.$emit("onQuerySearch",e,t,!1)):t.items=[]}),500),onSearchItemSelect(e,t){t.selected=null!==e?e:null,this.$emit("onQuerySearch",name,t,!0)},searchFocusToggle(e,t){}}}),Vue.component("yuno-autocomplete-search",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoAutocompleteSearch" :class="[data.filter]">\n            <b-field>\n                <b-autocomplete\n                    v-model="data.current"\n                    :clearable="true"\n                    :placeholder="data.placeholder"\n                    :data="data.items"\n                    :loading="data.loading"\n                    :field="data.search_field"\n                    autocomplete="search"\n                    @typing="onSearchType($event, data)"\n                    @select="onSearchItemSelect($event, data)"\n                >\n                    <template slot="empty">No results for {{selectedOption}}</template>\n                    <template slot-scope="props">\n                        {{props.option[data.search_field]}}\n                    </template>\n                </b-autocomplete>  \n            </b-field>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user","filterResult"])},async created(){},mounted(){},methods:{onSearchType:_.debounce((function(e,t){e.length>2?(t.loading=!0,t.items=[],this.$emit("onQuerySearch",e,t,!1)):t.items=[]}),500),onSearchItemSelect(e,t){null!==e?t.selected=e[this.$props.data.id_field]:(t.selected=null,this.filterResult.payload[t.filter]=this.defaultFilters[t.filter]),this.$emit("onQuerySearch",name,t,!0)},searchFocusToggle(e,t){}}}),Vue.component("yuno-dynamic-dropdown",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoDropdown" :class="[data.filter]">\n            <template v-if="data.loading">\n                <b-skeleton width="150px" height="43px"></b-skeleton>\n            </template>\n            <template v-if="data.success">\n                <b-dropdown \n                    v-model="selectedOption" \n                    aria-role="list"\n                    :mobile-modal="false"\n                    @change="onChange($event, data)"\n                >\n                    <template #trigger>\n                        <div class="labelWrapper">\n                            <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>\n                            <span class="selectedItem">{{ showSelected(data.selected, data) }}</span>\n                            <a \n                                href="#" \n                                class="clearFilter" \n                                v-if="manageClearFilterCTA(data)"\n                                @click="clearFilter($event, data.selected, data)"\n                            >\n                                <span class="material-icons">cancel</span>\n                            </a>\n                            <span class="material-icons icon">expand_more</span>\n                        </div>\n                    </template>\n                    <template v-for="(option, i) in data.items">\n                        <b-dropdown-item \n                            :value="option" \n                            aria-role="listitem"\n                        >\n                            {{ option.label }}   \n                        </b-dropdown-item>\n                    </template>\n                </b-dropdown>\n            </template>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{manageClearFilterCTA(e){return this.payload[e.filter]!==this.$props.defaultFilters[e.filter]},clearFilter(e,t,n){e.preventDefault(),this.selectedOption="",this.$emit("clearFilter",t,n)},showSelected(e,t){let n="";return n=this.payload[t.filter]===this.$props.defaultFilters[t.filter]?t.placeholder:YUNOCommon.findObjectByKey(t.items,"slug",this.payload[t.filter]).label,n},onChange(e,t){this.$props.payload[e.filter]=e.slug,t.selected=e,this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-empty-state-v2",{props:{options:{type:Object,required:!0},cta:{type:Array,default:()=>[]},image:{type:String,default:"/assets/images/noDataFound.svg"}},template:'\n        <section class="emptyStateV2 " :class="[options.type]">\n            <figure>\n                <img \n                    width="80" \n                    height="59" \n                    :src="wpThemeURL + image" \n                    alt="Yuno Learning"\n                >\n                <figcaption>\n                    {{ options.message }}\n                </figcaption>\n                <div class="ctaWrapper" v-if="cta.length">\n                    <b-button \n                        @click="ctaTrigger()"\n                        :tag="ctaItem.type === \'link\' ? \'a\' : \'button\'"\n                        :href="ctaItem.url"\n                        :target="ctaItem.target"\n                        class="yunoPrimaryCTA wired"\n                        v-for="(ctaItem, index) in cta"\n                    >\n                        {{ ctaItem.label }}\n                    </b-button>\n                </div>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{ctaTrigger(){Event.$emit("ctaTrigger")}}}),Vue.component("yuno-table-grid-filters",{props:["data","options","defaultFilters"],template:'\n        <section class="filtersWrapper" id="filters" :class="[options.isFilterMobile ? \'mobileView\' : \'\']">\n            <template v-if="filters.loading">\n                <div class="filters">\n                    <div class="yunoDropdown" v-for="i in 4" :key="i">\n                        <b-skeleton height="43px" width="150px"></b-skeleton>\n                    </div>\n                </div>\n            </template>\n            <template v-if="filters.success">\n                <div class="filterHeader">\n                    <h3 class="largerTitle">Filters</h3>\n                    <a class="filterTrigger" @click="manageFilters">\n                        <span class="material-icons">close</span>\n                    </a>\n                </div>\n                <div class="filters otherFilters" :class="[options.isFilterMobile ? \'mobileView\' : \'\']">\n                    <template v-for="(filter, i) in filters.data">\n                        <template v-if="filter.ui_control_type === \'suggestion\'">\n                            <yuno-autocomplete\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :defaultFilters="defaultFilters"\n                                :payload="data"\n                            >\n                            </yuno-autocomplete>\n                        </template>\n                        <template v-if="filter.ui_control_type === \'query_suggestion\'">\n                            <yuno-autocomplete-search\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :defaultFilters="defaultFilters"\n                                :payload="data"\n                                @onQuerySearch="onQuerySearch"\n                            >\n                            </yuno-autocomplete-search>\n                        </template>\n                        <template v-if="filter.ui_control_type === \'dropdown\'">\n                            <yuno-simple-dropdown\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :payload="data"\n                                @onDropdownChange="onDropdownChange"\n                                @clearFilter="clearFilter"\n                                :defaultFilters="defaultFilters"\n                            >\n                            </yuno-simple-dropdown>\n                        </template>\n                        <template v-if="filter.ui_control_type === \'dynamic_dropdown\'">\n                            <yuno-dynamic-dropdown\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :payload="data"\n                                @onDropdownChange="onDropdownChange"\n                                @clearFilter="clearFilter"\n                                :defaultFilters="defaultFilters"\n                            >\n                            </yuno-dynamic-dropdown>\n                        </template>\n                        <template v-if="filter.ui_control_type === \'dropdown_multi_select\'">\n                            <yuno-multi-select-dropdown\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :options="{\'payload\': data}"\n                                @onDropdownChange="onDropdownChange"\n                                @clearFilter="clearFilter"\n                                :defaultFilters="defaultFilters"\n                            >\n                            </yuno-multi-select-dropdown>\n                        </template>\n                        <template v-if="filter.ui_control_type === \'search\'">\n                            <yuno-search\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :defaultFilters="defaultFilters"\n                                :payload="data"\n                                @onSearchInput="onSearchInput"\n                            >\n                            </yuno-search>\n                        </template>\n                    </template>\n                    <a class="filterTrigger" @click="manageFilters">\n                        <span class="material-icons">filter_list</span>\n                    </a>\n                </div>\n            </template>\n        </section>\n    ',data:()=>({mobileView:!1}),computed:{...Vuex.mapState(["user","filters"])},async created(){},mounted(){},methods:{onSearchInput(e,t,n){this.$emit("onSearchInput",e,t,n)},onQuerySearch(e,t,n){this.$emit("onQuerySearch",e,t,n)},clearFilter(e,t){this.$emit("clearFilter",e,t)},manageFilters(){this.$emit("manageFilters")},onDropdownChange(e,t){this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-multi-select-dropdown",{props:["data","options","defaultFilters"],template:'\n        <div class="yunoDropdown multiSelect" :class="[data.filter]">\n            <b-dropdown \n                v-model="selectedOption" \n                aria-role="list"\n                :mobile-modal="false"\n                :multiple="true"\n                @change="onChange($event, data)"\n            >\n                <template #trigger>\n                    <div class="labelWrapper">\n                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>\n                        <span class="selectedItem hasGrid">\n                            <template v-if="data.selected.length === 0">\n                                {{ data.placeholder }}\n                            </template>\n                            <template v-else>\n                                <template v-for="(selected, i) in data.selected">\n                                    <div class="item" :key="i">\n                                        <span>\n                                            {{ selected.label }}\n                                        </span>\n                                        <a \n                                            href="#" \n                                            class="clearFilter" \n                                            v-if="manageClearFilterCTA(data)"\n                                            @click="clearFilter($event, selected, data)"\n                                        >\n                                            <span class="material-icons">cancel</span>\n                                        </a>\n                                    </div>\n                                </template>\n                                \n                            </template>\n                        </span>\n                        <span class="material-icons icon">expand_more</span>\n                    </div>\n                </template>\n                <template v-for="(option, i) in data.items">\n                    <b-dropdown-item \n                        :value="option" \n                        aria-role="listitem"\n                        :class="option.is_checked ? \'is-active\' : \'\'"\n                        @click="onItemSelect(option, data)"\n                    >\n                        {{ option.label }}\n                    </b-dropdown-item>\n                </template>\n            </b-dropdown>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{onItemSelect(e,t){e.is_checked?e.is_checked=!1:e.is_checked=!0},manageClearFilterCTA:e=>1!==e.selected.length||"all"!==e.selected[0].slug,clearFilter(e,t,n){e.preventDefault();const o=this.$props.options.payload;YUNOCommon.removeObjInArr(n.selected,"slug",t.slug),YUNOCommon.removeValInArr(o[n.filter],t.slug),0===o[n.filter].length&&(o[n.filter]=this.$props.defaultFilters[this.$props.data.filter]),this.$emit("onDropdownChange",e,n)},onChange(e,t){const n=this.$props.options.payload;"all"===e[0].slug?n[t.filter]=[e[0].slug]:(YUNOCommon.removeValInArr(n[t.filter],"all"),YUNOCommon.removeValInArr(t.selected,"all"),YUNOCommon.findInArray(n[t.filter],e[0].slug)?(YUNOCommon.removeValInArr(n[t.filter],e[0].slug),YUNOCommon.removeObjInArr(t.selected,"slug",e[0].slug)):(n[t.filter].push(e[0].slug),t.selected.push(e[0]))),this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-search",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoAutocompleteSearch" :class="[data.filter]">\n            <b-field>\n                <b-input \n                    v-model="data.selected"\n                    :placeholder="data.placeholder"\n                    @input="onSearchInput($event, data)"\n                >\n                </b-input>\n                \n            </b-field>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{onSearchInput:_.debounce((function(e,t){const n=this.$props.payload;e.length>2?(t.loading=!0,t.items=[],n[this.$props.data.filter]=e,this.$emit("onSearchInput",e,t,!1)):(t.items=[],n[this.$props.data.filter]=this.$props.defaultFilters[this.$props.data.filter],this.$emit("onSearchInput",e,t,!1))}),500)}}),Vue.component("yuno-simple-dropdown",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoDropdown" :class="[data.filter]">\n            <b-dropdown \n                v-model="selectedOption" \n                aria-role="list"\n                :mobile-modal="false"\n                @change="onChange($event, data)"\n            >\n                <template #trigger>\n                    <div class="labelWrapper">\n                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>\n                        <span class="selectedItem">{{ showSelected(data.selected, data) }}</span>\n                        <a \n                            href="#" \n                            class="clearFilter" \n                            v-if="manageClearFilterCTA(data)"\n                            @click="clearFilter($event, data.selected, data)"\n                        >\n                            <span class="material-icons">cancel</span>\n                        </a>\n                        <span class="material-icons icon">expand_more</span>\n                    </div>\n                </template>\n                <template v-for="(option, i) in data.items">\n                    <b-dropdown-item \n                        :value="option" \n                        aria-role="listitem"\n                    >\n                        {{ option.label }}\n                    </b-dropdown-item>\n                </template>\n            </b-dropdown>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{manageClearFilterCTA(e){return this.payload[e.filter]!==this.$props.defaultFilters[e.filter]},clearFilter(e,t,n){e.preventDefault(),this.selectedOption="",this.$emit("clearFilter",t,n)},showSelected(e,t){let n="";return n=this.payload[t.filter]===this.$props.defaultFilters[t.filter]?t.placeholder:YUNOCommon.findObjectByKey(t.items,"slug",this.payload[t.filter]).label,n},onChange(e,t){this.$props.payload[e.filter]=e.slug,t.selected=e,this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-table-grid",{props:{apiURL:{type:Object,required:!0},filterAPIURL:{type:Object,required:!0},apiMethodType:{type:String,required:!0},payload:{type:Object,required:!1},sessionStorage:{type:Object,required:!0},emptyStateCTA:{type:Array,default:()=>[]},emptyStateImg:{type:String},recordFoundCaption:{type:String,default:"found"}},template:'\n        <div :class="[isFilterMobile ? \'filterActive\' : \'\']">\n            <div class="mainHeader">\n                <yuno-table-grid-filters\n                    @onDropdownChange="onDropdownChange"\n                    @manageFilters="onManageFilters"\n                    @clearFilter="onClearFilter"\n                    :data="filterResult.payload"\n                    :defaultFilters="defaultFilters"\n                    :options="{\'isFilterMobile\': isFilterMobile}"\n                    @onQuerySearch="onQuerySearch"\n                    @onSearchInput="onSearchInput"\n                >\n                </yuno-table-grid-filters>\n                <slot name="stats"></slot>\n                <div class="gridInfo" v-if="filterResult.success && !filterResult.error">\n                    <p class="note">\n                        <span>{{ filterResult.count }}</span> {{ recordFoundCaption }} \n                    </p>\n                    <ul class="actions">\n                        <template v-for="(action, i) in gridActions">\n                            <li v-if="action.type === \'dropdown\'" :key="i">\n                                <div class="yunoDropdown iconOnly" :class="[action.filter]">\n                                    <b-dropdown \n                                        v-model="action.selected" \n                                        aria-role="list"\n                                        :mobile-modal="false"\n                                        :multiple="true"\n                                        @change="onHideColChange($event, action)"\n                                    >\n                                        <template #trigger>\n                                            <div class="labelWrapper">\n                                                <span class="selectedItem"><span class="material-icons-outlined">{{ action.icon }}</span></span>\n                                                <span class="material-icons icon">expand_more</span>\n                                            </div>\n                                        </template>\n                                        <p class="listCaption">Hide Columns</p>\n                                        <template v-for="(option, j) in filterResult.data.columns">\n                                            <b-dropdown-item \n                                                :value="option.field.parent" \n                                                :key="j"\n                                                aria-role="listitem"\n                                                @click="onHideColItemSelect(option, action)"\n                                            >\n                                                {{ option.label }}\n                                            </b-dropdown-item>\n                                        </template>\n                                    </b-dropdown>\n                                </div>\n                            </li>\n                        </template>\n                    </ul>\n                </div>\n            </div>\n            <yuno-csv-download-modal :data="filterResult"></yuno-csv-download-modal>\n            <section class="yunoTable">\n                <template v-if="filterResult.loading">\n                    <b-skeleton height="500px"></b-skeleton>\n                </template>\n                <template v-if="filterResult.success">\n                    <template v-if="filterResult.error">\n                        <yuno-empty-state-v2 \n                            :options="{\'type\': \'noDataFound\', \'message\': filterResult.errorData}"\n                            :cta="emptyStateCTA"\n                            :image="emptyStateImg"\n                        >\n                        </yuno-empty-state-v2>\n                    </template>    \n                    <template v-else>\n                        <b-table\n                            :data="filterResult.data.rows"\n                            :loading="filterResult.isLoadMore"\n                            :paginated="true"\n                            :backend-pagination="true"\n                            :total="filterResult.count"\n                            :per-page="filterResult.limit"\n                            :current-page="filterResult.currentPage"\n                            :striped="true"\n                            backend-sorting\n                            @sort="sortPressed"\n                            :default-sort-direction="defaultSortOrder"\n                            :default-sort="defaultSort"\n                            @page-change="pageChange($event)"\n                            ref="table">\n                            <b-table-column\n                                v-for="(column, i) in filterResult.data.columns" \n                                :key="i" \n                                :field="column.field.parent" \n                                :visible="column.is_active"\n                                :label="column.label" \n                                v-slot="props"\n                                :sortable="column.sortable">\n                                <template v-if="column.field.child.length > 0">\n                                    <template v-if="column.field.parent ===  \'start_date\' || column.field.parent === \'end_date\'">\n                                        <div :class="[column.field.parent]">\n                                            <template v-for="(child, j) in column.field.child">\n                                                {{ formatDate(props.row[column.field.parent][child]) }}\n                                            </template>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'learner\' || column.field.parent === \'instructor\' || column.field.parent === \'counselor\'">\n                                        <figure \n                                            :class="[column.field.parent]" class="userWithPhoto"\n                                        >\n                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">\n                                            <figcaption>\n                                                {{ props.row[column.field.parent].full_name }}\n                                            </figcaption>\n                                        </figure>    \n                                    </template>\n                                    <template v-else-if="column.field.parent === \'course\'">\n                                        <div :class="[column.field.parent]" class="grid">\n                                            <template v-for="(child, j) in column.field.child">\n                                                <span class="gridItem">\n                                                    <template v-if="child === \'title\'">\n                                                        {{ props.row[column.field.parent][child] }}\n                                                    </template>\n                                                    <template v-if="child === \'id\'">\n                                                        {{ "(" +  props.row[column.field.parent][child] + ")" }}\n                                                    </template>\n                                                </span>\n                                                <template v-if="child === \'url\'">\n                                                    <a class="gridItem" :href="props.row[column.field.parent][child]" target="_blank"><span class="material-icons">open_in_new</span></a>\n                                                </template>\n                                            </template>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'academy\' || column.field.parent === \'of_org\'">\n                                        <div :class="[column.field.parent]" class="grid">\n                                            <span class="gridItem">\n                                                {{ props.row[column.field.parent].name }}\n                                            </span>\n                                            <span class="gridItem">\n                                                ({{ props.row[column.field.parent].id }})\n                                            </span>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'list_price\' || column.field.parent === \'selling_price\'">\n                                        <div :class="[column.field.parent]" class="grid">\n                                            <span class="gridItem">\n                                                {{ props.row[column.field.parent].exclusive_tax }}\n                                            </span>\n                                            <span class="gridItem">\n                                                ({{ props.row[column.field.parent].inclusive_tax }})\n                                            </span>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'classes\'">\n                                        <div :class="[column.field.parent]" class="grid">\n                                            <span class="gridItem">\n                                                {{ props.row[column.field.parent].attended }}\n                                            </span>\n                                            <span class="gridItem">\n                                                ({{ props.row[column.field.parent].total }})\n                                            </span>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'attendance\'">\n                                        <template v-if="props.row[column.field.parent].percentage === 0 || props.row[column.field.parent].percentage === \'\'">\n                                            NA\n                                        </template>\n                                        <div class="percentageBlock" v-else>\n                                            <b-progress \n                                                :type="{\n                                                    \'is-red\': props.row[column.field.parent].percentage <= 30,\n                                                    \'is-orange\': props.row[column.field.parent].percentage > 30,\n                                                    \'is-yellow\': props.row[column.field.parent].percentage > 50,\n                                                    \'is-lightGreen\': props.row[column.field.parent].percentage > 70,\n                                                    \'is-darkGreen\': props.row[column.field.parent].percentage > 80\n                                                }"  \n                                                format="percent"    \n                                                :value="Number(props.row[column.field.parent].percentage)">\n                                                {{props.row[column.field.parent].percentage}}\n                                            </b-progress>\n                                            <div class="percentage">{{ props.row[column.field.parent].percentage }}% <a href="#" @click.prevent="manageDrawer(props.row, column.field.parent)"><span class="material-icons">pin_invoke</span></a></div>\n                                        </div>\n                                    </template>\n                                    <template v-else-if="column.field.parent === \'enrolled_by\'">\n                                        <figure \n                                            :class="[column.field.parent]" class="userWithPhoto"\n                                        >\n                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">\n                                            <figcaption>\n                                                {{ props.row[column.field.parent].full_name + " " + "(" + props.row[column.field.parent].id + " " + props.row[column.field.parent].role + ")" }} \n                                            </figcaption>\n                                        </figure>    \n                                    </template>\n                                    <template v-else>\n                                        <template v-if="column.field.parent === \'referral\'">\n                                            <figure \n                                                :class="[column.field.parent]" class="userWithPhoto"\n                                            >\n                                                <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">\n                                                <figcaption>\n                                                    {{ props.row[column.field.parent].full_name }}\n                                                </figcaption>\n                                            </figure>    \n                                        </template>\n                                        <template v-else>\n                                            <div :class="[column.field.parent]">\n                                                <template v-for="(child, j) in column.field.child">\n                                                    <template v-if="child.sub_child && child.sub_child.length > 0">\n                                                        <template v-for="(subChild, k) in child.sub_child">\n                                                            <span class="subChild">\n                                                                {{ formatDate(props.row[column.field.parent][child.field][subChild], column.field.parent) }}\n                                                            </span>\n                                                        </template>\n                                                    </template>\n                                                    <template v-else>\n                                                        {{ props.row[column.field.parent][child] }}\n                                                    </template>\n                                                </template>\n                                            </div>\n                                        </template>\n                                    </template>\n                                </template>\n                                <template v-else>\n                                    <template v-if="column.field.parent === \'status\'">\n                                        <div :class="[column.field.parent, \'hasActiveInactive\', props.row[column.field.parent] === \'ACTIVE\' ? \'active\' : \'inactive\']">\n                                            <span class="value">{{ props.row[column.field.parent] }}</span>\n                                        </div>    \n                                    </template>\n                                    <template v-else-if="column.field.parent === \'is_active\'">\n                                        <div :class="[column.field.parent, \'hasActiveInactive\', props.row[column.field.parent] ? \'active\' : \'inactive\']">\n                                            <template v-if="props.row[column.field.parent]">\n                                                <span class="material-icons">how_to_reg</span>\n                                            </template>\n                                            <template v-else>\n                                                <span class="material-icons">person_off</span>\n                                            </template>\n                                        </div>    \n                                    </template>\n                                    <template v-else-if="column.field.parent === \'actions\'">\n                                        <ul :class="[column.field.parent]">\n                                            <li \n                                                v-for="(item, k) in props.row[column.field.parent]"\n                                                v-if="item.is_active"\n                                                :key="\'action-\' + k">\n                                                <b-tooltip :label="item.label"\n                                                    type="is-dark"\n                                                    position="is-left">\n                                                    <a :href="item.url !== false ? item.url : \'#\'" :target="item.link_target" @click="initAction(props.row, item, $event)">\n                                                        <span class="itemLabel">{{ item.label }}</span>\n                                                        <span class="itemIcon" :class="[item.active_class]" @mouseover="manageMouse(item, \'over\')" @mouseout="manageMouse(item, \'out\')">\n                                                            {{ item.icon.value === undefined ?  item.icon.font : item.icon.value }}\n                                                        </span>\n                                                    </a>\n                                                </b-tooltip>\n                                            </li>\n                                        </ul>    \n                                    </template>\n                                    <template v-else>\n                                        <div :class="[column.field.parent]">{{ props.row[column.field.parent] }}</div>    \n                                    </template>\n                                </template>\n                            </b-table-column>\n                        </b-table>    \n                    </template>    \n                </template>\n            </section>\n        </div>\n    ',data:()=>({hasDrawerCols:["enrollment_count","attendance_count"],isFilterMobile:!1,defaultFilters:[],selectedOption:[],defaultSortOrder:"asc",defaultSort:[],gridActions:[{message:"Request CSV download",icon:"summarize",filter:"request_CSV_download",is_active:!0,selected:"",type:"custom",placeholder:"Role",items:[]},{message:"Hide columns",icon:"settings",filter:"hideColumn",is_active:!0,selected:[],type:"dropdown",placeholder:"Role",items:[]}]}),computed:{...Vuex.mapState(["user","userInfo","userRole","filterResult","learnerInsightsClass","filters"]),attendance(){return this.learnerInsightsClass.data}},watch:{"filterResult.refreshTable":{handler(e,t){e&&this.refreshTable()},deep:!0}},created(){this.manageDefaultFilters()},mounted(){this.getStorage()},methods:{formatDate(e,t){const n=new Date(e);if("0"===e||/^\d+$/.test(e)||isNaN(n.getTime()))return e;return`${["Sun","Mon","Tue","Wed","Thu","Fri","Sat"][n.getDay()]}, ${["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][n.getMonth()]} ${n.getDate()}, ${n.getFullYear()}`},async copyToClipboard(e,t){try{await navigator.clipboard.writeText(t.url),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})}catch(e){console.error("Failed to copy:",e),this.$buefy.toast.open({duration:1e3,message:"Failed to copy!",position:"is-bottom"})}},initAction(e,t,n){if("clickToCopy"===t.slug)n.preventDefault(),this.copyToClipboard(e,t);this.$emit("initAction",e,t,n)},recordingActions(e,t){const n={id:e.recording},o=encodeURI(JSON.stringify(n));return 0!==e.recording.length&&[{label:"Open in new tab",is_active:!0,slug:"openInNewTab",active_class:"material-icons-outlined",url:YUNOCommon.config.host()+"/recording/?videoID="+o+"&class_id="+e.id,link_target:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"open_in_new"}},{label:"Click to copy",is_active:!0,slug:"clickToCopy",active_class:"material-icons-outlined",url:YUNOCommon.config.host()+"/recording/?videoID="+o+"&class_id="+e.id,link_target:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"file_copy"}}]},isDrawerCol(e,t){if(YUNOCommon.findInArray(e,t))return t},manageDrawer(e,t){this.$emit("manageDrawer",e,t)},getClassDetail(e){this.fetchClassDetails(e.class_id,e.user_id)},gotClassDetails(e){const{response:t}=e;if(200===t?.data?.code){const{data:e}=t;e?.is_absent&&(this.learnerInsightsClass.errorData=t.data.message)}},fetchClassDetails(e,t){Object.assign(this.learnerInsightsClass,{success:!1,error:null,data:[]});const n={apiURL:YUNOCommon.config.learnerInsightsClassAPI(e,t,!0),module:"gotData",store:"learnerInsightsClass",callback:!0,callbackFunc:e=>this.gotClassDetails(e)};this.$store.dispatch("fetchData",n)},manageAPIURL(){return"/wp-json/"+YUNOCommon.getFromString(YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type,this.$props.apiURL.role,this.$props.apiURL.view,this.filterResult.limit,this.filterResult.offset),/wp-json\/(.*)/)},onHideColItemSelect(e,t){e.is_active?e.is_active=!1:e.is_active=!0,this.setStorage(!0)},onHideColChange(e,t){},manageMouse(e,t){e.active_class="over"===t?e.icon.hover:e.icon.class},activeOrg(){return this.userInfo.data.current_state.org_id||null},getStorage(){const e=this.$props.sessionStorage;let t=Number(JSON.parse(JSON.stringify(e.version))),n=e.name+"V"+--t;sessionStorage.removeItem(n);const o=sessionStorage.getItem(e.name+"V"+e.version);if(null!==o){const e=JSON.parse(o);if(this.filterResult.payload=e.payload,this.filters.data=e.filters,this.filters.success=!0,"org-admin"===this.userRole.data){const e=this.activeOrg();["org_id","org"].forEach((t=>{t in this.filterResult.payload&&(this.filterResult.payload[t]=e)}))}this.gridActions=e.gridActions,this.filterResult.currentPage=e.currentPage,this.filterResult.offset=e.offset,this.fetchGrid(!0)}else this.fetchFilters(!0),this.fetchGrid(!0)},setStorage(e){const t=this.$props.sessionStorage,n={payload:this.filterResult.payload,filters:this.filters.data,gridActions:this.gridActions,currentPage:this.filterResult.currentPage,offset:this.filterResult.offset,currentTab:this.filterResult.tabs.activeTab};setTimeout((()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(n))}),100)},manageDefaultFilters(){this.defaultFilters=JSON.parse(JSON.stringify(this.$props.payload))},onClearFilter(e,t){this.$props.payload[t.filter]=this.defaultFilters[t.filter],t.selected=this.defaultFilters[t.filter],this.resetTable(),this.fetchGrid(!0)},onDropdownChange(e,t){this.resetTable(),this.fetchGrid(!0)},onManageFilters(){this.isFilterMobile=!this.isFilterMobile},resetTable(){this.filterResult.currentPage=1,this.filterResult.error=null,this.filterResult.data=[],this.filterResult.success=!1,this.filterResult.offset=0,this.filterResult.payload.offset=this.filterResult.offset},sortPressed(e,t,n){const o=this.$props.payload;o.sorting.column=e,o.sorting.type=t,this.filterResult.isLoadMore=!0,this.fetchGrid(!1)},refreshTable(){this.filterResult.isLoadMore=!0,this.fetchGrid(!1)},pageChange(e){this.filterResult.currentPage=e,this.filterResult.isLoadMore=!0,this.filterResult.offset=Math.floor(this.filterResult.limit*e-this.filterResult.limit),this.$emit("onPageChange"),this.fetchGrid(!1)},manageColumnsVisibility(e){const t=YUNOCommon.findObjectByKey(this.gridActions,"filter","hideColumn");if(0!==t.selected.length)for(const n of t.selected){const t=e.find((e=>e.field.parent===n));t&&(t.is_active=!1)}},onQuerySearch(e,t,n){this.$emit("onQuerySearch",e,t,n),n&&(this.resetTable(),this.fetchGrid(!0))},onSearchInput(e,t){this.resetTable(),this.fetchGrid(!0)},parseColumnField(e){if(-1!==e.indexOf(",")){let t=e.split(",").map((e=>e.trim())),n="",o={};t.forEach(((e,t)=>{let a=e.split(".");if(0===t&&(n=a[0]),1===a.length)o[a[0]]=[];else if(2===a.length){let e=a[1];o[e]||(o[e]=[])}else if(a.length>=3){let e=a[1],t=a.slice(2).join(".");o[e]||(o[e]=[]),-1===o[e].indexOf(t)&&o[e].push(t)}}));let a=[];for(let e in o)o[e].length>0?a.push({field:e,sub_child:o[e]}):a.push(e);return{parent:n,child:a}}if(-1!==e.indexOf(".")){let t=e.split(".");return t.length>=3?{parent:t[0],child:[{field:t[1],sub_child:t.slice(2)}]}:{parent:t[0],child:t.slice(1)}}return{parent:e,child:[]}},manageColumns(e){for(let t=0;t<e.length;t++){const n=e[t];n.field=this.parseColumnField(n.field),n.is_active=!0}},objectToQueryString:e=>new URLSearchParams(e).toString(),gotGrid(e){this.filterResult.isLoadMore=!1,this.filterResult.loading=!1,this.filterResult.refreshTable=!1;const{code:t,data:n,count:o}=e.response?.data||{};201!==t&&200!==t||(this.manageColumns(n.columns),this.manageColumnsVisibility(n.columns),this.$emit("gotGrid",n),this.filterResult.count=o,this.filterResult.data=n)},fetchGrid(e){this.setStorage(),this.isFilterMobile=!1,this.filterResult.loading=e;const t={view:this.$props.apiURL.options.view,params:`?${this.objectToQueryString(this.filterResult.payload)}`};let n={apiURL:YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type,t),module:"gotData",store:"filterResult",moduleLoading:e,callback:!0,addToModule:!1,callbackFunc:e=>this.gotGrid(e)};"POST"===this.$props.apiMethodType&&(n.payload=this.filterResult.payload,n.headers={accept:"application/json","content-type":"application/json"}),this.$store.dispatch("POST"===this.$props.apiMethodType?"postData":"fetchData",n)},gotFilters(e){const{code:t,data:n}=e.response?.data||{};201!==t&&200!==t||(this.$emit("gotFilters",n),this.filters.data=n)},fetchFilters(e){let t={apiURL:YUNOCommon.config[this.$props.filterAPIURL.configMethod](this.$props.filterAPIURL.type,this.$props.filterAPIURL.options),module:"gotData",store:"filters",moduleLoading:e,callback:!0,addToModule:!1,callbackFunc:e=>this.gotFilters(e)};this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-tabs-v2",{props:{animated:{type:Boolean,default:!1},destroyOnHide:{type:Boolean,default:!1}},template:'\n        <section class="yunoTabsWrapper" :class=[filterResult.tabs.wrapperClass]>\n            <b-tabs \n                class="yunoTabsV5" \n                v-model="filterResult.tabs.activeTab" \n                @input="tabChange" \n                :animated="animated"\n                :destroy-on-hide="destroyOnHide"\n            >\n                <b-tab-item \n                    v-for="(tab, i) in filterResult.tabs.items"\n                    :visible="tab.isVisible"\n                    :key="i"\n                    :label="tab.label">\n                    <slot :name="tab.slug"></slot>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user","filterResult"])},async created(){},destroyed(){},mounted(){},methods:{tabChange(e){this.$emit("tabChange",e)}}}),Vue.component("yuno-all",{props:{storage:{type:Object,required:!0},apiURL:{type:Object,required:!0},filterAPIURL:{type:Object,required:!0}},template:'\n        <div>   \n            <b-loading :is-full-page="true" v-model="isLoading" :can-cancel="false"></b-loading>\n            <yuno-table-grid\n                :apiURL="apiURL"\n                :filterAPIURL="filterAPIURL"\n                apiMethodType="GET"\n                :payload="filterResult.payload"\n                :sessionStorage="storage"\n                emptyStateImg="/assets/images/group.png"\n                @onRefineFilters="onRefineFilters"\n                @onQuerySearch="onQuerySearch"\n                @onSearchInput="onSearchInput"\n                @manageDrawer="manageDrawer"\n                @gotFilters="gotFilters"\n                @onPageChange="onPageChange"\n                @gotGrid="onGotGrid"\n                @initAction="initAction"\n                recordFoundCaption="Enrollments found"\n            >\n            </yuno-table-grid>\n            <b-modal \n                :active.sync="drawerModal.modal" \n                :width="drawerModal.width" \n                animation="slide-out"\n                :can-cancel="[\'escape\', \'x\']"\n                :on-cancel="closeDrawer"\n                class="yunoModal drawerModal">\n                    <template v-if="drawerModal.modal">\n                        <div class="scrollable">\n                            <h2 class="drawerTitle">\n                                {{ drawerModal.data.learner.full_name }}\n                            </h2>\n                            <yuno-learner-attendance v-if="drawerModal.type === \'attendance\'" :data="drawer" @getClassDetail="onGetClassDetail"></yuno-learner-attendance>\n                        </div>\n                    </template>\n            </b-modal>\n            <b-modal \n                :active.sync="changeBatchModal.modal" \n                :width="500" \n                :can-cancel="[\'escape\', \'x\']"\n                :on-cancel="changeBatchClose"\n                class="yunoModal lightTheme">\n                    <template v-if="changeBatchModal.modal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Change Batch</h2>\n                        </div>\n                        <validation-observer tag="div" ref="changeBatchObserver" v-slot="{ handleSubmit }">\n                            <form id="changeBatchForm" @submit.prevent="handleSubmit(initChangeBatch)">\n                                <div class="modalBody">\n                                    <b-field label="New Batch">\n                                        <validation-provider :rules="{required:true, isBatchSelected:filteredBatchObj.length}" v-slot="{ errors, classes }">\n                                            <b-autocomplete\n                                                :class="classes"\n                                                v-model="changeBatchModal.selectedBatch"\n                                                :data="filteredBatchObj"\n                                                placeholder="Search by name"\n                                                field="name"\n                                                @select="onBatchSelect($event)"\n                                                :clearable="true">\n                                            </b-autocomplete>\n                                            <p class="error">{{errors[0]}}</p>\n                                        </validation-provider>\n                                    </b-field>\n                                    <b-field label="Current Batch">\n                                        <b-input :value="changeBatchModal.data.batch.id" readonly></b-input>\n                                    </b-field>\n                                    <div class="ctaWrapper alignLeft">\n                                        <b-button\n                                            :loading="changeBatchModal.loading"\n                                            :disabled="changeBatchModal.loading" \n                                            native-type="submit"\n                                            class="yunoSecondaryCTA">\n                                            Change Batch\n                                        </b-button>\n                                    </div>        \n                                </div>\n                            </form>\n                        </validation-observer>\n                    </template>\n            </b-modal>\n        </div>\n    ',data:()=>({isLoading:!1,changeBatchModal:{modal:!1,data:[],selectedBatch:"",loading:!1,payload:{new_batch_id:"",action:"CHG_BATCH"}},drawerModal:{modal:!1,data:[],width:1e3,type:""}}),computed:{...Vuex.mapState(["userInfo","filters","filterResult","userRole","drawer","orgAdmin","counsellorList","allCourses","allBatches","learnerInsightsClass"]),filteredBatchObj(){return this.allBatches.data?this.allBatches.data.filter((e=>e.name.toLowerCase().includes(this.changeBatchModal.selectedBatch.toLowerCase()))):[]}},mounted(){this.fetchModules()},methods:{fetchModules(){this.fetchAllBatches()},fetchAllBatches(){this.allBatches.data=[],this.allBatches.success=!1,this.allBatches.error=null;const e={apiURL:YUNOCommon.config.allBatchesAPI(),module:"gotData",store:"allBatches",callback:!1};this.$store.dispatch("fetchData",e)},enrollDone(e,t,n){this.isLoading=!1;const o=e.response?.data,a=o?.message,r=201===o?.code;this.$buefy.toast.open({duration:5e3,message:a,position:"is-bottom",type:r?"is-success":"is-danger"})},initEnroll(e,t){this.isLoading=!0;const n={apiURL:YUNOCommon.config.enrollmentsV4("enrollToggle",{enrollmentID:e.id}),module:"gotData",store:"manageEnroll",payload:{is_unenroll:!!e.is_unenroll},callback:!0,callbackFunc:n=>this.enrollDone(n,t,e)};this.$store.dispatch("putData",n)},initAction(e,t){switch(t.slug){case"changeBatch":this.changeBatchModal.modal=!0,this.changeBatchModal.data=e;break;case"enrollToggle":const n=e.is_unenroll?"Unenroll":"Enroll";this.$buefy.dialog.confirm({title:`${n} User`,message:`Are you sure you want to ${n.toLowerCase()} this user?`,cancelText:"Cancel",confirmText:"Yes",type:"is-danger",onConfirm:()=>this.initEnroll(e,t)})}},changeBatchClose(){const e=this.changeBatchModal;e.data=[],e.selectedBatch="",e.payload.new_batch_id="",this.$refs.changeBatchObserver.reset(),e.modal=!1},onBatchSelect(e){this.changeBatchModal.payload.new_batch_id=e?e.batch_id:""},changeBatchDone(e){this.changeBatchModal.loading=!1;const t=e.response?.data,n=t?.message,o=201===t?.code;this.filterResult.refreshTable=!0,this.$buefy.toast.open({duration:5e3,message:n,position:"is-bottom",type:o?"is-success":"is-danger"}),o&&this.changeBatchClose()},initChangeBatch(){const e=this.changeBatchModal;e.loading=!0;const t={apiURL:YUNOCommon.config.enrollmentsV4("changeBatch",{enrollmentID:e.data.id}),module:"gotData",store:"changeBatch",payload:e.payload,callback:!0,callbackFunc:e=>this.changeBatchDone(e)};this.$store.dispatch("putData",t)},isFilterAvailable(e){return YUNOCommon.findInArray(e,this.userRole.data)},onGotGrid(e){const t=e.rows;for(let e=0;e<t.length;e++){const n=t[e];n.actions=[{label:"Change Batch",is_active:this.isFilterAvailable(["yuno-admin","org-admin"]),slug:"changeBatch",active_class:"material-icons-outlined",url:!1,link_target:"",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"edit"}},{label:n.is_unenroll?"Unenroll":"Enroll",is_active:this.isFilterAvailable(["yuno-admin","Counselor","org-admin"]),slug:"enrollToggle",active_class:"material-icons-outlined",url:!1,link_target:"",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:n.is_unenroll?"person_remove":"person_add"}}]}},onPageChange(){this.$emit("onPageChange")},onRefineFilters(e){},onQuerySearch(e,t,n){if(n){this.filterResult.payload[t.filter]=t.selected??0}else switch(t.filter){case"course_id":this.fetchCourseSuggestions(e,t);break;case"instructor_id":this.fetchInstructorSuggestions(e,t);break;case"learner_id":this.fetchLearnerSuggestions(e,t)}},onSearchInput(e,t){},gotClassDetails(e){if(200===e.response?.data?.code){e.response.data.data.is_absent&&(this.learnerInsightsClass.errorData=e.response.data.message)}},fetchClassDetails(e,t){this.learnerInsightsClass.success=!1,this.learnerInsightsClass.error=null,this.learnerInsightsClass.data=[];const n={apiURL:YUNOCommon.config.learnerInsightsClassAPI(e,t,!0),module:"gotData",store:"learnerInsightsClass",callback:!0,callbackFunc:e=>this.gotClassDetails(e)};this.$store.dispatch("fetchData",n)},onGetClassDetail(e,t){const n=t.days,o=t.learner.id;n.forEach((e=>{e.hasClicked=!1})),e.hasClicked=!0,this.fetchClassDetails(e.class_ID,o)},gotLearnerDetails(e){e.response},fetchLearnerDetails(e){this.drawer.success=!1,this.drawer.data=[],this.drawer.error=null,this.learnerInsightsClass.success=!1,this.learnerInsightsClass.data=[],this.learnerInsightsClass.error=null;const t={apiURL:YUNOCommon.config.enrollmentClassDetailAPI(e.learner.id,e.batch.id),module:"gotData",store:"drawer",callback:!0,callbackFunc:e=>this.gotLearnerDetails(e)};this.$store.dispatch("fetchData",t)},manageDrawer(e,t){this.drawerModal.data=e,this.drawerModal.modal=!0,this.drawerModal.type="","attendance"===t&&(this.drawerModal.type="attendance",this.fetchLearnerDetails(e))},closeDrawer(){this.drawerModal.modal=!1,this.drawerModal.data=[],this.drawerModal.type=""},activeOrg(){return this.userInfo.data.current_state.org_id||0},gotLearnerSuggestions(e,t){t.loading=!1,200===e.response?.data?.code&&(t.items=e.response.data.data)},fetchLearnerSuggestions(e,t){const n={apiURL:YUNOCommon.config.generic("userSearch",e,"learner",this.activeOrg()),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:e=>this.gotLearnerSuggestions(e,t)};this.$store.dispatch("fetchData",n)},gotInstructorSuggestions(e,t){t.loading=!1,200===e.response?.data?.code&&(t.items=e.response.data.data)},fetchInstructorSuggestions(e,t){const n={apiURL:YUNOCommon.config.generic("userSearch",e,"instructor",this.activeOrg()),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:e=>this.gotInstructorSuggestions(e,t)};this.$store.dispatch("fetchData",n)},gotCourseSuggestions(e,t){if(t.loading=!1,200===e.response?.data?.code){const{course:n}=e.response.data.data;n&&t.items.push(...n)}},fetchCourseSuggestions(e,t){const n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:e=>this.gotCourseSuggestions(e,t)};this.$store.dispatch("fetchData",n)},gotFilters(e){const t=e,n=YUNOCommon.findObjectByKey(t,"filter","org_id"),o=YUNOCommon.findObjectByKey(t,"filter","counselor_id");t.forEach((e=>{switch(e.search_field="",e.current="",e.id_field="",e.filter){case"course_id":e.search_field="title",e.current=e.selected?e.selected.title:"",e.id_field="id";break;case"instructor_id":case"learner_id":e.search_field="name",e.current=e.selected?e.selected.name:"",e.id_field="user_id"}})),this.userRole.data.includes("yuno-admin")&&(this.fetchOrgAdminList(n,"all"),this.fetchCounsellorList(o))},gotOrgAdminList(e,t){t.loading=!1,t.success=!0;const{response:n}=e,{code:o,data:a}=n?.data||{};200!==o&&201!==o||(a||[]).forEach((e=>{e.organisation_name&&t.items.push({filter:t.filter,label:e.organisation_name,slug:e.id})}))},fetchOrgAdminList(e,t){e.loading=!0,this.orgAdmin.data=[],this.orgAdmin.success=!1,this.orgAdmin.error=null;const n=`?search=${t}`,o={apiURL:YUNOCommon.config.orgList(n),module:"gotData",store:"orgAdmin",addToModule:!1,callback:!0,callbackFunc:t=>this.gotOrgAdminList(t,e)};this.$store.dispatch("fetchData",o)},gotCounsellors(e,t){t.loading=!1,t.success=!0;const{response:n}=e,{code:o,data:a}=n?.data||{};200!==o&&201!==o||(a||[]).forEach((e=>{t.items.push({filter:t.filter,label:e.name_email,slug:e.id})}))},fetchCounsellorList(e){e.loading=!0,this.counsellorList.data=[],this.counsellorList.success=!1,this.counsellorList.error=null;const t={apiURL:YUNOCommon.config.listOfCounsellorsAPI(),module:"gotData",store:"counsellorList",addToModule:!1,callback:!0,callbackFunc:t=>this.gotCounsellors(t,e)};this.$store.dispatch("fetchData",t)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-enrollments-v4",{template:'\n        <yuno-page-grid\n            :authorizedRoles="authorizedRoles"\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="false"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <div class="mainHeader">\n                        <div class="block">\n                            <h1 class="pageTitle">{{ pageHeader.title }} \n                                <a \n                                    class="refresh" \n                                    @click.prevent="refreshTable()"\n                                    href="#">\n                                    <b-tooltip label="Refresh"\n                                        type="is-dark"\n                                        position="is-right">\n                                        <span class="material-icons">refresh</span>\n                                    </b-tooltip>\n                                </a>\n                            </h1>\n                            <div class="action" v-if="pageHeader.button.isActive">\n                                <template v-if="pageHeader.button.type === \'custom\'">\n                                    <b-button \n                                        @click="pageCTA()"\n                                        class="yunoSecondaryCTA wired">\n                                        {{pageHeader.button.label}}\n                                    </b-button>\n                                </template>\n                                <template v-else>\n                                    <b-button tag="a"\n                                        :href="pageHeader.button.url"\n                                        class="yunoSecondaryCTA wired">\n                                        {{pageHeader.button.label}}\n                                    </b-button>\n                                </template>\n                            </div> \n                        </div>\n                    </div>\n                    <figure class="infiniteSpinner" v-if="manageTabsVisibility(\'loading\')">\n                        <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                    </figure>\n                </div>\n                <yuno-tabs-v2\n                    :destroyOnHide="true"\n                    @tabChange="tabChange"\n                    v-if="manageTabsVisibility(\'success\')"\n                >\n                    <template v-slot:all>\n                        <yuno-all\n                            @tabChanged="onTabChanged"\n                            ref="yunoAll" \n                            :storage="storage"\n                            :apiURL="apiURL"\n                            :filterAPIURL="filterAPIURL"\n                            @onPageChange="onPageChange"\n                        >\n                        </yuno-all>\n                    </template>\n                    <template v-slot:academy>\n                        <yuno-all\n                            @tabChanged="onTabChanged"\n                            ref="yunoAll" \n                            :storage="storage"\n                            :apiURL="apiURL"\n                            :filterAPIURL="filterAPIURL"\n                            @onPageChange="onPageChange"\n                        >\n                        </yuno-all>\n                    </template>\n                </yuno-tabs-v2>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({apiURL:null,filterAPIURL:null,isMiniSidebar:!1,storage:{name:"enrollmentInsightsV4",version:1},authorizedRoles:["yuno-admin","org-admin","Instructor","Counselor"],pageHeader:{title:"Enrollments",button:{label:"New Enrollment",type:"link",url:"/generate-link/",isActive:!0}}}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","loader","filterResult","orgAdmin","filters"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{manageTabsVisibility(e){switch(e){case"loading":return"org-admin"===this.userRole.data&&this.orgAdmin.loading;case"success":switch(this.userRole.data){case"org-admin":return this.orgAdmin.success;case"yuno-admin":case"Instructor":case"Counselor":return!0;default:return!1}default:return!1}},refreshTable(){this.filterResult.refreshTable=!0},gotOrgInfo(e){const{response:{data:{code:t,data:n}={}}={}}=e;if(200===t){n.yuno_academy_subscription.academies.length>0?this.setupTabs("tabsAvailable"):this.setupTabs("tabsNotAvailable")}this.setupTableGrid(this.userRole.data),this.setupFilters()},fetchOrgInfo(){const e={apiURL:YUNOCommon.config.generic("org",!1,!1,this.activeOrg()),module:"gotData",store:"orgAdmin",callback:!0,callbackFunc:this.gotOrgInfo};this.$store.dispatch("fetchData",e)},fetchModules(e){"org-admin"===e?this.fetchOrgInfo():(this.setupTabs("tabsNotAvailable"),this.setupTableGrid(this.userRole.data),this.setupFilters())},onUserInfo(e){this.fetchModules(e.role)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},tabChange(e){this.manageSessionStorage(),this.resetModule(),this.setupTableGrid(this.userRole.data,e),setTimeout((()=>{const t=[this.$refs.yunoUpcomingOngoing,this.$refs.yunoPast][e];t&&t.onTabChanged(e)}),50)},onTabChanged(){},resetModule(){this.filterResult.data=[],this.filterResult.error=null,this.filterResult.errorData=[],this.filterResult.success=!1,this.filterResult.count="",this.filterResult.currentCount="",this.filterResult.offset=0,this.filterResult.payload=[],this.filters.data=[],this.filters.error=null,this.filters.errorData=[],this.filters.success=!1},setupFilters(){const e=JSON.parse(JSON.stringify(this.filterResult.payload));delete e.limit,delete e.offset,this.filterAPIURL={configMethod:"enrollmentsV4",type:"filters",options:{params:`?${this.objectToQueryString(e)}`}}},onPageChange(){this.setupTableGrid(this.userRole.data)},setupTableGrid(e,t){const n={user_id:isLoggedIn,learner_id:0,status:"all",course_id:0,limit:this.filterResult.limit,offset:this.filterResult.offset},o={Counselor:{...n,referrals:"all"},"org-admin":n,Instructor:n,"yuno-admin":{...n,referrals:"all",instructor_id:0,org_id:0,counselor_id:0,payment_status:"all"}},a=this.filterResult.tabs.items[this.filterResult.tabs.activeTab];"academy"===a.slug&&"org-admin"===e&&(o["org-admin"].academy_id=0,o["org-admin"].org_id=this.activeOrg()),"all"===a.slug&&"org-admin"===e&&(o["org-admin"].org_id=this.activeOrg()),this.filterResult.payload=o[e]||n,this.apiURL={configMethod:"enrollmentsV4",type:"list",options:{view:"grid",params:this.filterResult.payload}}},objectToQueryString:e=>new URLSearchParams(e).toString(),manageSessionStorage(){const e=this.storage;sessionStorage.getItem(e.name+"V"+e.version)&&sessionStorage.removeItem(e.name+"V"+e.version)},setupTabs(e){function t(e,t,n,o){return{label:e,slug:t,isActive:!1,isVisible:o,class:n}}const n=this.storage,o=sessionStorage.getItem(n.name+"V"+n.version);let a="";a=o?JSON.parse(o).currentTab:0,this.filterResult.tabs={activeTab:a,wrapperClass:e,items:[t("All","all","yunoAll",!0),t("My Academy","academy","yunoAcademy",!0)]}}}}),Vue.component("yuno-learner-attendance",{props:["data","options"],template:'\n        <div>\n            <template v-if="data.loading">\n                <div class="loaderWrapper">\n                    <div class="smallLoader"></div>\n                </div>\n            </template>\n            <template v-if="data.success">\n                <template v-if="data.error">\n                    {{ data.errorData }}\n                </template>\n                <template v-else>\n                    <div class="classDate">\n                        {{ data.data.attendance_detail.class_date }}\n                        <a \n                            class="action" \n                            v-if="false"\n                            @click.prevent="createCSV(\'batchenrollmentevent\')"\n                            href="#">\n                            <b-tooltip label="Download CSV"\n                                type="is-dark"\n                                position="is-bottom">\n                                <span class="material-icons">file_download</span>\n                            </b-tooltip>\n                        </a>\n                    </div>\n                    <div class="intro">\n                        <p class="introLabel">Course</p>\n                        <h2 class="primaryTitle">{{ data.data.attendance_detail.course_title }}</h2>\n                        <p class="introLabel">Batch</p>\n                        <h3 class="secondaryTitle">{{ data.data.attendance_detail.batch_name }}</h3>\n                    </div>\n                    <ul class="stats">\n                        <li v-for="(item, i) in data.data.attendance_detail.stats" :key="i">\n                            <div class="itemValue">{{ item.value }}</div>\n                            <div class="itemLabel">{{ item.label }}</div>\n                        </li>\n                    </ul>\n                    <div class="classDetail">\n                        <template v-if="learnerInsightsClass.loading">\n                            <div class="loaderWrapper">\n                                <div class="smallLoader"></div>\n                            </div>\n                        </template>\n                        <template v-if="learnerInsightsClass.success">\n                            <template v-if="learnerInsightsClass.error">\n                                <p class="inform">{{learnerInsightsClass.errorData}}</p>\n                            </template>\n                            <template v-else>\n                                <p class="inform" v-if="learnerInsightsClass.data.is_absent !== undefined && learnerInsightsClass.data.is_absent">{{learnerInsightsClass.errorData}}</p>\n                                <p class="classDateV2">{{ learnerInsightsClass.data.class_date }}</p>\n                                <p class="classTitle">{{ learnerInsightsClass.data.class_title }}</p>\n                                <ul class="classInfo">\n                                    <li>\n                                        <figure>\n                                            <img :src="learnerInsightsClass.data.instructor.image" :alt="learnerInsightsClass.data.instructor.name">\n                                            <figcaption>\n                                                <p class="itemLabel">Instructor</p>\n                                                <p class="itemValue">{{ learnerInsightsClass.data.instructor.name }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </li>\n                                    <li>\n                                        <p class="itemLabel">Start Time</p>\n                                        <p class="itemValue">{{ learnerInsightsClass.data.class_start_time }}</p>\n                                    </li>\n                                    <li>\n                                        <p class="itemLabel">Duration</p>\n                                        <p class="itemValue">{{ learnerInsightsClass.data.class_duration }}</p>\n                                    </li>\n                                </ul>\n                                <div class="barWrapper">\n                                    <div class="barHeader">\n                                        <p class="classDateV2">Attendence Duration</p>\n                                        <p class="barValue">{{learnerInsightsClass.data.attendence_duration.value}}</p>\n                                    </div>\n                                    <b-progress \n                                        :type="{\n                                            \'is-red\': learnerInsightsClass.data.attendence_duration.percentage <= 30,\n                                            \'is-orange\': learnerInsightsClass.data.attendence_duration.percentage > 30,\n                                            \'is-yellow\': learnerInsightsClass.data.attendence_duration.percentage > 50,\n                                            \'is-lightGreen\': learnerInsightsClass.data.attendence_duration.percentage > 70,\n                                            \'is-darkGreen\': learnerInsightsClass.data.attendence_duration.percentage > 80\n                                        }"  \n                                        format="percent"    \n                                        :value="Number(learnerInsightsClass.data.attendence_duration.percentage)">\n                                    </b-progress>\n                                </div>\n                            </template>\n                        </template>\n                        <template v-else>\n                            <p class="inform" v-if="!learnerInsightsClass.loading">{{ data.data.attendance_detail.message.before_class_select }}</p>\n                        </template>\n                    </div>\n                    <div class="classes">\n                        <p class="classesLabel">Classes ({{ data.data.attendance_detail.days.length }})</p>\n                        <ul class="days">\n                            <li \n                                v-for="(day, i) in data.data.attendance_detail.days" \n                                :class="[day.hasAttended ? \'active\' : \'disable\', day.hasClicked ? \'isClicked\' : \'\']"\n                                :key="i">\n                                <div class="disc" @click="getClassDetail(day, data.data.attendance_detail)">{{ day.label }}</div>\n                            </li>\n                        </ul>\n                    </div>\n                </template>\n            </template>\n        </div>  \n    ',data:()=>({}),computed:{...Vuex.mapState(["learnerInsightsClass"])},async created(){},mounted(){},methods:{getClassDetail(e,t){this.$emit("getClassDetail",e,t)}}}),Vue.component("yuno-my-academy",{props:{storage:{type:Object,required:!0},apiURL:{type:Object,required:!0}},template:"\n        <div>   \n            hhhh\n        </div>\n    ",data:()=>({}),computed:{...Vuex.mapState(["userInfo","filters","filterResult","userRole","drawer"])},async created(){},mounted(){},methods:{onTabChanged(e){}}});