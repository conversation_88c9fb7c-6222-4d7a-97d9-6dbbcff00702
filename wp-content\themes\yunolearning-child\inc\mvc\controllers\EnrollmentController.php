<?php

namespace V4;

/**
 * Class EnrollmentController
 * Handles enrollment-related operations.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class EnrollmentController extends Controller
{
    /**
     * Constructor for EnrollmentController.
     * Loads required libraries and models.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('validate');
        $this->loadModel('category');
        $this->loadModel('user');
        $this->loadModel('batch');
        $this->loadModel('enrollment');
        $this->loadModel('payment');
        $this->loadModel('referral');
        $this->loadModel('learner');
        $this->loadModel('course');
        $this->loadModel('org');
        $this->loadModel('academy');
        $this->loadModel('instructor');
        $this->loadModel('counselor');
    }

    /**
     * Adds a new enrollment.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     * @throws Exception If an error occurs during enrollment.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function addEnrollment($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/new_enrollment.log";
            error_log("addEnrollment - Request received at " . date("Y-m-d H:i:s") . " === " . $request->get_body() . "\n\n", 3, $logFile);
            $request = json_decode($request->get_body(), true);

            // Validation
            $validationChecks = [
                'user_id' => 'numeric',
                'course_id' => 'numeric',
                'batch_id' => 'numeric',
                'payment_gateway' => 'string',
                'payment_mode' => 'string',
                'amount' => 'numeric',
                'currency_code' => '/^(INR|USD|AED)$/',
                'enrolled_by' => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            $userId = $request['user_id'];
            $courseId = $request['course_id'];
            $batchId = $request['batch_id'];

            // Log initial extracted data
            error_log("addEnrollment - Extracted request data at " . date("Y-m-d H:i:s") . " === " . json_encode($request) . "\n\n", 3, $logFile);

            $referralCodeStatus = $request['referral_code_status'];
            // Validate referral code
            $referrerId = $this->referralModel->validateReferralCode($referralCodeStatus, $userId);
            error_log("addEnrollment - Referral validation at " . date("Y-m-d H:i:s") . " === Referrer ID: " . $referrerId . "\n\n", 3, $logFile);

            if ($referralCodeStatus === "applied" && $referrerId === false) {
                // Referral code is applied but invalid or disabled
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid or disabled referral code']);
            }

            // Check if user is already enrolled (from CPT or Elasticsearch)
            $enrollmentPosts = $this->enrollmentModel->isUserAlreadyEnrolled($userId, $courseId, $batchId, 'POST');
            error_log("addEnrollment - enrollmentPosts - isUserAlreadyEnrolled " . date("Y-m-d H:i:s") . " === " . json_encode($enrollmentPosts) . "\n\n", 3, $logFile);
            $enrollmentPostsStatus = $enrollmentPosts['status'];

            $enrollmentES = $this->enrollmentModel->isUserAlreadyEnrolled($userId, $courseId, $batchId, 'ES');
            error_log("addEnrollment - enrollmentES - isUserAlreadyEnrolled " . date("Y-m-d H:i:s") . " === " . json_encode($enrollmentES) . "\n\n", 3, $logFile);
            $enrollmentEsStatus = $enrollmentES['enrollment_status'];

            // Check if user is already enrolled using the model
            if ($enrollmentPostsStatus === "ACTIVE" && $enrollmentEsStatus === "ACTIVE") {
                error_log("addEnrollment - User is already enrolled in this course and batch " . date("Y-m-d H:i:s") . "\n\n", 3, $logFile);
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'User is already enrolled in this course and batch.']);
            }

            // Fetch all batches in which the user is already enrolled for this course (from CPT and Elasticsearch)
            $enrolledBatches = $this->enrollmentModel->getUserEnrolledBatches($userId, $courseId);
            error_log("addEnrollment - enrolledBatches - getUserEnrolledBatches " . date("Y-m-d H:i:s") . " === " . json_encode($enrolledBatches) . "\n\n", 3, $logFile);

            // If the user is enrolled in ANY batch for this course, block enrollment
            if (!empty($enrolledBatches)) {
                error_log("addEnrollment - enrolledBatches - Enrollment Blocked! User is already enrolled in a course (Course ID: $courseId)\n\n", 3, $logFile);
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'You are already enrolled in this course and cannot enroll again.']);
            }

            // Fetch batch details
            $wpBatches = $this->batchModel->getBatchDetails($batchId); // models
            error_log("addEnrollment - Batch details fetched at " . date("Y-m-d H:i:s") . " === " . json_encode($wpBatches) . "\n\n", 3, $logFile);
            if ($wpBatches === false) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Batch not found']);
            } else {
                // Validate batch duration
                if (empty($wpBatches['duration']) || !is_numeric($wpBatches['duration'])) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid duration']);
                }
            }

            // check batch locked
            $isBatchLocked = $this->batchModel->validateBatchLock($wpBatches); // models
            error_log("addEnrollment - Batch lock check at " . date("Y-m-d H:i:s") . " === Locked: " . ($isBatchLocked ? 'Yes' : 'No') . "\n\n", 3, $logFile);
            if ($isBatchLocked === false) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'This batch is locked, please select another batch']);
            }

            $errorMessage = ($wpBatches['enrollmentType'] === 'fixed') ? 'Batch has already started, please select another batch and try again' : 'Enrollment is not allowed in this batch. Please update the end date of the batch and try again';
            // Validate batch enrollment
            $isValid = $this->batchModel->validateBatchEnrollment($wpBatches, $batchId);
            error_log("addEnrollment - isValid " . date("Y-m-d H:i:s") . " === " . json_encode($isValid) . "\n\n", 3, $logFile);

            if (!$isValid) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => $errorMessage]);
            }

            error_log("addEnrollment - before - enrollmentPosts -Enrollment POSTT entry created at " . date("Y-m-d H:i:s") . " === ID: " . $enrollmentPosts . "\n\n", 3, $logFile);
            if ($enrollmentPosts === false) {
                // Insert data into the custom post type
                $enrollmentPostId = $this->enrollmentModel->addCustomPostEnrollment($request, $wpBatches);
                error_log("addEnrollment - enrollmentPostId -- Enrollment POST entry created at " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentPostId) . "\n\n", 3, $logFile);

                if ($enrollmentPostId === false) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Enrollment Failed. (C) Try Again!']);
                }
            }

            error_log("addEnrollment - before - enrollmentES -Enrollment ES entry created at " . date("Y-m-d H:i:s") . " === ID: " .  json_encode($enrollmentES) . "\n\n", 3, $logFile);
            if (
                $enrollmentES === false ||
                $enrollmentES === null ||
                (is_array($enrollmentES) && empty($enrollmentES))
            ) {
                // Insert Enrollment into Elasticsearch
                $enrollmentEsRes = $this->enrollmentModel->addEnrollment($request, $wpBatches, $enrollmentPostId, $referrerId);
                error_log("addEnrollment - after - enrollmentEsRes -Enrollment ES entry created at " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsRes) . "\n\n", 3, $logFile);

                if ($enrollmentEsRes === false) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Enrollment Failed. (E) Try Again!']);
                }
            }

            error_log("addEnrollment - after - enrollmentPosts - enrollmentEsRes -Enrollment ES entry created at " . date("Y-m-d H:i:s") . " === ID: "  . json_encode($enrollmentPosts) . ' -- ' . json_encode($enrollmentEsRes) . "\n\n", 3, $logFile);

            $existingPostPayment = $this->paymentModel->checkIfPaymentExistsInCPT($userId, $courseId, $batchId);

            error_log("addEnrollment - before - existingPostPayment  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($existingPostPayment) . "\n\n", 3, $logFile);

            if ($existingPostPayment) {
                // Payment already exists in the custom post type
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Payment already exists!']);
            } else {
                // Proceed to add payment
                // Handle Payment Processing via CPT and Elasticsearch
                $paymentPostId = $this->paymentModel->addCustomPostPayment($request, $wpBatches, $enrollmentPostId);

                error_log("addEnrollment - after - paymentPostId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentPostId) . "\n\n", 3, $logFile);

                if ($paymentPostId === false) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Payment Failed (C). Try Again !']);
                }
            }

            $existingEsPayment = $this->paymentModel->getPaymentByEs($userId, $courseId, $batchId, $paymentPostId);
            error_log("addEnrollment - before - existingEsPayment  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($existingEsPayment) . "\n\n", 3, $logFile);

            // Step 1: Check for Existing Document
            if (!empty($existingEsPayment)) {
                return $this->response->error('DUPLICATE_PAYMENT', ['message' => 'Duplicate payment entry exists in Elasticsearch.']);
            } else {
                $paymentEsRes = $this->paymentModel->addPayment($request, $wpBatches, $paymentPostId, $enrollmentPostId);

                error_log("addEnrollment - after - existingEsPayment  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($existingEsPayment) . "\n\n", 3, $logFile);

                if ($paymentEsRes === false) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Payment Failed (E). Try Again !']);
                }
            }

            // Check all payments
            error_log("addEnrollment - all payment - paymentTblId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($existingEsPayment) . "\n\n", 3, $logFile);
            error_log("addEnrollment - all payment - paymentPostId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentPostId) . "\n\n", 3, $logFile);
            error_log("addEnrollment - all payment - paymentEsRes  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsRes) . "\n\n", 3, $logFile);

            $paymentGateway = $request['payment_gateway'];
            // Payment gateway ( e.g., RazorPay )
            $paymentMode = $request['payment_mode'];
            $enrolledBy = $request['enrolled_by'];
            //yuno admin // org admin 

            if ($paymentPostId > 0 && isset($paymentEsRes['status_code']) && $paymentEsRes['status_code'] == 201) {
                error_log("addEnrollment - all payment afters checks - paymentEsRes  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsRes) . "\n\n", 3, $logFile);

                if ($paymentMode === 'offline' && $paymentGateway === 'offline') {

                    error_log("addEnrollment - calll  - handleOfflinePayment  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsRes) . "\n\n", 3, $logFile);

                    $offlinePaymentResult = $this->paymentModel->handleOfflinePayment($request, $wpBatches, $paymentPostId, $paymentEsRes, $enrollmentPostId, $enrollmentEsRes);

                    error_log("addEnrollment  - handleOfflinePayment " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($offlinePaymentResult) . "\n\n", 3, $logFile);

                    if ($offlinePaymentResult) {

                        error_log("addEnrollment  - after - offlinePaymentResult " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentEsRes) . "\n\n", 3, $logFile);

                        $event_details = [
                            'user_id'   => $enrolledBy,
                            'action'    => 'enrollment',
                            'target'    => 'enrollment',
                            'target_id' => $enrollmentEsRes['body']['_id'],
                            'timestamp' => $this->dt->currentSystemDT()
                        ];

                        error_log("addEnrollment  - event_details -- Course Analytics " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($event_details) . "\n\n", 3, $logFile);

                        trigger_custom_event($event_details);
                        $enrollmentActionsResponse = $this->postEnrollmentActions($request, $wpBatches, $enrollmentPostId);

                        error_log("addEnrollment - enrollmentActionsResponse " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentActionsResponse) . "\n\n", 3, $logFile);

                        error_log("addEnrollment - enrollmentActionsResponse -- postEnrollmentActions " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentActionsResponse) . "\n\n", 3, $logFile);

                        return $this->response->success('POST_INSERT', ['enrollment_id' => $enrollmentPostId], ['message' => 'Offline payment processed successfully.']);

                    } else {

                        error_log("addEnrollment - enrollmentActionsResponse -- postEnrollmentActions " . date("Y-m-d H:i:s") . " === Offline: elseeeeeeeeeee " . json_encode($response) . "\n\n", 3, $logFile);

                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to process offline payment.']);
                    }
                } elseif ($paymentMode === 'online' && $paymentGateway === 'generate_link') {

                    error_log("addEnrollment - condition online " . date("Y-m-d H:i:s") . " === Link: " . $paymentMode . " " . $paymentGateway . "\n\n", 3, $logFile);
                    $paymentLink = $this->paymentModel->handleOnlinePayment($request, $wpBatches, $enrollmentPostId, $paymentPostId);
                    error_log("addEnrollment - Payment link generated at " . date("Y-m-d H:i:s") . " === Link: " . json_encode($paymentLink) . "\n\n", 3, $logFile);

                    if ($paymentLink) {
                        return $this->response->success('POST_INSERT', ['enrollment_id' => $enrollmentPostId, 'payment_link' => $paymentLink], ['message' => 'Payment link generated successfully.']);
                    } else {
                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to generate payment link.']);
                    }
                } elseif ($paymentMode === 'online' && $paymentGateway === 'direct_payment') {
                    error_log("addEnrollment - condition online " . date("Y-m-d H:i:s") . " === Payment Payload: " . $paymentMode . " " . $paymentGateway . "\n\n", 3, $logFile);
                    $razorpayCheckoutPayload  = $this->paymentModel->handleDirectOnlinePayment($request, $wpBatches, $enrollmentPostId, $paymentPostId);
                    error_log("addEnrollment -Payment Payload generated at " . date("Y-m-d H:i:s") . " === Payment Payload: " . json_encode($razorpayCheckoutPayload) . "\n\n", 3, $logFile);
                    if ($razorpayCheckoutPayload) {
                        error_log("addEnrollment - Payment Payload generated at " . date("Y-m-d H:i:s") . " === Payment Payload: " . json_encode($razorpayCheckoutPayload) . "\n\n", 3, $logFile);
                        return $this->response->success('POST_INSERT', $razorpayCheckoutPayload, ['message' => 'Payment Payload generated successfully.']);
                    } else {
                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to generate payload for Checkout.']);
                    }
                }
            } else {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Enrollment failed.']);
            }
            error_log("addEnrollment - Last " . date("Y-m-d H:i:s") . " === Link: " . $paymentMode . "\n\n", 3, $logFile);
            error_log("addEnrollment - Enrollment and payment processed successfully at " . date("Y-m-d H:i:s") . "\n\n", 3, $logFile);
        } catch (Exception $e) {
            return $this->response->error('POST_INSERT_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Performs post-enrollment actions like notifications and linking.
     *
     * @param object $request HTTP request object.
     * @param array  $wpBatches Batch details.
     * @param int    $enrollmentTblId Enrollment table ID.
     * @return bool Success or failure.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function postEnrollmentActions($request, $wpBatches, $enrollmentPostId)
    {
        $logFile = ABSPATH . "error-logs/new_enrollment_actions.log";
        // Update Org Details
        $orgLink = $this->learnerModel->linkOrgDetails($request);

        error_log("addEnrollment - inside postEnrollmentActions -- orgLink" . date("Y-m-d H:i:s") . " === Offline: " . json_encode($orgLink) . "\n\n", 3, $logFile);

        // Link Learner to Instructor
        $isLearnerAdded = $this->learnerModel->addLearnerToInstructor($request, $wpBatches);
        error_log("addEnrollment - Debugging isLearnerAdded: postEnrollmentActions " . date("Y-m-d H:i:s") . var_export($isLearnerAdded, true) . $isLearnerAdded . "\n\n", 3, $logFile);

        // if (!$isLearnerAdded) {
        //     return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to add learner to instructor or relationship already exists.']);
        // }

        // Trigger external API calls (e.g., Segment)
        $segmentCall = $this->learnerModel->triggerSegmentCall($enrollmentPostId);
        error_log("addEnrollment - Debugging segmentCall: postEnrollmentActions " . date("Y-m-d H:i:s") . var_export($segmentCall, true) . $segmentCall . "\n\n", 3, $logFile);
        return true;
    }

    /**
     * Retrieves enrollments based on filters.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getEnrollments($request)
    {
        // Extract query string parameters
        $userId = isset($_GET['user_id']) ? (int) $_GET['user_id'] : 0;
        $instructorId = isset($_GET['instructor_id']) ? (int) $_GET['instructor_id'] : 0;
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';
        $counselorId = isset($_GET['counselor_id']) ? (int) $_GET['counselor_id'] : 0;
        $learnerId = isset($_GET['learner_id']) ? (int) $_GET['learner_id'] : 0;
        $courseId = isset($_GET['course_id']) ? (int) $_GET['course_id'] : 0;
        $organizationId = isset($_GET['org_id']) ? (int) $_GET['org_id'] : 0;
        $academyId = isset($_GET['academy_id']) ? (int) $_GET['academy_id'] : 0;
        $referrals = isset($_GET['referrals']) ? $_GET['referrals'] : 'all';
        $paymentStatus = isset($_GET['payment_status']) ? $_GET['payment_status'] : 'all';
        $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 10; // Default limit
        $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0; // Default offset

        $viewType = isset($request['viewType']) ? $request['viewType'] : 'all';

        // Validation rules
        $validationChecks = [
            'user_id' => 'numeric',                  // Must be numeric
            //'instructor_id' => 'numeric',            // Must be numeric
            //'org_id' => 'numeric',                   // Must be numeric
           // 'referrals' => 'string',   // Must match one of these values
            //'learner_id' => 'numeric',               // Must be numeric
           // 'status' => 'string', // Must match one of these values
            // 'counselor_id' => 'numeric',             // Must be numeric
           // 'payment_status' => 'string', // Must match one of these values
            //'course_id' => 'numeric',                // Must be numeric
            'limit' => 'numeric',                    // Must be numeric
        ];

        // Perform validation on each field
        foreach ($validationChecks as $key => $type) {
            $result = $this->validate->validateRequired($request, $key, $type);
            if (is_wp_error($result)) {
                return $result; // Return the error immediately if validation fails
            }
        }

        if ($userId <= 0) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
        }

        // Fetch User Role
        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);


        // Role-Based Validation
        switch ($role) {
            case 'org-admin':
                if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $referrals !== 'all') {
                    return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
                }
                break;

            case 'counselor':
                if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $learnerId !== 0) {
                    return $this->response->error('ACCESS_DENIED', ['message' => 'Counselors can only filter by course, learners, referrals, and enrollment status']);
                }
                break;

            case 'instructor':
                if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $organizationId !== 0 || $referrals !== 'all') {
                    return $this->response->error('ACCESS_DENIED', ['message' => 'Instructors can only filter by course, learners, and enrollment status']);
                }
                break;

            case 'yuno-admin':
                // No restrictions, yuno-admin has full access
                break;

            default:
                return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
        }

        // Validation checks
        if ($userId <= 0) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
        }

        $mustConditions = [];
        $mustNotConditions = [];

        if (isset($instructorId) && $instructorId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.instructor_id" => $instructorId
                ]
            ];
        }

        if (isset($organizationId) && $organizationId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.org_admin.id" => $organizationId
                ]
            ];
        }

        if (isset($courseId) && $courseId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.course_id" => $courseId
                ]
            ];
        }

        if (isset($learnerId) && $learnerId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.user_id" => $learnerId
                ]
            ];
        }

        if ($referrals !== "all") {
            if ($instructorId === 0) {
                $mustNotConditions[] = [
                    "match" => [
                        "data.details.refer.referrer.id" => 0
                    ]
                ];
            } else {
                $referrerId = $instructorId;
                $mustConditions[] = [
                    "match" => [
                        "data.details.refer.referrer.id" => $referrerId
                    ]
                ];
            }
        }

        if (isset($counselorId) && $counselorId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.counselor_id" => $counselorId
                ]
            ];
        }

        if ($status !== "all") {
            $enrollmentStatus = ($status === "active") ? "ACTIVE" : "INACTIVE";
            $mustConditions[] = [
                "match" => [
                    "data.details.enrollment_status" => $enrollmentStatus
                ]
            ];
        }

        if ($paymentStatus !== "all") {
            $paymentCondition = "Paid in full";
            if ($paymentStatus === "full") {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.payment_status" => "*" . $paymentCondition . "*"
                    ]
                ];
            } else {
                $mustNotConditions[] = [
                    "match_phrase" => [
                        "data.details.payment_status" => "*" . $paymentCondition . "*"
                    ]
                ];
            }
        }

        $mustNotConditions[] = [
            "match" => [
                "data.details.course_name" => false
            ]
        ];

        $elasticQuery = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.instructor_id",
                "data.details.counselor_id",
                "data.details.payment_status",
                "data.details.user_id",
                "data.details.enrolled_end",
                "data.details.enrolled_days_left",
                "data.details.crm_contact_id",
                "data.details.payment_method",
                "data.details.payment",
                "data.details.payment_description",
                "data.details.amount_pending",
                "data.details.amount_received",
                "data.details.total_amount",
                "data.details.course_id",
                "data.details.enrollment_status",
                "data.details.course_name",
                "data.details.batch_id",
                "data.details.user_id",
                "data.details.event_date",
                "data.details.enrolled_classes_counter",
                "data.details.batch_end_date",
                "data.details.enrollment_type",
                "data.details.personalization",
                "data.details.enrollment_id",
                "data.details.attended_classes_counter",
                "data.details.enrolled_classes_collection",
                "data.details.attended_classes_collection",
                "data.details.enrolled_on",
                "data.details.event_date",
                "data.details.org_admin.id",
                "data.details.org_admin.image",
                "data.details.org_admin.name",
                "data.details.refer.referrer.id",
                "data.details.refer.discount_amount.referrer_discount_percentage",
                "data.details.selling_price",
                "data.details.listing_price",
                "data.details.enrolled_by",
                "data.details.full_part",
                "data.details.is_unenroll",
                "data.details.is_change_batch",
            ],
            "query" => [
                "bool" => [
                    "must" => $mustConditions,
                    "must_not" => $mustNotConditions
                ]
            ],
            "sort" => [
                [
                    "data.details.event_date" => "desc"
                ]
            ]
        ];

        $query['custom'] = $elasticQuery;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];
        //$query['cache'] = true;
        //$logFile = ABSPATH . "error-logs/get_enrollment.log";
        // Step 1: Decode the request body
        //error_log("get_enrollment - Request received at " . date("Y-m-d H:i:s") . " === " . json_encode($elasticQuery) . "\n\n", 3, $logFile);

        $enrollmentDataResponse = $this->enrollmentModel->getEnrollments($query);
        // Debugging the value and type of $enrollmentDataResponse

        if (!empty($enrollmentDataResponse)) {

            if ($viewType === "list") {
                // Return success response with place details
                return $this->response->success('GET_SUCCESS', $enrollmentDataResponse, ['message' => 'Enrollments found']);
            } elseif ($viewType === "grid") {

                if ($role === 'org admin' && isset($request['academy_id'])) {
                    // Override or modify $columns as needed
                    $columns = $this->getColumnsForOrgAdminWithAcademy(); // replace with your actual method
                }

                $columns = $this->getColumnsBasedOnRole($userRole ?? null);
                // Check for org admin and presence of academy_id in the request (even if it's 0)
                
                $enrollmentData = [
                    "rows" =>  $enrollmentDataResponse['data'],
                    "columns" => $columns
                ];
                return $this->response->success('GET_SUCCESS', ['data' => $enrollmentData, 'count' => $enrollmentDataResponse['count']], ['message' => 'Enrollments found']);
            }
        }

        return $this->response->error('GET_FAIL', ['message' => "As your learners enroll in courses, you'll see their enrollments here"]);
    }

    /**
     * Retrieves the column configuration for enrollments based on user role.
     *
     * @since 1.0.0
     * @access private
     * @param string|null $role The role of the user.
     * @return array An array of column configurations for the specified role.
     * <AUTHOR>
     */
    private function getColumnsBasedOnRole($role)
    {
        $columns = [];

        if (isset($role) && $role == 'Instructor') {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "is_active",
                    "label" => "Is Active",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.currency.code, list_price.currency.name, list_price.currency.symbol, list_price.currency.symbol_html",
                    "label" => "List Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.exclusive_tax, list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.tax.type, list_price.tax.label, list_price.tax.percentage, list_price.tax.amount",
                    "label" => "List Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.currency.code, selling_price.currency.name, selling_price.currency.symbol, selling_price.currency.symbol_html",
                    "label" => "Selling Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.exclusive_tax, selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.tax.type, selling_price.tax.label, selling_price.tax.percentage, selling_price.tax.amount",
                    "label" => "Selling Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.discount.amount, selling_price.discount.percentage",
                    "label" => "Discount",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        } elseif (isset($role) && $role == 'org-admin') {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "is_active",
                    "label" => "Is Active",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.currency.code, list_price.currency.name, list_price.currency.symbol, list_price.currency.symbol_html",
                    "label" => "List Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.exclusive_tax, list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.tax.type, list_price.tax.label, list_price.tax.percentage, list_price.tax.amount",
                    "label" => "List Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.currency.code, selling_price.currency.name, selling_price.currency.symbol, selling_price.currency.symbol_html",
                    "label" => "Selling Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.exclusive_tax, selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.tax.type, selling_price.tax.label, selling_price.tax.percentage, selling_price.tax.amount",
                    "label" => "Selling Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.discount.amount, selling_price.discount.percentage",
                    "label" => "Discount",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "in_crm.id, in_crm.platform",
                    "label" => "CRM Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        } else {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "is_active",
                    "label" => "Is Active",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.currency.code, list_price.currency.name, list_price.currency.symbol, list_price.currency.symbol_html",
                    "label" => "List Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.exclusive_tax, list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.tax.type, list_price.tax.label, list_price.tax.percentage, list_price.tax.amount",
                    "label" => "List Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.currency.code, selling_price.currency.name, selling_price.currency.symbol, selling_price.currency.symbol_html",
                    "label" => "Selling Price Currency",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.exclusive_tax, selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.tax.type, selling_price.tax.label, selling_price.tax.percentage, selling_price.tax.amount",
                    "label" => "Selling Price Tax",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.discount.amount, selling_price.discount.percentage",
                    "label" => "Discount",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        }

        return $columns;
    }

    private function getColumnsForOrgAdminWithAcademy($role){
        $columns = [
            [
                "field" => "id",
                "label" => "ID",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "status",
                "label" => "Status",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "is_active",
                "label" => "Is Active",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_date.time, start_date.timezone",
                "label" => "Enrolled On",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "end_date.time, end_date.timezone",
                "label" => "Enrolment Ends",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "days_left",
                "label" => "Days Left",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                "label" => "Learner",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                "label" => "Enrolled By",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                // Add other fields as needed
            ]
        ];

        return $columns;

    }

    /**
     * Handles Razorpay webhooks.
     *
     * @return void
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function handleRazorpayWebhook()
    {
        $this->paymentModel->handleWebhook();
    }

    /**
     * Updates the batch for an enrollment.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     * @throws Exception If an error occurs while updating.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function updEnrollment($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/updateEnrollmentBatch.log";
            // Step 1: Decode the request body
            error_log("updEnrollment - Request received at " . date("Y-m-d H:i:s") . " === " . $request->get_body() . "\n\n", 3, $logFile);
            $enrollmentId = (int)$request['enrollmentId']; // enrollment id
            $payload = json_decode($request->get_body(), true);
            // Step 2: Initialize necessary variables from the payload with validation checks
            $validationChecks = [
                'enrollmentId' => 'numeric'
            ];

            $prmBody = [
                'enrollmentId' => $enrollmentId
            ];

            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            $action = $payload['action']; //CHG_BATCH

            // Validate each field using the common functions
            $validation_checks = [
                "action" => '/^(CHG_BATCH|UNENROLL)$/'
            ];

            $errors = $this->validate->validateData($payload, $validation_checks);

            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            if ($action !== 'CHG_BATCH' && $action !== 'UNENROLL') {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Invalid action provided']);
            }

            if ($action === 'UNENROLL') {
                $isUnenroll = $payload['is_unenroll']; //UNENROLL
                $validation_checks = [
                    "is_unenroll" => 'boolean'
                ];

                $errors = $this->validate->validateData($payload, $validation_checks);

                if (!empty($errors)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
                }

                $data = [
                    'is_unenroll' => $isUnenroll
                ];

                $response = $this->enrollmentModel->unEnrollLearner($enrollmentId, $data);
                error_log("updEnrollment - unEnrollLearner - response " . date("Y-m-d H:i:s") . " === " . json_encode($response) . "\n\n", 3, $logFile);

                if ($response === false) {
                    return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
                } else {
                    // Success
                    return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
                }
            }

            // If action is CHG_BATCH, handle that case
            if ($action === 'CHG_BATCH') {
                $newBatchId = $payload['new_batch_id'] ?? null;

                // Validate the fields needed for CHG_BATCH
                $validation_checks = [
                    "new_batch_id" => '/^[0-9]+$/' // Batch ID
                ];

                $errors = $this->validate->validateData($payload, $validation_checks);

                if (!empty($errors)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
                }

                // Proceed with batch change logic
                $response = $this->enrollmentModel->updateBatch($enrollmentId, $newBatchId);
                error_log("updEnrollment - updateBatch - response " . date("Y-m-d H:i:s") . " === " . json_encode($response) . "\n\n", 3, $logFile);

                if ($response === false) {
                    return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
                } else {
                    // Success
                    return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
                }
            }
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Retrieves enrollment filters based on request parameters.
     *
     * @since 1.0.0
     * @access public
     * @param object $request HTTP request object containing query parameters.
     * @return object JSON response with filters data.
     * @throws Exception If an error occurs while fetching filters.
     * <AUTHOR>
     */
    public function getEnrollmentsFilters($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/get_enrollments_filters.log";

            // Log the request
            error_log("getEnrollmentsFilters - Request received at " . date("Y-m-d H:i:s") . " === " . json_encode($request->get_query_params()) . "\n\n", 3, $logFile);

            // Extract query parameters
            $queryParams = $request->get_query_params();

            $userId = (int) $queryParams['user_id'] ?? 0;
            $orgId = (int)$queryParams['org_id'] ?? 0;
            $instructorId = (int)$queryParams['instructor_id'] ?? 0;
            $learnerId = (int)$queryParams['learner_id'] ?? 0;
            $courseId = (int)$queryParams['course_id'] ?? 0;
            $status = $queryParams['status'] ?? 'all';
            $paymentStatus = $queryParams['payment_status'] ?? 'all';
            $counselorId = (int)$queryParams['counselor_id'] ?? 0;
            $referrals = $queryParams['referrals'] ?? 'all';

            // Get user role based on role_id
            $userRole = $this->userModel->getUserRole($userId);

            // Validation: Restrict access to unauthorized filters based on user role
            switch ($userRole) {
                case 'org-admin':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $referrals !== 'all') {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
                    }
                    break;

                case 'counselor':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $orgId !== 0) {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'Counselors can only filter by course, learners, referrals, and enrollment status']);
                    }
                    break;

                case 'instructor':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $orgId !== 0 || $referrals !== 'all') {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'Instructors can only filter by course, learners, and enrollment status']);
                    }
                    break;

                case 'yuno-admin':
                    // No restrictions, yuno-admin has full access
                    break;

                default:
                    return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }

            // Initialize filters based on role
            $filters = [];

            switch ($userRole) {
                case 'yuno-admin':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->orgModel->generateEnrollmentOrgFilters($userId, $orgId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->instructorModel->generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->counselorModel->generateEnrollmentCounselorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->paymentModel->generateEnrollmentPaymentFilters($paymentStatus),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status),
                        $this->referralModel->generateEnrollmentReferralFilters($referrals)
                    ];
                    break;
                case 'counselor':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->referralModel->generateEnrollmentReferralFilters($referrals),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
                case 'org-admin':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId,$courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
                case 'instructor':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
            }

            // Step 4: Return 204 if no data is found
            if (empty(array_filter($filters))) {
                return $this->response->error('GET_FAIL', ['message' => 'No filters found']);
            }

            return $this->response->success('GET_SUCCESS', $filters, ['message' => 'Filters retrieved successfully']);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }

    public function changeEnrollmentBatch($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/updateEnrollmentBatch.log";
            error_log("changeEnrollmentBatch - Request: " . $request->get_body() . "\n\n", 3, $logFile);

            $payload = json_decode($request->get_body(), true);
            $enrollmentId = (int)($payload['enrollmentId'] ?? 0);
            $newBatchId = $payload['new_batch_id'] ?? null;

            if (!$enrollmentId || !$newBatchId) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing enrollmentId or new_batch_id']);
            }

            $validationChecks = [
                'new_batch_id' => '/^[0-9]+$/'
            ];

            $errors = $this->validate->validateData($payload, $validationChecks);
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            $response = $this->enrollmentModel->updateBatch($enrollmentId, $newBatchId);
            error_log("changeEnrollmentBatch - updateBatch - response: " . json_encode($response) . "\n\n", 3, $logFile);

            if ($response === false) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
            }

            return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Batch changed successfully.']);
        } catch (Exception $e) {
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    public function updEnrollmentUnenroll($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/unenrollLearner.log";
            error_log("unenrollLearner - Request: " . $request->get_body() . "\n\n", 3, $logFile);

            $payload = json_decode($request->get_body(), true);
            $isUnenroll = $payload['is_unenroll'] ?? null;
            $enrollmentId = (int)($request['enrollmentId'] ?? 0);

            if (!isset($isUnenroll)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing is_unenroll']);
            }

            $validationChecks = [
                'is_unenroll' => 'boolean'
            ];

            $errors = $this->validate->validateData($payload, $validationChecks);
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            $response = $this->enrollmentModel->unEnrollLearner($enrollmentId, ['is_unenroll' => $isUnenroll]);
            error_log("unenrollLearner - response: " . json_encode($response) . "\n\n", 3, $logFile);

            if ($response === false) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
            }

            return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
        } catch (Exception $e) {
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Fetches enrollments from the database based on course and batch IDs.
     *
     * @param int $courseId Course ID.
     * @param int $batchId Batch ID.
     * @return array Array of enrollment objects.
     * <AUTHOR>
     */  
    public function getActiveEnrollmentsByEntity($request) {

        try {
            $logFile = ABSPATH . "error-logs/getActiveEnrollmentsByEntity.log";
            error_log("getActiveEnrollmentsByEntity - Request: " . json_encode($request->get_query_params()) . "\n\n", 3, $logFile);
            // Extract course and batch IDs from the request
            $entityType = $request['entityType'];
            if (!$entityType) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing entityType']);
            }
            $entityId = $request['entityId'];

            if ($entityType !== 'batch') {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Invalid entity type']);
            }
            $entityId = $request['entityId'];
            if (!$entityId) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing entityId']);
            }
            // Validate course and batch IDs
            $validationChecks = [
                'entityId' => 'numeric',
                'entityType' => '/^(batch)$/'
            ];
            $prmBody = [
                'entityId' => $entityId,
                'entityType' => $entityType
            ];
            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }
            // Fetch enrollments from the es database
            $enrollments = $this->enrollmentModel->getActiveEnrollmentsByBatchId($entityId);

            error_log("getActiveEnrollmentsByEntity - Enrollments: " . json_encode($enrollments) . "\n\n", 3, $logFile);
            if (empty($enrollments)) {
                return $this->response->error('GET_FAIL', ['message' => 'No enrollments found']);
            }
           
            return $this->response->success('GET_SUCCESS', $enrollments, ['message' => 'Active Enrollments retrieved successfully']);


        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    
    }
      
}
