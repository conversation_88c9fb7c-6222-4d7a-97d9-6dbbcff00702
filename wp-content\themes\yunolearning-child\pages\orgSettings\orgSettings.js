window.Event = new Vue();

const validationMsg = {
  messages: {
    required: "This field is required",
    numeric: "Numbers only",
    min: "Minimum 10 numbers required",
    max: "Maximum 15 numbers required ",
    is_not: "New batch shouldn't be same as current batch",
  },
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component("yuno-org-settings", {
  template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
        >
            <template v-slot:main>
                <div class="container-fluid">
                    <template v-if="moduleWithoutTab.loading">
                        <figure class="infiniteSpinner">
                            <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                        </figure>
                    </template>
                    <template v-if="moduleWithoutTab.success">
                        <div class="mainHeader">
                            <h1>{{ pageHeader.title }}</h1>
                        </div>
                        <yuno-tabs-v2
                            :destroyOnHide="true"
                            @tabChange="tabChange"
                        >
                            <template v-slot:general>
                                <yuno-general
                                    @tabChanged="onTabChanged"
                                    ref="yunoGeneral" 
                                    :storage="storage"
                                >
                                </yuno-general>
                            </template>
                            <template v-slot:prasar>
                                <yuno-prasar
                                    @tabChanged="onTabChanged"
                                    ref="yunoPrasar" 
                                    :storage="storage"
                                >
                                </yuno-prasar>
                            </template>
                            <template v-slot:academy>
                                <yuno-academy
                                    @tabChanged="onTabChanged"
                                    ref="yunoAcademy" 
                                    :storage="storage"
                                >
                                </yuno-academy>
                            </template>
                        </yuno-tabs-v2>
                    </template>
                </div>
            </template>
        </yuno-page-grid>
    `,
  data() {
    return {
      apiURL: null,
      isMiniSidebar: false,
      pageHeader: {
        title: "Org Settings",
      },
      authorizedRoles: ["org-admin"],
      storage: {
        name: "orgSettings",
        version: 1,
      },
    };
  },
  computed: {
    ...Vuex.mapState([
      "user",
      "userInfo",
      "header",
      "userProfile",
      "userRole",
      "filterResult",
      "moduleWithoutTab",
    ]),
    wpThemeURL() {
      return this.$store.state.themeURL;
    },
    isUserAuthorized: {
      get() {
        if (YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
          return true;
        } else {
          return false;
        }
      },
    },
    emptyStates() {
      return {
        state: "notAuthorized",
      };
    },
    isPageLoading: {
      get() {
        const module = this.userInfo.loading;

        return module;
      },
    },
    isPageReady: {
      get() {
        let module = "";

        if (this.user.isLoggedin) {
          module = this.userInfo.success;
        } else {
          module = true;
        }

        return module;
      },
    },
  },
  async created() {},
  destroyed() {},
  mounted() {},
  methods: {
    setupTabs(wrapperClass, hasAcademy, hasPrasar) {
      function createTab(label, slug, eleClass, isVisible) {
        return {
          label,
          slug,
          isActive: false,
          isVisible: isVisible,
          class: eleClass,
        };
      }

      const storage = this.storage;
      const store = sessionStorage.getItem(
        storage.name + "V" + storage.version
      );
      let activeTab = "";

      if (store) {
        activeTab = JSON.parse(store).currentTab;
      } else {
        activeTab = 0;
      }

      this.filterResult.tabs = {
        activeTab: activeTab,
        wrapperClass: wrapperClass,
        items: [
          createTab("General", "general", "yunoGeneral", true),
          createTab("Prasar", "prasar", "yunoPrasar", hasPrasar),
          createTab("Academy", "academy", "yunoAcademy", hasAcademy),
        ],
      };
    },
    tabChange(e) {
      setTimeout(() => {
        const tabRefs = [
          this.$refs.yunoGeneral,
          this.$refs.yunoPrasar,
          this.$refs.yunoAcademy,
        ];
        const tabRef = tabRefs[e];
        if (tabRef) {
          tabRef.onTabChanged(e);
        }
      }, 50);
    },
    onTabChanged() {},
    /**
     * Handles the user information.
     *
     * @param {Object} data - The user information data.
     */
    onUserInfo(data) {
      if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
        // this.setupTabs();
        this.fetchOrgInfo();
      }
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    gotOrgInfo(options) {
      const { response: { data: { code, data } = {} } = {} } = options;
      if (code === 200) {
        const academies = data.yuno_academy_subscription.academies;
        const prasar = data.yuno_academy_subscription.plan.prasar;
        this.setupTabs("tabsAvailable", academies.length > 0, prasar);
      }
    },
    fetchOrgInfo() {
      const options = {
        apiURL: YUNOCommon.config.generic(
          "org",
          false,
          false,
          this.activeOrg()
        ),
        module: "gotData",
        store: "moduleWithoutTab",
        callback: true,
        callbackFunc: this.gotOrgInfo,
      };

      this.$store.dispatch("fetchData", options);
    },
  },
});
