Vue.component("yuno-prasar", {
  props: {
    storage: {
      type: Object,
      required: true,
    },
  },
  template: `
        <div class="row">
            <div class="col-12 col-md-6">
                <template v-if="orgAdmin.loading">
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="100px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                    <div class="field">
                        <b-skeleton height="40px"></b-skeleton>
                    </div>
                </template>
                <template v-if="orgAdmin.success">
                    <yuno-org-settings-form
                        :fields="form.fields"
                        :payload="form.payload"
                        :defaultTaxonomy="defaultTaxonomy"
                        @submitForm="submitForm"
                    >
                    </yuno-org-settings-form>  
                </template>
            </div>
        </div>
    `,
  data() {
    return {
      defaultTaxonomy: 0,
      industry: null,
    };
  },
  computed: {
    ...Vuex.mapState([
      "userInfo",
      "user",
      "filterResult",
      "subform2",
      "form",
      "orgAdmin",
      "config",
    ]),
  },
  async created() {},
  destroyed() {},
  mounted() {
    this.manageOrg(this.userInfo.data);
  },
  methods: {
    setStorage(hasColumns) {
      const storage = this.$props.storage;
      const store = {
        currentTab: this.filterResult.tabs.activeTab,
      };

      setTimeout(() => {
        sessionStorage.setItem(
          storage.name + "V" + storage.version,
          JSON.stringify(store)
        );
      }, 100);
    },
    resetModules() {
      this.orgAdmin.data = [];
      this.orgAdmin.success = false;
      this.orgAdmin.error = null;
    },
    onTabChanged(e) {
      this.setStorage();
      this.resetModules();
    },
    /**
     * Displays a toast message.
     * @param {string} message - The message to be displayed in the toast.
     */
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },
    /**
     * Handles the response after form submission.
     * @param {Object} options - The options object containing the response data.
     */
    formPosted(options) {
      this.form.isLoading = false;

      const response = options?.response?.data;

      if (response?.code === 201) {
        this.showToastMessage(response.message);
      } else if (response?.message) {
        this.showToastMessage(response.message);
      }
    },
    /**
     * Dispatches an action to the store with the specified options.
     *
     * @param {string} action - The name of the action to dispatch.
     * @param {Object} options - The options to pass to the action.
     */
    dispatchData(action, options) {
      this.$store.dispatch(action, options);
    },
    /**
     * Fetches data from the specified API URL and invokes the callback function with the fetched data.
     * @param {string} field - The name of the field to fetch data for.
     * @param {string} apiURL - The URL of the API to fetch data from.
     * @param {Function} callbackFunc - The callback function to invoke with the fetched data.
     */
    fetchData(field, apiURL, callbackFunc) {
      const formField = YUNOCommon.findObjectByKey(
        this.form.fields,
        "name",
        field
      );
      formField.loading = true;
      const options = {
        apiURL: apiURL,
        module: "gotData",
        store: "subform2",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => callbackFunc(options, formField),
      };
      this.dispatchData("fetchData", options);
    },
    /**
     * Submits the form data and updates the organization details.
     * @param {Object} payload - The payload containing the form data.
     */
    submitForm(payload) {
      this.form.isLoading = true;

      const options = {
        apiURL: YUNOCommon.config.org("detailsUpdate"),
        module: "gotData",
        store: "form",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.formPosted(options),
      };

      this.dispatchData("postData", options);
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    // Function to set up the form with default field properties and payload
    setupForm(userInfo, activeOrg) {
      const defaultFieldProperties = {
        isRequired: false,
        isDisabled: false,
        isLoading: false,
      };

      const form = [
        {
          label: "Prasar URL",
          placeholder: "Enter a valid URL",
          type: "text",
          name: "prasar_url",
          ...defaultFieldProperties,
        },
        {
          label: "App Name",
          placeholder: "Enter a app name",
          type: "text",
          name: "app_name",
          ...defaultFieldProperties,
        },
        {
          label: "Excerpt",
          placeholder: "Enter excerpt",
          type: "textarea",
          name: "app_short_description",
          ...defaultFieldProperties,
        },
        {
          label: "Description",
          placeholder: "Enter description",
          type: "textarea",
          name: "app_long_description",
          ...defaultFieldProperties,
        },
        {
          label: "Privacy Policy URL",
          placeholder: "Enter a valid URL",
          type: "text",
          name: "privacy_policy_url",
          ...defaultFieldProperties,
        },
        {
          label: "App Developer Email",
          placeholder: "Enter a valid email",
          type: "text",
          name: "app_dev_email",
          ...defaultFieldProperties,
        },
        {
          label: "App Developer Phone",
          placeholder: "Enter a valid phone",
          type: "text",
          name: "app_dev_phone",
          ...defaultFieldProperties,
        },
        {
          label: "App Developer Website",
          placeholder: "Enter a valid url",
          type: "text",
          name: "app_dev_website",
          ...defaultFieldProperties,
        },
        {
          label: "Video URL",
          placeholder: "Enter a valid url",
          type: "text",
          name: "video_url",
          ...defaultFieldProperties,
        },
        {
          label: "Live Classes Included",
          placeholder: "",
          type: "checkbox",
          name: "live_classes_included",
          ...defaultFieldProperties,
        },
        {
          label: "Study Material",
          placeholder: "",
          type: "checkbox",
          name: "study_material",
          ...defaultFieldProperties,
        },
        {
          label: "Videos",
          placeholder: "",
          type: "checkbox",
          name: "study_material",
          ...defaultFieldProperties,
        },
        {
          label: "Practice Tests",
          placeholder: "",
          type: "checkbox",
          name: "practice_tests",
          ...defaultFieldProperties,
        },
      ];

      const payload = {
        live_classes_included: "",
        study_material: "",
        videos: "",
        practice_tests: "",
        prasar_url: "",
        app_name: "",
        app_short_description: "",
        app_long_description: "",
        privacy_policy_url: "",
        app_dev_email: "",
        app_dev_phone: "",
        app_dev_website: "",
        video_url: "",
        org_id: activeOrg,
        user_id: isLoggedIn,
      };

      // Set the form fields and payload
      this.form.fields = form;
      this.form.payload = payload;
    },
    manageOrg(data) {
      const activeOrg = this.activeOrg();

      if (activeOrg) {
        // isLoggedIn = activeOrg;
        this.setupForm(data, activeOrg);
        this.fetchModules(data, activeOrg);
      }
      // else {
      //     window.location.href = "/select-an-organization";
      // }
    },
    /**
     * Handles the response of the organization data API call.
     * @param {Object} options - The response object from the API call.
     */
    gotOrg(options, activeOrg) {
      const { code, data } = options.response?.data || {};

      if (code === 200) {
        const imageFields = ["app_icon", "banner_image"];
        const payload = this.form.payload;
        const prasarData = data.prasar;

        // Convert image fields to object format
        // imageFields.forEach(field => {
        //     if (data[field] !== null) {
        //     data[field] = { name: data[field] };
        //     }
        // });

        payload.live_classes_included = prasarData.live_classes_included;
        payload.study_material = prasarData.resources.study_material;
        payload.videos = prasarData.resources.videos;
        payload.practice_tests = prasarData.resources.practice_tests;
        payload.video_url = prasarData.mobile_app.video_url;
        payload.privacy_policy_url = prasarData.mobile_app.privacy_policy_url;
        payload.prasar_url = prasarData.prasar_url;
        payload.app_short_description =
          prasarData.mobile_app.app_short_description;
        payload.app_name = prasarData.mobile_app.app_name;
        payload.app_long_description =
          prasarData.mobile_app.app_long_description;
        payload.app_dev_email = prasarData.mobile_app.app_developer.email;
        payload.app_dev_phone = prasarData.mobile_app.app_developer.phone;
        payload.app_dev_website = prasarData.mobile_app.app_developer.website;
        payload.user_id = isLoggedIn;
        payload.org_id = activeOrg;

        // Update orgAdmin data and form payload
        this.orgAdmin.data = data;
        this.form.payload = payload;
      }
    },
    /**
     * Fetches organization data from the server.
     */
    fetchOrg(userInfo, activeOrg) {
      const options = {
        apiURL: YUNOCommon.config.org("settings", activeOrg),
        module: "gotData",
        store: "orgAdmin",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotOrg(options, activeOrg),
      };
      this.dispatchData("fetchData", options);
    },
    /**
     * Fetches the modules for the organization.
     * This function internally calls the fetchOrg function.
     */
    fetchModules(userInfo, activeOrg) {
      this.fetchOrg(userInfo, activeOrg);
    },
  },
});
