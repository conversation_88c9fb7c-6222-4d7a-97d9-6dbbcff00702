<?php

/**
 * Resource controller is use for get list of resources as per post type
 */
class ResourceController
{
  function __construct()
  {
    $this->namespace = 'yuno/v2';
    $this->resource_name1 = '/resources/(?P<criteria>[a-zA-Z0-9-]+)/(?P<category>[a-zA-Z0-9-]+)/(?P<user_id>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)'; //android,web
    $this->resource_name2 = '/resources'; //get resources count per category
    
    // Calendar endpoints
    $this->calendar_add_single = '/calendar/(?P<calendar_id>[^/]+)/add_single_event';
    $this->calendar_add_recurring = '/calendar/(?P<calendar_id>[^/]+)/add_recurring_event';
    $this->calendar_delete = '/calendar/delete/(?P<calendar_id>[^/]+)/(?P<event_id>[^/]+)';
    $this->calendar_update = '/calendar/(?P<calendar_id>[^/]+)/(?P<event_startdatetime>[^/]+)/(?P<event_duration>[^/]+)/(?P<event_id>[^/]+)/(?P<batch_id>[^/]+)';
    $this->calendar_update_recurring = '/calendar/update/(?P<calendar_id>[^/]+)/(?P<event_id>[^/]+)/(?P<event_startdatetime>[^/]+)/(?P<event_enddatetime>[^/]+)/(?P<weekdays>[^/]+)';
    $this->calendar_delete_instance = '/calendar/delete/(?P<calendar_id>[^/]+)/(?P<event_id>[^/]+)/(?P<event_startdatetime>[^/]+)/(?P<duration>[^/]+)';
  }

  public function register_routes()
  {
    register_rest_route($this->namespace, $this->resource_name1, array(
      array(
        'methods'             => WP_REST_Server::READABLE,
        'callback'            => array($this, 'get_resources'),
        'permission_callback' => array($this, 'get_resources_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->resource_name2, array(
      array(
        'methods'             => WP_REST_Server::READABLE,
        'callback'            => array($this, 'get_resources_count_per_category'),
        'permission_callback' => array($this, 'get_resources_count_per_category_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_add_single, array(
      array(
        'methods'             => WP_REST_Server::CREATABLE,
        'callback'            => array($this, 'add_single_event'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_add_recurring, array(
      array(
        'methods'             => WP_REST_Server::CREATABLE,
        'callback'            => array($this, 'add_recurring_event'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_delete, array(
      array(
        'methods'             => WP_REST_Server::DELETABLE,
        'callback'            => array($this, 'delete_event'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_update, array(
      array(
        'methods'             => WP_REST_Server::EDITABLE,
        'callback'            => array($this, 'update_event'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_update_recurring, array(
      array(
        'methods'             => WP_REST_Server::EDITABLE,
        'callback'            => array($this, 'update_recurring_event'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));

    register_rest_route($this->namespace, $this->calendar_delete_instance, array(
      array(
        'methods'             => WP_REST_Server::DELETABLE,
        'callback'            => array($this, 'delete_event_instance'),
        'permission_callback' => array($this, 'check_access_permissions_check'),
        'args'                => array(),
      ),
    ));
  }
  /**
   * Token authorization check
   * This function common for all post login apis  
   */
  public function check_access_permissions_check(WP_REST_Request $request)
  {
    $authToken = $request->get_header('authorization');
    if (empty($authToken)) {
      return false;
    }
    list($bearer, $token) = explode(" ", $authToken);
    if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
      return true;
    } else {
      $return = jwt_token_validation_check($token);  // this is for postman
      if ($return) {
        return true;
      } else {
        return false;
      }
    }
    $codes = error_code_setting();
    $authToken = $request->get_header('authorization');
    list($bearer, $token) = explode(" ", $authToken);
    $result = token_validation_check($token);
    $newData = [];
    if ($result === true) {
      return true;
    } else if ($result != '' && strlen($result) > 10) {
      $newData = [
        "status" => $codes["TOKEN_FAIL"]["code"],
        "reValidate" => true,
        "token" => $result
      ];
      return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
    } else {
      $newData = [
        "status" => $codes["TOKEN_FAIL"]["code"],
        "reValidate" => false
      ];
      return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
    }
  }
  /**
   * To get time difference
   */
  function array_sort_by_column(&$arr, $col, $dir = SORT_DESC)
  {
    $sort_col = array();
    foreach ($arr as $key => $row) {
      $sort_col[$key] = $row[$col];
    }

    array_multisort($sort_col, $dir, $arr);
  }
  /**
   * Getting all resources from es
   */
  public function get_resources($request)
  {
    global $wpdb;
    $limit = (int)$request['limit'];
    $offset = (int)$request['offset'];
    $display_view = $request['view'];
    $user_id = (int)$request['user_id'];
    $criteria = $request['criteria'];
    $category = $request['category'];
    $codes = error_code_setting();
    date_default_timezone_set('Asia/Kolkata');
    $currentDate = date("Y-m-d H:i:s");
    $query_string = $request->get_param('filters');
    //$str=urldecode($query_string);
    $query = json_decode($query_string, true);
   // echo json_encode($query);
   // echo "<pre>";
    $event_types = $query['filters'][0]['options'];
    if (empty($event_types)) {
      $str1 = '{
            "filters": [
                {
                    "type": "resource_type",
                    "options": ["videos","documents-and-notes","ebooks","articles","webinars"]
                },
                {
                    "type": "sub_categories",
                    "options": []
                }           
            ]
          }';
      $query1 = json_decode($str1, true);
      $event_types = $query1['filters'][0]['options'];
      $resources_taxonomy = $query1['filters'][1]['options'];
      if ($category === 'all') {
        $details = get_terms('course_category', ['hide_empty' => false, 'parent' => 0]);
        foreach ($details as $detail) {
         // $resources_taxonomy[] = $detail->term_id;
          if (is_array($resources_taxonomy)) {
            array_push($resources_taxonomy, $detail->term_id);
          }
        }

      } else {
        $details = get_terms('course_category', array('search' => $category, 'hide_empty' => 0, 'parent' => 0));
        $parent_id = $details[0]->term_id;
        if (is_array($resources_taxonomy)) {
          array_push($resources_taxonomy, $parent_id);
        }
        
      }
    } else {
      if (count($event_types) == 0) {
        return new WP_Error($codes["GET_FAIL"]["code"], 'Resource types are not found', array('status' => $codes["GET_FAIL"]["status"]));
      }
      $typeCollectionArray = array("videos", "documents-and-notes", "ebooks", "articles", "webinars");
      if (empty(array_intersect($typeCollectionArray, $event_types))) {
        return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the resource type', array('status' => $codes["GET_FAIL"]["status"]));
      }
      $resources_taxonomy = $query['filters'][1]['options'];
    }

    //new code reverted 

    /*$category = $request['category'];
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Kolkata');
        $currentDate = date("Y-m-d H:i:s");
        $query_string = $request->get_param('filters');
        $query = json_decode($query_string, true);
        $event_types = $query['filters'][0]['options'] ?? ["videos", "documents-and-notes", "ebooks", "articles", "webinars"];

        // Initialize resources_taxonomy empty array for 'all' categories or specific category handling.
        $resources_taxonomy = [];

        // Handle 'all' categories case
        if ($category === 'all') {
            $details = get_terms('course_category', ['hide_empty' => false, 'parent' => 0]);
            foreach ($details as $detail) {
                $resources_taxonomy[] = $detail->term_id;
            }
        } elseif (!empty($category)) {
            // Handle specific category case
            $details = get_terms('course_category', ['name' => $category, 'hide_empty' => false, 'parent' => 0]);
            if (!empty($details)) {
                $resources_taxonomy[] = $details[0]->term_id;
            }
        }

        // Validate if category handling resulted in a valid category/terms
        if (empty($resources_taxonomy)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the resource category', ['status' => $codes["GET_FAIL"]["status"]]);
        }

        // Continue with event types validation and processing
        if (empty(array_intersect(["videos", "documents-and-notes", "ebooks", "articles", "webinars"], $event_types))) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the resource type', ['status' => $codes["GET_FAIL"]["status"]]);
        }*/

    //new code reverted 
    // If everything is validated, proceed with your logic for fetching and returning the data based on the event_types and resources_taxonomy
    $msg = "Resource";
    $days = 0;
    if ($days != "0") {
      $calculatedDate = Date('Y-m-d', strtotime('-' . $days . ' days'));
    }

    error_log(" resources_taxonomy --   === " . date('Y-m-d H:i:s') . json_encode($resources_taxonomy) . "\n\n", 3, ABSPATH . 'error-logs/resources_taxonomy_search.log');

    if (is_array($resources_taxonomy) && count($resources_taxonomy) == 0) {

      if ($category === 'all') {
        $details = get_terms('course_category', ['hide_empty' => false, 'parent' => 0]);
      } else {
        $details = get_terms('course_category', array('search' => $category, 'hide_empty' => 0, 'parent' => 0));
      }

      $parent_id = $details[0]->term_id;
      if (is_array($resources_taxonomy)) {
        array_push($resources_taxonomy, $parent_id);
      }
      $flag = "false";
    } else {
      $flag = "true";
    }

    $categoryData = $wpdb->get_results("SELECT category from wp_signup_template", OBJECT_K);

    error_log(" categoryData --   === " . date('Y-m-d H:i:s') . json_encode($categoryData) . "\n\n", 3, ABSPATH . 'error-logs/resources_taxonomy_search.log');

    $categoryCollectionArray = array_keys($categoryData);

    error_log(" categoryCollectionArray --   === " . date('Y-m-d H:i:s') . json_encode($categoryCollectionArray) . "\n\n", 3, ABSPATH . 'error-logs/resources_taxonomy_search.log');

    error_log(" category --   === " . date('Y-m-d H:i:s') . json_encode($category) . "\n\n", 3, ABSPATH . 'error-logs/resources_taxonomy_search.log');
    /*if (!in_array($category, $categoryCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the resource category', array('status' => $codes["GET_FAIL"]["status"]));
        }*/

    // Check if the category exists in the array, but allow "all" as a special case
    if ($category !== 'all' && !in_array($category, $categoryCollectionArray)) {
      return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the resource category', ['status' => $codes["GET_FAIL"]["status"]]);
    }

    // if ($offset == 0 && count($event_types)==5 && $days==0 && $flag=="true"){
    //     if($display_view == "list-view") {
    //         $resourcesCacheData = get_cache("list_view_get_resources_v2_".$user_id."_".$criteria."_".$category);    
    //     } else {
    //         $resourcesCacheData = get_cache("grid_view_get_resources_v2_".$user_id."_".$criteria."_".$category);    
    //     } 
    // }

    // error_log(" resourcesCacheData --   === " . date('Y-m-d H:i:s') . json_encode($resourcesCacheData)."\n\n", 3, ABSPATH . 'error-logs/resources_taxonomy_search.log');

    // if (!empty($resourcesCacheData) && $offset == 0) {
    //     return new WP_REST_Response($resourcesCacheData, 200); 
    // }
    $post_login_access = true;
    $selected = [];
    foreach ($event_types as $event_type) {
      if ($event_type == "webinars") {
        $item = "webinar";
        $type = "event_type";
        $index = "webinar";
      } else {
        if ($event_type == "videos") {
          $item = "video";
          $selected[] = $item;
        }
        if ($event_type == "documents-and-notes") {
          $item = "document";
          $selected[] = $item;
        }
        if ($event_type == "ebooks") {
          $item = "ebook";
          $selected[] = $item;
        }
        if ($event_type == "articles") {
          $item = "article";
          $selected[] = $item;
        }
        $type = "resource_type";
        $index = "resourceevent";
      }
    }
    if (count($event_types) == 1) {
      $msg = ucfirst($event_types[0]);
    } else {
      $msg = "Resource";
    }
    $pos = array_search('webinars', $event_types);
    if ($pos === false) {
      $exist_webinar = "no";
      $taxonomy = "taxonomy";
      $filter = "terms";
      $cat_ary = $resources_taxonomy;
    } else {
      $exist_webinar = "yes";
      $taxonomy = "categories";
      $filter = "terms";
      $cat_ary = $resources_taxonomy;
    }
    if (count($event_types) > 1) {
      $type = "resource_type";
      $index = "resourceevent";
      $curlPost = [
        "query" => [
          "constant_score" => [
            "filter" => [
              "bool" => [
                "must" => [
                  [
                    "terms" => [
                      "data.details." . $type => $selected
                    ]
                  ],
                  [
                    "terms" => [
                      "data.details.taxonomy" => $resources_taxonomy
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
    } else {
      $curlPost = [
        "query" => [
          "constant_score" => [
            "filter" => [
              "bool" => [
                "must" => [
                  [
                    "match" => [
                      "data.details." . $type => $item
                    ]
                  ],
                  [
                    $filter => [
                      "data.details." . $taxonomy => $cat_ary
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
    }

    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_PORT => ELASTIC_SEARCH_PORT,
      CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . $index . "/_search?size=" . ELASTIC_RECORDS_COUNT,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "GET",
      CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
      CURLOPT_HTTPHEADER => array(
        "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
        "cache-control: no-cache",
        "content-type: application/json"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);
    $res = json_decode($response);
    if (count($event_types) == 1 && $event_types[0] == "videos") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = $data->_source->data->details->privacy;
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $collections[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $data->_source->data->details->resource_type,
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'time' => strtotime($published_date),
              'taxonomy' => $data->_source->data->details->taxonomy,
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => true, "share" => true, "download" => false],
              "is_active" => false
            );
          }
        }
      }
    } else if (count($event_types) == 1 && $event_types[0] == "ebooks") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = $data->_source->data->details->privacy;
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $collections[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $data->_source->data->details->resource_type,
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'time' => strtotime($published_date),
              'taxonomy' => $data->_source->data->details->taxonomy,
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => true, "share" => true, "download" => true],
              "is_active" => false
            );
          }
        }
      }
    } else if (count($event_types) == 1 && $event_types[0] == "documents-and-notes") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = $data->_source->data->details->privacy;
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $collections[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $data->_source->data->details->resource_type,
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'time' => strtotime($published_date),
              'taxonomy' => $data->_source->data->details->taxonomy,
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => true, "share" => true, "download" => true],
              "is_active" => false
            );
          }
        }
      }
    } else if (count($event_types) == 1 && $event_types[0] == "articles") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = $data->_source->data->details->privacy;
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $collections[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $data->_source->data->details->resource_type,
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'time' => strtotime($published_date),
              'taxonomy' => $data->_source->data->details->taxonomy,
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => true, "share" => true, "download" => false],
              "is_active" => false
            );
          }
        }
      }
    } else if (count($event_types) == 1 && $event_types[0] == "webinars") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $instructor_id = $data->_source->data->details->class_instructors;
            $EventStartDate = get_post_meta($post_id, '_EventStartDate', true);
            $date = date("M d, Y ", strtotime($EventStartDate));
            $time = date(" h:i A", strtotime($EventStartDate));

            $EventEndDate = get_post_meta($post_id, '_EventEndDate', true);
            $instructorEmail = get_user_meta($instructor_id, 'yuno_gplus_email', true);
            $instructorDisplayName = $data->_source->data->details->instructor_name;
            $city_id = get_user_meta($instructor_id, 'yuno_user_address_city', true);
            // Escape the city ID to prevent SQL injection
            $escaped_city_id = $wpdb->prepare('%d', $city_id);
            // Query the database and retrieve the city name
            $city = $wpdb->get_row("SELECT name FROM {$wpdb->prefix}cities WHERE id = $escaped_city_id");
            if ($city) {
              $city_name = $city->name;
              // Do something with the city name
            }
            //$city = $wpdb->get_row( "SELECT name FROM wp_cities where id=$city_id");      
            $biographicalInfo = get_user_meta($instructor_id, 'Instructor_About', true);
            $instructor = [
              "image" => $data->_source->data->details->instructor_image,
              "name" => $instructorDisplayName,
              "email" => $instructorEmail,
              "id" => (int)$instructor_id,
              "city" => $city_name,
              "state" => get_user_meta($instructor_id, 'yuno_user_address_state', true),
              "country" => get_user_meta($instructor_id, 'yuno_user_address_country', true),
              "about" => $biographicalInfo,
              "name_email" => $instructorDisplayName . " (" . $instructorEmail . ")"
            ];

            $enrollment_count = $wpdb->get_results("SELECT * FROM wp_enrolled where classId=$post_id and userId=$user_id");
            if (count($enrollment_count) > 0) {
              $hasWebinarEnrolled = true;
            } else {
              $hasWebinarEnrolled = false;
            }
            if ($EventEndDate >= date("Y-m-d H:i:s")) {
              $resultCategoryWebinarArray = get_post_meta($post_id, 'Category_Tree_Structure', true);
              $resource_webinar_taxonomy = [];
              $resource_webinar_taxonomy[] = $resultCategoryWebinarArray['id'];
              $sub_1_category = $resultCategoryWebinarArray['sub_category'];
              if (!empty($sub_1_category)) {
                foreach ($sub_1_category as $v) {
                  $resource_webinar_taxonomy[] = $v['id'];
                  if (!empty($v['sub_category'])) {
                    foreach ($v['sub_category'] as $vv) {
                      $resource_webinar_taxonomy[] = $vv['id'];
                    }
                  }
                }
              }
              $endDate = date("Y-m-d\TH:i:s+05:30", strtotime($EventEndDate));
              $startDate = date("Y-m-d\TH:i:s+05:30", strtotime($EventStartDate));
              $collections[] = array(
                'id' => $post_id,
                'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
                'slug'  => get_post_field('post_name', $post_id),
                'excerpt' => html_entity_decode($data->_source->data->details->ClassExcerpt, ENT_NOQUOTES, 'UTF-8'),
                'post_type'  => $data->_source->data->details->event_type,
                'event_date'  => $data->_source->data->details->event_date,
                'description'  => html_entity_decode($data->_source->data->details->PreClassAssignment, ENT_NOQUOTES, 'UTF-8'),
                'display_image' => get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
                'instructor' => $instructor,
                'end_date' => $endDate,
                'start_date' => $startDate,
                'time' => strtotime($startDate),
                'taxonomy' => $resource_webinar_taxonomy,
                'privacy' => get_post_meta($post_id, 'WEBINAR_PRIVACY', true),
                'url' => $data->_source->data->details->class_detail,
                'is_enrolled' => $hasWebinarEnrolled,
                'promo_video' => $data->_source->data->details->PromoVideo,
                'published_at' => $date . "at" . $time,
                'post_login_access' => $post_login_access,
                "actions" => ["bookmark" => false, "share" => true, "download" => false],
                "is_active" => false
              );
            }
          }
        }
      }
    } else if (count($event_types) > 1 && $exist_webinar == "yes") {
      if (!empty($res->status) && $res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $others = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = isset($data->_source->data->details->privacy) ? $data->_source->data->details->privacy : "";
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $post_type = $data->_source->data->details->resource_type;
            if ($post_type == "video") {
              $bookmark = true;
              $share = true;
              $download = false;
            }
            if ($post_type == "document") {
              $bookmark = true;
              $share = true;
              $download = true;
            }
            if ($post_type == "ebook") {
              $bookmark = true;
              $share = true;
              $download = true;
            }
            if ($post_type == "article") {
              $bookmark = true;
              $share = true;
              $download = false;
            }
            $others[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $post_type,
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'taxonomy' => $data->_source->data->details->taxonomy,
              'time' => strtotime($published_date),
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => $bookmark, "share" => $share, "download" => $download],
              "is_active" => false
            );
          }
        }
      }


      $curlPost = [
        "query" => [
          "constant_score" => [
            "filter" => [
              "bool" => [
                "must" => [
                  [
                    "match" => [
                      "data.details.event_type" => "webinar"
                    ]
                  ],
                  [
                    "terms" => [
                      "data.details.categories" => $resources_taxonomy
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];

      $curl = curl_init();
      curl_setopt_array($curl, array(
        CURLOPT_PORT => ELASTIC_SEARCH_PORT,
        CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/webinar/_search?size=" . ELASTIC_RECORDS_COUNT,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "GET",
        CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);

      curl_close($curl);
      $res_data = json_decode($response);
      $webinars = [];
      if (count($res_data->hits->hits) > 0) {
        foreach ($res_data->hits->hits as $data) {
          $post_id = $data->_source->data->details->record_id;
          $instructor_id = $data->_source->data->details->class_instructors;
          $EventStartDate = get_post_meta($post_id, '_EventStartDate', true);
          $date = date("M d, Y ", strtotime($EventStartDate));
          $time = date(" h:i A", strtotime($EventStartDate));

          $EventEndDate = get_post_meta($post_id, '_EventEndDate', true);
          $instructorEmail = get_user_meta($instructor_id, 'yuno_gplus_email', true);
          $instructorDisplayName = $data->_source->data->details->instructor_name;
          $city_id = get_user_meta($instructor_id, 'yuno_user_address_city', true);

          if ($EventEndDate >= date("Y-m-d H:i:s")) {
            //$city = $wpdb->get_row( "SELECT name FROM wp_cities where id=$city_id");
            $escaped_city_id = $wpdb->prepare('%d', $city_id);
            // Query the database and retrieve the city name
            $city = $wpdb->get_row("SELECT name FROM {$wpdb->prefix}cities WHERE id = $escaped_city_id");
            if ($city) {
              $city_name = $city->name;
              // Do something with the city name
            }

            $biographicalInfo = get_user_meta($instructor_id, 'Instructor_About', true);
            $instructor = [
              "image" => $data->_source->data->details->instructor_image,
              "name" => $instructorDisplayName,
              "email" => $instructorEmail,
              "id" => (int)$instructor_id,
              "city" => isset($city_name) ? $city_name : "",
              "state" => get_user_meta($instructor_id, 'yuno_user_address_state', true),
              "country" => get_user_meta($instructor_id, 'yuno_user_address_country', true),
              "about" => $biographicalInfo,
              "name_email" => $instructorDisplayName . " (" . $instructorEmail . ")"
            ];

            $enrollment_count = $wpdb->get_results("SELECT * FROM wp_enrolled where classId=$post_id and userId=$user_id");
            if (count($enrollment_count) > 0) {
              $hasWebinarEnrolled = true;
            } else {
              $hasWebinarEnrolled = false;
            }
            $resultCategoryWebinarArray = get_post_meta($post_id, 'Category_Tree_Structure', true);
            $resource_webinar_taxonomy = [];
            $resource_webinar_taxonomy[] = $resultCategoryWebinarArray['id'];
            $sub_1_category = $resultCategoryWebinarArray['sub_category'];
            if (!empty($sub_1_category)) {
              foreach ($sub_1_category as $v) {
                $resource_webinar_taxonomy[] = $v['id'];
                if (!empty($v['sub_category'])) {
                  foreach ($v['sub_category'] as $vv) {
                    $resource_webinar_taxonomy[] = $vv['id'];
                  }
                }
              }
            }
            $endDate = date("Y-m-d\TH:i:s+05:30", strtotime($EventEndDate));
            $startDate = date("Y-m-d\TH:i:s+05:30", strtotime($EventStartDate));
            $webinars[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->ClassExcerpt, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $data->_source->data->details->event_type,
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->PreClassAssignment, ENT_NOQUOTES, 'UTF-8'),
              'display_image' => get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'instructor' => $instructor,
              'end_date' => $endDate,
              'start_date' => $startDate,
              'time' => strtotime($startDate),
              'taxonomy' => $resource_webinar_taxonomy,
              'privacy' => get_post_meta($post_id, 'WEBINAR_PRIVACY', true),
              'url' => $data->_source->data->details->class_detail,
              'is_enrolled' => $hasWebinarEnrolled,
              'promo_video' => $data->_source->data->details->PromoVideo,
              'published_at' => $date . "at" . $time,
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => false, "share" => true, "download" => false],
              "is_active" => false
            );
          }
        }
      }

      $collections = array_merge($webinars, $others);
    } else if (count($event_types) > 1 && $exist_webinar == "no") {
      if ($res->status == 404) {
        $msg = $codes["CURL_FAIL"]["message"];
      } else {
        $collections = [];
        if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
          foreach ($res->hits->hits as $data) {
            $post_id = $data->_source->data->details->record_id;
            $featuredImage = $data->_source->data->details->featuredImage;
            $privacy = $data->_source->data->details->privacy;
            $url = $data->_source->data->details->url;
            $published_at = $data->_source->data->details->published_at;
            $published_date = !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id);
            $post_type = $data->_source->data->details->resource_type;
            if ($post_type == "video") {
              $bookmark = true;
              $share = true;
              $download = false;
              $is_active = false;
            }
            if ($post_type == "document") {
              $bookmark = true;
              $share = true;
              $download = true;
              $is_active = false;
            }
            if ($post_type == "ebook") {
              $bookmark = true;
              $share = true;
              $download = true;
              $is_active = false;
            }
            if ($post_type == "article") {
              $bookmark = true;
              $share = true;
              $download = false;
              $is_active = false;
            }

            $collections[] = array(
              'id' => $post_id,
              'title'  => html_entity_decode($data->_source->data->details->title, ENT_NOQUOTES, 'UTF-8'),
              'slug'  => get_post_field('post_name', $post_id),
              'excerpt' => html_entity_decode($data->_source->data->details->excerpt, ENT_NOQUOTES, 'UTF-8'),
              'post_type'  => $post_type,
              'event_date'  => $data->_source->data->details->event_date,
              'description'  => html_entity_decode($data->_source->data->details->description, ENT_NOQUOTES, 'UTF-8'),
              'display_image' => !empty($featuredImage) ? $featuredImage : get_stylesheet_directory_uri() . '/inc/img/videoDefault.jpg',
              'taxonomy' => $data->_source->data->details->taxonomy,
              'time' => strtotime($published_date),
              'privacy' => !empty($privacy) ? $privacy : '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
              'url' => str_ends_with($url, "//") ? rtrim($url, '/').'/'.get_post_field('post_name', $post_id) : $url,
              'published_at' => !empty($published_at) ? $published_at : get_the_time('M j, Y g:i A', $post_id),
              'post_login_access' => $post_login_access,
              "actions" => ["bookmark" => $bookmark, "share" => $share, "download" => $download],
              "is_active" => $is_active
            );
          }
        }
      }
    }

    $this->array_sort_by_column($collections, 'time');
    if ($days != "0") {
      $collected = [];
      foreach ($collections as $collect) {
        $current_post_status = get_post_status($collect["id"]);
        if ($current_post_status == "publish") {
          if ($calculatedDate < date('Y-m-d H:i:s') && $collect['event_date'] > $calculatedDate) {
            $collected[] = $collect;
          }
        }
      }
    } else {
      foreach ($collections as $collect) {
        $current_post_status = get_post_status($collect["id"]);
        if ($current_post_status == "publish") {
          $collected[] = $collect;
        }
      }
    }

    // $collection=array_slice($collected,$offset,$limit);
    if (is_array($collected)) {
      $total_records = count($collected);
      $collection = array_slice($collected, $offset, $limit);
    }

    if (empty($collection)) {
      return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", $msg, $codes["RESOURCE_GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
    }
    $selections = [];
    if ($user_id > 0) {
      foreach ($collection as $key => $data) {
        $current_post_status = get_post_status($data['id']);
        if ($current_post_status == "publish") {
          $bookmark_status = filter_bookmark_of_resource_item_of_user($user_id, $data['id']);
          $selections[] = $data;
          $selections[$key]['bookmark_status'] = $bookmark_status;
        }
      }
    } else {
      $bookmark_status = false;
      foreach ($collection as $key => $data) {
        $current_post_status = get_post_status($data['id']);
        if ($current_post_status == "publish") {
          $selections[] = $data;
          $selections[$key]['bookmark_status'] = $bookmark_status;
        }
      }
    }
    if ($display_view == "list-view") {
      $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", $msg, $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $selections);
      // if($offset == 0 && count($event_types)==5 && $days==0 && $flag=="true"){
      //   add_cache('list_view_get_resources_v2_'.$user_id."_".$criteria."_".$category, $result, '', 3600);
      // }        
    } else {
      $resourcesData = [
        "rows" => $selections,
        "columns" => [
          [
            "field" => "title",
            "label" => "Title",
            "sortable" => true
          ],
          [
            "field" => "published_at",
            "label" => "First Published",
            "sortable" => true
          ]
        ]
      ];

      $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", $msg, $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $resourcesData);
      // if($offset == 0 && count($event_types)==5 && $days==0 && $flag=="false"){
      //   add_cache('grid_view_get_resources_v2_'.$user_id."_".$criteria."_".$category, $result, '', 3600);
      // }      
    }
    return new WP_REST_Response($result, 200);
  }
  /**
   * Getting (videos/ articles/ documents/ ebooks) resources per category from es
   */
  public function get_resources_count_per_category($request)
  {
    global $wpdb;
    $codes = error_code_setting();
    $ids = $request->get_param('ids');
    $query = json_decode($ids, true);
    $resourceCategories = $query['filters'][0]['options'];
    $wcatTerms = get_terms('course_category', array('hide_empty' => 0, 'parent' => 0));
    $filter_1 = [];
    foreach ($wcatTerms as $wcatTerm) {
      $filter_1[] = $wcatTerm->term_id;
    }
    if (!in_array($resourceCategories[0], $filter_1)) {
      return new WP_Error($codes["GET_FAIL"]["code"], "Sorry, we couldn't find any resource count that match your search", array('status' => $codes["GET_FAIL"]["status"]));
    }
    $selected = ["video", "article", "ebook", "document"];
    //$resourcesCountCacheData = get_cache("get_resources_count_v2_".$ids);    
    // $str = '{
    //   "filters": [
    //       {
    //           "type": "categories",
    //           "options": ["3060"]
    //       }           
    //   ]
    // }';https://charlesdickens.wpengine.com/wp-json/yuno/v2/resources?ids=%7B%22filters%22:%5B%7B%22type%22:%22categories%22,%22options%22:%5B%224328%22%5D%7D%5D%7D&sdfsdf=sdfsmm

    if (!empty($resourcesCountCacheData)) {
      return new WP_REST_Response($resourcesCountCacheData, 200);
    }

    $curlPost = [
      "query" => [
        "constant_score" => [
          "filter" => [
            "bool" => [
              "must" => [
                [
                  "terms" => [
                    "data.details.resource_type" => $selected
                  ]
                ],
                [
                  "terms" => [
                    "data.details.taxonomy" => $resourceCategories
                  ]
                ]
              ]
            ]
          ]
        ]
      ],
      "fields" => [
        "data.details.record_id",
        "data.details.resource_type"
      ],
      "_source" => false
    ];
    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_PORT => ELASTIC_SEARCH_PORT,
      CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/resourceevent/_search?size=" . ELASTIC_RECORDS_COUNT . "&filter_path=hits.hits.fields",
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "GET",
      CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
      CURLOPT_HTTPHEADER => array(
        "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
        "cache-control: no-cache",
        "content-type: application/json"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);
    $res = json_decode($response);
    if ($res->status == 404) {
      $msg = $codes["CURL_FAIL"]["message"];
    } else {
      $collections = [];
      if (is_array($res->hits->hits) && count($res->hits->hits) > 0) {
        $video_count = $article_count = $document_count = $ebook_count = 0;
        foreach ($res->hits->hits as $data) {
          $record_id = "data.details.record_id";
          $resource_type = "data.details.resource_type";
          $current_post_status = get_post_status($data->fields->$record_id[0]);
          if ($current_post_status == "publish") {
            if ($data->fields->$resource_type[0] == "video") {
              $video_count++;
            }

            if ($data->fields->$resource_type[0] == "article") {
              $article_count++;
            }

            if ($data->fields->$resource_type[0] == "document") {
              $document_count++;
            }

            if ($data->fields->$resource_type[0] == "ebook") {
              $ebook_count++;
            }
          }
        }
      }
      $total_records = $video_count + $article_count + $ebook_count + $document_count;
      if ($video_count == 1) {
        $video_name = "Video";
      } else {
        $video_name = "Videos";
      }

      if ($article_count == 1) {
        $article_name = "Article";
      } else {
        $article_name = "Articles";
      }

      if ($ebook_count == 1) {
        $ebook_name = "Ebook";
      } else {
        $ebook_name = "Ebooks";
      }

      if ($document_count == 1) {
        $document_name = "Document";
      } else {
        $document_name = "Documents";
      }
      $video = array("type" => "video", "label" => $video_name, "count" => $video_count);
      $article = array("type" => "article", "label" => $article_name, "count" => $article_count);
      $ebook = array("type" => "ebook", "label" => $ebook_name, "count" => $ebook_count);
      $document = array("type" => "document", "label" => $document_name, "count" => $document_count);
      $collections = [$video, $article, $ebook, $document];
    }
    $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Resource", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $collections);
    //add_cache('get_resources_count_v2_'.$ids, $result, '', 3600);    
    return new WP_REST_Response($result, 200);
  }
  /**
   * Getting (videos/ articles/ documents/ ebooks) resources per category from es
   */
  public function get_resources_count_per_category_permissions_check()
  {
    return true;
  }
  /**
   * Getting all resources
   */
  public function get_resources_permissions_check()
  {
    return true;
  }

  /**
   * Add a single calendar event
   */
  public function add_single_event($request) {
    $calendar_id = $request['calendar_id'];
    $body = $request->get_json_params();
    
    $summary = $body['summary'] ?? '';
    $description = $body['description'] ?? '';
    $start = $body['start'] ?? '';
    $end = $body['end'] ?? '';
    $timezone = $body['timezone'] ?? 'UTC';
    $attendees = $body['attendees'] ?? [];
    $class_id = $body['class_id'] ?? 0;
    $class_excerpt = $body['class_excerpt'] ?? '';

    if (empty($summary) || empty($start) || empty($end)) {
      return new WP_Error('missing_fields', 'Required fields are missing', array('status' => 400));
    }

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];

    try {
      $result = $googleCalendar->CreateCalendarEvent(
        $calendar_id,
        $summary,
        $start,
        $end,
        $timezone,
        $access_token,
        $class_excerpt,
        $class_id,
        array_map(function($email) { return ['email' => $email]; }, $attendees)
      );
      return new WP_REST_Response($result, 200);
    } catch (Exception $e) {
      return new WP_Error('event_creation_failed', $e->getMessage(), array('status' => 500));
    }
  }

  /**
   * Add a recurring calendar event
   */
  public function add_recurring_event($request) {
    $calendar_id = $request['calendar_id'];
    $body = $request->get_json_params();
    
    $summary = $body['summary'] ?? '';
    $description = $body['description'] ?? '';
    $start_date = $body['start_date'] ?? '';
    $end_date = $body['end_date'] ?? '';
    $days_of_week = $body['days_of_week'] ?? [];
    $start_time = $body['start_time'] ?? '';
    $end_time = $body['end_time'] ?? '';
    $timezone = $body['timezone'] ?? 'UTC';
    $batch_id = $body['batch_id'] ?? 0;

    if (empty($summary) || empty($start_date) || empty($end_date) || empty($days_of_week) || empty($start_time) || empty($end_time)) {
      return new WP_Error('missing_fields', 'Required fields are missing', array('status' => 400));
    }

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];

    try {
      $result = $googleCalendar->addBatchInCalendar(
        get_current_user_id(),
        $description,
        $start_date,
        $end_date,
        $start_time,
        $end_time,
        $days_of_week,
        $batch_id
      );
      return new WP_REST_Response($result, 200);
    } catch (Exception $e) {
      return new WP_Error('event_creation_failed', $e->getMessage(), array('status' => 500));
    }
  }

  /**
   * Delete a calendar event
   */
  public function delete_event($request) {
    $calendar_id = $request['calendar_id'];
    $event_id = $request['event_id'];

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];

    try {
      $result = $googleCalendar->DeleteCalendarEvent($event_id, $access_token, get_current_user_id());
      return new WP_REST_Response(['message' => 'Event deleted successfully'], 200);
    } catch (Exception $e) {
      return new WP_Error('event_deletion_failed', $e->getMessage(), array('status' => 500));
    }
  }

  /**
   * Update a calendar event
   */
  public function update_event($request) {
    $calendar_id = $request['calendar_id'];
    $event_id = $request['event_id'];
    $event_startdatetime = $request['event_startdatetime'];
    $event_duration = $request['event_duration'];
    $batch_id = $request['batch_id'];
    $body = $request->get_json_params();

    $new_calendar_id = $body['new_calendar_id'] ?? $calendar_id;
    $summary = $body['summary'] ?? null;
    $description = $body['description'] ?? null;
    $timezone = $body['timezone'] ?? 'UTC';
    $class_id = $body['class_id'] ?? 0;

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];
    
    try {
      $result = $googleCalendar->updateAndMoveSingleEvent(
        get_current_user_id(),
        $class_id,
        $new_calendar_id,
        $summary,
        $description,
        $event_startdatetime,
        date('Y-m-d\TH:i:s', strtotime($event_startdatetime . ' + ' . $event_duration)),
        $timezone
      );
      return new WP_REST_Response($result, 200);
    } catch (Exception $e) {
      return new WP_Error('event_update_failed', $e->getMessage(), array('status' => 500));
    }
  }

  /**
   * Update a recurring calendar event
   */
  public function update_recurring_event($request) {
    $calendar_id = $request['calendar_id'];
    $event_id = $request['event_id'];
    $event_startdatetime = $request['event_startdatetime'];
    $event_enddatetime = $request['event_enddatetime'];
    $weekdays = explode(',', $request['weekdays']);
    $body = $request->get_json_params();

    $description = $body['description'] ?? '';
    $batch_id = $body['batch_id'] ?? $event_id;

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];

    try {
      $result = $googleCalendar->updateBatchInCalendar(
        get_current_user_id(),
        $description,
        $event_startdatetime,
        $event_enddatetime,
        date('H:i:s', strtotime($event_startdatetime)),
        date('H:i:s', strtotime($event_enddatetime)),
        $weekdays,
        $batch_id
      );
      return new WP_REST_Response($result, 200);
    } catch (Exception $e) {
      return new WP_Error('event_update_failed', $e->getMessage(), array('status' => 500));
    }
  }

  /**
   * Delete a specific instance of a recurring event
   */
  public function delete_event_instance($request) {
    $calendar_id = $request['calendar_id'];
    $event_id = $request['event_id'];
    $event_startdatetime = $request['event_startdatetime'];
    $duration = $request['duration'];

    $googleCalendar = new GoogleCalendarApiV2();
    $accessTokenResponse = $googleCalendar->GetAccessToken(get_current_user_id());

    if (is_wp_error($accessTokenResponse) || empty($accessTokenResponse->get_data()['access_token'])) {
      return new WP_Error('token_error', 'Failed to get access token', array('status' => 401));
    }

    $access_token = $accessTokenResponse->get_data()['access_token'];

    try {
      $service = new Google_Service_Calendar($googleCalendar->getClient());
      $result = $googleCalendar->deleteEvent($service, $calendar_id, $event_id);
      return new WP_REST_Response(['message' => 'Event instance deleted successfully'], 200);
    } catch (Exception $e) {
      return new WP_Error('event_deletion_failed', $e->getMessage(), array('status' => 500));
    }
  }
}
