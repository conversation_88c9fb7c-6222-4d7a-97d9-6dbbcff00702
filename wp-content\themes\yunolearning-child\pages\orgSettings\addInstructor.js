Vue.component("yuno-add-instructor", {
  props: {
    data: {
      type: Object,
      required: true,
    },
    currentTabSlug: {
      type: String,
      required: true,
    },
  },
  template: `
    <div class="addInstructor">
  		<div class="tagInputWrapper">
			<validation-observer 
				tag="div" 
				class="observer"
				ref="addInstructorFormObserver" 
				v-slot="{ handleSubmit, invalid }"
			>
				<form @submit.prevent="handleSubmit(initDemoForm)">
					<validation-provider 
						tag="div" 
						class="fieldWrapper" 
						:rules="{ required: true }" 
						v-slot="{ errors, classes }"
					>
						<b-field :label="data.label">
							<b-taginput
								v-model="selectedInstructors"
								:data="filteredInstructors"
								autocomplete
								open-on-focus
								field="name"
								placeholder="Add instructors"
								@typing="updateSearchText"
								@select="handleInstructorSelect"
								class="demoInstructorTagInput">
							</b-taginput>
						</b-field>
						<p class="error">{{errors[0]}}</p>
					</validation-provider>
					<div class="ctaWrapper">
						<b-button
							native-type="reset"
							@click="closeModal"
							class="yunoPrimaryCTA wired fat">
							Cancel
						</b-button>    
						<b-button
							native-type="submit"
							:loading="filters.loading"
							:disabled="filters.isLoading"
							class="yunoSecondaryCTA fat">
							Save
						</b-button>    
					</div>
				</form>
			</validation-observer>
		</div>
    </div>
  `,
  computed: {
    ...Vuex.mapState(["filters", "filterResult"]),
    filteredInstructors() {
      if (this.filters.data.length > 0) {
        return this.filters.data.filter((items) => {
          return items.name
            .toString()
            .toLowerCase()
            .includes(this.searchText.toLowerCase());
        });
      } else {
        return this.filters.data;
      }
    },

    selectedInstructors: {
      get() {
        // Ensure we're working with an array
        const instructors = Array.isArray(this.filters.payload.instructors)
          ? this.filters.payload.instructors
          : [];

        // Map the instructor IDs to full instructor objects with names
        return instructors
          .map((instructor) => {
            const fullInstructor = this.filters.data.find(
              (item) => item.id === instructor.id
            );
            return fullInstructor || null;
          })
          .filter(Boolean); // Remove any null values
      },
      set(value) {
        // Update the payload with the new selection
        this.filters.payload.instructors = value.map((instructor) => ({
          id: instructor.id,
        }));
      },
    },
  },
  data() {
    return {
      searchText: "",
    };
  },
  methods: {
    updateSearchText(text) {
      this.searchText = text;
    },
    closeModal() {
      this.$emit("closeModal");
    },
    handleInstructorSelect(instructor) {
      if (instructor && instructor.id) {
        // Ensure instructors array exists
        if (!Array.isArray(this.filters.payload.instructors)) {
          this.filters.payload.instructors = [];
        }

        // Check if instructor is already selected
        const exists = this.filters.payload.instructors.some(
          (item) => item.id === instructor.id
        );

        if (!exists) {
          // Add the new instructor
          this.filters.payload.instructors.push({
            id: instructor.id,
          });
        }
      }
    },
    initDemoForm() {
      // Category is already set in the parent component
      this.$emit("submitForm");
    },
  },
});
