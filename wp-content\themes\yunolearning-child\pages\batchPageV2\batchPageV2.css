@font-face {
  font-family: 'Material Icons Outlined';
  font-style: normal;
  font-weight: 400;
  src: url("../../assets/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url("../../assets/fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}

.ylIcon, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .pageTitle, #app .batch, #app .course, #app .course .subtitle, #app .batches .batchCard, #app .filtersWrapper .button.is-primary, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item, #app .filtersWrapper .filterMenu .filterSet .listCaption, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .batch .batchInfo li, #app .mappedInstructor .insRating .caption, #app .mappedInstructor .studentCount, #app .course .description, #app .yunoModal .successMsg .alertGrid .alertGridLabel, #app .batchesCount, #app .batches .batchCard .scheduleInfo li, #app .batches .batchCard .mappedInstructor .studentCount, #app .batches .batchCard .cardFooter .full, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemLabel {
  color: rgba(0, 0, 0, 0.6);
}

#app .orgEnabled .isSticky {
  display: none;
}

#app .orgEnabled .yunoFooter {
  display: none;
}

#app .orgEnabled .yunoHeader .logo {
  margin-left: 0;
}

#app .checkout {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .checkout {
    margin-bottom: 60px;
  }
}

#app .yunoHeader .logo img.square {
  height: 72px;
  width: auto;
}

#app .alignR {
  text-align: right;
}

#app .mainHeader {
  margin-top: 30px;
  margin-bottom: 30px;
}

#app .mainHeader .block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (min-width: 768px) {
  #app .mainHeader .block {
    padding: 0;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

#app .pageTitle {
  font-size: 32px;
  line-height: 40px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .batch {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 15px;
}

#app .batch .spaceBetween {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .batch .price {
  font-size: 28px;
  line-height: 30px;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .batch .batchInfo li {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 10px;
}

#app .batch .batchInfo li.grid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .batch .batchInfo li .classDays {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 95px);
          flex: 0 0 calc(100% - 95px);
  margin-left: 15px;
}

#app .batch .batchInfo li .classDays li {
  text-transform: uppercase;
  font-size: 10px;
  margin-right: 5px;
  background-color: rgba(168, 30, 34, 0.04);
  border: 1px solid #A81E22;
  padding: 3px 8px;
  border-radius: 100px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 35px;
          flex: 0 0 35px;
  text-align: center;
  color: #A81E22;
  margin-top: 5px;
}

@media (min-width: 1214px) {
  #app .batch .batchInfo li .classDays li {
    margin-top: 0;
  }
}

#app .batch .batchInfo li .classDays li:last-child {
  margin-right: 0;
}

#app .batch .batchInfo li .classDays li.disabled {
  background-color: rgba(0, 0, 0, 0.02);
  color: rgba(0, 0, 0, 0.38);
  border-color: transparent;
}

#app .mappedInstructor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 30px;
}

#app .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  margin-right: 10px;
}

#app .mappedInstructor .imgWrapper img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}

#app .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
}

#app .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .mappedInstructor .insRating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  left: -3px;
}

#app .mappedInstructor .insRating .material-icons, #app .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .filtersWrapper .button .icon .mappedInstructor .insRating .mdi.mdi-menu-down:after, #app .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .filtersWrapper .button .icon .mappedInstructor .insRating .mdi.mdi-menu-up:after, #app .mappedInstructor .insRating .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .mappedInstructor .insRating .dropdown-item:before, #app .mappedInstructor .insRating .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .mappedInstructor .insRating .dropdown-item:before {
  color: #F9B600;
  margin-right: 5px;
  font-size: 18px;
}

#app .mappedInstructor .insRating .caption {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

#app .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 10px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  margin-top: 10px;
  text-transform: uppercase;
}

#app .course {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 30px 60px;
  margin-top: 30px;
}

@media (min-width: 768px) {
  #app .course {
    margin-top: 0;
  }
}

#app .course .name {
  font-size: 24px;
  line-height: 28px;
  font-weight: 400;
  margin-bottom: 10px;
}

#app .course .description {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .course .subtitle {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .course .orderDetail li {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .course .orderDetail li.primary {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 15px;
  padding-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 30px;
}

#app .course .orderDetail li .withGrid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .course .orderDetail li .withGrid a {
  margin-left: 5px;
}

#app .course .orderDetail li .withGrid a .material-icons-outlined {
  font-size: 18px;
}

#app .course .yunoSecondaryCTA {
  width: 100%;
  margin-bottom: 30px;
}

#app .course .caption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .course .note {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .course .formWrapper {
  margin-bottom: 30px;
}

#app .course .formWrapper #yunoForm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

#app .course .formWrapper .field {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(40% - 15px);
          flex: 0 0 calc(40% - 15px);
  margin-right: 15px;
  margin-bottom: 0;
}

#app .course .formWrapper .field .label .b-tooltip {
  top: 6px;
}

#app .course .formWrapper .button {
  height: 36px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 30%;
          flex: 0 0 30%;
}

#app .course .formWrapper .msg {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60%;
          flex: 0 0 60%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 6px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .course .formWrapper .msg.success {
  color: green;
}

#app .course .formWrapper .msg.error {
  color: red;
}

#app .course .formWrapper .msg .material-icons, #app .course .formWrapper .msg .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .filtersWrapper .button .icon .course .formWrapper .msg .mdi.mdi-menu-down:after, #app .course .formWrapper .msg .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .filtersWrapper .button .icon .course .formWrapper .msg .mdi.mdi-menu-up:after, #app .course .formWrapper .msg .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .course .formWrapper .msg .dropdown-item:before, #app .course .formWrapper .msg .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .course .formWrapper .msg .dropdown-item:before {
  margin-right: 5px;
}

#app .yunoModal.hasMinHeight .modalBody {
  min-height: 500px;
}

#app .yunoModal .successMsg h3 {
  font-size: 20px;
  margin-bottom: 15px;
}

#app .yunoModal .successMsg .subTitle {
  margin-bottom: 15px;
}

#app .yunoModal .successMsg .alertGrid {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  font-size: 14px;
}

#app .yunoModal .successMsg .alertGrid .alertGridVal {
  margin-bottom: 10px;
}

#app .yunoModal .successMsg p {
  font-size: 12px;
}

#app .yunoModal .subscriptionList {
  margin-bottom: 10px;
  background: #FCFCFC;
  border: 1px solid #eee;
  border-radius: 4px;
}

#app .yunoModal .subscriptionList:last-child {
  margin-bottom: 0;
}

#app .yunoModal .hasScroll {
  overflow-y: auto;
}

#app .yunoModal .modalBody.noOverFlow {
  overflow: visible;
}

#app .batchesCount {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .batches {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
}

#app .batches .batchCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .batches .batchCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
  }
}

#app .batches .batchCard .wrapper {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
  border: 1px solid transparent;
}

#app .batches .batchCard .wrapper:hover {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

#app .batches .batchCard .wrapper:hover .ctaWrapper, #app .batches .batchCard .wrapper:hover .full {
  visibility: visible;
}

#app .batches .batchCard .batchdate {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .batches .batchCard .batchdate .material-icons, #app .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-down:after, #app .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-up:after, #app .batches .batchCard .batchdate .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .batches .batchCard .batchdate .dropdown-item:before, #app .batches .batchCard .batchdate .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .batches .batchCard .batchdate .dropdown-item:before {
  margin-right: 5px;
}

#app .batches .batchCard .days {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 10px;
}

#app .batches .batchCard .days li {
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  color: rgba(0, 0, 0, 0.38);
  text-transform: uppercase;
}

#app .batches .batchCard .days li.isActive {
  color: #A81E22;
}

#app .batches .batchCard .scheduleInfo {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

#app .batches .batchCard .scheduleInfo li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 5px;
}

#app .batches .batchCard .scheduleInfo li .material-icons-outlined {
  font-size: 16px;
}

#app .batches .batchCard .scheduleInfo li .itemCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 5px;
}

#app .batches .batchCard .scheduleInfo li .hasBG {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  background-color: rgba(168, 30, 34, 0.04);
  border-radius: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 4px 10px;
}

#app .batches .batchCard .scheduleInfo li .hasBG .material-icons-outlined {
  margin-right: 5px;
}

#app .batches .batchCard .mappedInstructor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

#app .batches .batchCard .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  margin-right: 10px;
}

#app .batches .batchCard .mappedInstructor .imgWrapper img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}

#app .batches .batchCard .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
}

#app .batches .batchCard .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .batches .batchCard .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

#app .batches .batchCard .cardFooter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .batches .batchCard .cardFooter .price {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .batches .batchCard .cardFooter .ctaWrapper {
    visibility: hidden;
  }
}

#app .batches .batchCard .cardFooter .full {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 5px;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  visibility: hidden;
}

#app .filtersWrapper {
  padding: 0;
  margin: 0 0 15px;
}

@media (min-width: 768px) {
  #app .filtersWrapper {
    padding: 0;
  }
}

#app .filtersWrapper > ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
}

#app .filtersWrapper > ul li {
  padding: 0 15px;
}

@media (min-width: 768px) {
  #app .filtersWrapper > ul li {
    padding: 0 0 0 15px;
  }
}

#app .filtersWrapper > ul li.course_search, #app .filtersWrapper > ul li.search_instructor {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  #app .filtersWrapper > ul li.course_search, #app .filtersWrapper > ul li.search_instructor {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 400px;
            flex: 0 0 400px;
    margin-bottom: 0;
  }
}

#app .filtersWrapper > ul li.isFocus.search .field .control input[type="text"] {
  border-color: black;
}

#app .filtersWrapper > ul li.search .field .control input[type="text"] {
  height: 40px;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .filtersWrapper > ul li.search .dropdown-menu .dropdown-item .subtitle {
  font-size: 10px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 0;
}

#app .filtersWrapper > ul li.singleCheck {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (min-width: 1280px) {
  #app .filtersWrapper > ul li.singleCheck {
    margin-top: 15px;
  }
}

@media (min-width: 1800px) {
  #app .filtersWrapper > ul li.singleCheck {
    margin-top: 0;
  }
}

#app .filtersWrapper > ul li.singleCheck .b-checkbox {
  margin-bottom: 0;
}

#app .filtersWrapper > ul li.singleCheck .b-checkbox.is-disabled {
  cursor: not-allowed;
}

#app .filtersWrapper.loading .b-skeleton {
  width: auto;
  margin: 0 0 0 15px;
}

#app .filtersWrapper .fa-filter {
  font-size: 24px;
  padding-right: 15px;
}

#app .filtersWrapper .button.is-primary {
  background-color: #FFF;
  color: #000;
  font-size: 14px;
  border-color: rgba(0, 0, 0, 0.12);
  font-size: 14px;
  border-radius: 4px;
  padding: 8px 15px 9px;
}

#app .filtersWrapper .button.is-primary:active, #app .filtersWrapper .button.is-primary:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .filtersWrapper .button .icon {
  margin: 0 0 0 5px;
  position: relative;
  top: 1px;
}

#app .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c5";
}

#app .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c7";
}

#app .filtersWrapper .filterMenu {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 15px;
  margin-left: 0;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
    margin: 0;
  }
}

#app .filtersWrapper .filterMenu.active button.filter {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .filtersWrapper .filterMenu.active button.filter .icon {
  color: #A81E22;
}

#app .filtersWrapper .filterMenu.is-active button {
  border-color: rgba(0, 0, 0, 0.87);
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-menu {
  padding: 0;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content {
  padding: 0;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle) {
  padding: 6px 15px 6px 40px;
  position: relative;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  content: "unCheck";
  position: absolute;
  left: 15px;
  top: 10px;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle).is-active:before {
  content: "checked";
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 15px 0;
  padding: 15px 0;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper .yunoSecondaryCTA {
  border-color: #A81E22;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
    width: 440px;
  }
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before {
  font-family: 'Material Icons' !important;
  position: static;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.night:before {
  content: "\e51c";
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 0;
  padding-bottom: 5px;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
    width: 440px;
  }
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:hover {
  background: none;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:before {
  display: none;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 15px 0 0;
  padding-bottom: 5px;
}

#app .filtersWrapper .filterMenu.class_days_time .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .filtersWrapper .filterMenu.class_days_time .ctaWrapper .button {
  border-color: #A81E22;
}

#app .filtersWrapper .filterMenu .filterSet .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 10px;
}

#app .filtersWrapper .filterMenu .filterSet.class_days .innerWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu .filterSet.class_days .innerWrapper {
    width: 440px;
  }
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:hover {
  background: none;
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:before {
  display: none;
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper {
    width: 440px;
  }
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before {
  font-family: 'Material Icons' !important;
  position: static;
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.night:before {
  content: "\e51c";
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .filtersWrapper .filterMenu .filterSet.class_time .ctaWrapper {
  border: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

#app .filtersWrapper .filterMenu .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .filtersWrapper .filterMenu .ctaWrapper .button {
  border-color: #A81E22;
}

#app .filtersWrapper .filterMenu .dropdown-trigger {
  width: 100%;
}

#app .filtersWrapper .filterMenu button {
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filterMenu button {
    width: auto;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

#app .filtersWrapper .filterMenu button > span {
  text-transform: capitalize;
}

#app .filtersWrapper .filterMenu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}

#app .filtersWrapper .filterMenu .dropdown-content a {
  color: rgba(0, 0, 0, 0.5);
}

#app .filtersWrapper .filterMenu .dropdown-content a.is-active {
  background: none;
  color: black;
}

#app .filtersWrapper .filterMenu .dropdown-content a:active, #app .filtersWrapper .filterMenu .dropdown-content a:focus {
  background: none;
  outline: none;
}
/*# sourceMappingURL=batchPageV2.css.map */