<?php

namespace V4;

/**
 * <PERSON>chController
 *
 * This class handles batch-related functionalities.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class BatchController extends Controller
{
    /**
     * BatchController Constructor
     *
     * Initializes the controller by loading required libraries and models 
     * needed for batch-related functionalities.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadModel('batch');
        $this->loadLibary('validate');
    }
    /**
     * Get a batch by ID
     *
     * Retrieves all info of batch from the es and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatch($request){
        try {
            $batchId = $request['batchId'];
            $batch = $this->batch->getBatch($batchId);
            if ($batch) {
                $this->response->success('GET_SUCCESS', $batch, ['message' => 'Batch retrieved successfully']);
            } else {
                $this->response->error('GET_FAIL', ['message' => 'Batch not found']);
            } 
        } catch (\Exception $e) {
            $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
            return;
        }
    }
}
