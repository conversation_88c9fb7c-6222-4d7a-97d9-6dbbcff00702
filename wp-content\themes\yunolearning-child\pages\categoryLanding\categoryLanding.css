.ylIcon, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .videosWrapper .videoCard figure .imgWrapper::before, #app .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .clearFilters .tags .is-delete::before {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark38, #app .practiceTests .filters li .innerWrap, #app .practiceTests .cardWrapper .yunoCard p, #app .practiceTests .cardWrapper .yunoCard .attempt li, #app .practiceTests .cardWrapper .yunoCard .attempt li:first-child::after {
  color: rgba(0, 0, 0, 0.38);
}

.dark87, .largerTitle, #app, #app .yunoTabsStatic .tabsV2 li a:hover, #app .yunoTabsStatic .tabsV2 li.is-active a, #app .courses .courseCard .courseInstructor, #app .studentsResultsWrapper .yunoStudentCard .wrapper, #app .studentsResultsWrapper .yunoStudentCard .userImg .userIcn, #app .studentsResultsWrapper .yunoStudentCard .userImg figcaption h3, #app .sections .sectionWrapper h2, #app .sections .sectionWrapper h3, #app .sections .sectionWrapper h4, #app .practiceTests .filters li .innerWrap:hover, #app .practiceTests .filters li.active .innerWrap, #app .practiceTests .cardWrapper .yunoCard h2, #app .practiceTests .cardWrapper .yunoCard .count, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper, #app .categoryCourses .filtersWrapper .button.is-primary, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item, #app .categoryCourses .filtersWrapper .filterMenu .filterSet .listCaption, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item, #app .categoryCourses .clearFilters, #app .categoryCourses .clearFilters .tags .is-delete, #app .categoryCourses .courseCard .cardRating .vue-star-rating-rating-text .rating, #app .categoryCourses .courseCard .instructorsListWrapper, #app .categoryCourses .batches .batchCard, #app .collectionCard .innerWrap, #app .videoSelected .videoDetails .description h2, #app .videoSelected .videoDetails .description h3, #app .videoSelected .videoDetails .description h4 {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, .body1, #app .section .sectionHeader .description, .body2, #app .courses .courseCard .courseFeatures li, #app .courses .courseCard .courseInstructor .innerCaption, #app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li .key, #app .videosWrapper .videoCard .videoDetail .videoDate, #app .studentsResultsWrapper .yunoStudentCard .userImg .tag, #app .studentsResultsWrapper .yunoStudentCard .scoreList li .helper, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemLabel, #app .categoryCourses .coursesCount, #app .categoryCourses .back, #app .categoryCourses .courseCard .cardBlock .note, #app .categoryCourses .courseCard .highlighhts .listWithCheck li, #app .categoryCourses .courseCard .cardDescription, #app .categoryCourses .courseCard .cardRating .vue-star-rating-rating-text, #app .categoryCourses .courseCard .cardStats li .material-icons-outlined, #app .categoryCourses .courseCard .cardStats li .listLabel .dark, #app .categoryCourses .courseCard .totalTime, #app .categoryCourses .batchNotSelected .block .caption, #app .categoryCourses .batchesCount, #app .categoryCourses .batches .batchCard .scheduleInfo li, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .caption, #app .categoryCourses .batches .batchCard .mappedInstructor .studentCount, #app .categoryCourses .batches .batchCard .cardFooter .full, #app .collectionCard .cardDescription, #app .collectionCard .relatedResource li .listLable, #app .resultsCard .studentInfo .studentInfo_image figcaption h4, #app .resultsCard .studentInfo .studentInfo_score .score li .listLable, #app .videoSelected .videoDetails .description {
  color: rgba(0, 0, 0, 0.6);
}

.largestTitle {
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .largestTitle {
    font-size: 48px;
    line-height: 62px;
    font-weight: 700;
    margin-bottom: 10px;
  }
}

.largerTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 10px;
}

.largeTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallerCaption {
  font-size: 16px;
  line-height: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

.body1, #app .section .sectionHeader .description {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

.body2 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .filterActive {
  height: 100vh;
}

#app .filterActive .mainBody section + .container {
  position: static;
}

#app .mainBody.postLogin .filtersWrapper {
  top: 52px;
}

@media (min-width: 768px) {
  #app .forAd .filtersWrapper {
    top: 0;
  }
}

#app .filtersWrapper {
  top: 60px;
}

#app .filtersWrapper.mobileView .categoryFilter {
  display: none;
}

#app .filtersWrapper .filters.categoryFilter {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  position: absolute;
  right: 0;
  top: 0;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filters.categoryFilter {
    position: static;
  }
}

#app .filtersWrapper .filters.otherFilters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 15px;
}

#app .filtersWrapper .filters.otherFilters .yunoDropdown {
  display: none;
}

#app .filtersWrapper .filters.otherFilters .yunoDropdown:first-child {
  display: block;
}

@media (min-width: 768px) {
  #app .filtersWrapper .filters.otherFilters {
    margin-top: 0;
  }
  #app .filtersWrapper .filters.otherFilters .yunoDropdown {
    display: block;
  }
}

#app .filtersWrapper .filters.otherFilters.mobileView .yunoDropdown {
  display: block;
}

#app .filtersWrapper .categoryFilter .category {
  display: none;
}

@media (min-width: 768px) {
  #app .filtersWrapper {
    top: 70px;
  }
}

#app .filtersWrapper .b-tooltip.is-primary:after {
  background: #A81E22;
}

#app .emptyStateV2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 5%;
}

#app .emptyStateV2 figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .emptyStateV2 figure img {
  width: 158px;
  height: auto;
}

#app .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .emptyStateV2 figure .button {
  margin-top: 15px;
}

#app .heroBanner {
  position: relative;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 30px 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: auto;
}

@media (min-width: 768px) {
  #app .heroBanner {
    padding: 60px 0;
  }
}

#app .heroBanner .videoLPPlayer {
  position: relative;
  padding-bottom: 60.7%;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

#app .heroBanner .videoLPPlayer.loading .b-skeleton-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100% !important;
}

#app .heroBanner .videoLPPlayer iframe,
#app .heroBanner .videoLPPlayer object,
#app .heroBanner .videoLPPlayer embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#app .heroBanner .container {
  z-index: 3;
  height: 100%;
}

#app .heroBanner .container .row {
  height: 100%;
}

#app .heroBanner:before {
  content: "";
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(29, 29, 31, 0.6);
}

#app .heroBanner .colLeft {
  color: white;
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
}

#app .heroBanner .colLeft .ctaWrapper {
  margin-top: 15px;
}

#app .heroBanner .colLeft .ctaWrapper .button {
  height: 48px;
}

#app .heroBanner .colLeft .ctaWrapper .button .material-icons, #app .heroBanner .colLeft .ctaWrapper .button .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .heroBanner .colLeft .ctaWrapper .button .imgWrapper::before, #app .heroBanner .colLeft .ctaWrapper .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .heroBanner .colLeft .ctaWrapper .button .icon .mdi.mdi-menu-down:after, #app .heroBanner .colLeft .ctaWrapper .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .heroBanner .colLeft .ctaWrapper .button .icon .mdi.mdi-menu-up:after, #app .heroBanner .colLeft .ctaWrapper .button .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .heroBanner .colLeft .ctaWrapper .button .dropdown-item:before, #app .heroBanner .colLeft .ctaWrapper .button .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .heroBanner .colLeft .ctaWrapper .button .dropdown-item:before, #app .heroBanner .colLeft .ctaWrapper .button .categoryCourses .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .heroBanner .colLeft .ctaWrapper .button .is-delete::before {
  margin-left: 5px;
}

#app .heroBanner .colRight {
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
}

#app .heroBanner .colRight .wrapper {
  background-color: white;
  border-radius: 4px;
  padding: 30px;
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .heroBanner .colRight .wrapper {
    margin-top: 0;
  }
}

#app .heroBanner .colRight .yunoSecondaryCTA {
  margin-top: 15px;
}

#app .heroBanner .colRight .observer {
  margin-top: 15px;
}

#app .heroBanner .colRight .observer .field .label {
  font-size: 14px !important;
  color: rgba(0, 0, 0, 0.6);
}

#app .heroBanner .colRight .observer .field .error {
  display: none;
}

#app .heroBanner .colRight .observer .field .control input[type="text"] {
  height: 40px !important;
}

#app .heroBanner .colRight .observer .field .control.invalid + .error {
  display: block;
}

#app .heroBanner .colRight .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .heroBanner .colRight .googleLogin {
  border: 1px solid #1976D2;
  border-radius: 4px;
  width: 100%;
  padding: 9px 15px 8px;
  background-color: white;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #1976D2;
  font-weight: 500;
}

#app .heroBanner .colRight .googleLogin.width70 {
  width: 80%;
}

#app .heroBanner .colRight .googleLogin img {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

#app .yunoTabsStatic {
  margin-top: 15px;
  overflow-x: auto;
  position: -webkit-sticky;
  position: sticky;
  top: 60px;
  background-color: #FFF;
  z-index: 8;
}

@media (min-width: 768px) {
  #app .yunoTabsStatic {
    overflow: visible;
    top: 72px;
  }
  #app .yunoTabsStatic.postLogin {
    top: 0;
  }
}

#app .yunoTabsStatic .tabsV2 {
  border-color: rgba(0, 0, 0, 0);
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}

#app .yunoTabsStatic .tabsV2::after {
  content: "";
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.08);
  position: absolute;
  left: 0;
  bottom: 0;
}

#app .yunoTabsStatic .tabsV2 li {
  font-size: 14px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

#app .yunoTabsStatic .tabsV2 li a {
  color: rgba(0, 0, 0, 0.38);
  padding: 15px 20px;
  display: block;
  border-bottom: 1px solid transparent;
}

#app .yunoTabsStatic .tabsV2 li a:hover {
  text-decoration: none;
}

#app .yunoTabsStatic .tabsV2 li.is-active a {
  border-color: black;
  font-weight: 400;
  background: rgba(0, 0, 0, 0.02);
}

#app .section {
  padding: 0 0 30px;
  margin: 0 0 0;
}

@media (min-width: 768px) {
  #app .section {
    padding: 0 0 60px;
    margin: 30px 0 0;
  }
}

#app .section .sectionHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .section .sectionHeader {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

#app .section .sectionHeader .description {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 10px;
}

#app .section .sectionHeader .largeTitle {
  margin-bottom: 0;
}

#app .section .sectionHeader .actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .section .sectionHeader .actions li {
  padding: 0 10px;
}

#app .categoryStudyMaterial {
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .categoryStudyMaterial {
    margin-top: 30px;
  }
}

#app .courses .coursesWrapper {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .courses .courseCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .courses .courseCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    margin-bottom: 0;
  }
}

#app .courses .courseCard .wrapper {
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 24px;
  height: 100%;
  display: block;
  border: 1px solid transparent;
}

#app .courses .courseCard .wrapper .smallerCaption {
  color: initial;
}

#app .courses .courseCard .wrapper:hover {
  text-decoration: none;
  border-color: #E6E6E6;
}

#app .courses .courseCard .courseFeatures li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  margin-bottom: 5px;
}

#app .courses .courseCard .courseFeatures li .material-icons-outlined {
  font-size: 18px;
  margin-right: 5px;
}

#app .courses .courseCard .courseFeatures li:last-child {
  margin-bottom: 0;
}

#app .courses .courseCard .courseInstructor {
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding-top: 10px;
  margin-top: 10px;
}

@media (min-width: 768px) {
  #app .courses .courseCard .courseInstructor {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

#app .courses .courseCard .courseInstructor .innerCaption {
  padding-left: 5px;
}

#app .courses .courseCard .courseInstructor .instructorsList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 10px;
  padding-right: 10px;
}

#app .courses .courseCard .courseInstructor .instructorsList li {
  margin-left: -12px;
}

#app .courses .courseCard .courseInstructor .instructorsList .instructorMapped img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #FFF;
  font-size: 0;
}

#app .courses .courseCard .courseInstructor .instructorsList .material-icons, #app .courses .courseCard .courseInstructor .instructorsList .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .courses .courseCard .courseInstructor .instructorsList .imgWrapper::before, #app .courses .courseCard .courseInstructor .instructorsList .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .courses .courseCard .courseInstructor .instructorsList .mdi.mdi-menu-down:after, #app .courses .courseCard .courseInstructor .instructorsList .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .courses .courseCard .courseInstructor .instructorsList .mdi.mdi-menu-up:after, #app .courses .courseCard .courseInstructor .instructorsList .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .courses .courseCard .courseInstructor .instructorsList .dropdown-item:before, #app .courses .courseCard .courseInstructor .instructorsList .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .courses .courseCard .courseInstructor .instructorsList .dropdown-item:before, #app .courses .courseCard .courseInstructor .instructorsList .categoryCourses .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .courses .courseCard .courseInstructor .instructorsList .is-delete::before {
  font-size: 24px;
  background-color: #FFF;
  border-radius: 50%;
}

#app .studyMaterialWrapper {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .studyMaterialWrapper .studyMaterialCard {
  padding: 0 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .studyMaterialWrapper .studyMaterialCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
    margin-bottom: 0;
  }
}

#app .studyMaterialWrapper .studyMaterialCard .wrapper {
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 15px;
  height: 100%;
  display: block;
  border: 1px solid transparent;
}

#app .studyMaterialWrapper .studyMaterialCard .wrapper .largeTitle {
  color: initial;
}

#app .studyMaterialWrapper .studyMaterialCard .wrapper .studyMaterialFeatures li .val {
  color: initial;
}

#app .studyMaterialWrapper .studyMaterialCard .wrapper:hover {
  border-color: #E6E6E6;
  text-decoration: none;
}

#app .studyMaterialWrapper .studyMaterialCard figure img {
  max-width: 100%;
  max-height: 180px;
  height: auto;
  display: block;
  margin: 0 auto;
}

#app .studyMaterialWrapper .studyMaterialCard figure figcaption {
  margin-bottom: 15px;
}

#app .studyMaterialWrapper .studyMaterialCard figure .largeTitle {
  margin: 10px 0;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 5px;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li .val {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 5px;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li .key {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li:nth-child(odd) {
  position: relative;
}

#app .studyMaterialWrapper .studyMaterialCard .studyMaterialFeatures li:nth-child(odd)::after {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.08);
}

#app .videosWrapper {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .videosWrapper .emptyStateV2 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .videosWrapper .videoCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .videosWrapper .videoCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
  }
}

#app .videosWrapper .videoCard .wrapper {
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 0;
  border-radius: 4px;
  overflow: hidden;
}

#app .videosWrapper .videoCard figure .imgWrapper {
  display: block;
  position: relative;
  min-height: 196px;
}

#app .videosWrapper .videoCard figure .imgWrapper::before {
  content: "\e037";
  width: 40px;
  height: 28px;
  background-color: rgba(168, 30, 34, 0.38);
  border-radius: 4px;
  position: absolute;
  left: calc(50% - 20px);
  top: calc(50% - 14px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(255, 255, 255, 0.645);
}

#app .videosWrapper .videoCard figure img {
  width: 100%;
  height: auto;
  display: block;
}

#app .videosWrapper .videoCard figure figcaption {
  padding: 15px;
}

#app .videosWrapper .videoCard figure .largeTitle {
  margin: 0 0 10px;
}

#app .videosWrapper .videoCard .videoDetail {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .videosWrapper .videoCard .videoDetail .videoDate {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .videosWrapper .videoCard .videoDetail a {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  text-decoration: underline;
}

#app .videos .videoCard {
  cursor: pointer;
}

#app .videos .videoCard .wrapper {
  border: 1px solid transparent;
}

#app .videos .videoCard .wrapper:hover {
  border-color: rgba(0, 0, 0, 0.12);
}

#app .videos .videoCard.active .wrapper {
  border-color: rgba(0, 0, 0, 0.12);
}

#app .meetInstructors button[data-action="stop"], #app .meetInstructors .tns-visually-hidden {
  display: none;
}

#app .meetInstructors .tns-inner {
  overflow: hidden;
  margin: 0 -15px;
  padding: 30px 15px;
}

@media (min-width: 768px) {
  #app .meetInstructors .tns-inner {
    margin: 0 -30px;
  }
}

#app .meetInstructors .carouselListControls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .meetInstructors .carouselListControls button {
  width: 44px;
  height: 44px;
  background-color: #FFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 0;
}

#app .meetInstructors .carouselListControls button.prev {
  margin-right: 30px;
}

#app .meetInstructorsWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .meetInstructorsWrapper .meetInstructorsCard {
  padding: 0 15px;
}

#app .meetInstructorsWrapper .meetInstructorsCard.tns-slide-active .wrapper {
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
}

#app .meetInstructorsWrapper .meetInstructorsCard .wrapper {
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0);
  background-color: white;
  padding: 24px;
  height: 100%;
}

#app .meetInstructorsWrapper .meetInstructorsCard .smallerCaption {
  margin: 0;
  line-height: normal;
}

#app .meetInstructorsWrapper .meetInstructorsCard figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 15px;
}

#app .meetInstructorsWrapper .meetInstructorsCard figure .insImage {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  margin-right: 15px;
}

#app .meetInstructorsWrapper .meetInstructorsCard figure .insImage img {
  width: 100%;
  height: 64px;
  border-radius: 50%;
  background-color: #E6E6E6;
  font-size: 0;
  display: block;
}

#app .meetInstructorsWrapper .meetInstructorsCard figure figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
}

#app .meetInstructorsWrapper .meetInstructorsCard .stars {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .meetInstructorsWrapper .meetInstructorsCard .stars .vue-star-rating-rating-text {
  font-size: 14px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 10px;
  position: relative;
  top: 1px;
}

#app .meetInstructorsWrapper.loading .meetInstructorsCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 33.3%;
          flex: 0 0 33.3%;
}

#app .studentsResultsWrapper {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .studentsResultsWrapper .yunoStudentCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
}

@media (min-width: 768px) {
  #app .studentsResultsWrapper .yunoStudentCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    margin-bottom: 0;
  }
}

#app .studentsResultsWrapper .yunoStudentCard .wrapper {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
  margin-top: 30px;
}

#app .studentsResultsWrapper .yunoStudentCard .cardHeader {
  margin-bottom: 15px;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg .userIcn {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 20px;
  text-transform: uppercase;
  font-weight: 500;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg .userIcn img {
  height: 64px;
  width: 100%;
  border-radius: 50%;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg figcaption {
  margin-left: 15px;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg figcaption h3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  margin: 5px 0 0;
}

#app .studentsResultsWrapper .yunoStudentCard .userImg .tag {
  text-transform: uppercase;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 1.5px;
  font-weight: 500;
  border-radius: 15px;
  background-color: rgba(165, 166, 246, 0.2);
}

#app .studentsResultsWrapper .yunoStudentCard .userImg .tag.yellow {
  background-color: rgba(249, 182, 0, 0.2);
}

#app .studentsResultsWrapper .yunoStudentCard .cardBody h4 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 10px;
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-right: 10px;
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList li .helper {
  font-size: 12px;
  text-transform: lowercase;
  font-weight: 400;
  display: inline-block;
  padding: 0 10px 0 5px;
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList li::after {
  content: "|";
  display: inline-block;
  color: rgba(0, 0, 0, 0.2);
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList li:last-child {
  margin-right: 0;
}

#app .studentsResultsWrapper .yunoStudentCard .scoreList li:last-child::after {
  display: none;
}

#app .sections .sectionWrapper {
  margin-bottom: 30px;
}

#app .sections .sectionWrapper p {
  margin-bottom: 15px;
}

#app .sections .sectionWrapper h2 {
  font-size: 28px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
  margin-top: 15px;
}

#app .sections .sectionWrapper h3 {
  font-size: 24px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
  margin-top: 15px;
}

#app .sections .sectionWrapper h4 {
  font-size: 20px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
  margin-top: 15px;
}

#app .sections .sectionWrapper ul, #app .sections .sectionWrapper ol {
  padding: 0;
  margin: 0 0 15px 18px;
}

#app .sections .sectionWrapper ul li, #app .sections .sectionWrapper ol li {
  list-style: disc outside;
  margin-bottom: 5px;
  font-size: 16px;
}

#app .sections .sectionWrapper ul li:last-child, #app .sections .sectionWrapper ol li:last-child {
  margin-bottom: 0;
}

#app .sections .sectionWrapper ol li {
  list-style: decimal outside;
}

#app .yunoAccordion {
  margin-bottom: 60px;
}

#app .practiceTests .filters {
  margin: 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 0;
}

#app .practiceTests .filters li {
  padding: 0 0 0 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

@media (min-width: 768px) {
  #app .practiceTests .filters li {
    padding: 0 0 0 15px;
  }
}

#app .practiceTests .filters li .innerWrap {
  background-color: transparent;
  padding: 15px 20px;
  font-size: 14px;
  display: block;
  cursor: pointer;
  position: relative;
}

#app .practiceTests .filters li .innerWrap::after {
  content: "";
  width: 100%;
  height: 1px;
  background: rgba(0, 0, 0, 0.87);
  position: absolute;
  left: 0;
  bottom: 0;
  display: none;
}

#app .practiceTests .filters li .innerWrap:hover {
  text-decoration: none;
}

#app .practiceTests .filters li.active .innerWrap {
  background: rgba(0, 0, 0, 0.02);
}

#app .practiceTests .filters li.active .innerWrap:after {
  display: block;
}

#app .practiceTests .cardWrapper .listView {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -15px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .practiceTests .cardWrapper .yunoCard {
  padding: 0 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 30px;
}

@media (min-width: 1600px) {
  #app .practiceTests .cardWrapper .yunoCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
}

@media (min-width: 1366px) {
  #app .practiceTests .cardWrapper .yunoCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33%;
            flex: 0 0 33.33%;
  }
}

@media (min-width: 1280px) {
  #app .practiceTests .cardWrapper .yunoCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33%;
            flex: 0 0 33.33%;
  }
}

#app .practiceTests .cardWrapper .yunoCard .wrapper {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 24px;
  height: 100%;
}

@media (min-width: 768px) {
  #app .practiceTests .cardWrapper .yunoCard .wrapper .ctaWrapper {
    visibility: hidden;
  }
  #app .practiceTests .cardWrapper .yunoCard .wrapper:hover .ctaWrapper {
    visibility: visible;
  }
}

#app .practiceTests .cardWrapper .yunoCard h2 {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .practiceTests .cardWrapper .yunoCard p {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 30px;
  min-height: 80px;
  overflow-wrap: anywhere;
}

#app .practiceTests .cardWrapper .yunoCard .count {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  min-height: auto;
}

#app .practiceTests .cardWrapper .yunoCard .attempt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

#app .practiceTests .cardWrapper .yunoCard .attempt li {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  padding-right: 15px;
  position: relative;
}

#app .practiceTests .cardWrapper .yunoCard .attempt li:first-child::after {
  content: "|";
  position: absolute;
  right: 0;
  top: 0;
}

#app .practiceTests .cardWrapper .yunoCard .attempt li:last-child::after {
  content: "";
  display: none;
}

#app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper {
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 10px;
  padding-right: 10px;
}

#app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList li {
  margin-left: -12px;
}

#app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .instructorMapped img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #FFF;
  font-size: 0;
}

#app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .material-icons, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .imgWrapper::before, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .mdi.mdi-menu-down:after, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .mdi.mdi-menu-up:after, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .dropdown-item:before, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .dropdown-item:before, #app .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .categoryCourses .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .practiceTests .cardWrapper .yunoCard .instructorsListWrapper .instructorsList .is-delete::before {
  font-size: 24px;
  background-color: #FFF;
  border-radius: 50%;
}

#app .categoryCourses .filters {
  background: #FFF;
  z-index: 6;
  padding-bottom: 15px;
}

@media (min-width: 768px) {
  #app .categoryCourses .filters {
    position: -webkit-sticky;
    position: sticky;
    top: 72px;
  }
}

#app .categoryCourses .filtersWrapper {
  padding: 0 15px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px 0;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper {
    padding: 0 0 5px;
  }
}

#app .categoryCourses .filtersWrapper.loading .b-skeleton {
  width: auto;
  margin: 0 0 0 15px;
}

#app .categoryCourses .filtersWrapper .fa-filter {
  font-size: 24px;
  padding-right: 15px;
}

#app .categoryCourses .filtersWrapper .button.is-primary {
  background-color: #FFF;
  color: #000;
  font-size: 14px;
  border-color: rgba(0, 0, 0, 0.12);
  font-size: 14px;
  border-radius: 4px;
  padding: 8px 15px 9px;
}

#app .categoryCourses .filtersWrapper .button.is-primary:active, #app .categoryCourses .filtersWrapper .button.is-primary:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .categoryCourses .filtersWrapper .button .icon {
  margin: 0 0 0 5px;
  position: relative;
  top: 1px;
}

#app .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c5";
}

#app .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c7";
}

#app .categoryCourses .filtersWrapper .filterMenu {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 15px;
  margin-left: 0;
}

#app .categoryCourses .filtersWrapper .filterMenu.category {
  display: none;
}

#app .categoryCourses .filtersWrapper .filterMenu.active button.filter {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu.active button.filter .icon {
  color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu.is-active button {
  border-color: rgba(0, 0, 0, 0.87);
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-menu {
  padding: 0;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content {
  padding: 0;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle) {
  padding: 6px 15px 6px 40px;
  position: relative;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  content: "unCheck";
  position: absolute;
  left: 15px;
  top: 10px;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle).is-active:before {
  content: "checked";
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 15px 0;
  padding: 15px 0;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper .yunoSecondaryCTA {
  border-color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
    width: 440px;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before {
  font-family: 'Material Icons' !important;
  position: static;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.night:before {
  content: "\e51c";
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 0;
  padding-bottom: 5px;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
    width: 440px;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:hover {
  background: none;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:before {
  display: none;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 15px 0 0;
  padding-bottom: 5px;
}

#app .categoryCourses .filtersWrapper .filterMenu.class_days_time .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .categoryCourses .filtersWrapper .filterMenu.class_days_time .ctaWrapper .button {
  border-color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 10px;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .wrapper {
    width: 440px;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:hover {
  background: none;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:before {
  display: none;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper {
    width: 440px;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before {
  font-family: 'Material Icons' !important;
  position: static;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.night:before {
  content: "\e51c";
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .ctaWrapper {
  border: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

#app .categoryCourses .filtersWrapper .filterMenu .dropdown-trigger {
  width: 100%;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
    margin: 10px 0 0 15px;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu button {
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .categoryCourses .filtersWrapper .filterMenu button {
    width: auto;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

#app .categoryCourses .filtersWrapper .filterMenu button > span {
  text-transform: capitalize;
}

#app .categoryCourses .filtersWrapper .filterMenu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}

#app .categoryCourses .filtersWrapper .filterMenu .dropdown-content a {
  color: rgba(0, 0, 0, 0.5);
}

#app .categoryCourses .filtersWrapper .filterMenu .dropdown-content a.is-active {
  background: none;
  color: black;
}

#app .categoryCourses .filtersWrapper .filterMenu .dropdown-content a:active, #app .categoryCourses .filtersWrapper .filterMenu .dropdown-content a:focus {
  background: none;
  outline: none;
}

#app .categoryCourses .clearFilters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-bottom: 0;
  margin-top: 0;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .categoryCourses .clearFilters .tags {
  margin: 10px 10px 0 0;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

#app .categoryCourses .clearFilters .tags .tag {
  margin-bottom: 0;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-right: 0;
  height: 28px;
  background: rgba(0, 0, 0, 0.02);
}

#app .categoryCourses .clearFilters .tags .is-delete {
  border-left: 0;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

#app .categoryCourses .clearFilters .tags .is-delete::after {
  display: none;
}

#app .categoryCourses .clearFilters .tags .is-delete::before {
  content: "\e5cd";
  left: 0;
  top: 6px;
  -webkit-transform: none;
          transform: none;
  background: none;
  font-size: 14px;
}

#app .categoryCourses .clearFilters .tags .is-delete:hover {
  background: rgba(0, 0, 0, 0.02);
  color: #A81E22;
}

#app .categoryCourses .clearFilters .multiChips {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 10px 10px 0 0;
}

#app .categoryCourses .clearFilters .multiChips .tags:not(.placeholder) {
  margin: 0;
}

#app .categoryCourses .clearFilters .multiChips .tags:not(.placeholder) .tag {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  text-transform: capitalize;
}

#app .categoryCourses .clearFilters .multiChips .tags:not(.placeholder) .is-delete {
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#app .categoryCourses .clearFilters .multiChips .tags:not(.placeholder):last-child .is-delete {
  border-right: 1px;
  border-top-right-radius: 290486px;
  border-bottom-right-radius: 290486px;
}

#app .categoryCourses .clearFilters .multiChips .tags.placeholder {
  margin: 0;
}

#app .categoryCourses .clearFilters .multiChips .tags.placeholder .tag {
  padding-right: 0;
}

#app .categoryCourses .clearFilters .multiChips .tags.placeholder .is-delete {
  display: none;
}

#app .categoryCourses .result {
  margin: 0 0 15px;
  position: relative;
  z-index: 5;
}

#app .categoryCourses .listView {
  margin: 15px 0 0;
}

#app .categoryCourses .listView.detailView {
  margin-top: 5px;
}

#app .categoryCourses .coursesCount {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .categoryCourses .back {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  cursor: pointer;
}

#app .categoryCourses .back .material-icons-outlined {
  margin-right: 5px;
  font-size: 18px;
}

#app .categoryCourses .courseCard {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 24px;
  border: 1px solid transparent;
  margin-bottom: 30px;
  cursor: pointer;
}

#app .categoryCourses .courseCard:hover {
  border-color: #A81E22;
}

#app .categoryCourses .courseCard.hideCard {
  display: none;
}

#app .categoryCourses .courseCard.active {
  border-color: #A81E22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .categoryCourses .courseCard .cardBlock {
  margin: 15px 0 0;
  padding: 30px 0 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

#app .categoryCourses .courseCard .cardBlock.addGutter1x {
  margin-bottom: 15px;
}

#app .categoryCourses .courseCard .cardBlock.addGap1x {
  padding: 15px 0 0 0;
}

#app .categoryCourses .courseCard .cardBlock h3 {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .categoryCourses .courseCard .cardBlock .note {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}

#app .categoryCourses .courseCard .highlighhts {
  margin: 15px 0 0;
  padding: 30px 0 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

#app .categoryCourses .courseCard .highlighhts h3 {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .categoryCourses .courseCard .highlighhts .listWithCheck li {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryCourses .courseCard .highlighhts .listWithCheck li .material-icons-outlined {
  margin-right: 10px;
  font-size: 16px;
}

#app .categoryCourses .courseCard .cardCategory {
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  display: block;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.38);
}

#app .categoryCourses .courseCard .cardTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .categoryCourses .courseCard .cardDescription {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 15px;
  letter-spacing: 0.25px;
}

#app .categoryCourses .courseCard .cardRating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .categoryCourses .courseCard .cardRating .vue-star-rating {
  position: relative;
  top: -4px;
}

#app .categoryCourses .courseCard .cardRating .vue-star-rating-rating-text {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-left: 10px;
}

#app .categoryCourses .courseCard .cardRating .vue-star-rating-rating-text .rating {
  font-size: 14px;
  font-weight: 500;
}

#app .categoryCourses .courseCard .cardStats {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -15px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  position: relative;
}

#app .categoryCourses .courseCard .cardStats li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 15px;
  position: relative;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  margin-bottom: 10px;
}

#app .categoryCourses .courseCard .cardStats li::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.08);
}

#app .categoryCourses .courseCard .cardStats li .material-icons-outlined {
  font-size: 20px;
}

#app .categoryCourses .courseCard .cardStats li .listLabel {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 5px;
}

#app .categoryCourses .courseCard .cardStats li .listLabel .dark {
  font-weight: 400;
}

#app .categoryCourses .courseCard .cardStats li:nth-child(2n)::after {
  display: none;
}

@media (min-width: 768px) {
  #app .categoryCourses .courseCard .cardStats li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
}

#app .categoryCourses .courseCard .totalTime {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 10px;
}

#app .categoryCourses .courseCard .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .categoryCourses .courseCard .ctaWrapper .yunoPrimaryCTA {
  margin-right: 15px;
}

#app .categoryCourses .courseCard .instructorsListWrapper {
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryCourses .courseCard .instructorsListWrapper .instructorsList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 10px;
  padding-right: 10px;
}

#app .categoryCourses .courseCard .instructorsListWrapper .instructorsList li {
  margin-left: -12px;
}

#app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .instructorMapped img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #FFF;
  font-size: 0;
}

#app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .material-icons, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .categoryCourses .courseCard .instructorsListWrapper .instructorsList .imgWrapper::before, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .courseCard .instructorsListWrapper .instructorsList .mdi.mdi-menu-down:after, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .courseCard .instructorsListWrapper .instructorsList .mdi.mdi-menu-up:after, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .courseCard .instructorsListWrapper .instructorsList .dropdown-item:before, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .courseCard .instructorsListWrapper .instructorsList .dropdown-item:before, #app .categoryCourses .courseCard .instructorsListWrapper .instructorsList .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .courseCard .instructorsListWrapper .instructorsList .is-delete::before {
  font-size: 24px;
  background-color: #FFF;
  border-radius: 50%;
}

#app .categoryCourses .batchNotSelected {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: -webkit-sticky;
  position: sticky;
  top: 179px;
}

@media (min-width: 768px) {
  #app .categoryCourses .batchNotSelected {
    padding-top: 70px;
  }
}

#app .categoryCourses .batchNotSelected .arrow {
  width: 122px;
  height: 183px;
  background-image: url("../../../assets/images/lineWithArrow.svg");
  background-repeat: no-repeat;
  position: relative;
  top: 58px;
  display: none;
}

@media (min-width: 768px) {
  #app .categoryCourses .batchNotSelected .arrow {
    display: block;
  }
}

#app .categoryCourses .batchNotSelected .block {
  width: 100%;
  height: 226px;
  background-image: url("../../../assets/images/batchEmpty.svg");
  background-repeat: no-repeat;
  background-size: contain;
  position: relative;
}

@media (min-width: 768px) {
  #app .categoryCourses .batchNotSelected .block {
    width: 360px;
    height: 226px;
  }
}

#app .categoryCourses .batchNotSelected .block .caption {
  display: block;
  background: #fefbf4;
  position: absolute;
  width: calc(100% - 4px);
  bottom: 29px;
  left: 2px;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .categoryCourses .batchNotSelected .block .caption {
    display: none;
  }
}

#app .categoryCourses .batchesCount {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .categoryCourses .batches {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
}

#app .categoryCourses .batches .batchCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .categoryCourses .batches .batchCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
}

#app .categoryCourses .batches .batchCard .wrapper {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
  border: 1px solid transparent;
}

#app .categoryCourses .batches .batchCard .wrapper:hover {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

#app .categoryCourses .batches .batchCard .wrapper:hover .ctaWrapper, #app .categoryCourses .batches .batchCard .wrapper:hover .full {
  visibility: visible;
}

#app .categoryCourses .batches .batchCard .batchdate {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryCourses .batches .batchCard .batchdate .material-icons, #app .categoryCourses .batches .batchCard .batchdate .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .categoryCourses .batches .batchCard .batchdate .imgWrapper::before, #app .categoryCourses .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-down:after, #app .categoryCourses .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-up:after, #app .categoryCourses .batches .batchCard .batchdate .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .batches .batchCard .batchdate .dropdown-item:before, #app .categoryCourses .batches .batchCard .batchdate .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .batches .batchCard .batchdate .dropdown-item:before, #app .categoryCourses .batches .batchCard .batchdate .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .batches .batchCard .batchdate .is-delete::before {
  margin-right: 5px;
}

#app .categoryCourses .batches .batchCard .days {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 10px;
}

#app .categoryCourses .batches .batchCard .days li {
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  color: rgba(0, 0, 0, 0.38);
  text-transform: uppercase;
}

#app .categoryCourses .batches .batchCard .days li.isActive {
  color: #A81E22;
}

#app .categoryCourses .batches .batchCard .scheduleInfo {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

#app .categoryCourses .batches .batchCard .scheduleInfo li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 5px;
}

#app .categoryCourses .batches .batchCard .scheduleInfo li .material-icons-outlined {
  font-size: 16px;
}

#app .categoryCourses .batches .batchCard .scheduleInfo li .itemCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 5px;
}

#app .categoryCourses .batches .batchCard .scheduleInfo li .hasBG {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  background-color: rgba(168, 30, 34, 0.04);
  border-radius: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 4px 10px;
}

#app .categoryCourses .batches .batchCard .scheduleInfo li .hasBG .material-icons-outlined {
  margin-right: 5px;
}

#app .categoryCourses .batches .batchCard .mappedInstructor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  margin-right: 10px;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .imgWrapper img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}

#app .categoryCourses .batches .batchCard .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
}

#app .categoryCourses .batches .batchCard .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .insRating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  left: -3px;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .insRating .material-icons, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .categoryCourses .batches .batchCard .mappedInstructor .insRating .imgWrapper::before, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .batches .batchCard .mappedInstructor .insRating .mdi.mdi-menu-down:after, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .batches .batchCard .mappedInstructor .insRating .mdi.mdi-menu-up:after, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .batches .batchCard .mappedInstructor .insRating .dropdown-item:before, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .batches .batchCard .mappedInstructor .insRating .dropdown-item:before, #app .categoryCourses .batches .batchCard .mappedInstructor .insRating .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .batches .batchCard .mappedInstructor .insRating .is-delete::before {
  color: #F9B600;
  margin-right: 5px;
  font-size: 18px;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .insRating .caption {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

#app .categoryCourses .batches .batchCard .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

#app .categoryCourses .batches .batchCard .cardFooter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryCourses .batches .batchCard .cardFooter .price {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .categoryCourses .batches .batchCard .cardFooter .ctaWrapper {
    visibility: hidden;
  }
}

#app .categoryCourses .batches .batchCard .cardFooter .full {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 5px;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  visibility: hidden;
}

#app .cardsListing {
  padding-bottom: 60px;
}

#app .collectionCard {
  margin-bottom: 30px;
}

#app .collectionCard .innerWrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
  border: 1px solid transparent;
}

#app .collectionCard .innerWrap:hover {
  text-decoration: none;
  border-color: #A81E22;
}

#app .collectionCard .cardImgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  background-color: #E6E6E6;
}

#app .collectionCard .cardImgWrapper img {
  width: auto;
  height: auto;
  font-size: 0;
  max-height: 146px;
  display: block;
  margin: 0 auto;
}

@media (min-width: 768px) {
  #app .collectionCard .cardImgWrapper {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 260px;
            flex: 0 0 260px;
  }
}

#app .collectionCard .info {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 15px 0 0 0;
}

@media (min-width: 768px) {
  #app .collectionCard .info {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 260px);
            flex: 0 0 calc(100% - 260px);
    padding: 0 0 0 15px;
  }
}

#app .collectionCard .cardTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .collectionCard .cardDescription {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .collectionCard .relatedResource {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .collectionCard .relatedResource li {
  position: relative;
  padding-right: 15px;
  margin-right: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .collectionCard .relatedResource li:nth-child(2n) {
  margin-right: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 15px);
          flex: 0 0 calc(50% - 15px);
}

#app .collectionCard .relatedResource li:nth-child(2n)::after {
  display: none;
}

@media (min-width: 768px) {
  #app .collectionCard .relatedResource li:nth-child(2n) {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-right: 15px;
  }
  #app .collectionCard .relatedResource li:nth-child(2n)::after {
    display: block;
  }
}

@media (min-width: 768px) {
  #app .collectionCard .relatedResource li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
  #app .collectionCard .relatedResource li:last-child::after {
    display: none;
  }
}

#app .collectionCard .relatedResource li::after {
  content: "";
  width: 1px;
  height: 70%;
  position: absolute;
  right: 0;
  top: 15%;
  background-color: rgba(0, 0, 0, 0.08);
}

#app .collectionCard .relatedResource li .listValue {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 5px;
}

#app .collectionCard .relatedResource li .listLable {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 -15px;
}

#app .pagination li {
  padding: 0 10px;
}

#app .pagination li.noRightGap {
  padding-right: 0;
}

#app .pagination li.page {
  padding-left: 0;
}

#app .pagination li.gapRight {
  padding-right: 15px;
}

#app .pagination li.firstLast {
  display: none;
}

@media (min-width: 768px) {
  #app .pagination li.firstLast {
    display: block;
  }
}

#app .pagination button {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 5px 15px;
  height: 36px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .pagination button.nextPrev {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .pagination button.nextPrev .material-icons, #app .pagination button.nextPrev .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .pagination button.nextPrev .imgWrapper::before, #app .pagination button.nextPrev .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .pagination button.nextPrev .mdi.mdi-menu-down:after, #app .pagination button.nextPrev .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .pagination button.nextPrev .mdi.mdi-menu-up:after, #app .pagination button.nextPrev .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .pagination button.nextPrev .dropdown-item:before, #app .pagination button.nextPrev .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .pagination button.nextPrev .dropdown-item:before, #app .pagination button.nextPrev .categoryCourses .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .pagination button.nextPrev .is-delete::before {
  font-size: 24px;
}

#app .pagination button.disabled {
  cursor: not-allowed;
}

#app .pagination button.disabled.active {
  background: #A81E22;
  border-color: transparent;
  color: #FFFFFF;
}

#app .resultsCard {
  margin-bottom: 30px;
}

#app .resultsCard:last-child {
  margin-bottom: 0;
}

#app .resultsCard .studentInfo {
  border-radius: 4px;
  background-color: #FFF;
  -webkit-box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .resultsCard .studentInfo .studentInfo_image {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .resultsCard .studentInfo .studentInfo_image .img {
  border-radius: 50%;
  width: 64px;
  height: 64px;
  background-color: #E6E6E6;
  margin-right: 15px;
}

#app .resultsCard .studentInfo .studentInfo_image .img img {
  border-radius: 50%;
  width: 64px;
  height: 64px;
}

#app .resultsCard .studentInfo .studentInfo_image figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
  line-height: normal;
}

#app .resultsCard .studentInfo .studentInfo_image figcaption small {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  border-radius: 100px;
  display: inline-block;
  padding: 5px 8px;
  text-transform: uppercase;
}

#app .resultsCard .studentInfo .studentInfo_image figcaption small.academic {
  background-color: rgba(249, 182, 0, 0.2);
}

#app .resultsCard .studentInfo .studentInfo_image figcaption small.general {
  background-color: rgba(165, 166, 246, 0.2);
}

#app .resultsCard .studentInfo .studentInfo_image figcaption h3 {
  margin: 5px 0 10px;
}

#app .resultsCard .studentInfo .studentInfo_image figcaption h4 {
  font-size: 10px;
  line-height: 4px;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .resultsCard .studentInfo .studentInfo_image figcaption a {
  font-size: 10px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 1.5px;
}

#app .resultsCard .studentInfo .studentInfo_score {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 10px;
}

@media (min-width: 768px) {
  #app .resultsCard .studentInfo .studentInfo_score {
    margin-top: 0;
  }
}

#app .resultsCard .studentInfo .studentInfo_score .score {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: 10px;
}

#app .resultsCard .studentInfo .studentInfo_score .score li {
  position: relative;
  padding-right: 15px;
  margin-right: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .resultsCard .studentInfo .studentInfo_score .score li:nth-child(2n) {
  margin-right: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 15px);
          flex: 0 0 calc(50% - 15px);
}

#app .resultsCard .studentInfo .studentInfo_score .score li:nth-child(2n)::after {
  display: none;
}

@media (min-width: 768px) {
  #app .resultsCard .studentInfo .studentInfo_score .score li:nth-child(2n) {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-right: 15px;
  }
  #app .resultsCard .studentInfo .studentInfo_score .score li:nth-child(2n)::after {
    display: block;
  }
}

@media (min-width: 768px) {
  #app .resultsCard .studentInfo .studentInfo_score .score li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
  #app .resultsCard .studentInfo .studentInfo_score .score li:last-child::after {
    display: none;
  }
}

#app .resultsCard .studentInfo .studentInfo_score .score li::after {
  content: "";
  width: 1px;
  height: 70%;
  position: absolute;
  right: 0;
  top: 15%;
  background-color: rgba(0, 0, 0, 0.08);
}

#app .resultsCard .studentInfo .studentInfo_score .score li .listValue {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 5px;
}

#app .resultsCard .studentInfo .studentInfo_score .score li .listLable {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .resultsCard .studentInfo .studentInfo_image {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 60%;
            flex: 0 0 60%;
  }
  #app .resultsCard .studentInfo .studentInfo_score {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 40%;
            flex: 0 0 40%;
  }
}

#app .videoSelected {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 30px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

#app .videoSelected .videoDetails {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding-top: 15px;
}

@media (min-width: 768px) {
  #app .videoSelected .videoDetails {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 40%;
            flex: 0 0 40%;
    padding-left: 30px;
    padding-top: 0;
  }
}

#app .videoSelected .videoDetails .description {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 15px;
}

#app .videoSelected .videoDetails .description.hasShowMore {
  height: 150px;
  overflow: hidden;
  -webkit-mask-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, black), to(transparent));
  -webkit-mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
  mask-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, black), to(transparent));
  mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
}

#app .videoSelected .videoDetails .description .media div[data-oembed-url] {
  width: 100%;
  margin-bottom: 15px;
}

#app .videoSelected .videoDetails .description p {
  margin-bottom: 15px;
}

#app .videoSelected .videoDetails .description h2 {
  font-size: 28px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .videoSelected .videoDetails .description h3 {
  font-size: 24px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .videoSelected .videoDetails .description h4 {
  font-size: 20px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .videoSelected .videoDetails .description ul, #app .videoSelected .videoDetails .description ol {
  padding: 0;
  margin: 0 0 15px 18px;
}

#app .videoSelected .videoDetails .description ul li, #app .videoSelected .videoDetails .description ol li {
  list-style: disc outside;
  margin-bottom: 5px;
  font-size: 16px;
}

#app .videoSelected .videoDetails .description ul li:last-child, #app .videoSelected .videoDetails .description ol li:last-child {
  margin-bottom: 0;
}

#app .videoSelected .videoDetails .description ol li {
  list-style: decimal outside;
}

#app .videoSelected .videoDetails .showmore {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
  text-decoration: none;
}

#app .videoSelected .videoDetails .showmore:hover {
  text-decoration: none;
}

#app .videoSelected .videoDetails .showmore .anchorLabel {
  text-decoration: underline;
}

#app .videoSelected .videoDetails .showmore .material-icons, #app .videoSelected .videoDetails .showmore .videosWrapper .videoCard figure .imgWrapper::before, #app .videosWrapper .videoCard figure .videoSelected .videoDetails .showmore .imgWrapper::before, #app .videoSelected .videoDetails .showmore .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .categoryCourses .filtersWrapper .button .icon .videoSelected .videoDetails .showmore .mdi.mdi-menu-down:after, #app .videoSelected .videoDetails .showmore .categoryCourses .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .categoryCourses .filtersWrapper .button .icon .videoSelected .videoDetails .showmore .mdi.mdi-menu-up:after, #app .videoSelected .videoDetails .showmore .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .videoSelected .videoDetails .showmore .dropdown-item:before, #app .videoSelected .videoDetails .showmore .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .categoryCourses .filtersWrapper .filterMenu .filterSet.class_time .wrapper .videoSelected .videoDetails .showmore .dropdown-item:before, #app .videoSelected .videoDetails .showmore .categoryCourses .clearFilters .tags .is-delete::before, #app .categoryCourses .clearFilters .tags .videoSelected .videoDetails .showmore .is-delete::before {
  font-size: 18px;
}

#app .videoSelected .loadingCol {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  #app .videoSelected .loadingCol {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 60%;
            flex: 0 0 60%;
  }
}

#app .videoSelected .videoWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  position: relative;
  padding-bottom: 65.7%;
  overflow: hidden;
  max-width: 100%;
  margin: -10px 0 0 0;
}

@media (min-width: 768px) {
  #app .videoSelected .videoWrapper {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 60%;
            flex: 0 0 60%;
    padding-bottom: 36.7%;
  }
}

#app .videoSelected .videoWrapper.loading {
  padding-bottom: 0;
}

#app .videoSelected .videoWrapper iframe,
#app .videoSelected .videoWrapper object,
#app .videoSelected .videoWrapper embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#app .videoSelected .socialList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 15px;
  margin-bottom: 30px;
}

#app .videoSelected .socialList .socialItem {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #E6E6E6;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-left: 8px;
}

#app .videoSelected .socialList .socialItem .fa {
  font-size: 14px;
}

#app .videoSelected .socialList .socialItem:hover {
  text-decoration: none;
}

#app .videoSelected .socialList .socialItem.share-network-whatsapp {
  background-color: #25D366;
  color: white;
}

#app .videoSelected .socialList .socialItem.share-network-twitter {
  background-color: #1DA1F2;
  color: white;
}

#app .videoSelected .socialList .socialItem.share-network-facebook {
  background-color: #1877F2;
  color: white;
}

#app .videoSelected .socialList .socialItem.share-network-linkedin {
  background-color: #2867B2;
  color: white;
}
/*# sourceMappingURL=categoryLanding.css.map */