<?php

/**
 * WritingTask controller is the new resource
 */
//require_once 'path-to-wordpress/wp-load.php';
//require_once ABSPATH . 'wp-includes/class-wp-unit-test-case.php';
// Include the logger file
//require_once ABSPATH . 'wp-content/themes/yunolearning-child/inc/logger/logger.php';

class WritingTaskController extends WP_REST_Controller
{
    function __construct()
    {
        $this->namespace = 'yuno/v1';
        $this->resource_name1 = '/writingtask/create';
        $this->resource_name2 = '/writingtask/update';
        $this->resource_name3 = '/writingtask/create/test';
        $this->resource_name4 = '/writingtask/(?P<writing_task_id>\d+)/(?P<user_id>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name5 = '/writingtask/delete/metakeys';
        $this->resource_name6 = '/writingtask/type/(?P<exam_type>[a-zA-Z0-9-]+)/(?P<writing_task_type>[a-zA-Z0-9-]+)/';
        $this->resource_name7 = '/writingtask/migrate/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name8 = '/writingtask/delete/(?P<writing_task_id>\d+)';
        $this->resource_name9 = '/writingtask/migrate/categories/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name10 = '/writingtask/migrate/status/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name11 = '/attempt/writingtask/';
        $this->resource_name12 = '/attempt/writingtask/(?P<attempt_id>\d+)';
    }

    public function register_routes()
    {

        register_rest_route($this->namespace, $this->resource_name1, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'post_writing_task'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                // 'permission_callback' => array( $this, 'post_writing_task_permissions_check' ),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name2, array(
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array($this, 'post_writing_task'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                //'permission_callback' => array( $this, 'post_writing_task_permissions_check' ),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name3, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'post_writing_task_test'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));


        register_rest_route($this->namespace, $this->resource_name4, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_writing_task'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'get_writing_task_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name5, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'delete_post_meta_writingtask'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'delete_post_meta_writingtask_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name6, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_dropdown_values'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'get_dropdown_values_permissions_check'),
                'args'                => array(),
            ),
        ));


        register_rest_route($this->namespace, $this->resource_name7, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'migrate_writing_task'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'migrate_writing_task_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name8, array(
            array(
                'methods'             => WP_REST_Server::DELETABLE,
                'callback'            => array($this, 'delete_writing_task'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'delete_writing_task_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name9, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'migrate_writing_task_categories'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'migrate_writing_task_categories_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name10, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'migrate_writing_task_status'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'migrate_writing_task_status_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name11, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'post_attempt_writing_task'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'migrate_writing_task_status_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name12, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_feedback_by_attempt_id'),
                //'permission_callback' => array( $this, 'check_access_permissions_check' ),
                'permission_callback' => array($this, 'migrate_writing_task_status_permissions_check'),
                'args'                => array(),
            ),
        ));
    }
    /**
     * Token authorization check
     * This function common for all post login apis  
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {
            return false;
        }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) {
                return true;
            } else {
                return false;
            }
        }
        $codes = error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }
    /**
     * Create and Update post_writing_task from web
     * New Resource
     */
    public function post_writing_task($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        //$data = json_decode($request->get_body(), true);
        $post_id = (int)$_POST['id'];
        $title = isset($_POST['title']) ? $_POST['title'] : '';
        $sub_title = isset($_POST['subTitle']) ? $_POST['subTitle'] : '';
        $alt_text = isset($_POST['altText']) ? $_POST['altText'] : '';
        $excerpt = isset($_POST['excerpt']) ? $_POST['excerpt'] : '';
        $saveAction = !empty($_POST['action']) ? strtolower($_POST['action']) : 'publish';
        if (strlen($excerpt) > 145) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Excerpt length upto 145 character', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
        if (empty($title)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Title should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
        $yuno_test_code = isset($_POST['yuno_test_code']) ? $_POST['yuno_test_code'] : '';
        $yuno_test_Author = isset($_POST['yuno_test_Author']) ? $_POST['yuno_test_Author'] : '';
        $yuno_test_authoring_date = isset($_POST['yuno_test_authoring_date']) ? $_POST['yuno_test_authoring_date'] : '';
        $yuno_tset_sample_answer_high_band_content = isset($_POST['model_answer']) ? $_POST['model_answer'] : '';
        $yuno_test_sample_answer_low_band_content = isset($_POST['yuno_test_sample_answer_low_band_content']) ? $_POST['yuno_test_sample_answer_low_band_content'] : '';
        $yuno_test_reasons_for_low_band_score = isset($_POST['yuno_test_reasons_for_low_band_score']) ? $_POST['yuno_test_reasons_for_low_band_score'] : '';

        $description = !empty($_POST['description']) ? $_POST['description'] : '';
        $topic = stripslashes(isset($_POST['topic']) ? $_POST['topic'] : '');
        $filteredCategory = stripslashes(isset($_POST['category']) ? $_POST['category'] : '');

        /*if (isset($_FILES['featuredImage'])) {
            $files = $_FILES['featuredImage'];  
        }*/

        $topic = json_decode($topic, true);

        $writing_task_category = json_decode($filteredCategory, true);
        $categoryArray = json_decode($filteredCategory, true);
        $resultCategoryArray = $categoryArray;
        $writing_task_category = [];
        $writing_task_category[] = $resultCategoryArray['id'];
        $sub_1_category = $resultCategoryArray['sub_category'];
        if (!empty($sub_1_category)) {
            foreach ($sub_1_category as $v) {
                $writing_task_category[] = $v['id'];
                if (!empty($v['sub_category'])) {
                    foreach ($v['sub_category'] as $vv) {
                        $writing_task_category[] = $vv['id'];
                    }
                }
            }
        }

        $term = get_term(min($writing_task_category), 'course_category');
        $slug = $term->slug;

        if ($post_id > 0) {
            $post = array();
            $post['ID']   = $post_id;
            $post['post_status']   = $saveAction;
            $post['post_type']     = 'writing_test';
            $post['post_title']    = $title;
            $post['post_content']  = $description;
            //$post['post_excerpt']  = $excerpt;
            $post['post_author']   = 2;
            // Create Post
            wp_update_post($post);
            wp_set_post_terms($post_id, $writing_task_category, 'course_category');
            $message = str_replace("[Module_Name]", "Writing Task", $codes["PUT_UPDATE"]["message"]);
            $code = $codes["PUT_UPDATE"]["code"];

            $status = get_post_status($post_id);

            /*if (isset($_FILES['featuredImage'])) {
               // $this->uploadImage($post_id, $files);
                uploadImage($post_id, $files);
            }
            //$fileName = 'PDF/'.$post_id.$title;//file_name with extension
            if (count($attachments) > 0) {
                $bucket_name = WRITING_TASK_BUCKET_NAME;
                // 4th param is edit mode true/1
                $this->uploadFileOnAWS($bucket_name, $attachments, $post_id, 1);
            }
            $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'full', false);*/


            //if($saveAction == 'publish') {
            $curlPost = [
                "data" => [
                    "details" => [
                        "record_id" => $post_id,
                        "update_event_type" => "resourceevent",
                        // "update_event_type" => "resourceevent-temp",
                        "title" => get_the_title($post_id),
                        "excerpt" => get_the_excerpt($post_id),
                        //"sub_title"=> $sub_title,
                        "description" => $description,
                        "topic" => $topic,
                        //"signup_form"=> $signup_form,
                        //"featuredImage"=> $featured_image[0],
                        "taxonomy" => $writing_task_category,
                        "parent_taxonomy" => "ielts",
                        "status" => $status,
                        "url" => get_permalink($post_id),
                        "published_at" => get_the_time('M j, Y g:i A', $post_id),

                    ],
                    "@timestamp" => date("Y-m-d H:i:s")
                ]
            ];

            //error_log('Call update_ebookresourceevent'.json_encode($curlPost));       
            update_elastic_event($curlPost);
            //}           
        } else {
            $post = array();
            $post['post_status']   = $saveAction;
            $post['post_type']     = 'writing_test';
            $post['post_title']    = $title;
            $post['post_content']  = $description;
            //$post['post_excerpt']  = $excerpt;
            $post['post_author']   = 2;
            // Create Post
            $post_id = wp_insert_post($post);
            wp_set_post_terms($post_id, $writing_task_category, 'course_category');
            //$this->uploadImage($post_id, $files);
            //uploadImage($post_id, $files);
            $message = str_replace("[Module_Name]", "Writing Task", $codes["POST_INSERT"]["message"]);
            $code = $codes["POST_INSERT"]["code"];
            $status = get_post_status($post_id);

            /*$bucket_name = WRITING_TASK_BUCKET_NAME;
            $this->uploadFileOnAWS($bucket_name, $attachments, $post_id);
            //$this->deleteFileFromAWS($bucket_name, $path, $fileName);
            $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'full', false);*/

            $curlPost = [
                "data" => [
                    "details" => [
                        "record_id" => $post_id,
                        "android_view_type" => 0,
                        "resource_type" => "writing_task",
                        "user_id" => CURRENT_LOGGED_IN_USER_ID,
                        "event_type" => "resourceevent",
                        //"event_type" => "resourceevent-temp",
                        "event_label" => "Writing Task Resource",
                        "title" => get_the_title($post_id),
                        "excerpt" => get_the_excerpt($post_id),
                        //"sub_title"=> $sub_title,
                        "description" => $description,
                        "topic" => $topic,
                        //"featuredImage"=> $featured_image[0],
                        "taxonomy" => $writing_task_category,
                        "parent_taxonomy" => "ielts",
                        "status" => $status,
                        "url" => get_permalink($post_id),
                        "published_at" => get_the_time('M j, Y g:i A', $post_id),

                    ],
                    "@timestamp" => date("Y-m-d H:i:s")
                ]
            ];

            post_elastic_event($curlPost);
        }

        update_post_meta($post_id, 'Category_Tree_Structure', $resultCategoryArray);
        update_post_meta($post_id, 'topic', $topic);
        update_post_meta($post_id, 'yuno_tset_sample_answer_high_band_content', $yuno_tset_sample_answer_high_band_content);

        if ($post_id > 0) {
            $result = array(
                'code' => $code,
                'message' => $message,
                'data' => array('status' => $code, 'id' => $post_id)
            );
            //return new WP_REST_Response($result, 200);    
            return $result;
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
    public function post_writing_task_permissions_check()
    {
        return true;
    }
    public function post_writing_task_test(WP_REST_Request $request)
    {
        // Call the actual post_writing_task method here
        $response = $this->post_writing_task($request);
        return new WP_REST_Response($response, 200);

        // Return a test response or perform assertions if needed
    }
    /**
     * Upload files in bucket on AWS
     */
    public function uploadFileOnAWS($bucket_name, $attachments, $post_id, $edit_mode = 0)
    {
        try {
            // Create an S3 client instance
            $s3 = new Aws\S3\S3Client([
                'version'     => 'latest',
                'region'      => 'ap-south-1',
                'credentials' => [
                    'key'    => PINPOINT_KEY,
                    'secret' => PINPOINT_SECRET
                ]
            ]);

            $file_names = []; // Renamed for clarity
            // Loop through all attachments
            foreach ($attachments as $key => $value) {
                // Split the name and extension
                list($name, $extension) = explode(".", $value['name']);
                $name = str_replace(" ", "_", $name);
                $extension = strtolower($extension);
                $new_file_name = $name . "_" . time() . "." . $extension;
                $file_names[$extension] = $new_file_name;

                $file_handle = fopen($new_file_name, 'r+'); // Open file handle

                // Upload the object to the specific bucket
                $result = $s3->putObject([
                    'Bucket'     => $bucket_name,
                    'Key'        => $key . "/" . $new_file_name, // File name with extension
                    'SourceFile' => $value['tmp_name'],          // Temporary file path
                    'Body'       => $file_handle,
                    'ACL'        => 'public-read'                 // Access control
                ]);

                fclose($file_handle); // Close file handle

                // Get the access URL and update post meta
                $access_url = $result['ObjectURL']; // Corrected
                $ext = strtolower($key);
                update_post_meta($post_id, 'WRITINGTASK_S3_URL_' . $ext, $access_url);
            }

            // Handle attachments based on edit mode
            if ($edit_mode == 1) {
                $existing_attachment = get_post_meta($post_id, 'WRITINGTASK_ATTACHMENTS', true);
                foreach ($file_names as $key => $value) {
                    $existing_attachment[$key] = $value;
                }
                update_post_meta($post_id, 'WRITINGTASK_ATTACHMENTS', $existing_attachment);
            } else {
                update_post_meta($post_id, 'WRITINGTASK_ATTACHMENTS', $file_names);
            }
            return;
        } catch (AwsException $e) {
            // Handle AWS specific exception
            error_log("AWS_FILE_UPLOAD= " . $e->getMessage());
            // Consider returning or handling the error as appropriate for your application
        }
    }

    public function delete_post_meta_writingtask($request)
    {
        // actual name of your custom post type
        $custom_post_type = 'writing_test';

        //specific string you want to match in the meta keys
        $meta_key_contains = 'pyre';

        // Get all posts of the custom post type
        $args = array(
            'post_type' => $custom_post_type,
            'posts_per_page' => -1,
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();

                $post_id = get_the_ID();

                // Get all meta keys for the current post
                $meta_keys = get_post_custom_keys($post_id);

                if ($meta_keys) {
                    foreach ($meta_keys as $meta_key) {
                        // Check if the meta key contains the specific string
                        if (strpos($meta_key, $meta_key_contains) !== false) {
                            // Delete the post meta
                            delete_post_meta($post_id, $meta_key);
                        }
                    }
                }
            }

            wp_reset_postdata();
        }
    }
    public function delete_post_meta_writingtask_permissions_check($request)
    {
        return true;
    }
    /** dropdown list***/
    public function get_dropdown_values($request)
    {
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        $exam_type = $request['exam_type']; //academics //general
        $writing_task_type = $request['writing_task_type']; // writing-task-1 // writing-task-2

        // Validate inputs
        if (is_null($exam_type) || is_null($writing_task_type)) {
            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Exam type and writing task type are required", $codes["GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
        }

        // Determine dropdown values based on conditions
        $dropdown_values = array();

        if ($writing_task_type == "writing-task-1") {
            if ($exam_type == 'academics') {
                // $dropdown_values = array('Graph', 'Table', 'Chart', 'Diagram', 'Maps', 'Processes');
                $dropdown_values = array(
                    array(
                        'name' => 'Graph',
                        'description' => 'Bar graphs, line graphs or a combination of both',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Table',
                        'description' => 'Single table with sub categories or 2-3 tables for comparison',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Chart',
                        'description' => 'Pie Charts or tables with pie charts',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Diagram',
                        'description' => 'Images or pictures with labels such as parts of a machine or euipment',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Maps',
                        'description' => 'Usually 2 maps are given for the same place, but they depict different time periods and changes that took place',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Processes',
                        'description' => 'Flowchart or a manufacturing process',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    )
                );
            } elseif ($exam_type == 'general') {
                $dropdown_values = array(
                    array(
                        'name' => 'Formal Letter',
                        'description' => 'Written to someone whom you have not seen or met',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Informal Letter',
                        'description' => 'Written to someone whom you know personally',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    ),
                    array(
                        'name' => 'Semi formal Letter',
                        'description' => 'Written to someone whom you only know officially',
                        'minimum_words' => 150,
                        'maximum_words' => 190
                    )
                );
            }
        } elseif ($writing_task_type == "writing-task-2") {
            $dropdown_values = array(
                array(
                    'name' => 'Agree/Disagree Essay',
                    'description' => "You're presented with a statement or question and asked to express your agreement or disagreement with it",
                    'minimum_words' => 250,
                    'maximum_words' => 290
                ),
                array(
                    'name' => 'Opinion Essay',
                    'description' => 'You have to express your personal opinion on a given topic',
                    'minimum_words' => 250,
                    'maximum_words' => 290
                ),
                array(
                    'name' => 'Problem/solution Essay',
                    'description' => 'Identify a specific problem and propose one or more solutions to address it',
                    'minimum_words' => 250,
                    'maximum_words' => 290
                ),
                array(
                    'name' => 'Discussion Essay',
                    'description' => 'Examine both sides of an argument or topic and give your opinion',
                    'minimum_words' => 250,
                    'maximum_words' => 290
                ),
                array(
                    'name' => 'Advantage/Disadvantage Essay',
                    'description' => 'Discuss the pros and cons of a particular issue or situation',
                    'minimum_words' => 250,
                    'maximum_words' => 290
                ),
                array(
                    'name' => 'Direct Question Essay',
                    'description' => 'You get a specific question to answer',
                    'minimum_words' => 250,
                    'maximum_words' => 290
                )
            );
        }

        $logger = WP_Structured_Logger::get_instance();
        $logtype = "info";
        $timestamp = date('Y-m-d H:i:s');
        $module = "writing_tests";
        $action = "GET dropdown";
        $message = "get call ";
        $user = array();
        $request = array();
        $data = array();
        $logger_result =  $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);
        //return new WP_REST_Response($logger_result, 200);
        if (!empty($dropdown_values)) {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Exam Type and Writing Task Type ", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'data' => $dropdown_values);
            return new WP_REST_Response($result, 200);
        }
    }
    public function get_dropdown_values_permissions_check($request)
    {
        return true;
    }
    public function migrate_writing_task($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $limit = (int) $request['limit'];
        $offset = (int) $request['offset'];
        $args = array(
            'post_type'      => "writing_test",
            'post_status'    => 'publish',
            'posts_per_page' => $limit,
            'offset'         => $offset,
            'order'          => 'DESC',
            'orderby'        => 'ID',
        );
        $the_query = new WP_Query($args);

        //return new WP_REST_Response($the_query, 200); 
        if ($the_query->have_posts()) {
            while ($the_query->have_posts()) {
                $the_query->the_post();
                $post_id = get_the_ID();

                //return new WP_REST_Response($post_id, 200); 
                if (!empty($post_id)) {
                    // Create the document
                    //return new WP_REST_Response($document, 200); 
                    $es_response = $this->addWritingTaskElasticsearch($post_id);
                    if ($es_response === true) {
                        $collections[] = $post_id;
                        //return new WP_REST_Response($result, 200); 
                    } else {
                        // return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
                    }
                    //return new WP_REST_Response($collections, 200); 
                }
            }
        }
        if (!empty($collections)) {
            return new WP_REST_Response($collections, 200);
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
    public function addWritingTaskElasticsearch($post_id)
    {
        date_default_timezone_set('Asia/Kolkata');
        $content_post = get_post($post_id);
        $content = $content_post->post_content;
        $content = apply_filters('the_content', $content);
        $filteredCategory = get_post_meta($post_id, 'Category_Tree_Structure', true);

        if (empty($filteredCategory)) {
            $filteredCategory = ["id" => "", "name" => "", "slug" => "", "parent_id" => 0, "sub_category" => []];
        }

        if (is_string($filteredCategory)) {
            // Decode only if it's a string
            $categoryArray = json_decode($filteredCategory, true);
        } else if (is_array($filteredCategory)) {
            // No need to decode if it's already an array
            $categoryArray = $filteredCategory;
        } else {
            // Handle other cases if necessary
        }

        $resultCategoryArray = $categoryArray;
        $writing_task_category = [];
        $writing_task_category[] = $resultCategoryArray['id'];
        $sub_1_category = $resultCategoryArray['sub_category'];

        if (!empty($sub_1_category)) {
            foreach ($sub_1_category as $v) {
                $writing_task_category[] = $v['id'];
                if (!empty($v['sub_category'])) {
                    foreach ($v['sub_category'] as $vv) {
                        $writing_task_category[] = $vv['id'];
                    }
                }
            }
        }

        $curlPost = [
            "data" => [
                "details" => [
                    "record_id" => $post_id,
                    "android_view_type" => 0,
                    "resource_type" => "writing_task",
                    "user_id" => CURRENT_LOGGED_IN_USER_ID,
                    "event_type" => "resourceevent",
                    //"event_type" => "resourceevent-temp",
                    "event_label" => "Writing Task Resource",
                    "title" => get_the_title($post_id),
                    "excerpt" => get_the_excerpt($post_id),
                    "sub_title" => '',
                    "description" => html_entity_decode($content, ENT_NOQUOTES, 'UTF-8'),
                    //"featuredImage"=> $featured_image[0],
                    "topic" => get_post_meta($post_id, 'topic', true),
                    "taxonomy" => $writing_task_category,
                    "parent_taxonomy" => "ielts",
                    "url" => get_permalink($post_id),
                    "published_at" => get_the_time('M j, Y g:i A', $post_id)

                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];

        $posted = post_elastic_event($curlPost);
        if (!empty($posted)) {
            return true;
        }
        //return true;
    }
    public function migrate_writing_task_permissions_check()
    {
        return true;
    }
    public function get_writing_task($request)
    {
        global $wpdb;
        $date = date_default_timezone_set('Asia/Kolkata');
        $writing_task_id = (int)$request['writing_task_id'];
        $limit = (int)$request['limit'];
        $offset = (int)$request['offset'];
        $codes = error_code_setting();
        $display_view = $request['view'];
        $user_id = $request['user_id'];

        if ($writing_task_id > 0) {
            $args = array(
                'page_id' => $writing_task_id,
                'post_type'      => 'writing_test',
                'post_status'    => array('publish', 'draft')
            );
        } else {
            $args = array(
                'post_type'      => 'writing_test',
                'post_status'    => 'publish',
                'posts_per_page' => $limit,
                'offset'         => $offset,
                'order'          => 'DESC',
                'orderby'        => 'ID',
            );

            $args1 = array(
                'post_type'      => 'writing_test',
                'post_status'    => 'publish',
                'posts_per_page' => -1
            );

            $the_query = new WP_Query($args1);
            $total_records = $the_query->post_count;
        }

        $the_query = new WP_Query($args);
        $writing_tests = [];

        if ($the_query->have_posts()) {
            $i = 0;
            while ($the_query->have_posts()) {
                $the_query->the_post();
                $post_id = get_the_ID();

                $content_post = get_post($post_id);
                $content = $content_post->post_content;
                $content = apply_filters('the_content', $content);

                //$content = get_post_field('post_content', $post_id);
                //$content = get_the_content($post_id);
                $post_excerpt = get_the_excerpt($post_id);
                $categories = wp_get_post_terms($post_id, 'course_category');
                $category_id = [];
                foreach ($categories as $key => $value) {
                    $category_id[] = $value->term_id;
                }
                $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'full', false);
                $featured_image_id = get_post_thumbnail_id($post_id);
                $altText = get_post_meta($featured_image_id, '_wp_attachment_image_alt', true);

                if ($writing_task_id > 0) {
                    // Check if topic is empty or false, then set default values
                    $topic_meta = get_post_meta($request['post_id'], 'topic', true);
                    $topic = empty($topic_meta) ? [
                        "name" => "",
                        "description" => "",
                        "minimum_words" => 0,
                        "maximum_words" => 0
                    ] : $topic_meta;
                    $model_answer = get_post_meta($post_id, 'yuno_tset_sample_answer_high_band_content', true);
                    $published_at = get_the_date('M j, Y');
                    $updated_at = get_the_modified_date('M j, Y', $post_id);
                    $has_attempt_details = Quiz::get_attempted_writing_task_details($post_id,$user_id);
                    $category = get_post_meta($post_id, 'Category_Tree_Structure', true);
                    $exam_type_name = '';
                    $writing_task_type_name = '';
                    // Iterate through each category to find the selected child categories
                    foreach ($categories as $term) {
                        // Check if the term is a child of the exam type or writing task type
                        if($term->slug == 'exam-type'){
                            $exam_type_name = $term->name;
                            $exam_type_id = $term->term_id;
                            $exam_type_terms = get_terms(array(
                                'taxonomy' => 'course_category',
                                'hide_empty' => false,
                                'parent' => $exam_type_id
                            ));

                            // Check if there are any errors or if the terms array is empty
                            if (is_wp_error($exam_type_terms)) {
                               // echo 'Error retrieving terms: ' . $exam_type_terms->get_error_message();
                            } elseif (empty($exam_type_terms)) {
                                //echo 'No child terms found for Exam Type.';
                            } else {
                                // Output the names of child terms
                                //echo "Child Terms of Exam Type:";
                                foreach ($exam_type_terms as $child_term) {
                                    $exam_type_name =  $child_term->name;
                                }
                            }

                        } else if($term->slug == 'writing-task-type'){
                            $writing_task_type_name = $term->name;
                            $writing_task_type_id = $term->term_id;
                            $writing_task_type_terms = get_terms(array(
                                'taxonomy' => 'course_category',
                                'hide_empty' => false,
                                'parent' => $writing_task_type_id
                            ));

                            // Check if there are any errors or if the terms array is empty
                            foreach ($writing_task_type_terms as $child_term) {
                                $writing_task_type_name =  $child_term->name;
                            }
                        }
                    }

                    $writing_tests = array(
                        'id' => $post_id,
                        'title'  => html_entity_decode(get_the_title($post_id), ENT_NOQUOTES, 'UTF-8'),
                        //'excerpt' => $post_excerpt,
                        // 'description' => $content,
                        'description' => html_entity_decode($content, ENT_NOQUOTES, 'UTF-8'),
                        //'featured_img' => !empty($featured_image[0]) ? $featured_image[0] : null,
                        //'altText' => !empty($altText) ? $altText : '',
                        'category' => !empty(get_post_meta($post_id, 'Category_Tree_Structure', true)) ? get_post_meta($post_id, 'Category_Tree_Structure', true) : ["id" => "", "name" => "", "slug" => "", "parent_id" => 0, "sub_category" => []],
                        //'category' => $categories,
                        //'category' => get_post_meta($post_id, 'category', true),
                        'model_answer' => html_entity_decode($model_answer, ENT_NOQUOTES, 'UTF-8'),
                        // 'yuno_test_sample_answer_low_band_content' => get_post_meta($post_id, 'yuno_test_sample_answer_low_band_content', true),
                        //'yuno_test_reasons_for_low_band_score' => get_post_meta($post_id, 'yuno_test_reasons_for_low_band_score', true),
                        'topic' => $topic,
                        //'url' => get_permalink($post_id),
                        //'published' => $published_at,
                        //'modified_date' => $updated_at,
                        'exam_type' => $exam_type_name,
                        'writing_task_type' => $writing_task_type_name,
                        'action' => get_post_status($post_id),
                        //'actions' => []
                        "zoho_meta" => [
                            "content_type" => "writing_task",
                            "content_id" => $post_id,
                            "productcode" => '',
                            "leadstatus" => "Not Contacted Yet"
                        ],
                        "has_attempt" => $has_attempt_details
                    );
                } else {
                    // Check if topic is empty or false, then set default values
                    $topic_meta = get_post_meta($request['post_id'], 'topic', true);
                    $topic = empty($topic_meta) ? [
                        "name" => "",
                        "description" => "",
                        "minimum_words" => 0,
                        "maximum_words" => 0
                    ] : $topic_meta;
                    $model_answer = get_post_meta($post_id, 'yuno_tset_sample_answer_high_band_content', true);
                    // related courses array
                    $published_at = get_the_date('M j, Y');
                    $updated_at = get_the_modified_date('M j, Y', $post_id);
                    $writing_tests[$i] = array(
                        'id' => $post_id,
                        'title'  => html_entity_decode(get_the_title($post_id), ENT_NOQUOTES, 'UTF-8'),
                        'excerpt' => $post_excerpt,
                        //'description' => $content,
                        'description' => html_entity_decode($content, ENT_NOQUOTES, 'UTF-8'),
                        //'featured_img' => !empty($featured_image[0]) ? $featured_image[0] : null,
                        //'altText' => !empty($altText) ? $altText : '',
                        'category' => !empty(get_post_meta($post_id, 'Category_Tree_Structure', true)) ? get_post_meta($post_id, 'Category_Tree_Structure', true) : ["id" => "", "name" => "", "slug" => "", "parent_id" => 0, "sub_category" => []],
                        //'category' => $categories,
                        //'category' => get_post_meta($post_id, 'category', true),
                        'model_answer' => html_entity_decode($model_answer, ENT_NOQUOTES, 'UTF-8'),
                        //'yuno_test_sample_answer_low_band_content' => get_post_meta($post_id, 'yuno_test_sample_answer_low_band_content', true),
                        //'yuno_test_reasons_for_low_band_score' => get_post_meta($post_id, 'yuno_test_reasons_for_low_band_score', true),
                        'topic' => $topic,
                        'url' => get_permalink($post_id),
                        'published' => $published_at,
                        //'modified_date' => $updated_at,
                        'action' => get_post_status($post_id),
                        'actions' => []
                    );
                }
                $i++;
            }
        }

        if (empty($writing_tests)) {
            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Writing Task", $codes["GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
        }
        $logger = WP_Structured_Logger::get_instance();

        $logtype = "info";
        $timestamp = date('Y-m-d H:i:s');
        $module = "writing_tests";
        $action = "GET";
        $message = "get call ";
        $user = array();
        $request = array();
        $data = array();
        $logger_result =  $logger->custom_log($logtype, $module, $action, $message, $user, $request, $data);

        if ($writing_task_id == 0 && $display_view == "grid-view") {
            $writing_testsData = [];
            $writing_testsData = [
                "rows" => $writing_tests,
                "columns" => [
                    [
                        "field" => "title",
                        "label" => "Title",
                        "sortable" => true
                    ],
                    [
                        "field" => "published",
                        "label" => "First Published",
                        "sortable" => true
                    ],
                    [
                        "field" => "actions",
                        "label" => "Actions",
                        "sortable" => false
                    ]
                ]
            ];
        } else if ($writing_task_id == 0 && $display_view == "list-view") {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Writing Task", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $writing_tests);
            return new WP_REST_Response($result, 200);
        }

        if ($writing_task_id == 0) {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Writing Task(s)", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $writing_testsData);
            return new WP_REST_Response($result, 200);
        } else {
            if ($display_view == "single") {
                $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Writing Task(s)", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'data' => $writing_tests);
                return new WP_REST_Response($result, 200);
            }
        }
    }
    public function get_writing_task_permissions_check()
    {
        return true;
    }
    /*** Delete Writing Task ***/
    public function delete_writing_task($request)
    {
        global $wpdb;
        $writing_task_id = (int)$request['writing_task_id'];
        $codes = error_code_setting();
        wp_delete_post($writing_task_id);
        $sentData["record_id"] = $writing_task_id;
        $sentData["event_type"] = "resourceevent";
        $status = delete_elastic_event($sentData);
        if ($status) {
            $result = array('code' => $codes["DELETE_DATA"]["code"], 'message' => str_replace("[Module_Name]", "Writing Task", $codes["DELETE_DATA"]["message"]), 'status' => $codes["DELETE_DATA"]["status"]);
            return new WP_REST_Response($result, 200);
        }
    }
    public function delete_writing_task_permissions_check()
    {
        return true;
    }
    public function migrate_writing_task_categories($request)
    {
        $date = date_default_timezone_set('Asia/Kolkata');
        $limit = (int)$request['limit'];
        $offset = (int)$request['offset'];
        $codes = error_code_setting();
        /* $args = array(
                'post_type'      => 'writing_test',
                'post_status'    => 'publish',
                'posts_per_page' => $limit,
                'offset'         => $offset,
                'order'          => 'DESC',
                'orderby'        => 'ID',
            );*/
        $writing_task_id = 4006;

        $args = array(
            'page_id' => $writing_task_id,
            'post_type'      => 'writing_test',
            'post_status'    => array('publish', 'draft')
        );

        $the_query = new WP_Query($args);
        $writing_tests = [];

        if ($the_query->have_posts()) {
            $i = 0;
            while ($the_query->have_posts()) {
                $the_query->the_post();
                $post_id = get_the_ID();
                error_log("post_id -  " . date('Y-m-d H:i:s') . json_encode($post_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                $test_type = get_post_meta($post_id, 'yuno_test_type_check', true); //academics //general

                error_log("test_type -  " . date('Y-m-d H:i:s') . json_encode($test_type) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                $yuno_test_general_or_academic = get_post_meta($post_id, 'yuno_test_general_or_academic', true); //A or G

                error_log("yuno_test_general_or_academic -  " . date('Y-m-d H:i:s') . json_encode($yuno_test_general_or_academic) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                $cloudinary_transformations_terms = get_post_meta($post_id, 'cloudinary_transformations_terms', true); //ids of writing_test_category - taxonomy

                error_log("cloudinary_transformations_terms -  " . date('Y-m-d H:i:s') . json_encode($cloudinary_transformations_terms) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                $writing_test_category = wp_get_post_terms($post_id, 'writing_test_category');
                $categories = wp_get_post_terms($post_id, 'course_category');

                error_log("writing_test_category -  " . date('Y-m-d H:i:s') . json_encode($writing_test_category) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                // Decode JSON to PHP array
                //$writing_test_category_terms = json_decode($writing_test_category, true);
                // Initialize an empty associative array to hold the term_id and slug
                $writing_test_category_termsData = [];

                // Iterate through the array to store term_id and slug
                foreach ($writing_test_category as $term) {

                    error_log("term -  " . date('Y-m-d H:i:s') . json_encode($term) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                    $termID = $term->term_id;  // Changed from $term['term_id'] to $term->term_id
                    $slug = $term->slug;  // Changed from $term['slug'] to $term->slug
                    $name = $term->name;  // Changed from $term['name'] to $term->name
                    $writing_test_category_termsData[$termID] = ["name" => $name, "slug" => $slug];
                }

                error_log("writing_test_category_termsData -  " . date('Y-m-d H:i:s') . json_encode($writing_test_category_termsData) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                // Now you can access them like so:
                foreach ($writing_test_category_termsData as $id => $writing_test_category_term_Data) {
                    //echo "Term ID: $id, Slug: $slug\n";

                    error_log("slugslug -  " . date('Y-m-d H:i:s') . json_encode($writing_test_category_term_Data) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                // ------ get already existed data -- 
                $taxonomy = 'course_category'; // Replace with your custom taxonomy if not 'category'
                $category_name = 'IELTS';
                $slug = 'ielts';

                // Get term by 'name'
                $term = get_term_by('name', $category_name, $taxonomy);

                if ($term && $term->slug === $slug) {
                    $category_id = $term->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("Category ID -  " . date('Y-m-d H:i:s') . json_encode($category_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("Category ID - not found " . date('Y-m-d H:i:s') . json_encode($category_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                $sub_category_name1 = 'Exam Module';
                $sub_catgory_slug1 = 'exam-module';

                // Get term by 'name'
                $term1 = get_term_by('name', $sub_category_name1, $taxonomy);

                if ($term1 && $term1->slug === $sub_catgory_slug1) {
                    $sub_category_id1 = $term1->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("sub_category_id1 -  " . date('Y-m-d H:i:s') . json_encode($sub_category_id1) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("sub_category_id1 - not found " . date('Y-m-d H:i:s') . json_encode($sub_category_id1) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                // ielts exam type with child 
                $sub_category_name2 = 'IELTS Exam Type';
                $sub_catgory_slug2 = 'exam-type';

                // Get term by 'name'
                $term2 = get_term_by('name', $sub_category_name2, $taxonomy);

                if ($term2 && $term2->slug === $sub_catgory_slug2) {
                    $sub_category_id2 = $term2->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("sub_category_id2 -  " . date('Y-m-d H:i:s') . json_encode($sub_category_id2) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("sub_category_id2 - not found " . date('Y-m-d H:i:s') . json_encode($sub_category_id2) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                $sub_category_name2 = 'IELTS Exam Type';
                $sub_catgory_slug2 = 'exam-type';

                // Get term by 'name'
                $term2 = get_term_by('name', $sub_category_name2, $taxonomy);

                if ($term2 && $term2->slug === $sub_catgory_slug2) {
                    $sub_category_id2 = $term2->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("sub_category_id2 -  " . date('Y-m-d H:i:s') . json_encode($sub_category_id2) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("sub_category_id2 - not found " . date('Y-m-d H:i:s') . json_encode($sub_category_id2) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                // ielts exam type with child


                $sub_category_name3 = 'Writing Task Type';
                $sub_catgory_slug3 = 'writing-task-type';

                // Get term by 'name'
                $term3 = get_term_by('name', $sub_category_name3, $taxonomy);

                if ($term3 && $term3->slug === $sub_catgory_slug3) {
                    $sub_category_id3 = $term3->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("sub_category_id3 -  " . date('Y-m-d H:i:s') . json_encode($sub_category_id3) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("sub_category_id3 - not found " . date('Y-m-d H:i:s') . json_encode($sub_category_id3) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }

                // sub_category_id3 child categories

                $sub_category_name3_child1 = 'Writing Task 1';
                $sub_catgory_slug3_child1 = 'writing-task-1';

                // Get term by 'name'
                $term3_child1 = get_term_by('name', $sub_category_name3_child1, $taxonomy);

                if ($term3_child1 && $term3_child1->slug === $sub_catgory_slug3_child1) {
                    $sub_catgory_slug3_child1_id = $sub_catgory_slug3_child1->term_id;
                    //echo 'Category ID: ' . $category_id;
                    error_log("sub_category_id3 -  " . date('Y-m-d H:i:s') . json_encode($sub_catgory_slug3_child1_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                } else {
                    //echo 'Category not found';
                    error_log("sub_category_id3 - not found " . date('Y-m-d H:i:s') . json_encode($sub_catgory_slug3_child1_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
                }


                $category_array = [
                    'id' => 3060,
                    'name' => 'IELTS',
                    'slug' => 'ielts',
                    'parent_id' => 0,
                    'sub_category' => [
                        [
                            'id' => 3076,
                            'name' => 'Exam Module',
                            'slug' => 'exam-module',
                            'parent_id' => 3060,
                            'sub_category' => [
                                [
                                    'id' => 3100,
                                    'name' => 'Writing',
                                    'slug' => 'writing',
                                    'parent_id' => 3076,
                                ],
                            ],
                        ],
                        [
                            'id' => 3081,
                            'name' => 'IELTS Exam Type',
                            'slug' => 'exam-type',
                            'parent_id' => 3060,
                            'sub_category' => [
                                [
                                    'id' => 3082,
                                    'name' => 'Academic',
                                    'slug' => 'academics',
                                    'parent_id' => 3081,
                                ],
                            ],
                        ],
                        [
                            'id' => 5351,
                            'name' => 'Writing Task Type',
                            'slug' => 'writing-task-type',
                            'parent_id' => 3060,
                            'sub_category' => [
                                [
                                    'id' => 5352,
                                    'name' => 'Writing Task 1',
                                    'slug' => 'writing-task-1',
                                    'parent_id' => 5351,
                                ],
                            ],
                        ],
                    ],
                ];

                // To verify the array structure, you can use the following:
                error_log("category_array -  " . date('Y-m-d H:i:s') . json_encode($category_array) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

                // wp_set_post_terms($post_id, $category_id, 'course_category');
            }
        }
    }
    public function migrate_writing_task_categories_permissions_check()
    {
        return true;
    }
    /*** Migrate status of all writing tasks ***/
    public function migrate_writing_task_status($request)
    {
        $date = date_default_timezone_set('Asia/Kolkata');
        $limit = (int)$request['limit'];
        $offset = (int)$request['offset'];
        $codes = error_code_setting();
        $update_event_type = "resourceevent";
        $must[] = [
            "match" => [
                "data.details.resource_type" => "writing_task"
            ]
        ];

        $collections = [];
        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id",
                "data.details.resource_type",
                "data.details.event_type",
                "data.details.event_label",
                "data.details.status",
            ]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/resourceevent/_search?from=" . $offset . "&size=" . $limit,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json",
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $results = json_decode($response);
        foreach ($results->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;

            $status = get_post_status($record_id);

            $id = "resourceevent-" . (int) $record_id;

            if (!empty($status)) {
                $var = "ctx._source.data.details.status = '" . $status . "';";
            } else {
                $var = "ctx._source.data.details.status = '';";
            }

            $curlPostn = [
                "script" => [
                    "inline" => $var,
                    "lang" => "painless",
                ],
            ];
            //return new WP_REST_Response($curlData, 200);
            $param = $update_event_type . "/_update/" . $id;
            //$param="privateclass_temp3"."/_update/".$id;
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" . $param,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($curlPostn, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => array(
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache",
                    "content-type: application/json",
                ),
            ));
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
            //$res = json_decode($response);
            //echo "<pre>";print_r($res);die("if cURL Error");
            //error_log("ccc -- ".json_encode($res));

            if (empty($err)) {
                $res = json_decode($response);
                if ($res === null) {
                    return new WP_Error('json_error', 'Error decoding JSON', array('status' => 500));
                }
                // Consider adding error handling based on the response from Elasticsearch
            } else {
                // Handle cURL error, log it, or return a WP_Error
                return new WP_Error('curl_error', 'cURL Error: ' . $err, array('status' => 500));
            }
        }

        return new WP_REST_Response($response, 200);
    }
    public function migrate_writing_task_status_permissions_check()
    {
        return true;
    }
    public function post_attempt_writing_task($request) { 
        // Initialize the type of log; it can be "info", "error", etc.
        $logtype = "info";
        // Message to be logged; gives a human-readable description of the log
        $message = "start";
        // Data to be logged; typically, this would be the data you're working with
        $request = $request;
        // Initializing an empty array for additional data; can be used for meta information
        $data = [];
        // User-related data; if you have user info, this is where you'd put it
        $user = [];
        // Get the singleton instance of WP_Structured_Logger
        $logger = WP_Structured_Logger::get_instance();

        // Call the custom_log method to create the log entry
        // Parameters: logtype, module, action, message, user, request, and additional data
        $logger_result = $logger->custom_log($logtype, "Writing Task Attempt", "post_attempt_writing_task", $message, $user, $request, $data);
        global $wpdb;
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        error_log("post_attempt_writing_task  request-  " . date('Y-m-d H:i:s') . json_encode($request) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

        // Extract data from the request body
        $data = json_decode($request->get_body(), true);
        error_log("post_attempt_writing_task  data -  " . date('Y-m-d H:i:s') . json_encode($data) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
        $writing_task_id = $data['id']; //writing task id
        $learner_id = $data['learner_id'];//learner who is attempting quiz
        $exam_type = $data['exam_type']; // Academics or General
        $task_type = $data['task_type']; // Task 1 or Task 2
        $answer = $data['answer']; // Answer given by learner
        $category = $data['category']; // Category of the writing task

        // Validate writing task ID
        if (!isset($writing_task_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Writing Task should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate learner ID
        if (empty($learner_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate exam type
        if (empty($exam_type)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Exam type should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate task type
        if (empty($task_type)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Task type should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate answer
        if (empty($answer)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Answer should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Validate category
        if (empty($category)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        // Get current timestamp for the attempt
        $attempt_datetime = date("Y-m-d H:i:s");
        // Assume successful creation in database
        $post = array();
        $post['post_status'] = 'publish';
        $post['post_type'] = 'quiz_attempt';
        $post['post_title'] = 'Attempt for ' . $writing_task_id;
        $post['post_author'] = 2;
        $attempt_id = wp_insert_post($post);
        // Check if the post was created successfully
        error_log("post_attempt_writing_task  attempt_id -  " . date('Y-m-d H:i:s') . json_encode($attempt_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
        
        if (is_wp_error($attempt_id)) {
            error_log('Failed to insert post: ' . $attempt_id->get_error_message());
            return $attempt_id;
        }
        // Update post metadata
        update_post_meta($attempt_id, 'writing_task_id', $writing_task_id);
        update_post_meta($attempt_id, 'learner_id', $learner_id);
        update_post_meta($attempt_id, 'attempt_datetime', $attempt_datetime);
        update_post_meta($attempt_id, 'exam_type', $exam_type);
        update_post_meta($attempt_id, 'task_type', $task_type);
        update_post_meta($attempt_id, 'answer', $answer);
        update_post_meta($attempt_id, 'category', $category);

        $all_meta = get_post_meta($attempt_id);
        error_log("post_attempt_writing_task  all_meta -  " . date('Y-m-d H:i:s') . json_encode($all_meta) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
        error_log("post_attempt_writing_task  attempt_id -  " . date('Y-m-d H:i:s') . json_encode($attempt_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

        // Get the writing task categories
        $categories = wp_get_post_terms($writing_task_id, 'course_category', array('fields' => 'all'));
        error_log("post_attempt_writing_task  categories -  " . date('Y-m-d H:i:s') . json_encode($categories) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');

        $parent_category = null;
        // Loop through all categories and find the parent category
        foreach ($categories as $category) {
            if ($category->parent == 0) {
                // This is the parent category (has no parent)
                $parent_category = $category;
                break; // Exit the loop since we found the parent category
            }
        }
        error_log("post_attempt_writing_task  parent_category -  " . date('Y-m-d H:i:s') . json_encode($parent_category) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');
        // Check if a parent category was found
        if ($parent_category !== null) {
            // Do something with the parent category
            $category_id = $parent_category->term_id;
            // You can use $category_id or any other properties of $parent_category
            update_post_meta($attempt_id, 'category_id', $category_id);
            // Optionally, return or use $parent_category as needed
           // return new WP_REST_Response($parent_category, 200);
        } else {
            // Handle the case where no parent category is found (optional)
            return new WP_REST_Response('No parent category found', 404);
        }
        error_log("post_attempt_writing_task  category_id -  " . date('Y-m-d H:i:s') . json_encode($category_id) . "\n\n", 3, ABSPATH . 'error-logs/writing_task_script.log');


        // Log the attempt in Elasticsearch
        $curlPost = [
            "data" => [
                "details" => [
                    "record_id" => $attempt_id,
                    "user_id" => CURRENT_LOGGED_IN_USER_ID,
                    "event_type" => "quiz_attempt",
                    "event_label" => "Writing Task Attempt",
                    "resource_type" => "writing_task", // resource type
                    "writing_task_id" => $writing_task_id, // resource ID
                    "learner_id" => $learner_id, // current user id
                    "exam_type" => $exam_type, // Academics or General
                    "task_type" => $task_type, // Task 1 or Task 2
                    "taxonomy" => get_post_meta($attempt_id, 'category_id', true),
                    "attempt_datetime" => $attempt_datetime,
                    "attempted_datetime" => $attempt_datetime,
                    "published_at" => get_the_time('M j, Y g:i A', $attempt_id)
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];
        // After successful creation in database call ES function
        try {
            post_elastic_event($curlPost);
        } catch (Exception $e) {
            // Handle Elasticsearch exception
            //error_log('Failed to insert post in Elasticsearch: ' . $e->getMessage());
            return new WP_REST_Response(['error' => 'Failed to insert post in Elasticsearch'], 500);
        }
    
        // Simulate external API call for token
        $token_response = $this->cgf_api_request_token();
        error_log(" cgf_api_request_token token_response === " . date('Y-m-d H:i:s') . json_encode($token_response)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
        if ($token_response['status'] === 'error') {
            return new WP_REST_Response(['error' => 'Failed to retrieve token'], 500);
        }
        $evaluation_response = $this->cgf_api_evaluate_writing($token_response['token'], $writing_task_id, $learner_id, $exam_type, $task_type, $answer, $category_id);
        error_log(" cgf_api_evaluate_writing evaluation_response === " . date('Y-m-d H:i:s') . json_encode($evaluation_response)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');

        error_log(" cgf_api_evaluate_writing evaluation_response status === " . date('Y-m-d H:i:s') . json_encode($evaluation_response['status'])."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
        if ($evaluation_response['status'] === 'error') {
            return new WP_REST_Response(['error' => 'Evaluation failed'], 500);
        } else {
            // Store evaluation ID
            update_post_meta($attempt_id, 'evaluation_id', $evaluation_response['evaluation_id']);
            // final api response
            $final_response = [
                'attempt_id' => (int)$attempt_id,
                'learner_id' => (int)$learner_id,
                'evaluation_id' => $evaluation_response['evaluation_id'],
            ];
            $message = str_replace("[Module_Name]", "Writing Task Attempt", $codes["POST_INSERT"]["message"]);
            $code = $codes["POST_INSERT"]["code"];
            if ($attempt_id > 0) {
                $result = array(
                    'code' => $code,
                    'message' => $message,
                    'status' => $code,
                    'data' => $final_response
                );
                //return new WP_REST_Response($result, 200);    
                return $result;
                $logger_result = $logger->custom_log($logtype, "Writing Task Attempt", "post_attempt_writing_task", "end post_attempt_writing_task", $user, $request, $result);
            } else {
                return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
            }

        }
    }
    function cgf_api_request_token() { 
        //Simulated token request
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => CGF_URL .'token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query([
            'username' => CGF_USERNAME,
            'password' => CGF_PASSWORD
        ]),
        //CURLOPT_POSTFIELDS => 'username=yuno&password=yuno%40ielts',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded'
        ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        // Decode the JSON string into an associative array
        $responseArray = json_decode($response, true);
        error_log(" cgf_api_request_token function err  === " . date('Y-m-d H:i:s') . json_encode($err)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
        if ($err) {
            return ['status' => 'error', 'message' => 'cURL Error: ' . $err];
        }
        error_log(" cgf_api_request_token response === " . date('Y-m-d H:i:s') . json_encode($response)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
        //return $response; // Consider handling response more appropriately depending on the context
        return ['token' => $responseArray['access_token'], 'token_type' => $responseArray['token_type']];
    }

    function cgf_api_evaluate_writing($token, $writing_task_id, $learner_id, $exam_type, $task_type, $answer, $category_id) {
        // Simulated evaluation API call
        global $wpdb;
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();

         // Adjust exam_type based on input
        if ($exam_type == 'General') {
            $exam_type = 'GT';
        } elseif ($exam_type == 'Academics') {
            $exam_type = 'AC';
        }
        // Determine question_type based on exam_type and task_type
       // $question_type = 'default_type'; // Default value if none of the conditions match
        if ($task_type == 'Writing Task 1' && $exam_type == 'AC') {
            $question_type = 'ilt003';
        } elseif ($task_type == 'Writing Task 2' && $exam_type == 'AC') {
            $question_type = 'ilt002';
        } elseif ($task_type == 'Writing Task 1' && $exam_type == 'GT') {
            $question_type = 'ilt004';
        } elseif ($task_type == 'Writing Task 2' && $exam_type == 'GT') {
            $question_type = 'ilt001';
        }

        // Fetch the post title from WordPress
        $post = get_post($writing_task_id);
        $question_text = $post ? $post->post_title : '';
    
        $payload = [
            "client_id" => CGF_CLIENT_ID,
           // "client_id" => 'CF048',
            "student_id" => (string)$learner_id,
            "exam_type" => $exam_type,
            "student_name" => "Test", // Consider making this dynamic if possible
            "image_urls" => ["string"], // You might want to update this as needed
            "evaluation_text" => [
                [
                    "question_id" => (string)$writing_task_id,
                    "question_number" => "1",
                    "question_section" => "writing",
                    "question_type" => $question_type,
                    "question_text" => $question_text, // Consider making this dynamic if applicable
                    "answer_text" => $answer
                ]
            ]
        ];

        error_log(" cgf_api_evaluate_writing function CURL URL === " . date('Y-m-d H:i:s') . CGF_URL.'v1/ielts/evaluation'."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');

        error_log(" cgf_api_evaluate_writing function CURL PAYLOAD === " . date('Y-m-d H:i:s') . json_encode($payload)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => CGF_URL.'v1/ielts/evaluation',
            //CURLOPT_URL => 'https://ilets-qv4g7htqyq-el.a.run.app/v1/ielts/evaluation/',
            //CURLOPT_URL => 'https://api-staging.crazygoldfish.com/v1/ielts/evaluation',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30, // Set to a sensible default, 0 means no timeout which is not recommended
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $token
            ],
        ]);
    
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
    
        if ($err) {
            return ['status' => 'error', 'message' => 'cURL Error: ' . $err];
        }
        error_log(" cgf_api_evaluate_writing function responseArray === " . date('Y-m-d H:i:s') . json_encode($response)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
       // return $response; // Consider handling response more appropriately depending on the context
       $responseArray = json_decode($response, true);

       error_log(" cgf_api_evaluate_writing function responseArray  responseArray === " . date('Y-m-d H:i:s') . $responseArray."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');

       error_log(" cgf_api_evaluate_writing function evaluation_id  === " . date('Y-m-d H:i:s') . json_encode($responseArray['data']['evaluation_id'])."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
       // Check if 'data' is present and is not empty
        if (isset($responseArray['data']['evaluation_id'])) {
            $evaluation_id = $responseArray['data']['evaluation_id'];
            // Assuming you want to also capture the top-level 'status' if available
            $evaluation_status = isset($responseArray['status']) ? $responseArray['status'] : 'unknown';
            return [
                'evaluation_id' => $evaluation_id,
                'status' => 'success',
                'api_status' => $evaluation_status // This captures the 'status' from the API response
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Evaluation failed or evaluation_id not found'
            ];
        }
      
    }

    public function get_feedback_by_attempt_id($request) {
        global $wpdb;
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Kolkata');
        // Get the 'attempt_id' from the query parameters
        $attempt_id = $request['attempt_id'];

        // Validate attempt_id is present
        if (empty($attempt_id)) {
           // return new WP_REST_Response(['error' => 'Missing attempt_id'], 400);
            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Missing attempt_id", $codes["GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
        }

        // Check if data is stored in DB
        $feedback_data = $this->check_feedback_in_db($attempt_id);
        if (is_wp_error($feedback_data)) {
           // return new WP_REST_Response(['error' => 'Data not found'], 404);
            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Data not found", $codes["GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
        } 
        // If data exists, return it
        $result = [
            'code' => $codes["GET_SUCCESS"]["code"],
            'message' => str_replace("[Module_Name]", "Writing Task Score and Feedback", $codes["GET_SUCCESS"]["message"]),
            'status' => $codes["GET_SUCCESS"]["status"],
            'data' => $feedback_data
        ];
        return new WP_REST_Response($result, 200);
    }
    
    function check_feedback_in_db($attempt_id) { 
        global $wpdb;
        $args = array(
            'post_type' => 'quiz_attempt',
            'post_status' => 'publish',
            'p' => $attempt_id,
        );
        $query = new WP_Query($args);
        // Check if the query returned any posts
        error_log("check_feedback_in_db  query === " . date('Y-m-d H:i:s') . json_encode($query)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');

        if (!$query->have_posts()) {
            return new WP_Error('no_data', 'No data found for this attempt_id');

        }
        while ($query->have_posts()) {
            $query->the_post();
            // Gather all required meta data
            $task_id = get_post_meta($attempt_id, 'writing_task_id', true);
            $learner_id = get_post_meta($attempt_id, 'learner_id', true);
            $exam_type = get_post_meta($attempt_id, 'exam_type', true);
            $task_type = get_post_meta($attempt_id, 'task_type', true);
            $answer = get_post_meta($attempt_id, 'answer', true);
            $category = get_post_meta($attempt_id, 'category', true);
            $model_answer = get_post_meta($task_id, 'model_answer', true);
            $overall_score = get_post_meta($attempt_id, 'score', true);
            $scope_of_improvement = get_post_meta($attempt_id, 'scope_of_improvement', true);
           // Handling empty meta and setting default structures
            $holistic_evaluation =  trim(get_post_meta($attempt_id, 'answer_holistic_evaluation', true));
            $objective_evaluation = get_post_meta($attempt_id, 'answer_objective_evaluation', true);

            //$holistic_evaluation = !empty($holistic_evaluation) ? json_decode($holistic_evaluation, true) : [];
            $holistic_evaluation = is_array($tmp = json_decode($holistic_evaluation, true)) ? $tmp : [];

            $objective_evaluation = !empty($objective_evaluation) ? json_decode($objective_evaluation, true) : new stdClass();


            // Construct the response
            $response = [
                "id" => (int)$task_id,
                "learner_id" => intval($learner_id),
                "exam_type" => $exam_type,
                "task_type" => $task_type,
                "answer" => $answer,
               // "category" => explode(',', $category),
                "category" => $category,
                "model_answer" => $model_answer,
                "overall_score" => $overall_score,
                "scope_of_improvement" => $scope_of_improvement,
                "answer_holistic_evaluation" => $holistic_evaluation,
                "answer_objective_evaluation" => $objective_evaluation,
            ];

            error_log("check_feedback_in_db  response === " . date('Y-m-d H:i:s') . json_encode($response)."\n\n", 3, ABSPATH . 'error-logs/attempt_writing_task.log');
            return $response;
        }
    }
    
}
