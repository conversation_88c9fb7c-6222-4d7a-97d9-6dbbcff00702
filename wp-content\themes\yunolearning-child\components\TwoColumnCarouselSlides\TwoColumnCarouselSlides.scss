@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 30px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}
#app {
  .carousel-dots {
    padding-bottom: 45px;
  }
  .carousel-dots .dot {
    display: inline-block;
    height: 10px;
    width: 10px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    cursor: pointer;
  }
  .carousel-dots .dot.active {
    background-color: $primary;
  }
  .carousel-arrow {
    opacity: 1 !important;
    visibility: visible !important;
  }

  .carousel-list {
    &.has-shadow {
      box-shadow: none;
    }

    .carousel-slides {
      .hero-body {
        align-items: center;
        padding: 2rem 1.5rem 0 1.5rem; 

        @media (min-width: 768px) {
          padding-top: 15px; 
        }
        .contentWrapper {
          text-align: center;
          @media (max-width: 768px) {
            padding: 30px;
            text-align: center;
          }
          .description {
            max-width: 600px;
            margin: auto;
          }
        }
        .videoWrapper {
          @media (max-width: 768px) {
            padding: 0px;
            text-align: center;
          }
        
        }
      }

      figure {
        .itemInfo {
          @include setFont($body1, 24px, 400, $gapLargest);

          @media (min-width: 768px) {
            min-height: 72px;
          }
        }

        img {
          width: 100%;
          height: auto;

          @media (min-width: 768px) {
            height: 343px;
          }
        }
      }
    }

    .carousel-arrow {
      .icon {
        color: #0000005e;

        &::after {
          content: "\e5cb";
          @extend .material-icons;
        }

        &.has-icons-right {
          right: 0.3rem !important;
          top: 61% !important;
          &::after {
            content: "\e5cc";
          }
        }
        &.has-icons-left {
          left: 0.3rem !important;
          top: 61% !important;
        }
        &:hover {
          color: #7957d5;
          border: none;
        }
      }
    }

    .carousel-indicator {
      .indicator-item {
        .indicator-style {
          border-color: #d9d9d9;
          background-color: #d9d9d9;
        }

        &.is-active {
          .indicator-style {
            background-color: $primary;
          }
        }
      }
    }
    .cta {
      .secondaryCTA {
        padding: 8px 20px;
        border-radius: 4px;
        border: 1px solid #a81e22;
        background-color: #a81e22;
        line-height: normal;
        font-size: 16px;
        font-weight: 500;
        color: #fff;

        &:hover {
          text-decoration: none;
          background-color: #410004;
        }
      }
    }
  }
}
