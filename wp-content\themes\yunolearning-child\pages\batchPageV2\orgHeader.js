Vue.component('yuno-org-header', {
    template: `
        <div class="yunoHeader">
            <div class="container-fluid noOverflow">
                <figure class="logo navbar-brand">
                    <template v-if="orgAdmin.loading">
                        <b-skeleton height="72px" width="106px"></b-skeleton>
                    </template>
                    <template v-else-if="orgAdmin.success && orgAdmin.error === null">
                        <img :src="orgAdmin.data.logo_image_url" :alt="orgAdmin.data.organisation_name" :class="imageClass"  @load="calculateAspectRatio">
                    </template>
                </figure>
            </div>
        </div>
    `,
    data() {
        return {
            imageClass: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'orgAdmin'
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        calculateAspectRatio(event) {
            const img = event.target;
            const aspectRatio = img.naturalWidth / img.naturalHeight;
            if (aspectRatio > 1) {
                this.imageClass = 'landscape';
            } else if (aspectRatio < 1) {
                this.imageClass = 'portrait';
            } else {
                this.imageClass = 'square';
            }
        }
    }
});