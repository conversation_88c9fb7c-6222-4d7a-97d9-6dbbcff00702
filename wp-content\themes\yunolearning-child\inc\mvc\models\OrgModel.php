<?php

namespace V4;

/**
 * Class OrgModel
 * Handles Org-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class OrgModel extends Model
{
    /**
     * Constructor for OrgModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema', 'schema');
        $this->loadLibary('locale');
        $this->loadLibary('common');
    }

    /**
     * Retrieves the total number of enrollments for a given organization.
     *
     * This function queries Elasticsearch to fetch the count of enrollments based on the organization ID. 
     * It ensures that the enrollments have a valid enrollment status and aggregates the result.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $orgId The organization ID or an array of organization IDs.
     * @return array An associative array containing the total number of enrollments.
     * @throws Exception If an error occurs while querying Elasticsearch or if the response is invalid.
     * <AUTHOR> Name]
     */
    public function getOrgEnrollments($orgId)
    {
        // Validate and format orgId array
        if (!is_array($orgId)) {
            $orgId = [$orgId];
        }

        // Build the optimized query with aggregations
        $curlPost = [
            "size" => 0, // No need to return full documents
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            "terms" => [
                                "data.details.org_admin.id" => array_map('intval', (array) $orgId) // Ensure it's an array of integers
                            ]
                        ]
                    ],
                    "filter" => [
                        ["exists" => ["field" => "data.details.enrollment_status.keyword"]] // Ensure status exists
                    ]
                ]
            ]
        ];


        // Send the query to Elasticsearch
        $response = $this->es->customQuery($curlPost, 'batchenrollmentevent');
        // Extract total count from response
        $totalCount = $response['body']['hits']['total']['value'] ?? 0;


        // Return the enrollment details with total counts
        return [
            'total_enrollments' => $totalCount
        ];
    }

    /**
     * Retrieves detailed information about an organization, including its enrollment data, features, subscription, and more.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query The organization identifier (ID) or an array containing query parameters.
     * @param array $filter Optional filters for debugging and validation.
     * @return array JSON response containing organization details.
     * @throws Exception If an error occurs while fetching or processing data from Elasticsearch or WordPress.
     * <AUTHOR>
     */
    public function getOrganization($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch organization data from your data source (like a database or Elasticsearch)
            $organizationDataResponse = $this->es->read('org', 'org-' . $query['id']);
        }

        if ($organizationDataResponse['status_code'] == 200) {
            $organization = $organizationDataResponse['body']['_source']['data']['details'];
            $orgEnrollments = $this->getOrgEnrollments($query['id']);
            $enrollments = [];

            if (!empty($orgEnrollments)) {
                $enrollments = $orgEnrollments['total_enrollments'];
            }
            $prasar = [];



            if (!empty($prasar_prasar_url = get_post_meta($organization['record_id'], 'prasar_url', true))) {
                $prasar['prasar_url'] = $prasar_prasar_url;
            } else {
                $prasar['prasar_url'] = "";
            }
            if (!empty($privacy_policy_url = get_post_meta($organization['record_id'], 'privacy_policy_url', true))) {
                $prasar['privacy_policy_url'] = $privacy_policy_url;
            } else {
                $prasar['privacy_policy_url'] = "";
            }
            if (!empty($terms_and_conditions_url = get_post_meta($organization['record_id'], 'terms_and_conditions_url', true))) {
                $prasar['terms_and_conditions_url'] = $terms_and_conditions_url;
            } else {
                $prasar['terms_and_conditions_url'] = "";
            }

            if (!empty($prasar_live_classes_included = get_post_meta($organization['record_id'], 'live_classes_included', true))) {
                $prasar['features']['has_courses'] = filter_var($prasar_live_classes_included, FILTER_VALIDATE_BOOLEAN);
            } else {
                $prasar['features']['has_courses'] = false;
            }

            if (!empty($study_material = get_post_meta($organization['record_id'], 'study_material', true))) {
                $prasar['features']['has_study_material'] = filter_var($study_material, FILTER_VALIDATE_BOOLEAN);
            } else {
                $prasar['features']['has_study_material'] = false;
            }

            if (!empty($videos = get_post_meta($organization['record_id'], 'videos', true))) {
                $prasar['features']['has_videos'] = filter_var($videos, FILTER_VALIDATE_BOOLEAN);
            } else {
                $prasar['features']['has_videos'] = false;
            }

            if (!empty($practice_tests = get_post_meta($organization['record_id'], 'practice_tests', true))) {
                $prasar['features']['has_practice_tests'] = filter_var($practice_tests, FILTER_VALIDATE_BOOLEAN);
            } else {
                $prasar['features']['has_practice_tests'] = false;
            }

            $prasar['subject_categories'] = array_map(function ($category) {
                return $this->load->subData("category", "getCategory", $category['id'], ['schema' => 'Category_Minimal']);
            }, $organization['prasar_subject_categories'] ?? []);

            if (!empty($only_my_org_courses = get_post_meta($organization['record_id'], 'only_my_org_courses', true))) {
                $prasar['only_my_org_courses'] = $only_my_org_courses;
            } else {
                $prasar['only_my_org_courses'] = "";
            }

            if (!empty($prasar_mobile_app = get_post_meta($organization['record_id'], 'prasar_mobile_app', true))) {
                $prasar['mobile_app'] = $prasar_mobile_app;
            } else {
                $prasar['mobile_app']['app_name'] = "";
                $prasar['mobile_app']['app_short_description'] = "";
                $prasar['mobile_app']['app_long_description'] = "";
                $prasar['mobile_app']['privacy_policy_url'] = "";
                $prasar['mobile_app']['video_url'] = "";
            }
            if (!empty($prasar['mobile_app']) && !empty($prasar_mobile_app_developer = get_post_meta($organization['record_id'], 'prasar_app_developer', true))) {
                $prasar['mobile_app']['app_developer'] = $prasar_mobile_app_developer;
            } else {
                $prasar['mobile_app']['app_developer']['email'] = "";
                $prasar['mobile_app']['app_developer']['phone'] = "";
                $prasar['mobile_app']['app_developer']['website'] = "";
            }
            if (!empty($last_updated = get_post_meta($organization['record_id'], 'prasar_last_updated', true))) {
                $prasar['last_updated'] = $last_updated;
            } else {
                $prasar['last_updated'] = "";
            }
            $prasar['subject_categories'] = array_map(function ($category) {
                    return $this->load->subData("category", "getCategory", $category['id'], ['schema' => 'Category_Minimal']);
                }, $organization['category'] ?? []);

            $vC = $organization['org_vc_app'];
            $gmeetIcon = "images/googleMeetIcon.png";
            $zoomIcon = "images/zoomIcon.png";
            $vcFavIcon = ($vC === "gmeet") ? $gmeetIcon : (($vC === "zoom") ? $zoomIcon : null);
            $favIcon = array(
                "url" => ynAssetURL($vcFavIcon),
                "alt_text" => $this->common->imgAltTextFromUrl(ynAssetURL($vcFavIcon))
            );

            $virtualClassroom = array(
                'platform' => $vC,
                'name' => ($vC == "zoom" ? ucfirst($vC) : ($vC == "gmeet" ? "Google Meet" : "")),
                'fav_icon_url' => $favIcon
            );



            // Build the structured response
            $orgResponse = array(
                'id' =>  $organization['record_id'] ?? '',  // Organization ID
                'name' =>  $organization['organisation_name'] ?? '',  // Organization name
                'created_at' =>  [
                    'time' => $organization['created_at'] ?? '',
                    'timezone' => $this->locale->activeTimezone() ?? ''
                ],  // Creation timestamp
                'short_description' =>  $organization['excerpt'] ?? '',  // Short description
                'long_description' =>  $organization['description'] ?? '',  // Long description
                'url' =>  $organization['org_url'] ?? '',  // URL of the organization
                // Logo images
                'logo' =>  [
                    'url' =>  $organization['logo_image'] ?? '',
                    "alt_text" => $this->common->imgAltTextFromUrl($organization['logo_image'])
                ],
                'fav_icon' => [
                    'url' =>  $organization['fav_icon'] ?? '',
                    "alt_text" => $this->common->imgAltTextFromUrl($organization['fav_icon'])
                ],
                'theme' => [
                    'font_family' => $organization['color_theme']['font'],
                    'background_color' => $organization['color_theme']['background_color'],
                    'primary_color' => $organization['color_theme']['primary_color'] ?? $organization['color_theme']['theme_color']['primary'],
                ],
                'number_of_employees' =>  $organization['number_of_employees'],  // Number of employees
                // Industry data
                'industry' =>  [
                    'type' => $organization['industry'] ?? '', // ['MANUFACTURING', 'TECHNOLOGY', 'CREATIVESERVICES', 'PROFESSIONALSERVICES']
                    'slug' => $organization['industry'] ?? '',
                    'name' => ucfirst($organization['industry'] ?? ''),
                    'sub_industry' => array_map(function ($sub_industry) {
                        return [
                            'type' => $sub_industry,
                            'slug' => $sub_industry,
                            'name' => ucfirst($sub_industry), // Capitalize the first letter
                        ];
                    }, (array)($organization['sub_industry'] ?? [])),
                ],

                // Category data (array of categories the organization belongs to)
                'subject_categories' => array_map(function ($category) {
                    return $this->load->subData("category", "getCategory", $category['id'], ['schema' => 'Category_Minimal']);
                }, $organization['category'] ?? []),
                // Admins
                'admins' => array_map(function ($admin) {
                    return $this->load->subData("user", "getUser", $admin, ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]);
                }, (array)$organization['user_id']),

                // Enrollments data
                'enrollments' => $enrollments,

                // Academies data
                'academies' => $this->load->subData("academy", "getAcademies", ['orgId' => $organization['record_id']], ['key' => 'data']),

                // Yuno academy subscription
                'yuno_academy_subscription' =>  [
                    'id' => $organization['yuno_academy_subscription']['plan_id'] ?? '', // Unique ID of plan; example: 1
                    'plan_name' => $organization['yuno_academy_subscription']['plan_label'] ?? '', // label of plan; enum: "BASIC, CORE, PRO"; example: Basic; default: BASIC
                    'is_active' => ($organization['yuno_academy_subscription']['plan_status'] ?? '') == "active" ? true : false, // status of plan; example: active; default: true
                    'billing' => [ // time interval i.e. frequency; example: month
                        'frequency' => $organization['yuno_academy_subscription']['billing_format']['frequency'] ?? '', // frequency of billing: monthly, quarterly, or annually; enum: "MONTHLY, QUARTERLY, ANNUALLY"; example: MONTHLY
                        'country' => [
                            'id' => 0,
                            'name' => $organization['yuno_academy_subscription']['billing_format']['country_name'] ?? '',
                            'code' => $organization['yuno_academy_subscription']['billing_format']['country_code'] ?? ''
                        ], // The customer's country
                        'currency' => [
                            'code' => $organization['yuno_academy_subscription']['billing_format']['currency'] ?? '',
                            'name' => $organization['yuno_academy_subscription']['billing_format']['currency_name'] ?? '',
                            'symbol' => $organization['yuno_academy_subscription']['billing_format']['currency_symbol'] ?? '',
                            'symbol_html' => $organization['yuno_academy_subscription']['billing_format']['currency_symbol_html'] ?? ''
                        ], // The currency in which the customer is billed
                        'inclusive_tax' => 0.0,
                        'exclusive_tax' => 0.0,
                        'tax' => [
                            'type' => '', //type of tax like GST
                            'label' => '', //label of tax
                            'percentage' => '',  //percentage of tax
                            'amount' => ''   //amount of tax
                        ], // Tax associated with the billed amount
                        'payment_gateway' => [ // gateway info for payments
                            'gateway' => '', // Name of payment gateway, e.g., Stripe, Razorpay; enum: "Razorpay, Stripe"; example: Razorpay
                            'subscription_plan_id' => '', // Subscription plan ID in the payment gateway database
                        ],
                    ],
                    'has_access' => [ // The list of platform capabilities that the Academy is entitled to
                        'number_of_instructors' =>  $organization['yuno_academy_subscription']['max_instructors'] ?? '', // max number of instructors allowed to be mapped with courses of academy; example: 1
                        'api' =>  $organization['yuno_academy_subscription']['api_access'] ?? '', // API access to academy; default: true
                        'prasar' =>  $organization['yuno_academy_subscription']['prasar'] ?? '', // stands for white label app; default: true
                    ],
                    'academies' => $organization['academies'] ?? [0]
                ],
                'prasar' => $prasar,
                'virtual_classroom' => $virtualClassroom
            );
            
            // Validate the response against the Organization schema
            return $this->schema->validate($orgResponse, 'Organization', $filter);
        }
    }

    /**
     * Updates the virtual classroom settings (app) for a given organization.
     *
     * @since 1.0.0
     * @access public
     * @param int $orgId The ID of the organization whose virtual classroom settings are to be updated.
     * @param array $data Array containing the new virtual classroom app data.
     * @return bool Returns true if the settings were updated successfully, false otherwise.
     * @throws Exception If the post ID is invalid or if an error occurs during the update.
     * <AUTHOR> Name]
     */
    function updOrgVCSettings($orgId, $data)
    {
        $post = get_post($orgId);
        if (! $post || 'org' !== get_post_type($orgId)) {
            return false; // Invalid post ID or not a place
        }
        update_post_meta($orgId, 'org_vc_app', $data['app']);

        $query = [
            "data" => [
                "details" => [
                    "org_vc_app" => $data['app']
                ]
            ]
        ];

        $orgDataResponse = $this->es->update('org', 'org-' . $orgId, $query);

        if ($orgDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    /**
     * Generates organization filters based on the user's role.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The selected organization ID (if any).
     * @return array|false Returns an array containing organization filter data or false if no data found.
     * <AUTHOR>
     */
    public function generateEnrollmentOrgFilters($userId, $orgId)
    {
        return [
            'filter' => 'org_id',
            'title' => 'Organization',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Organization',
            'ui_control_type' => 'dynamic_dropdown',
            'selected' => $orgId,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];
    }
    public function generateEnrollmentOrgFiltersOld($userId, $orgId)
    {
        $this->loadModel('user');
        $row = [
            'filter' => 'organization',
            'title' => 'Organization',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Organization',
            'ui_control_type' => 'dropdown',
            'selected' => $orgId,
            'items' => []
        ];

        $role = $this->userModel->getUserRole($userId);

        // Query conditions based on role
        $queryConditions = [];
        if ($role === 'yuno-admin') {
            // Yuno Admin: Fetch all organizations
            $queryConditions = [];
        } elseif ($role === 'org-admin' && $orgId > 0) {
            // Org Admin: Fetch only their organization
            $queryConditions = [['term' => ['data.details.org_id' => $orgId]]];
        }

        // Build Elasticsearch query
        $customQuery = [
            'query' => ['bool' => ['must' => $queryConditions]]
        ];

        // Fetch organizations from Elasticsearch
        $orgRecords = $this->es->customQuery($customQuery, 'org', []);

        if ($orgRecords['status_code'] == 200) {
            $organizations = $orgRecords['body']['hits']['hits'];
        } else {
            return false;
        }

        // Populate organization filter list
        foreach ($organizations as $organization) {
            $details = $organization['_source']['data']['details'] ?? [];
            $organizationId = $details['record_id'] ?? 0;
            $organizationName = $details['organisation_name'] ?? '';

            if ($organizationId) {
                $row['items'][] = [
                    'id' => $organizationId,
                    'label' => $organizationName,
                    'filter' => 'organization',
                ];
            }
        }

        return $row;
    }
}
