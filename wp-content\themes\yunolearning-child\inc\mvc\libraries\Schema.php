<?php
namespace V4;
/**
 * Schema class to handle schema validation and type casting



* Use https://jsontophp.com/ to convert stoplight models to PHP array for easy integration 
*/
class Schema extends Library
{
    /**
     * Constructor to initialize the Schema class
     */
    public function __construct()
    {
        parent::__construct();
        // Initialization code if needed
    }

    /**
     * Type cast a value to a specified type
     *
     * @param mixed $value The value to type cast
     * @param string $type The type to cast the value to
     * @return mixed The type casted value
     */
    private function typeCast($value, $type)
    {
        switch ($type) {
            case 'int':
            case 'integer':
            case 'number':
                return (int) $value;
        
            case 'float':
            case 'double':
            case 'real':
                return (float) $value;
        
            case 'bool':
            case 'boolean':
                return (bool) $value;
        
            case 'string':
                return (string) $value;
        
            case 'date-time':
            case 'string<date-time>':
                return (string) $value;
        
            case 'uri':
            case 'string<uri>':
                return (string) $value;
        
            case 'array':
                return (array) $value;
        
            case 'object':
                return (object) $value;
        
            default:
                return 'Error::Invalid_Data_Type';
        }
    }

    private function blankVal($type)
    {
        switch ($type) {
            case 'int':
            case 'integer':
            case 'number':
                return 0;
        
            case 'float':
            case 'double':
            case 'real':
                return 0.0;
        
            case 'bool':
            case 'boolean':
                return false;
        
            case 'string':
                return '';
        
            case 'date-time':
            case 'string<date-time>':
                return '0000-00-00T00:00:00Z';
        
            case 'uri':
            case 'string<uri>':
                return '';
        
            case 'array':
                return [];
        
            case 'object':
                return (object) [];
        
            default:
                return $type;
        }
    }

    /**
     * Load a schema from a file or array
     *
     * @param mixed $schema The schema to load
     * @return mixed The loaded schema or an error message if the schema is not found
     */
    private function load($schema)
    {
        if(is_string($schema) && !in_array($schema, ["int", "integer", "number", "float", "double", "real", "bool", "boolean", "string", "date-time", "string<date-time>", "uri", "string<uri>", "array", "object"])){

            if(file_exists(get_stylesheet_directory()."/inc/mvc/schemas/".$schema.".php")){
                $schema = require(__DIR__."/../schemas/".$schema.".php");
            }else{
                return "Error::Schema_Not_Found#".$schema;
            }

        }
        
        foreach($schema as $key=>$value){
            if(is_string($value) && str_contains($value, '#')){
                $schema[$key] = $this->load(explode("#",$value)[1]);
            }
            
            if(is_array($value)){
                $schema[$key] = $this->load($value);
            }
        }

        return $schema;
    }

    /**
     * Validates the provided data against the given schema.
     *
     * @param array $data The data to be validated.
     * @param array|string $schema The schema to validate against. Can be an array or a string representing a schema property.
     * @param array $filter Optional filter to override the schema.
     * 
     * @return array|bool The validated and type-cast data, or false if validation fails.
     */
    public function validate($data, $schema, $filter = [])
    {
        if($schema===false || ( isset($filter['schema']) && $filter['schema']===false )) return $data;

        $result = [];
        $schemaName = json_encode($schema);
        $schema = $this->load($schema);
        
        if(isset($filter['schema'])){
            $schema = $this->load($filter['schema']);
        }

        if(is_string($schema) && str_contains($schema, 'Error::')){
            return $schema;
        }
        
        
        if(is_array($data)){
            $assoc = array_keys($data) !== range(0, count($data) - 1);
        }else{
            die('Error::Invalid_Data_Not_Array - '.$schemaName);
        }

        if (!$assoc) {
            $count = count($data) - 1;
            while ($count > 0) {
                $schema[] = $schema[0];
                $count--;
            }
        }
        

        /*    
        if( is_array($data) && isset($data['count']) && isset($data['data']) && is_array($schema) && isset($schema['count']) && isset($schema['data']) ){
            $assoc = array_keys($data['data']) !== range(0, count($data['data']) - 1);
            if (!$assoc) {
                $count = count($data['data']) - 1;
                while ($count > 0) {
                    $schema['data'][] = $schema['data'][0];
                    $count--;
                }
            }
        }elseif(is_array($data)){
            $assoc = array_keys($data) !== range(0, count($data) - 1);
            if (!$assoc) {
                $count = count($data) - 1;
                while ($count > 0) {
                    $schema[] = $schema[0];
                    $count--;
                }
            }
        }else{
            die('Error::Invalid_Data_Not_Array - '.$schemaName);
        }
        */
        //reduce the schema to the same length as the data for parent lavel not sub level

        $data = $this->filterData($data, $schema);
        $data = $this->loadSubData($data);

        //Skip if validate is false
        if(( isset($filter['debug']) && $filter['debug']===true )){
            return isset($filter['key'])
                ? $this->keyFilter($filter['key'], $data)
                : $data;
        }

         //Debug
        /*if (str_contains($schemaName, 'Enrollment_Minimal')) {
            print_r($data);
            echo count($data['data']);
            print_r($schema);
            echo count($schema['data']);
            die;
        }*/
        
        
        //typecast the data
        foreach ($schema as $key => $value) {
            if (!array_key_exists($key, $data)) {
                $result[$key] = "Error::Key_Not_Found_In_Data";
                continue;
            }

            if(is_string($value) && str_contains($value, 'Error::')){
                $result[$key] = $value;
                continue;
            }
            
            if (is_array($value)) {
                if(!is_array($data[$key])){
                    $result[$key] = ($data[$key]===false) ? [] : "Error::Invalid_Data_Type_Not_Array";
                    continue;
                }
                $result[$key] = $this->validate($data[$key], $value);
            } else {
                $result[$key] = $this->typeCast($data[$key], $value);
            }
        }
        
        return isset($filter['key'])
                ? $this->keyFilter($filter['key'], $result)
                : $result;
    }

    private function keyFilter(string $path, $data) {
        
        $keys = explode('->', $path);
        $current = $data;
    
        foreach ($keys as $key) {
            if (is_array($current)) {
                // Determine if the current array is associative
                $isAssoc = array_keys($current) !== range(0, count($current) - 1);
    
                if ($isAssoc) {
                    // Associative array: directly access the key
                    if (array_key_exists($key, $current)) {
                        $current = $current[$key];
                    } else {
                        // Key does not exist
                        return null;
                    }
                } else {
                    // Indexed array: apply the key to each element
                    $current = array_map(function($item) use ($key) {
                        return is_array($item) && array_key_exists($key, $item) ? $item[$key] : null;
                    }, $current);
    
                    // Remove nulls (in case some items don't have the key)
                    $current = array_filter($current, function($value) {
                        return !is_null($value);
                    });
    
                    // Reindex the array
                    $current = array_values($current);
                }
            } else {
                // Current is not an array; cannot traverse further
                return null;
            }
        }
    
        return $current;

    }

    public function falseResponse($schema)
    {
        if($schema===false) return false;

        $result = [];

        $schema = $this->load($schema);
        
        //typecast the data
        foreach ($schema as $key => $value) {

            if (is_array($value)) {
                $result[$key] = $this->falseResponse($value, true);
            } else {
                $result[$key] = $this->blankVal($value);
            }
        }

        return $result;
    }

    function filterData($data, $schema, $level=0){

        if($level!==0){
            if(is_array($schema)){
                $assoc = array_keys($schema) !== range(0, count($schema) - 1);
            }else{
                die('Error::Invalid_Data_Not_Array_In_FilterData');
            }
    
            if (!$assoc) {
                $count = count($data) - 1;
                while ($count > 0) {
                    $schema[] = $schema[0];
                    $count--;
                }
            }
        }
        

        foreach ($data as $key => $val) {

            if (array_key_exists($key, $schema)) {
                // If both sides have arrays, recurse
                if (is_array($val)) {
                    if(!is_array($schema[$key]) ){
                        if($schema[$key]=='array' || $schema[$key]=='object'){
                            $data[$key] = $val;
                            continue;
                        }else{
                            $data[$key] = "Filter::Invalid_Data_Type_Not_Array";
                            continue;
                        }
                    }
                    $data[$key] = $this->filterData($val, $schema[$key],$level+1);
                } else {
                    // Otherwise, override the schema's value with data's value
                    $data[$key] = $val;
                }
            }else{
                unset($data[$key]);
            }
        }

        return $data;
    }

    function loadSubData($data)
    {
        // If $data is not an array, check if it's a JSON string
        if (!is_array($data)) {
            if (is_string($data)) {
                $decoded = json_decode($data, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Return the decoded JSON as an array
                    if(isset($decoded['loadSubData'])){
                        //"loadSubData" => true,
                        //"modelClass" => $modelClass,
                        //"modelMethod" => $modelMethod,
                        //"queryParam" => $query,
                        //"filterParam" => $filter
                        //"noResponse" => $noResponse
                        global $YC;
                        
                        $loadedResponse = $YC->loadModel($decoded['modelClass'])->{$decoded['modelMethod']}($decoded['queryParam'], $decoded['filterParam']);

                        if($loadedResponse===false || $loadedResponse===null || empty($loadedResponse)){

                            if(isset($decoded['filterParam']['noResponse'])){
                                
                                if($decoded['filterParam']['noResponse']===true){
                                    if(isset($decoded['filterParam']['schema'])){
                                        $schemaName = str_contains($decoded['filterParam']['schema'], '#') ? explode('#',$decoded['filterParam']['schema'])[1] : $decoded['filterParam']['schema'];
                                        $result = $this->falseResponse($schemaName);
                                        
                                        return $result;
                                    }
                                }
                                
                                $result = $this->falseResponse($decoded['filterParam']['noResponse']);
                                
                                return $result;
                            }
                        }
                            
                        return $loadedResponse;
                    }
                }
            }
            // Return the original value if not a valid JSON string
            return $data;
        }

        // If $data is an array, loop through each element
        foreach ($data as $key => $value) {
            // Recursively process each value
            $data[$key] = $this->loadSubData($value);
        }

        return $data;
    }
}
