{"took": 2, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 354, "relation": "eq"}, "max_score": 1.0, "hits": [{"_index": "course", "_type": "_doc", "_id": "RiRAr5IB1ihz2ZolGEGg", "_score": 1.0, "_ignored": ["data.details.description.keyword"], "_source": {"data": {"details": {"record_id": 28104, "update_event_type": "course", "resource_type": "course", "title": "IELTS Preparation Course", "excerpt": "A comprehensive 4-week course designed to effectively increase your IELTS score, provided by the leading IELTS coaching institute in the northern region of India.", "description": "In this IELTS Preparation Course, we aim to help students increase their IELTS scores in just four weeks with 24 hours of live instruction. Our coaching institute has a decade of experience, and we have helped thousands of students achieve their IELTS goals. We apply a unique teaching methodology and content that sets us apart from our competitors, delivering an extremely high success rate.", "unit_price": 0, "live_classes": 0, "instructor_hours": 0, "published_at": "Oct 21, 2024 6:54 PM"}, "@timestamp": "2024-10-21 18:54:26"}}}, {"_index": "course", "_type": "_doc", "_id": "course-28046", "_score": 1.0, "_ignored": ["data.details.post_description.keyword", "data.details.product_description.keyword"], "_source": {"data": {"details": {"parent_taxonomy": "french", "is_enable": true, "modules_covered": "55", "schedule_content": "1", "group_price": "4555", "taxonomy": ["5590"], "group_type": ["1-Many"], "type": "Youtube", "media_url": "https://www.youtube.com/embed/1CdYFoJYHu8", "per_class_price": "0", "product_order": "0", "id": "0", "product_description": "Come join our exciting 5 day French workshop designed for kids aged 7 to10. Over the course of five days, children will dive into the basics of the French language and culture through the help of live online classes. From learning how to say hello to counting to 20, discovering the names of animals, and exploring classroom words, each day will be filled with interactive activities and fun learning experiences. By the end of the workshop, kids will have gained confidence in greeting others in French, counting, identifying animals, and using French words in a classroom setting. This free workshop is a fantastic opportunity for children to have fun while beginning their journey into the French language.", "duration_weeks": 8, "segment_english_speaking": "", "price_month_9": "0", "price_month_8": "0", "price_month_7": "0", "price_month_6": "0", "price_month_5": "0", "resource_type": "course", "product_name": "Bonjour French Fluency Workshop", "mapped_instructor_count": "0", "academies": [31189], "instructor_hours": "5", "user_id": "2", "featuredImage": "", "org_id": "31185", "cancellation_policy": "In case students don't like the classes, they can get a full refund anytime during the first 3 days of starting their enrollment.", "excerpt": "Come join our exciting 5 day French workshop designed for kids aged 7 to 10", "featured_course": "0", "writing_assignments": "", "short_description": "", "mock_tests": "", "update_event_type": "course", "description": "", "cognitive_level": "", "post_description": "Come join our exciting 5 day French workshop designed for kids aged 7 to10. Over the course of five days, children will dive into the basics of the French language and culture through the help of live online classes. From learning how to say hello to counting to 20, discovering the names of animals, and exploring classroom words, each day will be filled with interactive activities and fun learning experiences. By the end of the workshop, kids will have gained confidence in greeting others in French, counting, identifying animals, and using French words in a classroom setting. This free workshop is a fantastic opportunity for children to have fun while beginning their journey into the French language.", "title": "Bonjour French Fluency Workshop", "product_code": "FRENCH-FLUENCY-WOR<PERSON><PERSON>OP", "is_course_have_levels": "0", "highlight_2": "", "highlight_1": "", "policy_for_instructors": "", "duration_months": "0", "price_month_4": "0", "highlight_8": "", "price_month_3": "0", "highlight_7": "", "live_classes": "5", "price_month_2": "0", "price_month_1": "0", "highlight_9": "", "highlight_4": "", "highlight_3": "", "highlight_6": "", "highlight_5": "", "age_limit": "", "enrollment_type": "fixed", "one_to_one_price": "3333", "any_time": "0", "unit_price": "4555", "url": "https://dev.yunolearning.com/french/courses/bonjour-french-fluency-workshop/", "max_seats": "10", "duration_days": "0", "highlight_10": "", "price_month_12": "0", "speaking_sessions": "", "price_month_10": "0", "price_month_11": "0"}}}}, {"_index": "course", "_type": "_doc", "_id": "course-33856", "_score": 1.0, "_ignored": ["data.details.description.keyword"], "_source": {"data": {"details": {"record_id": 33856, "update_event_type": "course", "resource_type": "course", "title": "Ace the IELTS in 3 Weeks! Fast-track Your IELTS Success Today!", "excerpt": "Optimally designed 3-week IELTS prep course. Stand out from the crowd by excelling in the IELTS exam.", "description": "Join our intensive, fast-tracked IELTS preparation course to sharpen your skills in English proficiency. Our course is structured into 3 weeks, with flexible live class hours curated specifically for working individuals and students. The ‘Ace the IELTS in 3 weeks!’ course is uniquely designed with a focus on maximizing your IELTS test scores. Each live class spans a student-friendly duration, ensuring a full understanding of the material before moving on to the next topic.\n\nWith our course, we strive to create a learning environment that supports various learning styles and needs. We integrate a mixture of real-time interaction and independent study approaches into our class structure. The course content is curated by expert instructors who have years of experience in teaching and understand the dynamics of the IELTS exam.\n\nBy the end of this course, you will be familiar with the actual test format, and you will have practiced the strategies and techniques needed to complete the exam on time. Our trainers are dedicated to helping you understand the test structure and teaching you effective test-taking strategies.\n\nWe stand out from other IELTS prep courses due to our commitment to individual student success. We believe that every student’s language learning journey is unique. Hence, our approach to teaching the IELTS prep course is student-centered. Our course content addresses specific areas of the IELTS test—listening, reading, writing, and speaking—providing comprehensive coverage and efficient test-taking strategies.\n\nJoin our community of learners at the Ace the IELTS in 3 weeks! course to fast-track your IELTS success in an engaging, supportive, and flexible learning environment.", "unit_price": 0, "live_classes": 0, "instructor_hours": 0, "enrollment_type": null, "group_type": [null], "published_at": "Apr 28, 2025 6:02 PM", "active_enrollments": "0", "active_batches": "0", "ongoing_batches": "0", "successful_students": "0"}, "@timestamp": "2025-04-28 18:02:46"}}}, {"_index": "course", "_type": "_doc", "_id": "course-33266", "_score": 1.0, "_ignored": ["data.details.description.keyword"], "_source": {"data": {"details": {"record_id": 33266, "update_event_type": "course", "resource_type": "course", "title": "Master the French Language: A Comprehensive Introduction", "excerpt": "Embark on a language journey. Add fluency in French to your skills with our comprehensive course of 3 weeks!", "description": "If you are looking to boost your language skills and add another feather to your cap, this course is for you. 'Master the French Language: A Comprehensive Introduction’ is a tailor-made course designed to give you confidence in French language communication skills. It guarantees an immersive, enjoyable, and intellectually stimulating French-learning experience.\n\nWe welcome students of all abilities, whether you are an absolute beginner or someone looking to sharpen up your existing French skills. Rather than complex jargon, we use plain English to explain all the concepts. This is a three-week instructor-led course, ensuring plenty of one-on-one interaction with the instructor.\n\nOur distinctive approach will ensure you not just learn French, but also connect with its rich history and culture. Learning a language requires a strong understanding of its roots and we make sure you are well-equipped in that regard. Each lesson is crafted carefully keeping your learning potential in mind. Moreover, we have plenty of activities, exercises, quizzes, and homework to reinforce your learning.\n\nBesides the language skills, you will also develop your critical thinking abilities, problem-solving skills, and enhance your global awareness making you a well-rounded individual. This course does not just cater to your professional development but your personal growth as well. There is no prerequisite for the course. All we ask of you is dedication and commitment.\n\nIn three weeks, you will not just read, write and speak in French, but also appreciate the beauty of this romantic language, the culture it represents and the people who speak it daily.", "unit_price": 0, "live_classes": 0, "instructor_hours": 0, "enrollment_type": null, "group_type": [null], "published_at": "Mar 28, 2025 5:50 PM", "active_enrollments": "0", "active_batches": "0", "ongoing_batches": "0", "successful_students": "0"}, "@timestamp": "2025-03-28 17:50:31"}}}, {"_index": "course", "_type": "_doc", "_id": "course-33317", "_score": 1.0, "_ignored": ["data.details.description.keyword"], "_source": {"data": {"details": {"record_id": 33317, "update_event_type": "course", "resource_type": "course", "title": "Master the Art of French Language: A Comprehensive 3-Week Course", "excerpt": "Dive into French culture, accent, and semantics with our immersive 3-week course, and learn French like a native!", "description": "This comprehensive, instructor-led course is your one-stop solution to mastering the intricacies of the French language. In just three weeks, you'll be exposed to intensive training with the aim of making you proficient in French.\n\nWhy should you choose this course? Because we believe in making learning French an enjoyable and engaging experience. Our team of expert educators strategically structures classes to cover all aspects of learning — from French grammar and vocabulary to pronunciation and sentence construction.\n\nWe understand that each learner's needs and pace of learning differs. That's why our course, despite being instructor-led, is tailored to match individual learning styles. Each class is interactive, with real-time feedback from instructors that helps participants learn and improve rapidly.\n\nOur innovative use of technology sets us apart from competitors. The course features video modules, interactive quizzes, exercises, and follow-up homework that together complement live classes, ensuring comprehensive learning. Our study material is regularly updated, keeping in line with current language trends and norms. \n\nThe three-week duration of the course is meticulously divided into manageable modules. Each week focuses on building specific skills in a systematic and gradual manner, enabling students to get a solid grasp of the language.\n\nWhat makes this course unique? The customization, attention to detail, innovative techniques, and our commitment to making you proficient in French.", "unit_price": 0, "live_classes": 0, "instructor_hours": 0, "enrollment_type": null, "group_type": [null], "published_at": "Mar 31, 2025 6:12 PM", "active_enrollments": "0", "active_batches": "0", "ongoing_batches": "0", "successful_students": "0"}, "@timestamp": "2025-03-31 18:12:07"}}}, {"_index": "course", "_type": "_doc", "_id": "course-8848", "_score": 1.0, "_ignored": ["data.details.mapped_instructors.about.keyword", "data.details.post_description.keyword", "data.details.product_description.keyword", "data.details.description.keyword"], "_source": {"data": {"@timestamp": "2023-02-28 11:08:09", "details": {"batch_detail_ids": [875, 877, 882, 898, 901, 956, 966, 1052, 1536, 1542, 1543, 1544, 1561, 1563, 2586, 2587, 2594, 2636, 2656, 2748, 2817, 2828, 2837, 2838, 2858, 2903, 2971, 2972], "parent_taxonomy": "english-speaking", "is_enable": true, "modules_covered": "", "batch_details": [{"publisher_id": 14167, "zoho_batch_id": 67701000017473002, "instructor_profile_url": false, "batch_id": 2586, "enrollment_start_date": null, "personalisation": 1, "batch_deleted_status": 0, "class_end_time": "12:00:00", "duration": 60, "days_of_week": ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "0", "batch_post_id": 28486, "instructor_completed_enrollment": 0, "image": "", "course_id": 8848, "batch_display_start_date": "2024-12-27 12:15:10", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/english-speakin-classes/", "any_time": 1, "term_id": 5648, "last_enrollment_end_date": null, "batch_start_date": "2024-07-11 11:00:00", "max_seats": 1, "batch_name": "2586-english speakin classes", "user_id": 14167, "batch_display_end_date": "2025-06-13 12:15:10", "class_start_time": "11:00:00", "instructor_id": 14168, "instructor_name": "", "time": "11:00:00", "batch_title": "english speakin classes", "vacancy": 1, "batch_end_date": "2024-12-26 11:00:00"}, {"publisher_id": 14167, "zoho_batch_id": 67701000017474002, "instructor_profile_url": false, "batch_id": 2587, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "12:00:00", "duration": 60, "days_of_week": ["Mon", "Wed", "<PERSON><PERSON>", "<PERSON>hu", "<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "0", "batch_post_id": 28487, "instructor_completed_enrollment": 0, "image": "", "course_id": 8848, "batch_display_start_date": "2024-12-28 12:15:07", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/lumber-1-batch/", "any_time": 1, "term_id": 5649, "last_enrollment_end_date": "2024-12-25 23:59:59", "batch_start_date": "2024-07-12 11:00:00", "max_seats": 8, "batch_name": "2587-lumber 1 batch", "user_id": 14167, "batch_display_end_date": "2025-06-14 12:15:07", "class_start_time": "11:00:00", "instructor_id": 14168, "instructor_name": "", "time": "11:00:00", "batch_title": "lumber 1 batch", "vacancy": 8, "batch_end_date": "2024-12-27 11:00:00"}, {"publisher_id": 14167, "zoho_batch_id": 67701000017473127, "instructor_profile_url": false, "batch_id": 2594, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:23:00", "duration": 30, "days_of_week": ["Mon", "Wed", "<PERSON><PERSON>", "Sat"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "0", "batch_post_id": 28518, "instructor_completed_enrollment": 0, "image": "", "course_id": 8848, "batch_display_start_date": "2025-01-05 12:10:10", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/free-enrol/", "any_time": 1, "term_id": 5656, "last_enrollment_end_date": "2024-12-26 23:59:59", "batch_start_date": "2024-07-20 15:53:00", "max_seats": 8, "batch_name": "2594-free enrol", "user_id": 14167, "batch_display_end_date": "2025-06-22 12:10:10", "class_start_time": "15:53:00", "instructor_id": 14168, "instructor_name": "", "time": "15:53:00", "batch_title": "free enrol", "vacancy": 8, "batch_end_date": "2025-01-04 15:53:00"}, {"publisher_id": 14237, "zoho_batch_id": 67701000017736077, "instructor_profile_url": false, "batch_id": 2636, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:04:00", "duration": 60, "days_of_week": ["<PERSON><PERSON>", "Wed", "Mon"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "0", "batch_post_id": 28814, "instructor_completed_enrollment": 0, "image": "", "course_id": 8848, "batch_display_start_date": "2025-01-15 12:24:30", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/this-is-english-batch/", "any_time": 1, "term_id": 5699, "last_enrollment_end_date": "2025-01-13 23:59:59", "batch_start_date": "2024-07-30 15:04:00", "max_seats": 8, "batch_name": "2636-this is english batch", "user_id": 14237, "batch_display_end_date": "2025-07-02 12:24:30", "class_start_time": "15:04:00", "instructor_id": 14240, "instructor_name": "", "time": "15:04:00", "batch_title": "this is english batch", "vacancy": 8, "batch_end_date": "2025-01-14 15:04:00"}, {"publisher_id": 14216, "zoho_batch_id": 67701000017848080, "instructor_profile_url": false, "batch_id": 2656, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:00:00", "duration": 75, "days_of_week": ["Wed", "Mon", "<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "0", "batch_post_id": 29061, "instructor_completed_enrollment": 0, "image": "", "course_id": 8848, "batch_display_start_date": "2025-01-18 12:10:12", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/adv-teenagers/", "any_time": 1, "term_id": 5719, "last_enrollment_end_date": "2025-01-17 23:59:59", "batch_start_date": "2024-08-02 14:45:00", "max_seats": 8, "batch_name": "2656-adv teenagers", "user_id": 14216, "batch_display_end_date": "2025-07-05 12:10:12", "class_start_time": "14:45:00", "instructor_id": 14262, "instructor_name": "", "time": "14:45:00", "batch_title": "adv teenagers", "vacancy": 8, "batch_end_date": "2025-01-17 14:45:00"}, {"publisher_id": 14241, "zoho_batch_id": 67701000018325042, "instructor_profile_url": false, "batch_id": 2748, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "17:53:00", "duration": 30, "days_of_week": ["<PERSON>hu", "Mon", "<PERSON><PERSON>", "<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "0", "batch_post_id": 29474, "instructor_completed_enrollment": 1, "image": "", "course_id": 8848, "batch_display_start_date": "2025-02-20 12:30:07", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/test-batch-group/", "any_time": 1, "term_id": 5812, "last_enrollment_end_date": "2025-02-19 23:59:59", "batch_start_date": "2024-09-04 17:23:00", "max_seats": 9, "batch_name": "BT-2748-2024-09-04-17:23", "user_id": 14241, "batch_display_end_date": "2025-08-07 12:30:07", "class_start_time": "17:23:00", "instructor_id": 14462, "instructor_name": "", "time": "17:23:00", "batch_title": "test batch group neww", "vacancy": 9, "batch_end_date": "2025-02-19 17:23:00"}, {"publisher_id": 14558, "zoho_batch_id": 67701000018871827, "instructor_profile_url": "https://local.yunolearning.com/profile/xorra/", "batch_id": 2817, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:00:00", "duration": 60, "days_of_week": ["Mon", "<PERSON>hu"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "4.7", "batch_post_id": 30652, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-04-17 12:38:16", "active_batch": 0, "batch_post_url": "https://local.yunolearning.com/batch/new-batch-10/", "any_time": 1, "term_id": 5891, "last_enrollment_end_date": null, "batch_start_date": "2024-10-30 15:00:00", "max_seats": 9, "batch_name": "2817-new batch", "user_id": 14558, "batch_display_end_date": "2025-10-02 12:38:16", "class_start_time": "15:00:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "15:00:00", "batch_title": "new batch", "vacancy": 9, "batch_end_date": "2025-04-16 15:00:00"}, {"publisher_id": 13754, "zoho_batch_id": 67701000019197144, "instructor_profile_url": "https://dev.yunolearning.com/profile/xorra/", "batch_id": 2828, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "11:54:00", "duration": 15, "days_of_week": ["<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "4.7", "batch_post_id": 30826, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-04-24 12:10:08", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/test-61/", "any_time": 1, "term_id": 5902, "last_enrollment_end_date": null, "batch_start_date": "2024-11-06 11:39:00", "max_seats": 9, "batch_name": "2828-test", "user_id": 13754, "batch_display_end_date": "2025-10-09 12:10:08", "class_start_time": "11:39:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "11:39:00", "batch_title": "test", "vacancy": 9, "batch_end_date": "2025-04-23 11:39:00"}, {"publisher_id": 13754, "zoho_batch_id": 67701000019389355, "instructor_profile_url": "https://dev.yunolearning.com/profile/xorra/", "batch_id": 2838, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "09:00:00", "duration": 30, "days_of_week": ["Sun"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "4.7", "batch_post_id": 31074, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-05-14 12:00:07", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/true/", "any_time": 1, "term_id": 5912, "last_enrollment_end_date": null, "batch_start_date": "2024-11-26 08:30:00", "max_seats": 9, "batch_name": "2838-true", "user_id": 13754, "batch_display_end_date": "2025-10-29 12:00:07", "class_start_time": "08:30:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "08:30:00", "batch_title": "true", "vacancy": 9, "batch_end_date": "2025-05-13 08:30:00"}, {"publisher_id": 15213, "zoho_batch_id": 0, "instructor_profile_url": "https://dev.yunolearning.com/profile/xorra/", "batch_id": 2972, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:11:00", "duration": 15, "days_of_week": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "4.7", "batch_post_id": 33539, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-05-20 11:50:05", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/bug-3/", "any_time": 1, "term_id": 6095, "last_enrollment_end_date": null, "batch_start_date": "2025-04-16 15:56:00", "max_seats": 9, "batch_name": "2972-bug", "user_id": 15213, "batch_display_end_date": "2025-11-04 11:50:05", "class_start_time": "15:56:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "15:56:00", "batch_title": "bug", "vacancy": 9, "batch_end_date": "2025-10-01 15:56:00"}, {"publisher_id": 15213, "zoho_batch_id": 0, "instructor_profile_url": "https://dev.yunolearning.com/profile/xorra/", "batch_id": 2971, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "16:09:00", "duration": 15, "days_of_week": ["Mon", "<PERSON>hu"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "4.7", "batch_post_id": 33538, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-05-20 11:50:08", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/56456/", "any_time": 1, "term_id": 6094, "last_enrollment_end_date": null, "batch_start_date": "2025-04-16 15:54:00", "max_seats": 9, "batch_name": "2971-56456", "user_id": 15213, "batch_display_end_date": "2025-11-04 11:50:08", "class_start_time": "15:54:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "15:54:00", "batch_title": "56456", "vacancy": 9, "batch_end_date": "2025-10-01 15:54:00"}, {"publisher_id": 13754, "zoho_batch_id": 67701000020344080, "instructor_profile_url": false, "batch_id": 2903, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "15:39:00", "duration": 15, "days_of_week": ["<PERSON><PERSON>"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "evening", "instructor_rating": "0", "batch_post_id": 32053, "instructor_completed_enrollment": 4, "image": "", "course_id": 8848, "batch_display_start_date": "2025-05-20 11:55:03", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/test-76/", "any_time": 1, "term_id": 5989, "last_enrollment_end_date": null, "batch_start_date": "2025-01-24 15:24:00", "max_seats": 9, "batch_name": "2903-test", "user_id": 13754, "batch_display_end_date": "2025-11-04 11:55:03", "class_start_time": "15:24:00", "instructor_id": 14745, "instructor_name": "", "time": "15:24:00", "batch_title": "test", "vacancy": 9, "batch_end_date": "2025-07-11 15:24:00"}, {"publisher_id": 14740, "zoho_batch_id": 67701000019602312, "instructor_profile_url": false, "batch_id": 2858, "enrollment_start_date": "2024-12-03 06:44:25", "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "12:29:00", "duration": 30, "days_of_week": ["Mon", "<PERSON>hu"], "mode": 0, "enrollment_counter": 1, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "0", "batch_post_id": 31211, "instructor_completed_enrollment": 4, "image": "", "course_id": 8848, "batch_display_start_date": "2025-05-20 11:55:05", "active_batch": 1, "batch_post_url": "https://dev.yunolearning.com/batch/new-batch-14/", "any_time": 1, "term_id": 5932, "last_enrollment_end_date": "2025-05-20 23:59:59", "batch_start_date": "2024-12-06 11:59:00", "max_seats": 9, "batch_name": "2858-new batch", "user_id": 14740, "batch_display_end_date": "2025-11-04 11:55:05", "class_start_time": "11:59:00", "instructor_id": 14745, "instructor_name": "", "time": "11:59:00", "batch_title": "new batch", "vacancy": 8, "batch_end_date": "2025-05-23 11:59:00"}, {"publisher_id": 13754, "zoho_batch_id": 67701000019400339, "instructor_profile_url": "https://dev.yunolearning.com/profile/xorra/", "batch_id": 2837, "enrollment_start_date": null, "personalisation": 0, "batch_deleted_status": 0, "class_end_time": "10:10:00", "duration": 120, "days_of_week": ["Mon", "<PERSON>hu"], "mode": 0, "enrollment_counter": 0, "ongoing_batch": 0, "enrollable_status": 0, "batch_visibility": 0, "locked_batch": 0, "price": 4100, "batch_type": "morning", "instructor_rating": "4.7", "batch_post_id": 31071, "instructor_completed_enrollment": 207, "image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "course_id": 8848, "batch_display_start_date": "2025-05-20 11:55:07", "active_batch": 0, "batch_post_url": "https://dev.yunolearning.com/batch/new-batch-13/", "any_time": 1, "term_id": 5911, "last_enrollment_end_date": "2025-05-14 23:59:59", "batch_start_date": "2024-12-10 08:10:00", "max_seats": 9, "batch_name": "BT-2837-2024-12-10-08:10", "user_id": 13754, "batch_display_end_date": "2025-11-04 11:55:07", "class_start_time": "08:10:00", "instructor_id": 6565, "instructor_name": "Yunotestxornor 04", "time": "08:10:00", "batch_title": "last class", "vacancy": 9, "batch_end_date": "2025-05-27 08:10:00"}], "schedule_content": "1", "group_price": "5100", "taxonomy": ["3086", "3085", "3084", "3821", "3819", "3814", "3815"], "group_type": ["1-1", "1-Many"], "active_batches": "1", "type": "Image", "course_economics": [{"one_to_one_class_90_min_duartion": "0", "group_class_75_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "6", "group_class_120_min_duartion": "0", "one_to_one_class_60_min_duartion": "0", "personalization": "1-Many", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "", "price": "", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "group_class_180_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "7", "id": 31075, "number_of_assignments_per_student_that_require_correction": "5", "group_class_45_min_duartion": "0", "published_at": "Nov 22, 2024 5:59 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 8848, "duration_diagnostic_test": "60", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "group_class_30_min_duartion": "0", "group_class_60_min_duartion": "6", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "0", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-92/", "time_investment_per_student_on_post_test": "45", "group_class_150_min_duartion": "1", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "2", "group_class_90_min_duartion": "0"}], "mapped_instructor_ids": [6565, 14745], "media_url": "https://www.youtube.com/embed/vw_Cv_BUvdA?si=cBOPjtW2KInJgAEv", "uuid": "63fd9341b65f90.91936474", "per_class_price": "150", "event_type": "course", "event_date": "2023-02-28 11:08:09", "product_order": "1", "id": "67701000002006774", "product_description": "This is an advanced level course for children between the age group of 1013 years The course is designed keeping in mind that you have a good understanding of the language and can hold a conversation But it is time you move to an advanced level of English langage with higher level vocabulary and complex sentence formation All you need is a good internet connection and a laptop desktop or tablet You can also use your mobile phone but your experience might vary depending on the screen size This course promises Enhanced vocabulary Ability to debate on various topics Ability to discuss different topics with others Ability to speak extempore on different topics Fluency in speaking Public speaking", "published_at": "Oct 6, 2020 6:21 PM", "duration_weeks": 24, "segment_english_speaking": "Preteens", "price_month_9": "0", "price_month_8": "0", "price_month_7": "0", "price_month_6": "0", "price_month_5": "0", "resource_type": "course", "product_name": "Advanced Program for English Speaking & Communication – for Preteens", "mapped_instructor_count": "2", "course_schedule_id": 19059, "instructor_hours": "36", "mapped_instructors": [{"image": "https://lh3.googleusercontent.com/a/ACg8ocLoIGxgKO-b-7wcRBLYelEVFlzAJieTrvtNpt1PSvQ4cRTmpqXt=s96-c", "country": "India", "address": "INR", "profile_url": "https://dev.yunolearning.com/profile/xorra/", "city": "<PERSON><PERSON><PERSON><PERSON>", "about": "<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. <PERSON><PERSON>a consequat massa quis enim.</p><p><PERSON>c pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus.</p>", "fluent_languages": "Azerbaijani", "name": "Yunotestxornor 04", "name_email": "Yunotestxornor 04 (<EMAIL>)", "understand_languages": "English,Hindi,Punjabi", "id": 6565, "state": "Punjab", "email": "<EMAIL>"}, {"image": "https://lh3.googleusercontent.com/a/ACg8ocLw5nnAwH4ga6YuksrDfoL-hn7z49dcJyDU192GIvX-vHC88w=s96-c", "country": "India", "address": "", "profile_url": "https://dev.yunolearning.com/profile/shivam-dogra-5/", "city": "Chandigarh", "about": "", "fluent_languages": "Punjabi", "name": "shivam dogra", "name_email": "<PERSON><PERSON> (<EMAIL>)", "understand_languages": "English", "id": 14745, "state": "Chandigarh", "email": "<EMAIL>"}], "user_id": "2", "featuredImage": "https://dev.yunolearning.com/wp-content/uploads/2024/07/161_blog.jpg", "org_id": "18630", "course_schedule": [{"duration": "45", "sub_cat": [{"sub_cat": [{"name": "Advanced", "id": 3086, "slug": "advanced"}], "name": "Cognitive Level", "id": 3085, "slug": "cognitive-level-english-speaking"}, {"sub_cat": [{"name": "Listening", "id": 3816, "slug": "listening-skills"}], "name": "Skills", "id": 3814, "slug": "skills"}, {"sub_cat": [{"name": "Preteens", "id": 3821, "slug": "preteens"}], "name": "Segment", "id": 3819, "slug": "segment"}], "is_active": false, "activity": {"label": "Live Class", "slug": "live_class"}, "description": "test description", "is_remove": false, "id": "live_class-0", "title": "new live class", "excerpt": "", "order": 0}, {"duration": "", "sub_cat": [{"sub_cat": [], "name": "Cognitive Level", "id": 3085, "slug": "cognitive-level-english-speaking", "selected": ""}, {"sub_cat": [], "name": "Skills", "id": 3814, "slug": "skills", "selected": ""}, {"sub_cat": [], "name": "Segment", "id": 3819, "slug": "segment", "selected": ""}], "is_active": false, "activity": {"label": "Assignment", "slug": "assignment"}, "description": "test desccc", "is_remove": false, "id": "assignment-1", "title": "new assignment", "excerpt": "", "order": 1}, {"duration": "", "sub_cat": [{"sub_cat": [], "name": "Cognitive Level", "id": 3085, "slug": "cognitive-level-english-speaking", "selected": ""}, {"sub_cat": [], "name": "Skills", "id": 3814, "slug": "skills", "selected": ""}, {"sub_cat": [], "name": "Segment", "id": 3819, "slug": "segment", "selected": ""}], "is_active": false, "activity": {"label": "<PERSON><PERSON>", "slug": "mock_test"}, "description": "old description", "is_remove": false, "id": "mock_test-2", "title": "mock tests", "excerpt": "", "order": 2}], "cancellation_policy": "testt", "excerpt": "no course", "course_category": [{"sub_category": [{"sub_category": [{"sub_category": [], "parent_id": 3085, "name": "Advanced", "id": 3086, "slug": "advanced"}], "parent_id": 3084, "name": "Cognitive Level", "id": 3085, "slug": "cognitive-level-english-speaking"}, {"sub_category": [{"sub_category": [], "parent_id": 3819, "name": "Preteens", "id": 3821, "slug": "preteens"}], "parent_id": 3084, "name": "Segment", "id": 3819, "slug": "segment"}, {"sub_category": [{"sub_category": [], "parent_id": 3814, "name": "Speaking", "id": 3815, "slug": "speaking-skills"}], "parent_id": 3084, "name": "Skills", "id": 3814, "slug": "skills"}], "parent_id": 0, "name": "English Speaking", "id": 3084, "slug": "english-speaking"}], "featured_course": "0", "writing_assignments": "", "short_description": "", "mock_tests": "", "successful_students": "0", "update_event_type": "course", "description": "This is an advanced level course for children between the age group of 1013 years The course is designed keeping in mind that you have a good understanding of the language and can hold a conversation But it is time you move to an advanced level of English langage with higher level vocabulary and complex sentence formation All you need is a good internet connection and a laptop desktop or tablet You can also use your mobile phone but your experience might vary depending on the screen size This course promises Enhanced vocabulary Ability to debate on various topics Ability to discuss different topics with others Ability to speak extempore on different topics Fluency in speaking Public speaking", "cognitive_level": "Advanced", "post_description": "This is an advanced level course for children between the age group of 1013 years The course is designed keeping in mind that you have a good understanding of the language and can hold a conversation But it is time you move to an advanced level of English langage with higher level vocabulary and complex sentence formation All you need is a good internet connection and a laptop desktop or tablet You can also use your mobile phone but your experience might vary depending on the screen size This course promises \r\nEnhanced vocabulary\r\nAbility to debate on various topics\r\nAbility to discuss different topics with others\r\nAbility to speak extempore on different topics\r\nFluency in speaking\r\nPublic speaking\r\n", "title": "Advanced Program for English Speaking & Communication –test for teens", "product_code": "ENGLISH-FOR-PRETEENS-ADVANCED", "is_course_have_levels": "0", "highlight_2": "Debate on various topics", "highlight_1": "Enhance vocabulary", "policy_for_instructors": "no scolding", "duration_months": "6", "price_month_4": "0", "highlight_8": "", "price_month_3": "0", "highlight_7": "", "live_classes": "36", "price_month_2": "0", "price_month_1": "0", "highlight_9": "", "highlight_4": "Speak extempore on different topics", "highlight_3": "Discuss different topics with others", "highlight_6": "", "course_economics_one_to_one": [{"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "2", "one_to_one_class_60_min_duartion": "2", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 20510, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Mar 11, 2023 1:48 AM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 20503, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-42/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Mar 2, 2023 10:53 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/test-course-new-dev/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Jan 10, 2023 3:18 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/test-course-vedic/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Jan 9, 2023 6:11 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/dev-test-course-ielts/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "5", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "1", "id": 20593, "number_of_assignments_per_student_that_require_correction": "1", "published_at": "Mar 14, 2023 11:09 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 20066, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-44/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "0", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "0", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "0", "id": 20283, "number_of_assignments_per_student_that_require_correction": "0", "published_at": "Feb 10, 2023 6:48 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "0", "course_id": 19254, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "0", "instructor_effort_per_student_on_diagnostic_test_correction": "45", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-36/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "0", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "60", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "1", "is_there_a_post_test": "1", "number_of_mock_exams": "15", "one_to_one_class_60_min_duartion": "1", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "15", "one_to_one_class_45_min_duartion": "1", "number_of_assignments_per_student_that_not_require_correction": "15", "id": 19432, "number_of_assignments_per_student_that_require_correction": "15", "published_at": "Nov 15, 2022 12:53 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "15", "course_id": 19099, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "1", "instructor_effort_per_student_on_mock_exam_correction": "15", "instructor_effort_per_student_on_diagnostic_test_correction": "15", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-4/", "time_investment_per_student_on_post_test": "15", "time_investment_per_student_on_assignment_that_require_correction": "15", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "15", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "1"}, {"one_to_one_class_90_min_duartion": "2", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "2", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "1", "number_of_assignments_per_student_that_not_require_correction": "3", "id": 19821, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Dec 9, 2022 5:17 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "60", "course_id": 19081, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "1", "instructor_effort_per_student_on_mock_exam_correction": "15", "instructor_effort_per_student_on_diagnostic_test_correction": "0", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-10/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "120", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "15", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "3"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "0", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "0", "id": 19850, "number_of_assignments_per_student_that_require_correction": "0", "published_at": "Dec 14, 2022 1:10 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "0", "course_id": 18980, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-20/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "0", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "0", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "5"}, {"one_to_one_class_90_min_duartion": "1", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "60", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "1", "id": 19823, "number_of_assignments_per_student_that_require_correction": "1", "published_at": "Dec 9, 2022 5:28 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 17913, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "15", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-12/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "15", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "1", "id": 19928, "number_of_assignments_per_student_that_require_correction": "1", "published_at": "Dec 19, 2022 3:23 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 17447, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "2", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-28/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "0", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "0", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "0", "id": 20284, "number_of_assignments_per_student_that_require_correction": "0", "published_at": "Feb 10, 2023 6:51 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "0", "course_id": 13417, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "0", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-37/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "0", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "45", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "60", "is_there_a_post_test": "", "number_of_mock_exams": "15", "one_to_one_class_60_min_duartion": "90", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "60", "one_to_one_class_45_min_duartion": "75", "number_of_assignments_per_student_that_not_require_correction": "10", "id": 19809, "number_of_assignments_per_student_that_require_correction": "10", "published_at": "Dec 8, 2022 1:01 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "3", "course_id": 11646, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "90", "instructor_effort_per_student_on_mock_exam_correction": "75", "instructor_effort_per_student_on_diagnostic_test_correction": "90", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-6/", "time_investment_per_student_on_post_test": "75", "time_investment_per_student_on_assignment_that_require_correction": "2", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "60", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "60"}, {"one_to_one_class_90_min_duartion": "4", "is_there_a_post_test": "", "number_of_mock_exams": "0", "one_to_one_class_60_min_duartion": "10", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "0", "one_to_one_class_45_min_duartion": "6", "number_of_assignments_per_student_that_not_require_correction": "0", "id": 19841, "number_of_assignments_per_student_that_require_correction": "0", "published_at": "Dec 13, 2022 5:53 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "0", "course_id": 10706, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "1", "instructor_effort_per_student_on_mock_exam_correction": "0", "instructor_effort_per_student_on_diagnostic_test_correction": "15", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-18/", "time_investment_per_student_on_post_test": "0", "time_investment_per_student_on_assignment_that_require_correction": "0", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "0", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "5"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "2", "one_to_one_class_60_min_duartion": "10", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "15", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 19829, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Dec 12, 2022 8:27 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "15", "course_id": 10703, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "15", "instructor_effort_per_student_on_diagnostic_test_correction": "15", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-16/", "time_investment_per_student_on_post_test": "15", "time_investment_per_student_on_assignment_that_require_correction": "15", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "15", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 19910, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Dec 16, 2022 10:19 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 10700, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-27/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "9"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "2", "one_to_one_class_60_min_duartion": "150", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 20116, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Jan 19, 2023 4:29 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 10641, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "15", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-31/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "75", "is_there_a_post_test": "1", "number_of_mock_exams": "10", "one_to_one_class_60_min_duartion": "75", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "75", "one_to_one_class_45_min_duartion": "90", "number_of_assignments_per_student_that_not_require_correction": "10", "id": 19806, "number_of_assignments_per_student_that_require_correction": "5", "published_at": "Dec 8, 2022 11:17 AM", "time_investment_per_student_on_assignment_that_not_require_correction": "2", "course_id": 10143, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "75", "instructor_effort_per_student_on_mock_exam_correction": "75", "instructor_effort_per_student_on_diagnostic_test_correction": "90", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-5/", "time_investment_per_student_on_post_test": "90", "time_investment_per_student_on_assignment_that_require_correction": "2", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "75", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "75"}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Jan 8, 2021 1:09 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/finance-edge/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "3", "one_to_one_class_60_min_duartion": "10", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "3", "id": 19826, "number_of_assignments_per_student_that_require_correction": "3", "published_at": "Dec 12, 2022 2:47 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 9570, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "30", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-14/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "4", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "420", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "5", "id": 20615, "number_of_assignments_per_student_that_require_correction": "6", "published_at": "Mar 16, 2023 4:32 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "60", "course_id": 9468, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "5", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "90", "instructor_effort_per_student_on_diagnostic_test_correction": "120", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-47/", "time_investment_per_student_on_post_test": "75", "time_investment_per_student_on_assignment_that_require_correction": "180", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "120", "one_to_one_class_180_min_duartion": "5", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "1", "number_of_mock_exams": "2", "one_to_one_class_60_min_duartion": "10", "personalization": "1-1", "is_diagnostic_test": "1", "is_instructor_feedback_on_diagnostic_test": "1", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "60", "one_to_one_class_45_min_duartion": "1", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 19819, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Dec 9, 2022 3:41 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "60", "course_id": 9139, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "60", "instructor_effort_per_student_on_diagnostic_test_correction": "60", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-8/", "time_investment_per_student_on_post_test": "60", "time_investment_per_student_on_assignment_that_require_correction": "60", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "60", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "1"}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Oct 8, 2020 3:24 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/homemakers/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "5", "is_there_a_post_test": "1", "number_of_mock_exams": "1", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "30", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "2", "id": 19844, "number_of_assignments_per_student_that_require_correction": "2", "published_at": "Dec 13, 2022 9:09 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "30", "course_id": 8857, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "30", "instructor_effort_per_student_on_diagnostic_test_correction": "0", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course_economics/course-economics-19/", "time_investment_per_student_on_post_test": "30", "time_investment_per_student_on_assignment_that_require_correction": "30", "instructor_feedback_on_post_test": "1", "instructor_effort_per_student_on_post_test_correction": "30", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": "0"}, {"one_to_one_class_90_min_duartion": "", "is_there_a_post_test": "", "number_of_mock_exams": "", "one_to_one_class_60_min_duartion": "", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "", "one_to_one_class_45_min_duartion": "", "number_of_assignments_per_student_that_not_require_correction": "", "id": 0, "number_of_assignments_per_student_that_require_correction": "", "published_at": "Oct 6, 2020 6:44 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "", "course_id": 0, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "", "one_to_one_class_30_min_duartion": "", "instructor_effort_per_student_on_mock_exam_correction": "", "instructor_effort_per_student_on_diagnostic_test_correction": "", "one_to_one_class_150_min_duartion": "", "url": "https://dev.yunolearning.com/course/student/", "time_investment_per_student_on_post_test": "", "time_investment_per_student_on_assignment_that_require_correction": "", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "", "one_to_one_class_180_min_duartion": "", "one_to_one_class_75_min_duartion": ""}, {"one_to_one_class_90_min_duartion": "0", "is_there_a_post_test": "", "number_of_mock_exams": "0", "one_to_one_class_60_min_duartion": "0", "personalization": "1-1", "is_diagnostic_test": "", "is_instructor_feedback_on_diagnostic_test": "", "price": "0", "instructor_effort_per_student_on_assignment_that_require_correction": "0", "one_to_one_class_45_min_duartion": "0", "number_of_assignments_per_student_that_not_require_correction": "36", "id": 20622, "number_of_assignments_per_student_that_require_correction": "0", "published_at": "Mar 16, 2023 7:41 PM", "time_investment_per_student_on_assignment_that_not_require_correction": "15", "course_id": 8848, "duration_diagnostic_test": "0", "one_to_one_class_120_min_duartion": "0", "one_to_one_class_30_min_duartion": "0", "instructor_effort_per_student_on_mock_exam_correction": "0", "instructor_effort_per_student_on_diagnostic_test_correction": "0", "one_to_one_class_150_min_duartion": "0", "url": "https://dev.yunolearning.com/course_economics/course-economics-49/", "time_investment_per_student_on_post_test": "0", "time_investment_per_student_on_assignment_that_require_correction": "0", "instructor_feedback_on_post_test": "", "instructor_effort_per_student_on_post_test_correction": "0", "one_to_one_class_180_min_duartion": "0", "one_to_one_class_75_min_duartion": "0"}], "active_enrollments": "1", "highlight_5": "Participate in public speaking", "age_limit": "Age 10-13", "any_time": "1", "enrollment_type": "rolling", "one_to_one_price": "6000", "event_label": "Course Publish", "unit_price": "4100", "url": "https://dev.yunolearning.com/english-speaking/courses/kids-2/", "record_id": 8848, "max_seats": "8", "duration_days": "168", "highlight_10": "", "ongoing_batches": "5", "price_month_12": "0", "speaking_sessions": "", "price_month_10": "0", "price_month_11": "0", "academies": [28784], "other_currencies": {"AED": {"group_price": "234", "one_to_one_price": "234", "unit_price": "12"}, "USD": {"group_price": "234", "one_to_one_price": "234", "unit_price": "324"}}}}}}]}}