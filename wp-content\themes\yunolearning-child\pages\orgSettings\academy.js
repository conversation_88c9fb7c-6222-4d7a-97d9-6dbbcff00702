Vue.component("yuno-academy", {
  props: {
    storage: {
      type: Object,
      required: true,
    },
  },
  template: `
          <div class="academyForm">
              <div class="row">
                  <div class="col-12 col-md-6">
                      <template v-if="academy.length > 1">
                          <b-field label="Academy">
                              <b-skeleton height="40px" v-if="subform.loading"></b-skeleton>
                              <b-select
                                  v-if="subform.success"
                                  v-model="selectedAcademy"
                                  placeholder="Select academy"
                                  @input="dropdownChange($event)"
                              >
                                  <option
                                      v-for="(option, j) in subform.data"
                                      :key="j"
                                      :value="option.value"
                                  >
                                      {{ option.label }}
                                  </option>
                              </b-select>
                          </b-field>
                          <template v-if="selectedAcademy">
                              <template v-if="orgAdmin.loading">
                                  <div class="field">
                                      <b-skeleton height="40px"></b-skeleton>
                                  </div>
                                  <div class="field">
                                      <b-skeleton height="100px"></b-skeleton>
                                  </div>
                                  <div class="field">
                                      <b-skeleton height="40px"></b-skeleton>
                                  </div>
                                  <div class="field">
                                      <b-skeleton height="40px"></b-skeleton>
                                  </div>
                              </template>
                              <template v-if="orgAdmin.success">
                                  <yuno-org-settings-form
                                      :fields="form.fields"
                                      :payload="form.payload"
                                      :defaultTaxonomy="defaultTaxonomy"
                                      @openModal="openModal"
                                      @submitForm="submitForm"
                                  >
                                  </yuno-org-settings-form>  
                              </template>
                          </template>
                      </template>
                      <template v-else>
                          <template v-if="orgAdmin.loading">
                              <div class="field">
                                  <b-skeleton height="40px"></b-skeleton>
                              </div>
                              <div class="field">
                                  <b-skeleton height="100px"></b-skeleton>
                              </div>
                              <div class="field">
                                  <b-skeleton height="40px"></b-skeleton>
                              </div>
                              <div class="field">
                                  <b-skeleton height="40px"></b-skeleton>
                              </div>
                          </template>
                          <template v-if="orgAdmin.success">
                              <yuno-org-settings-form
                                  :fields="form.fields"
                                  :payload="form.payload"
                                  :defaultTaxonomy="defaultTaxonomy"
                                  @openModal="openModal"
                                  @submitForm="submitForm"
                              >
                              </yuno-org-settings-form>  
                          </template> 
                      </template>
                  </div>
              </div>
              <b-modal
                  :active="addDemoInstructors"
                  :width="500"
                  class="yunoModal demoClass lightTheme"
                  :can-cancel="['escape', 'x']"
                  @close="closeModal"
                  :destroy-on-hide="false"
              >  
                  <div class="modalTitle">
                      <h3 class="headline5">Demo Classes</h3>
                  </div>
                  <b-tabs 
                        class="yunoTabsV5 demoClass"
                      v-model="subjectTabs.activeTab"
                      @input="tabChange"
                      :animated="false"
                      :destroy-on-hide="false"
                  >
                      <b-tab-item
                          v-for="(subject, index) in selectedSubjects"
                          :key="index"
                          :label="subject.label"
                      >
                          <template v-if="module.loading">
                              <b-skeleton height="30px" width="30%"></b-skeleton>
                              <b-skeleton height="60px" width="100%"></b-skeleton>
                          </template>
                          <template v-if="module.success && module.error === null && !module.loading">
                              <yuno-add-instructor
                                  :data="subject"
                                  :currentTabSlug="currentTabSlug"
                                  @closeModal="closeModal"
                                  @submitForm="submitDemoInstructorForm"
                              >
                              </yuno-add-instructor>
                          </template>
                          <template v-else-if="module.success">
                              {{ module.errorData }}
                          </template>
                      </b-tab-item>
                  </b-tabs>
              </b-modal>
          </div>
      `,
  data() {
    return {
      defaultTaxonomy: 0,
      industry: null,
      selectedAcademy: null,
      addDemoInstructors: false,
      selectedSubjects: [],
      currentTabSlug: "",
      subjectTabs: {
        activeTab: 0,
        wrapperClass: "subjectTbsWrapper",
        tabs: [],
      },
      subjectInstructors: {},
      filteredInstructors: [],
      modalDescription:
        "For each subject category, select the instructors who can deliver demo classes. Only these instructors can schedule and deliver demo classes.",
    };
  },
  computed: {
    ...Vuex.mapState([
      "userInfo",
      "user",
      "filterResult",
      "subform",
      "subform2",
      "form",
      "orgAdmin",
      "config",
      "moduleWithoutTab",
      "module",
      "updateLink",
      "filters",
    ]),
    academy() {
      return this.moduleWithoutTab.data.yuno_academy_subscription.academies;
    },
  },
  async created() {},
  destroyed() {},
  mounted() {
    if (this.academy.length > 1) {
      this.fetchAcademies();
    } else {
      this.selectedAcademy = this.academy[0];
      this.manageOrg(this.userInfo.data);
    }
  },
  methods: {
    tabChange(e) {
      this.currentTabSlug = this.subjectTabs.tabs[e].slug;
      this.filters.payload.category.slug = this.currentTabSlug;
      this.setInstructorsForCurrentTab();
    },
    setupSubjectTab() {
      // This method is now just for any additional tab setup if needed
      // The tabs are now directly created from selectedSubjects in the template
      this.subjectTabs = {
        activeTab: 0,
        wrapperClass: "subjectTbsWrapper",
        tabs: this.selectedSubjects,
      };
    },
    gotOrgInstructors(options) {
      let response = options.response;

      if (response?.data?.code === 200) {
        let getData = response.data.data;

        // Map through list_view array and extract instructor data from each item
        if (getData.list_view && Array.isArray(getData.list_view)) {
          this.filters.data = getData.list_view
            .filter((item) => item.instructor) // Filter out any items without instructor
            .map((item) => ({
              id: item.instructor.id,
              name: item.instructor.name,
            }));
        }

        this.filters.payload = {
          org_admin: {
            id: isLoggedIn,
          },
          org: {
            id: this.activeOrg(),
          },
          academy: {
            id: this.selectedAcademy,
          },
          category: [
            {
              slug: this.currentTabSlug || "",
            },
          ],
          instructors: [],
        };
      }
    },
    fetchOrgInstructors() {
      const props = { id: isLoggedIn };
      const options = {
        apiURL: YUNOCommon.config.academy("getOrgInstructors", props),
        module: "gotData",
        store: "filters",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotOrgInstructors(options),
      };
      this.dispatchData("fetchData", options);
    },

    dropdownChange(e) {
      this.resetModules();
      this.manageOrg(this.userInfo.data);
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    openModal() {
      this.addDemoInstructors = true;
      this.initializeSelectedSubjects();
      this.fetchDemoInstructorDetail();
    },
    closeModal() {
      this.addDemoInstructors = false;
    },
    initializeSelectedSubjects() {
      // Initialize selectedSubjects based on the current form data
      if (this.form.fields && this.form.fields.length > 0) {
        const categoryField = this.form.fields.find(
          (field) => field.name === "category"
        );
        if (categoryField && categoryField.selected) {
          this.selectedSubjects = categoryField.selected.map((subject) => ({
            label: subject.name,
            value: subject.slug,
            slug: subject.slug,
            class: subject.class || "",
            instructors: [],
          }));
          // Initialize currentTabSlug with the first subject's slug
          if (this.selectedSubjects.length > 0) {
            this.currentTabSlug = this.selectedSubjects[0].slug;
          }
          this.setupSubjectTab();
        }
      }
    },
    hasSelectedSubjects() {
      const categoryField = this.form.fields.find(
        (field) => field.name === "category"
      );
      const addInstructorsField = this.form.fields.find(
        (field) => field.name === "instructors"
      );
      if (categoryField && categoryField.selected) {
        if (categoryField.selected.length > 0) {
          addInstructorsField.visible = true;
        } else {
          addInstructorsField.visible = false;
        }
      }
    },
    gotAcademies(options) {
      const { code, data } = options.response?.data || {};

      if (code === 200) {
        this.subform.data = data;
      }
    },
    fetchAcademies() {
      const options = {
        apiURL: YUNOCommon.config.org("orgAcademies", this.activeOrg()),
        module: "gotData",
        store: "subform",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotAcademies(options),
      };
      this.dispatchData("fetchData", options);
    },

    fetchData(field, apiURL, callbackFunc) {
      const formField = YUNOCommon.findObjectByKey(
        this.form.fields,
        "name",
        field
      );
      formField.loading = true;
      const options = {
        apiURL: apiURL,
        module: "gotData",
        store: "subform2",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => callbackFunc(options, formField),
      };
      this.dispatchData("fetchData", options);
    },
    setStorage(hasColumns) {
      const storage = this.$props.storage;
      const store = {
        currentTab: this.filterResult.tabs.activeTab,
      };

      setTimeout(() => {
        sessionStorage.setItem(
          storage.name + "V" + storage.version,
          JSON.stringify(store)
        );
      }, 100);
    },
    resetModules() {
      this.orgAdmin.data = [];
      this.orgAdmin.success = false;
      this.orgAdmin.error = null;
    },
    onTabChanged(e) {
      this.setStorage();
      this.resetModules();
    },
    manageOrg(data) {
      const activeOrg = this.activeOrg();

      if (activeOrg) {
        this.setupForm(data, activeOrg);
        this.fetchModules(data, activeOrg);
      }
    },
    /**
     * Dispatches an action to the store with the specified options.
     *
     * @param {string} action - The name of the action to dispatch.
     * @param {Object} options - The options to pass to the action.
     */
    dispatchData(action, options) {
      this.$store.dispatch(action, options);
    },
    /**
     * Fetches data from the specified API URL and invokes the callback function with the fetched data.
     * @param {string} field - The name of the field to fetch data for.
     * @param {string} apiURL - The URL of the API to fetch data from.
     * @param {Function} callbackFunc - The callback function to invoke with the fetched data.
     */
    fetchData(field, apiURL, callbackFunc) {
      const formField = YUNOCommon.findObjectByKey(
        this.form.fields,
        "name",
        field
      );
      formField.loading = true;
      const options = {
        apiURL: apiURL,
        module: "gotData",
        store: "subform2",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => callbackFunc(options, formField),
      };
      this.dispatchData("fetchData", options);
    },
    // Function to set up the form with default field properties and payload
    setupForm(userInfo, activeOrg) {
      const defaultFieldProperties = {
        isRequired: false,
        isDisabled: false,
        isLoading: false,
      };

      const form = [
        {
          label: "Name",
          placeholder: "Enter academy name",
          type: "text",
          name: "academy_name",
          ...defaultFieldProperties,
        },
        {
          label: "Descripition",
          placeholder: "Enter description here...",
          type: "textarea",
          name: "description",
          ...defaultFieldProperties,
        },
        {
          label: "Excerpt",
          placeholder: "Enter excerpt here...",
          type: "textarea",
          name: "excerpt",
          ...defaultFieldProperties,
        },
        {
          label: "Upload Logo",
          placeholder:
            "File format: JPG, GIF or PNG. Dimensions: 512 x 512 pixels",
          type: "upload",
          name: "logo",
          cta: "Click to upload",
          ...defaultFieldProperties,
        },
        {
          label: "Subjects",
          placeholder: "Select subjects",
          type: "multiSelectDropdown",
          name: "category",
          options: [],
          loading: false,
          ...defaultFieldProperties,
          isRequired: true,
          selected: [],
        },
        {
          label: "Add or remove demo instructors",
          type: "addInstructors",
          class: "addInstructors",
          visible: false,
          name: "instructors",
        },
      ];

      const payload = {
        id: this.academy,
        org_id: activeOrg,
        academy_name: "",
        user_id: isLoggedIn,
        excerpt: "",
        description: "",
        logo: null,
        banner_image: null,
        action: "publish",
        category: [],
        app: null,
      };

      // Set the form fields and payload
      this.form.fields = form;
      this.form.payload = payload;
    },
    /**
     * Fetches the modules for the organization.
     * This function internally calls the fetchOrg function.
     */
    fetchModules(userInfo, activeOrg) {
      this.fetchAcademy(userInfo, activeOrg);
      this.fetchOrgInstructors();
    },
    /**
     * Handles the response of the organization data API call.
     * @param {Object} options - The response object from the API call.
     */
    gotOrg(options) {
      const { code, data } = options.response?.data || {};

      if (code === 200) {
        // const fieldsToNullify = ['sub_industry', 'font_family'];
        const imageFields = ["logo", "banner_image"];

        // Convert "null" strings to null values
        // fieldsToNullify.forEach(field => {
        //     if (data[field] === "null") {
        //     data[field] = null;
        //     }
        // });

        // Convert image fields to object format
        imageFields.forEach((field) => {
          if (data[field] !== null) {
            data[field] = { name: data[field] };
          }
        });

        data.user_id = isLoggedIn;

        // Update orgAdmin data and form payload
        this.orgAdmin.data = data;
        this.form.payload = data;
        this.inlineData(["category"]);
      }
    },
    /**
     * Fetches organization data from the server.
     */
    fetchAcademy(userInfo, activeOrg) {
      const options = {
        apiURL: YUNOCommon.config.org("getAcademy", this.selectedAcademy),
        module: "gotData",
        store: "orgAdmin",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotOrg(options),
      };
      this.dispatchData("fetchData", options);
    },
    /**
     * Fetches inline data for the given fields.
     * @param {Array} fields - The array of fields to fetch inline data for.
     */
    inlineData(fields) {
      fields.forEach((field) => {
        this.fetchInlineDataData(field);
      });
    },
    /**
     * Fetches inline data based on the given field.
     * @param {string} field - The field to fetch inline data for.
     */
    fetchInlineDataData(field) {
      switch (field) {
        case "category":
          this.fetchSubjects(field);
          break;
      }
    },
    /**
     * Handles the response of the organization industry API call.
     * @param {Object} options - The response object from the API call.
     * @param {Object} formField - The form field object.
     */
    gotSubjects(options, formField) {
      const { code, data } = options.response?.data || {};
      formField.loading = false;

      if (code === 200) {
        formField.options = data;

        formField.selected = data.filter((subject) =>
          this.orgAdmin.data.category.some(
            (picked) => picked.slug === subject.slug
          )
        );

        // Check if subjects are selected after loading
        Vue.nextTick(() => {
          this.hasSelectedSubjects();
        });
      }
    },
    /**
     * Fetches the organization industry data.
     * @param {string} field - The field to fetch data for.
     */
    fetchSubjects(field) {
      this.fetchData(
        field,
        YUNOCommon.config.generic("categories"),
        this.gotSubjects
      );
    },
    gotDemoInstructorDetail(options) {
      const { code, data } = options.response?.data || {};
      if (code === 200) {
        let demoInstructor = data.demo_instructors || [];

        // Initialize subjectInstructors
        this.subjectInstructors = {};

        // Check if demoInstructor is empty
        if (!demoInstructor || demoInstructor.length === 0) {
          console.warn("No demo instructors found");
          this.setInstructorsForCurrentTab();
          return;
        }

        // Process demo instructors and store them for each subject
        demoInstructor.forEach((categoryObj) => {
          if (!categoryObj) return; // Skip if category object is null/undefined

          const category = Object.keys(categoryObj)[0];
          const instructorData = categoryObj[category]?.instructor;

          // Skip if no instructor data
          if (!instructorData) return;

          // Handle both array and object formats of instructor data
          let instructorIds = [];
          if (Array.isArray(instructorData)) {
            instructorIds = instructorData
              .map((inst) => inst?.id)
              .filter(Boolean);
          } else {
            instructorIds = Object.values(instructorData)
              .map((inst) => inst?.id)
              .filter(Boolean);
          }

          this.subjectInstructors[category] = {
            hasRecord: instructorIds.length > 0,
            instructors: instructorIds,
          };
        });

        // Set initial instructors for current tab
        this.setInstructorsForCurrentTab();
      }
    },
    fetchDemoInstructorDetail() {
      const props = {
        id: this.selectedAcademy,
        org_id: this.activeOrg(),
        academy_id: this.selectedAcademy,
      };
      const options = {
        apiURL: YUNOCommon.config.academy("getDemoInstructors", props),
        module: "gotData",
        store: "module",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotDemoInstructorDetail(options),
      };
      this.dispatchData("fetchData", options);
    },
    demoInstructorFormPosted(options) {
      this.filters.loading = false;
      const response = options?.response?.data;

      if (response?.code === 201) {
        this.showToastMessage(response.message);
      } else if (response?.message) {
        this.showToastMessage(response.message);
      }
    },
    submitDemoInstructorForm() {
      this.filters.loading = true;
      let payload = this.filters.payload;

      // Ensure category slug is set correctly
      this.filters.payload.category[0].slug = this.currentTabSlug;

      // Safely check if the subject exists and has hasRecord property
      let hasRecord =
        this.subjectInstructors[this.currentTabSlug]?.hasRecord || false;

      let apiUrl = hasRecord ? "updateDemoInstructors" : "addDemoInstructors";

      const options = {
        apiURL: YUNOCommon.config.academy(apiUrl, false),
        module: "gotData",
        store: "updateLink",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.demoInstructorFormPosted(options),
      };
      this.dispatchData("postData", options);
    },
    /**
     * Submits the form data and updates the organization details.
     * @param {Object} payload - The payload containing the form data.
     */
    submitForm(payload) {
      this.form.isLoading = true;

      const options = {
        apiURL: YUNOCommon.config.org("createUpdateAcademy"),
        module: "gotData",
        store: "form",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.formPosted(options),
      };

      this.dispatchData("postData", options);
    },
    /**
     * Handles the response after form submission.
     * @param {Object} options - The options object containing the response data.
     */
    formPosted(options) {
      this.form.isLoading = false;

      const response = options?.response?.data;

      if (response?.code === 201) {
        this.showToastMessage(response.message);
      } else if (response?.message) {
        this.showToastMessage(response.message);
      }
    },

    /**
     * Displays a toast message.
     * @param {string} message - The message to be displayed in the toast.
     */
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },
    setInstructorsForCurrentTab() {
      if (this.currentTabSlug && this.subjectInstructors && this.filters.data) {
        const subjectData = this.subjectInstructors[this.currentTabSlug] || {
          instructors: [],
        };
        const instructorIds = subjectData.instructors;

        // Map instructor IDs to full instructor objects with names
        const instructors = instructorIds
          .map((id) => {
            const instructor = this.filters.data.find((inst) => inst.id === id);
            return instructor ? { id: id } : null;
          })
          .filter(Boolean);

        // Update filters payload
        this.filters.payload.instructors = instructors;

        // Ensure category slug is set correctly
        this.filters.payload.category[0].slug = this.currentTabSlug;

        // Update hasRecord in tabs if needed
        const currentSubject = this.selectedSubjects.find(
          (subject) => subject.slug === this.currentTabSlug
        );
        if (currentSubject) {
          currentSubject.hasRecord = subjectData.hasRecord || false;
        }
      }
    },
  },
});
