<?php

namespace V2;

use WP_REST_Controller;
use WP_REST_Response;
use WP_REST_Request;
use WP_Error;
use WP_REST_Server;
use V1\PayloadController;
use UserElasticSearch;
use Utility;
use DateTime;

class UserController
{
    public $namespace;
    public $resource_name_1;
    public $resource_name_2;
    public $resource_name_3;
    public $resource_name_4;
    public $resource_name_5;
    public $resource_name_6;
    public $resource_name_7;
    public $resource_name_8;
    public $create_working_hours;
    public $update_working_hours;
    public $check_resource_working_hours;
    public $resources_available_batch;
    public $resources_available_class;
    public $get_resource_availability;
    public $get_mylearners_instructor;
    public $get_mylearner_details;

    public function __construct()
    {
        $this->namespace = 'yuno/v2';
        $this->resource_name_1 = '/users/(?P<user_id>\d+)/(?P<category>[a-zA-Z0-9-]+)';
        $this->resource_name_2 = '/user/info/(?P<userId>\d+)';
        $this->resource_name_3 = '/users/(?P<userID>\d+)/profile';
        $this->resource_name_4 = '/users/(?P<userID>\d+)/(?P<criteria>[a-zA-Z0-9-]+)/profile';
        $this->resource_name_5 = '/(?P<role>[a-zA-Z0-9-]+)/list'; // route name (to get all learners plus org-users users)
        $this->resource_name_6 = '/users/(?P<user_role>[a-zA-Z0-9-]+)/(?P<role>[a-zA-Z0-9-]+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name_7 = '/demo-requests/yuno-admin/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name_8 = '/demo-requests/(?P<learnerId>\d+)';
        $this->create_working_hours = '/(?P<resource>[a-zA-Z0-9-]+)/(?P<resource_id>\d+)/workinghours/create';
        $this->update_working_hours = '/(?P<resource>[a-zA-Z0-9-]+)/(?P<resource_id>\d+)/workinghours/update';
        $this->check_resource_working_hours = '(?P<resource>[a-zA-Z0-9-]+)/(?P<resource_id>\d+)/workinghours';
        $this->resources_available_batch = '/(?P<resource>[a-zA-Z0-9-]+)/freebusy/batch';
        $this->resources_available_class = '/(?P<resource>[a-zA-Z0-9-]+)/freebusy/class';
        $this->get_resource_availability = '/(?P<resource>[a-zA-Z0-9-]+)/freebusy/slots';
        $this->get_mylearners_instructor = '/instructor/mylearners/(?P<instructorId>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->get_mylearner_details = '/instructor/mylearner/(?P<learnerId>\d+)';
    }

    public function register_routes()
    {
        register_rest_route($this->namespace, $this->resource_name_1, array(
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'user_signup'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name_2, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'user_info'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name_3, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_user_profile'),
                'permission_callback' => array($this, 'get_user_profile_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name_4, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_user_profile'),
                'permission_callback' => array($this, 'get_user_profile_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->resource_name_5, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_learner_list'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resource_name_6, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'get_users_insights'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resource_name_7, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'get_demo_requests'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resource_name_8, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_demo_request'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->create_working_hours, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'create_instructor_working_hours'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->update_working_hours, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'update_instructor_working_hours'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resources_available_batch, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'getResourceAvailability'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resources_available_class, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'getResourceAvailabilityClass'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->check_resource_working_hours, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_instructor_working_hours'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->get_resource_availability, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'get_instructor_free_slots'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->get_mylearners_instructor, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_mylearners_instructor'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->get_mylearner_details, array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array($this, 'get_mylearner_details'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
    }

    /**
     * Token authorization check
     * This function common for all post login apis
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {
        return true;
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {
            return false;
        }
        list($bearer, $token) = explode(" ", $authToken);
        /*error_log("111jwt permission check 62: ".date('Y-m-d H:i:s')." === ". CURRENT_LOGGED_IN_USER_ID. "\n\n", 3, ABSPATH.'error-logs/jwt.log');*/
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function get_demo_request_permissions_check($request)
    {
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {
            return false;
        }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) {
                return true;
            } else {
                return false;
            }
        }
        $codes = error_code_setting();
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } else if ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false,
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }

    public function userWhatsAppNotificationSettings($settings_array)
    {
        if ($settings_array['yuno_user_whatsapp_check']) {
            $yuno_user_whatsapp_check = "true";
        } else {
            $yuno_user_whatsapp_check = "false";
        }
        $notification_data = [
            "user_id" => $settings_array['user_id'],
            "email" => $settings_array['email'],
            "phoneno" => $settings_array['mobile'],
            "first_name" => $settings_array['first_name'],
            "last_name" => $settings_array['last_name'],
            "whatsapp" => $settings_array['mobile'],
            "events" => [
                [
                    "id" => "1",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "2",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "3",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "4",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "5",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "6",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "7",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "8",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "9",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "10",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "11",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
                [
                    "id" => "12",
                    "whatsapp" => $yuno_user_whatsapp_check,
                    "sms" => "false",
                    "email" => "true",
                    "voice" => "false",
                    "desktop_push" => "false",
                    "mobile_push" => "true",
                ],
            ],
        ];
        $settings_obj = json_encode($notification_data);
        //error_log('notification settings====='.$settings_obj);
        postNotificationSettings($settings_obj);
        return;
    }
    /**
     * Category base signup - insert user input - new
     */
    public function user_signup($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $when_planning_ielts = $when_planning_ielts = $time_of_study = $type_of_ielts = $target_band = $yuno_gplus_mobile = $yuno_user_whatsapp_check = $user_id = $student_age = $what_best_describes_you = $laptop_desktop_access = $reason_enroll_string = $classes_duration = "";

        $user_id = $request['user_id'];
        //error_log("raman test userid: ".$user_id);
        $category = strtolower($request['category']);
        //error_log("resulted output:".$request->get_body());
        $data = json_decode($request->get_body());

        update_user_meta($user_id, 'profile_privacy', "private");
        update_user_meta($user_id, 'classattendevent', "false");

        if ($category == "general") {

            /*$term_and_condition = $data->term_and_condition;
            $categoryName = isset($data->categories) ? $data->categories : "";
            if (empty($what_best_describes_you) || empty($term_and_condition)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));
            }
            update_user_meta($user_id, 'term_and_condition', $term_and_condition);*/

            // crm leads / contacts update
            $form_type_step = isset($data->form_type_step) ? $data->form_type_step : "";
            error_log('Ram whatsapp check' . json_encode($data));
            //error_log('form-1 case raman'.json_encode($data->form_type_step));
            if ($form_type_step == 1) {
                $yuno_gplus_mobile = isset($data->mobile) ? $data->mobile : '';
                $yuno_user_whatsapp_check = isset($data->whatsapp) ? $data->whatsapp : '';
                error_log('Ram whatsapp check' . json_encode($yuno_user_whatsapp_check));
                if ($yuno_user_whatsapp_check == "true" || $yuno_user_whatsapp_check == true) {
                    $whatsappFlag = true;
                    update_user_notifications($user_id, true, "whatsapp");
                } else {
                    $whatsappFlag = false;
                }

                error_log('Ram whatsapp check' . json_encode($whatsappFlag));
                $categories = !empty($data->categories) ? $data->categories : [];
                update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
                update_user_meta($user_id, 'yuno_user_whatsapp_check', $whatsappFlag);
                update_user_meta($user_id, 'Category_URL_For_Signup', $categories);
                update_user_meta($user_id, 'is_step_one_complete', true);

                $userdata = get_userdata($user_id);
                $email = $userdata->data->user_email;
                //error_log("whatsapp flag test: ".$yuno_user_whatsapp_check);

                //$whatsappFlag = $yuno_user_whatsapp_check == "true" ? true : false;
                $settings_array = [
                    "user_id" => $user_id,
                    "email" => $email,
                    "mobile" => $yuno_gplus_mobile,
                    "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                    "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                    "whatsapp" => $yuno_gplus_mobile,
                    "yuno_user_whatsapp_check" => $whatsappFlag,
                    "time_of_study" => $time_of_study,
                ];

                //error_log("whatsapp flag test: ".$whatsappFlag);

                $this->userWhatsAppNotificationSettings($settings_array);
                if ($whatsappFlag == true) {
                    $event_trigger_obj = [
                        "event_id" => "1",
                        "user_ids" => (explode(" ", $user_id)),
                        "customfields" => [],
                    ];
                    $notification_obj = json_encode($event_trigger_obj);
                    //error_log('signup trigger====='.$notification_obj);
                    $channel = "whatsapp";
                    $item = "welcome";
                    sendNotification($notification_obj, $channel, $item);
                }
            } else if ($form_type_step == 2) {
                $what_best_describes_you = isset($data->what_best_describes_you) ? $data->what_best_describes_you : '';
                $time_of_study = isset($data->time_of_study) ? $data->time_of_study : '';
                $type_of_ielts = isset($data->which_ielts_exam_do_you_want_to_take) ? $data->which_ielts_exam_do_you_want_to_take : '';
                $target_band = isset($data->what_is_your_target_band_score) ? $data->what_is_your_target_band_score : '';
                $yuno_user_whatsapp_check = get_user_meta($user_id, 'yuno_user_whatsapp_check', true);
                $yuno_gplus_mobile = get_user_meta($user_id, 'yuno_gplus_mobile', true);
                update_user_meta($user_id, 'time_of_study', $time_of_study);
                if (!empty($type_of_ielts)) {
                    update_user_meta($user_id, 'type_of_ielts', $type_of_ielts);
                }
                if (!empty($target_band)) {
                    update_user_meta($user_id, 'target_band', $target_band);
                }
                if (!empty($classes_duration)) {
                    update_user_meta($user_id, 'classes_duration', $classes_duration);
                }
                if (!empty($when_planning_ielts)) {
                    update_user_meta($user_id, 'when_planning_ielts', $when_planning_ielts);
                }
                if (!empty($laptop_desktop_access)) {
                    update_user_meta($user_id, 'laptop_desktop_access', $laptop_desktop_access);
                }
                if (!empty($student_age)) {
                    update_user_meta($user_id, 'student_age', $student_age);
                }
                if (!empty($reason_enroll_string)) {
                    update_user_meta($user_id, 'reason_enroll', $reason_enroll_string);
                }
                update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
                update_user_meta($user_id, 'is_signup_complete', true);
                update_user_meta($user_id, 'is_step_one_complete', false);

                // $curlPost = [
                // "data" =>[
                //     "details" => [
                //         "user_id" => $user_id,
                //         "event_type" => "signedup",
                //         "event_label" => "User signed up",
                //         "role" => "Learner",
                //         "name" => get_user_meta($user_id, 'yuno_display_name', true),
                //         "image" => get_user_meta($user_id, 'googleplus_profile_img', true),
                //         "category_url_for_signup" => is_array(get_user_meta($user_id, 'Category_URL_For_Signup', true)) ? implode(",", get_user_meta($user_id, 'Category_URL_For_Signup', true)) : "",
                //         "yuno_user_whatsapp_check" => (bool)get_user_meta($user_id, 'yuno_user_whatsapp_check', true),//todo
                //         "privacy_policy_terms_of_service" => true,
                //         "when_planning_ielts" =>  $when_planning_ielts,
                //         "time_of_study" =>  $time_of_study,
                //         "type_of_ielts" =>  $type_of_ielts,
                //         "ielts_target_band" =>  $target_band,
                //         "what_best_describes_you" =>  $what_best_describes_you,
                //         "laptop_desktop_access" =>  $laptop_desktop_access,
                //         "student_age" =>  $student_age,
                //         "reason_enroll" =>  $reason_enroll,
                //         "classes_duration" =>  $classes_duration,
                //         "referred_by" =>  ""
                //     ],
                //     "@timestamp" => date("Y-m-d H:i:s")
                //   ]
                // ];
                //error_log('form-2 case raman'.json_encode($curlPost));
                //post_elastic_event($curlPost);
                $req['user_id'] = $user_id;
                $details = PayloadController::get_user_identify($req);
                if ($details->error_data['204']['status'] == "FAIL") {
                    return new WP_Error($codes["GET_FAIL"]["code"], "Invalid user id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
                }
                $properties = $details->data['data']['properties'];
                $curlPost['data'] = [
                    "data" => $properties,
                ];
                //error_log("every post es call" . date("Y-m-d H:i:s") . "=== " . $url . "Event type " . json_encode($curlPost) . "=== Response" . json_encode($response) . "\n", 3, ABSPATH . "error-logs/every-remote.log");
                UserElasticSearch::update_signedup("basic_details", $curlPost);
            }

            $signup_result = array(
                'code' => $codes["POST_INSERT"]["code"],
                'message' => 'User signup completed',
                'data' => array('status' => $codes["POST_INSERT"]["code"]),
            );
            return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);
        }

        if (empty($user_id) || empty($category)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));
        }
        // Need to confirm from gagan
        //$isExist = update_user_meta($user_id, 'is_signup_complete', true);

        /*if($isExist == ture || $isExist == "ture"){
        return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User already signed up', array('status' => $codes["POST_INSERT_FAIL"]["code"]));
        }*/

        /*if (empty($yuno_gplus_mobile) || strlen($yuno_gplus_mobile) != 10) {
        return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"], 'message' => 'Please enter valid mobile number of 10 digit'));
        }*/

        //error_log("user sign up new category: ".$category);
        if (
            $category == "ielts" || $category == "english-speaking" ||
            $category == "coding-for-kids" || $category == 'anil-lamba-on-finance' ||
            $category == "vedic-maths" || $category == "microsoft-excel" ||
            $category == "python-programming" || $category == "pte" || $category == "toefl" || $category == "duolingo" || $category == "french" || $category == "data-science-and-analytics" || $category == "cuet" || $category == "gmat" || $category == "gre" || $category == "sat" || $category == "german" || $category == "spanish" || $category == "italian" || $category == "hindi" || $category == "neet" || $category == "jee"
        ) {
            $yuno_gplus_mobile = isset($data->mobile) ? $data->mobile : '';
            $yuno_user_whatsapp_check = isset($data->whatsapp) ? $data->whatsapp : '';
            $type_of_ielts = isset($data->which_ielts_exam_do_you_want_to_take) ? $data->which_ielts_exam_do_you_want_to_take : '';
            $target_band = isset($data->what_is_your_target_band_score) ? $data->what_is_your_target_band_score : '';
            $time_of_study = isset($data->time_of_study) ? $data->time_of_study : '';
            $term_and_condition = isset($data->term_and_condition) ? $data->term_and_condition : '';
            $what_best_describes_you = isset($data->what_best_describes_you) ? $data->what_best_describes_you : '';
            /*$when_planning_ielts = $data->when_planning_ielts;
            $time_of_study = $data->time_of_study;
            $type_of_ielts = $data->type_of_ielts;
            $target_band = $data->target_band;
            $term_and_condition = $data->term_and_condition;*/
            /*if (empty($yuno_gplus_mobile) || empty($time_of_study) ||
            empty($type_of_ielts) || empty($target_band) ||
            empty($term_and_condition)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not complete', array('status' => $codes["POST_INSERT_FAIL"]["code"]));
            }*/
            update_user_meta($user_id, 'term_and_condition', $term_and_condition);
            update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
            if (!empty($time_of_study)) {
                update_user_meta($user_id, 'time_of_study', $time_of_study);
            }
            if (!empty($type_of_ielts)) {
                update_user_meta($user_id, 'type_of_ielts', $type_of_ielts);
            }
            if (!empty($target_band)) {
                update_user_meta($user_id, 'target_band', $target_band);
            }
            if (!empty($classes_duration)) {
                update_user_meta($user_id, 'classes_duration', $classes_duration);
            }
            if (!empty($when_planning_ielts)) {
                update_user_meta($user_id, 'when_planning_ielts', $when_planning_ielts);
            }
            if (!empty($what_best_describes_you)) {
                update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
            }
            if (!empty($laptop_desktop_access)) {
                update_user_meta($user_id, 'laptop_desktop_access', $laptop_desktop_access);
            }
            if (!empty($student_age)) {
                update_user_meta($user_id, 'student_age', $student_age);
            }
            if (!empty($reason_enroll_string)) {
                update_user_meta($user_id, 'reason_enroll', $reason_enroll_string);
            }
            //update_user_meta($user_id, 'Category_URL_For_Signup', [$category]);

            //start
            // if ($category == "ielts") {
            //     $data = [
            //         "user_id" => $user_id,
            //         //"when_planning_ielts" => $when_planning_ielts,
            //         "time_of_study" => $time_of_study,
            //         "type_of_ielts" => $type_of_ielts,
            //         "target_band" => $target_band
            //     ];
            //     $table = "wp_ielts_demographic";
            //     $inserted = $wpdb->insert($table, $data);
            // }
            //end

            update_user_meta($user_id, 'is_signup_complete', true);
            update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);

            $signup_result = array(
                'code' => $codes["POST_INSERT"]["code"],
                'message' => 'User signup is completed',
                'data' => array('status' => $codes["POST_INSERT"]["code"]),
            );

            $userdata = get_userdata($user_id);
            $email = $userdata->data->user_email;
            if ($yuno_user_whatsapp_check == "true" || $yuno_user_whatsapp_check == true) {
                $whatsappFlag = true;
                update_user_notifications($user_id, true, "whatsapp");
            } else {
                $whatsappFlag = false;
            }

            $settings_array = [
                "user_id" => $user_id,
                "email" => $email,
                "mobile" => $yuno_gplus_mobile,
                "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                "whatsapp" => $yuno_gplus_mobile,
                "yuno_user_whatsapp_check" => $whatsappFlag,
            ];
            $this->userWhatsAppNotificationSettings($settings_array);
            if ($whatsappFlag == true) {
                $event_trigger_obj = [
                    "event_id" => "1",
                    "user_ids" => (explode(" ", $user_id)),
                    "customfields" => [],
                ];
                $notification_obj = json_encode($event_trigger_obj);
                //error_log('signup trigger====='.$notification_obj);
                $channel = "whatsapp";
                $item = "welcome";
                sendNotification($notification_obj, $channel, $item);
            }

            // $curlPost = [
            //     "data" =>[
            //         "details" => [
            //             "user_id" => $user_id,
            //             "event_type" => "signedup",
            //             "event_label" => "User signed up",
            //             "role" => "Learner",
            //             "name" => get_user_meta($user_id, 'yuno_display_name', true),
            //             "image" => get_user_meta($user_id, 'googleplus_profile_img', true),
            //             "category_url_for_signup" =>is_array(get_user_meta($user_id, 'Category_URL_For_Signup', true)) ? implode(",", get_user_meta($user_id, 'Category_URL_For_Signup', true)) : "",
            //             "yuno_user_whatsapp_check" => (bool)get_user_meta($user_id, 'yuno_user_whatsapp_check', true),//todo
            //             "privacy_policy_terms_of_service" => true,
            //             "when_planning_ielts" =>  $when_planning_ielts,
            //             "time_of_study" =>  $time_of_study,
            //             "type_of_ielts" =>  $type_of_ielts,
            //             "ielts_target_band" =>  $target_band,
            //             "what_best_describes_you" =>  $what_best_describes_you,
            //             "laptop_desktop_access" =>  $laptop_desktop_access,
            //             "student_age" =>  $student_age,
            //             "reason_enroll" =>  $reason_enroll,
            //             "classes_duration" =>  $classes_duration,
            //             "referred_by" =>  ""
            //         ],
            //         "@timestamp" => date("Y-m-d H:i:s")
            //       ]
            //     ];
            //error_log($category.'Call post_signup_event'.json_encode($curlPost));
            //post_elastic_event($curlPost);
            $req['user_id'] = $user_id;
            $details = PayloadController::get_user_identify($req);
            if ($details->error_data['204']['status'] == "FAIL") {
                return new WP_Error($codes["GET_FAIL"]["code"], "Invalid user id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
            }
            $properties = $details->data['data']['properties'];
            $curlPost['data'] = [
                "data" => $properties,
            ];
            //error_log("every post es call" . date("Y-m-d H:i:s") . "=== " . $url . "Event type " . json_encode($curlPost) . "=== Response" . json_encode($response) . "\n", 3, ABSPATH . "error-logs/every-remote.log");
            UserElasticSearch::update_signedup("basic_details", $curlPost);
            return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);
        }

        /*if ($inserted) {
    $lastInsertedId = $wpdb->insert_id;
    update_user_meta($user_id, 'is_signup_complete', true);
    $signup_result = array(
    'code' => $codes["POST_INSERT"]["code"],
    'message' => 'User signup is done',
    'data' => array('record_inserted' => $lastInsertedId)
    );
    $userdata = get_userdata($user_id);
    $email = $userdata->data->user_email;
    $whatsappFlag = $yuno_user_whatsapp_check == "true" ? true : false;
    $settings_array = [
    "user_id"=> $user_id,
    "email"=> $email,
    "mobile"=> $yuno_gplus_mobile,
    "first_name"=> get_user_meta($user_id, 'yuno_first_name', true),
    "last_name"=>  get_user_meta($user_id, 'yuno_last_name', true),
    "whatsapp"=> $yuno_gplus_mobile,
    "yuno_user_whatsapp_check"=> $whatsappFlag,
    ];
    $this->userWhatsAppNotificationSettings($settings_array);
    if($yuno_user_whatsapp_check == true || $yuno_user_whatsapp_check == "true"){
    $event_trigger_obj = [
    "event_id"=> "1",
    "user_ids"=> (explode(" ",$user_id)),
    "customfields"=> []
    ];
    $notification_obj = json_encode($event_trigger_obj);
    error_log('signup trigger====='.$notification_obj);
    sendNotification($notification_obj);
    }
    return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);
    } else {
    return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));
    }*/
    }

    public function get_user_info_permissions_check()
    {
        return true;
    }
    /**
     * Getting user info
     * Current using for signup page for optimize performance speed up of login
     * call only 1 api instead of 3 apis
     */
    public function user_info($request)
    {
        date_default_timezone_set('Asia/Kolkata');
        track_logs(time(), "user_info", '', time(), "start", 'user');
        $codes = error_code_setting();
        global $wpdb;
        /*$userInfoCacheData = get_cache("user_info");
        if (!empty($userInfoCacheData)) {
        error_log("before user_info");
        return new WP_REST_Response($userInfoCacheData, 200);
        error_log("after user_info");
        }*/
        $userId = (int) $request['userId'];
        if ($userId > 0) {
        } else {
            return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));
        }

        $userdata = get_userdata($userId);
        error_log("step 1: user id: " . $userId . " : " . json_encode($userdata) . "\n\n", 3, ABSPATH . "error-logs/user_info_6_june.log");
        if (empty($userdata)) {
            return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
        }
        $user_email = $userdata->data->user_email;
        $profileUserIdReference = get_user_meta($userId, 'profile_user_id_reference', true);
        $profileURL = get_permalink($profileUserIdReference);
        $profileURLArray = explode("/", $profileURL);
        $userProfileArray = array_filter($profileURLArray);
        $userProfileName = end($userProfileArray);
        $user_nicename = $userProfileName;
        //$user_nicename = $userdata->data->user_nicename;
        $isHomePageSignup = get_user_meta($userId, 'Home_Page_Signup_Form', true);

        $roles = $userdata->roles ? $userdata->roles : '';
        if ($userdata->roles) {
            if (in_array('um_instructor', $userdata->roles)) {
                $current_role = 'Instructor';
            } else if (in_array('um_counselor', $userdata->roles)) {
                $current_role = 'Counselor';
            } else if (in_array('um_yuno-admin', $userdata->roles)) {
                $current_role = 'yuno-admin';
            } else if (in_array('um_content-admin', $userdata->roles)) {
                $current_role = 'content-admin';
            } else if (in_array('um_yuno-category-admin', $userdata->roles)) {
                $current_role = 'yuno-category-admin';
            } else if (in_array('administrator', $userdata->roles)) {
                $current_role = 'administrator';
            } else if (in_array('um_dashboard-viewer', $userdata->roles)) {
                $current_role = 'dashboard-viewer';
            } else if (in_array('um_org-admin', $userdata->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'Learner';
            }
        } else {
            error_log("step 2: " . date("Y-m-d H:i:s") . " : " . $userdata->roles . "\n\n", 3, ABSPATH . "error-logs/user_info_6_june.log");
            return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
        }
        $signupDetail = get_user_meta($userId, 'is_signup_complete', true);
        if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
            $isSignup = "completed";
            $isInvited = false;
            $withoutCategory = false;
        } else {
            //error_log("home page signup1:".$isHomePageSignup);
            if (!empty($isHomePageSignup)) {
                $withoutCategory = true;
            } else {
                $withoutCategory = false;
            }
            //error_log("home page signup2:".$withoutCategory);
            $isInvited = get_user_meta($userId, 'Yuno_Is_Invited', true);
            if ($isInvited == 1) {
                $isInvited = true;
            } else {
                $isInvited = false;
            }
            $isSignup = "pending";
            //error_log("Cognito first attempt step 3: " . date("Y-m-d H:i:s") . "\n" . "first user arrival before redirecting after success\n", 3, ABSPATH . " === email === ".$UEmail."error-logs/cognito-custom-errors.log");
            $classEnrollmentStatus = get_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_STATUS', true);
            //error_log("classEnrollmentStatus123 === ".$classEnrollmentStatus);
            if ($classEnrollmentStatus == true) {
                /*$classId = get_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_ID', true);
                //update_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_STATUS', false);
                $ClassTitle = get_the_title($classId);
                $ClassExcerpt = $ClassTitle;
                $evantStartDate = get_post_meta($classId, '_EventStartDate', true);
                $evantDuration = get_post_meta($classId, '_EventDuration', true)/60;
                $classObject = [
                "hasEnrolled"=> true,
                "title" => $ClassTitle,
                "dateTime" => date("F j, Y, g:i a", strtotime($evantStartDate)),
                "duration" => $evantDuration." Minutes"
                ];
                error_log("raman test class object === ".json_encode($classObject));*/
                $hasWebinarEnrolled = true;
            }
        }
        $l_category = get_user_meta($userId, 'Category_URL_For_Signup', true);
        if (is_array($l_category) && count($l_category) > 0) {
            $learnerCategory = $l_category;
        } else {
            if (empty($l_category)) {
                $learnerCategory = [];
            } else {
                if (strpos($l_category, '/') !== false) {
                    $cat = strtolower(ltrim($l_category, '/'));
                } else {
                    $cat = strtolower($l_category);
                }

                if ($cat == trim($cat) && strpos($cat, ' ') !== false) {
                    $learner_cat = str_replace(' ', '-', $cat);
                    $learnerCategory = explode(" ", $learner_cat);
                } else {
                    $learnerCategory = explode(" ", $cat);
                }
            }
        }

        /*if (in_array("general", $learnerCategory)) {
        foreach ($learnerCategory as $k => $v) {
        if ($v == "general") {
        unset($learnerCategory[$k]);
        }
        }
        }*/
        $org_id = 0;
        if ($current_role == "Learner") {
            $classEnrolledUser = $wpdb->get_results("SELECT * FROM wp_postmeta WHERE
			meta_key = 'YunoClassPrivateLearners'
			and (meta_value REGEXP '[[:<:]]" . $userId . "[[:>:]]')", ARRAY_A);
            if (count($classEnrolledUser) > 0) {
                $hasEnrolledInClassBefore = true;
            } else {
                $hasEnrolledInClassBefore = false;
            }
            date_default_timezone_set('Asia/Kolkata');
            $currentDate = date("Y-m-d H:i:s");
            $enrollmentData = [];
            $enrollmentFalseStatusData = [];
            $data = $wpdb->get_results("SELECT id,product_db_id, enrollment_end_date, enrollment_status FROM wp_enrollment WHERE user_id='" . $userId . "'", ARRAY_A);
            foreach ($data as $key => $value) {
                if ($currentDate < $value['enrollment_end_date']) {
                    $enrollmentData[] = [
                        "course_id" => $value['product_db_id'],
                        "id" => $value['id'],
                        //"enrollment_status" => "active"
                    ];
                }
            }
            // $user_registration_org_url = get_user_meta($userId, 'user_registration_org_url', true);
            // if (!empty($user_registration_org_url)) {
            //     $isSignup = "completed";
            // }

            // get active category
            $active_category = get_user_meta($userId, 'active_category', true);

            // Initialize $active_category_obj with default values
            $active_category_obj = [
                'id' => 0,
                'slug' => '',
                'label' => '',
            ];

            if (!empty($active_category)) {
                // Assume $taxonomy is your custom taxonomy name, and $slug is the slug of the category you're interested in
                $taxonomy = 'course_category';

                // Get the term object by slug
                $term = get_term_by('slug', $active_category, $taxonomy);

                // Check if the term exists and there are no errors
                if ($term !== false && !is_wp_error($term)) {
                    // Update $active_category_obj with the term's details
                    $active_category_obj = [
                        'id' => (int)$term->term_id,
                        'slug' => $term->slug,
                        'label' => $term->name,
                    ];
                }
            }
            // get category belongs to logged in learner
            if (count($enrollmentData) > 0) {
                $category = [];
                foreach ($enrollmentData as $key => $value) {
                    $categories = wp_get_post_terms((int) $value['course_id'], 'course_category');
                    foreach ($categories as $k => $v) {
                        if ($v->parent == 0) {
                            $category[] = strtolower($v->slug);
                        }
                    }
                }

                $userData = [
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == 1 ? true : false,
                    'ins_meet_permission' => get_user_meta($userId, 'ins_meet_permission', true) == 1 ? true : false,
                    'profile_name' => $user_nicename,
                    "mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
                    //"category_url" => !empty(get_user_meta($userId, 'Category_URL_For_Signup', true)) ? str_replace("/", "", get_user_meta($userId, 'Category_URL_For_Signup', true)) : [],
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => true,
                    //'category' => $category,
                    'isInvited' => $isInvited,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj
                ];
            } else {
                $enrollmentStatus = "INACTIVE";
                $update_status = [
                    'enrollment_status' => $enrollmentStatus,
                ];
                foreach ($data as $key => $value) {
                    if ($currentDate > $value['enrollment_end_date']) {
                        $enrollmentFalseStatusData[] = [
                            "id" => $value['id'],
                            //"enrollment_status" => "active"
                        ];
                    }
                }
                if (count($enrollmentFalseStatusData) > 0) {
                    foreach ($enrollmentFalseStatusData as $key => $value) {
                        $wpdb->update('wp_enrollment', $update_status, ['id' => $value['id']]);
                    }
                }
                $userData = [
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == 1 ? true : false,
                    'ins_meet_permission' => get_user_meta($userId, 'ins_meet_permission', true) == 1 ? true : false,
                    'profile_name' => $user_nicename,
                    "mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
                    //"category_url" => !empty(get_user_meta($userId, 'Category_URL_For_Signup', true)) ? str_replace("/", "", get_user_meta($userId, 'Category_URL_For_Signup', true)) : [],
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => false,
                    //'category' => [],
                    'isInvited' => $isInvited,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj
                ];
            }
        } else {
            if (in_array('um_instructor', $userdata->roles)) {
                $is_completed_step_3 = get_user_meta($userId, 'is_completed_step_3', true);
                if ($is_completed_step_3) {
                    if ($is_completed_step_3 == "yes") {
                        $isSignup = "completed";
                    } else {
                        $isSignup = "pending";
                    }
                } else {
                    $isSignup = "completed";
                }
            }
            if (in_array('um_org-admin', $userdata->roles)) {
                $org_array = get_user_meta($userId, 'organisation', true);
                $org_id = (int) $org_array[0] ?? 0;
            }
            $userData = [
                "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                'email' => $user_email,
                'role' => $current_role,
                'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == "true" ? true : false,
                //'ins_meet_permission' => get_user_meta($userId,'ins_meet_permission',true) == "true" ? true :false,//old
                'ins_meet_permission' => get_user_meta($userId, 'zoom_user_status', true) == "free" ? false : true, //apply to all existing & old instructors
                'ins_meet_permission_verified' => get_user_meta($userId, 'ins_meet_permission_verified', true) == "true" ? true : true,
                'profile_name' => $user_nicename,
                "mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
                //"category_url" => !empty(get_user_meta($userId, 'Category_URL_For_Signup', true)) ? str_replace("/", "", get_user_meta($userId, 'Category_URL_For_Signup', true)) : [],
                "signup_category" => array_values($learnerCategory),
                "is_signup_completed" => $isSignup,
                "is_lead_created" => true,
                'isInvited' => $isInvited,
                'org_id' => $org_id,
                'account_status' => get_user_meta($userId, 'account_login_status', true),
                'withoutCategory' => $withoutCategory,
                'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
            ];
        }

        if (!empty($hasWebinarEnrolled)) {
            $userData = array_merge($userData, ["hasWebinarEnrolled" => $hasWebinarEnrolled]);
        }

        $response = array(
            'code' => $codes["GET_SUCCESS"]["code"],
            'message' => 'User info found',
            "data" => $userData
        );
        //add_cache('user_info', $response, '', 300);
        track_logs(time(), "user_info", '', time(), "end", 'user');
        return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    }
    /**
     * Getting city name by id
     */
    public function get_city($request)
    {
        $city_id = (int) $request['city_id'];
        $codes = error_code_setting();
        global $wpdb;
        $resultCollections = [];
        $table_name_1 = $wpdb->prefix . "cities";
        $resultCollections = [];
        $cities = $wpdb->get_results("SELECT * FROM $table_name_1 where id=$city_id");
        foreach ($cities as $city) {
            $resultCollections = ['id' => $city->id, 'name' => $city->name, 'country_code' => $city->country_code];
        }
        $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'city(s) are found', 'data' => $resultCollections);
        return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
    }
    /**
     * Getting address on the basis of criteria
     * Criteria: Short/Detail
     */
    public function getting_address($request)
    {
        $criteria = $request['criteria'];
        $codes = error_code_setting();
        $user_id = $request['user_id'];
        global $wpdb;
        $userExist = $wpdb->get_results("SELECT ID from wp_users where ID=$user_id", ARRAY_A);
        if ($user_id == '' || $user_id == 0 || count($userExist) == 0) {
            return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));
        }

        if ($criteria == 'detail') {
            $country = get_user_meta($user_id, 'yuno_user_address_country', true);
            $pin_code = get_user_meta($user_id, 'yuno_user_address_pin_code', true);
            $house = get_user_meta($user_id, 'yuno_user_address_flat_house_number', true);
            $street = get_user_meta($user_id, 'yuno_user_address_street', true);
            $landmark = get_user_meta($user_id, 'yuno_user_address_landmark', true);
            $city = get_user_meta($user_id, 'yuno_user_address_city', true);
            $state = get_user_meta($user_id, 'yuno_user_address_state', true);
            $address_type = get_user_meta($user_id, 'yuno_user_address_type', true);

            if (
                $country == "" && $pin_code == "" && $house == "" && $street == ""
                && $landmark == "" && $city == "" && $state == "" && $address_type == ""
            ) {
                return new WP_Error($codes["GET_FAIL"]["code"], 'Address not found', array('status' => $codes["GET_FAIL"]["status"]));
            }
            $cityData['city_id'] = $city;
            $city_name = $this->get_city($cityData);
            $cityName = $city_name->data['data']['name'];
            $addressData = [
                "country" => $country,
                "pin_code" => $pin_code,
                "flat_house_number" => $house,
                "street" => $street,
                "landmark" => $landmark,
                "city" => $cityName,
                "state" => $state,
                "address_type" => $address_type,
            ];
            $otherdetails = [];
            if (filter_var($city, FILTER_VALIDATE_INT) === true) {
                $cityExist = $wpdb->get_row("SELECT wp_cities.name  as city_name,wp_states.`name`  as state_name,wp_countries.iso2,wp_countries.phonecode,wp_countries.currency ,wp_countries.timezones FROM `wp_countries` left join wp_states on wp_states.country_id=wp_countries.id left join wp_cities on wp_cities.country_id=wp_countries.id where wp_cities.id=$city");
                $curl = curl_init();

                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://app.zipcodebase.com/api/v1/code/city?city=' . $cityExist->city_name . '&state_code=' . $cityExist->state_name . '&country=' . $cityExist->iso2,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        ': 02e2ba90-646d-11eb-b55d-754dae387822',
                        'apikey: 02e2ba90-646d-11eb-b55d-754dae387822',
                        'Cookie: __cfduid=d86db6af94e19c543d4e1fea7329353db1612170655',
                    ),
                ));

                $postal_code = json_decode(curl_exec($curl));
                curl_close($curl);

                $timezones = json_decode($cityExist->timezones);
                foreach ($timezones as $timezone) {
                    $zones = ["zoneName" => $timezone->zoneName, "gmtOffsetName" => $timezone->gmtOffsetName];
                }
                $postals = $postal_code->results;
                foreach ($postals as $postal) {
                    $zip_code = $postal;
                }
                $otherdetails = ["country_calling_code" => $cityExist->phonecode, "currency" => $cityExist->currency, "timezones" => $zones, "postal_code" => $zip_code];
            }

            $response = array(
                'code' => $codes["GET_SUCCESS"]["code"],
                'message' => 'Address detail found',
                "data" => $addressData
            );
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else if ($criteria == 'short') {
            $country = get_user_meta($user_id, 'yuno_user_address_country', true);
            $city = get_user_meta($user_id, 'yuno_user_address_city', true);
            $state = get_user_meta($user_id, 'yuno_user_address_state', true);

            if ($country == "" && $city == "" && $state == "") {
                return new WP_Error($codes["GET_FAIL"]["code"], 'Address not found', array('status' => $codes["GET_FAIL"]["status"]));
            }
            $cityData['city_id'] = $city;
            $city_name = $this->get_city($cityData);
            $cityName = $city_name->data['data']['name'];
            $addressData = [
                "country" => $country,
                "city" => $cityName,
                "state" => $state,
            ];

            $response = array(
                'code' => $codes["GET_SUCCESS"]["code"],
                'message' => 'Short address found',
                "data" => $addressData
            );
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please use criteria as detail/short', array('status' => $codes["GET_FAIL"]["status"]));
        }
    }

    public function get_user_profile($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $criteria = $request['criteria'];
        $parms = json_decode($request->get_body());
        (int) $user_id = $request['userID'] ? $request['userID'] : '';
        $userdata = get_userdata($user_id);
        if (empty($userdata)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'No user detail found', array('status' => $codes["GET_FAIL"]["status"]));
        }
        // $userCacheData = get_cache("get_user_profile_".$user_id);
        // if (!empty($userCacheData)) {
        //     return new WP_REST_Response($userCacheData, 200);
        // }
        // $nonce = $request['X_WP_Nonce'] ?? '';
        // $nonce = wp_create_nonce( 'wp_rest' );
        // if (! wp_verify_nonce($nonce, 'wp_rest')){
        //     return new WP_Error('rest_security_error', 'Invalid token.', array('status' => 403));
        // }
        $user_email = $userdata->data->user_email;
        $user_nicename = $userdata->data->user_nicename;
        $user_registered = $userdata->data->user_registered;
        $current_role = '';
        if ($userdata->roles) {
            if (in_array('um_instructor', $userdata->roles)) {
                $current_role = 'Instructor';
            } else if (in_array('um_counselor', $userdata->roles)) {
                $current_role = 'Counselor';
            } else {
                $current_role = 'Learner';
            }
        }

        $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
        if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
            $isSignup = "completed";
        } else {
            $isSignup = "pending";
        }

        $requestedData = ["user_id" => $user_id, "criteria" => 'detail'];
        $address = $this->getting_address($requestedData);
        $addressData = $address->data['data'];
        if (isset($address->errors)) {
            $address_details = [
                "country" => "",
                "pin_code" => "",
                "flat_house_number" => "",
                "street" => "",
                "landmark" => "",
                "city" => "",
                "state" => "",
                "address_type" => "",
            ];
        } else {
            $address_details = $addressData;
        }
        $fluent_in = get_user_meta($user_id, 'Fluent_In', true);
        $understand = get_user_meta($user_id, 'Instructor_Understand', true);
        if (strlen($fluent_in) > 0) {
            $fluent = explode(',', $fluent_in);
        } else {
            $fluent = [];
        }

        if (strlen($understand) > 0) {
            $understanding = explode(',', $understand);
        } else {
            $understanding = [];
        }

        $userCategory = get_user_meta($user_id, 'Category_URL_For_Signup', true);
        if (!is_array($userCategory)) {
            $signupCategory[] = str_replace("/", "", $userCategory);
        } else {
            $signupCategory = $userCategory;
        }
        if ($criteria == "instructor") {
            $user_whatsapp_check = get_user_meta($user_id, 'yuno_user_whatsapp_check', true);
            if ($user_whatsapp_check == "yes") {
                $yuno_user_whatsapp_check = true;
            } else {
                $yuno_user_whatsapp_check = false;
            }
            $city_id = get_user_meta($user_id, 'yuno_user_address_city', true);

            /*$city = $wpdb->query($wpdb->prepare("SELECT name FROM wp_cities where id=$city_id"));*/
            $escaped_city_id = $wpdb->prepare('%s', $city_id);
            // Query the database to retrieve the row
            $cityQuery = "SELECT name FROM {$wpdb->prefix}cities
			        WHERE id = $escaped_city_id";
            $city = $wpdb->get_row($cityQuery, ARRAY_A);

            /*$city = $wpdb->get_row( "SELECT name FROM wp_cities where id=$city_id");*/

            $fields1 = [
                "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                "email" => get_user_meta($user_id, 'yuno_gplus_email', true),
                "country" => get_user_meta($user_id, 'yuno_user_address_country', true),
                "state" => get_user_meta($user_id, 'yuno_user_address_state', true),
                "city" => !empty($city->name) ? $city->name : '',
                "phone" => get_user_meta($user_id, 'yuno_gplus_mobile', true),
                "yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
                "laptop_availability" => get_user_meta($user_id, 'yuno_user_laptop_availability', true),
                "broadband_connection_availability" => get_user_meta($user_id, 'yuno_user_broadband_connection_availability', true),
                "online_teaching_exp" => get_user_meta($user_id, 'yuno_user_online_teaching_exp', true),
                "dob" => get_user_meta($user_id, 'yuno_user_dob', true),
                "understand" => $understanding,
                "fluent_in" => $fluent
            ];
            $step_1_status = get_user_meta($user_id, 'is_completed_step_1', true);
            if ($step_1_status == "yes") {
                $step1status = true;
            } else {
                $step1status = false;
            }
            $step1 = ["type" => "one", "is_step_completed" => $step1status, "fields" => $fields1];

            $can_teach = get_user_meta($user_id, 'can_teach', true);
            $teaching_preference = get_user_meta($user_id, 'yuno_user_teaching_preference', true);
            $fields2 = [
                "can_teach" => is_array($can_teach) ? explode("", $can_teach) : [],
                "teaching_preference" => is_array($teaching_preference) ? explode("", $teaching_preference) : []
            ];
            $step_2_status = get_user_meta($user_id, 'is_completed_step_2', true);
            if ($step_2_status == "yes") {
                $step2status = true;
            } else {
                $step2status = false;
            }
            $step2 = ["type" => "two", "is_step_completed" => $step2status, "fields" => $fields2];

            $step_3_status = get_user_meta($user_id, 'is_completed_step_3', true);
            if ($step_3_status == "yes") {
                $step3status = true;
            } else {
                $step3status = false;
            }
            $step3 = ["type" => "three", "is_step_completed" => $step3status, "fields" => null];
            $steps = [$step1, $step2, $step3];
            $user_detail = array("about" => get_user_meta($user_id, 'Instructor_About', true), "steps" => $steps);
        } else {
            $instructor_profile_url = get_permalink(get_user_meta($user_id, 'profile_user_id_reference', true));
            if (!$instructor_profile_url) {
                $profile_url = site_url() . "/profile/" . strtolower(get_user_meta($user_id, 'yuno_first_name', true)) . "-" . strtolower(get_user_meta($user_id, 'yuno_last_name', true));
            } else {
                $profile_url = $instructor_profile_url;
            }
            $user_detail = array(
                //'email' => $user_email,
                'registered_on' => date('F Y', strtotime($user_registered)),
                'first_name' => get_user_meta($user_id, 'yuno_first_name', true),
                'last_name' => get_user_meta($user_id, 'yuno_last_name', true),
                'profile_img' => get_user_meta($user_id, 'googleplus_profile_img', true),
                'yuno_display_name' => get_user_meta($user_id, 'yuno_display_name', true),
                //'mobile' => get_user_meta($user_id, 'yuno_gplus_mobile', true),
                'counselor_name' => get_user_meta($user_id, 'Counselor_Name', true),
                'about' => get_user_meta($user_id, 'Instructor_About', true),
                'fluent_in' => $fluent,
                'profile_url' => $profile_url,
                "laptop_availability" => get_user_meta($user_id, 'yuno_user_laptop_availability', true),
                "broadband_connection_availability" => get_user_meta($user_id, 'yuno_user_broadband_connection_availability', true),
                "online_teaching_exp" => get_user_meta($user_id, 'yuno_user_online_teaching_exp', true),
                "dob" => get_user_meta($user_id, 'yuno_user_dob', true),
                'address' => $address_details,
                'understand' => $understanding,
                "last_login" => date("D, M j, Y", strtotime(get_user_meta($user_id, 'Yuno_Last_Login_Time', true))),
                'experience' => get_user_meta($user_id, 'Instructor_Experience', true),
                'profile_name' => $user_nicename,
                //'mobile' => get_user_meta($user_id, 'yuno_gplus_mobile', true),
                'category_url' => $signupCategory,
                'is_signup_completed' => $isSignup,
                'is_lead_created' => true,
                'roles' => $userdata->roles ? $userdata->roles : '',
                'capabilities' => get_user_meta($user_id, 'wp_capabilities', true),
                "account_status" => get_user_meta($user_id, 'account_login_status', true),
                "age" => date_diff(date_create(get_user_meta($user_id, 'yuno_user_dob', true)), date_create('today'))->y,
                'current_role' => $current_role,
            );
        }

        $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User detail found', 'data' => $user_detail);
        //add_cache("get_user_profile_".$user_id, $result, '', 3600);
        return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
    }

    public function get_user_profile_permissions_check()
    {
        return true;
    }
    /**
     * Get list of learner
     */
    public function get_learner_list($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $type = (string) $request['role'];
        if ($type == "org-admin") {
            $role__in = array('um_org-admin');
        } else {
            $role__in = array('SEO Manager');
        }
        $query_string = $request->get_param('search');
        if (strlen($query_string) < 3) {
            return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));
        }

        $typeCollectionArray = array("learner", "org-admin");
        if (!in_array($type, $typeCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the user role', array('status' => $codes["GET_FAIL"]["status"]));
        }
        $learner_array = array();
        if (isset($query_string) && $query_string == "all") {
            $search_params = [];
            $users = UserElasticSearch::get_signedup("search_all_users_info_from_es", $search_params);
            if (!empty($users)) {
                foreach ($users->hits->hits as $result) {
                    $learner_array[] = [
                        "id" => $result->_source->data->details->user_id,
                        "name" => $result->_source->data->details->user->name,
                        "email" => $result->_source->data->details->user->email,
                        "name_email" => $result->_source->data->details->user->name . " (" . $result->_source->data->details->user->email . ")",
                    ];
                }
            }
        } else {
            if ($query_string == trim($query_string) && strpos($query_string, ' ') !== false) {
                $ary = explode(' ', $query_string);
                $search_params = ["first_name" => $ary[0], "last_name" => $ary[1]];
                $users = UserElasticSearch::get_signedup("search_string_users_info_from_es", $search_params);
                if (!empty($users)) {
                    foreach ($users->hits->hits as $result) {
                        $learner_array[] = [
                            "id" => $result->_source->data->details->user_id,
                            "name" => $result->_source->data->details->user->name,
                            "email" => $result->_source->data->details->user->email,
                            "name_email" => $result->_source->data->details->user->name . " (" . $result->_source->data->details->user->email . ")",
                        ];
                    }
                }
            } else {
                $search_params = ["name" => $query_string];
                $users = UserElasticSearch::get_signedup("search_name_users_info_from_es", $search_params);
                if (!empty($users)) {
                    foreach ($users->hits->hits as $result) {
                        $learner_array[] = [
                            "id" => $result->_source->data->details->user_id,
                            "name" => $result->_source->data->details->user->name,
                            "email" => $result->_source->data->details->user->email,
                            "name_email" => $result->_source->data->details->user->name . " (" . $result->_source->data->details->user->email . ")",
                        ];
                    }
                } else {
                    $search_params = ["phone" => $query_string];
                    $user_names = UserElasticSearch::get_signedup("search_phone_users_info_from_es", $search_params);
                    if (!empty($user_names)) {
                        foreach ($user_names->hits->hits as $result) {
                            $learner_array[] = [
                                "id" => $result->_source->data->details->user_id,
                                "name" => $result->_source->data->details->user->name,
                                "email" => $result->_source->data->details->user->email,
                                "name_email" => $result->_source->data->details->user->name . " (" . $result->_source->data->details->user->email . ")",
                            ];
                        }
                    }
                }
            }
        }
        if (!empty($learner_array)) {
            $response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User data found', 'data' => $learner_array);
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else {
            return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["code"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));
        }
    }

   // Define a function to fetch insights about users based on provided request filters
	// public function get_users_insights($request)
	// {
	// 	global $Utility; // Access a global utility object for general operations like logging
	// 	$codes = error_code_setting(); // Retrieve predefined error codes settings, presumably for consistent error handling across the application

	// 	// Extract parameters from the request object
	// 	$data = $request->get_params();

	// 	// Log the initiation of the insights fetching process
	// 	$Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_users_insights", $message = "start", $request);

	// 	// Define all possible filters that can be applied to the user insights search
	// 	$filters = [ "time_of_study", "whatsapp", "created_time", "last_login", "term_of_service", "yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"];

	// 	// Specify filters that apply at the root level of the search criteria
	// 	$root_filter_keys = ["time_of_study", "role"];
	// 	// Filters applicable to specific user details
	// 	$filter_keys = ["whatsapp", "term_of_service"];
	// 	// Date range filters for creation and last login times
	// 	$date_ranges = ["created_time", "last_login"];

    //     $utm_filters_keys = ["yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"]; // Marketing-related filters (UTM parameters).


	// 	// Define options for each filter to validate or process request data
	// 	$time_of_study = ["morning", "afternoon", "evening", "night"];
	// 	// $role = ["all", "learner", "instructor", "org-admin", "content-admin", "counselor"];
	// 	$view = ["list-view", "grid-view"]; // Presumably options for how the fetched insights might be displayed
	// 	$time = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"]; // Unified time options for created_time and last_login filters
	// 	$bool = ["all", "true", "false"]; // Boolean options for filters that expect a boolean value

	// 	// Actions that can be taken with the insights, though its usage is not directly seen in the provided code
	// 	$actions = ["user_history"];

	// 	// Initialize arrays to hold processed data for filtering
	// 	$ranges = [];
	// 	$root_filters = [];
	// 	$basic_details_filters = [];

	// 	// Iterate over request data to apply and process filters
	// 	foreach ($data as $key => $value) {
	// 		// Return an error if a required filter value is missing
	// 		if ($value == "" && $key != "state") {
	// 			$msg = $key . " can not be empty";
	// 			return new WP_Error($codes["POST_INSERT_FAIL"]["code"], $msg, $codes["POST_INSERT_FAIL"]["status"]);
	// 		} elseif (!is_array($value) && strtolower($value) == "all") { // Normalize "all" filter values to empty strings for processing
	// 			$value = "";
	// 		}
	// 		// Apply root level filters
	// 		if (in_array($key, $root_filter_keys)) {
	// 			$root_filters[$key] = $value;
	// 		}

    //         elseif (in_array($key, $utm_filters_keys)) {
    //             $data['utm_filters'][$key] = $value;
    //         }
	// 		// Process specific user detail filters
	// 		if (in_array($key, $filter_keys)) {
	// 			if ($key == "whatsapp") {
	// 				// Map the 'whatsapp' filter to a specific field in the user details
	// 				$basic_details_filters["yuno_user_whatsapp_check"] = $value;
	// 			} elseif ($key == "term_of_service") {
	// 				// Similarly map 'term_of_service' to its corresponding field
	// 				$basic_details_filters["privacy_policy_terms_of_service"] = $value;
	// 			} else {
	// 				$basic_details_filters[$key] = $value;
	// 			}
	// 		}
	// 		// Handle date range filters by converting time periods to timestamps
	// 		if (in_array($key, $date_ranges)) {
	// 			if ($value == "24-hours") {
	// 				$value = "1-days"; // Normalize "24-hours" to "1-days" for consistency
	// 			}
	// 			$val = str_replace('-days', ' days', $value);
	// 			$val = date("Y-m-d H:i:s", strtotime("-{$val}"));
	// 			if ($key == "created_time") {
	// 				$ranges["registration_date"] = $val;
	// 			}
	// 			if ($key == "last_login") {
	// 				$ranges["last_login_time"] = $val;
	// 			}
	// 		}
	// 	}

	// 	// Assign processed filters back to the data array for use in the search
	// 	$data['root_filters'] = $root_filters;
	// 	$data['basic_details_filters'] = $basic_details_filters;
	// 	$data['filters'] = $filters;
	// 	$data['ranges'] = $ranges;
	// 	$data['actions'] = $actions;
	// 	// Assign filter options directly to the data array for ease of access
	// 	$data['filter']['time_of_study'] = $time_of_study;
	// 	$data['filter']['view'] = $view;
	// 	$data['filter']['created_time'] = $time;
	// 	$data['filter']['last_login'] = $time;
	// 	$data['filter']['whatsapp'] = $bool;
	// 	$data['filter']['term_of_service'] = $bool;

	// 	// Perform the user insights search using the processed filters
	// 	$UserElasticSearch = new UserElasticSearch;
	// 	$user_exist_row = $UserElasticSearch->get_learner_insights($data);

	// 	// Log the completion of the insights fetching process
	// 	$Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_users_insights", $message = "end", $user_exist_row);

	// 	// Output the fetched insights as a JSON-encoded string
	// 	echo json_encode($user_exist_row);
	// 	return;
	// }

    public function get_users_insights($request)
    {
        global $Utility; // Access a global utility object for general operations like logging
        $codes = error_code_setting(); // Retrieve predefined error codes settings, presumably for consistent error handling across the application

        // Extract parameters from the request object
        $data = $request->get_params();

        // Log the initiation of the insights fetching process
        $Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_users_insights", $message = "start", $request);

        // Define all possible filters that can be applied to the user insights search
        $filters = ["time_of_study", "whatsapp", "created_time", "last_login", "term_of_service", "YL_campaign", "YL_medium", "YL_keyword", "YL_lead_source", "YL_ad_content", "YL_ad_group"];

        // Specify filters that apply at the root level of the search criteria
        $root_filter_keys = ["time_of_study", "role"];
        // Filters applicable to specific user details
        $filter_keys = ["whatsapp", "term_of_service"];
        // Date range filters for creation and last login times
        $date_ranges = ["created_time", "last_login"];

        $utm_filters_keys = ["YL_campaign", "YL_medium", "YL_keyword", "YL_keyword", "YL_lead_source", "YL_ad_content", "YL_ad_group"];

        // Define options for each filter to validate or process request data
        $time_of_study = ["morning", "afternoon", "evening", "night"];
        // $role = ["all", "learner", "instructor", "org-admin", "content-admin", "counselor"];
        $view = ["list-view", "grid-view"]; // Presumably options for how the fetched insights might be displayed
        $time = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"]; // Unified time options for created_time and last_login filters
        $bool = ["all", "true", "false"]; // Boolean options for filters that expect a boolean value

        // Actions that can be taken with the insights, though its usage is not directly seen in the provided code
        $actions = ["user_history"];

        // Initialize arrays to hold processed data for filtering
        $ranges = [];
        $root_filters = [];
        $basic_details_filters = [];
        $utm_filters = [];

        // Iterate over request data to apply and process filters
        foreach ($data as $key => $value) {
            // Return an error if a required filter value is missing
            if ($value == "" && $key != "state") {
                $msg = $key . " can not be empty";
                return new WP_Error($codes["POST_INSERT_FAIL"]["code"], $msg, $codes["POST_INSERT_FAIL"]["status"]);
            } elseif (!is_array($value) && strtolower($value) == "all") { // Normalize "all" filter values to empty strings for processing
                $value = "";
            }
            // Apply root level filters
            if (in_array($key, $root_filter_keys)) {
                $root_filters[$key] = $value;
            }
            if (in_array($key, $utm_filters_keys)) {
                $utm_filters[$key] = $value;
            }
            // Process specific user detail filters
            if (in_array($key, $filter_keys)) {
                if ($key == "whatsapp") {
                    // Map the 'whatsapp' filter to a specific field in the user details
                    $basic_details_filters["yuno_user_whatsapp_check"] = $value;
                } elseif ($key == "term_of_service") {
                    // Similarly map 'term_of_service' to its corresponding field
                    $basic_details_filters["privacy_policy_terms_of_service"] = $value;
                } else {
                    $basic_details_filters[$key] = $value;
                }
            }
            // Handle date range filters by converting time periods to timestamps
            if (in_array($key, $date_ranges)) {
                if ($value == "24-hours") {
                    $value = "1-days"; // Normalize "24-hours" to "1-days" for consistency
                }
                $val = str_replace('-days', ' days', $value);
                $val = date("Y-m-d H:i:s", strtotime("-{$val}"));
                if ($key == "created_time") {
                    $ranges["registration_date"] = $val;
                }
                if ($key == "last_login") {
                    $ranges["last_login_time"] = $val;
                }
            }
        }

        // Assign processed filters back to the data array for use in the search
        $data['root_filters'] = $root_filters;
        $data['basic_details_filters'] = $basic_details_filters;
        $data['utm_filters'] = $utm_filters;
        $data['filters'] = $filters;
        $data['ranges'] = $ranges;
        $data['actions'] = $actions;
        // Assign filter options directly to the data array for ease of access
        $data['filter']['time_of_study'] = $time_of_study;
        $data['filter']['view'] = $view;
        $data['filter']['created_time'] = $time;
        $data['filter']['last_login'] = $time;
        $data['filter']['whatsapp'] = $bool;
        $data['filter']['term_of_service'] = $bool;

        // Perform the user insights search using the processed filters
        $UserElasticSearch = new UserElasticSearch;
        $user_exist_row = $UserElasticSearch->get_learner_insights($data);

        // Log the completion of the insights fetching process
        $Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_users_insights", $message = "end", $user_exist_row);

        // Output the fetched insights as a JSON-encoded string
        // echo json_encode($user_exist_row);
        // return;
        return new WP_REST_Response($user_exist_row, $codes["POST_INSERT"]["code"]);
    }


    // Defines a function to fetch demo requests based on various filters from a request object.
    public function get_demo_requests($request)
    {
        // Retrieve parameters from the request object.
        global $Utility;
        $data = $request->get_params();

        $Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_demo_requests", $message = "start", $request);
        // Assign a specific case identifier for the operation, useful for conditional processing later.
        $data['case'] = "get_demo_requests";
        $data['role'] = "learner";
        // Define all possible filters that can be applied to demo requests.
        $filters = ["time_of_study", "cta", "category", "course", "info-available", "demo_request_period", "yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"];

        // Categorize filters for specific processing needs.
        $root_filters_keys = ["time_of_study", "role"]; // Filters that apply at the top-level query.
        $cta_keys = ["cta"]; // Filters related to call-to-action.
        $cat_filters_keys = ["category", "course"]; // Category-specific filters.
        $utm_filters_keys = ["yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"]; // Marketing-related filters (UTM parameters).

        // Explicitly declare that these are the filters to be applied.
        $data['filters'] = $filters;

        // Define possible values for each filter to validate or map request data.
        $date_ranges = ["demo_request_period"];
        $time_of_study = ["morning", "afternoon", "evening", "night"];
        $created = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"];
        // $created = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"];
        $categories = ["all", "ielts", "pte", "english-speaking"];
        $cta = ["all", "book_a_demo"];
        $info_available = ["whatsapp", "phone", "email"];

        // Assign filter options back to the data array for use in filtering logic.
        $data['filter']['time_of_study'] = $time_of_study;
        $data['filter']['demo_request_period'] = $created;
        $data['filter']['category'] = $categories;
        $data['filter']['cta'] = $cta;
        $data['filter']['info-available'] = $info_available;

        // Iterate over each filter in the data array to apply specific logic and categorize filters.
        foreach ($data as $key => $value) {
            // Handle 'info-available' filter to set communication preferences.
            if ($key == "info-available") {
                foreach ($value as $v) {
                    if (strtolower($v) == "all") {
                        // If 'All' is selected, enable all communication methods.
                        $data['user_filters']['phone'] = true;
                        $data['user_filters']['email'] = true;
                        $data['basic_details_filters']['yuno_user_whatsapp_check'] = true;
                        break; // Exit the loop once handled.
                    } else {
                        // Apply specific filters based on the communication method.
                        if ($v == "whatsapp") {
                            $data['basic_details_filters']['yuno_user_whatsapp_check'] = true;
                        } else {
                            $data['user_filters'][$v] = true;
                        }
                    }
                }
            }
            // Reset 'All' value to an empty string for processing.
            if (!is_array($value) && strtolower($value) == "all") {
                $value = "";
                continue;
            }

            // Categorize each filter based on predefined categories for more specific query building.
            if (in_array($key, $root_filters_keys)) {
                $data['root_filters'][$key] = $value;
            } elseif (in_array($key, $cat_filters_keys)) {
                $data['cat_filters'][$key] = $value;
            } elseif (in_array($key, $utm_filters_keys)) {
                $data['utm_filters'][$key] = $value;
            } elseif (in_array($key, $cta_keys)) {
                $data['cta_filters'][$key] = $value;
            } elseif (in_array($key, $date_ranges)) {
                // Convert '24-hours' to '1-day' for consistency and compute the date range.
                if ($value == "24-hours") {
                    $value = "1-days";
                }
                if ($value != "" && $value !== "all") {
                    $val = str_replace('-days', ' days', $value);
                    $val = date("Y-m-d H:i:s", strtotime("-{$val}", strtotime(date("Y-m-d H:i:s"))));
                } else {
                    $val = "";
                }
                // Apply the computed date range filter.
                if ($key == "demo_request_period") {
                    $data['ranges']["updated_at"] = $val;
                }
            }
        }

        // Instantiate the UserElasticSearch class and fetch demo requests based on the processed filters.
        $UserElasticSearch = new UserElasticSearch;
        $demo_requests = $UserElasticSearch->get_learner_demo_requests($data);
        $Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_demo_requests", $message = "end", $demo_requests);
        // Output the fetched demo requests as JSON.
        echo json_encode($demo_requests);
    }

    public function get_demo_request($request)
    {
        // $data = $request->get_params();
        $learner_id = $request['learnerId'];

        $UserElasticSearch  = new UserElasticSearch;
        $demo_request = $UserElasticSearch->get_learner_single_demo_requests($learner_id);
        echo json_encode($demo_request);
    }

    public function create_instructor_working_hours($request)
    {
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Kolkata');
        $resource = $request['resource'];
        if ($resource !== 'instructor') {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'Resource not allowed');
            return new WP_Error($codes["GET_FAIL"]["code"], "Resource not allowed", array('status' => $codes["GET_FAIL"]["status"]));
        }
        $instructor_id = (int)$request['resource_id'];
        $request = json_decode($request->get_body(), true);
        $userdata = get_userdata($instructor_id);
        if ($userdata == false || $userdata->roles[0] != "um_instructor") {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Instructor doesn’t exist", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($instructor_id)) {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'No instructor found');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $weeks = $request['weeks'];
        $default_days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $working_hours = [];
        // $time_pattern = "/^(?:[01]\d|2[0-3]):[0-5]\d$/";
        $time_pattern = "/^(?:[01]\d|2[0-3]):[0-5]\d$|^24:00$/";
        $valid_days_lowercase = array_map('strtolower', $default_days);

        foreach ($weeks as $week) {
            $day_lowercase = strtolower($week['day']);
            if (!in_array($day_lowercase, $valid_days_lowercase)) {
                $response = array(
                    'code' => $codes["GET_FAIL"]["code"],
                    'message' => 'Invalid day: ' . $week['day'],
                    'data' => array('status' => $codes["GET_FAIL"]["status"])
                );
                return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
            }

            foreach ($week['availability'] as $availabilitySlot) {
                if (!preg_match($time_pattern, $availabilitySlot['start']) || !preg_match($time_pattern, $availabilitySlot['end'])) {
                    $response = array(
                        'code' => $codes["GET_FAIL"]["code"],
                        'message' => 'Invalid time format for start or end',
                        'data' => array('status' => $codes["GET_FAIL"]["status"])
                    );
                    return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
                }
                if ($availabilitySlot['start'] >= $availabilitySlot['end']) {
                    $response = array(
                        'code' => $codes["GET_FAIL"]["code"],
                        'message' => 'Start time must be before end time',
                        'data' => array('status' => $codes["GET_FAIL"]["status"])
                    );
                    return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
                }
            }
        }

        foreach ($default_days as $default_day) {
            $day_data = array_filter($weeks, function ($week) use ($default_day) {
                return $week['day'] === $default_day;
            });

            $day_data = reset($day_data);

            $slots = [];

            for ($hour = 0; $hour < 24; $hour++) {
                for ($minute = 0; $minute < 60; $minute += 30) {
                    $end_minute = $minute + 30;
                    $end_hour = $hour;
                    if ($end_minute >= 60) {
                        $end_minute -= 60;
                        $end_hour += 1;
                    }
                    $start_time = sprintf("%02d:%02d", $hour, $minute);
                    $end_time = sprintf("%02d:%02d", $end_hour, $end_minute);
                    $slots[] = [
                        "starttime" => $start_time,
                        "endtime" => $end_time,
                        "status" => false
                    ];
                }
            }

            if ($day_data && !$day_data['isDayOff']) {
                foreach ($day_data['availability'] as $availabilitySlot) {
                    $start_time = $availabilitySlot['start'];
                    $end_time = $availabilitySlot['end'];
                    $status = !$availabilitySlot['is24Hours'];

                    foreach ($slots as &$slot) {
                        if ($slot['starttime'] >= $start_time && $slot['endtime'] <= $end_time) {
                            $slot['status'] = $status;
                        }
                    }
                }
            }

            $working_hours[] = [
                "day" => $default_day,
                "slots" => $slots
            ];
        }

        $param = "instructorsignedup/_update/instructorsignedup-" . $instructor_id;
        $es_url = ELASTIC_SEARCH_END_URL . "/" . $param;

        $es_data = [
            'doc' => [
                'data' => [
                    'details' => [
                        'working_hours' => $working_hours
                    ]
                ]
            ]
        ];

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $es_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($es_data),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = array('code' => $codes["POST_INSERT"]["code"], 'message' => 'Data has been saved');

        return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
    }

    public function update_instructor_working_hours($request)
    {
        $codes = error_code_setting();
        date_default_timezone_set('Asia/Kolkata');
        $resource = $request['resource'];
        if ($resource !== 'instructor') {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'Resource not allowed');
            return new WP_Error($codes["GET_FAIL"]["code"], "Resource not allowed", array('status' => $codes["GET_FAIL"]["status"]));
        }
        $instructor_id = (int)$request['resource_id'];
        $request = json_decode($request->get_body(), true);
        $userdata = get_userdata($instructor_id);
        if ($userdata == false || $userdata->roles[0] != "um_instructor") {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Instructor doesn’t exist", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($instructor_id)) {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'No instructor found');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $weeks = $request['weeks'];
        $default_days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $working_hours = [];
        // $time_pattern = "/^(?:[01]\d|2[0-3]):[0-5]\d$/";
        $time_pattern = "/^(?:[01]\d|2[0-3]):[0-5]\d$|^24:00$/";
        $valid_days_lowercase = array_map('strtolower', $default_days);

        foreach ($weeks as $week) {
            $day_lowercase = strtolower($week['day']);
            if (!in_array($day_lowercase, $valid_days_lowercase)) {
                $response = array(
                    'code' => $codes["GET_FAIL"]["code"],
                    'message' => 'Invalid day: ' . $week['day'],
                    'data' => array('status' => $codes["GET_FAIL"]["status"])
                );
                return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
            }

            foreach ($week['availability'] as $availabilitySlot) {
                if (!preg_match($time_pattern, $availabilitySlot['start']) || !preg_match($time_pattern, $availabilitySlot['end'])) {
                    $response = array(
                        'code' => $codes["GET_FAIL"]["code"],
                        'message' => 'Invalid time format for start or end',
                        'data' => array('status' => $codes["GET_FAIL"]["status"])
                    );
                    return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
                }
                if ($availabilitySlot['start'] >= $availabilitySlot['end']) {
                    $response = array(
                        'code' => $codes["GET_FAIL"]["code"],
                        'message' => 'Start time must be before end time',
                        'data' => array('status' => $codes["GET_FAIL"]["status"])
                    );
                    return new WP_REST_Response($response, $codes["GET_FAIL"]["status"]);
                }
            }
        }

        foreach ($default_days as $default_day) {
            $day_data = array_filter($weeks, function ($week) use ($default_day) {
                return $week['day'] === $default_day;
            });

            $day_data = reset($day_data);
            $slots = [];

            for ($hour = 0; $hour < 24; $hour++) {
                for ($minute = 0; $minute < 60; $minute += 30) {
                    $end_minute = $minute + 30;
                    $end_hour = $hour;
                    if ($end_minute >= 60) {
                        $end_minute -= 60;
                        $end_hour += 1;
                    }
                    $start_time = sprintf("%02d:%02d", $hour, $minute);
                    $end_time = sprintf("%02d:%02d", $end_hour, $end_minute);
                    $slots[] = [
                        "starttime" => $start_time,
                        "endtime" => $end_time,
                        "status" => false
                    ];
                }
            }

            if ($day_data && !$day_data['isDayOff']) {
                foreach ($day_data['availability'] as $availabilitySlot) {
                    $start_time = $availabilitySlot['start'];
                    $end_time = $availabilitySlot['end'];
                    $status = !$availabilitySlot['is24Hours'];

                    foreach ($slots as &$slot) {
                        if (($slot['starttime'] >= $start_time && $slot['starttime'] < $end_time) ||
                            ($slot['endtime'] > $start_time && $slot['endtime'] <= $end_time) ||
                            ($slot['starttime'] <= $start_time && $slot['endtime'] >= $end_time)
                        ) {
                            $slot['status'] = $status;
                        }
                    }
                }
            }

            $working_hours[] = [
                "day" => $default_day,
                "slots" => $slots
            ];
        }

        $param_clear = "instructorsignedup/_update/instructorsignedup-" . $instructor_id;
        $es_url_clear = ELASTIC_SEARCH_END_URL . "/" . $param_clear;

        $es_data_clear = [
            'doc' => [
                'data' => [
                    'details' => [
                        'working_hours' => []
                    ]
                ]
            ]
        ];

        $curl_clear = curl_init();

        curl_setopt_array($curl_clear, array(
            CURLOPT_URL => $es_url_clear,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($es_data_clear),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION
            ),
        ));

        $response_clear = curl_exec($curl_clear);

        curl_close($curl_clear);

        // Create new slots
        $param_create = "instructorsignedup/_update/instructorsignedup-" . $instructor_id;
        $es_url_create = ELASTIC_SEARCH_END_URL . "/" . $param_create;

        $es_data_create = [
            'doc' => [
                'data' => [
                    'details' => [
                        'working_hours' => $working_hours
                    ]
                ]
            ]
        ];

        $curl_create = curl_init();

        curl_setopt_array($curl_create, array(
            CURLOPT_URL => $es_url_create,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($es_data_create),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION
            ),
        ));

        $response_create = curl_exec($curl_create);
        curl_close($curl_create);

        // Check if the creation was successful
        if ($response_create === false) {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'Failed to create new working hours');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $response = array('code' => $codes["POST_INSERT"]["code"], 'message' => 'Data has been updated');
        return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
    }

    public function get_instructor_working_hours($request)
    {
        $codes = error_code_setting();
        $instructor_id = (int)$request['resource_id'];
        $resource = $request['resource'];
        if ($resource !== 'instructor') {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'Resource not allowed');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $userdata = get_userdata($instructor_id);
        if ($userdata == false || $userdata->roles[0] != "um_instructor") {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Instructor doesn’t exist", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (empty($instructor_id)) {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'No resource found');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $param = "instructorsignedup/_doc/instructorsignedup-" . $instructor_id;
        $es_url = ELASTIC_SEARCH_END_URL . "/" . $param;

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $es_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . ELASTIC_SEARCH_BASIC_AUTHORIZATION
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        if ($response === false) {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'status' => 'FAIL', 'message' => 'Error retrieving data');
            return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
        }

        $response_data = json_decode($response, true);

        if (isset($response_data['_source']['data']['details']['working_hours'])) {
            $working_hours = $response_data['_source']['data']['details']['working_hours'];
            $response = array('code' => $codes["GET_SUCCESS"]["code"], 'status' => 'SUCCESS', 'data' => $working_hours);
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else {
            $response = array('code' => $codes["GET_FAIL"]["code"], 'message' => 'No working hours found');
            return new WP_Error($codes["GET_FAIL"]["code"], "resources not found", array('status' => $codes["GET_FAIL"]["status"]));
        }
    }


    public function checkInstructorsAreWorking($resource_ids, $start_time, $end_time, $weekdays)
    {
        $codes = error_code_setting();
        $resource_ids = array_map(function ($id) {
            return "instructorsignedup-{$id}";
        }, $resource_ids);

        $start = strtotime($start_time);
        $end = strtotime($end_time);
        $slots = [];

        while ($start < $end) {
            $slot_start = date('H:i', $start);
            $slot_end = date('H:i', strtotime('+30 minutes', $start));
            $slots[] = [
                'starttime' => $slot_start,
                'endtime' => $slot_end
            ];
            $start = strtotime($slot_end);
        }

        $day_clauses = [];

        foreach ($weekdays as $day) {
            $slot_clauses = [];
            foreach ($slots as $slot) {
                $slot_clauses[] = [
                    'nested' => [
                        'path' => 'data.details.working_hours.slots',
                        'query' => [
                            'bool' => [
                                'must' => [
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.starttime.keyword' => $slot['starttime']
                                        ]
                                    ],
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.endtime.keyword' => $slot['endtime']
                                        ]
                                    ],
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.status' => true
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
            }

            $day_clauses[] = [
                'nested' => [
                    'path' => 'data.details.working_hours',
                    'query' => [
                        'bool' => [
                            'must' => array_merge(
                                [
                                    [
                                        'term' => [
                                            'data.details.working_hours.day.keyword' => $day
                                        ]
                                    ]
                                ],
                                $slot_clauses
                            )
                        ]
                    ]
                ]
            ];
        }

        $query = [
            'query' => [
                'bool' => [
                    'must' => array_merge(
                        [
                            [
                                'terms' => [
                                    '_id' => $resource_ids
                                ]
                            ]
                        ],
                        $day_clauses
                    )
                ]
            ],
            '_source' => ['_id']
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/instructorsignedup/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
        } else {
            $response_data = json_decode($response, true);
            if (isset($response_data['hits']['hits']) && !empty($response_data['hits']['hits'])) {
                $working_resource_ids = array_map(function ($hit) {
                    return str_replace('instructorsignedup-', '', $hit['_id']);
                }, $response_data['hits']['hits']);

                $response = array(
                    'status' => 'success',
                    'message' => 'Working hours slots for Resource',
                    'data' => array(
                        'is_working' => true,
                        'resource_ids' => $working_resource_ids
                    )
                );
                return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
            } else {
                $response = array(
                    'status' => 'fail',
                    'message' => 'No resources found'
                );
                return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
            }
        }
    }


    public function getResourceAvailability($request)
    {
        $codes = error_code_setting();
        global $wpdb;

        $resource = $request['resource'];
        if ($resource !== 'instructor') {
            return new WP_Error($codes["GET_FAIL"]["code"], "resource not allowed", ['status' => $codes["GET_FAIL"]["status"]]);
        }

        $request = json_decode($request->get_body(), true);

        $course_id = (int)$request['course_id'];
        $personalisation = $request['personalisation'];
        $start_date = $request['start_date'];
        $end_date = $request['end_date'];
        $class_starttime = $request['class_starttime'];
        $class_endtime = $request['class_endtime'];
        $class_days = $request['class_days'];

        $esQuery = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['data.details.group_type.keyword' => $personalisation]],
                                ['term' => ['data.details.record_id' => $course_id]]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($esQuery),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return new WP_Error($codes["GET_FAIL"]["code"], "Elasticsearch error: $err", ['status' => $codes["GET_FAIL"]["status"]]);
        }

        $responseData = json_decode($response);
        $collection_ids = [];
        foreach ($responseData->hits->hits as $result) {
            $collection_ids = $result->_source->data->details->mapped_instructor_ids;
        }
        $instructor_ids = array_unique($collection_ids);

        $checkIfWorking = $this->checkInstructorsAreWorking($instructor_ids, $class_starttime, $class_endtime, $class_days);
        $response_data = $checkIfWorking->get_data();
        $working_instructors = [];

        if (isset($response_data['status']) && $response_data['status'] === 'success') {
            $working_instructors = $response_data['data']['resource_ids'] ?? [];
        }

        if (empty($working_instructors)) {
            return new WP_Error($codes["GET_FAIL"]["code"], "No resources found", ['status' => $codes["GET_FAIL"]["status"]]);
        }

        $googleHelper = new GoogleCalendarApiV2();
        $final_result = $googleHelper->getAvailabilityOfResourceInCalendar(
            $working_instructors,
            $start_date,
            $end_date,
            $class_starttime,
            $class_endtime,
            $class_days
        );

        if (empty($final_result)) {
            return new WP_Error($codes["GET_FAIL"]["code"], "No resources available in the given date and time range", ['status' => $codes["GET_FAIL"]["status"]]);
        }

        $esQuery = [
            '_source' => [
                'data.details.user_id',
                'data.details.user.name',
                'data.details.user.image'
            ],
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['terms' => ['data.details.user_id' => $final_result]],
                                ['term' => ['data.details.role.keyword' => $resource]]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . count($final_result),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($esQuery),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            return new WP_Error($codes["GET_FAIL"]["code"], "Error while fetching user data: $err", ['status' => $codes["GET_FAIL"]["status"]]);
        }

        $responseData = json_decode($response);
        $resourceList = [];

        foreach ($responseData->hits->hits as $hit) {
            $details = $hit->_source->data->details;
            $resourceList[] = [
                'id' => $details->user_id,
                'name' => $details->user->name,
                'image_URL' => $details->user->image
            ];
        }

        $response = [
            'status' => 'success',
            'message' => 'Resources available in the given date and time range',
            'data' => [
                'resource_type' => $resource,
                'list' => $resourceList
            ]
        ];

        return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    }

    public function checkInstructorsIsWorking($resource_ids, $start_time, $end_time, $weekdays)
    {
        $codes = error_code_setting();
        $resource_ids = array_map(function ($id) {
            return "instructorsignedup-{$id}";
        }, $resource_ids);

        $start = strtotime($start_time);
        $end = strtotime($end_time);
        $slots = [];

        while ($start < $end) {
            $slot_start = date('H:i', $start);
            $slot_end = date('H:i', strtotime('+30 minutes', $start));
            $slots[] = [
                'starttime' => $slot_start,
                'endtime' => $slot_end
            ];
            $start = strtotime($slot_end);
        }

        $day_clauses = [];

        foreach ($weekdays as $day) {
            $slot_clauses = [];
            foreach ($slots as $slot) {
                $slot_clauses[] = [
                    'nested' => [
                        'path' => 'data.details.working_hours.slots',
                        'query' => [
                            'bool' => [
                                'must' => [
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.starttime.keyword' => $slot['starttime']
                                        ]
                                    ],
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.endtime.keyword' => $slot['endtime']
                                        ]
                                    ],
                                    [
                                        'term' => [
                                            'data.details.working_hours.slots.status' => true
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
            }

            $day_clauses[] = [
                'nested' => [
                    'path' => 'data.details.working_hours',
                    'query' => [
                        'bool' => [
                            'must' => array_merge(
                                [
                                    [
                                        'term' => [
                                            'data.details.working_hours.day.keyword' => $day
                                        ]
                                    ]
                                ],
                                $slot_clauses
                            )
                        ]
                    ]
                ]
            ];
        }

        $query = [
            'query' => [
                'bool' => [
                    'must' => array_merge(
                        [
                            [
                                'terms' => [
                                    '_id' => $resource_ids
                                ]
                            ]
                        ],
                        $day_clauses
                    )
                ]
            ],
            '_source' => ['_id']
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/instructorsignedup/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($query, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        // print_r($response);exit;

        if ($err) {
        } else {
            $response_data = json_decode($response, true);
            if (isset($response_data['hits']['hits']) && !empty($response_data['hits']['hits'])) {
                $working_resource_ids = array_map(function ($hit) {
                    return str_replace('instructorsignedup-', '', $hit['_id']);
                }, $response_data['hits']['hits']);

                $response = array(
                    'status' => 'success',
                    'message' => 'Working hours slots for Resource',
                    'data' => array(
                        'is_working' => true,
                        'resource_ids' => $working_resource_ids
                    )
                );
                return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
            } else {
                $response = array(
                    'status' => 'fail',
                    'message' => 'No resources found'
                );
                return new WP_REST_Response($response, $codes["GET_FAIL"]["code"]);
            }
        }
    }


    public function getResourceAvailabilityClass($request)
    {
        $codes = error_code_setting();
        $resource = $request['resource'];
        if ($resource !== 'instructor') {
            return new WP_Error($codes["GET_FAIL"]["code"], "resource not allowed", array('status' => $codes["GET_FAIL"]["status"]));
        }
        $request = json_decode($request->get_body(), true);
        $start_date = $request['start_date'];
        $end_date = $request['end_date'];
        $class_starttime = $request['class_starttime'];
        $class_endtime = $request['class_endtime'];
        $class_days = $request['class_days'];
        $resource_id = $request['resource_id'];

        $checkIfWorking = $this->checkInstructorsIsWorking($resource_id, $class_starttime, $class_endtime, $class_days);
        // print_r(json_encode($checkIfWorking));exit;
        $response_data = $checkIfWorking->get_data();
        $working_instructors = [];
        if (isset($response_data['status']) && $response_data['status'] == 'success') {
            if (isset($response_data['data']['resource_ids'])) {
                $working_instructors = $response_data['data']['resource_ids'];
            }
        }
        if (empty($working_instructors)) {
            $message = "No resources found ";
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                $message,
                array('status' => $codes["GET_FAIL"]["status"])
            );
        } else {
            $googleHelper = new GoogleCalendarApiV2();
            $final_result = $googleHelper->getAvailabilityOfResourceInCalendar($working_instructors, $start_date, $end_date, $class_starttime, $class_endtime, $class_days);
            if (!empty($final_result)) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Resources available in the given date and time range',
                    'data' => array(
                        'available_resource_ids' => $final_result
                    )
                );
                // print_r(json_encode($response)); exit;
                return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
            } else {

                $message = "No resources available in the given date and time range";
                return new WP_Error(
                    $codes["GET_FAIL"]["code"],
                    $message,
                    array('status' => $codes["GET_FAIL"]["status"])
                );
            }
        }
    }

    public static function get_instructor_free_slots($request)
    {
        $codes = error_code_setting();
        if (is_object($request) && method_exists($request, 'get_body')) {
            // If get_body() exists, use it to get the request body
            $request = json_decode($request->get_body(), true);
        } else {
            // Otherwise, assume $request is already a JSON string
            $request = json_decode($request, true);
        }

        $startDate = $request['start_date'];
        $usrId = $request['resource_id'];
        $endDate = $request['end_date'];

        $working_instructors = $request['resource_id'];
        // $startTime = $request['start_time'];

        $today = date('Y-m-d');
        if ($startDate === $today) {
            $startTime = $request['start_time']; 
        } else {
            $startTime = '00:00'; 
        }

        $endTime = $request['end_time'];
        $googleHelper = new GoogleCalendarApiV2();
        $getAvailability = $googleHelper->getAvailabilityOfInstructor($working_instructors, $startDate, $endDate, $startTime, $endTime);

        if (empty($working_instructors)) {
            return new WP_Error(
                $codes["GET_FAIL"]["code"],
                'No demo instructor available',
                array('status' => $codes["GET_FAIL"]["status"])
            );
        }

        if (!empty($getAvailability) && isset($getAvailability['instructors']) && !empty($getAvailability['instructors'])) {
            $response = array(
                'status' => 'success',
                'message' => 'Resources available in the given date and time range',
                'data' => array(
                    'available_slots' => $getAvailability
                )
            );
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else {
            $errorMessage = empty($getAvailability) ? 'No resources available in the given date and time range' : 'No calendar found for the given user(s).';


            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Data", $errorMessage), array('status' => $codes["GET_FAIL"]["status"]));
        }
    }

    public function get_mylearners_instructor($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $instructorId = $request['instructorId'];
        $view = $request['view'];
        $limit = $request['limit'];
        $offset = $request['offset'];


        $sort = isset($request['sort']) ? $request['sort'] : '';

        $typeCollectionArray = array("list-view", "grid-view");
        if (!in_array($view, $typeCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the view type', array('status' => $codes["GET_FAIL"]["status"]));
        }

        $must = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.role" => "learner"
                                    ]
                                ],
                                [
                                    "terms" => [
                                        "data.details.my_instructors" => [$instructorId]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Construct the main Elasticsearch query
        $curlPost = [
            "_source" => [
                "data.details.user_id",
                "data.details.user.name",
                "data.details.user.email",
                "data.details.user.phone",
                "data.details.role",
                "data.details.location.city",
                "data.details.location.country",
                "data.details.basic_details.registration_date",
                "data.details.basic_details.last_login_time",
                "data.details.user.image",
                "data.details.time_of_study",
                "@timestamp"
            ],
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ]
        ];

        // Apply script-based sorting if 'sort' parameter is present
        if (!empty($sort)) {
            $curlPost['sort'] = [
                [
                    "_script" => [
                        "type" => "number",
                        "script" => [
                            "lang" => "painless",
                            "source" => "if (params['_source']['data']['details']['user']['name'].toLowerCase().startsWith(params['prefix'].toLowerCase())) { return 0; } else { return 1; }",
                            "params" => [
                                "prefix" => $sort // Use the 'sort' parameter value here, e.g., 'C'
                            ]
                        ],
                        "order" => "asc"
                    ]
                ],
                [
                    "data.details.user.name.keyword" => [
                        "order" => "asc",
                        "nested" => [
                            "path" => "data.details"
                        ]
                    ]
                ]
            ];
        } else {
            // If no 'sort' parameter is present, just sort alphabetically by user name
            $curlPost['sort'] = [
                [
                    "data.details.user.name.keyword" => [
                        "order" => "asc",
                        "nested" => [
                            "path" => "data.details"
                        ]
                    ]
                ]
            ];
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/signedup/_search?from=" . $offset . "&size=" . $limit,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $res = json_decode($response, true);
        $learners = [];
        if (isset($res['hits']['hits'])) {
            foreach ($res['hits']['hits'] as $hit) {
                $details = $hit['_source']['data']['details'];
                $user = $details['user'];
                $basicDetails = $details['basic_details'];
                $location = $details['location'] ?? [];

                $learners[] = [
                    'learner' => [
                        'name' => $user['name'] ?? "", // Empty string if null or key does not exist
                        'image' => $user['image'] ?? "", // Empty string if null or key does not exist
                        'user_id' => (int)($details['user_id'] ?? 0), // Default to 0 if null or key does not exist
                    ],
                    'email' => $user['email'] ?? "", // Empty string if null or key does not exist
                    'phone' => $user['phone'] ?? "", // Empty string if null or key does not exist
                    'time_of_study' => $details['time_of_study'] ?? "", // Empty string if null or key does not exist
                    // 'created_time' => Date('M d, Y, h:i A', strtotime($hit['_source']['@timestamp'])),
                    'last_login' => isset($basicDetails['last_login_time']) && strtotime($basicDetails['last_login_time']) ? Date('M d, Y, h:i A', strtotime($basicDetails['last_login_time'])) : "", // Fallback to empty string if invalid date
                    'country' => $location['country'] ?? "", // Empty string if null or key does not exist
                    'state' => $location['state'] ?? "", // Empty string if null or key does not exist
                    'city' => $location['city'] ?? "" // Empty string if null or key does not exist
                ];
            }
        }

        $total_records = $res['hits']['total']['value'];

        $columns = [
            [
                "field" => "created_time",
                "label" => "Created Time"
            ],
            [
                "field" => "last_login",
                "label" => "Last Login"
            ],
            [
                "field" => "country",
                "label" => "Country"
            ],
            [
                "field" => "state",
                "label" => "State"
            ],
            [
                "field" => "city",
                "label" => "City"
            ],
            [
                "field" => "is_active",
                "label" => "Is Active"
            ]
        ];

        $learnerData = [
            "rows" => $learners,
            "columns" => $columns
        ];

        if (empty($learners)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Learners Not Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            if ($view == 'list-view') {
                $result = array(
                    'code' => $codes["GET_SUCCESS"]["code"],
                    'message' => str_replace("[Module_Name]", "Learners", $codes["GET_SUCCESS"]["message"]),
                    'status' => $codes["GET_SUCCESS"]["status"],
                    'count' => $total_records,
                    'data' => $learners
                );
            } else {
                $result = array(
                    'code' => $codes["GET_SUCCESS"]["code"],
                    'message' => str_replace("[Module_Name]", "Learners", $codes["GET_SUCCESS"]["message"]),
                    'status' => $codes["GET_SUCCESS"]["status"],
                    'count' => $total_records,
                    'data' => $learnerData
                );
            }
            return new WP_REST_Response($result, 200);
        }
    }

    public function get_mylearner_details($request)
    {
        $learner_id = $request['learnerId'];

        $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/signedup/_doc/signedup-" . $learner_id;
        $headers = [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache"
        ];
        $curlPost = "";
        $response1 = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);


        $results = json_decode($response1['response'], true);
        $codes = error_code_setting();

        if (!empty($results)) {
            $resp = array(
                'code' => $codes["POST_INSERT"]["code"],
                'message' => 'Results found',
                'data' => array('status' => $codes["POST_INSERT"]["code"])
            );
        } else {
            $resp = array(
                'code' => $codes["POST_INSERT_FAIL"]["code"],
                'message' => 'No results found',
                'data' => array('status' => $codes["POST_INSERT_FAIL"]["status"])
            );
        }




        $root = $results['_source']['data']['details'];
        $user = $results['_source']['data']['details']['user'];
        $basic_details = $results['_source']['data']['details']['basic_details'];


        $reg_date = new DateTime($basic_details['registration_date']);
        $login_date = new DateTime($basic_details['last_login_time']);
        $created_time = $reg_date->format('M d, Y');
        $last_login = $login_date->format('M d, Y');

        $row = [];
        // $row['learner'] = [
        $row['name'] = $user['name'];
        $row['email'] = $user['email'];
        $row['image'] = $user['image'];
        $row['phone'] = !empty($user['phone']) ? $user['phone'] : "";
        $row['user_id'] = $root['user_id'];
        false;
        $row['time_of_study'] = isset($root['time_of_study']) ? ucfirst($root['time_of_study']) : "N/A";
        $row['term_of_service'] = isset($basic_details['privacy_policy_terms_of_service']) ? $basic_details['privacy_policy_terms_of_service'] : false;
        $row['created_time'] = $created_time;
        $row['last_login'] = $last_login;
        $row['native_language'] = isset($basic_details['native_language']) ? $basic_details['native_language'] : "";
        $row['country'] = isset($root['location']['country']) ? $root['location']['country'] : "";
        $row['state'] = isset($root['location']['state']) ? $root['location']['state'] : "";
        $row['city'] = isset($root['location']['city']) ? $root['location']['city'] : "";
        $resp['data'] = $row;


        return new WP_REST_Response($resp, 200);
    }
}
