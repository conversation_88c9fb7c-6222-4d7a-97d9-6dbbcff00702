<?php
/**
 * Template Name: Webhook Event Handler
 */
$json_razorpay_event = file_get_contents('php://input');
$payment_event = json_decode($json_razorpay_event, true);

error_log(" First CALLL Webhook, payment_event -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");

/**
* [payment api handler]
*/
error_log("********.1  Webhook, payment_event -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log"); 
global $YC;
$YC->loadModel('payment');
error_log("json_razorpay_event---".$json_razorpay_event);
error_log("Webhook Event Handler, payment_event -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/test_razorpay_payment.log");
error_log("********.1  Webhook, payment_event -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log"); 

// Include the PaymentModel class
// require_once get_template_directory() . '/inc/mvc/models/PaymentModel.php';
// use V4\PaymentModel;
// $paymentModel = new PaymentModel();
error_log("payment_event---".$payment_event);
/*if (isset($payment_event["event"]) && $payment_event["event"] == 'invoice.paid') {
	$update_info = [];
	$update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
	$update_info['receipt'] = $payment_event["payload"]["order"]["entity"]["receipt"];
	$update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
	$update_info['amount_paid'] = $payment_event["payload"]["order"]["entity"]["amount_paid"]/100;
	//$update_info['amount_due'] = $payment_event["payload"]["order"]["entity"]["amount_due"];
	$update_info['status'] = $payment_event["payload"]["order"]["entity"]["status"];
	$update_info['created_at'] = $payment_event["payload"]["order"]["entity"]["created_at"];
	//error_log("invoice paid :: raman");
	error_log("invoiceLinkPaid: ".$update_info['receipt']);
	updatePaymentResponse($update_info);

} 
else*/ if (isset($payment_event["event"]) && $payment_event["event"] == 'order.paid') {
	$update_info = [];
	$update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
	$update_info['receipt'] = $payment_event["payload"]["order"]["entity"]["receipt"];
	if ($payment_event["payload"]["payment"]["entity"]['notes']["org_on_yuno"] > 0) {
		$update_info['org_on_yuno'] = $payment_event["payload"]["payment"]["entity"]['notes']["org_on_yuno"];
	}

	$update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
	$update_info['amount_paid'] = $payment_event["payload"]["order"]["entity"]["amount_paid"]/100;
	//$update_info['amount_due'] = $payment_event["payload"]["order"]["entity"]["amount_due"];
	$update_info['status'] = $payment_event["payload"]["order"]["entity"]["status"];
	$update_info['created_at'] = $payment_event["payload"]["order"]["entity"]["created_at"];
	//error_log("invoice paid :: raman");
	error_log("directOrderPaid: ".$update_info['receipt']);
	error_log("ORDER>PAID  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($update_info)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");
	updatePaymentResponse($update_info);
	//$paymentModel->updatePaymentDetails($update_info);

	$YC->paymentModel->updatePaymentDetails($update_info);
	
	
	
} else if (isset($payment_event["event"]) && $payment_event["event"] == 'invoice.paid') {
	error_log("invoice.paid  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");

	$update_info = [];
	$update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
	$update_info['org_on_yuno'] = $payment_event["payload"]["invoice"]["entity"]['notes']["org_on_yuno"];
	$update_info['customer_email'] = $payment_event["payload"]["invoice"]["entity"]['customer_details']["email"];
	$update_info['receipt'] = $payment_event["payload"]["order"]["entity"]["receipt"];
	$update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
	$update_info['amount_paid'] = $payment_event["payload"]["order"]["entity"]["amount_paid"]/100;
	//$update_info['amount_due'] = $payment_event["payload"]["order"]["entity"]["amount_due"];
	$update_info['status'] = $payment_event["payload"]["order"]["entity"]["status"];
	$update_info['created_at'] = $payment_event["payload"]["order"]["entity"]["created_at"];
	
	error_log("directOrderPaid: ".$update_info['receipt']);
	error_log("INVOICE PAID  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($update_info)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log"); 
	updateOrgPaymentResponse($update_info);
	$YC->paymentModel->updatePaymentDetails($update_info);
	
	//$paymentModel->updatePaymentDetails($update_info);
	// $update_info = [];
	// $update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
	// $update_info['receipt'] = $payment_event["payload"]["order"]["entity"]["receipt"];
	// $update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
	// $update_info['amount_paid'] = $payment_event["payload"]["order"]["entity"]["amount_paid"]/100;
	// //$update_info['amount_due'] = $payment_event["payload"]["order"]["entity"]["amount_due"];
	// $update_info['status'] = $payment_event["payload"]["order"]["entity"]["status"];
	// $update_info['created_at'] = $payment_event["payload"]["order"]["entity"]["created_at"];
	// //error_log("invoice paid :: raman");
	// error_log("invoiceLinkPaid: ".$update_info['receipt']);
	// updatePaymentResponse($update_info);
}
// else if (isset($payment_event["event"]) && $payment_event["event"] == 'invoice.partially_paid') {
	
// 	$update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
// 	$update_info['receipt'] = $payment_event["payload"]["payment"]["entity"]["receipt"];
// 	$update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
// 	$update_info['amount_paid'] = $payment_event["payload"]["order"]["entity"]["amount_paid"];
// 	$update_info['amount_due'] = $payment_event["payload"]["order"]["entity"]["amount_due"];
// 	$update_info['status'] = $payment_event["payload"]["order"]["entity"]["status"];
// 	$update_info['created_at'] = $payment_event["payload"]["order"]["entity"]["created_at"];
// 	//error_log("partially paid :: raman");
// 	payment_data_update_after_webhook_event($update_info);

// } 
else if (isset($payment_event["event"]) && $payment_event["event"] == 'invoice.expired') {
	error_log("invoice.expired  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");
	$update_info = [];
	$update_info['invoice_id'] = $payment_event["payload"]["invoice"]["entity"]["id"];
	$update_info['receipt'] = $payment_event["payload"]["invoice"]["entity"]["receipt"];
	$update_info['payment_id'] = $payment_event["payload"]["invoice"]["entity"]["payment_id"];
	$update_info['amount_paid'] = $payment_event["payload"]["invoice"]["entity"]["amount_paid"];
	//$update_info['amount_due'] = $payment_event["payload"]["invoice"]["entity"]["amount_due"];
	$update_info['status'] = $payment_event["payload"]["invoice"]["entity"]["status"];
	$update_info['created_at'] = $payment_event["payload"]["invoice"]["entity"]["created_at"];
	//error_log("my expired :: raman");
	error_log("invoiceLinkExpired: ".$update_info['receipt']);

	global $wpdb;
	$data = $wpdb->get_row("SELECT zoho_deal_id from wp_payment_status 
		where receipt_id = '".$update_info['receipt']."' and payment_mode = 'generate_link'", ARRAY_A);
	error_log("invoice.expired  Webhook, deal_data -- ".date("Y-m-d H:i:s")." === ".json_encode($deal_data)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log"); 
	error_log("invoice.expired  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($update_info)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");
	updatePaymentResponse($update_info);
	//$paymentModel->updatePaymentDetails($update_info);
	$YC->paymentModel->updatePaymentDetails($update_info);
	
} else if (isset($payment_event["event"]) && $payment_event["event"] == 'payment.failed') {
	error_log("payment.failed  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($payment_event)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log");
	$update_info = [];
	$update_info['invoice_id'] = $payment_event["payload"]["payment"]["entity"]["invoice_id"];
	$update_info['receipt'] = $payment_event["payload"]["payment"]["entity"]["receipt"];
	$update_info['payment_id'] = $payment_event["payload"]["payment"]["entity"]["id"];
	$update_info['try_to_pay_amount'] = $payment_event["payload"]["payment"]["entity"]["amount"];
	//$update_info['amount_due'] = $payment_event["payload"]["payment"]["entity"]["amount_due"];
	$update_info['status'] = $payment_event["payload"]["payment"]["entity"]["status"];
	$update_info['created_at'] = $payment_event["payload"]["payment"]["entity"]["created_at"];
	//error_log("payment failed :: raman");
	error_log("paymentLinkFailed: ".$update_info['receipt']);
	error_log("********.4  Webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($update_info)."\n\n", 3, ABSPATH."error-logs/RazorPayWebhookRes.log"); 
	updatePaymentResponse($update_info);
	//$paymentModel->updatePaymentDetails($update_info);
	$YC->paymentModel->updatePaymentDetails($update_info);
	
	
}else{
	error_log($update_info,'else-webhook');
	error_log("********.4  else-webhook, update_info -- ".date("Y-m-d H:i:s")." === ".json_encode($update_info)."\n\n", 3, ABSPATH."error-logs/payment_test.log"); 
	error_log("ELSEEEE Webhook  - update_info -- Payment Analytics " . date("Y-m-d H:i:s") . " === RazorPay: " . json_encode($update_info) . "\n\n", 3, ABSPATH . "error-logs/RazorPayWebhookRes.log");
}
$data = [200];
error_log("********.5  Webhook, data -- ".date("Y-m-d H:i:s")." === ".json_encode($data)."\n\n", 3, ABSPATH."error-logs/payment_test.log");

error_log("Webhook  -ENDDD  " . date("Y-m-d H:i:s") . " === RazorPay: " . json_encode($data) . "\n\n", 3, ABSPATH . "error-logs/RazorPayWebhookRes.log");
echo json_encode($data);