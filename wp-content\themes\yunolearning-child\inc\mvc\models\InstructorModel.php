<?php

namespace V4;

use Google\Client as Google_Client;
use DateTimeZone;
use DateTime;

/**
 * Instructor model
 */
class InstructorModel extends Model
{
    /** @var mixed */
    public $es;
    /** @var mixed */
    public $schema;
    /** @var mixed */
    public $dt;
    /** @var mixed */
    public $locale;
    /** @var \V4\UserModel */
    public $userModel;
    /** @var \V4\CourseModel */
    public $courseModel;
    /** @var \V4\CategoryModel */
    public $categoryModel;
    /** @var \V4\ClassModel */
    public $classModel;
    /** @var \V4\GoogleModel */
    public $googleModel;
    /** @var \V4\OrgModel */
    public $orgModel;

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    /**
     * Retrieves the details of the instructor.
     *
     *
     * @return WP_REST_Response The response object containing the user's details.
     * @throws WP_Error If an exception occurs.
     */

    
    public function getInstructor($query, $filter = [])
    {
        $this->loadModel('user');
        $this->loadModel('course');
        $this->loadModel('category');

        $query = is_array($query) ? $query : ['id' => $query];

        if (empty($query['id'])) {
            return false;
        }

        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $query['id']);
        $userDataResponse = $this->load->subData('user', 'getUser', $query['id']);

        if ($instructorDataResponse['status_code'] !== 200 || empty($userDataResponse)) {
            return false;
        }

        $instructor = $instructorDataResponse['body']['_source']['data']['details'];
        $fluentLanguagesData = [];

        foreach (explode(',', $instructor['fluent_in']) as $language) {
            $fluentLanguagesData[] = [
                "name_in_english" => $language,
                "native_lang_name" => '',
                "code" => ''
            ];
        }

        $courseData = [];
        foreach ($instructor['mapped_courses'] as $courseId) {
            $courseData[] = $this->load->subData('course', 'getCourse', ['id' => $courseId], ['schema' => 'Course_Minimal']);
        }

        $categoryData = [];
        if (!empty($instructor['mapped_categories']) && is_array($instructor['mapped_categories'])) {
            foreach (array_unique($instructor['mapped_categories']) as $categoryId) {
                $categoryData[] = $this->load->subData('category', 'getCategory', ['id' => $categoryId], ['schema' => 'Category_Minimal']);
            }
        }

        $workingHours = $instructor['working_hours'] ?? [];
        $formattedWorkingHours = [];

        foreach ($workingHours as $dayData) {
            $daySlots = $dayData['slots'] ?? [];
            $dayShort = strtoupper(substr($dayData['day'], 0, 3));
            $totalHours = 0;
            $mergedSlots = [];

            $currentStart = null;
            $lastEnd = null;

            foreach ($daySlots as $slot) {
                if (!empty($slot['status'])) {
                    if ($currentStart === null) {
                        $currentStart = $slot['starttime'];
                    }
                    $lastEnd = $slot['endtime'];
                    $totalHours += (strtotime($slot['endtime']) - strtotime($slot['starttime'])) / 3600;
                } else {
                    if ($currentStart !== null && $lastEnd !== null) {
                        $mergedSlots[] = [
                            'start' => [
                                'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ],
                            'end' => [
                                'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ]
                        ];
                        $currentStart = null;
                        $lastEnd = null;
                    }
                }
            }

            if ($currentStart !== null && $lastEnd !== null) {
                $mergedSlots[] = [
                    'start' => [
                        'time' => $this->dt->convertToActiveDT($currentStart, 'H:i:s'),
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => $this->dt->convertToActiveDT($lastEnd, 'H:i:s'),
                        'timezone' => $this->locale->activeTimezone()
                    ]
                ];
            }

            $formattedWorkingHours[] = [
                'day' => $dayShort,
                'name' => $this->dt->getWeekDays($dayShort),
                'is_available' => !empty($mergedSlots),
                'working_hours' => round($totalHours, 1),
                'time_slot' => !empty($mergedSlots) ? $mergedSlots : [[
                    'start' => ['time' => '', 'timezone' => ''],
                    'end' => ['time' => '', 'timezone' => '']
                ]]
            ];
        }

        $responseData = [
            'user' => $userDataResponse,
            'about' => $instructor['instructor_experience'],
            'native_language' => [
                "name_in_english" => $instructor['native_language'],
                "native_lang_name" => '',
                "code" => ''
            ],
            'fluent_languages' => $fluentLanguagesData,
            'profile_url' => '',
            'avg_rating' => $instructor['learner_avg_class_rating'],
            'max_rating' => $instructor['staff_avg_class_rating'],
            'review_count' => count($instructor['reviews'] ?? []),
            'active_learners' => [],
            'past_learners' => [],
            'courses_can_teach' => $courseData,
            'subjects_can_teach' => $categoryData,
            'working_hours' => [
                'resource' => [
                    'type' => 'INSTRUCTOR',
                    'name' => $instructor['name'] ?? 'Instructor'
                ],
                'days' => $formattedWorkingHours
            ]
        ];
        return $this->schema->validate($responseData, 'Instructor', $filter);
    }

    /**
     * Checks instructor's calendar availability for a given time slot.
     *
     * Verifies instructor's availability by checking Google Calendar for conflicts,
     * processes available time slots, and returns formatted availability data.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Contains resource_id, start_date, end_date, start_time, end_time, org_id, class_id
     * @param array $filter Optional filter for availability check
     * @return array|bool Returns formatted availability data or false on failure
     */
    public function getInstructorAvailability($query, $filter)
    {
        if (empty($query['resource_id']) || empty($query['start_date']) || empty($query['end_date']) || empty($query['start_time']) || empty($query['end_time'])) {
            return false;
        }

        $this->loadModel('class');

        $userId = $query['resource_id'];
        $startDate = $query['start_date'];
        $endDate = $query['end_date'];
        $startTime = $query['start_time'];
        $endTime = $query['end_time'];
        $orgId = $query['org_id'] ?? null;
        $classId = $query['class_id'] ?? null;

        $accessToken = $this->classModel->getGoogleMeetAccessToken($userId, $this->locale->activeTimezone(), $orgId);
        if (empty($accessToken)) {
            return false;
        }

        $client = new Google_Client();
        $client->setAccessToken($accessToken);

        $calendarId = 'primary';
        if (empty($calendarId)) {
            return false;
        }

        $instructorName = get_user_meta($userId, 'yuno_display_name', true);
        $instructors = [
            [
                'id' => $userId,
                'name' => $instructorName
            ]
        ];
        $instructorCalendarIds = [$calendarId];

        $availableSlotsRaw = $this->getAvailableSlotsResource($client, $instructorCalendarIds, $startDate, $endDate, $startTime, $endTime, $instructors);

        $finalResponse = []; 

        if (!empty($availableSlotsRaw['available_slots'])) {
            foreach ($availableSlotsRaw['available_slots'] as $day) {
                $date = $day['date'];

                $isAvailableForDay = true;
                foreach ($day['slots'] as $slot) {
                    if (empty($slot['status'])) {
                        $isAvailableForDay = false;
                        break;
                    }
                }

                $finalResponse[] = [
                    'resource' => [
                        'type' => 'Instructor',
                        'name' => $instructors[0]['name']
                    ],
                    'time_slots' => [
                        'time_slot' => [
                            'start' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $startTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ],
                            'end' => [
                                'time' => $this->dt->convertToActiveDT($date . ' ' . $endTime, 'Y-m-d H:i:s'),
                                'timezone' => $this->locale->activeTimezone()
                            ]
                        ],
                        'is_available' => $isAvailableForDay
                    ]
                ];
            }
        }

        $validatedResponse = [];
        foreach ($finalResponse as $item) {
            try {
                $validatedItem = $this->schema->validate($item, 'Availability', $filter);
                $validatedResponse[] = $validatedItem;
            } catch (\Exception $e) { // Using \Exception for global namespace
                return false;
            }
        }

        return $validatedResponse;
    }


    public function getAvailableSlotsResource($client, $calendarId, $startDate, $endDate, $startTime, $endTime, $instructors)
    {
        $date = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $end->modify('+1 day');
        $timeZone = $this->locale->activeTimezone();

        $this->loadModel('google');

        $calendarTimeZone = $this->googleModel->getCalendarTimeZone($client, $calendarId);

        $availableSlots = [];

        while ($date < $end) {
            $currentDate = $date->format('Y-m-d');
            $dayOfWeek = $date->format('l');
            $timeMin = "$currentDate $startTime";
            $timeMax = "$currentDate $endTime";

            $timeMinDT = new \DateTime($timeMin, new \DateTimeZone($calendarTimeZone));
            $timeMaxDT = new \DateTime($timeMax, new \DateTimeZone($calendarTimeZone));

            $combinedBusyTimes = $this->getCombinedBusyTimes($client, $calendarId, $timeMin, $timeMax);
            $slots = $this->generateTimeSlots($timeMinDT, $timeMaxDT, $combinedBusyTimes);

            $availableSlots[] = [
                'date' => $currentDate,
                'day_of_week' => $dayOfWeek,
                'slots' => $slots
            ];

            $output = [
                'instructors' => $instructors,
                'available_slots' => $availableSlots
            ];

            $date->modify('+1 day');
        }

        return $output;
    }

    public function getCombinedBusyTimes($client, $calendarIds, $timeMin, $timeMax)
    {
        $combinedBusyTimes = [];

        foreach ($calendarIds as $calendarId) {
            $busyTimes = $this->googleModel->getFreeTimeSlots($client, $calendarId, $timeMin, $timeMax);
            foreach ($busyTimes as $busyTime) {
                $busyStart = new \DateTime($busyTime->getStart());
                $busyEnd = new \DateTime($busyTime->getEnd());
                $combinedBusyTimes[] = [
                    'start' => $busyStart,
                    'end' => $busyEnd
                ];
            }
        }

        usort($combinedBusyTimes, function ($a, $b) {
            return $a['start'] <=> $b['start'];
        });

        return $combinedBusyTimes;
    }

    public function generateTimeSlots($timeMin, $timeMax, $combinedBusyTimes)
    {
        $slots = [];
        $currentSlotStart = clone $timeMin;

        while ($currentSlotStart < $timeMax) {
            $currentSlotEnd = clone $currentSlotStart;
            $currentSlotEnd->modify('+30 minutes');
            if ($currentSlotEnd > $timeMax) {
                $currentSlotEnd = clone $timeMax;
            }

            $isFree = true;
            foreach ($combinedBusyTimes as $busyTime) {
                $busyStart = $busyTime['start'];
                $busyEnd = $busyTime['end'];

                if (($busyStart < $currentSlotEnd && $busyEnd > $currentSlotStart)) {
                    $isFree = false;
                    break;
                }
            }

            $slots[] = [
                'starttime' => $currentSlotStart->format('H:i'),
                'endtime' => $currentSlotEnd->format('H:i'),
                'status' => $isFree
            ];

            $currentSlotStart = clone $currentSlotEnd;
        }

        return $slots;
    }

    public function getInstructorVirtualClasserooms($query, $filter = [])
    {
        if (isset($query['custom'])) {
            $instructorOrgDataResponse = $this->es->customQuery($query['custom'], 'course');
        } else {
            return false;
        }

        if ($instructorOrgDataResponse['status_code'] == 200) {
            $organizations = $instructorOrgDataResponse['body']['aggregations']['distinct_org_ids']['org_ids']["buckets"];
            // Build the structured response
            if (count($organizations)) {

                foreach ($organizations as $organization) {
                    $details = $organization['sample_field']['hits']['hits'][0]['_source'];

                    $vcQuery['custom'] = [
                        'id' => $query['params']['instructorId'],
                        'orgId' => $details['org_id'],
                        'platform' => $this->loadModel('org')->getOrganization($details['org_id'], ['key' => 'virtual_classroom->platform'])
                    ];

                    $responseData[] = [
                        'virtual_classroom' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $vcQuery, ['noResponse' => 'Virtual_Classroom']),
                        'org' => $this->load->subData("org", "getOrganization", $details['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => true]),
                        'academies' => $this->load->subData("academy", "getAcademies", ['orgId' => $details['org_id']], ['schema' => 'Refer#Academy_Minimal', 'key' => 'data', 'noResponse' => true]),
                        //'academies' => $this->load->subData("org", "getOrganization", $details['org_id'], [ 'key'=>'academies->academy','noResponse' => ['id' => 0, 'name' => '']]),
                    ];
                }

                $dataSchema = [[
                    "virtual_classroom" => 'Refer#Virtual_Classroom',
                    "org" => 'Refer#Organization_Minimal',
                    "academies" => ['Refer#Academy_Minimal'],
                ]];

                return $this->schema->validate($responseData, $dataSchema, $filter);
            }
        }
        return false;
    }

    /**
     * Generates instructor filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The selected instructor ID (if any).
     * @param int $learnerId The ID of the learner (for filtering learner-specific instructors).
     * @param int $counselorId The ID of the counselor (for filtering counselor-specific instructors).
     * @return array Returns an array containing instructor filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'instructor_id',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $instructorId, //  Pre-select instructor
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];

    }
    public function generateEnrollmentInstructorFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'instructor',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'dropdown',
            'selected' => $instructorId, //  Pre-select instructor
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "instructor"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        //  Pre-select instructor if provided
        if ($instructorId > 0) {
            $instructorData = $this->getInstructor($instructorId);
            if (!empty($instructorData)) {
                $filterData['selected'] = $instructorData['id'] ?? 0;
            }
        }

        if ($role === 'yuno-admin') {
            //  Yuno Admin: Fetch all instructors (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'learner' && $learnerId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.learner_id" => $learnerId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'counselor' && $counselorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.counselor_id" => $counselorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Instructor Query: " . json_encode($customQuery));

        //  Fetch Instructors from Elasticsearch
        $instructorRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($instructorRecords['status_code']) && $instructorRecords['status_code'] === 200) {
            $instructors = $instructorRecords['body']['hits']['hits'];

            foreach ($instructors as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $foundInstructorId = $details['user_id'] ?? 0;
                $instructorName = $userDetails['name'] ?? '';
                $instructorEmail = $userDetails['email'] ?? '';

                if ($foundInstructorId) {
                    $filterData['items'][] = [
                        'id' => $foundInstructorId,
                        'label' => $instructorName . " (" . $instructorEmail . ")",
                        'filter' => 'instructor'
                    ];
                }
            }
        }

        return $filterData;
    }

    /**
     * Returns a default 7-day working hours structure with all days marked unavailable.
     * Ignores any actual instructor data or working hour availability.
     *
     * @param mixed $query (Optional, to match method signature if needed)
     * @return array
     */
    public function getBlankWorkingHours($query = null)
    {
        $defaultDays = [
            ['day' => 'MON', 'name' => 'Monday'],
            ['day' => 'TUE', 'name' => 'Tuesday'],
            ['day' => 'WED', 'name' => 'Wednesday'],
            ['day' => 'THU', 'name' => 'Thursday'],
            ['day' => 'FRI', 'name' => 'Friday'],
            ['day' => 'SAT', 'name' => 'Saturday'],
            ['day' => 'SUN', 'name' => 'Sunday']
        ];

        return array_map(function ($day) {
            return [
                'day' => $day['day'],
                'name' => $day['name'],
                'is_available' => false,
                'working_hours' => 0,
                'time_slot' => [[
                    'start' => ['time' => '', 'timezone' => ''],
                    'end' => ['time' => '', 'timezone' => '']
                ]]
            ];
        }, $defaultDays);
    }

    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $instructorData Data for instructor creation
     * @return array|WP_Error Result of instructor creation
     */
    public function createInstructor($instructorData)
    {
        $codes = error_code_setting();
        
        // Check if we're creating a new user or using existing one
        if (empty($instructorData['user_id']) && !empty($instructorData['email'])) {
            // Create new user first
            $random_password = wp_generate_password(12, false);
            $email = sanitize_email($instructorData['email']);
            $username = sanitize_user($email);
            
            // Check if username exists
            if (username_exists($username)) {
                return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'Email already in use', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
            }
            
            // Create user
            $user_id = wp_create_user($username, $random_password, $email);
            
            if (is_wp_error($user_id)) {
                return $user_id;
            }
            
            // Set user meta
            wp_update_user([
                'ID' => $user_id,
                'first_name' => sanitize_text_field($instructorData['first_name']),
                'last_name' => sanitize_text_field($instructorData['last_name']),
                'display_name' => sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name'])
            ]);
            
            update_user_meta($user_id, 'yuno_gplus_email', $email);
            update_user_meta($user_id, 'yuno_first_name', sanitize_text_field($instructorData['first_name']));
            update_user_meta($user_id, 'yuno_last_name', sanitize_text_field($instructorData['last_name']));
            update_user_meta($user_id, 'yuno_display_name', sanitize_text_field($instructorData['first_name'] . ' ' . $instructorData['last_name']));
            
            if (!empty($instructorData['phone'])) {
                update_user_meta($user_id, 'yuno_gplus_mobile', sanitize_text_field($instructorData['phone']));
            }
        } else {
            $user_id = (int)$instructorData['user_id'];
        }
        
        // Validate user exists
        $userdata = get_userdata($user_id);
        if (empty($userdata)) {
            return new \WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'User not found', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
        }
        
        // Assign instructor role
        $u = new \WP_User($user_id);
        $u->remove_role('SEO Manager');
        $u->add_role('um_instructor');
        
        // Set basic instructor meta
        update_user_meta($user_id, 'profile_privacy', "public");
        update_user_meta($user_id, 'zoom_user_status', "free");
        
        // Process instructor details
        if (!empty($instructorData['bio'])) {
            update_user_meta($user_id, 'Instructor_About', $instructorData['bio']);
        }
        
        if (!empty($instructorData['expertise']) && is_array($instructorData['expertise'])) {
            update_user_meta($user_id, 'Instructor_Expertise', implode(',', $instructorData['expertise']));
        }
        
        if (!empty($instructorData['country'])) {
            update_user_meta($user_id, 'Instructor_Country', $instructorData['country']);
            update_user_meta($user_id, 'yuno_user_address_country', $instructorData['country']);
        }
        
        if (!empty($instructorData['state'])) {
            update_user_meta($user_id, 'Instructor_State', $instructorData['state']);
            update_user_meta($user_id, 'yuno_user_address_state', $instructorData['state']);
        }
        
        if (!empty($instructorData['city'])) {
            update_user_meta($user_id, 'Instructor_City', $instructorData['city']);
            update_user_meta($user_id, 'yuno_user_address_city', $instructorData['city']);
        }
        
        if (!empty($instructorData['native_language'])) {
            update_user_meta($user_id, 'Native_Language', $instructorData['native_language']);
        }
        
        if (!empty($instructorData['fluent_in']) && is_array($instructorData['fluent_in'])) {
            update_user_meta($user_id, 'Fluent_In', implode(',', $instructorData['fluent_in']));
        }
        
        if (!empty($instructorData['understand']) && is_array($instructorData['understand'])) {
            update_user_meta($user_id, 'Instructor_Understand', implode(',', $instructorData['understand']));
        }
        
        if (!empty($instructorData['teaching_preference']) && is_array($instructorData['teaching_preference'])) {
            update_user_meta($user_id, 'yuno_user_teaching_preference', implode(',', $instructorData['teaching_preference']));
            update_user_meta($user_id, 'Teaching_Preference', implode(',', $instructorData['teaching_preference']));
        }
        
        if (!empty($instructorData['experience'])) {
            update_user_meta($user_id, 'Instructor_Experience', $instructorData['experience']);
        }
        
        if (!empty($instructorData['laptop_availability'])) {
            update_user_meta($user_id, 'yuno_user_laptop_availability', $instructorData['laptop_availability']);
            update_user_meta($user_id, 'Laptop_Availability', $instructorData['laptop_availability']);
        }
        
        if (!empty($instructorData['broadband_connection_availability'])) {
            update_user_meta($user_id, 'yuno_user_broadband_connection_availability', $instructorData['broadband_connection_availability']);
            update_user_meta($user_id, 'Broadband_Connection_Availability', $instructorData['broadband_connection_availability']);
        }
        
        if (!empty($instructorData['online_teaching_exp'])) {
            update_user_meta($user_id, 'yuno_user_online_teaching_exp', $instructorData['online_teaching_exp']);
            update_user_meta($user_id, 'Online_Teaching_Exp', $instructorData['online_teaching_exp']);
        }
        
        if (!empty($instructorData['dob'])) {
            update_user_meta($user_id, 'yuno_user_dob', $instructorData['dob']);
            update_user_meta($user_id, 'Instructor_DOB', $instructorData['dob']);
        }
        
        if (!empty($instructorData['can_teach']) && is_array($instructorData['can_teach'])) {
            update_user_meta($user_id, 'can_teach', implode(',', $instructorData['can_teach']));
        }
        
        if (isset($instructorData['yuno_user_whatsapp_check'])) {
            $whatsappCheck = $instructorData['yuno_user_whatsapp_check'] ? "yes" : "no";
            update_user_meta($user_id, 'yuno_user_whatsapp_check', $whatsappCheck);
        }
        
        if (!empty($instructorData['profile_image'])) {
            update_user_meta($user_id, 'googleplus_profile_img', $instructorData['profile_image']);
        }
        
        // Set instructor as active if specified
        $login_status = 'inactive';
        if (!empty($instructorData['is_active']) && $instructorData['is_active'] === true) {
            update_user_meta($user_id, 'is_completed_step_1', "yes");
            update_user_meta($user_id, 'is_completed_step_2', "yes");
            update_user_meta($user_id, 'is_completed_step_3', "yes");
            update_user_meta($user_id, 'account_login_status', 'active');
            $login_status = 'active';
        }
        
        // Get city name if city ID is set
        global $wpdb;
        $city_name = '';
        $city_id = get_user_meta($user_id, 'yuno_user_address_city', true);
        if (!empty($city_id)) {
            $city = $wpdb->get_row("SELECT name FROM wp_cities where id=$city_id");
            $city_name = $city ? $city->name : '';
        }
        
        // Prepare user object for Elasticsearch
        $user_obj = [
            "name" => get_user_meta($user_id, 'yuno_display_name', true),
            "email" => get_user_meta($user_id, 'yuno_gplus_email', true),
            "phone" => get_user_meta($user_id, 'yuno_gplus_mobile', true),
            "image" => get_user_meta($user_id, 'googleplus_profile_img', true)
        ];
        
        // Create the instructor event in Elasticsearch
        $instructorEvent = [
            "data" => [
                "details" => [
                    "user_id" => $user_id,
                    "record_id" => $user_id,
                    "event_type" => "instructorsignedup",
                    "event_label" => "Instructor signed up",  
                    "account_login_status" => $login_status,
                    "first_name" => get_user_meta($user_id, 'yuno_first_name', true),
                    "last_name" => get_user_meta($user_id, 'yuno_last_name', true),
                    "country" => get_user_meta($user_id, 'yuno_user_address_country', true),
                    "city_id" => $city_id,
                    "city_name" => $city_name,
                    "fluent_in" => get_user_meta($user_id, 'Fluent_In', true),
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "instructor_understand" => get_user_meta($user_id, 'Instructor_Understand', true),
                    "laptop_availability" => get_user_meta($user_id, 'yuno_user_laptop_availability', true),
                    "broadband_connection_availability" => get_user_meta($user_id, 'yuno_user_broadband_connection_availability', true),
                    "online_teaching_exp" => get_user_meta($user_id, 'yuno_user_online_teaching_exp', true),
                    "dob" => get_user_meta($user_id, 'yuno_user_dob', true),
                    "can_teach" => get_user_meta($user_id, 'can_teach', true),
                    "teaching_preference" => get_user_meta($user_id, 'yuno_user_teaching_preference', true),
                    "instructor_experience" => get_user_meta($user_id, 'Instructor_Experience', true),
                    "address_pin_code" => get_user_meta($user_id, 'yuno_user_address_pin_code', true),
                    "address_flat_house_number" => get_user_meta($user_id, 'yuno_user_address_flat_house_number', true),
                    "address_street" => get_user_meta($user_id, 'yuno_user_address_street', true),           
                    "address_landmark" => get_user_meta($user_id, 'yuno_user_address_landmark', true),
                    "address_state" => get_user_meta($user_id, 'yuno_user_address_state', true),
                    "is_featured" => false,
                    "is_completed_step_1" => get_user_meta($user_id, 'is_completed_step_1', true),
                    "is_completed_step_2" => get_user_meta($user_id, 'is_completed_step_2', true),
                    "is_completed_step_3" => !empty(get_user_meta($user_id, 'is_completed_step_3', true)) ? get_user_meta($user_id, 'is_completed_step_3', true) : "no",
                    "yuno_user_whatsapp_check" => get_user_meta($user_id, 'yuno_user_whatsapp_check', true),
                    "active_enrolments" => '',
                    "completed_enrolments" => '',
                    "live_classes_delivered" => '',
                    "attendance" => '',
                    "active_batches" => 0,
                    "learner_avg_class_rating" => 0,
                    "staff_avg_class_rating" => 0,
                    "reviews_count" => 0,
                    "vc_status" => 'free',
                    "avg_rating" => '',
                    "instructor_about" => get_user_meta($user_id, 'Instructor_About', true),
                    "expertise" => get_user_meta($user_id, 'Instructor_Expertise', true),
                    "mapped_courses" => [],
                    "mapped_categories" => [],
                    "working_hours" => []
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];
        
        // Post the instructor event to Elasticsearch
        post_elastic_event($instructorEvent);
        
        // Create basic details in Elasticsearch
        $details = [
            "properties" => [
                "details" => [
                    "user_id" => $user_id,
                    "user" => $user_obj,
                    "native_language" => get_user_meta($user_id, 'Native_Language', true),
                    "role" => "instructor",
                ],
            ]
        ];
        
        $properties = $details['properties'];
        $curlPost['data'] = [
            "data" => $properties,
        ];
        
        \UserElasticSearch::update_signedup("basic_details", $curlPost);
        
        $reqquested_data = [
            "document_type" => "course",
            "fields" => ["mapped_instructor_ids" => $user_id]
        ];
        
        update_instructor_lang_es($reqquested_data);
        
        // Wait a moment for Elasticsearch to index the data
        sleep(1);
        
        // Fetch the actual data from Elasticsearch
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $user_id);
        
        // Prepare response with expected structure
        $response = [
            'code' => $codes["PUT_UPDATE"]["code"],
            'message' => 'Instructor created successfully',
            'user_id' => $user_id,
            'role' => 'um_instructor',
            'email' => get_user_meta($user_id, 'yuno_gplus_email', true),
            'is_active' => get_user_meta($user_id, 'account_login_status', true) === 'active',
            'elasticsearch' => [
                'index' => 'instructorsignedup',
                'id' => 'instructorsignedup-' . $user_id,
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'user' => $user_obj,
                            'native_language' => get_user_meta($user_id, 'Native_Language', true),
                            'role' => 'instructor'
                        ]
                    ]
                ]
            ]
        ];
        
        // Add actual Elasticsearch data if available
        if (!empty($instructorDataResponse) && $instructorDataResponse['status_code'] === 200) {
            $response['elasticsearch_actual'] = $instructorDataResponse['body']['_source'];
        }
        
        return $response;
    }

    /**
     * Checks the data saved in Elasticsearch for an instructor
     *
     * @param int $instructorId The ID of the instructor to check
     * @return array|false Returns the Elasticsearch data or false if not found
     */
    public function checkInstructorElasticsearchData($instructorId)
    {
        if (empty($instructorId)) {
            return false;
        }

        // Read from instructorsignedup index
        $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $instructorId);
        
        if ($instructorDataResponse['status_code'] !== 200) {
            return false;
        }

        return [
            'raw_data' => $instructorDataResponse['body']['_source'],
            'formatted_data' => [
                'user_id' => $instructorDataResponse['body']['_source']['data']['details']['user_id'] ?? null,
                'name' => $instructorDataResponse['body']['_source']['data']['details']['user']['name'] ?? null,
                'email' => $instructorDataResponse['body']['_source']['data']['details']['user']['email'] ?? null,
                'phone' => $instructorDataResponse['body']['_source']['data']['details']['user']['phone'] ?? null,
                'image' => $instructorDataResponse['body']['_source']['data']['details']['user']['image'] ?? null,
                'native_language' => $instructorDataResponse['body']['_source']['data']['details']['native_language'] ?? null,
                'role' => $instructorDataResponse['body']['_source']['data']['details']['role'] ?? null
            ]
        ];
    }

    public function getInstructors($filters = []) // Renamed from getInstructorsGrid
    {
        // Role check for user_id from filters
        if (isset($filters['user_id']) && !empty($filters['user_id'])) {
            $user_id_to_check = (int) $filters['user_id'];
            $user = get_userdata($user_id_to_check);

            if ($user) {
                $roles = (array) $user->roles;
                $allowed_roles = ['um_org-admin', 'um_yuno-admin'];
                $is_allowed = false;
                foreach ($roles as $role) {
                    if (in_array($role, $allowed_roles)) {
                        $is_allowed = true;
                        break;
                    }
                }

                if (!$is_allowed) {
                    return [
                        'code'    => 403, // HTTP Forbidden
                        'message' => 'Access Denied: User does not have the required role.',
                        'status'  => 'FAIL',
                        'count'   => 0,
                        'data'    => []
                    ];
                }
            } else {
                // If user_id is provided but user not found, it's an issue.
                return [
                    'code'    => 404, // HTTP Not Found (or 400 Bad Request)
                    'message' => 'User not found for the provided user_id.',
                    'status'  => 'FAIL',
                    'count'   => 0,
                    'data'    => []
                ];
            }
        }

        // Check if the request is for grid or list view
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $is_grid_view = strpos($request_uri, 'grid') !== false;

        // Load CategoryModel and CourseModel
        $this->loadModel('category');
        $this->loadModel('course');

        // Extract filters
        $limit = $filters['limit'] ?? 10000; // This limit is for PHP array_slice
        $offset = $filters['offset'] ?? 0;   // This offset is for PHP array_slice
        $status = $filters['status'] ?? null;
        $vc_status = $filters['vc_status'] ?? null;
        $category_id = $filters['category_id'] ?? 0;
        $course_id = $filters['course_id'] ?? 0;
        
        // Handle is_featured parameter
        $is_featured = null; // Default is null (no filter)
        if (isset($filters['is_featured'])) {
            // Convert any value to true/false/null
            if ($filters['is_featured'] === '1' || $filters['is_featured'] === 1 || 
                $filters['is_featured'] === 'true' || $filters['is_featured'] === true) {
                $is_featured = true;
            } else if ($filters['is_featured'] === '0' || $filters['is_featured'] === 0 || 
                       $filters['is_featured'] === 'false' || $filters['is_featured'] === false) {
                $is_featured = false;
            }
        }
        

        
        $native_language = $filters['native_language'] ?? null;
        $avg_rating = $filters['avg_rating'] ?? 0;
        $days = $filters['days'] ?? '0';
        $is_completed = $filters['is_completed'] ?? 'yes';

        // Convert string boolean values to actual booleans
        $has_active_batches = isset($filters['has_active_batches']) ? filter_var($filters['has_active_batches'], FILTER_VALIDATE_BOOLEAN) : null;
        $has_past_batches = isset($filters['has_past_batches']) ? filter_var($filters['has_past_batches'], FILTER_VALIDATE_BOOLEAN) : null;
        $has_active_enrollments = isset($filters['has_active_enrollments']) ? filter_var($filters['has_active_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
        $has_past_enrollments = isset($filters['has_past_enrollments']) ? filter_var($filters['has_past_enrollments'], FILTER_VALIDATE_BOOLEAN) : false;
        $is_disabled = isset($filters['is_disabled']) ? filter_var($filters['is_disabled'], FILTER_VALIDATE_BOOLEAN) : false;

        $has_mapped_courses = isset($filters['has_mapped_courses']) ? filter_var($filters['has_mapped_courses'], FILTER_VALIDATE_BOOLEAN) : null;
        
        // Additional parameters
        $batch_id = $filters['batch_id'] ?? 0;
        $enrollment_id = $filters['enrollment_id'] ?? 0;
        $org_id = $filters['org_id'] ?? 0;
        $category_ids = $filters['category_ids'] ?? [];
        $mapped_courses_filter = $filters['mapped_courses'] ?? []; // Renamed to avoid confusion with $mapped_courses from ES results
        


        // Convert category_ids from string to array if needed
        if (!empty($category_ids) && is_string($category_ids)) {
            // Check if it's in format [id1,id2,id3] and parse it
            if (preg_match('/^\[(.*)\]$/', $category_ids, $matches)) {
                $category_ids = array_map('intval', explode(',', $matches[1]));
            } 
            // Or if it's just a comma-separated list without brackets
            else if (strpos($category_ids, ',') !== false) {
                $category_ids = array_map('intval', explode(',', $category_ids));
            }
            // Or if it's a single value
            else {
                $category_ids = [(int)$category_ids];
            }
        }
        
        // Remove empty values
        $category_ids = array_filter($category_ids);

        $must = [];

        // Add filter for completed signup status - REMOVING THIS TO FETCH ALL INSTRUCTORS AS PER LATEST REQUEST
        // $must[] = [
        //     "match" => [
        //         "data.details.is_completed_step_3" => $is_completed
        //     ]
        // ];

        // Add filter for account status if specified
        if (!empty($status)) { // $status comes from $filters['status']
            if ($status == "disabled") {
                // If status is 'disabled', fetch all whose account_login_status is NOT 'active'
                $must[] = [
                    "bool" => [
                        "must_not" => [
                            [ "match" => [ "data.details.account_login_status.keyword" => "active" ] ]
                        ]
                    ]
                ];
            } else {
                // For any other status value, perform an exact match
                $must[] = [
                    "match" => [
                        "data.details.account_login_status.keyword" => $status
                    ]
                ];
            }
        }

        // Add filter for video conferencing status if specified
        if (!empty($vc_status) && $vc_status !== "all" && in_array($vc_status, ["free", "zoom", "google-meet"])) {
            $must[] = [
                "match" => [
                    "data.details.vc_status" => $vc_status
                ]
            ];
        }

        // Add filter for native language if specified
        if (!empty($native_language) && $native_language !== "all") {
            $native_language = str_replace("â��", " ", $native_language);
            $native_language = urldecode($native_language);
            $must[] = [
                "match" => [
                    "data.details.native_language" => $native_language
                ]
            ];
        }

        // Add filter for course ID if specified
        if (!empty($course_id) && $course_id != 0) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_courses" => [$course_id]
                ]
            ];
        }

        // Add filter for category ID if specified
        if (!empty($category_id) && $category_id != 0) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_categories" => [$category_id]
                ]
            ];
        }

        // Add filter for category IDs array if specified - using AND logic
        if (!empty($category_ids) && is_array($category_ids)) {
            // Use a simple terms query with minimum_should_match parameter
            // to ensure ALL categories must be present
            $should = [];
            foreach ($category_ids as $cat_id) {
                $should[] = [
                    "term" => [
                        "data.details.mapped_categories" => $cat_id
                    ]
                ];
            }
            
            $must[] = [
                "bool" => [
                    "should" => $should,
                    "minimum_should_match" => count($should)  // ALL must match
                ]
            ];
        }

        // Add filter for featured status if specified
        if ($is_featured !== null) {
            if ($is_featured === true) {
                // Filter for featured instructors
                $should = [
                    ["term" => ["data.details.is_featured" => true]],
                    ["term" => ["data.details.is_featured" => "true"]],
                    ["term" => ["data.details.is_featured" => 1]],
                    ["term" => ["data.details.is_featured" => "1"]]
                ];
                
                $must[] = [
                    "bool" => [
                        "should" => $should,
                        "minimum_should_match" => 1
                    ]
                ];
            } else {
                // Filter for non-featured instructors
                $must_not = [
                    ["term" => ["data.details.is_featured" => true]],
                    ["term" => ["data.details.is_featured" => "true"]],
                    ["term" => ["data.details.is_featured" => 1]],
                    ["term" => ["data.details.is_featured" => "1"]]
                ];
                
                $must[] = [
                    "bool" => [
                        "must_not" => $must_not
                    ]
                ];
            }
        }

        // Add filter for average rating if specified
        if (!empty($avg_rating)) {
            $must[] = [
                "range" => [
                    "data.details.avg_rating" => [
                        "gte" => $avg_rating,
                        "lte" => "5.0"
                    ]
                ]
            ];
        }

        // Add filter for mapped courses if specified for Elasticsearch initial filtering
        // We'll also handle this again in post-filtering for more precision
        if ($has_mapped_courses !== null) {
            if ($has_mapped_courses === true) {
                // For true case, ensure mapped_courses field exists
                $must[] = [
                    "exists" => [
                        "field" => "data.details.mapped_courses"
                    ]
                ];
            } else if ($has_mapped_courses === false) {
                // For false case, we want instructors without mapped courses
                // Since an empty array still means "exists", we need to rely on post-filtering
                // but we can exclude the ones that definitely have values
                $must[] = [
                    "bool" => [
                        "must_not" => [
                            [
                                "exists" => [
                                    "field" => "data.details.mapped_courses"
                                ]
                            ]
                        ]
                    ]
                ];
            }
        }

        // For active batches - we'll handle post-filtering later
        $need_batch_filtering = $has_active_batches !== null;
        
        // For past batches filtering
        $need_past_batch_filtering = $has_past_batches !== null;

        // Add filter for past enrollments if specified
        if ($has_past_enrollments) {
            $must[] = [
                "match" => [
                    "data.details.completed_enrolments" => true
                ]
            ];
        }

        // // Add filter for disabled status if specified
        // if ($is_disabled) {
        //     $must[] = [
        //         "match" => [
        //             "data.details.is_enable" => false
        //         ]
        //     ];
        // }

        // Add filter for specific user ID if specified
        // THIS ENTIRE BLOCK SHOULD BE REMOVED
        // if (!empty($user_id)) { 
        //     $must[] = [
        //         "term" => [
        //             "data.details.user_id" => $user_id
        //         ]
        //     ];
        // }

        // Add filter for batch ID if specified
        if (!empty($batch_id)) {
            $must[] = [
                "terms" => [
                    "data.details.batch_ids" => [$batch_id]
                ]
            ];
        }

        // Add filter for enrollment ID if specified
        if (!empty($enrollment_id)) {
            $must[] = [
                "terms" => [
                    "data.details.enrollment_ids" => [$enrollment_id]
                ]
            ];
        }

        // Add filter for specific mapped courses if specified
        if (!empty($mapped_courses_filter)) {
            $must[] = [
                "terms" => [
                    "data.details.mapped_courses" => $mapped_courses_filter
                ]
            ];
        }

        // Days filter (unchanged, as per original)
        if ($days != "0") {
            $calculatedDate = date('Y-m-d', strtotime('-' . $days . ' days'));
            // Placeholder as per original code
        }

        // Handle the is_disabled filter
        $filter_as_disabled = null; // Default: no filter based on is_disabled
        if (isset($filters['is_disabled'])) {
            $val = $filters['is_disabled'];
            if ($val === '1' || $val === 1 || $val === 'true' || $val === true) {
                $filter_as_disabled = true; // User wants to filter FOR disabled instructors
            } else if ($val === '0' || $val === 0 || $val === 'false' || $val === false) {
                $filter_as_disabled = false; // User wants to filter FOR enabled (active) instructors
            }
        }

        if ($filter_as_disabled === true) {
            // Instructors whose account_login_status is NOT 'active'
            $must[] = [
                "bool" => [
                    "must_not" => [
                        [ "match" => [ "data.details.account_login_status.keyword" => "active" ] ]
                    ]
                ]
            ];
        } elseif ($filter_as_disabled === false) {
            // Instructors whose account_login_status IS 'active'
            $must[] = [
                "match" => [ "data.details.account_login_status.keyword" => "active" ]
            ];
        }
        // If $filter_as_disabled is null (i.e., param not set or invalid), no specific ES filter is added by this block.
        
        // Handle the general 'status' filter (from $filters['status']) independently
        $status_filter_value = $filters['status'] ?? null;
        if (!empty($status_filter_value)) {
            // Map "disabled" to "de-active" for consistency with original behavior of this specific status value
            $actual_status_to_filter = ($status_filter_value === "disabled") ? "de-active" : $status_filter_value;
            $must[] = [
                "match" => [
                    "data.details.account_login_status.keyword" => $actual_status_to_filter
                ]
            ];
        }
        
        // The old is_disabled logic based on data.details.is_enable (using a boolean $is_disabled flag) 
        // should be ensured it's fully removed if any remnants existed before this block.

        // Construct the Elasticsearch query with minimal _source fields
        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "data.details.user_id",
                "data.details.is_featured",
                "data.details.first_name",
                "data.details.last_name",
                "data.details.mapped_courses",
                "data.details.mapped_categories",
                "data.details.vc_status",
                "data.details.is_active",
                "data.details.avg_rating",
                "data.details.is_enable",
                "data.details.native_language",
                "data.details.account_login_status", // Ensure this is requested
                "data.details.active_batches",
                "data.details.learner_avg_class_rating",
                "data.details.staff_avg_class_rating",
                "data.details.reviews_count",
                "data.details.batch_ids",
                "data.details.enrollment_ids",
                "data.details.org_id",
                "data.details.details_from_org"
            ]
        ];

        // Remove from and size for Elasticsearch pagination from $curlPost body
        // $curlPost["from"] = $offset; // REVERTED
        // $curlPost["size"] = $limit; // REVERTED

        // Execute the search query with a fixed fetch size for ES
        $fetch_size = $limit; // Use the processed limit from filters (controller defaults to 10000 if not set by user)
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/instructorsignedup/_search?size=" . $fetch_size, // Re-add ?size= to URL
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('cURL Error: ' . $err);
            return false;
        }

        $results = json_decode($response, true);
        $total_elasticsearch_hits = $results['hits']['total']['value'] ?? 0;

        if (empty($results) || empty($results['hits']['hits'])) {
            // Return empty response structure
            return [
                'code' => 204,
                'message' => 'No Data Found',
                'status' => 'success', 
                'count' => 0, 
                'data' => [] 
            ];
        }

        // Extract all instructor IDs to perform batch operations
        $user_ids = [];
        foreach ($results['hits']['hits'] as $result) {
            $user_ids[] = $result['_source']['data']['details']['user_id'];
        }
        $user_ids = array_unique($user_ids);
        
        // PERFORMANCE OPTIMIZATION:
        // Batch query for active batches instead of individual queries per instructor
        $activeBatchesCounts = [];
        $pastBatchesCounts = [];
        
        if ($need_batch_filtering || $has_past_batches !== null) {
            $activeBatchesCounts = $this->getBatchCountsForInstructors($user_ids, true);
            $pastBatchesCounts = $this->getBatchCountsForInstructors($user_ids, false);
        }

        // Collect unique IDs for batch queries - more efficiently
        $course_ids = [];
        $category_ids = [];
        
        // Use references to avoid array copies
        foreach ($results['hits']['hits'] as &$result) {
            $details = &$result['_source']['data']['details'];
            
            // Only process if the arrays are non-empty
            if (!empty($details['mapped_courses'])) {
                foreach ($details['mapped_courses'] as $id) {
                    $course_ids[$id] = true; // Use as keys for O(1) uniqueness
                }
            }
            
            if (!empty($details['mapped_categories'])) {
                foreach ($details['mapped_categories'] as $id) {
                    $category_ids[$id] = true; // Use as keys for O(1) uniqueness
                }
            }
        }
        
        // Convert to regular arrays
        $course_ids = array_keys($course_ids);
        $category_ids = array_keys($category_ids);
        
        // Free memory
        unset($result);
        unset($details);

        // Batch fetch course titles using mget
        $course_titles = [];
        if (!empty($course_ids)) {
            $mget_body = [
                'ids' => array_map(function($id) { return 'course-' . $id; }, $course_ids)
            ];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_mget",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mget_body, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => [
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "content-type: application/json"
                ],
            ]);
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);

            if (!$err) {
                $mget_results = json_decode($response, true);
                if (!empty($mget_results['docs'])) {
                foreach ($mget_results['docs'] as $doc) {
                    if ($doc['found']) {
                        $course_id = str_replace('course-', '', $doc['_id']);
                        $course_titles[$course_id] = $doc['_source']['data']['details']['title'] ?? "Course " . $course_id;
                        }
                    }
                }
            }
        }

        // Batch fetch category names
        $category_names = [];
        if (!empty($category_ids)) {
            $terms = get_terms([
                'taxonomy' => 'course_category',
                'include' => $category_ids,
                'hide_empty' => false,
                'fields' => 'id=>name'
            ]);
            if (!is_wp_error($terms)) {
                $category_names = $terms;
            }
        }

        // Batch fetch user data and metadata
        $user_data = [];
        if (!empty($user_ids)) {
            // Get user objects with limited fields for efficiency
            $users = get_users([
                'include' => $user_ids,
                'fields' => ['ID', 'user_login', 'user_email', 'roles']
            ]);
            
            // Prime the WordPress metadata cache to make all subsequent get_user_meta calls faster
            update_meta_cache('user', $user_ids);
            
            // Build the lookup array
            foreach ($users as $user) {
                // Get metadata with the cache already primed
                $meta = get_user_meta($user->ID, '', true);
                $user_data[$user->ID] = [
                    'userdata' => $user,
                    'meta' => $meta
                ];
            }
            
            // Free up memory
            unset($users);
        }
        
        // Pre-compute site URL
        $site_url = site_url();

        // Initialize for new logic
        $is_org_id_filter_active = !empty($filters['org_id']);
        $batchedAffiliationData = [
            'instructor_course_map' => [],
            'course_academy_map' => [],
            'academy_org_map' => [],
            'all_org_details_map' => [], // To store full organization details
        ];
        $candidate_rows = []; // To store instructors after initial filtering

        // Process each instructor - First Pass: Filtering and Candidate Collection
        // $rows = []; // Old $rows initialization, replaced by $candidate_rows
        foreach ($results['hits']['hits'] as $result) {
            $details = $result['_source']['data']['details'];
            $user_id = $details['user_id'];

            // --- START: New filter for has_active_enrollments ---
            $passes_active_enrollment_filter = true; // Assume true unless has_active_enrollments is set and conditions fail
            $current_instructor_linked_batch_info_for_candidate = []; // To store if needed for second pass

            if (isset($filters['has_active_enrollments'])) {
                if (empty($details['mapped_courses'])) {
                    $passes_active_enrollment_filter = false;
                } else {
                    // Get batch info, as this is needed for date checking and for the response
                    $current_instructor_linked_batch_info_for_candidate = $this->_getBatchesForInstructorFromCourses($user_id, $details['mapped_courses']);
                    
                    if (empty($current_instructor_linked_batch_info_for_candidate)) {
                        $passes_active_enrollment_filter = false; // No linked batches found for this instructor in those courses
                    } else {
                        $has_at_least_one_future_batch = false;
                        $currentDateTime = new \DateTime();

                        foreach ($current_instructor_linked_batch_info_for_candidate as $batch_info) {
                            if (!empty($batch_info['batch_end_date'])) {
                                try {
                                    $batchEndDateTime = new \DateTime($batch_info['batch_end_date']);
                                    if ($batchEndDateTime >= $currentDateTime) {
                                        $has_at_least_one_future_batch = true;
                                        break; // Found one, no need to check further for this instructor
                                    }
                                } catch (\Exception $e) {
                                    // Log error or handle invalid date format if necessary
                                    error_log("Error parsing batch_end_date '" . $batch_info['batch_end_date'] . "' for batch_id '" . $batch_info['batch_id'] . "': " . $e->getMessage());
                                }
                            }
                        }
                        if (!$has_at_least_one_future_batch) {
                            $passes_active_enrollment_filter = false; // All batches are in the past
                        }
                    }
                }
            }

            if (!$passes_active_enrollment_filter) {
                continue; // Skip instructor if they don't pass the active enrollment filter (if active)
            }
            // --- END: New filter for has_active_enrollments ---

            // Fetch user data
            if (!isset($user_data[$user_id])) {
                continue; // Skip if user data not found
            }
            
            // Check batch counts for filtering
            $activeBatchesCount = $activeBatchesCounts[$user_id] ?? 0;
            $pastBatchesCount = $pastBatchesCounts[$user_id] ?? 0;
            
            // Combine all filtering conditions into a single evaluation
            $shouldSkip = false;
            
            // PHP Post-processing for is_disabled
            $is_disabled_param_value = $filters['is_disabled'] ?? '0';
            if ($is_disabled_param_value === '1') {
                $instructor_account_status = $details['account_login_status'] ?? 'inactive'; // Get status from ES details
                if ($instructor_account_status === 'active') {
                    $shouldSkip = true; // Skip if status is 'active' when is_disabled=1
                }
            }

            // PHP Post-processing for general 'status' filter (if you still want it after making is_disabled a PHP filter)
            // This ensures it works in AND with other PHP filters.
            if (!$shouldSkip) { // Only apply if not already skipped
                $status_param_value = $filters['status'] ?? null;
                if (!empty($status_param_value)) {
                    $instructor_account_status = $details['account_login_status'] ?? 'inactive';
                    $target_status = ($status_param_value === 'disabled') ? 'de-active' : $status_param_value;
                    if ($instructor_account_status !== $target_status) {
                        $shouldSkip = true;
                    }
                }
            }

            // Check active batches filter (NEW LOGIC INTEGRATION)
            $current_instructor_mapped_courses = !empty($details['mapped_courses']) ? $details['mapped_courses'] : [];
            $linked_batches_data_for_response = []; // Initialize for storing detailed batch info

            // $has_active_batches is a boolean (true/false) or null, determined earlier from $filters['has_active_batches']
            if ($has_active_batches === true) { // User explicitly set has_active_batches=1
                if (empty($current_instructor_mapped_courses)) {
                    $shouldSkip = true;
                } else {
                    // Get detailed batch info (batch_id, instructor_id from batch_details)
                    $linked_batches_data_for_response = $this->_getLinkedBatchesInfoFromCourses($user_id, $current_instructor_mapped_courses);
                    if (empty($linked_batches_data_for_response)) {
                        $shouldSkip = true; // No linked batches found by the new method
                    }
                    // If not empty, $linked_batches_data_for_response will be stored in candidate_rows
                }
            } else if ($has_active_batches === false) { // User explicitly set has_active_batches=0
                // Current behavior for 0: skip if they DO have active batches (using old definition of active from getBatchCountsForInstructors)
                $currentActiveBatchesCountForOldLogic = $activeBatchesCounts[$user_id] ?? 0;
                if ($currentActiveBatchesCountForOldLogic > 0) {
                    $shouldSkip = true;
                }
            }
            // If $has_active_batches is null (not provided), no filtering based on active batches occurs from this specific conditional.

            // Check past batches filter
            if (!$shouldSkip && $need_past_batch_filtering && (
                ($has_past_batches === true && $pastBatchesCount === 0) || 
                ($has_past_batches === false && $pastBatchesCount > 0)
            )) {
                $shouldSkip = true;
            }
            
            // Check mapped courses filter
            if (!$shouldSkip && $has_mapped_courses !== null) {
                $instructor_mapped_courses = !empty($details['mapped_courses']) ? $details['mapped_courses'] : [];
                $course_count = count($instructor_mapped_courses);
                
                if (($has_mapped_courses === true && $course_count === 0) || 
                    ($has_mapped_courses === false && $course_count > 0)) {
                    $shouldSkip = true;
                }
            }
            
            // Skip to next iteration if any filter condition failed
            if ($shouldSkip) {
                continue;
            }
            
            $userdata = $user_data[$user_id]['userdata'];
            $user_meta = $user_data[$user_id]['meta'];

            // Extract user metadata
            $profile_user_id = $user_meta['profile_user_id_reference'][0] ?? null;
            $first_name = $user_meta['yuno_first_name'][0] ?? '';
            $last_name = $user_meta['yuno_last_name'][0] ?? '';
            $image_url = $user_meta['googleplus_profile_img'][0] ?? '';
            $status_value = $user_meta['account_login_status'][0] ?? 'inactive';
            $about = $user_meta['Instructor_About'][0] ?? '';

            // Get enrollments data (using empty placeholder for now)
            $activeEnrollmentsData = [];
            $pastEnrollmentsData = [];
            $activeLearners = [];
            $pastLearners = [];

            // Get profile URL
            $instructor_profile_url = $profile_user_id ? get_permalink($profile_user_id) : null;
            $profile_url = $instructor_profile_url ?: ($site_url . "/profile/" . strtolower($first_name) . "-" . strtolower($last_name));

            // Determine account status
            $account_status = ($status_value == "active");

            // --- START: New logic for has_active_enrollments --- 
            $linked_batch_ids_for_response = [];
            $linked_course_ids_for_response = [];

            if (isset($filters['has_active_enrollments'])) {
                // Fetch all mapped courses for the instructor first
                $instructor_es_data = $this->es->read('instructorsignedup', 'instructorsignedup-' . $user_id);
                $current_instructor_mapped_courses_from_es = [];
                if ($instructor_es_data['status_code'] === 200 && isset($instructor_es_data['body']['_source']['data']['details']['mapped_courses'])) {
                    $current_instructor_mapped_courses_from_es = $instructor_es_data['body']['_source']['data']['details']['mapped_courses'];
                }
                
                if (!empty($current_instructor_mapped_courses_from_es)) {
                    list($linked_batch_ids_for_response, $linked_course_ids_for_response) = $this->_getInstructorLinkedBatchAndCourseIds($user_id, $current_instructor_mapped_courses_from_es);
                }
            }
            // --- END: New logic for has_active_enrollments ---

            // Handle null or empty values
            $is_active = !empty($details['is_active']) ? $details['is_active'] : false;
            $is_enable = !empty($details['is_enable']) ? $details['is_enable'] : false;
            $mapped_courses = !empty($details['mapped_courses']) ? $details['mapped_courses'] : [];
            $mapped_categories = !empty($details['mapped_categories']) ? $details['mapped_categories'] : [];
            $native_language = !empty($details['native_language']) ? $details['native_language'] : 'English';

            $org_name = ''; // Initialize org_name
            if (!empty($details['org_id'])) {
                $org_id = (int)$details['org_id'];
                $org_source = "direct";
            } elseif (!empty($details['details_from_org']) && is_array($details['details_from_org'])) {
                foreach ($details['details_from_org'] as $org_detail) {
                    if (!empty($org_detail['org_id'])) {
                        $org_id = (int)$org_detail['org_id'];
                        $org_source = "details_from_org";
                        $org_details_from_array = $details['details_from_org'];
                        break;
                    }
                }
            }
            
            if ($org_id > 0) {
                $org_data = get_option('yuno_organization_' . $org_id);
                $org_name = !empty($org_data['name']) ? $org_data['name'] : $org_name;
            }

            // Prepare mapped courses array more efficiently
            $mapped_courses_array = [];
            if (!empty($mapped_courses_filter)) {
                $mapped_courses_array = array_map(function($course_id) use ($course_titles) {
                    return [
                        "id" => $course_id,
                        "title" => $course_titles[$course_id] ?? "Course " . $course_id
                    ];
                }, $mapped_courses_filter);
            }
            
            if (empty($mapped_courses_array)) {
                $mapped_courses_array = [["id" => 0, "title" => "Default Course"]];
            }

            // Prepare categories array more efficiently
            $categories_array = [];
            if (!empty($mapped_categories)) {
                $categories_array = array_map(function($category_id) use ($category_names) {
                    return [
                        "id" => $category_id,
                        "title" => $category_names[$category_id] ?? "Category " . $category_id
                    ];
                }, $mapped_categories);
            }
            
            if (empty($categories_array)) {
                $categories_array = [["id" => 0, "title" => "Default Category"]];
            }
            


            // Construct courses_data using pre-fetched aggregated data
            $currentInstructorMappedCourses = $instructorToMappedCourses[$user_id] ?? [];
            $currentCourseAcademies = [];
            $currentAcademyOrgsMap = []; // academy_id => org_id
            $instructorUniqueOrgIds = []; // org_id => true

            if (!empty($currentInstructorMappedCourses)) {
                foreach ($currentInstructorMappedCourses as $courseId) {
                    $courseIdStr = (string)$courseId;
                    $academiesForCourse = $courseToAcademies[$courseIdStr] ?? [];
                    if (!empty($academiesForCourse)) {
                        $currentCourseAcademies[$courseIdStr] = $academiesForCourse;
                        foreach ($academiesForCourse as $academyId) {
                            $academyIdStr = (string)$academyId;
                            $orgIdForAcademy = $academyToOrg[$academyIdStr] ?? null;
                            if ($orgIdForAcademy) {
                                $currentAcademyOrgsMap[$academyIdStr] = $orgIdForAcademy;
                                $instructorUniqueOrgIds[$orgIdForAcademy] = true;
                            }
                        }
                    }
                }
            }
            $instructorUniqueOrgIdsList = array_keys($instructorUniqueOrgIds);

            // Apply org_id filter if active
            if (!empty($filters['org_id'])) { 
                $filter_context_data = $this->getInstructorMappedCoursesAcademies($user_id, $filters['org_id']);
                
                $instructors_academy_orgs = !empty($filter_context_data['academy_orgs']) ? array_map('strval', array_values($filter_context_data['academy_orgs'])) : [];
                $instructors_academy_orgs = array_unique($instructors_academy_orgs);
                
                // CORRECTED: $filter_context_data['organization_data'] is already an array of org IDs (strings)
                // from getInstructorMappedCoursesAcademies when $filters['org_id'] is passed.
                $target_org_ids_for_filter = $filter_context_data['organization_data'] ?? [];
                // Ensure all elements are strings, though getInstructorMappedCoursesAcademies should already do this.
                $target_org_ids_for_filter = array_map('strval', $target_org_ids_for_filter);

                $matchFound = false;
                if (!empty($instructors_academy_orgs) && !empty($target_org_ids_for_filter)) {
                    foreach ($instructors_academy_orgs as $inst_org_id) {
                        // $inst_org_id is already a string
                        if (in_array($inst_org_id, $target_org_ids_for_filter, true)) {
                            $matchFound = true;
                            break;
                        }
                    }
                }

                if (!$matchFound) {
                    continue; 
                }
            }
            // The following block related to $currentInstructorMappedCourses, $courseToAcademies etc. is removed.
            // It was causing issues and its logic will be replaced by the new batched approach or the per-instructor call within the second loop.

            // Instead of building full $instructor_data, add candidate data to $candidate_rows
            $candidate_rows[] = [
                'user_id' => $user_id,
                'details' => $details, // Elasticsearch details
                'userdata' => $userdata, // WP_User object
                'user_meta' => $user_meta, // WordPress user meta
                'activeBatchesCount' => $activeBatchesCount,
                'pastBatchesCount' => $pastBatchesCount,
                'first_name' => $first_name, // For sorting
                'last_name' => $last_name, // For sorting
                'image_url' => $image_url,
                'about' => $about,
                'profile_user_id' => $profile_user_id,
                'status_value' => $status_value,
                'native_language_from_details' => $native_language, // from ES details
                'org_id_from_details' => $org_id ?? 0, 
                'org_name_from_details' => $org_name ?? '',
                'org_source_from_details' => $org_source ?? '',
                // Store linked_batches_data if has_active_batches=1 was processed and data found, otherwise store empty array
                'linked_batches_data_for_response' => !empty($linked_batches_data_for_response) ? $linked_batches_data_for_response : [],
                'account_login_status_from_es' => $details['account_login_status'] ?? 'inactive', // Store status from ES for use in second pass
                'linked_batch_info_for_candidate' => $current_instructor_linked_batch_info_for_candidate // Store for second pass if has_active_enrollments was set
            ];
            // The old $rows[] = $instructor_data and its preceding lines for $outputCoursesData are removed from this loop.
        }

        // Sort candidate instructors by name (using pre-extracted first_name, last_name for efficiency)
        usort($candidate_rows, function($a, $b) {
            $name_a = ($a['first_name'] ?? '') . " " . ($a['last_name'] ?? '');
            $name_b = ($b['first_name'] ?? '') . " " . ($b['last_name'] ?? '');
            return strcmp(trim($name_a), trim($name_b));
        });

        // Free memory from large unneeded arrays (original ES results are now processed into $candidate_rows)
        unset($results);
        // $user_data might still be needed for the second pass if not all data is in candidate_rows
        // unset($course_titles); // Potentially needed if not part of batched data strategy
        // unset($category_names); // Potentially needed
        unset($activeBatchesCounts); // These were per-instructor, counts now stored in $candidate_rows
        unset($pastBatchesCounts); // Same as above

        // PHP-based pagination is now re-instated.
        $total_count_after_filtering = count($candidate_rows); // Count before PHP pagination
        $paginated_candidate_rows = array_slice($candidate_rows, $offset, $limit); // RE-ADD PHP array_slice
        
        // Determine viewType from filters, default to 'grid'
        $viewType = $filters['viewType'] ?? 'grid';

        // Prepare for the second pass - final data construction
        $final_instructor_rows = [];
        $paginated_instructor_ids = array_map(function($candidate) { return $candidate['user_id']; }, $paginated_candidate_rows);

        // CONDITIONAL BATCHED DATA FETCHING
        if (!$is_org_id_filter_active && !empty($paginated_instructor_ids)) {
            $batchedAffiliationData = $this->_getBatchedAffiliationDataForOutput($paginated_instructor_ids);
        }

        // Second Pass: Construct final output for paginated instructors
        foreach ($paginated_candidate_rows as $candidate) {
            // Extract data from candidate for clarity
            $user_id = $candidate['user_id'];
            $details = $candidate['details']; // Elasticsearch details
            $userdata = $candidate['userdata'];
            $user_meta = $candidate['user_meta'];
            $activeBatchesCount = $candidate['activeBatchesCount'];
            $pastBatchesCount = $candidate['pastBatchesCount'];
            
            $first_name = $candidate['first_name'];
            $last_name = $candidate['last_name'];
            $image_url = $candidate['image_url'];
            $about = $candidate['about'];
            $profile_user_id = $candidate['profile_user_id'];
            $status_value = $candidate['status_value'];
            $native_language = $candidate['native_language_from_details'];
            $org_id = $candidate['org_id_from_details'];
            $org_name = $candidate['org_name_from_details'];
            $org_source = $candidate['org_source_from_details'];
            $account_login_status_value = $candidate['account_login_status_from_es'] ?? 'inactive'; // Use the value from ES stored in candidate_rows

            // Retrieve mapped_course_ids if they were stored for this candidate during the first pass
            $mapped_course_ids_for_response = $candidate['final_mapped_course_ids_for_response'] ?? [];

            // Get profile URL
            $instructor_profile_url = $profile_user_id ? get_permalink($profile_user_id) : null;
            $profile_url = $instructor_profile_url ?: ($site_url . "/profile/" . strtolower($first_name) . "-" . strtolower($last_name));

            // Determine account status
            $account_status = ($status_value == "active");

            // Prepare courses_data
            $outputCoursesData = [];
            if ($is_org_id_filter_active) {
                $instructor_specific_affiliation_data = $this->getInstructorMappedCoursesAcademies($user_id, null);
                $outputCoursesData = [
                    "mapped_courses" => $instructor_specific_affiliation_data['mapped_courses'] ?? [],
                    "course_academies" => $instructor_specific_affiliation_data['course_academies'] ?? [],
                    "academy_orgs" => $instructor_specific_affiliation_data['academy_orgs'] ?? [],
                    "organization_data" => $instructor_specific_affiliation_data['organization_data'] ?? []
                ];
            } else {
                $currentInstructorMappedCourseIds = $batchedAffiliationData['instructor_course_map'][$user_id] ?? [];
                $currentInstructorMappedCoursesOutput = [];
                $currentInstructorCourseAcademiesOutput = [];
                $currentInstructorAcademyOrgsOutput = [];
                $currentInstructorUniqueOrgIdsForOutput = [];

                if (!empty($currentInstructorMappedCourseIds)) {
                    foreach ($currentInstructorMappedCourseIds as $mc_id) {
                        $mc_id_str = (string)$mc_id;
                        $currentInstructorMappedCoursesOutput[] = [
                            "id" => (int)$mc_id,
                            "title" => $course_titles[$mc_id] ?? ("Course " . $mc_id) 
                        ];

                        $academiesForCourse = $batchedAffiliationData['course_academy_map'][$mc_id_str] ?? [];
                        if (!empty($academiesForCourse)) {
                            $currentInstructorCourseAcademiesOutput[$mc_id_str] = array_map('intval', $academiesForCourse); // Ensure academy IDs are integers
                            foreach ($academiesForCourse as $acad_id) {
                                $acad_id_str = (string)$acad_id;
                                $orgIdForAcademy = $batchedAffiliationData['academy_org_map'][$acad_id_str] ?? null;
                                if ($orgIdForAcademy) {
                                    $currentInstructorAcademyOrgsOutput[$acad_id_str] = (string)$orgIdForAcademy;
                                    $currentInstructorUniqueOrgIdsForOutput[(string)$orgIdForAcademy] = true;
                                }
                            }
                        }
                    }
                }
                
                $organizationDataForOutput = [];
                if (!empty($currentInstructorUniqueOrgIdsForOutput)) {
                    foreach (array_keys($currentInstructorUniqueOrgIdsForOutput) as $org_id_for_output_str) {
                        if (isset($batchedAffiliationData['all_org_details_map'][(int)$org_id_for_output_str])) {
                            $organizationDataForOutput[] = $batchedAffiliationData['all_org_details_map'][(int)$org_id_for_output_str];
                        } else {
                             $org_details_temp = get_option('yuno_organization_' . (int)$org_id_for_output_str); 
                             if ($org_details_temp && isset($org_details_temp['name'])) {
                                 $organizationDataForOutput[] = ['id' => (int)$org_id_for_output_str, 'name' => $org_details_temp['name']];
                             } else {
                                 $organizationDataForOutput[] = ['id' => (int)$org_id_for_output_str, 'name' => 'Organization ' . $org_id_for_output_str];
                             }
                        }
                    }
                }

                $outputCoursesData = [
                    "mapped_courses" => !empty($currentInstructorMappedCoursesOutput) ? $currentInstructorMappedCoursesOutput : [["id" => 0, "title" => "Default Course"]],
                    "course_academies" => $currentInstructorCourseAcademiesOutput,
                    "academy_orgs" => $currentInstructorAcademyOrgsOutput,
                    "organization_data" => $organizationDataForOutput
                ];
            }

            $instructor_data = [
                "instructor" => [
                    "user" => [
                        "id" => $user_id,
                        "role" => [$userdata->roles[0] ?? 'um_instructor'],
                        "full_name" => $first_name . " " . $last_name,
                        "image_url" => $image_url
                    ],
                    "about" => $about,
                    "profile_url" => $profile_url,
                    "native_language" => [
                        "name_in_english" => $native_language,
                        "native_lang_name" => $native_language,
                        "code" => substr(strtolower($native_language), 0, 2)
                    ],
                    "fluent_languages" => [
                        [
                            "name_in_english" => $native_language,
                            "native_lang_name" => $native_language,
                            "code" => substr(strtolower($native_language), 0, 2)
                        ]
                    ],
                    "avg_rating" => (float)($details['learner_avg_class_rating'] ?? 0),
                    "max_rating" => (float)($details['staff_avg_class_rating'] ?? 0),
                    "review_count" => (int)($details['reviews_count'] ?? 0),
                    "active_learners" => 0, 
                    "past_learners" => 0,
                    "account_login_status" => $account_login_status_value // Use the retrieved status
                ]
            ];

            // Check active batches filter (NEW LOGIC INTEGRATION)
            // $has_active_batches is a boolean (true/false) or null, determined earlier from $filters['has_active_batches']
            $linkedBatchIdsForInstructor = []; // Initialize for potential use in response
            if ($has_active_batches === true) { // User explicitly set has_active_batches=1
                $instructor_mapped_courses_es = !empty($details['mapped_courses']) ? $details['mapped_courses'] : [];
                $linkedBatchIdsForInstructor = $this->_getInstructorLinkedBatchIdsFromCourses($user_id, $instructor_mapped_courses_es);
                if (empty($linkedBatchIdsForInstructor)) {
                    $shouldSkip = true;
                }
            } else if ($has_active_batches === false) { // User explicitly set has_active_batches=0
                $shouldSkip = true;
            }

            // Add batches_id to response if linked batch IDs were found (implies has_active_batches=1 was processed)
            if (!empty($linkedBatchIdsForInstructor)) {
                $instructor_data["instructor"]["batches_id"] = $linkedBatchIdsForInstructor;
            }

            // Add mapped_course_ids to response if they were flagged and stored from the first pass
            if (!empty($mapped_course_ids_for_response)) {
                $instructor_data["instructor"]["mapped_course_ids"] = $mapped_course_ids_for_response;
            }

            // Retrieve linked_batches_data if it was stored for this candidate during the first pass
            $linked_batches_data = $candidate['linked_batches_data_for_response'] ?? [];

            // Add linked_batch_details to response if data was found (implies has_active_batches=1 was processed and successful)
            if (!empty($linked_batches_data)) {
                $instructor_data["instructor"]["linked_batch_details"] = $linked_batches_data;
            }

            // Ensure linked_batch_details (and older variations like mapped_course_ids/batches_id from this filter) 
            // are NOT added to the response when has_active_batches=1 was the trigger.
            // The filtering decision was already made in the first pass.
            if (isset($instructor_data["instructor"]["linked_batch_details"])) {
                unset($instructor_data["instructor"]["linked_batch_details"]);
            }
            if (isset($instructor_data["instructor"]["mapped_course_ids"])) { 
                unset($instructor_data["instructor"]["mapped_course_ids"]);
            }
            if (isset($instructor_data["instructor"]["batches_id"])) { 
                unset($instructor_data["instructor"]["batches_id"]);
            }

            // --- START: Add new keys to response if has_active_enrollments was set ---
            if (isset($filters['has_active_enrollments'])) {
                $instructor_data["instructor"]["linked_batch_ids"] = $linked_batch_ids_for_response;
                $instructor_data["instructor"]["linked_course_ids_for_batches"] = $linked_course_ids_for_response;
            }
            // --- END: Add new keys to response ---

            // --- START: New logic for has_active_enrollments --- 
            if (isset($filters['has_active_enrollments'])) {
                $instructor_mapped_courses_from_doc = $details['mapped_courses'] ?? []; // Get from ES details already fetched
                $instructor_data["instructor"]["mapped_courses_from_doc"] = $instructor_mapped_courses_from_doc;
            }
            // --- END: New logic for has_active_enrollments ---

            // --- START: New logic for has_active_enrollments: Add linked_batch_info ---
            // $instructor_data["instructor"]["linked_batch_info"] = []; // Initialize to empty array - MOVED TO CANDIDATE
            if (isset($filters['has_active_enrollments'])) {
                // Retrieve the pre-calculated batch info from the candidate data
                $instructor_data["instructor"]["linked_batch_info"] = $candidate['linked_batch_info_for_candidate'] ?? [];
            }
            // --- END: New logic for has_active_enrollments ---

            // Safeguard: Ensure specific old keys are removed if they somehow persisted
            if (isset($instructor_data["instructor"]["linked_batch_ids"])) {
                unset($instructor_data["instructor"]["linked_batch_ids"]);
            }
            if (isset($instructor_data["instructor"]["linked_course_ids_for_batches"])) {
                unset($instructor_data["instructor"]["linked_course_ids_for_batches"]);
            }

            $final_instructor_rows[] = $instructor_data;
        }

        // Free memory 
        unset($user_data);
        unset($course_titles); 
        unset($category_names); 
        // unset($candidate_rows); // Already unset earlier
        
        // Format the response based on viewType
        $viewType = $filters['viewType'] ?? 'grid'; // Ensure viewType is determined here

        if ($viewType === 'list') {
            $response_data = $final_instructor_rows;
        } else { // Default to grid view structure
            $response_data = [
                [
                    'rows' => $final_instructor_rows,
                    'columns' => [
                        ["field" => "name", "label" => "Name", "sortable" => true],
                        ["field" => "signedup", "label" => "Signed up", "sortable" => true],
                        ["field" => "can_schedule_class", "label" => "VC Status", "sortable" => true],
                        ["field" => "email", "label" => "Email", "sortable" => true],
                        ["field" => "phone", "label" => "Phone", "sortable" => true],
                        ["field" => "native_lang", "label" => "Native Lang", "sortable" => true],
                        ["field" => "active_batches", "label" => "Active Batches", "sortable" => true],
                        ["field" => "mapped_courses_count", "label" => "Mapped Courses", "sortable" => true],
                        ["field" => "learner_avg_class_rating", "label" => "Learner avg. class rating", "sortable" => true],
                        ["field" => "staff_avg_class_rating", "label" => "Staff avg. class rating", "sortable" => true],
                        ["field" => "reviews_count", "label" => "Review", "sortable" => true],
                        ["field" => "last_login", "label" => "Last Login", "sortable" => true],
                        ["field" => "location", "label" => "Location", "sortable" => true]
                    ]
                ]
            ];
        }

        // Format the final response object
        $response = [
            'code' => 0,
            'message' => 'Instructor insights has been fetched successfully',
            'status' => 'success',
            'count' => $total_count_after_filtering, // Use count of records before PHP array_slice
            'data' => $response_data // Use the viewType-dependent data structure
        ];
        
        return $response;
    }

    /**
     * Get batch counts for multiple instructors in a single query
     * This method replaces individual queries with a single batch query
     * 
     * @param array $instructorIds Array of instructor user IDs
     * @param bool $isActive Whether to count active or past batches
     * @return array Associative array of instructor_id => batch_count
     */
    private function getBatchCountsForInstructors($instructorIds, $isActive = true)
    {
        if (empty($instructorIds)) {
            return [];
        }
        
        // Current date for filtering
        $currentDateTime = date("Y-m-d H:i:s");
        
        // Build Elasticsearch query for batches
        $timeRangeOperator = $isActive ? "gt" : "lt";
        
        $must = [
            [
                "terms" => [
                    "data.details.batch_details.instructor_id" => $instructorIds
                ]
            ]
        ];
        
        // For active batches, add additional filters
        if ($isActive) {
            $must[] = [
                "match" => [
                    "data.details.batch_details.active_batch" => 1
                ]
            ];
            $must[] = [
                "match" => [
                    "data.details.batch_details.batch_deleted_status" => 0
                ]
            ];
        }
        
        // Add date range filter
        $must[] = [
            "range" => [
                "data.details.batch_details.batch_end_date" => [
                    $timeRangeOperator => $currentDateTime
                ]
            ]
        ];
        
        $curlPost = [
            "_source" => [
                "data.details.batch_details.instructor_id",
                "data.details.batch_details.batch_id"
            ],
            "query" => [
                "nested" => [
                    "path" => "data.details.batch_details",
                    "query" => [
                        "bool" => [
                            "must" => $must
                        ]
                    ],
                    "inner_hits" => [
                        "size" => 10000, // Set a high limit to ensure all batches are returned
                        "_source" => ["instructor_id", "batch_id"]
                    ]
                ]
            ],
            "size" => 1000 // We only need the aggregation results
        ];
        
        // Execute Elasticsearch query
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            error_log("Error fetching batch counts: " . $err);
            return [];
        }
        
        $results = json_decode($response, true);
        
        // Initialize counts array with zeros for all instructors
        $batchCounts = array_fill_keys($instructorIds, 0);
        
        // Process results to count batches per instructor
        if (!empty($results['hits']['hits'])) {
            $key = "data.details.batch_details";
            
            foreach ($results['hits']['hits'] as $hit) {
                if (empty($hit['inner_hits'][$key]['hits']['hits'])) {
                    continue;
                }
                
                $innerHits = $hit['inner_hits'][$key]['hits']['hits'];
                
                foreach ($innerHits as $innerHit) {
                    $instructorId = $innerHit['_source']['instructor_id'] ?? null;
                    
                    if ($instructorId && in_array($instructorId, $instructorIds)) {
                        $batchCounts[$instructorId]++;
                    }
                }
            }
        }
        
        return $batchCounts;
    }

    private function _fetchAggregatedAffiliationData(array $instructorUserIds, ?string $filterOrgIdParam_UNUSED = null)
    {
        $results = [
            'instructor_to_mapped_courses' => [],
            'course_to_academies' => [],
            'academy_to_org' => [],
            // 'all_unique_org_details' => [], // We might not need to fetch full org details here, only IDs
        ];

        if (empty($instructorUserIds)) {
            return $results;
        }

        // 1. Fetch Mapped Courses for all instructors
        $instructorDocsToFetch = [];
        foreach ($instructorUserIds as $id) {
            $instructorDocsToFetch[] = ['_index' => 'instructorsignedup', '_id' => 'instructorsignedup-' . (string)$id, '_source' => ['data.details.mapped_courses']];
        }

        $allCourseIdsFlatUnique = [];
        if (!empty($instructorDocsToFetch)) {
            $mgetBody = ['docs' => $instructorDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $instructorId = (int)str_replace('instructorsignedup-', '', $doc['_id']);
                            $courses = $doc['_source']['data']['details']['mapped_courses'] ?? [];
                            $results['instructor_to_mapped_courses'][$instructorId] = $courses;
                            foreach ($courses as $courseId) {
                                if (!empty($courseId)) $allCourseIdsFlatUnique[(string)$courseId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allCourseIdsFlatUnique = array_keys($allCourseIdsFlatUnique);

        if (empty($allCourseIdsFlatUnique)) return $results;

        // 2. Fetch Course-to-Academy Mappings
        $courseDocsToFetch = [];
        foreach($allCourseIdsFlatUnique as $cid) {
             $courseDocsToFetch[] = 'course-'. (string)$cid;
        }
        
        $allAcademyIdsFlatUnique = [];
        if(!empty($courseDocsToFetch)){
            $mgetBody = ['ids' => $courseDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $academies = $doc['_source']['data']['details']['academies'] ?? [];
                            $results['course_to_academies'][$courseId] = $academies;
                            foreach ($academies as $academyId) {
                                if(!empty($academyId)) $allAcademyIdsFlatUnique[(string)$academyId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allAcademyIdsFlatUnique = array_keys($allAcademyIdsFlatUnique);

        if (empty($allAcademyIdsFlatUnique)) return $results;

        // 3. Fetch Academy-to-Org Mappings
        $academyDocsToFetch = [];
        foreach($allAcademyIdsFlatUnique as $aid){ 
            $academyDocsToFetch[] = 'academies-' . (string)$aid;
        }

        if(!empty($academyDocsToFetch)){
            $mgetBody = ['ids' => $academyDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
                CURLOPT_TIMEOUT => 30
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $orgId = $doc['_source']['data']['details']['org_id'] ?? null;
                            if ($orgId && $orgId !== 'undefined') {
                                $results['academy_to_org'][$academyId] = (string)$orgId;
                                // We don't need to collect all_unique_org_details here for now
                                // Details will be fetched on demand or if absolutely necessary later
                            }
                        }
                    }
                }
            }
        }
        return $results;
    }

    // [START RESTORE METHOD - getInstructorMappedCoursesAcademies]
    private function getInstructorMappedCoursesAcademies($instructorId, $requestedOrgId = null)
    {
        if (empty($instructorId)) {
            return [
                'mapped_courses' => [],
                'course_academies' => [],
                'academy_orgs' => [],
                'organization_data' => []
            ];
        }

        // Step 1: Get mapped courses from instructor document
        $instructorDataResponse = ['status_code' => 0, 'body' => []]; 
        if (isset($this->es)) {
            // This line might cause a linter error if $this->es is not defined, but restoring original structure.
            $instructorDataResponse = $this->es->read('instructorsignedup', 'instructorsignedup-' . $instructorId);
        }
        
        $mappedCourseIds = [];
        if ($instructorDataResponse['status_code'] === 200) {
            $instructorDetails = $instructorDataResponse['body']['_source']['data']['details'] ?? [];
            if (!empty($instructorDetails['mapped_courses'])) {
                $mappedCourseIds = $instructorDetails['mapped_courses'];
            }
        }
            
        if (empty($mappedCourseIds)) {
            return [
                'mapped_courses' => [], 
                'course_academies' => [],
                'academy_orgs' => [],
                'organization_data' => []
            ];
        }

        $courseAcademies = [];
        $validCourseIds = []; 
        $academyIdsLookup = []; 
        
        $courseIdsForQuery = array_map(function($id) {
            return 'course-' . $id;
        }, $mappedCourseIds);
        
        if (!empty($courseIdsForQuery)) {
            $mgetBody = ['ids' => $courseIdsForQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);
            
            if (!$err && $response) {
                $coursesData = json_decode($response, true);
                if (!empty($coursesData['docs'])) {
                    foreach ($coursesData['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $courseDetails = $doc['_source']['data']['details'] ?? [];
                            if (!empty($courseDetails['academies'])) {
                                $courseAcademies[$courseId] = $courseDetails['academies'];
                                $validCourseIds[] = $courseId; 
                                foreach ($courseDetails['academies'] as $academyId) { 
                                    if(!empty($academyId)) $academyIdsLookup[(string)$academyId] = true; 
                                }
                            }
                        }
                    }
                }
            }
        }
        
        $academyIds = array_keys($academyIdsLookup);
        $academyOrgs = []; 
        $orgIdsLookup = []; 

        if (!empty($academyIds)) {
            $academyDocIds = array_map(function($id) { return 'academies-' . $id; }, $academyIds);
            $mgetBody = ['ids' => $academyDocIds];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);
            
            if (!$err && $response) {
                $academiesData = json_decode($response, true);
                if (!empty($academiesData['docs'])) {
                    foreach ($academiesData['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $academyDetails = $doc['_source']['data']['details'] ?? [];
                            $orgId = $academyDetails['org_id'] ?? '';
                            if (!empty($orgId) && $orgId !== 'undefined') {
                                $orgIdStr = (string)$orgId;
                                $academyOrgs[$academyId] = $orgIdStr;
                                $orgIdsLookup[$orgIdStr] = true;
                            }
                        }
                    }
                }
            }
        }
        $uniqueOrgIdsFromAcademies = array_keys($orgIdsLookup);
        
        $finalOrganizationData = [];
        if (!empty($requestedOrgId)) {
            $orgUserMetaResult = get_user_meta($requestedOrgId, 'organisation', true);
            if (!empty($orgUserMetaResult) && is_array($orgUserMetaResult)) {
                $finalOrganizationData = array_map('strval', $orgUserMetaResult);
            } elseif (!empty($orgUserMetaResult)) { 
                $finalOrganizationData = [(string)$orgUserMetaResult];
            } else {
                $finalOrganizationData = [(string)$requestedOrgId];
            }
        } else {
            $finalOrganizationData = array_map('strval', $uniqueOrgIdsFromAcademies);
        }
        $finalOrganizationData = array_values(array_unique($finalOrganizationData));

        return [
            'mapped_courses' => $mappedCourseIds, 
            'course_academies' => $courseAcademies, 
            'academy_orgs' => $academyOrgs, 
            'organization_data' => $finalOrganizationData
        ];
    }
    // [END RESTORE METHOD]

    private function _getBatchedAffiliationDataForOutput(array $instructorUserIds)
    {
        $affiliationData = [
            'instructor_course_map' => [], // instructor_id => [course_ids]
            'course_academy_map' => [],  // course_id => [academy_ids]
            'academy_org_map' => [],     // academy_id => org_id (string)
        ];

        if (empty($instructorUserIds)) {
            return $affiliationData;
        }

        // 1. Get mapped_courses for all instructors
        $instructorDocsToFetch = [];
        foreach ($instructorUserIds as $id) {
            $instructorDocsToFetch[] = ['_index' => 'instructorsignedup', '_id' => 'instructorsignedup-' . (string)$id, '_source' => ['data.details.mapped_courses']];
        }

        $allCourseIdsUnique = []; // Store unique course_ids to fetch their academies
        if (!empty($instructorDocsToFetch)) {
            $mgetBody = ['docs' => $instructorDocsToFetch];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $instructorId = (int)str_replace('instructorsignedup-', '', $doc['_id']);
                            $courses = $doc['_source']['data']['details']['mapped_courses'] ?? [];
                            $affiliationData['instructor_course_map'][$instructorId] = $courses;
                            foreach ($courses as $courseId) {
                                if (!empty($courseId)) $allCourseIdsUnique[(string)$courseId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allCourseIdsUnique = array_keys($allCourseIdsUnique);
        if (empty($allCourseIdsUnique)) return $affiliationData; // No courses, nothing more to fetch

        // 2. Get academies for all unique courses
        $courseDocsForAcademyQuery = array_map(function ($cid) { return 'course-' . $cid; }, $allCourseIdsUnique);
        $allAcademyIdsUnique = []; // Store unique academy_ids to fetch their orgs
        if (!empty($courseDocsForAcademyQuery)) {
            $mgetBody = ['ids' => $courseDocsForAcademyQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $courseId = (string)str_replace('course-', '', $doc['_id']);
                            $academies = $doc['_source']['data']['details']['academies'] ?? [];
                            $affiliationData['course_academy_map'][$courseId] = $academies;
                            foreach ($academies as $academyId) {
                                if (!empty($academyId)) $allAcademyIdsUnique[(string)$academyId] = true;
                            }
                        }
                    }
                }
            }
        }
        $allAcademyIdsUnique = array_keys($allAcademyIdsUnique);
        if (empty($allAcademyIdsUnique)) return $affiliationData; // No academies, nothing more to fetch

        // 3. Get org_id for all unique academies
        $academyDocsForOrgQuery = array_map(function ($aid) { return 'academies-' . $aid; }, $allAcademyIdsUnique);
        if (!empty($academyDocsForOrgQuery)){
            $mgetBody = ['ids' => $academyDocsForOrgQuery];
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/academies/_mget",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($mgetBody), CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $response = curl_exec($curl); $err = curl_error($curl); curl_close($curl);

            if (!$err && $response) {
                $esResponse = json_decode($response, true);
                if (!empty($esResponse['docs'])) {
                    foreach ($esResponse['docs'] as $doc) {
                        if ($doc['found']) {
                            $academyId = (string)str_replace('academies-', '', $doc['_id']);
                            $orgId = $doc['_source']['data']['details']['org_id'] ?? null;
                            if ($orgId && $orgId !== 'undefined') {
                                $affiliationData['academy_org_map'][$academyId] = (string)$orgId;
                            }
                        }
                    }
                }
            }
        }
        return $affiliationData;
    }

    /**
     * Checks if an instructor is linked to any batch within their mapped courses.
     * Relies on instructor_id match in batch_details, ignores active status.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return array An array of unique batch_ids the instructor is linked to, or an empty array.
     */
    private function _getInstructorLinkedBatchIdsFromCourses($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [];
        }

        $linkedBatchIds = [];
        // Prepare course document IDs for mget
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            // Ensure courseId is scalar and not empty or zero if zero is invalid
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) { 
                $courseDocsToFetch[] = ['_index' => 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return $linkedBatchIds;
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in _getInstructorLinkedBatchIdsFromCourses: ' . $err);
            return $linkedBatchIds; // Or handle error as appropriate
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    // return true; // Found a link -> OLD LOGIC
                                    $linkedBatchIds[] = (string)$batch['batch_id'];
                                }
                            }
                        }
                    }
                }
            }
        }
        // return false; // No link found -> OLD LOGIC
        return array_unique($linkedBatchIds); // Return unique batch IDs
    }

    /**
     * Checks if an instructor has past batches based on batch end dates from batchenrollmentevent.
     * An instructor is considered to have past batches if they are linked to a batch
     * (via mapped_courses -> course.batch_details -> batch.instructor_id)
     * and that batch's end_date (from batchenrollmentevent) is in the past.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return bool True if past batches exist by this logic, false otherwise.
     */
    private function _checkInstructorPastBatchesByEventEndDate($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return false;
        }

        // Step 1: Collect relevant batch_ids taught by the instructor from their mapped courses
        $relevantBatchIds = [];
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                $courseDocsToFetch[] = ['_index' => 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return false;
        }

        $mgetBodyCourses = ['docs' => $courseDocsToFetch];
        $curlCourses = curl_init();
        curl_setopt_array($curlCourses, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT, CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBodyCourses, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            CURLOPT_TIMEOUT => 30
        ]);
        $responseCourses = curl_exec($curlCourses);
        $errCourses = curl_error($curlCourses);
        curl_close($curlCourses);

        if ($errCourses) {
            error_log('ES mget error for courses in _checkInstructorPastBatchesByEventEndDate: ' . $errCourses);
            return false;
        }

        if ($responseCourses) {
            $esResponseCourses = json_decode($responseCourses, true);
            if (!empty($esResponseCourses['docs'])) {
                foreach ($esResponseCourses['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetails = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetails)) {
                            foreach ($batchDetails as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $relevantBatchIds[] = (string)$batch['batch_id'];
                                }
                            }
                        }
                    }
                }
            }
        }
        
        $relevantBatchIds = array_unique($relevantBatchIds);
        if (empty($relevantBatchIds)) {
            return false; // No batches found for this instructor in their mapped courses
        }

        // Step 2: Check batch_end_date from batchenrollmentevent for these batch_ids
        $searchBodyEnrollmentEvents = [
            'query' => [
                'terms' => [
                    'data.details.batch_id' => $relevantBatchIds 
                ]
            ],
            '_source' => ['data.details.batch_end_date'],
            'size' => count($relevantBatchIds) 
        ];
        
        $curlEnrollments = curl_init();
        curl_setopt_array($curlEnrollments, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search", 
            CURLOPT_RETURNTRANSFER => true, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($searchBodyEnrollmentEvents, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            CURLOPT_TIMEOUT => 30
        ]);
        $responseEnrollments = curl_exec($curlEnrollments);
        $errEnrollments = curl_error($curlEnrollments);
        curl_close($curlEnrollments);

        if ($errEnrollments) {
            error_log('ES search error for batchenrollmentevent in _checkInstructorPastBatchesByEventEndDate: ' . $errEnrollments);
            return false;
        }
        
        if ($responseEnrollments) {
            $esResponseEnrollments = json_decode($responseEnrollments, true);
            if (isset($esResponseEnrollments['hits']['hits']) && !empty($esResponseEnrollments['hits']['hits'])) {
                $currentDateTime = new DateTime(); 
                $currentDateTimestamp = $currentDateTime->setTime(0,0,0)->getTimestamp(); 

                foreach ($esResponseEnrollments['hits']['hits'] as $hit) {
                    if (isset($hit['_source']['data']['details']['batch_end_date'])) {
                        $batchEndDateStr = $hit['_source']['data']['details']['batch_end_date'];
                        try {
                            $batchEndDateTime = new DateTime($batchEndDateStr);
                            $batchEndDateTimestamp = $batchEndDateTime->setTime(0,0,0)->getTimestamp();

                            if ($batchEndDateTimestamp < $currentDateTimestamp) { // Check if batch_end_date is strictly in the past
                                return true; // Found a past batch
                            }
                        } catch (\Exception $e) {
                            error_log("Error parsing batch_end_date ('$batchEndDateStr') in _checkInstructorPastBatchesByEventEndDate: " . $e->getMessage());
                        }
                    }
                }
            }
        }
        return false; // No past batches found by this logic
    }

    /**
     * Retrieves detailed information (batch_id, instructor_id from batch) for batches linked to an instructor 
     * through their mapped courses. Relies on instructor_id match in batch_details.
     *
     * @param int $mainInstructorId The ID of the main instructor being processed.
     * @param array $mappedCourseIds An array of course IDs mapped to the main instructor.
     * @return array An array of objects, each like {"batch_id": "...", "instructor_id": "..."}, or an empty array.
     */
    private function _getLinkedBatchesInfoFromCourses($mainInstructorId, $mappedCourseIds)
    {
        if (empty($mainInstructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [];
        }

        $linkedBatchesInfo = [];
        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                $courseDocsToFetch[] = ['_index' => 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return [];
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in _getLinkedBatchesInfoFromCourses: ' . $err);
            return [];
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetails = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetails)) {
                            foreach ($batchDetails as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$mainInstructorId && isset($batch['batch_id'])) {
                                    $linkedBatchesInfo[] = [
                                        "batch_id" => (string)$batch['batch_id'],
                                        "instructor_id" => (string)$batch['instructor_id'] // This is the instructor_id from the batch_details object
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }
        return $linkedBatchesInfo; // No array_unique needed here as objects might differ subtly if batches share IDs but other (uncollected) details differ, though unlikely for this structure.
    }

    /**
     * Retrieves all linked batch IDs and their source course IDs for an instructor.
     * This is similar to fetching active batches but without the active status check.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseIds An array of course IDs mapped to the instructor.
     * @return array An array containing two elements: [0] => array of unique batch_ids, [1] => array of unique course_ids.
     */
    private function _getInstructorLinkedBatchAndCourseIds($instructorId, $mappedCourseIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseIds) || empty($mappedCourseIds)) {
            return [[], []]; // Return empty arrays for batch_ids and course_ids
        }

        $linkedBatchIds = [];
        $linkedCourseIdsForBatches = [];

        $courseDocsToFetch = [];
        foreach ($mappedCourseIds as $courseId) {
            if (!empty($courseId) && (is_string($courseId) || is_numeric($courseId))) {
                // We need to fetch data.details.batch_details and data.details.id (course_id itself for tracking)
                $courseDocsToFetch[] = ['_index' => 'course', '_id' => 'course-' . (string)$courseId, '_source' => ['data.details.batch_details', 'data.details.id']];
            }
        }

        if (empty($courseDocsToFetch)) {
            return [[], []];
        }

        $mgetBody = ['docs' => $courseDocsToFetch];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('Elasticsearch mget error in _getInstructorLinkedBatchAndCourseIds: ' . $err);
            return [[], []];
        }

        if ($response) {
            $esResponse = json_decode($response, true);
            if (!empty($esResponse['docs'])) {
                foreach ($esResponse['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        $sourceCourseId = $doc['_source']['data']['details']['id'] ?? null; // Get the course ID from the source

                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $linkedBatchIds[] = (string)$batch['batch_id'];
                                    if ($sourceCourseId) { // Only add course ID if it was found
                                        $linkedCourseIdsForBatches[] = (string)$sourceCourseId;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return [array_values(array_unique($linkedBatchIds)), array_values(array_unique($linkedCourseIdsForBatches))];
    }

    /**
     * Retrieves batch_id and instructor_id from batch_details for an instructor's mapped courses.
     *
     * @param int $instructorId The ID of the instructor.
     * @param array $mappedCourseRecordIds An array of course record_ids mapped to the instructor.
     * @return array An array of objects, each like {"batch_id": "...", "instructor_id": "..."}.
     */
    private function _getBatchesForInstructorFromCourses($instructorId, $mappedCourseRecordIds)
    {
        if (empty($instructorId) || !is_array($mappedCourseRecordIds) || empty($mappedCourseRecordIds)) {
            return [];
        }

        $initialBatchInfoFromCourses = [];
        $uniqueBatchIdsFromCourses = [];

        $courseDocIdsToFetch = array_map(function($recordId) {
            return 'course-' . $recordId;
        }, $mappedCourseRecordIds);

        if (empty($courseDocIdsToFetch)) {
            return [];
        }

        $mgetBodyCourses = [
            'docs' => array_map(function($docId) {
                // Request batch_details which includes batch_id, instructor_id, and potentially a fallback batch_end_date
                return ['_index' => 'course', '_id' => $docId, '_source' => ['data.details.batch_details']];
            }, $courseDocIdsToFetch)
        ];

        $curlCourses = curl_init();
        curl_setopt_array($curlCourses, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_mget",
            CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($mgetBodyCourses, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
        ]);
        $responseCourses = curl_exec($curlCourses);
        $errCourses = curl_error($curlCourses);
        curl_close($curlCourses);

        if ($errCourses) {
            error_log('Elasticsearch mget error for courses in _getBatchesForInstructorFromCourses: ' . $errCourses);
            return [];
        }

        if ($responseCourses) {
            $esResponseCourses = json_decode($responseCourses, true);
            if (!empty($esResponseCourses['docs'])) {
                foreach ($esResponseCourses['docs'] as $doc) {
                    if (isset($doc['found']) && $doc['found'] === true && isset($doc['_source']['data']['details']['batch_details'])) {
                        $batchDetailsArray = $doc['_source']['data']['details']['batch_details'];
                        if (is_array($batchDetailsArray)) {
                            foreach ($batchDetailsArray as $batch) {
                                if (isset($batch['instructor_id']) && (int)$batch['instructor_id'] === (int)$instructorId && isset($batch['batch_id'])) {
                                    $current_batch_id = (string)$batch['batch_id'];
                                    $initialBatchInfoFromCourses[] = [
                                        "batch_id" => $current_batch_id,
                                        "instructor_id" => (string)$batch['instructor_id'],
                                        "batch_end_date_from_course" => $batch['batch_end_date'] ?? null
                                    ];
                                    $uniqueBatchIdsFromCourses[$current_batch_id] = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        if (empty($initialBatchInfoFromCourses)) {
            return [];
        }

        $batchIdsToQueryEnrollmentEvent = array_keys($uniqueBatchIdsFromCourses);
        $batchIdToEndDateMap = [];

        if (!empty($batchIdsToQueryEnrollmentEvent)) {
            $queryEnrollmentEventBody = [
                'query' => [
                    'terms' => [
                        'data.details.batch_id' => $batchIdsToQueryEnrollmentEvent
                    ]
                ],
                '_source' => ['data.details.batch_id', 'data.details.batch_end_date'],
                'size' => count($batchIdsToQueryEnrollmentEvent) * 5 // Assuming up to 5 events per batch on average, adjust if needed or use a high fixed number
            ];

            $curlEnrollments = curl_init();
            curl_setopt_array($curlEnrollments, [
                CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search",
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST", // Using POST for query body
                CURLOPT_POSTFIELDS => json_encode($queryEnrollmentEventBody, JSON_UNESCAPED_SLASHES),
                CURLOPT_HTTPHEADER => ["authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, "content-type: application/json"],
            ]);
            $responseEnrollments = curl_exec($curlEnrollments);
            $errEnrollments = curl_error($curlEnrollments);
            curl_close($curlEnrollments);

            if ($errEnrollments) {
                error_log('Elasticsearch search error for batchenrollmentevent in _getBatchesForInstructorFromCourses: ' . $errEnrollments);
                // Proceed without event dates if this fails, will use fallback from course
            } else if ($responseEnrollments) {
                $esResponseEnrollments = json_decode($responseEnrollments, true);
                if (!empty($esResponseEnrollments['hits']['hits'])) {
                    foreach ($esResponseEnrollments['hits']['hits'] as $hit) {
                        $event_batch_id = $hit['_source']['data']['details']['batch_id'] ?? null;
                        $event_batch_end_date = $hit['_source']['data']['details']['batch_end_date'] ?? null;
                        if ($event_batch_id && !isset($batchIdToEndDateMap[$event_batch_id])) { // Take first one found
                            $batchIdToEndDateMap[$event_batch_id] = $event_batch_end_date;
                        }
                    }
                }
            }
        }

        $finalLinkedBatchInfo = [];
        foreach ($initialBatchInfoFromCourses as $info) {
            $finalLinkedBatchInfo[] = [
                "batch_id" => $info["batch_id"],
                "instructor_id" => $info["instructor_id"],
                "batch_end_date" => $batchIdToEndDateMap[$info["batch_id"]] ?? $info["batch_end_date_from_course"] ?? null
            ];
        }

        return $finalLinkedBatchInfo;
    }
}
