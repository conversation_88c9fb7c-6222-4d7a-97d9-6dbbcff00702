YUNOTable.table();
Vue.component('yuno-category-courses-v2', {
    props: ["data", "options"],
    template: `
        <div :class="[!options.isElementVisible ? 'forAd' : '']">
            <b-loading :is-full-page="true" v-model="isInProcess" :can-cancel="false"></b-loading>
            <h1 class="largerTitle" v-if="!options.isElementVisible" style="margin-top: 30px">{{ category.category_heading }}</h1>
            <yuno-filters
                @onDropdownChange="onDropdownChange"
                @onDropdownSliderChange="onDropdownSliderChange"
                @daysTimeToggle="daysTimeToggle"
                @manageFilters="onManageFilters"
                @clearFilter="clearFilter"
                :data="payload"
                :options="{'isFilterMobile': isFilterMobile}"
            >
            </yuno-filters>
            <yuno-courses 
                @initCourses="onInitCourses"
                @defaultFilters="defaultFilters"
                :hasLearnMore="true"
                :seeAvailability="false"
            >
            </yuno-courses>
        </div>
    `,
    data() {
        return {
            isInProcess: false,
            isScroll: false,
            isFilterMobile: false,
            hideHeader: false,
            payload: {
                limit: 20,
                offset: 0,
                personalization: "all",
                category: [],
                category_level_1: [],
                category_level_2: [],
                class_days_time: [
                    {
                        selected: [],
                        slug: "class_days"
                    },
                        {
                        selected: [],
                        slug: "class_time"
                    }
                ],
                instructor_id: 0,
                price_per_hour: 10000,
                total_duration: 24
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'userProfile',
            'course',
            'categoryTaxonomy'
        ]),
        categoryFeatures() {
            return this.categoryTaxonomy.data.category_features
        },
        category() {
            return this.categoryTaxonomy.data;
        },
    },
    async created() {
        this.manageState();
        this.emitEvents();
        this.managePaymentState();
    },
    mounted() {
        
    },
    methods: {
        gotEnrollmentStatus(options, payObj) {

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;

                if (!data.is_enrolled) {
                    window.location.href = YUNOCommon.config.host() + "/checkout/";
                } else {
                    this.$buefy.dialog.alert({
                        title: 'Alert',
                        message: `You have already enrolled a batch in this course`,
                        confirmText: 'Ok'
                    });
                    this.isInProcess = false;
                    localStorage.removeItem('paymentState');
                }
            }
        },
        verifyEnrollmentStatus(payObj) {
            this.isInProcess = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.courseEnrollmentStatus(payObj.course.id, isLoggedIn),
                module: "gotData",
                store: "module",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotEnrollmentStatus(options, payObj)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        managePaymentState() {
            const paymentState = localStorage.getItem('paymentState');

            if (paymentState !== null) {
                let payObj = JSON.parse(paymentState);
                payObj.payload.user_id = isLoggedIn;

                if (payObj.isCheckoutPage) {
                    localStorage.removeItem('paymentState');    
                } else {
                    this.verifyEnrollmentStatus(payObj);
                }
            }
        },
        emitEvents() {
            Event.$on('defaultFilters', () => {
                this.defaultFilters();
            });
        },
        fetchModule() {
            this.fetchCourses();
        },
        manageState() {
            this.payload.category = [Number(categoryID)];

            const state = YUNOCommon.getQueryParameter("state");

            if (state) {
                const stateData = JSON.parse(decodeURI(state));
                this.payload = stateData;
                this.fetchModule();
            } else {
                this.fetchModule();
            }
        },
        manageQueryParams() {
            if (history.pushState) {
                let newurl = window.location.protocol + "//" + window.location.host + window.location.pathname + '?state='+ encodeURI(JSON.stringify(this.payload)) +'';
                window.history.pushState({path:newurl},'',newurl);
            }
        },
        defaultFilters() {
            let payload = {
                limit: 20,
                offset: 0,
                personalization: "all",
                category: [],
                category_level_1: [],
                category_level_2: [],
                class_days_time: [
                    {
                        selected: [],
                        slug: "class_days"
                    },
                        {
                        selected: [],
                        slug: "class_time"
                    }
                ],
                instructor_id: 0,
                price_per_hour: 10000,
                total_duration: 24
            }

            payload.category = this.payload.category;
            
            this.payload = payload;
            this.onInitCourses(1);
            this.manageQueryParams();
        },
        clearFilter(e, data) {
            switch (data.filter) {
                // case "category_level_1":
                //     const currentCat = YUNOCommon.findObjectByKey(data.items, "id", e);
                //     data.selected = "";
                //     YUNOCommon.removeValInArr(this.payload.category_level_1, currentCat.parent_id);
                //     YUNOCommon.removeValInArr(this.payload.category_level_2, e);
                //     break;
                case "total_duration":
                case "price_per_hour":
                        data.selected = data.max;
                        this.payload[data.filter] = data.max;
                        break;
                case "personalization":
                    data.selected = "all";
                    this.payload[data.filter] = "all";
                    break;
                case "class_days_time":
                    data.selected = [];
                    this.payload[data.filter] = [
                        {
                            selected: [],
                            slug: "class_days"
                        },
                            {
                            selected: [],
                            slug: "class_time"
                        }
                    ];
                    break;
            };
            this.onInitCourses(1);
            this.manageQueryParams();
        },
        onManageFilters() {
            this.isFilterMobile = !this.isFilterMobile;
            this.$emit('manageFilters', this.isFilterMobile);
        },
        // Processes selected days and times and updates the payload
        daysTimeToggle(data) {
            const selected = data.selected;
            const payloadClassDays = this.payload.class_days_time[0];
            const payloadClassTimes = this.payload.class_days_time[1];

            payloadClassDays.selected = selected.filter(item => item.parentSlug === "class_days").map(item => item.label);
            payloadClassTimes.selected = selected.filter(item => item.parentSlug === "class_time").map(item => item.label);

            this.onInitCourses(1);
            this.manageQueryParams();
        },
        onDropdownSliderChange(data) {
            // if (data.filter === "price_per_hour") {
            //     this.payload[data.filter] = data.selected;
            // }

            this.payload[data.filter] = data.selected;

            this.onInitCourses(1)
            this.manageQueryParams();
        },
        // Handles dropdown changes for various filters and updates the payload
        onDropdownChange(e, data) {
            if (data.filter === "category") {
                this.payload[data.filter] = [];
                this.payload.category_level_1 = [];
                this.payload.category_level_2 = [];
                this.payload[data.filter].push(data.selected);
            } else if (data.filter === "category_level_1") {
                
            } else {
                this.payload[data.filter] = e.slug;
            }
            this.onInitCourses(1);
            this.manageQueryParams();
            Event.$emit('updateHeaderSearch');
        },
        scrollObserver() {
            setTimeout(() => {
                this.stickyObserver("filters", "headerTop");
                this.stickyObserver("yunoMain", "headerTop");
            }, 300);
        },
        stickyObserver(stickyEle, topEle) {
            const observer = new IntersectionObserver(function(entries) {
                if(entries[0].intersectionRatio === 0)
                   document.querySelector("#" + stickyEle).classList.add("isStickyEle");
                else if(entries[0].intersectionRatio === 1)
                    document.querySelector("#" + stickyEle).classList.remove("isStickyEle");
            }, { threshold: [0,1] })

            observer.observe(document.querySelector("#" + topEle));
        },
        initScroll() {
            if (window !== undefined) {
                this.onScroll(window, true);
            } else {
                setTimeout(() => {
                    this.onScroll(window, true);    
                }, 200);
            }
        },
        onScroll(element, isWindow) {
            const instance = this;
            let lastScrollTop = "";
            
            element.addEventListener('scroll', function(event) {
                let getScrollTop = "";

                if (isWindow) {
                    getScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                } else {
                    getScrollTop = event.target.scrollTop;
                }

                if (Math.sign(getScrollTop) === -1) {
                    instance.isScroll = false;
                    instance.hideHeader = false;
                    return false;
                };

                if (getScrollTop >= lastScrollTop) {
                    if (!instance.isScroll) {
                        instance.isScroll = true;
                        instance.hideHeader = true;
                    }
                } else {
                    if (!instance.isTabClicked && instance.isScroll) {
                        instance.isScroll = false;
                        instance.hideHeader = false;
                    }
                }

                lastScrollTop = getScrollTop <= 0 ? 0 : getScrollTop;
            });
        },
        onInitCourses(page) {
            this.isFilterMobile = false;
            this.$emit('manageFilters', this.isFilterMobile);
            // Scroll to specific element
            YUNOCommon.scrollToElement(".mainBody", 500, 0);

            setTimeout(() => {
                // Reset the filterResult object properties
                this.course.data = [];
                this.course.success = false;
                this.course.error = null;

                // Calculate the offset based on current page number and limit
                this.course.offset = this.course.limit * (page - 1);
                this.payload.offset = this.course.offset;
                
                // Fetch results
                this.fetchCourses();
            }, 400);
        },
        gotCourses(options) {
            // Stop the loading state
            this.course.loading = false;
        
            // Destructure the response object
            const { code, data } = options.response?.data || {};

            function updateFilterItems(filterObj) {
                // Add is_checked = false to each item in filterObj.items
                filterObj.items = filterObj.items.map(item => ({
                    ...item,
                    is_checked: false
                }));
            
                if (filterObj.filter === "category_level_1") {
                    const selectedIds = new Set(filterObj.selected);
                    filterObj.items = filterObj.items.map(item => ({
                        ...item,
                        is_checked: selectedIds.has(item.id)
                    }));
            
                    const selectedItems = filterObj.items.filter(item => item.is_checked);
            
                    return {
                        ...filterObj,
                        selected: selectedItems
                    };
                }
                return filterObj;
            }
            
            // Check if the response status code is 200 (OK)
            if (code === 200) {
                // Set the course count
                this.course.count = options.response.data.count;

                const filters = data.filters;
                const updatedData = filters.map(updateFilterItems);
                const courses = data.courses;
                data.filters = updatedData

                for (let i = 0; i < courses.length; i++) {
                    const course = courses[i];
                    course.isSidebar = false;
                }

            } else {
                const filters = data.filters;
                const updatedData = filters.map(updateFilterItems);
                data.filters = updatedData
            }
        
            // Set the course data
            this.course.data = data;
        },
        fetchCourses() {
            // API call options
            this.course.loading = true;
            const options = { 
                apiURL: YUNOCommon.config.availableCoursesV2("listing"),
                module: "gotData",
                store: "course",
                payload: this.payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                addToModule: false,
                callback: true,
                callbackFunc: (options) => this.gotCourses(options)
            };

             // Dispatch the API call
            this.$store.dispatch('postData', options); 
        },
    }
});