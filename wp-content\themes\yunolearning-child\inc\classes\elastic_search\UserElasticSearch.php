<?php

/**
 * This controller is using for signedup es doc 
 */
class UserElasticSearch
{
  /**
   * common-es-get cases
   */
  public static function get_signedup($case, $data)
  {
    switch ($case) {
      case "get_details_from_org_user":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.details_from_org"
          ],
          "query"   => [
            "nested" => [
              "path"  => "data.details",
              "query" => [
                "bool" => [
                  "must" => [
                    [
                      "match" => [
                        "data.details.user_id" => $data['user_id']
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?filter_path=hits.hits._source";
        break;
      case "get_details_from_org_user_object":
        //return new WP_REST_Response($data,200);
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name",
            "data.details.details_from_org.org_id",
            "data.details.details_from_org.user_id",
            "data.details.details_from_org.phone",
            "data.details.details_from_org.name",
            "data.details.details_from_org.created_at",
            "data.details.details_from_org.cohort",
            "data.details.details_from_org.programs",
            "data.details.details_from_org.business_unit",
            "data.details.details_from_org.email",
            "data.details.details_from_org.parents",
          ],
          "query" => [
            "bool" => [
              "must" => [
                [
                  "nested" => [
                    "path" => "data.details.details_from_org",
                    "query" => [
                      "bool" => [
                        "must" => [
                          [
                            "match_phrase" => [
                              "data.details.details_from_org.org_id" => $data['org_id']
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ],
                [
                  "nested" => [
                    "path" => "data.details",
                    "query" => [
                      "bool" => [
                        "must" => [
                          [
                            "match_phrase" => [
                              "data.details.user_id" => $data['user_id']
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];

        // echo json_encode($curlPost);
        $method = "GET";
        //$params = ELASTIC_SEARCH_END_URL . "/signedup/_search?filter_path=hits.hits._source";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "search_all_users_info_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name"
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "search_phone_users_info_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name"
          ],
          "query" => [
            "nested" => [
              "path" => "data.details",
              "query" => [
                "bool" => [
                  "must" => [
                    [
                      "match_phrase" => [
                        "data.details.user.phone" => "*" . $data['phone'] . "*"
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "search_name_users_info_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name"
          ],
          "query" => [
            "nested" => [
              "path" => "data.details",
              "query" => [
                "bool" => [
                  "should" => [
                    [
                      "match_phrase" => [
                        "data.details.user.name" => "*" . $data['name'] . "*"
                      ]
                    ],
                    [
                      "match_phrase" => [
                        "data.details.user.email" => "*" . $data['name'] . "*"
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "search_string_users_info_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name"
          ],
          "query" => [
            "nested" => [
              "path" => "data.details",
              "query" => [
                "bool" => [
                  "should" => [
                    [
                      "match_phrase" => [
                        "data.details.user.name" => "*" . $data['first_name'] . "*"
                      ]
                    ],
                    [
                      "match_phrase" => [
                        "data.details.user.name" => "*" . $data['last_name'] . "*"
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "search_params_users_info_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.email",
            "data.details.user.name"
          ],
          "query" => [
            "nested" => [
              "path" => "data.details",
              "query" => [
                "bool" => [
                  "must" => [
                    [
                      "match" => [
                        "data.details.user_id" => $data['user_id']
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "get_name_image_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.user.image",
            "data.details.user.name"
          ],
          "query"   => [
            "nested" => [
              "path"  => "data.details",
              "query" => [
                "constant_score" => [
                  "filter" => [
                    "bool" => [
                      "must" => [
                        "terms" => [
                          "data.details.user_id" => $data['user_ids']
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "get_id_role_name_image_phone_email_from_es":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.user_id",
            "data.details.role",
            "data.details.user.image",
            "data.details.user.name",
            "data.details.user.phone",
            "data.details.user.email",
          ],
          "query"   => [
            "nested" => [
              "path"  => "data.details",
              "query" => [
                "constant_score" => [
                  "filter" => [
                    "bool" => [
                      "must" => [
                        "match" => [
                          "data.details.user_id" => $data['user_id']
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
          // "query" => [
          //   "nested" => [
          //     "path" => "data.details",
          //     "query" => [
          //       "bool" => [
          //         "must" => [
          //           [
          //             "match" => [
          //               "data.details.user_id" => $data['user_id']
          //             ]
          //           ]
          //         ]
          //       ]
          //     ]
          //   ]
          // ]
        ];

        $index_name = $data['index'] ?? 'signedup';
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "get_category_details_from_es":
        $curlPost = [
          "_source" => $data['category_keys'],
          // "query"   => [
          //   "nested" => [
          //     "path"  => "data.details",
          //     "query" => [
          //       "constant_score" => [
          //         "filter" => [
          //           "bool" => [
          //             "must" => [
          //               "terms" => [
          //                 "data.details.user_id" => $data['user_id']
          //               ]
          //             ]
          //           ]
          //         ]
          //       ]
          //     ]
          //   ]
          // ]

          "query" => [
            "nested" => [
              "path" => "data.details",
              "query" => [
                "bool" => [
                  "must" => [
                    [
                      "match" => [
                        "data.details.user_id" => $data['user_id']
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        error_log("********** Existing CurlPOST fetched from ES" . date("Y-m-d H:i:s") . " === " . json_encode($curlPost) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");
        $index_name = $data['index'] ?? 'signedup';
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "details_from_org":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.details_from_org.user_id",
            "data.details.details_from_org.phone",
            "data.details.details_from_org.name",
            "data.details.details_from_org.email"
          ],
          "query"   => [
            "nested" => [
              "path"  => "data.details",
              "query" => [
                "constant_score" => [
                  "filter" => [
                    "bool" => [
                      "must" => [
                        "terms" => [
                          "data.details.user_id" => [$data]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "details_from_org_csv":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.details_from_org.user_id",
            "data.details.user.email",
            "data.details.user_id",
            "data.details.basic_details.registration_date",
            "data.details.details_from_org.phone",
            "data.details.details_from_org.name",
            "data.details.details_from_org.email",
            "data.details.details_from_org.org_id"
          ],
          "query"   => [
            "nested" => [
              "path"  => "data.details",
              "query" => [
                "constant_score" => [
                  "filter" => [
                    "bool" => [
                      "must" => [
                        "terms" => [
                          "data.details.user_id" => $data['user_ids']
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "details_from_org_csv_on_demand":
        $curlPost = [
          "_source" => [
            "inner_hits",
            "type",
            "data.details.details_from_org.user_id",
            "data.details.user.email",
            "data.details.user_id",
            "data.details.basic_details.registration_date",
            "data.details.details_from_org.phone",
            "data.details.details_from_org.name",
            "data.details.details_from_org.email",
            "data.details.details_from_org.org_id"
          ],
          "query" => [
            "bool" => [
              "must" => [
                [
                  "nested" => [
                    "path" => "data.details.details_from_org",
                    "query" => [
                      "bool" => [
                        "must" => [
                          [
                            "exists" => [
                              "field" => "data.details.details_from_org.email"
                            ]
                          ],
                          [
                            "terms" => [
                              "data.details.details_from_org.email.keyword" => $data['emails']
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ];
        $method = "GET";
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "get_es_users_on_demand":
        $date = $data['date'];
        if ($date == "all") {
          $curlPost = [
            "_source" => [
              "inner_hits",
              "type",
              "data.details.user_id",
              "data.details.basic_details.registration_date"
            ]
          ];
          $method = "GET";
        } else {
          $curlPost = [
            "_source" => [
              "inner_hits",
              "type",
              "data.details.user_id",
              "data.details.basic_details.registration_date"
            ],
            "query"   => [
              "nested" => [
                "path"  => "data.details",
                "query" => [
                  "bool" => [
                    "must" => [
                      [
                        "match_phrase" => [
                          "data.details.basic_details.registration_date" => "$date*"
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ];
          $method = "POST";
        }
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=" . ELASTIC_RECORDS_COUNT;
        break;
      case "get_es_users":
        $categories = $data['categories'];
        if (count($categories) == 0) {
          $curlPost = [
            "fields" => [
              "data.details.user_id"
            ],
            "query"   => [
              "nested" => [
                "path"  => "data.details",
                "query" => [
                  "match" => [
                    "data.details.basic_details.category_url_for_signup" => "*" . $categories[0] . "*"
                  ]
                ]
              ]
            ],
            "_source" => false
          ];
          $method = "GET";
        } else {
          $should = [];
          foreach ($categories as $category) {
            $should[] = [
              "match_phrase" => [
                "data.details.basic_details.category_url_for_signup" => "*" . $category . "*"
              ]
            ];
          }
          $curlPost = [
            "fields" => [
              "data.details.user_id"
            ],
            "query"   => [
              "nested" => [
                "path"  => "data.details",
                "query" => [
                  "bool" => ["should" => $should]
                ]
              ]
            ],
            "_source" => false
          ];
          $method = "POST";
        }
        $params = ELASTIC_SEARCH_END_URL . "/signedup/_search?size=24000&scroll=1m&filter_path=hits.hits.fields";
        break;
      default:
        echo "Your favorite color is neither red, blue, nor green!";
    }

    // return new WP_REST_Response($curlPost, 200);
    $curl = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response = curl_exec($curl);
    $err      = curl_error($curl);
    curl_close($curl);
    if ($case == "get_name_image_from_es") {
      $results = json_decode($response);
      $details = [];
      if (!empty($results)) {
        foreach ($results->hits->hits as $result) {
          $user_id           = $result->_source->data->details->user_id;
          $image             = $result->_source->data->details->user->image;
          $name              = $result->_source->data->details->user->name;
          $details[$user_id] = ["id" => $user_id, "name" => $name, "image" => $image];
        }
      }
      return $details;
    } else if ($case == "get_details_from_org_user") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "search_all_users_info_from_es") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "get_details_from_org_user_object") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "search_phone_users_info_from_es") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "search_name_users_info_from_es") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "search_string_users_info_from_es") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "details_from_org") {
      $results = json_decode($response);
      $details = [];
      foreach ($results->hits->hits as $result) {
        foreach ($result->_source->data->details->details_from_org as $value) {
          $details = [
            "user_id" => $value->user_id,
            "phone"   => $value->phone,
            "name"    => $value->name,
            "email"   => $value->email
          ];
          return $details;
        }
      }
      return "204";
    } else if ($case == "details_from_org_csv_on_demand") {
      $results = json_decode($response);
      $details = [];
      foreach ($results->hits->hits as $result) {
        $orgs = [];
        foreach ($result->_source->data->details->details_from_org as $value) {
          if ($value->org_id == $data['org_id']) {
            $orgs = [
              "user_id" => $value->user_id,
              "phone"   => $value->phone,
              "name"    => $value->name,
              "email"   => $value->email,
              "org_id"  => $value->org_id
            ];
          }
        }
        $user_id           = $result->_source->data->details->user_id;
        $email             = $result->_source->data->details->user->email;
        $name              = $result->_source->data->details->user->name;
        $registration_date = $result->_source->data->details->basic_details->registration_date;
        $details[$user_id] = ["id" => $user_id, "name" => $name, "email" => $email, "registration_date" => $registration_date, "details_from_org" => $orgs];
      }
      return $details;
    } else if ($case == "details_from_org_csv") {
      $results = json_decode($response);
      $details = [];
      foreach ($results->hits->hits as $result) {
        $orgs = [];
        foreach ($result->_source->data->details->details_from_org as $value) {
          if ($value->org_id == $data['org_id']) {
            $orgs = [
              "user_id" => $value->user_id,
              "phone"   => $value->phone,
              "name"    => $value->name,
              "email"   => $value->email,
              "org_id"  => $value->org_id
            ];
          }
        }
        $user_id           = $result->_source->data->details->user_id;
        $email             = $result->_source->data->details->user->email;
        $name              = $result->_source->data->details->user->name;
        $registration_date = $result->_source->data->details->basic_details->registration_date;
        $details[$user_id] = ["id" => $user_id, "name" => $name, "email" => $email, "registration_date" => $registration_date, "details_from_org" => $orgs];
      }
      return $details;
    } else if ($case == "get_es_users_on_demand") {
      $results = json_decode($response);
      return $results;
    } else if ($case == "get_es_users") {
      $res = json_decode($response, true);
      //echo "<pre>";echo $case;print_r($data);echo $params;echo json_encode($curlPost);print_r($res);die("vvvvvvvvvvv");
      return $res;
    } else if ($case == "get_category_details_from_es") {
      $results = json_decode($response);
      // $details = [];
      // if (!empty($results)) {
      //   foreach ($results->hits->hits as $result) {
      //     $user_id           = $result->_source->data->details->user_id;
      //     $image             = $result->_source->data->details->user->image;
      //     $name              = $result->_source->data->details->user->name;
      //     $details[$user_id] = ["id" => $user_id, "name" => $name, "image" => $image];
      //   }
      // }
      return $results;
    } else if ($case == "get_id_role_name_image_phone_email_from_es") {
      $results = json_decode($response);
      // $details = [];
      // if (!empty($results)) {
      //   foreach ($results->hits->hits as $result) {
      //     $user_id           = $result->_source->data->details->user_id;
      //     $image             = $result->_source->data->details->user->image;
      //     $name              = $result->_source->data->details->user->name;
      //     $details[$user_id] = ["id" => $user_id, "name" => $name, "image" => $image];
      //   }
      // }
      return $results;
    }
  }
  /**
   * common-es-update cases
   */
  public static function update_signedup($case, $data)
  {
    date_default_timezone_set('Asia/Calcutta');
    $cdate   = date("Y-m-d H:i:s");
    $user_id = $data['data']["data"]["details"]["user_id"];
    $var     = "";
    switch ($case) {
      case "update_lead_id":
        error_log("updated lead id" . json_encode($data) . "\n", 3, ABSPATH . "update_log_id.log");
        $lead_id = $data['lead_id'];
        $var .= "ctx._source.data.details.basic_details.zoho_lead_id= '" . $lead_id . "'; ";
        break;
      case "login":
        $es_last_login = get_user_meta($user_id, 'es_last_login', true);
        $last_login = explode(" ", $es_last_login);
        if ($last_login[0] == date("Y-m-d")) {
          return true;
        } else {
          update_user_meta($user_id, 'es_last_login', $cdate);
        }
        $var .= "ctx._source.data.details.basic_details.last_login_time= '" . $cdate . "'; ";
        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
        $var .= "ctx._source.data.details.basic_details.zoho_lead_id= '" . $userLeadId . "'; ";
        break;
      case "update_category_es":
        $web = "[";
        foreach ($data['categories'] as $options) {
          $web .= '"' . $options . '",';
        }
        $org = substr_replace($web, "", -1);
        $org .= "]";
        $var .= 'ctx._source.data.details.basic_details.category_url_for_signup = ' . $org . '; ';
        break;
      case "org_detail_upload":
        // $web = "[";

        // foreach ($data['details_from_org'] as $options) {

        //   $parents =  json_decode($options['parents']);
        //   $web1 = [];
        //   if(is_array($parents)){

        //     foreach($parents as $parent){
        //       error_log("ElasticSearch Response parents checking" . date("Y-m-d H:i:s") . " === " . json_encode($parent) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");

        //         $web1[] = [
        //           'email' => $parent->email,
        //           'name' => $parent->name,
        //           'phone' => $parent->phone
        //         ];
        //     }

        //   }
        //   error_log("ElasticSearch Response parents checking web" . date("Y-m-d H:i:s") . " === " . $web1 . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");
        //   error_log("ElasticSearch Response parents checking" . date("Y-m-d H:i:s") . " === " . $parents . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");


        // } $web .= '["org_id":' . (int) $options['org_id'] . ',"email":"' . $options['email'] . '","user_id":' . (int) $options['user_id'] . ',"phone":"' . $options['phone'] . '","name":"' . $options['name'] . '","created_at":"' . $options['datetime'] . '","crm_id":"' . (int) $options['crm_id'] . '","business_unit":"' . (int) $options['business_unit'] . '","parents":"' . $web1 . '"],';
        // $org = substr_replace($web, "]", -1);
        // $var .= "ctx._source.data.details.details_from_org= " . $org . "; ";

        $web = [];
        $var = '';

        foreach ($data['details_from_org'] as $options) {
          $parents = $options['parents'];
          $web1 = [];

          if (is_array($parents)) {
            foreach ($parents as $parent) {
              error_log("ElasticSearch Response parents checking " . date("Y-m-d H:i:s") . " === " . json_encode($parent) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");

              $web1[] = [
                'email' => $parent->email,
                'name' => $parent->name,
                'phone' => $parent->phone
              ];
            }
          }

          error_log("ElasticSearch Response parents checking web " . date("Y-m-d H:i:s") . " === " . json_encode($web1) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");
          error_log("ElasticSearch Response parents checking " . date("Y-m-d H:i:s") . " === " . json_encode($parents) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");

          $web[] = [
            'org_id' => (int) $options['org_id'],
            'email' => $options['email'],
            'user_id' => (int) $options['user_id'],
            'phone' => $options['phone'],
            'name' => $options['name'],
            'created_at' => $options['datetime'],
            'crm_id' =>  $options['crm_id'],
            'business_unit' =>  $options['business_unit'],
            'cohort' =>  $options['cohort'],
            'programs' => $options['programs'],
            'parents' => $web1
          ];
        }

        $var .= "ctx._source.data.details.details_from_org = params.org;";

        // Debugging purposes
        error_log("Final ES Update Script: " . $var . "\n", 3, ABSPATH . "error-logs/ScriptController.log");

        // cURL request to update Elasticsearch


        break;
      case "role_update":
        $var .= "ctx._source.data.details.role= '" . $data['role'] . "'; ";
        break;
      // case "org_detail_upload":
      //   $details_from_org = [];

      //   foreach ($data['details_from_org'] as $options) {
      //       // Decode the parents JSON string into a PHP array
      //       $parents = json_decode($options['parents'], true);

      //       // Construct the array for details_from_org
      //       $details_from_org[] = [
      //           "org_id" => (int) $options['org_id'],
      //           "email" => $options['email'],
      //           "user_id" => (int) $options['user_id'],
      //           "phone" => $options['phone'],
      //           "name" => $options['name'],
      //           "created_at" => $options['datetime'],
      //           "crm_id" => $options['crm_id'],
      //           "business_unit" => $options['business_unit'],
      //           "parents" => $parents
      //       ];
      //   }

      //   // Encode the entire details_from_org array as JSON
      //   $org = json_encode($details_from_org, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
      //   $var = "ctx._source.data.details.details_from_org = " . $org . ";";

      //   // Log the generated script
      //   error_log("Generated Elasticsearch Script: " . $var . "\n", 3, ABSPATH . "error-logs/ScriptController.log");

      //   break;
      case "name_update":
        $var .= "ctx._source.data.details.user.name= '" . $data['name'] . "'; ";
        break;
      case "android":
        $var .= "if (ctx._source.data.details.utm_params == null) { ctx._source.data.details.utm_params = new HashMap(); }";
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_medium"])) {
          $var .= "ctx._source.data.details.utm_params.YL_medium= '" . $data['data']["data"]["details"]["utm_params"]["YL_medium"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_lead_source"])) {
          $var .= "ctx._source.data.details.utm_params.YL_lead_source= '" . $data['data']["data"]["details"]["utm_params"]["YL_lead_source"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_keyword"])) {
          $var .= "ctx._source.data.details.utm_params.YL_keyword= '" . $data['data']["data"]["details"]["utm_params"]["YL_keyword"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_campaign"])) {
          $var .= "ctx._source.data.details.utm_params.YL_campaign= '" . $data['data']["data"]["details"]["utm_params"]["YL_campaign"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_group"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_group= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_group"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_content"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_content= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_content"] . "'; ";
        }
        break;
      case "utm-params":
        $var .= "if (ctx._source.data.details.utm_params == null) { ctx._source.data.details.utm_params = new HashMap(); }";
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_medium"])) {
          $var .= "ctx._source.data.details.utm_params.YL_medium= '" . $data['data']["data"]["details"]["utm_params"]["YL_medium"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_lead_source"])) {
          $var .= "ctx._source.data.details.utm_params.YL_lead_source= '" . $data['data']["data"]["details"]["utm_params"]["YL_lead_source"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_keyword"])) {
          $var .= "ctx._source.data.details.utm_params.YL_keyword= '" . $data['data']["data"]["details"]["utm_params"]["YL_keyword"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_campaign"])) {
          $var .= "ctx._source.data.details.utm_params.YL_campaign= '" . $data['data']["data"]["details"]["utm_params"]["YL_campaign"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_group"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_group= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_group"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_content"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_content= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_content"] . "'; ";
        }
        break;
      case "basic_details":
        // error_log("every post es call" . date("Y-m-d H:i:s") . "=== " . $url . "Event type " . json_encode($curlPost) . "=== Response" . json_encode($data) . "\n", 3, ABSPATH . "error-logs/every-remote2.log");
        $var .= "ctx._source.data.details.role= '" . $data['data']["data"]["details"]["role"] . "'; ";
        $var .= "ctx._source.data.details.user.name= '" . $data['data']["data"]["details"]["user"]["name"] . "'; ";
        $var .= "ctx._source.data.details.user.phone= '" . $data['data']["data"]["details"]["user"]["phone"] . "'; ";
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_medium"])) {
          $var .= "ctx._source.data.details.utm_params.YL_medium= '" . $data['data']["data"]["details"]["utm_params"]["YL_medium"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_lead_source"])) {
          $var .= "ctx._source.data.details.utm_params.YL_lead_source= '" . $data['data']["data"]["details"]["utm_params"]["YL_lead_source"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_keyword"])) {
          $var .= "ctx._source.data.details.utm_params.YL_keyword= '" . $data['data']["data"]["details"]["utm_params"]["YL_keyword"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_campaign"])) {
          $var .= "ctx._source.data.details.utm_params.YL_campaign= '" . $data['data']["data"]["details"]["utm_params"]["YL_campaign"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_group"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_group= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_group"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["utm_params"]["YL_ad_content"])) {
          $var .= "ctx._source.data.details.utm_params.YL_ad_content= '" . $data['data']["data"]["details"]["utm_params"]["YL_ad_content"] . "'; ";
        }

        if (!empty($data['data']["data"]["details"]["location"]["country"])) {
          $var .= "ctx._source.data.details.location.country= '" . $data['data']["data"]["details"]["location"]["country"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["pin_code"])) {
          $var .= "ctx._source.data.details.location.pin_code= '" . $data['data']["data"]["details"]["location"]["pin_code"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["flat_house_number"])) {
          $var .= "ctx._source.data.details.location.flat_house_number= '" . $data['data']["data"]["details"]["location"]["flat_house_number"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["street"])) {
          $var .= "ctx._source.data.details.location.street= '" . $data['data']["data"]["details"]["location"]["street"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["landmark"])) {
          $var .= "ctx._source.data.details.location.landmark= '" . $data['data']["data"]["details"]["location"]["landmark"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["city"])) {
          $var .= "ctx._source.data.details.location.city= '" . $data['data']["data"]["details"]["location"]["city"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["state"])) {
          $var .= "ctx._source.data.details.location.state= '" . $data['data']["data"]["details"]["location"]["state"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["location"]["address_type"])) {
          $var .= "ctx._source.data.details.location.address_type= '" . $data['data']["data"]["details"]["location"]["address_type"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["time_of_study"])) {
          $var .= "ctx._source.data.details.time_of_study= '" . $data['data']["data"]["details"]["time_of_study"] . "'; ";
        }

        if (!empty($data['data']["data"]["details"]["basic_details"]["locale"])) {
          $var .= "ctx._source.data.details.basic_details.locale= '" . $data['data']["data"]["details"]["basic_details"]["locale"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["referred_by"])) {
          $var .= "ctx._source.data.details.basic_details.referred_by= '" . $data['data']["data"]["details"]["basic_details"]["referred_by"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["native_language"])) {
          $var .= "ctx._source.data.details.basic_details.native_language= '" . $data['data']["data"]["details"]["basic_details"]["native_language"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["category_url_for_signup"])) {
          $var .= 'ctx._source.data.details.basic_details.category_url_for_signup= "' . $data["data"]["data"]["details"]["basic_details"]["category_url_for_signup"] . '";';
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["yuno_user_whatsapp_check"])) {
          if ($data['data']["data"]["details"]["basic_details"]["yuno_user_whatsapp_check"]) {
            $status = "true";
          } else {
            $status = "false";
          }

          $var .= "ctx._source.data.details.basic_details.yuno_user_whatsapp_check=$status;";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["privacy_policy_terms_of_service"])) {
          if ($data['data']["data"]["details"]["basic_details"]["privacy_policy_terms_of_service"]) {
            $status = "true";
          } else {
            $status = "false";
          }
          $var .= "ctx._source.data.details.basic_details.privacy_policy_terms_of_service=$status;";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["what_best_describes_you"])) {
          $var .= "ctx._source.data.details.basic_details.what_best_describes_you= '" . $data['data']["data"]["details"]["basic_details"]["what_best_describes_you"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["classes_duration"])) {
          $var .= "ctx._source.data.details.basic_details.classes_duration= '" . $data['data']["data"]["details"]["basic_details"]["classes_duration"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["when_planning_ielts"])) {
          $var .= "ctx._source.data.details.basic_details.when_planning_ielts= '" . $data['data']["data"]["details"]["basic_details"]["when_planning_ielts"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["time_of_study"])) {
          $var .= "ctx._source.data.details.basic_details.time_of_study= '" . $data['data']["data"]["details"]["basic_details"]["time_of_study"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["type_of_ielts"])) {
          $var .= "ctx._source.data.details.basic_details.type_of_ielts= '" . $data['data']["data"]["details"]["basic_details"]["type_of_ielts"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["ielts_target_band"])) {
          $var .= "ctx._source.data.details.basic_details.ielts_target_band= '" . $data['data']["data"]["details"]["basic_details"]["ielts_target_band"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["zoho_lead_id"])) {
          $var .= "ctx._source.data.details.basic_details.zoho_lead_id= '" . $data['data']["data"]["details"]["basic_details"]["zoho_lead_id"] . "'; ";
        }
        if (!empty($data['data']["data"]["details"]["basic_details"]["zoho_contact_id"])) {
          $var .= "ctx._source.data.details.basic_details.zoho_contact_id= '" . $data['data']["data"]["details"]["basic_details"]["zoho_contact_id"] . "'; ";
        }
        break;
      case "region_details":
        /* update user info setting for region start */
        $obj = [
          "country" => [
            "id" => (int) $data['data']["data"]["details"]["region"]["country"]["id"],
            "name" => $data['data']["data"]["details"]["region"]["country"]["name"],
            "code" => $data['data']["data"]["details"]["region"]["country"]["code"]
          ],
          "timezone" => $data['data']["data"]["details"]["region"]["timezone"],
          "currency" => [
            "code" => $data['data']["data"]["details"]["region"]["currency"]["code"],
            "name" => $data['data']["data"]["details"]["region"]["currency"]["name"],
            "symbol" => $data['data']["data"]["details"]["region"]["currency"]["symbol"],
            "symbol_html" => $data['data']["data"]["details"]["region"]["currency"]["symbol_html"]
          ],
          "language" => [
            "name_in_english" => $data['data']["data"]["details"]["region"]["language"]["name_in_english"],
            "native_lang_name" => $data['data']["data"]["details"]["region"]["language"]["native_lang_name"],
            "code" => $data['data']["data"]["details"]["region"]["language"]["code"]
          ]
        ];

        $var .= "ctx._source.data.details.region = params.region_obj;";
        /*
		if (!empty($data['data']["data"]["details"]["region"]["country_code"])) {	
          $var .= "ctx._source.data.details.region.country_code= '" . $data['data']["data"]["details"]["region"]["country_code"] . "'; ";
        }		
		if (!empty($data['data']["data"]["details"]["region"]["country"])) {
          $var .= "ctx._source.data.details.region.country= '" . $data['data']["data"]["details"]["region"]["country"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["timezone"])) {
          $var .= "ctx._source.data.details.region.timezone= '" . $data['data']["data"]["details"]["region"]["timezone"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["timezone_utc"])) {
          $var .= "ctx._source.data.details.region.timezone_utc= '" . $data['data']["data"]["details"]["region"]["timezone_utc"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["currency"])) {
          $var .= "ctx._source.data.details.region.currency= '" . $data['data']["data"]["details"]["region"]["currency"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["currency_symbol"])) {
          $var .= "ctx._source.data.details.region.currency_symbol= '" . $data['data']["data"]["details"]["region"]["currency_symbol"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["language"])) {
          $var .= "ctx._source.data.details.region.language= '" . $data['data']["data"]["details"]["region"]["language"] . "'; ";
        }
		if (!empty($data['data']["data"]["details"]["region"]["language_code"])) {
          $var .= "ctx._source.data.details.region.language_code= '" . $data['data']["data"]["details"]["region"]["language_code"] . "'; ";
        }*/
        /* update user info setting for region end */
        break;
      default:
        $var .= "ctx._source.data.details.user.name= '" . $data['name'] . "'; ";
    }
    if ($case == "org_detail_upload") {

      $curlPost = [
        "script" => [
          "source" => $var,
          "lang"   => "painless",
          "params" => [
            "org" => $web
          ]
        ]
      ];
    } else if ($case == "region_details") {
      $curlPost = [
        "script" => [
          "source" => $var,
          "lang"   => "painless",
          "params" => [
            "region_obj" => $obj
          ]
        ]
      ];
    } else {

      $curlPost = [
        "script" => [
          "inline" => $var,
          "lang"   => "painless"
        ]
      ];
    }





    //// Dec-21-- start
    $index_name = "signedup"; // index name
    $userid_prefix = "signedup-"; /// doc prefix
    if ($case == "es_update_category_details") {
      $index_name = $data['index'];
      $user_id = $data['user_id'];
      // $userid_prefix = 'signedup_temp39-';
      $userid_prefix = 'signedup-';
      $curlPost = $data['curlPost'];
    }


    //// Dec-21-- end

    // $curl     = curl_init();
    // curl_setopt_array($curl, array(
    //   CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
    //   CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/signedup/_update/signedup-" . $user_id,
    //   CURLOPT_RETURNTRANSFER => true,
    //   CURLOPT_ENCODING       => "",
    //   CURLOPT_MAXREDIRS      => 10,
    //   CURLOPT_TIMEOUT        => 30,
    //   CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
    //   CURLOPT_CUSTOMREQUEST  => "POST",
    //   CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
    //   CURLOPT_HTTPHEADER     => array(
    //     "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
    //     "cache-control: no-cache",
    //     "content-type: application/json"
    //   ),
    // )
    // );

    // $response = curl_exec($curl);
    // $err      = curl_error($curl);

    // curl_close($curl);//echo "<pre>";echo $var;print_r(json_decode($response));die("aaaaa");
    // return $res = json_decode($response);
    // if ($res->_shards->failed != 0) {
    // }
    // else {
    //   //error_log($event_type." es curl post success: ".json_encode($res));
    // }



    // $headers = array('Content-Type' => 'application/json',
    // 'authorization' => 'Basic ' .ELASTIC_SEARCH_BASIC_AUTHORIZATION
    // ); 
    // $args = array(
    //     'body' => json_encode($curlPost, JSON_UNESCAPED_SLASHES)
    // ); 
    // $params = []; 
    // $es_url = ELASTIC_SEARCH_END_URL. "/".$index_name."/_update/".$userid_prefix.$user_id;  
    // $response = Utility::custom_wp_remote_post($es_url, $args, $headers, $params);


    $curl     = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_update/" . $userid_prefix . $user_id,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => "POST",
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );

    $response = curl_exec($curl);
    $err      = curl_error($curl);
    error_log("Data sent to store in ElasticSearch" . date("Y-m-d H:i:s") . " === " . json_encode($curlPost, JSON_UNESCAPED_SLASHES) . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");
    error_log("ElasticSearch Response" . date("Y-m-d H:i:s") . " === " . $response . "\n\n", 3, ABSPATH . "error-logs/ScriptController.log");
    curl_close($curl); //echo "<pre>";echo $var;print_r(json_decode($response));die("aaaaa");
    return $res = json_decode($response);
    if ($res->_shards->failed != 0) {
    } else {
      //error_log($event_type." es curl post success: ".json_encode($res));
    }
  }
  /**
   * common-es-create cases
   */
  public static function create_signedup($case, $data)
  {
    $user_id = $data['data']["data"]["details"]["user_id"];
    $login   = get_user_meta($user_id, 'login', true);
    if ($login == 1) {
      UserElasticSearch::update_signedup("basic_details", $data['data']);
    } else {
      post_elastic_event($data['data']);
      update_user_meta($user_id, 'login', 1);
    }
  }
  /**
   * common-es-delete cases
   */
  public static function delete_signedup($case, $data) {}


  public static function mustQueries($data)
  {

    $mustQueries = [];

    if (isset($data['root_filters']) && is_array($data['root_filters'])) {
      foreach ($data['root_filters'] as $key => $value) {
        if ($value != "" && $value != "all") {
          $mustQueries[] = [
            "match" => [
              "data.details.$key" => $value
            ]
          ];
        } elseif ($key == "time_of_study" && $value == "all") {
          $mustQueries[] = [
            "terms" => [
              "data.details.$key" => ["morning", "afternoon", "evening", "night"]
            ]
          ];
        }
      }
    }

    // Add dynamic match queries for basic details filters
    if (isset($data['basic_details_filters']) && is_array($data['basic_details_filters'])) {
      foreach ($data['basic_details_filters'] as $key => $value) {
        if ($value != "") {
          if ($value === "false") {
            // Match records where the field is false or doesn't exist
            $mustQueries[] = [
              "bool" => [
                "should" => [
                  ["match" => ["data.details.basic_details.$key" => false]],
                  ["bool" => ["must_not" => ["exists" => ["field" => "data.details.basic_details.$key"]]]]
                ]
              ]
            ];
          } elseif ($value === "true") {
            // Match records where the field is true
            $mustQueries[] = [
              "match" => [
                "data.details.basic_details.$key" => true
              ]
            ];
          } else {
            $mustQueries[] = [
              "match" => [
                "data.details.basic_details.$key" => true
              ]
            ];
          }
        }
      }
    }

    if (isset($data['case']) && $data['case'] == "get_demo_requests") {
      //   foreach ($data['cta_filters'] as $key => $value) {
      //     if ($value != "") {
      $mustQueries[] = [
        "nested" => [
          "path" => "data.details.cta",
          "query" => [
            "bool" => [
              "must" => [
                [
                  "match" => [
                    "data.details.cta.cta" => "book_a_demo"
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      //     }
      //   }
    }

    if (isset($data['utm_filters']) && is_array($data['utm_filters'])) {
      foreach ($data['utm_filters'] as $key => $value) {
        if ($value != "") {
          $mustQueries[] = [
            "match" => [
              "data.details.utm_params.$key" => $value
            ]
          ];
        }
      }
    }
    if (isset($data['cat_filters']) && is_array($data['cat_filters'])) {
      foreach ($data['cat_filters'] as $key => $value) {
        if ($value != "") {
          $mustQueries[] = [
            "nested" => [
              "path" => "data.details.category_details_{$value}",
              "query" => [
                "bool" => [
                  "must" => [
                    [
                      "match" => [
                        "data.details.category_details_{$value}.{$key}" => $value
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ];
        }
      }
    }

    if (isset($data['cat_filters_academy']) && is_array($data['cat_filters_academy'])) {
      foreach ($data['cat_filters_academy'] as $key => $value) {
        // if (is_string($value)) {
        $value_array = $value;
        if (is_array($value_array) && count($value_array) > 0) {
          $category_queries = [];
          $categories = ["ielts", "pte", "english-speaking", "toefl", "duolingo", "french"];
          foreach ($categories as $category) {

            $category_queries[] = [
              "nested" => [
                "path" => "data.details.category_details_{$category}.demo_requests",
                "query" => [
                  "terms" => [
                    "data.details.category_details_{$category}.demo_requests.academy_id" => $value_array
                  ]
                ]
              ]
            ];
          }
          $mustQueries[] = [
            "bool" => [
              "should" => $category_queries,
              "minimum_should_match" => 1
            ]
          ];
        }
        // }
      }
    }

    if (isset($data['user_filters']) && is_array($data['user_filters'])) {
      foreach ($data['user_filters'] as $key => $value) {
        if ($value != "") {
          $mustQueries[] = [

            "exists" => [
              "field" => "data.details.user.{$key}"
            ]
          ];
        }
      }
    }

    return $mustQueries;
  }

  public static function rangeQueries($data)
  {
    $rangeFilters = [];

    // Add multiple range queries
    if (isset($data['case'])) {
      if ($data['case'] == "get_demo_requests") {
        $to_sort = "cta";
      } elseif ($data['case'] = "org-admin") {
        $to_sort = "details_from_org";
      }
    } else {
      $to_sort = "basic_details";
    }
    if (isset($data['ranges']) && is_array($data['ranges'])) {
      foreach ($data['ranges'] as $field => $range) {
        if ($range != "") {
          $rangeFilters[] = [
            "range" => [
              "data.details.$to_sort.$field" => [
                "gte" => $range,
                // Optionally, you can include "lte" if needed
                // "lte" => $range['lte']
              ]
            ]
          ];
        }
      }
    }

    return $rangeFilters;
  }

  public static function get_sorting_query($column, $order)
  {
    $sortingColumn = $column;
    $userColumns = ["learner", "email"];
    $timeColumns = ["created_time", "last_login", "org_created_time"];

    if (in_array($sortingColumn, $userColumns)) {
      if ($sortingColumn === "learner") {
        $sortingColumn = "name";
      }
      // Sorting query for user columns
      $scriptSource = [
        "sort" => [
          [
            "data.details.user.$sortingColumn.keyword" => [
              "order" => $order,
              "nested" => [
                "path" => "data.details"
              ]
            ]
          ]
        ]
      ];
    } else {
      // Sorting query for time columns
      $timeColumn = $sortingColumn;
      $scriptSource = [];

      if ($sortingColumn === "created_time") {
        $timeColumn = "registration_date";
      } elseif ($sortingColumn === "last_login") {
        $timeColumn = "last_login_time";
      } elseif ($sortingColumn === "org_created_time") {
        $timeColumn = "created_at";
        // Sorting query for nested org_created_time
        $scriptSource = [
          "sort" => [
            [
              "data.details.details_from_org.created_at" => [
                "order" => $order,
                "nested" => [
                  "path" => "data.details.details_from_org"
                ]
              ]
            ]
          ]
        ];
      }

      // If scriptSource is empty, use the default script for time columns
      // if (empty($scriptSource)) {
      $scriptSource = [
        // Default sorting query for time columns
        // You can modify this according to your requirements
        "sort" => [
          [
            "data.details.basic_details.{$timeColumn}" => [
              "order" => $order,
              "nested" => [
                "path" => "data.details"
              ]
            ]
          ]
        ]
      ];
      // }
    }

    return $scriptSource;
  }

  // public static function get_learner_insights($data)
  // {

  //   $codes = error_code_setting();
  //   $mustQueries = UserElasticSearch::mustQueries($data);
  //   // Configured data into must queries that will apply on must clause of script
  //   $rangeQueries = UserElasticSearch::rangeQueries($data);
  //   // Configured data into range queries that will apply on range clause of script

  //   $sorting = UserElasticSearch::get_sorting_query($data['sorting']['column'], $data['sorting']['type']);


  //   $curlPost = [];

  //   // Check if both $mustQueries and $rangeQueries are empty
  //   if (empty($mustQueries) && empty($rangeQueries)) {
  //     // If both are empty, construct the query to retrieve all results
  //     $curlPost = [
  //       "sort" => $sorting["sort"]
  //     ];
  //   } elseif (empty($data['sorting']['column']) && empty($data['sorting']['type'])) {
  //     $curlPost = [
  //       "query" => [
  //         "nested" => [
  //           "path" => "data.details",
  //           "query" => [
  //             "bool" => [
  //               "must" => $mustQueries,
  //               "filter" => [
  //                 "bool" => [
  //                   "must" => $rangeQueries
  //                 ]
  //               ]
  //             ]
  //           ]
  //         ]
  //       ]
  //     ];
  //   } else {
  //     // If not empty, construct the nested query with must and filter conditions
  //     $curlPost = [
  //       "query" => [
  //         "nested" => [
  //           "path" => "data.details",
  //           "query" => [
  //             "bool" => [
  //               "must" => $mustQueries,
  //               "filter" => [
  //                 "bool" => [
  //                   "must" => $rangeQueries
  //                 ]
  //               ]
  //             ]
  //           ]
  //         ]
  //       ],
  //       "sort" => $sorting["sort"]
  //     ];
  //   }

  //   // Convert the PHP array to JSON
  //   $jsonQuery = json_encode($curlPost, JSON_PRETTY_PRINT);
  //   $index_name = $data['index'] ?? 'signedup';
  //   $method = "GET";
  //   $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
  //   $curl = curl_init();
  //   curl_setopt_array(
  //     $curl,
  //     array(
  //       CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
  //       CURLOPT_URL            => $params,
  //       CURLOPT_RETURNTRANSFER => true,
  //       CURLOPT_ENCODING       => "",
  //       CURLOPT_MAXREDIRS      => 10,
  //       CURLOPT_TIMEOUT        => 30,
  //       CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
  //       CURLOPT_CUSTOMREQUEST  => $method,
  //       CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
  //       CURLOPT_HTTPHEADER     => array(
  //         "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ,
  //         "cache-control: no-cache",
  //         "content-type: application/json"
  //       ), //Ramshankar please donot use static es token - from gagan 14 may 2024
  //     )
  //   );
  //   $response = curl_exec($curl);
  //   $results = json_decode($response);
  //   $statusCode = "";
  //   $status = "";
  //   $msg = "";
  //   $count = "";

  //   $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

  //   if (isset($results->hits->total->value)) {
  //     $count = $results->hits->total->value;
  //   } elseif (isset($results->hits->total)) {
  //     $count = $results->hits->total;
  //   } else {
  //     $count = 0; // Or handle this scenario appropriately
  //   }
  //   if ($count > 0) {
  //     $resp = array(
  //       'code' => $codes["POST_INSERT"]["code"],
  //       'message' => 'Results found',
  //       'status' => "Success"
  //     );
  //   } else {
  //     $resp = array(
  //       'code' => $codes["POST_INSERT_FAIL"]["code"],
  //       'message' => 'No results found',
  //       'status' => "Error!"
  //     );
  //   }

  //   $resp['data']['count'] = $count;
  //   if ($data['view'] == "grid-view") {
  //     $grid_view = [];

  //     foreach ($results->hits->hits as $res) {
  //       $root = $res->_source->data->details;
  //       $user = $res->_source->data->details->user;
  //       $basic_details = $res->_source->data->details->basic_details;

  //       $org_ids = []; // Initialize an empty array to store org_id values

  //       // Check if details_from_org is not empty and is an array
  //       if (!empty($root->details_from_org) && is_array($root->details_from_org)) {
  //         // Loop through each item in the details_from_org array
  //         foreach ($root->details_from_org as $detail) {
  //           // Check if org_id exists and is not empty
  //           if (!empty($detail->org_id)) {
  //             // Add the org_id to the org_ids array
  //             $org_ids[] = $detail->org_id;
  //           }
  //         }
  //       }



  //       $row = [];
  //       $row['learner'] = [
  //         'name' => $user->name,
  //         'image' => $user->image,
  //         'user_id' => $root->user_id
  //       ];
  //       $row['email'] = $user->email;
  //       $row['phone'] = !empty($user->phone) ? intval($user->phone) : "N/A";
  //       // $row['role'] = $root->role;
  //       $row['time_of_study'] = !empty($root->time_of_study) ? $root->time_of_study : "N/A";
  //       $row['whatsapp']['value'] = !empty($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
  //       $row['whatsapp']['message'] = ($basic_details->yuno_user_whatsapp_check) ? "Can send messages on WhatsApp" : "Can not send messages on WhatsApp";
  //       $row['term_of_service']['value'] = !empty($basic_details->privacy_policy_terms_of_service) ? $basic_details->privacy_policy_terms_of_service : false;
  //       $row['term_of_service']['message'] = ($basic_details->privacy_policy_terms_of_service) ? "Has accepted Terms of Service" : "Has not accepted Terms of Service";
  //       $row['created_time'] = $basic_details->registration_date;
  //       $row['last_login'] = $basic_details->last_login_time;
  //       $row['zoho_crm_id'] = intval($basic_details->zoho_lead_id);
  //       // $row['native_language'] = $basic_details->native_language;
  //       $row['referred_by'] = !empty($basic_details->referred_by) ? $basic_details->referred_by : "N/A";
  //       // $row['utm'] = $root->utm_params;
  //       $row['country'] = isset($root->location->country) ? $root->location->country : "";
  //       $row['state'] = isset($root->location->state) ? $root->location->state : "";
  //       $row['city'] = isset($root->location->city) ? $root->location->city : "";
  //       $row['org_id'] = !empty($org_ids) ? implode(', ', $org_ids) : "N/A";
  //       $actions = [];
  //       foreach ($data['actions'] as $action) {

  //         $row1 = [];
  //         $row1['label'] = ucwords(str_replace('_', ' ', $action));
  //         $row1['is_active'] = true;
  //         $row1['slug'] = $action;
  //         $row1['url'] = "/learner-profile?learner={$root->user_id}";
  //         $row1['link_target'] = "_blank";
  //         $row1['active_class'] = "material-icons-outlined";
  //         $row1['icon']['type'] = "mdl";
  //         $row1['icon']['class'] = "material-icons-outlined";
  //         $row1['icon']['hover'] = "material-icons";
  //         $row1['icon']['value'] = "timeline";

  //         $actions[] = $row1;
  //       }
  //       $row['actions'] = $actions;

  //       $grid_view['rows'][] = $row;
  //     }



  //     $sortable = ["learner", "email", "created_time", "last_login"];

  //     foreach ($grid_view['rows'][0] as $key => $value) {
  //       $row = [];
  //       $row['field'] = $key;
  //       $row['label'] = ucwords(str_replace('_', ' ', $key));
  //       if (in_array($key, $sortable)) {
  //         $row['sortable'] = true;
  //       } else {
  //         $row['sortable'] = false;
  //       }
  //       $row['is_active'] = true;
  //       $grid_view['columns'][] = $row;
  //     }
  //   } else {
  //     $list_view = [];
  //     $resp['data']['list_view'] = $list_view;
  //   }
  //   $filter_data = [];
  //   foreach ($data['filters'] as $key) {
  //     $row = [];
  //     $row['filter'] = $key;
  //     $row['is_active'] = true;

  //     $row['selected'] = $data[$key];

  //     $row['type'] = "dropdown";

  //     $row['placeholder'] = ucwords(str_replace('_', ' ', $key));

  //     if ($data['filter'][$key]) {
  //       foreach ($data['filter'][$key] as $k) {
  //         $filter_items = [];
  //         $filter_items['slug'] = $k;
  //         $filter_items['label'] = ucwords(str_replace('-', ' ', $k));
  //         $filter_items['filter'] = $key;
  //         $row['items'][] = $filter_items;
  //       }
  //     } else {
  //       $row['items'] = [];
  //     }
  //     $filter_data[] = $row;
  //   }
  //   $resp['data']['grid_view'] = $grid_view;
  //   $resp['data']['filters'] = $filter_data;
  //   // $resp['data']['actions'] = $actions;
  //   return $resp;
  // }

  public static function get_learner_insights($data)
  {
    $codes = error_code_setting();
    $mustQueries = UserElasticSearch::mustQueries($data);
    // Configured data into must queries that will apply on must clause of script
    $rangeQueries = UserElasticSearch::rangeQueries($data);
    // Configured data into range queries that will apply on range clause of script

    $sorting = UserElasticSearch::get_sorting_query($data['sorting']['column'], $data['sorting']['type']);


    $curlPost = [];

    // Check if both $mustQueries and $rangeQueries are empty
    if (empty($mustQueries) && empty($rangeQueries)) {
      // If both are empty, construct the query to retrieve all results
      $curlPost = [
        "sort" => $sorting["sort"]
      ];
    } elseif (empty($data['sorting']['column']) && empty($data['sorting']['type'])) {
      $curlPost = [
        "query" => [
          "nested" => [
            "path" => "data.details",
            "query" => [
              "bool" => [
                "must" => $mustQueries,
                "filter" => [
                  "bool" => [
                    "must" => $rangeQueries
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
    } else {
      // If not empty, construct the nested query with must and filter conditions
      $curlPost = [
        "query" => [
          "nested" => [
            "path" => "data.details",
            "query" => [
              "bool" => [
                "must" => $mustQueries,
                "filter" => [
                  "bool" => [
                    "must" => $rangeQueries
                  ]
                ]
              ]
            ]
          ]
        ],
        "sort" => $sorting["sort"]
      ];
    }

    // Convert the PHP array to JSON
    $jsonQuery = json_encode($curlPost, JSON_PRETTY_PRINT);
    $index_name = $data['index'] ?? 'signedup';
    $method = "GET";
    $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
    $curl = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ,
          "cache-control: no-cache",
          "content-type: application/json"
        ), //Ramshankar please donot use static es token - from gagan 14 may 2024
      )
    );
    $response = curl_exec($curl);
    $results = json_decode($response);

    // Aggreagation fot UTM fields
    $curlPost1 = [
      "size" => 0,
      "aggs" => [
        "unique_YL_campaign" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_campaign_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_campaign.keyword",
                "size" => 9999
              ]
            ]
          ]
        ],
        "unique_YL_medium" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_medium_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_medium.keyword",
                "size" => 9999
              ]
            ]
          ]
        ],
        "unique_YL_keyword" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_keyword_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_keyword.keyword",
                "size" => 9999
              ]
            ]
          ]
        ],
        "unique_YL_lead_source" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_lead_source_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_lead_source.keyword",
                "size" => 9999
              ]
            ]
          ]
        ],
        "unique_YL_ad_content" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_ad_content_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_ad_content.keyword",
                "size" => 9999
              ]
            ]
          ]
        ],
        "unique_YL_ad_group" => [
          "nested" => [
            "path" => "data.details"
          ],
          "aggs" => [
            "unique_ad_group_values" => [
              "terms" => [
                "field" => "data.details.utm_params.YL_ad_group.keyword",
                "size" => 9999
              ]
            ]
          ]
        ]
      ]
    ];
    $params1 = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search";
    $curl1 = curl_init();
    curl_setopt_array(
      $curl1,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params1,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost1, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response1 = curl_exec($curl1);
    $results1 = json_decode($response1);

    // Initialize arrays to store non-empty values
    $utm_campaign = ['all'];
    $utm_medium = ['all'];
    $utm_keyword = ['all'];
    $utm_lead_source = ['all'];
    $utm_ad_content = ['all'];
    $utm_ad_group = ['all'];

    // Loop through the aggregations and extract non-empty values
    foreach ($results1->aggregations as $aggregation) {
      foreach ($aggregation as $key => $value) {
        if (isset($value->buckets)) {
          foreach ($value->buckets as $bucket) {
            // Skip empty values
            if (!empty($bucket->key)) {
              // Store non-empty values in respective arrays
              switch ($key) {
                case 'unique_campaign_values':
                  $utm_campaign[] = $bucket->key;
                  break;
                case 'unique_medium_values':
                  $utm_medium[] = $bucket->key;
                  break;
                case 'unique_keyword_values':
                  $utm_keyword[] = $bucket->key;
                  break;
                case 'unique_lead_source_values':
                  $utm_lead_source[] = $bucket->key;
                  break;
                case 'unique_ad_content_values':
                  $utm_ad_content[] = $bucket->key;
                  break;
                case 'unique_ad_group_values':
                  $utm_ad_group[] = $bucket->key;
                  break;
              }
            }
          }
        }
      }
    }
    $data['filter']['YL_campaign'] = $utm_campaign;
    $data['filter']['YL_medium'] = $utm_medium;
    $data['filter']['YL_keyword'] = $utm_keyword;
    $data['filter']['YL_lead_source'] = $utm_lead_source;
    $data['filter']['YL_ad_content'] = $utm_ad_content;
    $data['filter']['YL_ad_group'] = $utm_ad_group;

    // Aggreagation fot UTM fields

    $statusCode = "";
    $status = "";
    $msg = "";
    $count = "";

    $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (isset($results->hits->total->value)) {
      $count = $results->hits->total->value;
    } elseif (isset($results->hits->total)) {
      $count = $results->hits->total;
    } else {
      $count = 0; // Or handle this scenario appropriately
    }
    if ($count > 0) {
      $resp = array(
        'code' => $codes["POST_INSERT"]["code"],
        'message' => 'Results found',
        'status' => "Success"
      );
    } else {
      $resp = array(
        'code' => $codes["POST_INSERT_FAIL"]["code"],
        'message' => 'No results found',
        'status' => "Error!"
      );
    }

    $resp['data']['count'] = $count;
    if ($data['view'] == "grid-view") {
      $grid_view = [];

      foreach ($results->hits->hits as $res) {
        $root = $res->_source->data->details;
        $user = $res->_source->data->details->user;
        $basic_details = $res->_source->data->details->basic_details;

        $org_ids = []; // Initialize an empty array to store org_id values

        // Check if details_from_org is not empty and is an array
        if (!empty($root->details_from_org) && is_array($root->details_from_org)) {
          // Loop through each item in the details_from_org array
          foreach ($root->details_from_org as $detail) {
            // Check if org_id exists and is not empty
            if (!empty($detail->org_id)) {
              // Add the org_id to the org_ids array
              $org_ids[] = $detail->org_id;
            }
          }
        }



        $row = [];
        $row['learner'] = [
          'name' => $user->name,
          'image' => $user->image,
          'user_id' => $root->user_id
        ];
        $row['email'] = $user->email;
        $row['phone'] = !empty($user->phone) ? intval($user->phone) : "N/A";
        // $row['role'] = $root->role;
        $row['time_of_study'] = !empty($root->time_of_study) ? $root->time_of_study : "N/A";
        $row['whatsapp']['value'] = !empty($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
        $row['whatsapp']['message'] = ($basic_details->yuno_user_whatsapp_check) ? "Can send messages on WhatsApp" : "Can not send messages on WhatsApp";
        $row['term_of_service']['value'] = !empty($basic_details->privacy_policy_terms_of_service) ? $basic_details->privacy_policy_terms_of_service : false;
        $row['term_of_service']['message'] = ($basic_details->privacy_policy_terms_of_service) ? "Has accepted Terms of Service" : "Has not accepted Terms of Service";
        $row['created_time'] = $basic_details->registration_date;
        $row['last_login'] = $basic_details->last_login_time;
        $row['zoho_crm_id'] = intval($basic_details->zoho_lead_id);
        // $row['native_language'] = $basic_details->native_language;
        $row['referred_by'] = !empty($basic_details->referred_by) ? $basic_details->referred_by : "N/A";
        // $row['utm'] = $root->utm_params;
        $row['country'] = isset($root->location->country) ? $root->location->country : "";
        $row['state'] = isset($root->location->state) ? $root->location->state : "";
        $row['city'] = isset($root->location->city) ? $root->location->city : "";
        $row['org_id'] = !empty($org_ids) ? implode(', ', $org_ids) : "N/A";
        $actions = [];
        foreach ($data['actions'] as $action) {

          $row1 = [];
          $row1['label'] = ucwords(str_replace('_', ' ', $action));
          $row1['is_active'] = true;
          $row1['slug'] = $action;
          $row1['url'] = "/learner-profile?learner={$root->user_id}";
          $row1['link_target'] = "_blank";
          $row1['active_class'] = "material-icons-outlined";
          $row1['icon']['type'] = "mdl";
          $row1['icon']['class'] = "material-icons-outlined";
          $row1['icon']['hover'] = "material-icons";
          $row1['icon']['value'] = "timeline";

          $actions[] = $row1;
        }
        $row['actions'] = $actions;

        $grid_view['rows'][] = $row;
      }



      $sortable = ["learner", "email", "created_time", "last_login"];

      foreach ($grid_view['rows'][0] as $key => $value) {
        $row = [];
        $row['field'] = $key;
        $row['label'] = ucwords(str_replace('_', ' ', $key));
        if (in_array($key, $sortable)) {
          $row['sortable'] = true;
        } else {
          $row['sortable'] = false;
        }
        $row['is_active'] = true;
        $grid_view['columns'][] = $row;
      }
    } else {
      $list_view = [];
      $resp['data']['list_view'] = $list_view;
    }
    $filter_data = [];
    foreach ($data['filters'] as $key) {
      $row = [];
      $row['filter'] = $key;
      $row['is_active'] = true;

      $row['selected'] = $data[$key];

      $row['type'] = "dropdown";

      $row['placeholder'] = ucwords(str_replace('_', ' ', $key));

      if ($data['filter'][$key]) {
        foreach ($data['filter'][$key] as $k) {
          $filter_items = [];
          $filter_items['slug'] = $k;
          $filter_items['label'] = ucwords(str_replace('-', ' ', $k));
          $filter_items['filter'] = $key;
          $row['items'][] = $filter_items;
        }
      } else {
        $row['items'] = [];
      }
      $filter_data[] = $row;
    }
    $resp['data']['grid_view'] = $grid_view;
    $resp['data']['filters'] = $filter_data;
    // $resp['data']['actions'] = $actions;
    return $resp;
  }


  public static function get_learner_demo_requests($data)
  {
    $mustQueries = UserElasticSearch::mustQueries($data);
    // Configured data into must queries that will apply on must clause of script
    $rangeQueries = UserElasticSearch::rangeQueries($data);
    // Configured data into range queries that will apply on range clause of script
    if (!empty($data['sorting'])) {
      $curlPost = [
        "query" => [
          "nested" => [
            "path" => "data.details",
            "query" => [
              "bool" => [
                "must" => $mustQueries,
                "filter" => [
                  "nested" => [
                    "path" => "data.details.cta",
                    "query" => [
                      "bool" => [

                        "must" => $rangeQueries

                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ],
        "sort" => [
          [
            "_script" => [
              "type" => "string",
              "script" => [
                "source" => "if (params['_source']['data']['details'] != null && params['_source']['data']['details']['cta'] != null && params['_source']['data']['details']['cta']['updated_at'] != null) { params['_source']['data']['details']['cta']['updated_at'] } else { '1970-01-01T00:00:00Z' }"
              ],
              "order" => $data['sorting']
            ]
          ]
        ]
      ];
    } else {
      $curlPost = [
        "query" => [
          "nested" => [
            "path" => "data.details",
            "query" => [
              "bool" => [
                "must" => $mustQueries,
                "filter" => [
                  "nested" => [
                    "path" => "data.details.cta",
                    "query" => [
                      "bool" => [

                        "must" => $rangeQueries

                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
    }

    // Convert the PHP array to JSON
    $jsonQuery = json_encode($curlPost, JSON_PRETTY_PRINT);

    $academies = [];
    $org_id_present = false;

    if (isset($data['org_id']) && !empty($data['org_id'])) {

      $org_id_present = true;
      $academies = get_post_meta($data['org_id'], 'academies', true); // Fetch academies based on org_id

      if (!is_array($academies)) {
        $academies = [$academies]; // Ensure it's an array
      }
    }

    $index_name = $data['index'] ?? 'signedup';
    $method = "GET";
    $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
    $curl = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response = curl_exec($curl);
    $results = json_decode($response);

    $params1 = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=9999";
    $curl1 = curl_init();
    curl_setopt_array(
      $curl1,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params1,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response1 = curl_exec($curl1);
    $results1 = json_decode($response1);
    $codes = error_code_setting();
    $statusCode = "";

    $count = 0;
    // if (isset($results->hits->total->value)) {
    //   $count = $results->hits->total->value;
    // } elseif (isset($results->hits->total)) {
    //   $count = $results->hits->total;
    // } else {
    //   $count = 0; // Or handle this scenario appropriately
    // }
    $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    // if ($count > 0) {
    //   $resp = array(
    //     'code' => $codes["POST_INSERT"]["code"],
    //     'message' => 'Results found',
    //     'data' => array('status' => $codes["POST_INSERT"]["code"])
    //   );
    // } else {
    //   $resp = array(
    //     'code' => $codes["POST_INSERT_FAIL"]["code"],
    //     'message' => 'No results found',
    //     'data' => array('status' => $codes["POST_INSERT_FAIL"]["code"])
    //   );
    // }
    if ($data['view'] == "list" || $data['view'] == "list-view") {
      $list_view = [];
    }

    $request_count = 0;
    $request_countt = 0;
    $learner_count = 0;

    foreach ($results1->hits->hits as $res1) {
      $root = $res1->_source->data->details;
      $categoryDetailsFound = false; // Flag to track if any category_details_ key is found in the current record

      $categories = ["ielts", "pte", "english-speaking", "duolingo", "french", "toefl"];
      foreach ($root as $k => $v) {
        if (strpos($k, 'category_details_') === 0) {
          if (!is_array($v)) {
            if ($org_id_present) {
              foreach ($categories as $catt) {
                $cat_name = "category_details_" . $catt;
                if (strpos($k, $cat_name) === 0) {
                  foreach ($root->$cat_name->demo_requests as $demo_req) {

                    if (isset($demo_req->academy_id) && in_array($demo_req->academy_id, $academies)) {
                      $categoryDetailsFound = true;
                      $request_countt++; // Increment count if academy_id matches

                    }
                  }
                }
              }
            } else {
              $categoryDetailsFound = true;
              $request_countt++;
            }
          }
          // Mark that a category_details_ key is found
        }
      }

      if ($categoryDetailsFound) {
        $learner_count++;
      }
    }

    if ($learner_count > 0) {
      $resp = array(
        'code' => $codes["POST_INSERT"]["code"],
        'message' => 'Results found',
        'data' => array('status' => $codes["POST_INSERT"]["code"])
      );
    } else {
      $resp = array(
        'code' => $codes["POST_INSERT_FAIL"]["code"],
        'message' => 'No results found',
        'data' => array('status' => $codes["POST_INSERT_FAIL"]["code"])
      );
    }

    foreach ($results->hits->hits as $res) {
      $root = $res->_source->data->details;
      $user = $res->_source->data->details->user;
      $basic_details = $res->_source->data->details->basic_details;
      $location = $res->_source->data->details->location;

      if (strpos($k, 'category_details_') === 0) {
        if (!is_array($v)) {
          if ($org_id_present) {
            foreach ($categories as $catt) {
              $cat_name = "category_details_" . $catt;
              if (strpos($k, $cat_name) === 0) {
                foreach ($root->$cat_name->demo_requests as $demo_req) {

                  if (isset($demo_req->academy_id) && in_array($demo_req->academy_id, $academies)) {
                    $categoryDetailsFound = true;
                    // Increment count if academy_id matches

                  }
                }
              }
            }
          } else {
            $categoryDetailsFound = true;
          }
        }
      }


      $row = [];
      if ($categoryDetailsFound) {

        $row['learner'] = [
          'name' => $user->name,
          'image' => $user->image,
          'user_id' => $root->user_id
        ];
        $reg_date = new DateTime($basic_details->registration_date);
        $login_date = new DateTime($basic_details->last_login_time);
        $created_time = $reg_date->format('M d, Y, h:i A');
        $last_login = $login_date->format('M d, Y, h:i A');
        $row['time_of_study'] = isset($root->time_of_study) ? ucfirst($root->time_of_study) : "N/A";
        $row['created_time'] = $created_time;
        $row['last_login'] = $last_login;
        $row['country'] = isset($location->country) ? $location->country : "";
        $row['state'] = isset($location->state) ? $location->state : "";
        $row['city'] = isset($location->city) ? $location->city : "";
        $row['is_active'] = false;

        $list_view[] = $row;
      }

      foreach ($root as $k => $v) {
        if (strpos($k, 'category_details_') === 0) {
          $request_count++;
        }
      }
    }

    $categories = ["all", "ielts", "pte", "english-speaking", "toefl", "duolingo", "french", "data-science-and-analytics"];
    foreach ($categories as $category) {
      $taxonomy = 'course_category'; // Replace with your custom taxonomy
      $term_slug = $category; // Replace with your term slug
      $term = get_term_by('slug', $term_slug, $taxonomy);

      if ($term) {
        $term_name = $term->name;
        $term_id = $term->term_id;
      } else {
        $term_name = ucfirst($category);
        $term_id = "";
      }


      $row = [];
      $row['value'] = $term_name;
      $row['slug'] = $category;
      $row['category_id'] = $term_id;

      $cat[] = $row;
    }

    $active_filters = ["category", "info-available", "cta", "demo_request_period", "time_of_study", "academy"];
    foreach ($data as $key => $value) {
      if (in_array($key, $data['filters']) || ($key == "info-available" && in_array($key, $data['filters']))) {
        $row1 = [];
        $row1['filter'] = $key;
        if (in_array($key, $active_filters)) {
          if(empty($data['filter'][$key])){
            $row1['is_active'] = false;
          }else{
            $row1['is_active'] = true;
          }
        } else {
          $row1['is_active'] = false;
        }
        if ($key == "cta") {
          $row1['placeholder'] = strtoupper(str_replace('_', ' ', $key));
        } else {
          $row1['placeholder'] = ucfirst(str_replace(['-', '_'], ' ', $key));
        }


        if ($key == "info-available") {
          $row1['type'] = "dropdown_multi_select";
          foreach ($value as $sel_val) {
            $sel_items = [];
            $sel_items['slug'] = $sel_val;
            $sel_items['label'] = ucfirst(str_replace(['-', '_'], ' ', $sel_val));
            $sel_items['filter'] = $key;
            $sel_items['is_checked'] = true;
            $row1['selected'][] = $sel_items;
          }
        } elseif ($key == "org" || $key == "academy") {
          $row1['type'] = "dropdown_multi_select";
          // $row1['selected'] = [];
          $headers = [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache"
          ];
          if ($key == "org") {
            if($value[0] == "all"){
              $sel_items = [];
                $sel_items['slug'] = $value[0];
                $sel_items['label'] = "All";
                $sel_items['filter'] = $key;
                $sel_items['is_checked'] = true;
                $row1['selected'][] = $sel_items;
             }else{
              foreach ($value as $val) {
                $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/org/_doc/org-" . $val;
                // $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/academies/_doc/academies-" . $val;
                $curlPost = "";
                $response = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);
  
                // $name = get_post_meta($value, 'organisation_name', true);
                if (empty($name)) {
                  $name = get_post_meta($value, 'organisation_name', true);
                }
                $results = json_decode($response['response']);
                $name = "";
                // if($key == "org"){
                //   $name = $results->_source->data->details->organisation_name;
                // }elseif($key == "academy"){
                $name = $results->_source->data->details->organisation_name;
                // }
  
                $sel_items = [];
                $sel_items['slug'] = $val;
                $sel_items['label'] = $name;
                $sel_items['filter'] = $key;
                $sel_items['is_checked'] = true;
                $row1['selected'][] = $sel_items;
              }
            }
            
          } elseif($key == "academy") {
           if($value[0] == "all"){
            $sel_items = [];
              $sel_items['slug'] = $value[0];
              $sel_items['label'] = "All";
              $sel_items['filter'] = $key;
              $sel_items['is_checked'] = true;
              $row1['selected'][] = $sel_items;
           }else{
             foreach ($value as $val) {
               $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/academies/_doc/academies-" . $val;
               $curlPost = "";
               $response = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);
 
               // $name = get_post_meta($value, 'organisation_name', true);
               if (empty($name)) {
                 $name = get_post_meta($value, 'ACADEMY_TITLE', true);
               }
               $results = json_decode($response['response']);
               $name = "";
               // if($key == "org"){
               //   $name = $results->_source->data->details->organisation_name;
               // }elseif($key == "academy"){
               $name = $results->_source->data->details->academy_name;
               // }
 
               $sel_items = [];
               $sel_items['slug'] = $val;
               $sel_items['label'] = $name;
               $sel_items['filter'] = $key;
               $sel_items['is_checked'] = true;
               $row1['selected'][] = $sel_items;
             }
           }
          }
        } else {
          $row1['type'] = "dropdown";
          $row1['selected'] = $value;
        }


        if (isset($data['filter'][$key])) {

          if($key == "academy" && empty($data['filter'][$key])){
            $row1['items'] = [];
          }

          foreach ($data['filter'][$key] as $val) {

            if ($key === "demo_request_period") {
              if ($val === "180-days") {
                $label = "Last 180 Days";
              } elseif ($val === "90-days") {
                $label = "Last 90 Days";
              } elseif ($val === "30-days") {
                $label = "Last 30 Days";
              } elseif ($val === "7-days") {
                $label = "Last 7 Days";
              } elseif ($val === "24-hours") {
                $label = "Last 24 Hours";
              } elseif ($val === "all") {
                $label = "All";
              }

              $items = [];
              $items['slug'] = $val;
              $items['label'] = $label;
              $items['filter'] = $key;
              $row1['items'][] = $items;
            } elseif ($key === "time_of_study") {
              if ($val === "morning") {
                $label = "6am - 12pm (Morning)";
              } elseif ($val === "afternoon") {
                $label = "12pm - 4pm (Afternoon)";
              } elseif ($val === "evening") {
                $label = "4pm - 8pm (Evening)";
              } elseif ($val === "night") {
                $label = "8pm - 11pm (Night)";
              } elseif ($val === "all") {
                $label = "All";
              }
              $items = [];
              $items['slug'] = $val;
              $items['label'] = $label;
              $items['filter'] = $key;
              $row1['items'][] = $items;
            } elseif ($key === "info-available") {
              $items = [];
              $items['slug'] = $val;
              $items['label'] = ucfirst(str_replace(['-', '_'], ' ', $val));
              $items['filter'] = $key;
              $items['is_checked'] = false;
              $row1['items'][] = $items;
            } elseif ($key === "category") {


              $taxonomy = 'course_category'; // Replace with your custom taxonomy
              $term_slug = $val; // Replace with your term slug
              $term = get_term_by('slug', $term_slug, $taxonomy);

              if ($term) {
                $term_name = $term->name;
                $term_id = $term->term_id;
              } else {
                $term_name = ucfirst($val);
                $term_id = "";
              }


              $items = [];
              $items['slug'] = $val;
              $items['label'] = $term_name;
              $items['filter'] = $key;
              $row1['items'][] = $items;
            } elseif ($key == "academy" || $key == "org") {
              $row1['items'][] = $val;
            } else {

              $items = [];
              $items['slug'] = $val;
              $items['label'] = ucfirst(str_replace(['-', '_'], ' ', $val));
              $items['filter'] = $key;
              $row1['items'][] = $items;
            }
          }
        } else {
          $row1['items'] = [];
        }


        $filters[] = $row1;
      }
    }

    $resp['data']['learners_count'] = $learner_count;
    $resp['data']['demo_req_count'] = $request_countt;
    $resp['data']['list_view'] = $list_view;
    $resp['data']['category'] = $cat;
    $resp['data']['filters'] = $filters;

    // new WP_REST_Response($signup_result, $codes["POST_INSERT_FAIL"]["code"])
    return $resp;
  }

  public static function get_learner_single_demo_requests($learner_id, $org_id = "")
  {

    $index_name = 'signedup';
    $method = "GET";
    $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_doc/" . $index_name . "-" . $learner_id;
    $curl = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        // CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response = curl_exec($curl);
    $results = json_decode($response);
    $codes = error_code_setting();
    $statusCode = "";
    $count = "";
    if (isset($results->found)) {
      $count = 1;
    } else {
      $count = 0; // Or handle this scenario appropriately
    }
    $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    if ($count > 0) {
      $resp = array(
        'code' => $codes["POST_INSERT"]["code"],
        'message' => 'Results found',
        'data' => array('status' => $codes["POST_INSERT"]["code"])
      );
    } else {
      $resp = array(
        'code' => $codes["POST_INSERT_FAIL"]["code"],
        'message' => 'No results found',
        'data' => array('status' => $codes["POST_INSERT_FAIL"]["status"])
      );
    }



    // foreach ($results->hits->hits as $res) {
    $root = $results->_source->data->details;
    $user = $results->_source->data->details->user;
    $basic_details = $results->_source->data->details->basic_details;
    // $location = $res->_source->data->details->location;
    $demo_request = [];
    $categories = ["ielts", "pte", "english-speaking", "toefl", "duolingo", "french", "data-science-and-analytics"];
    $request_count = 0;
    $study_abroad = [];
    $target_score = ["what_is_your_target_pte_score", "what_is_your_target_toefl_score", "what_is_your_target_det_score"];
    $planned = ["when_do_you_plan_to_take_the_DET_exam", "when_do_you_plan_to_take_the_IELTS_exam", "when_do_you_plan_to_take_the_PTE_exam"];
    foreach ($categories as $category) {
      $cat_name = 'category_details_' . $category;
      $request = [];

      // Check if $root->$cat_name exists and is not an array
      if (isset($root->$cat_name) && !is_array($root->$cat_name)) {

        // If $org_id exists, fetch academies
        if (!empty($org_id)) {
          $academies = get_post_meta($org_id, "academies", true); // Fetch academies array

          // Ensure academies is an array
          if (!is_array($academies)) {
            $academies = [];
          }
        }

        // Check if demo_requests exist in $cat_name and is an array
        $proceed = true;
        if (isset($root->$cat_name->demo_requests) && is_array($root->$cat_name->demo_requests)) {

          // If $org_id exists, only proceed if any academy_id matches
          if (!empty($org_id)) {
            $proceed = false; // Initially set to false, will set true only if a match is found

            foreach ($root->$cat_name->demo_requests as $demo_req) {
              if (in_array($demo_req->academy_id, $academies)) {
                $proceed = true; // Found matching academy_id, proceed with this category
                break; // Exit the loop once a match is found
              }
            }
          }
        }

        // Only proceed with the category if $org_id is empty or academy_id matches
        if ($proceed) {


          foreach ($root->$cat_name as $key => $val) {
            if (isset($root->$cat_name->updated_at)) {
              $updated = new DateTime($root->$cat_name->updated_at);
              $updaated_time = $updated->format('M d, Y');
            } else {
              $updaated_time = "";
            }

            if (isset($root->$cat_name->course)) {
              $course = $root->$cat_name->course;
            } else {
              $course = "";
            }

            $req = [];
            switch ($key) {
              case "which_" . $category . "_exam_do_you_want_to_take":
                $req['label'] = "Type";
                $req['value'] = $val;
                break;
              case in_array($key, $planned):
                $req['label'] = "Planned";
                $req['value'] = $val;
                break;
              case in_array($key, $target_score):
                $req['label'] = "Target score";
                $req['value'] = $val;
                break;
              case "which_toefl_exam_are_you_planning_to_take":
                $req['label'] = "Planning For:";
                $req['value'] = ($val == "Don't_know" ? "Don't Know" : strtoupper($val));
                break;
              case "what_is_your_target_band_score":
                $req['label'] = "Target Band Score";
                $req['value'] = $val;
                break;
              case "updated_at":
                $request[lcfirst($key)] = $updaated_time;
                break;
              case "what_is_your_current_level_of_english":
                $req['label'] = "Current level of English";
                $req['value'] = $val;
                break;
              case "mainly_why_do_you_want_to_improve_your_english_speaking_skills":
                $req['label'] = "Why do you want to improve your English speaking skills";
                $req['value'] = $val;
                break;
              case "what_best_describes_you":
                $req['label'] = "What best describes you";
                $req['value'] = $val;
                break;
              case "why_do_you_want_to_learn_french":
                $req['label'] = "Why do you want to learn French";
                $req['value'] = $val;
                break;
              case "what_is_your_current_level_of_proficiency_in_the_french_language":
                $req['label'] = "Current level of proficiency in the French language";
                $req['value'] = $val;
                break;
              case "whats_your_experience_level":
                $req['label'] = "Experience Level";
                $req['value'] = $val;
                break;
              case "whats_your_main_goal":
                $req['label'] = "Main Goal";
                $req['value'] = $val;
                break;
              case "whats_your_area_of_interest":
                $req['label'] = "Area Of Interest";
                $req['value'] = $val;
                break;
            }

            if (!empty($req)) {
              $request['stats'][] = $req;
            }
          }

          // Get category name based on taxonomy and term slug
          $taxonomy = 'course_category'; // Custom taxonomy
          $term_slug = $category; // Term slug
          $term = get_term_by('slug', $term_slug, $taxonomy);

          if ($term) {
            $term_name = $term->name;
          } else {
            $term_name = ucfirst($val);
          }

          $request['category'] = $term_name;
          $request['course'] = (!empty($course)) ? $course : "N/A";

          // Add the request to demo_request array
          $demo_request[] = $request;
        } // End of proceed check
      }
      // }



      foreach ($root as $k => $v) {
        if (strpos($k, 'category_details_') === 0) {
          $request_count++;
        }
      }
      if (isset($root->study_abroad)) {
        foreach ($root->study_abroad as $key => $val) {
          $study_abroad[lcfirst($key)] = $val;
        }
      }

      $reg_date = new DateTime($basic_details->registration_date);
      $login_date = new DateTime($basic_details->last_login_time);
      $created_time = $reg_date->format('M d, Y');
      $last_login = $login_date->format('M d, Y');

      $row = [];
      // $row['learner'] = [
      $row['name'] = $user->name;
      $row['image'] = $user->image;
      $row['user_id'] = $root->user_id;
      $row['phone'] = !empty($user->phone) ? $user->phone : "N/A";
      $row['email']['label'] = "Email";
      $row['email']['is_verified'] = !empty($user->email) ? true : false;
      $row['email']['message'] = !empty($user->email) ? "Email verified" : "Email not verified";
      $row['email']['value'] = !empty($user->email) ? $user->email : "N/A";
      $row['whatsapp']['label'] = "Whatsapp";
      $row['whatsapp']['is_verified'] = isset($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
      $row['whatsapp']['message'] = (isset($basic_details->yuno_user_whatsapp_check) && $basic_details->yuno_user_whatsapp_check === true) ? "Learner has agreed to receive Whatsapp messages from Yuno" : "Learner has NOT agreed to receive Whatsapp messages from Yuno";
      $row['whatsapp']['value'] = isset($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
      $row['time_of_study'] = isset($root->time_of_study) ? ucfirst($root->time_of_study) : "N/A";
      $row['term_of_service'] = isset($basic_details->privacy_policy_terms_of_service) ? $basic_details->privacy_policy_terms_of_service : false;
      $row['created_time'] = $created_time;
      $row['last_login'] = $last_login;
      $row['zoho_crm_id'] = intval($basic_details->zoho_lead_id);
      $row['native_language'] = isset($basic_details->native_language) ? $basic_details->native_language : "";
      // $row['utm'] = $root->utm_params;
      $row['country'] = isset($root->location->country) ? $root->location->country : "";
      $row['state'] = isset($root->location->state) ? $root->location->state : "";
      $row['city'] = isset($root->location->city) ? $root->location->city : "";
      $row['cta'] = isset($root->cta) ? $root->cta : "";
      $row['study_abroad'] = isset($study_abroad) ? $study_abroad : "N/A";
      $row['demo_request_count'] = $request_count;
      $row['demo_requests'] = $demo_request;
      $resp['data'] = $row;
    }

    return $resp;
  }

  public static function get_orgadmin_learner_insights($data)
  {

    $codes = error_code_setting();
    $mustQueries = UserElasticSearch::mustQueries($data);
    // Configured data into must queries that will apply on must clause of script
    $rangeQueries = UserElasticSearch::rangeQueries($data);
    // Configured data into range queries that will apply on range clause of script

    $sorting = UserElasticSearch::get_sorting_query($data['sorting']['column'], $data['sorting']['type']);

    if (empty($mustQueries) && empty($rangeQueries)) {
      // If both are empty, construct the query to retrieve all results
      $curlPost = [
        "query" => [
          "bool" => [
            "must" => [
              [
                "nested" => [
                  "path" => "data.details.details_from_org",
                  "query" => [
                    "bool" => [
                      "must" => [
                        [
                          "match" => [
                            "data.details.details_from_org.org_id" => $data['org_id']
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ],
        "sort" => $sorting["sort"]
      ];
    } elseif (empty($data['sorting']['column']) && empty($data['sorting']['type'])) {
      $curlPost = [
        "query" => [
          "bool" => [
            "must" => [
              [
                "nested" => [
                  "path" => "data.details",
                  "query" => [
                    "bool" => [
                      "must" => $mustQueries
                    ]
                  ]
                ]
              ],
              [
                "nested" => [
                  "path" => "data.details.details_from_org",
                  "query" => [
                    "bool" => [
                      "must" => [
                        [
                          "match" => [
                            "data.details.details_from_org.org_id" => $data['org_id']
                          ]
                        ]
                      ],
                      "filter" => $rangeQueries
                    ]
                  ]
                ]
              ]
            ]
          ]
        ],
      ];
    } else {
      // If not empty, construct the nested query with must and filter conditions
      $curlPost = [
        "query" => [
          "bool" => [
            "must" => [
              [
                "nested" => [
                  "path" => "data.details",
                  "query" => [
                    "bool" => [
                      "must" => $mustQueries
                    ]
                  ]
                ]
              ],
              [
                "nested" => [
                  "path" => "data.details.details_from_org",
                  "query" => [
                    "bool" => [
                      "must" => [
                        [
                          "match" => [
                            "data.details.details_from_org.org_id" => $data['org_id']
                          ]
                        ]
                      ],
                      "filter" => $rangeQueries
                    ]
                  ]
                ]
              ]
            ]
          ]
        ],
        "sort" => $sorting["sort"]
      ];
    }


    // Convert the PHP array to JSON
    $jsonQuery = json_encode($curlPost, JSON_PRETTY_PRINT);

    $index_name = $data['index'] ?? 'signedup';
    $method = "GET";
    $params = ELASTIC_SEARCH_END_URL . "/" . $index_name . "/_search?size=" . $data['limit'] . "&from=" . $data['offset'];
    $curl = curl_init();
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
        CURLOPT_URL            => $params,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 30,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_POSTFIELDS     => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER     => array(
          "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION, /// . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
          "cache-control: no-cache",
          "content-type: application/json"
        ),
      )
    );
    $response = curl_exec($curl);
    $results = json_decode($response);

    $codes = error_code_setting();
    $statusCode = "";

    $count = "";
    if (isset($results->hits->total->value)) {
      $count = $results->hits->total->value;
    } elseif (isset($results->hits->total)) {
      $count = $results->hits->total;
    } else {
      $count = 0; // Or handle this scenario appropriately
    }
    $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($count > 0) {
      $resp = array(
        'code' => $codes["POST_INSERT"]["code"],
        'message' => 'Results found',
        'data' => array('status' => $codes["POST_INSERT"]["status"])
      );
    } else {
      $resp = array(
        'code' => $codes["POST_INSERT_FAIL"]["code"],
        'message' => 'There are no learners in your organization. To have a list of learners here, invite them to enroll in a course or share learning content with them',
        'data' => array('status' => $codes["POST_INSERT_FAIL"]["status"])
      );
    }
    if ($data['view'] == "list") {
      $list_view = [];
    }

    $request_count = 0;
    $resp['data']['count'] = $count;
    if ($data['view'] == "grid-view") {
      $grid_view = [];

      foreach ($results->hits->hits as $res) {
        $root = $res->_source->data->details;
        $user = $res->_source->data->details->user;
        $basic_details = $res->_source->data->details->basic_details;


        $row = [];
        $row['learner'] = [
          'name' => $user->name,
          'image' => $user->image,
          'user_id' => $root->user_id
        ];
        $row['email'] = $user->email;
        $row['phone'] = !empty($user->phone) ? intval($user->phone) : "N/A";
        // $row['role'] = $root->role;
        $row['time_of_study'] = !empty($root->time_of_study) ? $root->time_of_study : "N/A";
        $row['whatsapp']['value'] = !empty($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
        $row['whatsapp']['message'] = ($basic_details->yuno_user_whatsapp_check) ? "Can send messages on WhatsApp" : "Can not send messages on WhatsApp";

        $row['org_created_time'] = $root->details_from_org[0]->created_at;
        $row['last_login_time'] = $basic_details->last_login_time;
        // $row['native_language'] = isset($basic_details->native_language) ? $basic_details->native_language : "";
        $row['country'] = isset($root->location->country) ? $root->location->country : "";
        $row['state'] = isset($root->location->state) ? $root->location->state : "";
        $row['city'] = isset($root->location->city) ? $root->location->city : "";
        $actions = [];
        $grid_view['rows'][] = $row;
      }



      $sortable = ["learner", "email", "created_time", "last_login"];

      foreach ($grid_view['rows'][0] as $key => $value) {
        $row = [];
        $row['field'] = $key;
        $row['label'] = ucwords(str_replace('_', ' ', $key));
        if (in_array($key, $sortable)) {
          $row['sortable'] = true;
        } else {
          $row['sortable'] = false;
        }
        $row['is_active'] = true;
        $grid_view['columns'][] = $row;
      }
      $resp['data']['grid_view'] = $grid_view;
    } else {
      $list_view = [];
      foreach ($results->hits->hits as $res) {
        $root = $res->_source->data->details;
        $user = $res->_source->data->details->user;
        $basic_details = $res->_source->data->details->basic_details;

        $row = [];
        $row['learner'] = [
          'name' => isset($user->name) ? $user->name : "",
          'image' => isset($user->image) ? $user->image : "",
          'user_id' => isset($root->user_id) ? $user->user_id : ""
        ];
        $row['email'] = isset($user->email) ? $user->email : "N/A";
        $row['phone'] = !empty($user->phone) ? intval($user->phone) : "N/A";
        $row['time_of_study'] = !empty($root->time_of_study) ? $root->time_of_study : "N/A";
        // $row['whatsapp']['value'] = !empty($basic_details->yuno_user_whatsapp_check) ? $basic_details->yuno_user_whatsapp_check : false;
        // $row['whatsapp']['message'] = ($basic_details->yuno_user_whatsapp_check) ? "Can send messages on WhatsApp" : "Can not send messages on WhatsApp";
        // $row['org_created_time'] = $root->details_from_org[0]->created_at;
        $row['last_login_time'] = $basic_details->last_login_time;
        $row['country'] = isset($root->location->country) ? $root->location->country : "";
        $row['state'] = isset($root->location->state) ? $root->location->state : "";
        $row['city'] = isset($root->location->city) ? $root->location->city : "";

        // Add row to list view (same structure as grid, but for list view)
        $list_view[] = $row;
      }
      $resp['data']['list_view'] = $list_view;
    }
    $filter_data = [];
    foreach ($data['filters'] as $key) {
      $row = [];
      $row['filter'] = $key;
      $row['is_active'] = true;

      $row['selected'] = $data[$key];

      $row['type'] = "dropdown";

      $row['placeholder'] = ucwords(str_replace('_', ' ', $key));

      if ($data['filter'][$key]) {
        foreach ($data['filter'][$key] as $k) {
          $filter_items = [];
          $filter_items['slug'] = $k;
          $filter_items['label'] = ucwords(str_replace('-', ' ', $k));
          $filter_items['filter'] = $key;
          $row['items'][] = $filter_items;
        }
      } else {
        $row['items'] = [];
      }
      $filter_data[] = $row;
    }

    $resp['data']['filters'] = $filter_data;
    return $resp;
  }
}
