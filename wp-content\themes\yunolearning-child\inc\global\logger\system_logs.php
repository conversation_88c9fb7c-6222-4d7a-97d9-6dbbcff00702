<?php

// Define the base path for log files
define('YUNO_LOG_DIR', WP_CONTENT_DIR . '/yuno-all-logs');

class YUNO_Custom_Logger
{
	protected $yuno_debug_log = false;
	protected $yuno_debug_log_es = false;
	protected $yuno_debug_log_jf = false;
	protected $yuno_debug_display = false;

	protected function __construct()
	{
		// Ensure YUNO Debug Log is enabled
		if (!defined('YUNO_DEBUG_LOG') || !defined('YUNO_DEBUG_LOG_ES') || !defined('YUNO_DEBUG_LOG_JF') || !defined('YUNO_DEBUG_DISPLAY')) {
			error_log('YUNO_Custom_Logger: Ensure YUNO_DEBUG_LOG,_ES,_JF and YUNO_DEBUG_DISPLAY are set to true/false in wp-config.php');
			return;
		} else {
			$this->yuno_debug_log = (bool) YUNO_DEBUG_LOG;
			$this->yuno_debug_log_es = (bool) YUNO_DEBUG_LOG_ES;
			$this->yuno_debug_log_jf = (bool) YUNO_DEBUG_LOG_JF;
			$this->yuno_debug_display = (bool) YUNO_DEBUG_DISPLAY;
		}
	}
	// Helper function to get the log file path based on the current date
	private function get_log_file_path($file_type)
	{
		try {
			$year = date('Y');
			$month = date('m');

			$log_dirs = array();
			$log_dirs['API'] = YUNO_LOG_DIR . '/API';
			$log_dirs['PHP'] = YUNO_LOG_DIR . '/PHP';
			$log_dirs['SQL'] = YUNO_LOG_DIR . '/SQL';

			foreach ($log_dirs as $log_dir) {
				// Create year directory if it does not exist
				if (!file_exists($log_dir)) {
					if (!mkdir($log_dir, 0755, true)) {
						throw new Exception("Failed to create directory: " . $log_dir);
					}
				}
			}

			$log_dirs['API'] = YUNO_LOG_DIR . '/API/' . $year;
			$log_dirs['PHP'] = YUNO_LOG_DIR . '/PHP/' . $year;
			$log_dirs['SQL'] = YUNO_LOG_DIR . '/SQL/' . $year;

			foreach ($log_dirs as $log_dir) {
				// Create year directory if it does not exist
				if (!file_exists($log_dir)) {
					if (!mkdir($log_dir, 0755, true)) {
						throw new Exception("Failed to create directory: " . $log_dir);
					}
				}
			}

			// Define the log file path for the current month
			$log_dirs['API'] = YUNO_LOG_DIR . '/API/' . $year . '/api-log-' . $month . '-' . $year . '.json';
			$log_dirs['PHP'] = YUNO_LOG_DIR . '/PHP/' . $year . '/php-log-' . $month . '-' . $year . '.json';
			$log_dirs['SQL'] = YUNO_LOG_DIR . '/SQL/' . $year . '/sql-log-' . $month . '-' . $year . '.json';

			return $log_dirs[$file_type];
		} catch (Exception $e) {
			error_log('YUNO_Custom_Logger Error in get_log_file_path: ' . $e->getMessage());
			if ($this->yuno_debug_display) {
				echo 'Error getting log file path: ' . $e->getMessage();
			}
			return false;
		}
	}

	// Recursive function to convert PHP array to text
	private function convertToText($data, $indent = '')
	{
		$text = '';
		foreach ($data as $key => $value) {
			if (is_array($value)) {
				if (isset($value[0]) && is_array($value[0])) {
					// Handle array of objects
					$text .= $indent . $key . ":\n";
					foreach ($value as $item) {
						$text .= convertToText($item, $indent . '  ');
					}
				} else {
					// Handle nested objects
					$text .= $indent . $key . ":\n";
					$text .= convertToText($value, $indent . '  ');
				}
			} else {
				$text .= $indent . $key . ': ' . $value . "\n";
			}
		}
		return $text;
	}

	// Helper function to write logs
	protected function write_logs_hander($log_data, $file_type)
	{
		if ($this->yuno_debug_log_jf)
			$this->write_log_to_file($log_data, $file_type);

		if ($this->yuno_debug_log_es)
			$this->write_log_to_es($log_data, $file_type);
	}

	// Helper function to write log data to the file
	private function write_log_to_file($log_data, $file_type)
	{
		try {
			$log_file_path = $this->get_log_file_path($file_type);

			if (!file_exists($log_file_path)) {
				// Create the log file if it does not exist
				if (!touch($log_file_path)) {
					throw new Exception("Failed to create log file: " . $log_file_path);
				}
			}

			// Append the log data to the file
			if (file_put_contents($log_file_path, json_encode($log_data, JSON_PRETTY_PRINT) . PHP_EOL, FILE_APPEND) === false) {
				throw new Exception("Failed to write to log file: " . $log_file_path);
			}
		} catch (Exception $e) {
			error_log('YUNO_Custom_Logger Error: ' . $e->getMessage());
			if ($this->yuno_debug_display) {
				echo 'Error writing log: ' . $e->getMessage();
			}
		}
	}

	// Helper function to write log data to the elastic search
	private function write_log_to_es($log_data, $file_type)
	{
		try {
			// Get the current month and year
			$current_month = date('M');
			$current_year = date('Y');

			// Create a new filename using the current month and year
			$new_docname = 'yuno_' . strtolower($file_type) . '_logs_' . strtolower($current_month) . '_' . $current_year;

			global $YC;
			$YC->loadLibary('elasticSearch', 'es');
			
			if ($file_type == "SQL") return; //SQL is not logged in elastic search
			if ($file_type == "API") {

				$es_data = [
					'time_zone'			=> $log_data['time_zone'],
					'timestamp' 		=> $log_data['timestamp'],
					'start_time' 		=> $log_data['start_time'],
					'end_time' 			=> $log_data['end_time'],
					'elapsed_time'      => $log_data['elapsed_time'],
					'response_length'   => $log_data['response_length'],
					'ip_address' 		=> $log_data['ip_address'],
					'user'       		=> $log_data['user'],
					'request'           => json_encode($log_data['request'], JSON_UNESCAPED_SLASHES),
					'response'          => json_encode($log_data['response'], JSON_UNESCAPED_SLASHES),
				];

				$response = $YC->es->create($new_docname, $es_data);

				if (is_wp_error($response)) {
					throw new Exception("Elastic Search request failed for API: " . $response->get_error_message());
				}
			}//end of API
			if($file_type=="PHP"){

				$es_data = [
					'time_zone'			=> $log_data['time_zone'],
					'timestamp' 		=> $log_data['timestamp'],
					'user'       		=> $log_data['user'],
					'request'           => $log_data['request'],
					'type' => $log_data['type'],
					'error_number' => $log_data['error_number'],
					'error_message' => $log_data['error_message'],
					'file' => $log_data['file'],
					'line' => $log_data['line'],
					'server' => json_encode($log_data['server'], JSON_UNESCAPED_SLASHES)
				];

				$response = $YC->es->create($new_docname, $es_data);
				
				if (is_wp_error($response)) {
					throw new Exception("Elastic Search request failed for PHP: " . $response->get_error_message());
				}
			}//end of PHP
			if($file_type=="SQL"){
				
				$es_data = [
					'time_zone'			=> $log_data['time_zone'],
					'timestamp' 		=> $log_data['timestamp'],
					'total_execution_time' => $log_data['total_execution_time'],
					'query_detail' => json_encode($log_data['query_detail'], JSON_UNESCAPED_SLASHES),
					'server' => json_encode($log_data['server'], JSON_UNESCAPED_SLASHES)
				];

				$response = $YC->es->create($new_docname, $es_data);

				if (is_wp_error($response)) {
					throw new Exception("Elastic Search request failed for SQL: " . $response->get_error_message());
				}
			}//end of SQL
		} catch (Exception $e) {
			error_log('YUNO_Custom_Logger Elastic Search Error: ' . $e->getMessage());
			if ($this->yuno_debug_display) {
				echo 'Error writing to Elastic Search: ' . $e->getMessage();
			}
		}
	}
}

###################### API's log Start ######################

class YUNO_API_Logger extends YUNO_Custom_Logger
{
	// Variables to store request data, start/end times, and request date/time
	private $request_log_data = array();
	private $request_start_time = 0;
	private $request_end_time = 0;
	private $request_ip_address = '';
	private $logged_in_user = array();
	//api object
	private static $api_instance;

	private function __construct()
	{
		parent::__construct();
	}

	// Implement a singleton pattern to ensure only one instance of the logger_v2 is used.
	public static function get_instance()
	{
		if (null === self::$api_instance) {
			self::$api_instance = new self();
			self::$api_instance->trigger_api_logs();
		} else {
			self::$api_instance->trigger_api_logs();
		}
	}

	//Trigger API hook for logs
	private function trigger_api_logs()
	{
		if ($this->yuno_debug_log) {
			add_action('rest_api_init', array(&$this, 'capture_request_forwarder'));
			add_filter('rest_pre_dispatch', array(&$this, 'log_rest_api_start'), 10, 3);
			add_filter('rest_post_dispatch', array(&$this, 'log_rest_api_end'), 10, 3);
		}
	}

	// Capture the start time, IP address, and request date/time
	public function capture_request_forwarder()
	{

		$this->request_start_time = microtime(true);
		$this->request_ip_address = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
	}


	// Function to get controller and callback for the requested route
	private function get_route_details($request)
	{
		$server = rest_get_server();
		$routes = $server->get_routes();
		$request_route = $request->get_route();
		$request_method = $request->get_method();

		foreach ($routes as $route_pattern => $route_details) {
			// Convert the route pattern into a regex pattern
			$route_regex = '@^' . preg_replace('@\(\?P<\w+>(.+?)\)@', '$1', $route_pattern) . '$@';


			// Check if the request route matches the current route regex pattern
			if (preg_match($route_regex, $request_route)) {
				foreach ($route_details as $route) {
					if (in_array($request_method, $route['methods'])) {
						return array(
							'route' => $route_pattern,
							'controller' => isset($route['callback']) ? get_class($route['callback'][0]) : 'N/A',
							'object' => isset($route['callback']) ? serialize($route['callback'][0]) : 'N/A',
							'callback'   => isset($route['callback']) ? $route['callback'][1] : 'N/A',
						);
					}
				}
			}
		}
		return array('controller' => 'N/A', 'object' => 'N/A', 'callback' => 'N/A');
	}

	// Log API Request Details in JSON Format
	public function log_rest_api_start($result, $server, $request)
	{

		// Get route details (controller and callback)
		$route_details = $this->get_route_details($request);

		// Store request details in a global variable
		$this->request_log_data = array(
			'method'     => $request->get_method(),
			'route'      => $request->get_route(),
			'headers'    => $request->get_headers(),
			'parameters' => $request->get_params(),
			'backtrace'	 =>	$route_details,
		);

		return $result;
	}

	// Log API Response Details at the end of the REST API lifecycle
	public function log_rest_api_end($response, $request, $server)
	{
		// Get logged-in user details

		$user_id = get_current_user_id();
		if ($user_id) {
			$user_info = get_userdata($user_id);
			$this->logged_in_user = array(
				'ID'       => $user_info->ID,
				'username' => $user_info->user_login,
				'email'    => $user_info->user_email,
				'roles'    => $user_info->roles
			);
		} elseif (defined('YL_CURRENT_USER') && isset(YL_CURRENT_USER["user"])) {
			$this->logged_in_user = array(
				'ID'       => YL_CURRENT_USER["user"]['id'],
				'username' => YL_CURRENT_USER["user"]['login'],
				'email'    => YL_CURRENT_USER["user"]['email'],
				'roles'    => YL_CURRENT_USER["user"]['role']
			);
		} else {
			$this->logged_in_user = array(
				'ID' => null,
				'username' => 'Guest',
				'email'    => null,
				'roles'    => null
			);
		}

		// Capture the end time of the request
		$this->request_end_time = microtime(true);

		// Calculate elapsed time
		$elapsed_time = $this->request_end_time - $this->request_start_time;

		// Convert the start and end times to readable format
		$start_time_readable = date('Y-m-d H:i:s', (int) $this->request_start_time);
		$end_time_readable = date('Y-m-d H:i:s', (int) $this->request_end_time);

		// Prepare response details
		$response_data = is_wp_error($response) ?
			array('error' => $response->get_error_message()) :
			array('status' => $response->get_status(), 'data' => $response->get_data());

		// Calculate response length
		$response_length = strlen(json_encode($response_data));

		// Merge request and response data
		$log_entry = array(
			'time_zone'			=> date_default_timezone_get(),
			'timestamp' 		=> (new DateTime())->format('c'),
			'start_time' 		=> $start_time_readable,
			'end_time' 			=> $end_time_readable,
			'elapsed_time'      => $elapsed_time,
			'response_length'   => $response_length,
			'ip_address' 		=> $this->request_ip_address,
			'user'       		=> $this->logged_in_user,
			'request'           => $this->request_log_data,
			'response'          => $response_data,
		);

		// Encode the log entry as JSON
		//$json_log = json_encode($log_entry, JSON_PRETTY_PRINT);

		// Write the JSON log to the file
		$this->write_logs_hander($log_entry, 'API');

		return $response;
	}
} //class API's end
YUNO_API_Logger::get_instance();

###################### API's log End ######################

#######################  PHP Logs Start #######################

class YUNO_PHP_Logger extends YUNO_Custom_Logger
{
	// Variables to store last exception
	private $lastException = NULL;
	private $logged_in_user = array();
	//php object
	private static $php_instance;

	private function __construct()
	{
		parent::__construct();
	}

	// Implement a singleton pattern to ensure only one instance of the logger_v2 is used.
	public static function get_instance()
	{
		if (null === self::$php_instance) {
			self::$php_instance = new self();
			self::$php_instance->trigger_php_logs();
		} else {
			self::$php_instance->trigger_php_logs();
		}
	}

	//Trigger PHP hook for logs
	private function trigger_php_logs()
	{
		if ($this->yuno_debug_log) {
			// Set the custom error, exception, and shutdown handlers
			set_exception_handler(array(&$this, 'json_exception_handler'));
			set_error_handler(array(&$this, 'json_error_handler'));
			register_shutdown_function(array(&$this, 'json_shutdown_handler'));
		}
	}

	// Custom error handler
	public function json_error_handler($errno, $errstr, $errfile, $errline)
	{
		$error_types = [
			E_ERROR => 'Error',
			E_WARNING => 'Warning',
			E_PARSE => 'Parse Error',
			E_NOTICE => 'Notice',
			E_CORE_ERROR => 'Core Error',
			E_CORE_WARNING => 'Core Warning',
			E_COMPILE_ERROR => 'Compile Error',
			E_COMPILE_WARNING => 'Compile Warning',
			E_USER_ERROR => 'User Error',
			E_USER_WARNING => 'User Warning',
			E_USER_NOTICE => 'User Notice',
			E_STRICT => 'Strict',
			E_RECOVERABLE_ERROR => 'Recoverable Error',
			E_DEPRECATED => 'Deprecated',
			E_USER_DEPRECATED => 'User Deprecated'
		];

		$user_id = get_current_user_id();
		if ($user_id) {
			$user_info = get_userdata($user_id);
			$this->logged_in_user = array(
				'ID'       => $user_info->ID,
				'username' => $user_info->user_login,
				'email'    => $user_info->user_email,
				'roles'    => $user_info->roles
			);
		} elseif (defined('YL_CURRENT_USER') && isset(YL_CURRENT_USER["user"])) {
			$this->logged_in_user = array(
				'ID'       => YL_CURRENT_USER["user"]['id'],
				'username' => YL_CURRENT_USER["user"]['login'],
				'email'    => YL_CURRENT_USER["user"]['email'],
				'roles'    => YL_CURRENT_USER["user"]['role']
			);
		} else {
			$this->logged_in_user = array(
				'ID' => null,
				'username' => 'Guest',
				'email'    => null,
				'roles'    => null
			);
		}

		// Create an array with error details
		$error_details = [
			'time_zone'	=> date_default_timezone_get(),
			'timestamp' => (new DateTime())->format('c'),
			"user" => $this->logged_in_user,
			"request" => [
				"method" => $_SERVER['REQUEST_METHOD'],
				"route" => $_SERVER['REQUEST_URI'],
			],
			'type' => isset($error_types[$errno]) ? $error_types[$errno] : 'Unknown Error',
			'error_number' => $errno,
			'error_message' => $errstr,
			'file' => $errfile,
			'line' => $errline,
			'server' => $_SERVER
		];

		// Encode the log entry as JSON
		//$json_log = json_encode($error_details, JSON_PRETTY_PRINT);

		// Log the error
		$this->write_logs_hander($error_details, 'PHP');

		// Optionally, stop the script if the error is severe
		if ($errno === E_ERROR || $errno === E_USER_ERROR) {
			exit(1);
		}

		return true; // Indicate that the PHP internal error handler should not be called
	}

	// Custom exception handler
	public function json_exception_handler($exception)
	{

		$this->lastException = $exception;

		$user_id = get_current_user_id();
		if ($user_id) {
			$user_info = get_userdata($user_id);
			$this->logged_in_user = array(
				'ID'       => $user_info->ID,
				'username' => $user_info->user_login,
				'email'    => $user_info->user_email,
				'roles'    => $user_info->roles
			);
		} elseif (defined('YL_CURRENT_USER') && isset(YL_CURRENT_USER["user"])) {
			$this->logged_in_user = array(
				'ID'       => YL_CURRENT_USER["user"]['id'],
				'username' => YL_CURRENT_USER["user"]['login'],
				'email'    => YL_CURRENT_USER["user"]['email'],
				'roles'    => YL_CURRENT_USER["user"]['role']
			);
		} else {
			$this->logged_in_user = array(
				'ID' => null,
				'username' => 'Guest',
				'email'    => null,
				'roles'    => null
			);
		}
		// Create an array with exception details
		$exception_details = [
			'time_zone'	=> date_default_timezone_get(),
			'timestamp' => (new DateTime())->format('c'),
			"user" => $this->logged_in_user,
			"request" => [
				"method" => $_SERVER['REQUEST_METHOD'],
				"route" => $_SERVER['REQUEST_URI'],
			],
			'type' => get_class($exception),
			'error_number' => $exception->getCode(),
			'error_message' => $exception->getMessage(),
			'file' => $exception->getFile(),
			'line' => $exception->getLine(),
			'error_backtrace_string' => $exception->getTraceAsString(),
			'error_backtrace_stack' => $exception->getTrace(),
			'server' => $_SERVER
		];

		// Encode the log entry as JSON
		//$json_log = json_encode($exception_details, JSON_PRETTY_PRINT);

		// Log the exception
		$this->write_logs_hander($exception_details, 'PHP');

		// Optionally, stop the script
		//exit(1);
	}

	// Shutdown function to catch fatal errors
	public function json_shutdown_handler()
	{

		$last_error = error_get_last();

		$user_id = get_current_user_id();
		if ($user_id) {
			$user_info = get_userdata($user_id);
			$this->logged_in_user = array(
				'ID'       => $user_info->ID,
				'username' => $user_info->user_login,
				'email'    => $user_info->user_email,
				'roles'    => $user_info->roles
			);
		} elseif (defined('YL_CURRENT_USER') && isset(YL_CURRENT_USER["user"])) {
			$this->logged_in_user = array(
				'ID'       => YL_CURRENT_USER["user"]['id'],
				'username' => YL_CURRENT_USER["user"]['login'],
				'email'    => YL_CURRENT_USER["user"]['email'],
				'roles'    => YL_CURRENT_USER["user"]['role']
			);
		} else {
			$this->logged_in_user = array(
				'ID' => null,
				'username' => 'Guest',
				'email'    => null,
				'roles'    => null
			);
		}

		//if ($last_error && ($last_error['type'] & (E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR))) {
		if ($last_error != NULL) {
			$error_details = [
				'time_zone'	=> date_default_timezone_get(),
				'timestamp' => (new DateTime())->format('c'),
				"user" => $this->logged_in_user,
				"request" => [
					"method" => $_SERVER['REQUEST_METHOD'],
					"route" => $_SERVER['REQUEST_URI'],
				],
				'type' => 'Fatal Error',
				'error_number' => $last_error['type'],
				'error_message' => $last_error['message'],
				'file' => $last_error['file'],
				'line' => $last_error['line'],
				'server' => $_SERVER
			];

			if ($this->yuno_debug_display) {
				echo "<pre style=\"background-color:#ffcccb;\">" . print_r($error_details, true) . "</pre>";
			}

			// Encode the log entry as JSON
			//$json_log = json_encode($error_details, JSON_PRETTY_PRINT);

			// Log the exception
			$this->write_logs_hander($error_details, 'PHP');
		}

		if ($this->lastException != NULL) {
			$exception = $this->lastException;
			$exception_details = [
				'time_zone'	=> date_default_timezone_get(),
				'timestamp' => (new DateTime())->format('c'),
				"user" => $this->logged_in_user,
				"request" => [
					"method" => $_SERVER['REQUEST_METHOD'],
					"route" => $_SERVER['REQUEST_URI'],
				],
				'type' => get_class($exception),
				'error_number' => $exception->getCode(),
				'error_message' => $exception->getMessage(),
				'file' => $exception->getFile(),
				'line' => $exception->getLine(),
				'error_backtrace_string' => $exception->getTraceAsString(),
				'error_backtrace_stack' => $exception->getTrace(),
				'server' => $_SERVER
			];

			if ($this->yuno_debug_display) {
				echo "<pre style=\"background-color:#ffcccb;\">" . print_r($exception_details, true) . "</pre>";
			}
		}
	}
} //class PHP end
YUNO_PHP_Logger::get_instance();

#######################  PHP Logs End #######################

########################## SQL Logs Start ##########################

class YUNO_SQL_Logger extends YUNO_Custom_Logger
{
	//sql object
	private static $sql_instance;

	private function __construct()
	{
		parent::__construct();
	}

	// Implement a singleton pattern to ensure only one instance of the logger_v2 is used.
	public static function get_instance()
	{
		if (null === self::$sql_instance) {
			self::$sql_instance = new self();
			self::$sql_instance->trigger_sql_logs();
		} else {
			self::$sql_instance->trigger_sql_logs();
		}
	}

	//Trigger SQL hook for logs
	private function trigger_sql_logs()
	{
		if ($this->yuno_debug_log) {
			add_action('init', array(&$this, 'start_query_logging'));
			add_action('shutdown', array(&$this, 'log_queries_and_execution_time'));
		}
	}

	public function start_query_logging()
	{
		global $wpdb;

		// Ensure queries are saved
		if (!defined('SAVEQUERIES') || !SAVEQUERIES) {
			define('SAVEQUERIES', true);
		}

		// Start the timer
		$wpdb->timer_start();
	}

	public function log_queries_and_execution_time()
	{
		global $wpdb;

		// Stop the timer
		$execution_time = $wpdb->timer_stop();

		// Collect query data
		$query_detail = array();

		foreach ($wpdb->queries as $query) {
			$query_detail[] = [
				'query' => $query[0],
				'execution_time' => $query[1],
				'backtrace' => $query[2]
			];
		}

		// Add overall execution time
		$sql_log_data = [
			'timezone' => date_default_timezone_get(),
			'timestamp' => (new DateTime())->format('c'),
			'total_execution_time' => $execution_time,
			'query_detail' => $query_detail,
			'server' => $_SERVER
		];

		// Convert log data to JSON format
		//$json_log_entry = json_encode($sql_log_data, JSON_PRETTY_PRINT);

		$this->write_logs_hander($sql_log_data, 'SQL');
	}
} //class SQL end
//No need to log sql logs until we have a need for it!
//YUNO_SQL_Logger::get_instance();

########################## SQL Logs End ##########################
