Vue.component("yuno-course", {
	props: ["data", "options", "hasLearnMore", "seeAvailability"],
	template: `
		<div class="course">
			<div class="greyBG">
				<div class="categoryInfo">
					<div class="hasFlex">
						<small class="overline bold tertiaryBg ">{{ data.category.label }}</small>
						<div class="courseType">
							<b-tooltip label="Online: This is an online course. You can attend classes from anywhere"
								type="is-dark" multilined position="is-top">
								<span class="material-icons-outlined ">laptop</span>
							</b-tooltip>
							<span class="body4">Online</span>
						</div>
					</div>
					<div class="academy">
						<b-tooltip :label="data?.academy_name" type="is-dark" multilined position="is-top">
							<span class="smallestCaption text-capitalize">{{ data?.academy_name }}</span>
						</b-tooltip>
					</div>
				</div>
				<div class="courseHeader">
					<h2 class="largerTitle"><a :href="data.course_url" target="_blank">{{ data.course_title }}</a></h2>
					<div class="price">
						<div class="largerTitle">
							₹{{ separatedCost[0] }}
							<span class="smallestCaption " v-if="separatedCost[1]">+ {{ separatedCost[1] }}</span>
						</div>
						<div class="hasFlex">
							<span class="body1">{{'₹' + data.cost_per_hour }}</span>
							<span class="dark60 smallestCaption">per hour</span>
						</div>
					</div>
				</div>
				<ul class="features hasActiveBg">
					<li v-for="(feature, i) in data.stats.filter(f => f.slug === 'duration' || f.slug === 'liveClasses')"
						:key="i" :class="manageStatsClass(feature)">
						<b-tooltip :label="statsMessage(feature)" type="is-dark" multilined position="is-top">
							<template v-if="feature.slug === 'liveClasses'">
								<span class="material-icons-outlined">videocam</span>
							</template>
							<template v-else-if="feature.slug === 'duration'">
								<span class="material-icons-outlined">schedule</span>
							</template>
							{{ feature.label }}
						</b-tooltip>
					</li>
				</ul>
				<ul class="studentsInfo" v-if="false">
					<li v-if="data.active_students > 0">
						<span class="label">{{ data.active_students }}</span>
						<span class="value">active Students</span>
					</li>
					<li v-if="data.past_students > 0">
						<span class="label">{{ data.past_students }}</span>
						<span class="value">past Students</span>
					</li>
					<li>
						<span class="label">{{ data.hours }}</span>
						<span class="value">of live classes</span>
					</li>
				</ul>
				<ul class="studentsInfo" v-if="false">
					<li>
						<span class="label">{{ 'Total ₹' + data.total_cost }}</span>
					</li>
					<li>
						<span class="value">3-day money back guarantee</span>
					</li>
				</ul>
				<div class="row">
					<div class="col-12 col-md-8">
						<div class="instructorsWrapper" v-if="data.mapped_instructors.length !== 0">
							<ul class="instructorsList">
								<li v-for="(user, k) in data.mapped_instructors" :key="k" v-if="k <= 5">
									<b-tooltip :label="user.name" type="is-dark" multilined position="is-top">
										<template v-if="user.image !== ''">
											<figure class="instructorMapped">
												<img :src="user.image" :alt="user.name">
											</figure>
										</template>
										<template v-else>
											<span class="material-icons">account_circle</span>
										</template>
									</b-tooltip>
								</li>
							</ul>
							Choose from {{data.mapped_instructors.length}} <template
								v-if="data.mapped_instructors.length > 1">instructors</template> <template
								v-else>instructor</template>
						</div>
					</div>
					<div class="col-12 col-md-4 d-none d-sm-block"></div>
				</div>
			</div>
			<div class="noBg hasFlex">
				<ul class="features noborder hasCheckList hasFlexColumn ">
					<li v-for="(feature, i) in data.personalization" :key="i"
						:class="[feature.is_available ? 'active' : 'disabled']">
						<b-tooltip :label="personalizationMessage(feature)" type="is-dark" multilined position="is-top">
							<span class="material-icons">
								{{ feature.is_available ? 'done' : 'cancel'}}
							</span>
							{{ feature.label }}
							<div class="maxCount" v-if="feature.max">
								<span class="value">{{ feature.max }}</span>
								<span class="key">Max</span>
							</div>
						</b-tooltip>
					</li>
				</ul>
				<ul class="features availability d-none d-sm-flex" v-if="false">
					<li v-for="(time, i) in data.availability" :key="i" :class="[time.is_active ? 'active' : '']">
						<b-tooltip :label="availabilityMessage(time)" type="is-dark" multilined position="is-top">
							<span class="material-icons">
								{{ time.is_active ? 'check_circle' : 'cancel'}}
							</span>
							<span class="label">
								{{ time.label }} <small>{{ " - " + time.sub_title }}</small>
							</span>
						</b-tooltip>
					</li>
				</ul>
				<p class="body3 d-none d-sm-block" v-if="false"><a class="underline"
						:href="'/' + data.category.slug + '/resources/collections/'" target="_blank">{{
						data.total_resources_count }} study materials</a> and <a class="underline"
						:href="'/' + data.category.slug + '/practice-tests/'" target="_blank">practice tests included</a></p>
				<ul class="availability resources" v-if="false">
					<li v-for="(resource, i) in data.resources" :key="i">
						<span class="material-icons">
							<template v-if="resource.type === 'video'">
								play_circle
							</template>
							<template v-else-if="resource.type === 'article'">
								article
							</template>
							<template v-else-if="resource.type === 'ebook'">
								picture_as_pdf
							</template>
							<template v-else-if="resource.type === 'document'">
								picture_as_pdf
							</template>
							<template v-else-if="resource.type === 'quiz'">
								checklist
							</template>
						</span>
						<span class="label">{{ resource.label }}</span>
						<span class="value">{{ resource.count }}</span>
					</li>
				</ul>
				<div class="row" v-if="false">
					<div class="col-12">
						<div v-html="data.description" class="description"
							:class="[showMore.isActive && !showMore.toggle ? 'hasShowMore' : '']"
							v-if="countCharacters(data.description)">
						</div>
						<a href="#" class="showmore" :class="[showMore.toggle ? 'isActive' : '']" v-if="showMore.isActive"
							@click.prevent="showMoreToggle">
							<span class="anchorLabel">{{ showMore.label }}</span>
							<span class="material-icons">
								<template v-if="showMore.toggle">
									expand_less
								</template>
								<template v-else>
									expand_more
								</template>
							</span>
						</a>
					</div>
				</div>
				<div>
					<div v-if="false"></div>
					<div class="d-none d-sm-flex">
						<div class="ctaWrapper">
							<b-button v-if="seeAvailability === undefined || seeAvailability" tag="a" href="#" @click.prevent="toggleSidebar(data)"
								class="yunoPrimaryCTA wired button fat">
								See Availability
							</b-button>
							<b-button tag="a" v-if="hasLearnMore" :href="data.course_url" target="_blank" class="yunoPrimaryCTA wired button fat">
								Learn More
							</b-button>
							<b-button tag="a" :href="courseURL(data.demo_url, data.id, data.academy_id)" target="_blank"
								class="yunoSecondaryCTA button demoClass fat">
								Book A Demo Class
							</b-button>
						</div>
					</div>
				</div>
				<div class="ctaWrapper d-block d-sm-none w-100">
					<b-button tag="a" :href="courseURL(data.demo_url, data.id)" target="_blank"
						class="yunoSecondaryCTA button demoClass fat">
						Book A Demo Class
					</b-button>
					<b-button v-if="seeAvailability === undefined || seeAvailability" tag="a" href="#" @click.prevent="toggleSidebar(data)" class="yunoPrimaryCTA wired button fat">
						See Availability
					</b-button>
					<b-button tag="a" v-if="hasLearnMore" :href="data.course_url" target="_blank" class="yunoPrimaryCTA wired button fat">
						Learn More
					</b-button>
				</div>
			</div>
			<b-sidebar :fullheight="true" :fullwidth="false" :overlay="true" :right="true" v-model="data.isSidebar"
				:class="['yunoSidebar']">
				<div class="container" v-if="data.isSidebar">
					<span class="material-icons close" @click="data.isSidebar = !data.isSidebar">close</span>
					<template v-if="courseV2.loading">
						<section class="batchesWrapper scrollTo" id="couresBatches">
							<div class="course noPad">
								<p class="overline">{{ data.category.label }}</p>
								<h2 class="largerTitle">{{ data.course_title }}</h2>
							</div>
							<h3 class="sectionTitle">Availability</h3>
							<div class="batches">
								<article class="batchCard" v-for="i in loadingResult" :key="i">
									<div class="wrapper">
										<h4 class="batchdate"><b-skeleton active></b-skeleton></h4>
										<ul class="scheduleInfo">
											<li>
												<b-skeleton active width="50%"></b-skeleton>
											</li>
										</ul>
										<figure class="mappedInstructor">
											<div class="imgWrapper">
												<b-skeleton circle width="44px" height="44px"></b-skeleton>
											</div>
											<figcaption>
												<h3 class="insName"><b-skeleton active></b-skeleton></h3>
											</figcaption>
										</figure>
									</div>
								</article>
							</div>
						</section>
					</template>
					<template v-else>
						<div class="course noPad">
							<p class="overline">{{ data.category.label }}</p>
							<h2 class="largerTitle">{{ data.course_title }}</h2>
						</div>
					</template>
					<yuno-batches :data="courseBatches" v-if="data.isSidebar && courseV2.success" @showAll="onShowAll"
						@onEnroll="onEnroll"
						:options="{'batchesOptions': batchesOptions, 'filters': filters, 'instructorSidebar': false}">
					</yuno-batches>
				</div>
			</b-sidebar>
		</div>
      `,
	data() {
		return {
			loadingResult: 2,
			showMore: {
				isActive: false,
				toggle: false,
				label: "",
			},
			batchesOptions: {
				courseID: "",
				scheduleID: "",
				batch_count: "",
				totalCount: "",
				currentCount: "",
				limit: 20,
				offset: 0,
				title: "",
				isLoading: false,
			},
			filters: [
				{
					type: "groupMultiSelect",
					slug: "class_days_time",
					items: [
						{
							slug: "class_days",
							label: "Class Days",
							items: [
								{
									label: "Sun",
									filter: "class_days_time",
									slug: "sun",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Mon",
									filter: "class_days_time",
									slug: "mon",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Tue",
									filter: "class_days_time",
									slug: "tue",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Wed",
									filter: "class_days_time",
									slug: "wed",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Thu",
									filter: "class_days_time",
									slug: "thu",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Fri",
									filter: "class_days_time",
									slug: "fri",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Sat",
									filter: "class_days_time",
									slug: "sat",
									parentSlug: "class_days",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: false,
								},
							],
						},
						{
							slug: "class_time",
							label: "Class Time",
							items: [
								{
									label: "Morning",
									filter: "class_days_time",
									slug: "morning",
									parentSlug: "class_time",
									interval: "6 - 12",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Afternoon",
									filter: "class_days_time",
									slug: "afternoon",
									parentSlug: "class_time",
									interval: "12 - 4",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: false,
								},
								{
									label: "Evening",
									filter: "class_days_time",
									slug: "evening",
									parentSlug: "class_time",
									interval: "4 - 8",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
								{
									label: "Night",
									filter: "class_days_time",
									slug: "night",
									parentSlug: "class_time",
									interval: "8 - 11",
									isActive: true,
									isChecked: false,
									placeHolder: "Class Days & Time",
									displayStatus: true,
								},
							],
						},
					],
					is_highlighted: false,
					is_active: true,
					is_disabled: false,
					is_focus: false,
					selected: [],
					current: [],
					placeholder: "Class Days & Time",
				},
			],
			payload: {
				batch_days: ["sun", "mon", "tue", "wed", "thu", "fri", "sat"],
				batch_time: ["morning", "afternoon", "evening", "night"],
				personalisation: "one_to_many",
			},
		};
	},
	computed: {
		...Vuex.mapState([
			"user",
			"courseV2",
			"course",
			"courseBatches",
			"filterResult",
		]),
		separatedCost() {
			return this.data.total_cost.split(" + "); // Split at ' + ' and return an array
		},
	},
	async created() {
		this.emitEvents();
	},
	mounted() { },
	methods: {
		onShowAll() {
			this.batchesOptions.isLoading = true;
			this.courseBatches.limit = this.courseBatches.count;
			this.fetchBatches(false);
		},
		resetBatches(emptyCourse) {
			this.courseBatches.success = false;
			this.courseBatches.error = null;
			this.courseBatches.errorData = [];
			this.courseBatches.data = [];
			this.courseBatches.offset = 0;
			this.courseBatches.limit = 4;

			if (emptyCourse) {
				this.batchesOptions.courseID = "";
			}

			this.batchesOptions.offset = 0;
		},
		emitEvents() {
			Event.$on("onApplyFilter", (filter) => {
				if (this.batchesOptions.courseID === "") {
					return false;
				}

				const defaultTime = ["morning", "afternoon", "evening", "night"],
					defaultDays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];

				if (filter.current.length !== 0) {
					this.payload.batch_days =
						YUNOCommon.findObjectByKey(
							filter.current,
							"parentSlug",
							"class_days"
						) !== null
							? []
							: defaultDays;
					this.payload.batch_time =
						YUNOCommon.findObjectByKey(
							filter.current,
							"parentSlug",
							"class_time"
						) !== null
							? []
							: defaultTime;

					for (let i = 0; i < filter.current.length; i++) {
						const item = filter.current[i];

						if (item.parentSlug === "class_time") {
							this.payload.batch_time.push(item.slug);
						}

						if (item.parentSlug === "class_days") {
							this.payload.batch_days.push(item.slug);
						}
					}
				} else {
					this.payload.batch_days = defaultDays;
					this.payload.batch_time = defaultTime;
				}

				this.resetBatches(false);
				this.fetchBatches(true);
			});
		},
		onEnroll() {
			this.$props.data.isSidebar = false;
		},
		gotBatches(options) {
			this.courseBatches.loading = false;
			this.batchesOptions.isLoading = false;

			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				const data = options.response.data.data;
				this.batchesOptions.batch_count = options.response.data.batch_count;
			}
		},
		fetchBatches(isModuleLoading) {
			if (isModuleLoading) {
				this.courseBatches.loading = true;
			}

			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.availableBatches(
					this.batchesOptions.courseID,
					this.courseBatches.limit,
					this.courseBatches.offset
				),
				module: "gotData",
				store: "courseBatches",
				pushData: true,
				hasLoadmore: true,
				payload: this.payload,
				callback: true,
				callbackFunc: function (options) {
					return instance.gotBatches(options);
				},
			};

			this.$store.dispatch("postData", options);
		},
		gotCoursesDetail(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.data.code === 200
			) {
				const data = options.response.data.data;
				this.batchesOptions.scheduleID = data.course_schedule_id;
				this.batchesOptions.title = data.title;

				this.fetchBatches(true);
			}
		},
		fetchCoursesDetail() {
			const instance = this;
			const options = {
				apiURL: YUNOCommon.config.courseDetail(
					this.batchesOptions.courseID,
					isLoggedIn
				),
				module: "gotData",
				store: "courseV2",
				callback: true,
				callbackFunc: function (options) {
					return instance.gotCoursesDetail(options);
				},
			};

			this.$store.dispatch("fetchData", options);
		},
		toggleSidebar(data) {
			this.courseV2.data = [];
			this.courseV2.success = false;
			this.courseV2.error = null;
			this.courseBatches.data = [];
			this.courseBatches.success = false;
			this.courseBatches.error = null;
			this.courseBatches.offset = 0;

			this.batchesOptions.courseID = data.id;
			// this.filterResult.isSidebar = !this.filterResult.isSidebar;
			this.$props.data.isSidebar = !this.$props.data.isSidebar;
			this.fetchCoursesDetail();

			const defaultTime = ["morning", "afternoon", "evening", "night"],
				defaultDays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];

			this.payload.batch_days = defaultDays;
			this.payload.batch_time = defaultTime;
		},
		addQueryParamsUsingURLSearchParams(url, params) {
			let urlObj = new URL(url);
			// Iterate over the params object and append or overwrite each query parameter
			Object.keys(params).forEach((key) => {
				urlObj.searchParams.set(key, params[key]);
			});
			// Return the updated URL string
			return urlObj.toString();
		},
		courseURL(url, id, academyID) {
			const params = {
				course_id: id,
				...(academyID && { academy_id: academyID }),
			};

			return this.addQueryParamsUsingURLSearchParams(url, params);
		},
		manageStatsClass(feature) {
			return feature.slug !== "liveClasses" && feature.slug !== "duration"
				? "d-none d-sm-flex"
				: "";
		},
		availabilityMessage(time) {
			if (time.is_active) {
				return `${time.label} enrollments options`;
			} else {
				return "No enrollment option in this time slot";
			}
		},
		personalizationMessage(feature) {
			switch (feature.label) {
				case "1-to-1 online classes":
					return `You'll be the only learner, along with your instructor, in the live classes`;
				case "Group online classes":
					return `You'll be among a group of learners, no more than ${feature.max}, in the live classes`;
				default:
					return "";
			}
		},
		statsMessage(feature) {
			switch (feature.slug) {
				case "liveClasses":
					return `Live, online classes with your instructor in Zoom`;
				case "duration":
					return `Total duration of the course: ${feature.value} ${feature.value > 1 ? "weeks" : "week"
						} `;
				case "mock_tests":
					return "Simulated just like the actual exam";
				case "assignments":
					return `Your instructor will give you ${feature.value} homework assignments`;
				default:
					return "";
			}
		},
		showMoreToggle() {
			if (this.showMore.toggle) {
				this.showMore.toggle = false;
			} else {
				this.showMore.toggle = true;
			}
		},
		countCharacters(inputString) {
			const maxCharacters = 200; // Max character limit
			const currentLength = inputString.length; // Count the characters of the input string

			if (currentLength > maxCharacters) {
				this.showMore.isActive = true;
				this.showMore.label = this.showMore.toggle ? "Show less" : "Show more";
			} else {
				this.showMore.isActive = false;
				this.showMore.label = "";
			}

			return true;
		},
	},
});
