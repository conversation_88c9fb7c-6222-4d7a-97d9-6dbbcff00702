Vue.component("yuno-org-theme",{props:["data","options","resourceloaded"],template:"\n        <span></span>\n    ",data:()=>({}),watch:{resourceloaded(e){e&&this.resourceFetched()}},computed:{...Vuex.mapState(["orgAdmin"]),isNotYunoLearning:()=>!["yunolearning.com"].includes(window.location.hostname)},async created(){this.fetchOrgInfo(this.$props.options.orgID)},mounted(){},methods:{loadGoogleFont(e){var t=document.createElement("link");t.rel="stylesheet",t.href="https://fonts.googleapis.com/css2?family="+encodeURIComponent(e)+"&display=swap",document.head.appendChild(t)},resourceFetched(){const e=this.orgAdmin.data.theme;this.loadGoogleFont(e.font_family),document.documentElement.style.setProperty("--primary-color",e.primary_color),document.documentElement.style.setProperty("--body-bg-color",e.background_color),document.documentElement.style.setProperty("--font-family",e.font_family)},gotOrgInfo(e){const{code:t,data:a}=e.response?.data||{};200===t&&(void 0===this.$props.resourceloaded&&this.resourceFetched(),this.$emit("orgFetched"))},fetchOrgInfo(e){const t={apiURL:YUNOCommon.config.org("info",e),module:"gotData",store:"orgAdmin",callback:!0,callbackFunc:e=>this.gotOrgInfo(e)};console.log(this.isNotYunoLearning),this.isNotYunoLearning&&this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-powered-by",{props:["isOrg"],template:'\n        <footer class="leadFormFooter">\n            <div class="container">\n                <div class="columns is-mobile is-centered">\n                    <div class="poweredBy">\n                        <p>Powered By</p>\n                        <img\n                            :src="logo"\n                            alt="Yuno Learning"\n                            width="51"\n                            height="24"\n                        >\n                    </div>\n                </div>\n            </div>\n        </footer>\n    ',data(){return{logo:this.$store.state.themeURL+"/assets/images/yuno-logo-grey.svg"}},computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-batch",{props:["data","options"],template:'\n        <div class="batch">\n            <div class="row">\n                <div class="col-12">\n                    <div class="spaceBetween">\n                        <h2 class="price">\n                            <template v-if="data.price_included_gst !== 0 && data.price_included_gst !== \'0\' && data.price_included_gst !== \'Free\'">\n                                ₹{{ data.price_included_gst }}\n                            </template>\n                            <template v-else>\n                                Free\n                            </template>\n                        </h2>\n                        <b-button \n                            @click="updateBatch()"\n                            class="yunoPrimaryCTA wired">\n                            Change Batch\n                        </b-button>\n                    </div>\n                    <ul class="batchInfo">\n                        <li>Your enrollment starts on {{ data.enrollment_starts }}</li>\n                        <li>Class time: {{ data.class_start_time }}</li>\n                        <li class="grid">Class days: \n                            <ul class="classDays">\n                                <li \n                                    v-for="(day, d) in data.class_days" \n                                    :key="d"\n                                    :class="[day.is_available ? \'\' : \'disabled\']">\n                                    {{day.label}}\n                                </li>\n                            </ul>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{updateBatch(){Event.$emit("updateBatch")}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required",is:"Required",is_not:"",required_if:""}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-batch-page-v2",{template:'\n        <div :class="[isOrg ? \'orgEnabled\' : \'\']">\n            <yuno-page-loader v-if="isPageLoading"></yuno-page-loader>\n            <yuno-org-theme v-if="isOrg" :options="{\n                    \'orgID\': orgID\n                }" \n                :resourceloaded="moduleWithoutTab.data"\n            >\n            </yuno-org-theme>\n            <div v-show="isPageReady">\n                <yuno-loader></yuno-loader>\n                <yuno-header-revamp :hasnav="false"></yuno-header-revamp>\n                <yuno-org-header v-if="isOrg"></yuno-org-header>\n                <main id="yunoMain">\n                    <section class="container checkout">\n                        <div class="mainHeader" v-if="moduleWithoutTab.success">\n                            <div class="block">\n                                <h1 class="pageTitle">{{ pageHeader.title }}</h1>\n                            </div>\n                        </div>\n                        <template v-if="noBatchSelected">\n                            <yuno-empty-states :options="{\'state\': \'dataNotFound\'}"></yuno-empty-states>\n                        </template>\n                        <template v-if="moduleWithoutTab.loading || subform.loading">\n                            <div class="smallLoader"></div>\n                        </template>\n                        <template v-if="moduleWithoutTab.success">\n                            <template v-if="moduleWithoutTab.error">\n                                <p>{{ moduleWithoutTab.errorData }}</p>\n                            </template>\n                            <template v-else>\n                                <div class="row">\n                                    <div class="col-12 col-md-5">\n                                        <yuno-batch :data="moduleWithoutTab.data"></yuno-batch>\n                                        <yuno-user-list :data="moduleWithoutTab.data"></yuno-user-list>\n                                    </div>\n                                    <div class="col-12 col-md-1"></div>\n                                    <div class="col-12 col-md-6">\n                                        <yuno-course :data="moduleWithoutTab.data" :options="courseOptions" :hasReferralCode="hasReferralCode"></yuno-course>\n                                    </div>\n                                </div>\n                            </template>\n                        </template>\n                    </section>\n                    <b-modal \n                        :active.sync="paymentSuccess.modal" \n                        :width="500" \n                        :can-cancel="[\'x\']"\n                        :on-cancel="paymentSuccessClose"\n                        class="yunoModal loginSignupModal">\n                            <template v-if="paymentSuccess.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">Successfully Enrolled</h3>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="successMsg">\n                                        <h3>Hi {{userProfile.data.yuno_display_name}}</h3>\n                                        <template v-if="paymentSuccess.enrollmentType !== \'free\'">\n                                            <h4 class="subTitle">\n                                                Thanks for enrolling the <span v-html="paymentSuccess.batch.title"></span>\n                                            </h4>\n                                            <ul class="alertGrid">\n                                                <li class="alertGridLabel">Transaction ID:</li>\n                                                <li class="alertGridVal">{{paymentSuccess.rzp.razorpay_payment_id}}</li>\n                                                <li class="alertGridLabel">Order Amount:</li>\n                                                <li class="alertGridVal">₹{{paymentSuccess.batch.amount}}</li>\n                                            </ul>\n                                            <p v-if="false">{{paymentSuccess.message}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <h4 class="subTitle">\n                                                Thank you for enrolling. As soon as your instructor schedules a class, you\'ll be notified.\n                                            </h4>\n                                            <b-button \n                                                tag="a"\n                                                href="/learner"\n                                                @click="paymentSuccessClose"\n                                                class="yunoSecondaryCTA">\n                                                Upcoming Classes\n                                            </b-button>\n                                        </template>\n                                    </div>\n                                </div>\n                            </template>\n                    </b-modal>\n                    <b-modal \n                        :active.sync="updateBatches.modal" \n                        :width="1000"\n                        :can-cancel="[\'x\']"\n                        :on-cancel="updateBatchesClose"\n                        class="yunoModal loginSignupModal hasMinHeight">\n                            <template v-if="updateBatches.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">Availability</h3>\n                                </div>\n                                <div class="modalBody">\n                                    <yuno-batches :data="courseBatches" :options="{\'batchesOptions\': batchesOptions, \'filters\': filters}"></yuno-batches>\n                                </div>\n                            </template>\n                    </b-modal>\n                    <b-modal \n                        :active.sync="chooseCourse.modal" \n                        :width="500"\n                        :can-cancel="false"\n                        :on-cancel="chooseCourseClose"\n                        class="yunoModal loginSignupModal">\n                            <template v-if="chooseCourse.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">Choose Course</h3>\n                                </div>\n                                <div class="modalBody">\n                                    <p class="marginBtm16">Please select a course that you would like to enroll in</p>\n                                    <template v-for="(course, i) in chooseCourse.data">\n                                        <b-field :key="i">\n                                            <b-radio v-model="chooseCourse.selected"\n                                                :native-value="course.id"\n                                                @input="onCourseSelect(course)"\n                                            >\n                                                {{ course.title }}\n                                            </b-radio>\n                                        </b-field>\n                                    </template>\n                                </div>\n                            </template>\n                    </b-modal>\n                </main>\n                <yuno-powered-by v-if="isOrg" :isOrg="isOrg"></yuno-powered-by>\n                <yuno-footer :isnav="false"></yuno-footer>\n            </div>\n        </div>\n    ',data:()=>({isOrg:!1,orgID:"",chooseCourse:{modal:!1,data:[],selected:""},noBatchSelected:!1,courseOptions:{isLoading:!1},pageHeader:{title:"Checkout"},paymentSuccess:{modal:!1,batch:[],rzp:[],enrollmentType:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},updateBatches:{modal:!1},batchesOptions:{courseID:"",batch_count:"",limit:20,offset:0},payload:{batch_days:["sun","mon","tue","wed","thu","fri","sat"],batch_time:["morning","afternoon","evening","night"],personalisation:"one_to_many"},filters:[{type:"groupMultiSelect",slug:"class_days_time",items:[{slug:"class_days",label:"Class Days",items:[{label:"Sun",filter:"class_days_time",slug:"sun",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Mon",filter:"class_days_time",slug:"mon",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Tue",filter:"class_days_time",slug:"tue",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Wed",filter:"class_days_time",slug:"wed",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Thu",filter:"class_days_time",slug:"thu",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Fri",filter:"class_days_time",slug:"fri",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Sat",filter:"class_days_time",slug:"sat",parentSlug:"class_days",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!1}]},{slug:"class_time",label:"Class Time",items:[{label:"Morning",filter:"class_days_time",slug:"morning",parentSlug:"class_time",interval:"6 - 12",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Afternoon",filter:"class_days_time",slug:"afternoon",parentSlug:"class_time",interval:"12 - 4",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!1},{label:"Evening",filter:"class_days_time",slug:"evening",parentSlug:"class_time",interval:"4 - 8",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0},{label:"Night",filter:"class_days_time",slug:"night",parentSlug:"class_time",interval:"8 - 11",isActive:!0,isChecked:!1,placeHolder:"Class Days & Time",displayStatus:!0}]}],is_highlighted:!1,is_active:!0,is_disabled:!1,is_focus:!1,selected:[],current:[],placeholder:"Class Days & Time"}],hasReferralCode:!1,enrolmentPayload:{id:"",receipt:"",title:"",user_id:isLoggedIn,course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:"",referral_code_status:"not_applied"},isBatchLoading:!1}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","moduleWithoutTab","enrollmentV2","courseBatches","form","loader","referralCode","subform","module","orgAdmin"]),emptyStates:()=>({state:"notAuthorized"}),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){return!this.user.isLoggedin||this.userInfo.success}}},created(){this.manageUserQueryParams(),this.emitEvents()},destroyed(){Event.$off("removeCode"),Event.$off("initForm"),Event.$off("initPayment"),Event.$off("onApplyFilter"),Event.$off("onFilterChange"),Event.$off("onFilterItemSelect"),Event.$off("updateBatch"),Event.$off("checkLoggedInState")},mounted(){this.init()},methods:{manageState(){null!==localStorage.getItem("paymentState")&&this.initPayment(!1)},manageUserQueryParams(){const e=new URLSearchParams(window.location.search),t=e.get("user_id"),a=e.get("yuno_token"),s=e.get("org_id");s&&(this.orgID=s,this.isOrg=!0),t&&a&&(isLoggedIn=t,this.$store.state.config.yunoAPIToken="Bearer "+a,this.user.userID=t,this.user.isLoggedin=!0)},init(){const e=new URLSearchParams(window.location.search).get("course_id");e?(this.batchesOptions.courseID=e,this.fetchResult(this.batchesOptions.courseID,batchID)):this.fetchBatch()},onCourseSelect(){this.batchesOptions.courseID=this.chooseCourse.selected,this.chooseCourse.modal=!1,this.fetchResult(this.batchesOptions.courseID,batchID)},chooseCourseClose(){this.chooseCourse.data=[],this.chooseCourse.selected="",this.chooseCourse.modal=!1},resetResult(){this.moduleWithoutTab.success=!1,this.moduleWithoutTab.error=null,this.moduleWithoutTab.errorData=[],this.moduleWithoutTab.data=[]},resetBatches(e){this.courseBatches.success=!1,this.courseBatches.error=null,this.courseBatches.errorData=[],this.courseBatches.data=[],e&&(this.batchesOptions.courseID=""),this.batchesOptions.offset=0},manageClassDaysTime(e,t){},updateBatchesClose(){},updateBatch(){this.updateBatches.modal=!0,this.resetBatches(!1),this.fetchBatches()},setEnrolmentPayload(e){let t="",a="";t="0"===e.price_included_gst?"free":"direct_payment",a="0"===e.price_included_gst?"free":"regular",this.enrolmentPayload.course_id=e.course_id,this.enrolmentPayload.title=e.course_title,this.enrolmentPayload.batch_id=e.batch_id,this.enrolmentPayload.batch_name=e.batch_label,this.enrolmentPayload.batch_end_date=e.batch_end_date,this.enrolmentPayload.payment_mode=t,this.enrolmentPayload.amount=e.price_included_gst,this.enrolmentPayload.amount_due=e.price_included_gst,this.enrolmentPayload.duration=e.duration_weeks,this.enrolmentPayload.zoho_product_id=e.zoho_product_id;const s={id:e.course_id,title:e.course_title};let n={redirectURL:window.location.pathname+window.location.search+"?course_id="+s.id,batch:e,course:s,payload:this.enrolmentPayload,type:a};localStorage.setItem("paymentState",JSON.stringify(n))},gotBatch(e){if(200===e.response?.data?.code){const t=e.response.data.data,a=t.course_id;a.length>1?(this.chooseCourse.modal=!0,this.chooseCourse.data=t.courses):(this.batchesOptions.courseID=a[0],this.fetchResult(this.batchesOptions.courseID,batchID))}},fetchBatch(){if(this.isBatchLoading)return;this.isBatchLoading=!0;const e={apiURL:YUNOCommon.config.batchDetailAPI(batchID),module:"gotData",store:"subform",callback:!0,callbackFunc:e=>{this.isBatchLoading=!1,this.gotBatch(e)}};this.$store.dispatch("fetchData",e)},gotBatches(e){if(this.courseBatches.loading=!1,200===e.response?.data?.code){const t=e.response.data.data;this.courseBatches.data=t,this.batchesOptions.batch_count=e.response.data.batch_count}},fetchBatches(){this.courseBatches.loading=!0;const e={apiURL:YUNOCommon.config.availableBatches(this.batchesOptions.courseID,20,this.batchesOptions.offset),module:"gotData",store:"courseBatches",payload:this.payload,callback:!0,callbackFunc:e=>this.gotBatches(e)};this.$store.dispatch("postData",e)},gotReferralCode(e){if(200===e.response?.data?.code){const t=e.response.data.data;this.form.payload.referral_code=t.buyer_referral_code,this.initForm()}},fetchReferralCode(e){e&&(this.referralCode.data=[],this.referralCode.success=!1,this.referralCode.error=null);const t={apiURL:YUNOCommon.config.referrerID(0,isLoggedIn),module:"gotData",store:"referralCode",callback:!0,callbackFunc:e=>this.gotReferralCode(e)};this.$store.dispatch("fetchData",t)},setupForm(){this.form.payload={batch_id:this.moduleWithoutTab.data.batch_id,course_id:this.moduleWithoutTab.data.course_id,payment_method:"direct_payment",referral_code:"",user_id:isLoggedIn,action:this.hasReferralCode?"remove":"create"}},gotResult(e,t,a){if(200===e.response?.data?.code){const t=e.response.data.data;yunoCategory=t.category_slug,this.setupForm()}},fetchResult(e,t){const a={apiURL:YUNOCommon.config.checkout(e,t),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:a=>this.gotResult(a,e,t)};this.$store.dispatch("fetchData",a)},manageLoader(e){this.loader.isActive=e,this.loader.overlay=e},gotEnrollmentStatus(e){if(200===e.response?.data?.code){e.response.data.data.is_enrolled?(this.$buefy.dialog.alert({title:"Alert",message:"You have already enrolled a batch in this course",confirmText:"Ok"}),this.manageLoader(!1)):(this.manageLoader(!1),this.setEnrolmentPayload(this.moduleWithoutTab.data),this.processPayment())}},verifyEnrollmentStatus(e){const t={apiURL:YUNOCommon.config.courseEnrollmentStatus(this.batchesOptions.courseID,isLoggedIn),module:"gotData",store:"module",callback:!0,callbackFunc:e=>this.gotEnrollmentStatus(e)};this.$store.dispatch("fetchData",t)},onPaymentSuccess(e,t,a,s,n,o){this.paymentSuccess.rzp=!1!==a?a:[],this.paymentSuccess.batch=t,this.paymentSuccess.modal=!0,this.paymentSuccess.enrollmentType=o,s.is_enrolled=!0,n.is_enrolled=!0},updateQueryParams(e){const t=new URL(window.location);Object.keys(e).forEach((a=>t.searchParams.set(a,e[a]))),history.pushState({},"",t)},paymentSuccessClose(){this.paymentSuccess.rzp=[],this.paymentSuccess.batch=[],this.paymentSuccess.enrollmentType="",localStorage.removeItem("paymentState");const e=new URLSearchParams(window.location.search),t=e.get("user_id"),a=e.get("yuno_token");if(t&&a){this.updateQueryParams({is_success:!0});const t=e.get("redirect_url");window.location.href=t}else{const e="pending"===this.userInfo.data.is_signup_completed?"/sign-up/":"/learner";window.location.href=YUNOCommon.config.host()+e}},paymentCancelDone(e){if(201===e.response?.data?.code){e.response.data.data;this.paymentSuccessClose()}},onPaymentCancel(e,t){const a={apiURL:YUNOCommon.config.updatePaymentAPi(t.batch_id,t.course_id,isLoggedIn,e.receipt_id,e.zoho_deal_id),module:"gotData",store:"paymentDismiss",callback:!0,callbackFunc:e=>this.paymentCancelDone(e)};this.$store.dispatch("postData",a)},initEnrollmentDone(e,t,a,s,n){if(this.courseOptions.isLoading=!1,201===e.response?.data?.code){const o=e.response.data.data;if("free"!==n){o.modal={ondismiss:()=>this.onPaymentCancel(o,t)},o.handler=e=>{this.onPaymentSuccess(o,t,e,a,s,n),localStorage.removeItem("paymentState")};new Razorpay(o).open()}else this.onPaymentSuccess(o,t,!1,a,s,n),localStorage.removeItem("paymentState")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"}),localStorage.removeItem("paymentState")}},processEnrollment(e,t,a,s){this.courseOptions.isLoading=!0;const n={apiURL:YUNOCommon.config.enrollmentsV4("createLink",!1),module:"gotData",store:"enrollmentV2",payload:t,callback:!0,callbackFunc:n=>this.initEnrollmentDone(n,t,a,s,e)};this.$store.dispatch("postData",n)},paymentPayload(e){return{user_id:isLoggedIn,course_id:e.course.id,batch_id:e.batch.batch_id,payment_gateway:"direct_payment",payment_mode:"online",amount:e.payload.amount,amount_due:e.payload.amount_due,total_instalments:e.payload.total_instalments,instalment_amount:e.payload.total_instalments,self_notes:e.payload.self_notes,currency_code:e.payload.currency,counselor_id:e.payload.counselor_id,org_id:this.moduleWithoutTab.data.org_id,org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_parents:[],org_business_unit:"",referral_code_status:e.payload.referral_code_status,amount_paid:e.payload.amount,enrolled_by:isLoggedIn}},processPayment(){const e=localStorage.getItem("paymentState");if(null!==e){const t=JSON.parse(e);this.processEnrollment(t.type,this.paymentPayload(t),t.batch,t.course)}},initPayment(e){this.user.isLoggedin?(this.setEnrolmentPayload(this.moduleWithoutTab.data),this.processPayment()):(Event.$emit("initLoginModal",e),this.setEnrolmentPayload(this.moduleWithoutTab.data))},updateAmount(e){const t=localStorage.getItem("paymentState");if(null!==t){let a=JSON.parse(t);a.payload.amount=e?a.batch.price:this.form.data.discounted_price,a.payload.amount_due=e?a.batch.price:this.form.data.discounted_price,a.payload.referral_code_status=e?"not_applied":"applied",localStorage.setItem("paymentState",JSON.stringify(a))}},formPosted(e){if(this.manageLoader(!1),201===e.response?.data?.code){const t=e.response.data;this.form.data=t.data,"create"===this.form.payload.action?(this.hasReferralCode=!0,this.updateAmount(!1)):(this.hasReferralCode=!1,this.updateAmount(!0)),this.form.additional=t.message}else{e.response.data;this.hasReferralCode=!1,this.updateAmount(!0)}},initForm(){this.manageLoader(!0),this.form.data=[],this.form.error=null,this.form.success=!1,this.form.additional=[],this.form.errorData=[];const e={apiURL:YUNOCommon.config.referrerDetails(),module:"gotData",store:"form",payload:this.form.payload,callback:!0,callbackFunc:e=>this.formPosted(e)};this.$store.dispatch("postData",e)},emitEvents(){Event.$on("removeCode",(()=>{this.form.payload.action="remove",this.form.payload.referral_code="",this.initForm()})),Event.$on("initForm",(()=>{this.form.payload.action="create",this.initForm()})),Event.$on("initPayment",(e=>{this.initPayment(e)})),Event.$on("onApplyFilter",(e=>{const t=["morning","afternoon","evening","night"],a=["sun","mon","tue","wed","thu","fri","sat"];if(0!==e.current.length){this.payload.batch_days=null!==YUNOCommon.findObjectByKey(e.current,"parentSlug","class_days")?[]:a,this.payload.batch_time=null!==YUNOCommon.findObjectByKey(e.current,"parentSlug","class_time")?[]:t;for(let t=0;t<e.current.length;t++){const a=e.current[t];"class_time"===a.parentSlug&&this.payload.batch_time.push(a.slug),"class_days"===a.parentSlug&&this.payload.batch_days.push(a.slug)}}else this.payload.batch_days=a,this.payload.batch_time=t;this.resetBatches(!1),this.fetchBatches()})),Event.$on("onFilterChange",((e,t)=>{})),Event.$on("onFilterItemSelect",((e,t)=>{if("class_days_time"===t.slug)this.manageClassDaysTime(e,t)})),Event.$on("updateBatch",(()=>{this.updateBatch()})),Event.$on("checkLoggedInState",(e=>{e?Event.$on("gotUserRole",((e,t)=>{this.init()})):this.init()}))}}}),Vue.component("yuno-batches",{props:["data","options"],template:'\n        <section class="batchesWrapper">\n            <section class="filtersWrapper">\n                <ul>\n                    <template v-for="(filter, i) in options.filters">\n                        <template v-if="filter.type === \'dropdown\'">\n                            <li \n                                :key="i"\n                                v-if="filter.is_active"\n                                class="filter">\n                                <b-dropdown\n                                    :key="i"\n                                    v-model="filter.selected"\n                                    :multiple="false"\n                                    aria-role="list"\n                                    :disabled="filter.is_disabled"\n                                    @change="onFilterChange($event, filter)"\n                                    class="filterMenu"\n                                    :class="[filter.is_highlighted ? \'active\' : \'\']">\n                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">\n                                        <template v-if="filter.selected === \'\'">\n                                            <span>{{filter.placeholder}}</span>\n                                        </template>\n                                        <template v-else>\n                                            <span>{{filter.selected.label}}</span>\n                                        </template>\n                                        <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                    </button>\n                                    <template v-for="(item, j) in filter.items">\n                                        <b-dropdown-item \n                                            @click="onFilterItemSelect(item, filter)"\n                                            :class="[item.slug]"\n                                            :value="item"\n                                            :key="item.slug + \'-\' + j"\n                                            aria-role="listitem">\n                                            <span class="itemLabel">{{item.label}}</span>\n                                        </b-dropdown-item>\n                                    </template>\n                                </b-dropdown>\n                            </li>\n                        </template>\n                        <template v-if="filter.type === \'groupMultiSelect\'">\n                            <li \n                                :key="i"\n                                v-if="filter.is_active"\n                                class="filter">\n                                <b-dropdown\n                                    :key="i"\n                                    v-model="filter.selected"\n                                    ref="groupMultiSelect"\n                                    :multiple="true"\n                                    aria-role="list"\n                                    :disabled="filter.is_disabled"\n                                    @change="onFilterChange($event, filter)"\n                                    class="filterMenu"\n                                    :class="[filter.is_highlighted ? \'active\' : \'\']">\n                                    <button class="button is-primary filter" type="button" slot="trigger" slot-scope="{ active }">\n                                        <span>{{filter.placeholder}}</span>\n                                        <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                    </button>\n                                    <template v-for="(item, j) in filter.items">\n                                        <div class="filterSet" :class="[item.slug]">\n                                            <p :key="item.slug + \'-\' + j" class="listCaption">\n                                                {{item.label}}\n                                            </p>\n                                            <div class="innerWrapper">\n                                                <template v-for="(subItem, l) in item.items">\n                                                    <b-dropdown-item \n                                                        @click="onFilterItemSelect(subItem, filter)"\n                                                        :class="[subItem.slug]"\n                                                        :value="subItem.slug"\n                                                        :key="item.slug + \'-sub-\' + l"\n                                                        aria-role="listitem">\n                                                        <span class="itemLabel">{{subItem.label}}</span>\n                                                    </b-dropdown-item>\n                                                </template>\n                                            </div>\n                                        </div>\n                                    </template>\n                                    <div class="ctaWrapper">\n                                        <b-button class="noBorder" @click="clearMe(filter)">Clear All</b-button>\n                                        <b-button class="yunoSecondaryCTA" @click="applyFilter(filter)">Apply</b-button>\n                                    </div>\n                                </b-dropdown>\n                            </li>\n                        </template>\n                    </template>\n                </ul>\n            </section>\n            <template v-if="data.loading">\n                <div class="batches">\n                    <article class="batchCard" v-for="i in loadingResult" :key="i">\n                        <div class="wrapper">\n                            <h4 class="batchdate"><b-skeleton active></b-skeleton></h4>\n                            <ul class="scheduleInfo">\n                                <li>\n                                    <b-skeleton active width="50%"></b-skeleton>\n                                </li>\n                            </ul>\n                            <figure class="mappedInstructor">\n                                <div class="imgWrapper">\n                                    <b-skeleton circle width="44px" height="44px"></b-skeleton>\n                                </div>\n                                <figcaption>\n                                    <h3 class="insName"><b-skeleton active></b-skeleton></h3>\n                                </figcaption>\n                            </figure>\n                        </div>\n                    </article>\n                </div>\n            </template>\n            <template v-if="data.success">\n                <template v-if="data.error">\n                    <p>{{ data.errorData }}</p>\n                </template>\n                <template v-else>\n                    <p class="batchesCount">{{ options.batchesOptions.batch_count }}</p>\n                    <div class="batches">\n                        <article class="batchCard" v-for="(batch, i) in data.data" :key="i">\n                            <div class="wrapper">\n                                <h4 class="batchdate"><span class="material-icons">schedule</span>{{ batch.batch_start_time }}</h4>\n                                <ul class="days">\n                                    <li \n                                        v-for="(day, j) in batch.class_days" \n                                        :key="\'day-\' + j"\n                                        :class="[day.is_available ? \'isActive\' : \'\']">\n                                        {{ day.label }}\n                                    </li>\n                                </ul>\n                                <ul class="scheduleInfo">\n                                    <li>\n                                        <span class="material-icons-outlined">event</span>\n                                        <span class="itemCaption">{{ batch.starts_on }} {{ batch.ends_on }}</span>\n                                    </li>\n                                    <li class="hasGrid">\n                                        <div class="hasBG">\n                                            <span class="material-icons-outlined">groups</span>\n                                            <b-tooltip :label="tooltipLabel(batch)"\n                                                type="is-dark"\n                                                :multilined="true"\n                                                position="is-top">\n                                                Group classes\n                                            </b-tooltip>\n                                        </div>\n                                        <span class="itemCaption">\n                                            <b-tooltip label="Live classes will happen through the Zoom app. You can attend from your laptop or mobile device"\n                                                type="is-dark"\n                                                :multilined="true"\n                                                position="is-top">\n                                                | Online\n                                            </b-tooltip>\n                                        </span>\n                                    </li>\n                                </ul>\n                                <figure class="mappedInstructor">\n                                    <div class="imgWrapper">\n                                        <img width="44" height="44" :src="batch.instructor.image" :alt="batch.instructor.name">\n                                    </div>\n                                    <figcaption>\n                                        <h3 class="insName"><a :href="batch.instructor.profile_url" target="_blank">{{batch.instructor.name}}</a></h3>\n                                        <div class="insRating" v-if="batch.instructor.rating !== 0">\n                                            <span class="material-icons">star</span>\n                                            <span class="caption">{{ batch.instructor.rating }} of 5</span>\n                                        </div>\n                                    </figcaption>\n                                    <small class="studentCount">{{ batch.successful_enrollment }} successful students</small>\n                                </figure>\n                                <ul class="cardFooter">\n                                    <li class="price">\n                                        <template v-if="batch.price === 0 && batch.subscription.length === 0">\n                                            {{"Free"}}\n                                        </template>\n                                        <template v-else-if="batch.price === 0 && batch.subscription.length !== 0">\n                                            ₹{{batch.subscription[0].value}}\n                                        </template>\n                                        <template v-else>\n                                            ₹{{batch.price}}\n                                        </template>\n                                    </li>\n                                    <li class="ctaWrapper"><b-button tag="a" :href="refineBatchURL(batch.batch_url)" class="yunoSecondaryCTA">Change to this</b-button></li>\n                                </ul>\n                            </div>\n                        </article>\n                    </div>\n                </template>\n            </template>\n        </section>\n    ',data:()=>({loadingResult:3}),computed:{},async created(){},destroyed(){},mounted(){},methods:{refineBatchURL:e=>e+window.location.search,tooltipLabel:e=>"You'll be one of the maximum of "+e.max_seats+" students in the class",changeBatch(e){Event.$emit("changeBatch",e)},clearMe(e){e.selected=[],e.current=[]},applyFilter(e){this.$refs.groupMultiSelect[0].isActive=!1,Event.$emit("onApplyFilter",e)},onFilterChange(e,t){Event.$emit("onFilterChange",e,t)},onFilterItemSelect(e,t){if("class_days_time"===e.filter){YUNOCommon.findInArray(t.selected,e.slug)?t.current.push(e):YUNOCommon.removeObjInArr(t.current,"slug",e.slug)}Event.$emit("onFilterItemSelect",e,t)}}}),Vue.component("yuno-course",{props:["data","options","hasReferralCode"],template:'\n        <div class="course">\n            <h2 class="name">{{ data.course_title }}</h2>\n            <p class="description">{{ data.course_excerpt }}</p>\n            <h3 class="subtitle">Summary</h3>\n            <ul class="orderDetail">\n                <li>\n                    <span>Course fee</span>\n                    <span>₹{{ data.price_excluded_gst }}</span>\n                </li>\n                <li v-if="hasReferralCode && form.data.applied_discount_percentage !== 0">\n                    <span>Referral discount ({{ form.data.applied_discount_percentage + \'%\' }})</span>\n                    <span class="withGrid">-₹{{ form.data.applied_discount_amount }} <a href="#" @click.prevent="removeCode()"><span class="material-icons-outlined">highlight_off</span></a></span>\n                </li>\n                <li>\n                    <span>GST (18%)</span>\n                    <span>₹{{ data.gst }}</span>\n                </li>\n                <li class="primary">\n                    <span>Total</span>\n                    <span>\n                        <template v-if="hasReferralCode">\n                            ₹{{ form.data.discounted_price }}\n                        </template>\n                        <template v-else>\n                            ₹{{ data.price_included_gst }}\n                        </template>\n                    </span>\n                </li>\n            </ul>\n            <validation-observer \n                v-if="false"\n                tag="div" \n                class="formWrapper"\n                ref="formObserver" \n                v-slot="{ handleSubmit, invalid }">\n                <form id="yunoForm" @submit.prevent="handleSubmit(initForm)">\n                    <template v-if="referralCode.loading">\n                        <b-field>\n                            <template #label>\n                                Referral code\n                                <b-tooltip \n                                    type="is-dark" \n                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"\n                                    size="is-large"\n                                    multilined\n                                >\n                                    <span class="material-icons-outlined">info</span>\n                                </b-tooltip>\n                            </template>\n                            <b-skeleton active height="36px"></b-skeleton>\n                        </b-field>\n                    </template>\n                    <template v-if="referralCode.success">\n                        <b-field>\n                            <template #label>\n                                Referral code\n                                <b-tooltip \n                                    type="is-dark" \n                                    label="Referral code: If someone has referred you to Yuno Learning, please enter their code"\n                                    size="is-large"\n                                    multilined\n                                >\n                                    <span class="material-icons-outlined">info</span>\n                                </b-tooltip>\n                            </template>\n                            <validation-provider \n                                :rules="{required_if: \'\'}" v-slot="{ errors, classes }">\n                                <b-input @input="referralCodeInput" maxlength="6" :has-counter="false" v-model="form.payload.referral_code"></b-input>\n                            </validation-provider>\n                        </b-field>\n                        <p class="msg" v-if="form.success" :class="[form.error === null ? \'success\' : \'error\']">\n                            <template v-if="form.error === null">\n                                <span class="material-icons">check_circle</span> {{ form.additional }}\n                            </template>\n                            <template v-else>\n                                <span class="material-icons">cancel</span> {{ form.errorData }}\n                            </template>\n                        </p>\n                    </template>\n                </form>\n            </validation-observer>\n            <b-button \n                @click="initPayment($event)"\n                :loading="options.isLoading ? true : false"\n                :disabled="options.isLoading ? true : false" \n                class="yunoSecondaryCTA big">\n                Proceed to Pay <template v-if="hasReferralCode">₹{{ form.data.discounted_price }}</template><template v-else>₹{{ data.price_included_gst }}</template>\n            </b-button>\n            <template v-if="data.price_excluded_gst !== 0">\n                <h4 class="caption">Cancellation Policy</h4>\n                <p class="note">Full refund: get back 100% of your money within the first 3 days of starting your classes</p>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["form","referralCode"])},async created(){},destroyed(){},mounted(){},methods:{referralCodeInput(e){6===e.length&&(console.log("sss"),Event.$emit("initForm"))},removeCode(){Event.$emit("removeCode")},initForm(){Event.$emit("initForm")},initPayment(e){Event.$emit("initPayment",e)}}}),Vue.component("yuno-org-header",{template:'\n        <div class="yunoHeader">\n            <div class="container-fluid noOverflow">\n                <figure class="logo navbar-brand">\n                    <template v-if="orgAdmin.loading">\n                        <b-skeleton height="72px" width="106px"></b-skeleton>\n                    </template>\n                    <template v-else-if="orgAdmin.success && orgAdmin.error === null">\n                        <img :src="orgAdmin.data.logo_image_url" :alt="orgAdmin.data.organisation_name" :class="imageClass"  @load="calculateAspectRatio">\n                    </template>\n                </figure>\n            </div>\n        </div>\n    ',data:()=>({imageClass:""}),computed:{...Vuex.mapState(["orgAdmin"])},async created(){},destroyed(){},mounted(){},methods:{calculateAspectRatio(e){const t=e.target,a=t.naturalWidth/t.naturalHeight;this.imageClass=a>1?"landscape":a<1?"portrait":"square"}}}),Vue.component("yuno-user-list",{props:["data","options"],template:'\n        <div class="userList">\n            <figure class="mappedInstructor">\n                <div class="imgWrapper">\n                    <img \n                        width="44" \n                        height="44" \n                        :src="data.instructor.image" \n                        :alt="data.instructor.name">\n                </div>\n                <figcaption>\n                    <small class="studentCount">instructor</small>\n                    <h3 class="insName">{{ data.instructor.name }}</h3>\n                </figcaption>\n            </figure>\n            <figure class="mappedInstructor" v-if="false">\n                <div class="imgWrapper">\n                    <img \n                        width="44" \n                        height="44" \n                        src="https://lh3.googleusercontent.com/a-/AFdZucqaT_XL5RJsrCMlYly_jIvNtgymihOsjzVEOH2J=s96-c" \n                        alt="asd">\n                </div>\n                <figcaption>\n                    <small class="studentCount">counselor</small>\n                    <h3 class="insName">Reena Nagpal</h3>\n                </figcaption>\n            </figure>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{}});