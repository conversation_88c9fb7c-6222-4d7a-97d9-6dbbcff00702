#app .yunoTable {
  padding: 30px 0;
  text-align: center;
}

@media (min-width: 768px) {
  #app .yunoTable {
    padding: 100px 0;
  }
}

#app .yunoTable .alignC {
  text-align: center;
}

#app .yunoTable .fontonSurfaceVariant, #app .yunoTable .logoWrapper .zoomYunoWrapper .plus-sign, #app .yunoTable .tableWrapper table tbody .course-description {
  color: #534342;
}

#app .yunoTable .fontonSurface, #app .yunoTable .tableWrapper table tbody .course-title {
  color: #201A19;
}

#app .yunoTable .greyBorder, #app .yunoTable .logoWrapper .zoom-logo,
#app .yunoTable .logoWrapper .zoomYunoWrapper, #app .yunoTable .tableWrapper table th,
#app .yunoTable .tableWrapper table td {
  border: 1px solid #E6E6E6;
}

#app .yunoTable .fontSizeLarge, #app .yunoTable .tableWrapper table tbody .course-title {
  font-size: 16px;
}

#app .yunoTable .title {
  font-size: 32px;
  line-height: 40px;
  font-weight: 500;
  margin-bottom: 5px;
  text-align: center;
}

#app .yunoTable .logoWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  padding: 30px 0;
}

#app .yunoTable .logoWrapper .logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 106.61px;
  height: 52px;
  border-radius: 4px 0 0 0;
  padding: 10px;
}

#app .yunoTable .logoWrapper .zoomYunoWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  border-radius: 4px 0 0 0;
}

#app .yunoTable .logoWrapper .zoomYunoWrapper .plus-sign {
  font-size: 22px;
  font-weight: 500;
}

#app .yunoTable .tableWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: start;
}

#app .yunoTable .tableWrapper table {
  width: 100%;
  border-collapse: collapse;
}

#app .yunoTable .tableWrapper table th,
#app .yunoTable .tableWrapper table td {
  padding: 20px;
  font-family: Roboto, sans-serif;
  text-align: start;
}

#app .yunoTable .tableWrapper table th {
  font-size: 20px;
  font-weight: 500;
  width: 50%;
}

#app .yunoTable .tableWrapper table td[colspan="2"] {
  text-align: center;
}

#app .yunoTable .tableWrapper table thead th:nth-child(1) {
  background-color: #2c8cff0d;
}

#app .yunoTable .tableWrapper table thead th:nth-child(2) {
  background-color: #fff8f7;
}

#app .yunoTable .tableWrapper table tbody .course-title {
  font-family: Roboto;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.5px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

#app .yunoTable .tableWrapper table tbody .course-description {
  font-family: Roboto;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

#app .yunoTable .tableWrapper table tbody tr:nth-child(1) {
  background-color: #f5f5f5;
}
/*# sourceMappingURL=zoomYunoTable.css.map */