<?php

namespace V4;

/**
 * User model
 * 
 * @package V4
 * <AUTHOR>
 */

class UserModel extends Model
{
    public $yn_Administrator;
    public $yn_Keymaster;
    public $yn_Instructor;
    public $yn_Learner;
    public $yn_Counselor;
    public $yn_Yuno_Admin;
    public $yn_Content_Admin;
    public $yn_Yuno_Category_Admin;
    public $yn_Dashboard_Viewer;
    public $yn_Org_Admin;
    public $yn_Admin;

    /**
     * Constructor to initialize the UserModel
     * 
     * <AUTHOR>
     */
    function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');

        $this->yn_Administrator = "administrator";
        $this->yn_Keymaster = "bbp_keymaster";
        $this->yn_Instructor = "um_instructor";
        $this->yn_<PERSON><PERSON> = "SEO Manager";
        $this->yn_Counselor = "um_counselor";
        $this->yn_Yuno_Admin = "um_yuno-admin";
        $this->yn_Content_Admin = "um_content-admin";
        $this->yn_Yuno_Category_Admin = "um_yuno-category-admin";
        $this->yn_Dashboard_Viewer = "um_dashboard-viewer";
        $this->yn_Org_Admin = "um_org-admin";
        $this->yn_Admin = "um_admin";
    }

    /**
     * Get current logged in user information
     * 
     * @return array User information including ID, username, email and roles
     * <AUTHOR>
     */
    function getCurrentUser()
    {

        $userId = get_current_user_id();
        if ($userId) {
            $userInfo = get_userdata($userId);
            $loggedUser = array(
                'ID'       => $userInfo->ID,
                'username' => $userInfo->user_login,
                'email'    => $userInfo->user_email,
                'roles'    => $userInfo->roles
            );
        } elseif (defined('YL_CURRENT_USER') && isset(YL_CURRENT_USER["user"])) {
            $loggedUser = array(
                'ID'       => YL_CURRENT_USER["user"]['id'],
                'username' => YL_CURRENT_USER["user"]['login'],
                'email'    => YL_CURRENT_USER["user"]['email'],
                'roles'    => YL_CURRENT_USER["user"]['role']
            );
        } else {
            $loggedUser = array(
                'ID' => null,
                'username' => 'Guest',
                'email'    => null,
                'roles'    => null
            );
        }

        return $loggedUser;
    }
    /**
     * Get all available user roles
     * 
     * @return array Array of user roles with their display names
     * <AUTHOR>
     */
    function userRoles()
    {
        return [
            "administrator" => "Administrator",
            "bbp_keymaster" => "Keymaster",
            "um_instructor" => "Instructor",
            "SEO Manager" => "Learner",
            "um_counselor" => "Counselor",
            "um_yuno-admin" => "Yuno Admin",
            "um_content-admin" => "Content Admin",
            "um_yuno-category-admin" => "Yuno Category Admin",
            "um_dashboard-viewer" => "Dashboard Viewer",
            "um_org-admin" => "Org Admin",
            "um_admin" => "Admin"
        ];
    }

    /**
     * Validate if a role exists in the system
     * 
     * @param string|array $role Role or array of roles to validate
     * @return array|false Array containing role and title if valid, false otherwise
     * <AUTHOR>
     */
    function validRole($role)
    {
        $role = is_array($role) ?  $role : [$role];

        $roles = $this->userRoles();

        foreach ($role as $r) {
            foreach ($roles as $key => $value) {
                if (str_contains(strtolower($value), strtolower($r))) {
                    return [
                        'role' => $key,
                        'title' => $value
                    ];
                }
                if (str_contains(strtolower($key), strtolower($r))) {
                    return [
                        'role' => $key,
                        'title' => $value
                    ];
                }
            }
        }
        return false;
    }

    /**
     * Check if a specific role matches the given role
     * 
     * @param string|array $role Role or array of roles to check
     * @param string $toCheck Role to check against
     * @return array|false Array containing role and title if match found, false otherwise
     * <AUTHOR>
     */
    function checkRole($role, $toCheck)
    {
        $role = is_array($role) ?  $role : [$role];
        if ($role = $this->validRole($role)) {
            //if ($toCheck == $role['role']) {
            if (str_contains(strtolower($role['role']), strtolower($toCheck))) {
                return $role;
            }
        }

        return false;
    }

    /**
     * Get all users
     *
     * @return array The array of users
     */
    function getUsers() {}

    /**
     * Add a new user
     *
     * @param array $data The user data
     * @return bool True on success, false on failure
     */
    function addUser($data) {}

    /**
     * Get user information by user ID
     * 
     * @param string|array $query User ID or query parameters
     * @param array $filter Optional filter parameters
     * @return array|false User information if found, false otherwise
     * <AUTHOR>
     */
    function getUser($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $userDataResponse = $this->es->read('signedup', 'signedup-' . $query['id']);
        } else {
            return false;
        }

        if ($userDataResponse['status_code'] == 200) {

            $body = $userDataResponse['body']['_source']['data']['details'];

            $responseData = [
                "id" => $body['user_id'],
                "role" => [
                    $body['role']
                ],
                "full_name" => $body['user']['name'] ?? '',
                "image_url" => $body['user']['image'] ?? '',
                "phone" => $body['user']['phone'] ?? '',
                "email" => $body['user']['email'] ?? '',
                "address" => [
                    "type" => $body['location']['address_type'] ?? '',
                    "address_1" => $body['location']['flat_house_number'] ?? '',
                    "address_2" => $body['location']['street'] ?? '',
                    "landmark" => $body['location']['landmark'] ?? '',
                    "city" => $body['location']['city'] ?? '',
                    "state" => $body['location']['state'] ?? '',
                    "country" => [
                        "id" => 0,
                        "name" => $body['location']['country'] ?? '',
                        "code" => ""
                    ],
                    "pin_zip" => $body['location']['pin_code'] ?? ''
                ],
                "app_language" => [
                    "name_in_english" => $body['region']['language']['name_in_english'] ?? '',
                    "native_lang_name" => $body['region']['language']['native_lang_name'] ?? '',
                    "code" => $body['region']['language']['code'] ?? ''
                ],
                "app_timezone" => [
                    "name" => $body['region']['timezone'] ?? '',
                    'utc'  => (function ($timezone) {
                        $utc = '';
                        try {
                            if (trim($timezone)) {
                                $tz = new \DateTimeZone($timezone);
                                $datetime = new \DateTime("now", $tz);
                                $utc = $datetime->format('P');
                            }
                        } catch (\Exception $e) {
                            // Log the exception or handle it as needed
                            $utc = 'Invalid UTC';
                        }
                        return $utc;
                    })($body['region']['timezone'] ?? ''),
                ],
                "app_currency" => [
                    "code" => $body['region']['currency']['code'] ?? '',
                    "name" => $body['region']['currency']['name'] ?? '',
                    "symbol" => $body['region']['currency']['symbol'] ?? '',
                    "symbol_html" => $body['region']['currency']['symbol_html'] ?? ''
                ],
                "app_locale" => [
                    "id" => $body['region']['country']['id'] ?? '',
                    "name" => $body['region']['country']['name'] ?? '',
                    "code" => $body['region']['country']['code'] ?? '',
                ],
                "terms_of_service" => $body['basic_details']['privacy_policy_terms_of_service'] ?? '',
                "created_time" => $this->dt->convertToActiveDT($body['basic_details']['registration_date']) ?? '',
                "last_login" => $this->dt->convertToActiveDT($body['basic_details']['last_login_time']) ?? '',
                "whatsapp_optin" => $body['basic_details']['yuno_user_whatsapp_check'] ?? '',
                "in_crm" => [
                    "platform" => "",
                    "id" => ''
                ]
            ];

            return  $this->schema->validate($responseData, 'User', $filter);
        }

        return false;
    }

    /**
     * Update user information by user ID
     *
     * @param string $userID The user ID
     * @return bool True on success, false on failure
     */
    function updUser($userID) {}

    /**
     * Delete user by user ID
     *
     * @param string $userID The user ID
     * @return bool True on success, false on failure
     */
    function delUser($userID) {}

    /**
     * Update user region details
     * 
     * @param string $userID User ID to update
     * @param array $data Region data to update
     * @return bool True on success, false on failure
     * <AUTHOR>
     */
    function updRegion($userID, $data)
    {
        update_user_meta($userID, 'yuno_user_info_country_id', $data['country']['id']);
        update_user_meta($userID, 'yuno_user_info_country_name', $data['country']['name']);
        update_user_meta($userID, 'yuno_user_info_country_code', $data['country']['code']);
        update_user_meta($userID, 'yuno_user_info_timezone', $data['timezone']['name']);
        update_user_meta($userID, 'yuno_user_info_currency_code', $data['currency']['code']);
        update_user_meta($userID, 'yuno_user_info_currency_name', $data['currency']['name']);
        update_user_meta($userID, 'yuno_user_info_currency_symbol', $data['currency']['symbol']);
        update_user_meta($userID, 'yuno_user_info_currency_symbol_html', $data['currency']['symbol_html']);
        update_user_meta($userID, 'yuno_user_info_language_name', $data['language']['name_in_english']);
        update_user_meta($userID, 'yuno_user_info_language_native', $data['language']['native_lang_name']);
        update_user_meta($userID, 'yuno_user_info_language_code', $data['language']['code']);

        $region = [
            "data" => [
                "details" => [
                    "region" => [
                        "country" => [
                            "id" => (int) $data['country']['id'],
                            "name" => (string) $data['country']['name'],
                            "code" => (string) $data['country']['code']
                        ],
                        "timezone" => (string) $data['timezone']['name'],
                        "currency" => [
                            "code" => (string) $data['currency']['code'],
                            "name" => (string) $data['currency']['name'],
                            "symbol" => (string) $data['currency']['symbol'],
                            "symbol_html" => (string) $data['currency']['symbol_html']
                        ],
                        "language" => [
                            "name_in_english" => (string) $data['language']['name_in_english'],
                            "native_lang_name" => (string) $data['language']['native_lang_name'],
                            "code" => (string) $data['language']['code']
                        ]
                    ]
                ]
            ]
        ];

        $userDataResponse = $this->es->update('signedup', 'signedup-' . $userID, $region);

        if ($userDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    /**
     * Get user role based on user ID
     * 
     * @param int $userId User ID to check
     * @return string|false User role if found, false otherwise
     * <AUTHOR>
     */
    public function getUserRole($userId)
    {
        // Get user data
        $userDataResponse = $this->getUser($userId);
        if ($userDataResponse === false) {
            return false;
        }

        // Determine role based on user data
        if ($this->checkRole($userDataResponse['role'], $this->yn_Yuno_Admin) !== false) {
            return 'yuno-admin';
        } elseif ($this->checkRole($userDataResponse['role'], $this->yn_Counselor) !== false) {
            return 'counselor';
        } elseif ($this->checkRole($userDataResponse['role'], $this->yn_Content_Admin) !== false) {
            return 'content-admin';
        } elseif ($this->checkRole($userDataResponse['role'], $this->yn_Org_Admin) !== false) {
            return 'org-admin';
        } elseif ($this->checkRole($userDataResponse['role'], $this->yn_Instructor) !== false) {
            return 'instructor';
        } elseif ($this->checkRole($userDataResponse['role'], $this->yn_Learner) !== false) {
            return 'learner';
        }

        return 'unknown';
    }

    public function getDetailsFromOrganization($userId, $orgId)
    {
        if (!isset($userId)) {
            return false;
        }

        $userDataResponse = $this->es->read('signedup', 'signedup-' . $userId);

        if ($userDataResponse['status_code'] != 200) {
            return false;
        }

        $detailsFromOrg = $userDataResponse['body']['_source']['data']['details']['details_from_org'] ?? [];

        foreach ($detailsFromOrg as $org) {
            if ((int)$org['org_id'] === (int)$orgId) {
                return [
                    'user_id'       => $org['user_id'] ?? 0,
                    'phone'         => $org['phone'] ?? '',
                    'crm_id'        => $org['crm_id'] ?? '',
                    'org_id'        => $org['org_id'] ?? 0,
                    'name'          => $org['name'] ?? '',
                    'created_at'    => $org['created_at'] ?? '',
                    'cohort'        => $org['cohort'] ?? [],
                    'programs'      => $org['programs'] ?? [],
                    'business_unit' => $org['business_unit'] ?? [],
                    'email'         => $org['email'] ?? '',
                    'parents'       => $org['parents'] ?? [],
                ];
            }
        }

        return []; // Or return null, depending on what you expect when no match is found
    }
}
