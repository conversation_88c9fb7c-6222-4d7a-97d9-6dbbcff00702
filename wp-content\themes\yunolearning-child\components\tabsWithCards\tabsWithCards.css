.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete::before {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .tabsWithCards, #app .tabsWithCards .yunoTabsV2 .statsCard p, #app .tabsWithCards .yunoTabsV2 .aboutUser h2, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button.is-primary, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet .listCaption, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item, #app .tabsWithCards .yunoTabsV2 .clearFilters, #app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .tabsWithCards .smallerBody, #app .tabsWithCards .yunoTabsV2 .statsCard h3, #app .tabsWithCards .yunoTabsV2 .aboutUser p, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemLabel, #app .tabsWithCards .note, #app .tabsWithCards .grid .checkedList li .textWithTooltip small {
  color: rgba(0, 0, 0, 0.6);
}

#app .tabsWithCards {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .tabsWithCards {
    padding: 60px 0 0;
  }
}

#app .tabsWithCards .alignC {
  text-align: center;
}

#app .tabsWithCards.hideTabsEnabled nav.tabs {
  display: none;
}

#app .tabsWithCards .largestTitle {
  font-size: 32px;
  line-height: 40px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .tabsWithCards .largerTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}

#app .tabsWithCards .largeTitle {
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .tabsWithCards .largeTitle {
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    margin-bottom: 0;
  }
}

#app .tabsWithCards .smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .tabsWithCards .smallerBody {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .tabsWithCards h2.largestTitle {
  text-align: center;
}

#app .tabsWithCards .yunoTabsV2 {
  margin-top: 24px;
}

#app .tabsWithCards .yunoTabsV2 .switchContainer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 10px;
}

#app .tabsWithCards .yunoTabsV2 .switchContainer .switchLabel {
  font-size: 14px;
}

#app .tabsWithCards .yunoTabsV2 .collapse:not(.show) {
  display: block;
}

#app .tabsWithCards .yunoTabsV2 .modal-background {
  background-color: rgba(10, 10, 10, 0.5);
}

#app .tabsWithCards .yunoTabsV2.noTopGap {
  margin-top: 0;
}

#app .tabsWithCards .yunoTabsV2 .tab-item {
  min-height: 200px;
}

#app .tabsWithCards .yunoTabsV2.stickyEnabled > .tabs {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 11;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2.stickyEnabled > .tabs {
    top: 75px;
    z-index: 9;
  }
}

#app .tabsWithCards .yunoTabsV2 .tabs {
  background: #FFF;
  position: relative;
}

#app .tabsWithCards .yunoTabsV2 .tabs ul {
  border-color: rgba(0, 0, 0, 0.08);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .tabsWithCards .yunoTabsV2 .tabs ul li {
  font-size: 14px;
}

#app .tabsWithCards .yunoTabsV2 .tabs ul li a {
  color: rgba(0, 0, 0, 0.38);
  padding: 20px 20px;
  display: block;
  border-bottom: 1px solid transparent;
}

#app .tabsWithCards .yunoTabsV2 .tabs ul li a:hover {
  text-decoration: none;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .tabs ul li a {
    padding: 15px 20px;
  }
}

#app .tabsWithCards .yunoTabsV2 .tabs ul li.is-active a {
  color: #A81E22;
  border-color: #a81e22;
  font-weight: 400;
  background: transparent;
}

#app .tabsWithCards .yunoTabsV2.sizeMedium .tabs ul li {
  font-size: 12px;
}

#app .tabsWithCards .yunoTabsV2.sizeMedium .tabs ul li a {
  padding: 10px 20px;
}

#app .tabsWithCards .yunoTabsV2 .tab-content {
  padding: 15px 0;
}

#app .tabsWithCards .yunoTabsV2 .statsWrapper {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .tabsWithCards .yunoTabsV2 .statsCard {
  background: #FFF;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
}

#app .tabsWithCards .yunoTabsV2 .statsCard p {
  font-size: 28px;
  text-align: center;
}

#app .tabsWithCards .yunoTabsV2 .statsCard h3 {
  font-size: 10px;
  text-align: center;
  text-transform: uppercase;
  font-weight: 500;
}

#app .tabsWithCards .yunoTabsV2 .aboutUser {
  padding: 30px 0;
}

#app .tabsWithCards .yunoTabsV2 .aboutUser h2 {
  font-size: 24px;
  line-height: 28px;
  margin-bottom: 15px;
}

#app .tabsWithCards .yunoTabsV2 .aboutUser p {
  font-size: 14px;
  line-height: 20px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper {
  padding: 0 15px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px 15px;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper {
    padding: 0 0 5px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper.loading {
  margin-top: 24px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper.loading .b-skeleton {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100px;
          flex: 0 0 100px;
  padding: 0 15px 15px;
  margin: 0;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .fa-filter {
  font-size: 24px;
  padding-right: 15px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .button.is-primary {
  background-color: #FFF;
  color: #000;
  font-size: 14px;
  border-color: rgba(0, 0, 0, 0.12);
  font-size: 14px;
  border-radius: 4px;
  padding: 8px 15px 9px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .button.is-primary:active, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button.is-primary:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon {
  margin: 0 0 0 5px;
  position: relative;
  top: 1px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c5";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c7";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 15px;
  margin-left: 0;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.active button.filter {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.active button.filter .icon {
  color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.is-active button {
  border-color: rgba(0, 0, 0, 0.87);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-menu {
  padding: 0;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content {
  padding: 0;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle) {
  padding: 6px 15px 6px 40px;
  position: relative;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle):before {
  content: "unCheck";
  position: absolute;
  left: 15px;
  top: 10px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .dropdown-content a:not(.dropdownTitle).is-active:before {
  content: "checked";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 15px 0;
  padding: 15px 0;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time) .ctaWrapper .yunoSecondaryCTA {
  border-color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content {
    width: 440px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before {
  font-family: "Material Icons" !important;
  position: static;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item.night:before {
  content: "\e51c";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 0;
  padding-bottom: 5px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content {
    width: 440px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:hover {
  background: none;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item:before {
  display: none;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .dropdown-content .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_days .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin: 15px 0 0;
  padding-bottom: 5px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.class_days_time .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.class_days_time .ctaWrapper .button {
  border-color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 10px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .wrapper {
    width: 440px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:hover {
  background: none;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:before {
  display: none;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper {
    width: 440px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before {
  font-family: "Material Icons" !important;
  position: static;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.morning:before {
  content: "\e1c6";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.afternoon:before {
  content: "\e518";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.evening:before {
  content: "\e1c6";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item.night:before {
  content: "\e51c";
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-trigger {
  width: 100%;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
    margin: 10px 0 0 15px;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu button {
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu button {
    width: auto;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu button > span {
  text-transform: capitalize;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-content a {
  color: rgba(0, 0, 0, 0.5);
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-content a.is-active {
  background: none;
  color: black;
}

#app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-content a:active, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .dropdown-content a:focus {
  background: none;
  outline: none;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-bottom: 0;
  margin-top: 0;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags {
  margin: 10px 10px 0 0;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags .tag {
  margin-bottom: 0;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-right: 0;
  height: 28px;
  background: rgba(0, 0, 0, 0.02);
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete {
  border-left: 0;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete::after {
  display: none;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete::before {
  content: "\e5cd";
  left: 0;
  top: 6px;
  -webkit-transform: none;
          transform: none;
  background: none;
  font-size: 14px;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .tags .is-delete:hover {
  background: rgba(0, 0, 0, 0.02);
  color: #A81E22;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 10px 10px 0 0;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags:not(.placeholder) {
  margin: 0;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags:not(.placeholder) .tag {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  text-transform: capitalize;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags:not(.placeholder) .is-delete {
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags:not(.placeholder):last-child .is-delete {
  border-right: 1px;
  border-top-right-radius: 290486px;
  border-bottom-right-radius: 290486px;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags.placeholder {
  margin: 0;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags.placeholder .tag {
  padding-right: 0;
}

#app .tabsWithCards .yunoTabsV2 .clearFilters .multiChips .tags.placeholder .is-delete {
  display: none;
}

#app .tabsWithCards .note {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 30px;
  text-align: center;
}

#app .tabsWithCards .note a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .tabsWithCards .note .material-icons-outlined {
  font-size: 18px;
  margin-left: 5px;
}

@media (min-width: 768px) {
  #app .tabsWithCards .note {
    width: 70%;
    margin: 0 auto;
    margin-top: 30px;
  }
}

#app .tabsWithCards .grid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 15px -15px 0;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}

#app .tabsWithCards .grid.alignCenter {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .tabsWithCards .grid .item {
  padding: 0 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .tabsWithCards .grid .item {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
    margin-bottom: 0;
  }
}

#app .tabsWithCards .grid .item .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding-top: 25px;
  padding-bottom: 25px;
  height: 100%;
}

#app .tabsWithCards .grid .item .wrapper .cta .yunoPrimaryCTA {
  width: 80%;
}

#app .tabsWithCards .grid .item .wrapper .priceSummary {
  padding: 10px 10px 10px 10px;
  background: #eff5fb;
  color: #296fa8;
}

#app .tabsWithCards .grid .item .wrapper .dropdownWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}

#app .tabsWithCards .grid .item .largerTitle {
  padding: 20px;
  border-radius: 4px 4px 0 0;
}

#app .tabsWithCards .grid .item .largerTitle small {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  display: block;
  margin-top: 5px;
}

#app .tabsWithCards .grid .item .price {
  padding: 0 20px 20px;
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .tabsWithCards .grid .item .price span {
  font-size: 14px;
}

#app .tabsWithCards .grid .checkedList {
  margin: 20px;
}

#app .tabsWithCards .grid .checkedList li {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .tabsWithCards .grid .checkedList li::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: inline-block;
  margin-right: 15px;
  position: relative;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 5px;
  color: #4a4a4a;
  white-space: nowrap;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip small {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip.is-primary .tooltip-content {
  background: #201A19;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip.is-right .tooltip-content::before {
  border-right: 5px solid #201A19;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-content {
  width: 200px;
  font-size: 12px;
  white-space: normal;
  text-align: center;
  padding: 0.3rem 0.3rem;
}

@media (min-width: 768px) {
  #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-content {
    font-size: 14px;
    width: 300px;
  }
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-content::before {
  border-top-color: #201A19;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger {
  line-height: 0;
}

#app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .material-icons, #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-down:after, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .mdi.mdi-menu-down:after, #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .yunoTabsV2 .filtersWrapper .button .icon .mdi.mdi-menu-up:after, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .button .icon .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .mdi.mdi-menu-up:after, #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .dropdown-item:before, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu.multiSelect:not(.class_days_time).class_time .dropdown-content .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .dropdown-item:before, #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .dropdown-item:before, #app .tabsWithCards .yunoTabsV2 .filtersWrapper .filterMenu .filterSet.class_time .wrapper .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .dropdown-item:before, #app .tabsWithCards .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .yunoTabsV2 .clearFilters .tags .is-delete::before, #app .tabsWithCards .yunoTabsV2 .clearFilters .tags .grid .checkedList li .textWithTooltip .b-tooltip .tooltip-trigger .is-delete::before {
  font-size: 23px !important;
  color: #201a1947 !important;
  padding-top: 4px !important;
}

#app .tabsWithCards .grid .checkedList li:last-child {
  margin-bottom: 0;
}
/*# sourceMappingURL=tabsWithCards.css.map */