.slide-in-bottom {
  -webkit-animation: slide-in-bottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-bottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/* ----------------------------------------------
 * Generated by Animista on 2022-11-24 17:23:17
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation slide-in-bottom
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-bottom {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-bottom {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

.dark87, #app .yunoTabs .tabsContent .sectionTitle, #app .yunoSnackbar .snackbar, #app .yunoSnackbar .snackbar .closeSnackbar, #app .yunoSnackbar .mappedInstructor figcaption {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .yunoSnackbar .noticeSmall, #app .yunoSnackbar .mappedInstructor .studentCount, #app .yunoSnackbar .starLabel li {
  color: rgba(0, 0, 0, 0.6);
}

#app .classCard .classID {
  display: none;
}

#app .yunoTabs .tabsContent .sectionTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  #app .yunoTabs .tabsContent .sectionTitle {
    font-size: 24px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 24px;
  }
}

#app .yunoTabs .yunoTabNav {
  top: 0;
}

@media (max-width: 767px) {
  #app .yunoTabs.instructor .yunoTabNav {
    padding-left: 18px;
  }
}

#app .yunoSnackbar .snackbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding: 15px;
  background: #f2f2f2;
  max-width: 500px;
  overflow-y: auto;
}

#app .yunoSnackbar .snackbar .closeSnackbar {
  position: absolute;
  right: 10px;
  top: 10px;
}

#app .yunoSnackbar .snackbar .closeSnackbar .material-icons-outlined {
  font-size: 18px;
}

#app .yunoSnackbar .titleLarge {
  font-size: 24px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 5px;
}

#app .yunoSnackbar .titleSmall {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .subtitleSmall {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .yunoSnackbar .noticeSmall {
  margin-bottom: 10px;
}

#app .yunoSnackbar .noticeTitle {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1.5px;
}

#app .yunoSnackbar .mappedInstructor {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .yunoSnackbar .mappedInstructor.gapBtm15 {
  margin-bottom: 15px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 10px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 0;
  background-color: #FFF;
}

#app .yunoSnackbar .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 60px);
          flex: 0 0 calc(100% - 60px);
}

#app .yunoSnackbar .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

#app .yunoSnackbar .ctaWrapper {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoSnackbar .ctaWrapper .button {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper {
  width: 100%;
  position: relative;
  background: #fafafa;
  padding: 10px;
}

#app .yunoSnackbar .formWrapper.noBG {
  background: none;
  padding: 0;
}

#app .yunoSnackbar .formWrapper .innerWrapper {
  background-color: #fafafa;
  padding: 10px;
  margin-bottom: 15px;
}

#app .yunoSnackbar .formWrapper .innerWrapper .groupElement {
  margin: 0;
}

#app .yunoSnackbar .formWrapper.gapTop15 {
  padding-top: 15px;
}

#app .yunoSnackbar .formWrapper .vue-star-rating {
  padding-left: 15px;
}

#app .yunoSnackbar .formWrapper .field.noGap {
  margin: 0;
}

#app .yunoSnackbar .formWrapper .alert {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoSnackbar .formWrapper .alert .material-icons-outlined {
  font-size: 40px;
  margin-bottom: 10px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper {
  -webkit-box-pack: right;
      -ms-flex-pack: right;
          justify-content: right;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading {
  position: absolute;
  right: 5px;
  bottom: 54px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading .button {
  border: 0;
}

#app .yunoSnackbar .formWrapper .ctaWrapper .button {
  margin-right: 0;
}

#app .yunoSnackbar .formWrapper .checkList .fieldLabel {
  font-size: 14px;
  margin: 0 0 10px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 600;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .field {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox {
  border-radius: 20px;
  padding: 5px 10px;
  height: auto;
  font-size: 12px;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox.is-primary {
  background-color: #A81E22;
}

#app .yunoSnackbar .starLabel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 12px;
  margin-top: 15px;
}

#app .yunoSnackbar .starLabel li {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 67px;
          flex: 0 0 67px;
  text-align: center;
}

#app .yunoSnackbar .starLabel li.active {
  visibility: visible;
}

#app .yunoSnackbar .starLabel li.notActive {
  visibility: hidden;
}
/*# sourceMappingURL=instructorHomeV2.css.map */