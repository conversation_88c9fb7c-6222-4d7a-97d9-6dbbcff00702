<?php
class WpHead
{
  function hook_snippet()
  {
    ?>
            <!-- Facebook Pixel Code -->
            <script>
              !function (f, b, e, v, n, t, s) {
                if (f.fbq) return; n = f.fbq = function () {
                  n.callMethod ?
                  n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
                n.queue = []; t = b.createElement(e); t.async = !0;
                t.src = v; s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
              }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '664388571202064');
              // fbq('track', 'PageView');
            </script>
            <noscript><img height="1" width="1"
            src="https://www.facebook.com/tr?id=664388571202064&ev=PageView&noscript=1"
            /></noscript>
            <!-- End Facebook Pixel Code -->

            <!-- old code - Global site tag (gtag.js) - Google Ads: 779332663 -->
            <!--script async src="https://www.googletagmanager.com/gtag/js?id=AW-779332663"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'AW-779332663');
      </script-->

            <?php
            if (is_page_template('templates/auth.php')) {
              date_default_timezone_set('Asia/Calcutta');
              //error_log("client side script: ".date("Y-m-d H:i:s"), 3, ABSPATH."client_side_script_run.log");
              ?>
                    <script>
                      // Facebook conversion
                      fbq('track', 'PageView');
                      fbq('track', 'Lead');
                      // Unbounce conversion
                      /*var _ubaq = _ubaq || [];
                          _ubaq.push(['trackGoal', 'convert']);

                      (function() {
                          var ub_script = document.createElement('script');
                              ub_script.type = 'text/javascript';
                              ub_script.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'd3pkntwtp2ukl5.cloudfront.net/uba.js';
                          var s = document.getElementsByTagName('script')[0];
                          s.parentNode.insertBefore(ub_script, s);
                      }) ();*/

                      // Google conversion
                      gtag('event', 'conversion', { 'send_to': 'AW-779332663/xNpYCPWEptoBELfYzvMC' });
                    </script>
                  <?php
            }
            ?>
          <?php
  }


  function add_css_head()
  {
    if (is_user_logged_in()) {
      ?>
                <style>
                   .learnerlogo a img {
              display: block;

          }
          body.page-id-4124 .fusion-header {
              max-width: 1170px !important;
              margin: 0 auto !important;
              display: none;}
          .learnerlogo{
            display: block !important;}
          a.fusion-premium-paid {display: block !important;}
          .learnerlogo a img {
             display: block !important;
             width: 100px;
             margin-left: 10px;
          }
                </style>
             <?php
             if (is_page('ielts-demo-classes')) { ?>
                <style type="text/css">
                  .fusion-page-title-bar{
                display : none !important;
               }
                 #main{
                 padding-bottom: 0px !important;
                  }
                </style>
          <?php }
             if (is_page('yuno-live-classes')) { ?>
                <style type="text/css">
                  #main{
                 padding-left: 0 !important;
                 padding-right: 0 !important;
                 padding-bottom: 0 !important;
                 padding-top: 0 !important;
               }
               .fusion-page-title-bar{
                display : none !important;
               }
                </style>
          <?php }
    } else {
      ?>
                <style>
                  .learnerlogo a img {
              display: none;
          }
          .yuno_writing_test_inner_area {margin-left: 0px !important;}




             </style>
             <?php
    }
    if (is_page('yuno-live-classes')) { ?>
            <style type="text/css">
              #main{
             padding-left: 0 !important;
             padding-right: 0 !important;
             padding-bottom: 0 !important;
             padding-top: 0 !important;
           }
           .fusion-page-title-bar{
            display : none !important;
           }
            </style>
      <?php }
    if (is_page('ielts-demo-classes')) { ?>
            <style type="text/css">
              .fusion-page-title-bar{
            display : none !important;
           }
             #main{
             padding-bottom: 0px !important;
              }
            </style>
      <?php }
    if (is_page('compare-ielts-courses')) { ?>
            <style type="text/css">
              #main{
             padding-bottom: 60px;
              }
            </style>
      <?php }
  }

/**
 * Redirects users based on their login status, role, and the type of event or class.
 *
 * This function sets the default time zone, retrieves the current date and time,
 * and extracts the post ID from the current URL. It then gathers user data and
 * checks various conditions to determine the appropriate redirection path.
 *
 * The function handles redirection for both logged-in and non-logged-in users
 * based on the custom post type, user role, and event or class type. It includes
 * templates for different scenarios like past or active webinars and classes.
 *
 * @return void
 */
  function language_redirect()
  {
    // Set the default time zone
   date_default_timezone_set('Asia/Kolkata');

    // Get the current date and time
    $currentDate = date("Y-m-d H:i:s");

    // Extract the post ID from the current URL
    $post_id = url_to_postid("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);

    // Get the current user ID
    $user_id = get_current_user_id();

    // Get user data
    $userdata = get_userdata($user_id);

    // Get the list of previous learners for the webinar
    $previousLearners = get_post_meta($post_id, 'YunoClassPrivateLearners', true);

    // Define collections
    $collections = array("um_content-admin", "SEO Manager");

    // Get webinar class type
    $webinarclasstype = get_post_meta($post_id, '_webinar_class', true);

    // Get yuno redirect updates
    $yuno_wp_seo_redirect = get_post_meta($post_id, '_yuno_wp_seo_redirect', true);
    // Get the actual end date of the event
    $eventActualEndDate = get_post_meta($post_id, '_EventEndDate', true);

    // Get the cpt
    $custom_post_type_to_redirect = get_post_type($post_id);

    // Check if the post type is a tribe event
    if ($custom_post_type_to_redirect == "tribe_events") {
      if ($user_id > 0 && is_array($previousLearners)) {
        if ($webinarclasstype == "1" && in_array($userdata->roles[0], $collections) && !in_array($user_id, $previousLearners)) {
          if ($eventActualEndDate < $currentDate) {
            // Post-login past webinar page not for enrolled users
            include(get_stylesheet_directory() . "/templates/class-detail.php");
            exit;
          } else {
            // Post-login past class page not for enrolled users
            include(get_stylesheet_directory() . "/single-tribe_events.php");
            exit;
          }
        } else {
          // Post-login past class page for enrolled users
          include(get_stylesheet_directory() . "/templates/class-detail.php");
          exit;
        }
      } else {
        if ($webinarclasstype == "1") {
          // Pre-login active webinar page (commented out for now)
          // include(get_stylesheet_directory() . "/single-tribe_events.php");
        } else {
          // Pre-login active class page
          include(get_stylesheet_directory() . "/templates/class-detail.php");
          // exit; (commented out for now)
        }
      }
    }

    // Replace 'your_custom_post_type' with the slug of the custom post type you want to redirect
    // $custom_post_type_collections = WP_SEO_CPT_COLLECTION;
    //   if (in_array($custom_post_type_to_redirect, $custom_post_type_collections)) {
    //     //redirect trigger page to home page
    //     if (is_singular($custom_post_type_to_redirect)) {
    //       if ($yuno_wp_seo_redirect == "1") {
    //           $yuno_wp_seo_redirect_url = get_post_meta($post_id, '_yuno_wp_seo_redirect_url', true);
    //           wp_redirect($yuno_wp_seo_redirect_url, 301); // 301 indicates a permanent redirection
    //           exit;
    //       }
    //     }
    //   }
  }

  /**
   * Disable feeds
   */
  function wp_disable_feeds()
  {
    wp_die(__('No feeds available!'));
  }

  /**
   * Define logged in user id
   */
  function hf_Function()
  {
    $user_ID = get_current_user_id();
    define("CURRENT_LOGGED_IN_USER_ID", $user_ID);
	//Set userinfo only if logged in
	if ( is_user_logged_in() ) {
		global $TokenActivities;
		$TokenActivities->jwt_set_logged_user_detail($user_ID);
	}
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_ebook_id()
  {
    define('CURRENT_EBOOK_ID', get_the_ID());
  }

  /**
   * Providing current report id to yuno report template
   */
  function get_report_id()
  {
    define('CURRENT_REPORT_ID', get_the_ID());
  }

  /**
   * Providing current article id to yuno article template
   */
  function get_article_id()
  {
    define('CURRENT_ARTICLE_ID', get_the_ID());
  }

  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_video_id()
  {
    define('CURRENT_VIDEO_ID', get_the_ID());
  }
  /**
   * Providing current ebook id to yuno ebook template
   */
  function get_profile_id()
  {
    define('CURRENT_PROFILE_ID', get_the_ID());
  }
  /**
   * Providing current learning content id to yuno learning-content template
   */
  function get_learning_content_id()
  {
    define('CURRENT_LEARNING_CONTENT_ID', get_the_ID());
  }

  /**
   * Providing current webinar id to yuno webinar template
   */
  function get_webinar_id()
  {
    define('CURRENT_WEBINAR_ID', get_the_ID());
  }
  /**
   * Providing current video testimonial id to yuno video testimonial template
   */
  function get_video_testimonial_id()
  {
    define('CURRENT_VIDEO_TESTIMONIAL_ID', get_the_ID());
  }
  /**
   * Providing current exam result id to yuno exam result template
   */
  function get_exam_result_id()
  {
    define('CURRENT_EXAM_RESULT_ID', get_the_ID());
  }

  public function avada_lang_setup()
  {
    load_child_theme_textdomain('Avada', get_stylesheet_directory() . '/languages');
  }

  /*
  Get Script and Style IDs
  Adds inline comment to your frontend pages
  View source code near the <head> section
  Lists only properly registered scripts
  @ https://digwp.com/2018/08/disable-script-style-added-plugins/
  */
  function shapeSpace_inspect_script_style()
  {
    global $wp_scripts, $wp_styles;
    echo "\n" . '<!-- Scripts -->' . "\n";
    foreach ($wp_scripts->queue as $handle) {
      echo $handle . "\n";
    }
    echo '<!-- Styles -->' . "\n";
    foreach ($wp_styles->queue as $handle) {
      echo $handle . "\n";
    }
  }

  function addAsyncScript($url)
  {
    if (strpos($url, '#asyncload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#asyncload', '', $url);
    else
      return str_replace('#asyncload', '', $url) . "' async='async";
  }
  function addDeferScript($url)
  {
    if (strpos($url, '#deferload') === false)
      return $url;
    else if (is_admin())
      return str_replace('#deferload', '', $url);
    else
      return str_replace('#deferload', '', $url) . "' defer='defer";
  }


  function hook_js()
  {
    ?>
        <!-- Zoho page sense code for website -->
        <script type="text/javascript">(function (w, s) { var e = document.createElement("script"); e.type = "text/javascript"; e.async = true; e.src = "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js"; var x = document.getElementsByTagName("script")[0]; x.parentNode.insertBefore(e, x); })(window, "script");</script>
        <!-- Zoho page sense code for website -->
      <?php
  }

  function add_rel_preload($html, $handle, $href, $media)
  {

    if (is_admin())
      return $html;

    $html = "<link rel='stylesheet' rel='preload' as='style' onload='this.onload=null;this.rel='stylesheet'' id='$handle' href='$href' type='text/css' media='all' />";
    return $html;
  }

  function yunoThemeSetup()
  {
    add_theme_support('menus');
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    // add_image_size('smallest', 300, 300, true);
    // add_image_size('largest', 800, 800, true);
  }


  function yuno_change_admin_text_strings($translated_text, $text, $domain)
  {
    switch ($translated_text) {
      case 'Events':
        $translated_text = __('Live Classes', 'tribe_events');
        break;

      case 'Event':
        $translated_text = __('Live Class', 'tribe_events');
        break;

      case 'Event Add-Ons':
        $translated_text = __('Live Class Add-Ons', 'tribe_events');
        break;

      case "WP-Pro-Quiz":
        $translated_text = __('Practice Test', 'wp-pro-quiz');
        break;

      case "The Events Calendar":
        $translated_text = __('Live Class Calendar', 'tribe_events');
        break;
    }

    return $translated_text;
  }


  function theme_slug_widgets_init()
  {
    register_sidebar(
      array(
        'name' => __('New Learner Sidebar', ''),
        'id' => 'lernersidebar-1',
        'description' => __('', ''),
        'before_widget' => '<li id="%1$s" class="widget %2$s">',
        'after_widget' => '</li>',
        'before_title' => '<h2 class="widgettitle">',
        'after_title' => '</h2>',
      )
    );
  }



  function bybe_remove_yoast_json($data)
  {
    $data = array();
    return $data;
  }


  /********************************************************************************
   ********** Start: Send All Mail With Specific Mail ID (FROM Mail) ***************
   *********************************************************************************/
  function wpb_sender_email($original_email_address)
  {
    return '<EMAIL>';
  }

  /********************************************************************************
   ********************* Start: Function to change sender name *********************
   *********************************************************************************/
  function wpb_sender_name($original_email_from)
  {
    return 'Yuno Learning';
  }

  function version_id()
  {
    if (WP_DEBUG)
      return time();
    return VERSION;
  }

  function my_custom_fonts()
  {
    echo '<style>
   body.wp-admin .navbar  {
    display: none;
    }
    body.wp-admin .need_login_outer  {
        display: none;
    }
    body.wp-admin .top_footer  {
        display: none;
    }
    body.wp-admin .bottom_footer  {
        display: none;
    }
    .create_new_batch .select:not(.is-multiple):not(.is-loading)::after{display: none;}
    .select:not(.is-multiple):not(.is-loading)::after{display: none !important;}

   </style>';
  }
  /**
   * convert html into jpg
   */
  function convert_html_into_jpg($params)
  {
    $html = "<!DOCTYPE html>
  <html>
  <head>
  <style>
  table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }
  td, th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
  }
  tr:nth-child(even) {
    background-color: #dddddd;
  }
  </style>
  </head>
  <body>
  <h2>HTML Table</h2>
  <table>
    <tr>
      <td>Class ID</td>
      <td>[class_id]</td>
    </tr>
    <tr>
      <td>Class Title</td>
      <td>[class_title]</td>
    </tr>
      <tr>
      <td>Date Time</td>
      <td>[datetime]</td>
    </tr>
    <tr>
      <td>Instructor Name</td>
      <td>[instructor_name]</td>
    </tr>
      <tr>
      <td>Instructor Image</td>
      <td>[instructor_image]</td>
    </tr>
  </table>
  </body>
  </html>";
    $class_id = $params['class_id'];
    $class_title = $params['class_title'];
    $datetime = $params['datetime'];
    $instructor_name = $params['instructor_name'];
    $instructor_image = $params['instructor_image'];

    $file_format = str_replace("[class_id]", $class_id, $html);
    $file_format = str_replace("[class_title]", $class_title, $file_format);
    $file_format = str_replace("[datetime]", $datetime, $file_format);
    $file_format = str_replace("[instructor_name]", $instructor_name, $file_format);
    $file_format = str_replace("[instructor_image]", $instructor_image, $file_format);
    $myfile = fopen(ABSPATH . "webinar/" . $class_id . ".html", "w") or die("Unable to open file!");
    fwrite($myfile, $file_format);
    fclose($myfile);
    chmod(ABSPATH . "webinar/" . $class_id . ".html", 0777);
    $curl = curl_init();
    $curlPost = [
      "tasks" => [
        "import-my-file" => [
          "operation" => "import/url",
          "url" => site_url() . "/webinar/" . $class_id . ".html"
        ],
        "convert-my-file" => [
          "operation" => "convert",
          "input" => "import-my-file",
          "output_format" => "jpg"
        ],
        "export-my-file" => [
          "operation" => "export/url",
          "input" => "convert-my-file"
        ]
      ]
    ];
    curl_setopt_array(
      $curl,
      array(
        CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => array(
          'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
          'Content-Type: application/json'
        ),
      )
    );

    $response = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($response);
    //error_log('Call html'.json_encode($res));
    if (!empty($res->data->id)) {
      //error_log('Call jpg'.json_encode($res->data->id));
      $curl = curl_init();

      curl_setopt_array(
        $curl,
        array(
          CURLOPT_URL => 'https://api.cloudconvert.com/v2/jobs/' . $res->data->id . '/wait',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
          CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . get_option('yuno_cloudconvert_api_key') . '',
            'Content-Type: application/json'
          ),
        )
      );

      $response_data = curl_exec($curl);

      curl_close($curl);
      $res_data = json_decode($response_data);
      //error_log('Call url rep'.json_encode($res_data));
      //error_log('Call url rep'.json_encode($res_data->data->tasks[0]->result->files[0]->url));
      copy($res_data->data->tasks[0]->result->files[0]->url, ABSPATH . 'webinar/' . $class_id . '.jpg');
      update_post_meta($class_id, 'webinar_favicon', site_url() . '/webinar/' . $class_id . '.jpg');
    }

  }

  function get_class_id()
  {
    define('CURRENT_CLASS_ID', get_the_ID());
  }

  function get_org_id()
  {
    define('CURRENT_ORG_ID', get_the_ID());
  }
    
    
    
    
  /**
   * Logs an error using WP_Structured_Logger.
   *
   * @param array $logDetails An array containing log details.
   */
  function log_error($logDetails) {
      // Assuming WP_Structured_Logger is correctly set up
      $logger = WP_Structured_Logger::get_instance();
      $logger->custom_log(
          $logDetails['logtype'],
          $logDetails['module'],
          $logDetails['action'],
          $logDetails['message'],
          $logDetails['user'],
          $logDetails['request'],
          $logDetails['data']
      );
  }
    
    
    
    
/**
 * Retrieves the Google Meet access token for a given user ID and org ID.
 *
 * If the access token is expired, it refreshes the token and saves the new token
 * to the user meta.
 *
 * @param int $user_id The WordPress user ID.
 * @param int $org_id The org ID.
 * @return string The Google Meet access token.
 * @throws Exception If an error occurs while retrieving or refreshing the token.
 */
function get_google_meet_access_token($user_id, $org_id) {
  try {
    date_default_timezone_set('Asia/Kolkata');
    $access_token = "";
    // If org_id is not provided, fetch it from another source
    // if (is_null($org_id)) {
    // // Example: Fetch org_id from a database or configuration based on user_id
    // $org_id = (int)get_user_meta($user_id, 'active_org', true) ?? 0; // replace this with your actual method
    // }
    $filtered_virtual_classroom = [];

    // Get the current user meta
    $meta_key = 'virtual_classroom_data'; // Replace with your actual meta key name
    $data = get_user_meta($user_id, $meta_key, true);
    if (count($data) > 0) {
        // Loop through the data to find the entry with org_id = 0
        foreach ($data['data'] as $item) {

          if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) { //$item['org_id'] === $org_id &&
              // Extract the 'meet' data from 'virtual_classroom'
              $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
              break; // Exit loop after finding the first matching record
          }
      }
      $email = get_user_meta($user_id, 'yuno_gplus_email', true);
      $name = get_user_meta($user_id, 'yuno_display_name', true);
      if ($email == $filtered_virtual_classroom['email']) {
        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
      } else {
        $g_client_id = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
        $g_client_secret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
      }
      $refresh_token = $filtered_virtual_classroom['refresh_token'];
      $expires_in = $filtered_virtual_classroom['expires_in'];
      $access_token = $filtered_virtual_classroom['access_token'];
      //if (time() >= $expires_in) {
        $client = new Google_Client();
        $client->setClientId($g_client_id);
        $client->setClientSecret($g_client_secret);
        $client->setAccessType('offline');  // Required for refresh token usage

        // Set the refresh token
        $client->refreshToken($refresh_token);
        // Get the new access token
        $new_token = $client->getAccessToken();
        // Check if we successfully got a new token
        if ($new_token) {
          $org_academies = [];
          $academies = get_post_meta($org_id, "academies", true);
          if (is_array($academies)) {
            $org_academies = $academies;
          }
          $meet_entry = [
            'org_id' => $org_id,
            'academies' => $org_academies,
            'virtual_classroom' => [
                'meet' => [
                    'access_token' => $new_token['access_token'],
                    'refresh_token' => $new_token['refresh_token'],
                    'id_token' => $new_token['id_token'],
                    'token_type' => $new_token['token_type'],
                    'expires_in' => time() + $new_token['expires_in'],
                    'email' => $filtered_virtual_classroom['email'],
                    'name' => $name,
                    'scope' => $new_token['scope']
                ]
            ]
          ];
            $this->save_virtual_auth_access($user_id,$meet_entry);
            return $new_token['access_token'];
        }
      //}
    }
    // Query Elasticsearch to retrieve the plan
    $url = GOOGLE_MEET_API_URL;
    $headers = [
      "Authorization: Bearer " .$access_token,
    ];
    $curlPost = '';

    $return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');
    // Decode JSON into associative array
    $data = json_decode($return['response'], true);

    // Access specific values
    $returnStatus = $data['error']['status'];
    if ($returnStatus == "UNAUTHENTICATED") {
      return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.";
    }
    return $access_token;
  } catch (Exception $e) {
      // Log error
      $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
      $logDetails = [
          'logtype' => 'error',
          'module' => 'ES',
          'action' => 'Virtual - login | signup',
          'message' => $message,
          'user' => ['user_id' => $user_id],
          'request' => ['user_id' => $user_id],
          'data' => []
      ];
      $this->log_error($logDetails);
      return "invalid token";
      // Exit or handle the error as needed
      //exit($message);
  }
}

function check_user_virtual_classroom_permissions($userId){
  $meta_key = 'virtual_classroom_data';
  $data = get_user_meta($userId, $meta_key, true); 
  $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0; 
  $has_required_scopes = false;
    if (isset($data['data']) && is_array($data['data'])) {
        foreach ($data['data'] as $item) {
            if ( isset($item['virtual_classroom']['meet'])) {//$item['org_id'] === $org_id &&
                $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events'];
                $scopes = explode(' ', $filtered_virtual_classroom['scope'] ?? '');
                $has_required_scopes = !array_diff($required_scopes, $scopes);
                break;
            }
        }
    }
    if ($has_required_scopes) {
        return true;
    } else {
        return false;
    }
  }

  
}