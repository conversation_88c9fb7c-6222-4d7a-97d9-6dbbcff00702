<?php

namespace V4;

use Razorpay\Api\Api;

/**
 * Payment model
 */

class PaymentModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadLibary('utility');
    }

    public function getPayment($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // doc id generated randomly
            $paymentDataResponse = $this->es->read('paymentsuccessevent', 'paymentsuccessevent-' . $query['id']);
            $payment = $paymentDataResponse['body']['_source']['data']['details'];
        } elseif ($query['custom']) {
            $paymentDataResponse = $this->es->customQuery($query['custom'], 'paymentsuccessevent');
            if ($paymentDataResponse['status_code'] == 200) {
                $payment = $paymentDataResponse['body']['hits']['hits'][0]['_source']['data']['details'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        if (isset($payment)) {
            $this->loadModel('locale');
            $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
            $tax = $this->localeModel->getTax($ccCode);
            //get enrollment details
            $enrollmentQuery['custom'] = [
                "query" => [
                    "terms" => [
                        "data.details.enrollment_id" => $payment['enrollment_id'] // Use the keyword subfield
                    ]
                ]
            ];
            $enrollmentData = $this->load->subData("enrollment", "getEnrollment", $enrollmentQuery);
            return [
                'id' => $payment['payment_id'],
                'date' => [
                    'time' => $this->dt->convertToSystemDT($payment['event_date']),  // Assuming the event_date is the transaction time
                    'timezone' => $this->locale->activeTimezone(),               // Adjust the timezone if available in the data
                ],
                'mode' => $payment['payment_method'],             // Hardcoded, as no matching field in data
                'status' => $payment['link_status'] ?? '',
                'full_part' => $enrollmentData['full_part']['type'] ?? '',          // Assumed as full payment since no field exists for this
                'item' => [
                    'type' => 'ENROLLMENT',            // You can dynamically update this based on the data
                    'item_id' => $payment['enrollment_id'], // Assuming 'batch_name' is the item ID
                ],
                'gateway' => [
                    'platform' => 'OFFLINE',            // Payment method is Offline, assume platform to be the same
                    'name' => $payment['payment_method'],
                    'fav_icon' => [
                        'url' => '', // No direct favicon, assuming this image
                        'alt_text' => ''
                    ],
                    'transaction_id' => $payment['receipt_id'],
                ],
                'currency' => $this->load->subData("user", "getUser", $payment['user_id'], ['key' => 'app_currency']),
                'amount' => $payment['link_amount'],
                'amount_due' => $payment['link_amount'],
                'tax' => $tax
            ];
        }

        return false;
    }

    public function getPayments($query, $filter = [])
    {
        if ($query['custom']) {
            $paymentCntResponse = $this->es->count('paymentsuccessevent', $query['custom']);
            if ($paymentCntResponse['status_code'] == 200) {
                $paymentDataResponse = $this->es->customQuery($query['custom'], 'paymentsuccessevent', $query['qryStr']);

                if ($paymentDataResponse['status_code'] == 200) {
                    $payments = $paymentDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        $schemaPayments = [
            'Refer#Payment'
        ];

        if (count($payments)) {
            $this->loadModel('locale');
            $responseCount = $paymentCntResponse['body']['count'];
            foreach ($payments as $payment) {
                $details = $payment['_source']['data']['details'];
                //get enrollment details
                $enrollmentQuery['custom'] = [
                    "query" => [
                        "terms" => [
                            "data.details.enrollment_id" => $details['enrollment_id'] // Use the keyword subfield
                        ]
                    ]
                ];

                $enrollmentData = $this->load->subData("enrollment", "getEnrollment", $enrollmentQuery);

                $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
                $tax = $this->localeModel->getTax($ccCode);

                $paymentsResponse[] = array(
                    'id' => $details['payment_id'],
                    'date' => [
                        'time' => $this->dt->convertToSystemDT($details['event_date']),  // Assuming the event_date is the transaction time
                        'timezone' => $this->locale->activeTimezone(),               // Adjust the timezone if available in the data
                    ],
                    'mode' => $details['payment_method'] ?? '',               // Hardcoded, as no matching field in data
                    'status' => $details['link_status'] ?? '',
                    'full_part' => $enrollmentData['full_part']['type'] ?? '',
                    'item' => [
                        'type' => 'ENROLLMENT',            // You can dynamically update this based on the data
                        'item_id' => $details['enrollment_id'], // Assuming 'batch_name' is the item ID
                    ],
                    'gateway' => [
                        'platform' => $details['payment_link'],            // Payment method is Offline, assume platform to be the same
                        'name' => $details['payment_method'],
                        'fav_icon' => [
                            'url' => '', // No direct favicon, assuming this image
                            'alt_text' => ''
                        ],
                        'transaction_id' => $details['receipt_id'],
                    ],
                    'currency' => $this->load->subData("user", "getUser", $details['user_id'], ['key' => 'app_currency']),
                    'amount' => $details['link_amount'],
                    'amount_due' => $details['link_amount_due'],
                    'tax' => $tax
                );
            }

            if (isset($filter['schema'])) {
                $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
            }

            return $this->schema->validate(['count' => $responseCount, 'data' => $paymentsResponse], ['count' => 'integer', 'data' => $schemaPayments], $filter);
        }

        return false;
    }

    /**
     * Inserts or updates payment status in the database.
     *
     * @param array $request The request data containing payment details.
     * @param int $enrollmentId The ID of the associated enrollment record.
     * @return int|void The ID of the inserted/updated payment record or void in case of failure.
     */
    public function addCustomTablePayment($reqArgs, $batchDetails, $enrollmentId, $zohoDealResponse)
    {
        $this->loadModel('course');
        $this->loadModel('counselor');
        global $wpdb;
        $tableName = $wpdb->prefix . "payment_status";
        $receipt = $this->generateReceipt($reqArgs['user_id']);

        // Prepare the data for insertion/update
        $paymentData = [
            'course_id'               => $reqArgs['course_id'],
            'batch_id'                => $reqArgs['batch_id'],
            'user_id'                 => $reqArgs['user_id'],
            'receipt_id'              => $receipt,
            'payment_gateway'         => $reqArgs['payment_gateway'],
            'payment_mode'            => $reqArgs['payment_mode'],
            'amount'                  => $reqArgs['amount'],
            'amount_due'              => $reqArgs['amount_due'],
            'amount_paid'             => $reqArgs['amount_paid'],
            'duration'                => $this->courseModel->getCourse($reqArgs['course_id'])['duration'],
            'currency'                => $reqArgs['currency_code'],
            'payment_status'          => 'ISSUED',
            'payment_gateway_message' => '',
            'notes'                   => $reqArgs['self_notes'],
            'total_instalments'       => $reqArgs['total_instalments'],
            'paid_instalments'       => 0, //$reqArgs['total_instalments'],
            'zoho_deal_id'       => $zohoDealResponse,
            'customer_id' => '',
            'counselor_id'            => $reqArgs['counselor_id'],
            'counselor_name' => $this->counselorModel->getCounselor($reqArgs['counselor_id'])['full_name'],
            'payment_date_time' => $this->dt->currentSystemDT("Y-m-d H:i:s"),
            'created_at' =>  $this->dt->currentSystemDT("Y-m-d H:i:s"),
            'updated_at' => $this->dt->currentSystemDT("Y-m-d H:i:s")
        ];

        $insertResult = $wpdb->insert($tableName, $paymentData);

        // Check for errors during insertion
        if ($insertResult === false) {
            $this->handlePaymentFailure('Failed to insert payment status', $reqArgs['user_id']);
        }
        return $wpdb->insert_id;
    }

    /**
     * Handles payment failure by sending an email alert and logging the error.
     *
     * @param string $message The error message describing the payment failure.
     * @param int $userId The ID of the user associated with the payment failure.
     * @return void
     */
    private function handlePaymentFailure($message, $userId)
    {
        global $wpdb;

        // Fetch email template and subject from the database
        $emailData = $wpdb->get_row(
            "SELECT template, subject FROM wp_email_templates WHERE email_type = 'PAYMENT_FAILURE_ALERT'",
            ARRAY_A
        );

        if (!$emailData) {
            error_log("Failed to fetch email template for payment failure alert.", 3, ABSPATH . 'error-logs/payment_failure.log');
            return;
        }

        // Prepare the email template with dynamic values
        $templateHtml = str_replace("[ERROR_MSG]", $message, $emailData['template']);
        $userEmail = get_user_meta($userId, 'yuno_gplus_email', true);
        $emailContent = str_replace("[USER_EMAIL]", $userEmail, $templateHtml);

        // Set the email subject and recipients
        $emailSubject = $emailData['subject'];
        $recipients = [PAYMENT_FAILURE_ALERT_CC_EMAIL, PAYMENT_FAILURE_ALERT_ADMIN_EMAIL];

        // Send the email via Pinpoint
        SendEmailViaPinpoint($emailContent, $recipients, $emailSubject); // move to notification library

        // Log the error message to a file
        $logMessage = "Payment failure for user_id: $userId - Message: $message";
        error_log($logMessage, 3, ABSPATH . 'error-logs/payment_failure.log');
    }

    /**
     * Posts a payment success event to Elasticsearch and performs related actions.
     *
     * @param array $request The request data containing payment and user details.
     * @param int $enrollmentId The ID of the associated enrollment record.
     * @return void
     */
    public function addPayment($reqArgs, $batchDetails, $paymentPostId, $enrollmentPostId)
    {
        $this->loadModel('user');
        $this->loadModel('course');
        $this->loadModel('counselor');
        $this->loadModel('org');
        $this->loadModel('instructor');

        $paymentData = [
            "data" => [
                'details' => [
                    'payment_id' => $paymentPostId,
                    'enrollment_id' => $enrollmentPostId,
                    'user_id'          => $reqArgs['user_id'],
                    'event_type'       => 'paymentsuccessevent',
                    'event_label'      => 'Paid successfully',
                    'event_date' => date('Y-m-d H:i:s', strtotime($this->dt->currentSystemDT())),
                    'image'            => $this->userModel->getUser($reqArgs['user_id'])['image_url'] ?? '', // Assuming you have a method to get user image URL
                    'name'             =>  $this->userModel->getUser($reqArgs['user_id'])['full_name'] ?? '',
                    'payment_link'     => '', // Update as needed
                    'payment_method'   => $reqArgs['payment_gateway'],
                    'link_amount'      => $reqArgs['amount'],
                    'link_status'      => 'ISSUED', // Update as per your logic
                    'counselor_name'   => $this->counselorModel->getCounselor($reqArgs['counselor_id'])['full_name'] ?? '',
                    'counselor_image'  => $this->counselorModel->getCounselor($reqArgs['counselor_id'])['image_url'] ?? '',
                    'course_name'      => $this->courseModel->getCourse($reqArgs['course_id'])['title'] ?? '',
                    'org_admin' => $this->orgModel->getOrganization($reqArgs['org_id'], ['schema' => 'Organization_Minimal']),
                    'category'         => implode(',', wp_get_post_terms($reqArgs['course_id'], 'course_category', ['fields' => 'names'])),
                    'batch_name'       => $reqArgs['batch_id'],
                    'instructor_name'  => $this->instructorModel->getInstructor($batchDetails['instructor_id'])['user']['full_name'] ?? '',
                    'instructor_image' => $this->instructorModel->getInstructor($batchDetails['instructor_id'])['user']['image_url'] ?? '',
                    'generate_link'    => '', // Update as needed
                    'invoice_id'       => '',
                    'receipt_id'       => get_post_meta($paymentPostId, 'receipt_id', true),
                    'created_on'       => $this->dt->currentSystemDT()
                ],
                '@timestamp' => $this->dt->currentSystemDT()
            ]
        ];

        // Send data to Elasticsearch
        $esResponse = $this->es->create('paymentsuccessevent', $paymentData);
        if ($esResponse['status_code'] == 201) {
            return $esResponse;
        }

        return false;
    }

    /**
     * Inserts payment data into the custom post type 'payment'.
     *
     * @param array $request The request data containing payment details.
     * @param int $enrollmentId The ID of the associated enrollment record.
     * @return int|WP_Error The ID of the inserted payment post on success or WP_Error on failure.
     */
    public function addCustomPostPayment($reqArgs, $batchDetails, $enrollmentId)
    {
        $this->loadModel('course');
        global $wpdb;

        // Prepare data for inserting into the custom post type 'payment'
        $postData = [
            'post_title'   => 'Payment for User ID: ' . $reqArgs['user_id'], // Adjust the title as needed
            'post_content' => '', // Add relevant content if needed
            'post_status'  => 'publish', // Set the status to publish or draft as needed
            'post_type'    => 'payment', // Your custom post type slug
        ];

        // Insert the post into the custom post type
        $postId = wp_insert_post($postData);

        // Check if the post was inserted successfully
        if (is_wp_error($postId)) {
            error_log('Failed to insert payment post: ' . $postId->get_error_message());
            // $this->updateEnrollmentClassObject($enrollmentId); // Save current state
            // return new WP_Error('payment_insert_error', 'Failed to insert payment.', ['status' => 500]);
            return false;
        }

        $receipt = $this->generateReceipt($reqArgs['user_id']);

        // Update post meta with payment data
        update_post_meta($postId, 'user_id', $reqArgs['user_id']);
        update_post_meta($postId, 'course_id', $reqArgs['course_id']);
        update_post_meta($postId, 'batch_id', $reqArgs['batch_id']);
        update_post_meta($postId, 'batch_end_date', $batchDetails['end_date']);
        update_post_meta($postId, 'duration', $this->courseModel->getCourse($reqArgs['course_id'])['duration']);
        update_post_meta($postId, 'enrollment_id', $enrollmentId);
        update_post_meta($postId, 'amount', $reqArgs['amount']);
        update_post_meta($postId, 'amount_due', $reqArgs['amount_due']);
        update_post_meta($postId, 'amount_paid', $reqArgs['amount_paid']);
        update_post_meta($postId, 'currency', $reqArgs['currency_code']);
        update_post_meta($postId, 'payment_status', 'ISSUED');
        update_post_meta($postId, 'payment_gateway', $reqArgs['payment_gateway']);
        update_post_meta($postId, 'payment_gateway_message', $reqArgs['payment_gateway_message']);
        update_post_meta($postId, 'notes', $reqArgs['self_notes']);
        update_post_meta($postId, 'payment_description', $reqArgs['payment_description']);
        update_post_meta($postId, 'payment_mode', $reqArgs['payment_mode']);
        update_post_meta($postId, 'total_instalments', $reqArgs['total_instalments']);
        update_post_meta($postId, 'instructor_id', get_post_meta($reqArgs['batch_id'], 'instructor_id', true));
        update_post_meta($postId, 'counselor_id', $reqArgs['counselor_id']);
        update_post_meta($postId, 'receipt_id', $receipt);
        update_post_meta($postId, 'created_on', $this->dt->currentSystemDT("Y-m-d H:i:s"));
        update_post_meta($postId, 'payment_date_time', $this->dt->currentSystemDT("Y-m-d H:i:s"));
        update_post_meta($enrollmentId, 'payment_id', $postId);

        // Log success
        error_log("Payment post created with ID: $postId");
        return $postId;
    }

    /**
     * Retrieves the provided receipt or generates a new one if not provided.
     *
     * @param string|null $receipt The existing receipt, if any.
     * @param int $userId The ID of the user.
     * @return string The existing or newly generated receipt.
     */
    public function generateReceipt($userId)
    {
        $randomPart1 = mt_rand(0, 99999);
        $randomPart2 = rand(10, 9999);
        return "receipt-" . $randomPart1 . $randomPart2 . $userId;
    }

    /**
     * Updates the payment status in the payment_status table.
     *
     * @param int $paymentTblId The ID of the payment record in the custom table.
     * @param string $status The new payment status (e.g., 'ISSUED', 'SUCCESS', 'FAILED').
     * @return bool Returns true if the update was successful, false otherwise.
     */

    public function updateCustomTablePayment($paymentTblId, $args)
    {
        global $wpdb;

        // Prepare the table name
        $tableName = $wpdb->prefix . "payment_status";

        // Update the payment status
        $updateResult = $wpdb->update(
            $tableName,                      // Table name
            [
                'payment_status' => $args['status'] ?? null,
                'total_instalments' => $args['total_instalments'] ?? null,
                'paid_instalments' => $args['paid_instalments'] ?? null,
                'amount_paid' => $args['amount_paid'] ?? null,
                'amount' => $args['amount'] ?? null,
                'amount_due' => $args['amount_due'] ?? null,
                'order_id' => $args['order_id'] ?? null,
                'payment_gateway_message' => $args['payment_gateway_message'] ?? null,
            ],   // Data to update
            ['id' => $paymentTblId],         // Where condition
            [
                '%s', // Format for 'payment_status'
                '%d', // Format for 'total_instalments'
                '%d', // Format for 'paid_instalments'
                '%f', // Format for 'amount_paid'
                '%f', // Format for 'amount'
                '%f', // Format for 'amount_due'
                '%s', // Format for 'order_id'
                '%s', // Format for 'payment_gateway_message'
            ],                           // Data format
            ['%d']                           // Where format
        );

        // Return true if rows are updated, otherwise false
        return $updateResult !== false;
    }

    /**
     * Updates the payment custom post type after verifying the payment status in the custom table.
     *
     * @param int $paymentTblId The ID of the payment record in the custom table.
     * @param string $newStatus The new payment status to update in the custom post.
     * @return bool Returns true if the custom post is updated successfully, false otherwise.
     */
    public function updateCustomPostPayment($paymentPostId, $args)
    {
        global $wpdb;

        // Get the payment post ID from the custom table (assuming it is stored in the 'post_id' column)
        if (empty($paymentPostId) || !get_post($paymentPostId)) {
            error_log("No payment post found for payment ID: {$paymentPostId}");
            return false;
        }

        // Update the custom post type's metadata
        update_post_meta($paymentPostId, 'payment_status', $args['status']);
        update_post_meta($paymentPostId, 'updated_at', $this->dt->currentSystemDT());

        error_log("Custom post payment updated successfully for post ID: {$paymentPostId}");
        return true;
    }

    public function updatePayment($paymentId, $args)
    {
        $paymentData = [
            "data" => [
                "details" => [
                    "link_status" => $args['status']
                ]
            ]
        ];

        $paymentDataResponse = $this->es->update('paymentsuccessevent', $paymentId, $paymentData);
        if ($paymentDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    /**
     * Creates a payment link using Razorpay API.
     *
     * @param WP_REST_Request $request The request object containing payment details.
     * @return WP_REST_Response|WP_Error Returns a REST response with payment link data or WP_Error on failure.
     */
    public function createPaymentLink($request)
    {
        $requestData = $request;
        $razorpayKeyId = esc_attr(get_option('yuno_razorpay_api_key'));
        $razorpaySecretKey = esc_attr(get_option('yuno_razorpay_secret_key'));

        $paymentUrl = 'https://' . $razorpayKeyId . ':' . $razorpaySecretKey . '@api.razorpay.com/v1/invoices/';

        $paymentParams = [
            'customer' => [
                'name' => $requestData['customer']['name'],
                'email' => $requestData['customer']['email'],
                'contact' => $requestData['customer']['contact'],
            ],
            'type' => $requestData['type'],
            'notes' => [
                'org_on_yuno' => $requestData['notes']['org_id'] ?? 0,
            ],
            'view_less' => 1,
            'amount' => $requestData['amount'] * 100, // Convert amount to paise
            'currency' => $requestData['currency'],
            'description' => $requestData['description'],
            'receipt' => $requestData['receipt'],
            //'callback_url' => $requestData['callback_url'],
            //'expired_at' => RAZORPAY_EXPIRY_IN_DAYS,
            //'expire_by' => RAZORPAY_EXPIRY_IN_DAYS,
            'callback_method' => "get",
            'partial_payment' => $requestData['partial_payment'],
        ];

        error_log("payment - paymentParams - createPaymentLink " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentParams) . "\n\n", 3, ABSPATH . "error-logs/payment_model.log");

        // Call the cURL request function
        $headers = [
            "Content-Type: application/json",
            "Accept: application/json",
        ];
        //$apiResult = $this->payment->sendCurlRequest($paymentUrl, $paymentParams, $headers);
        $apiResult = $this->utility->curlRequest($paymentUrl, 'POST',  $paymentParams, $headers);

        error_log("payment - apiResult - createPaymentLink " . date("Y-m-d H:i:s") . " === ID: " . json_encode($apiResult) . "\n\n", 3, ABSPATH . "error-logs/payment_model.log");

        $apiResponse = $apiResult['response'];
        $httpCode = $apiResult['code'];

        // Check for errors
        if ($httpCode !== 200 || isset($apiResponse['error'])) {
            return false; // Return false if there's an error
        }

        // Call the new function to insert the payment link data into the database
        $insertSuccess = $this->insertPaymentLinkIntoDatabase($apiResponse, $requestData);

        // Format the response for successful cases
        $paymentResponse = [
            'amount' => $apiResponse['amount'],
            'amount_due' => $apiResponse['amount_due'],
            'gross_amount' => $apiResponse['gross_amount'],
            'invoice_url' => $apiResponse['short_url'],
            'message' => "Payment link successfully generated",
        ];

        // Return true with formatted response
        return $apiResponse['short_url'];
    }
    /**
     * Handle offline payments.
     */
    public function handleOfflinePayment($reqArgs, $batchDetails, $paymentPostId, $paymentEsRes, $enrollmentPostId, $enrollmentEsRes)
    {
        $logFile = ABSPATH . "error-logs/handleOfflinePayment.log";
        $this->loadModel('enrollment');
        // Update payment statuses

        error_log("addEnrollment  - handleOfflinePayment -- reqArgs " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($reqArgs) . "\n\n", 3, $logFile);
        error_log("addEnrollment  - handleOfflinePayment -- batchDetails " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($batchDetails) . "\n\n", 3, $logFile);
        error_log("addEnrollment  - handleOfflinePayment -- paymentPostId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentPostId) . "\n\n", 3, $logFile);
        error_log("addEnrollment  - handleOfflinePayment -- paymentEsRes " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentEsRes) . "\n\n", 3, $logFile);
        error_log("addEnrollment  - handleOfflinePayment -- enrollmentPostId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentPostId) . "\n\n", 3, $logFile);
        error_log("addEnrollment  - handleOfflinePayment -- enrollmentEsRes " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentEsRes) . "\n\n", 3, $logFile);

        $paymentPostUpdateId = $this->updateCustomPostPayment($paymentPostId, [
            'status' => 'COMPLETE'
        ]);

        error_log("addEnrollment  - handleOfflinePayment  --- paymentPostUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentPostUpdateId) . "\n\n", 3, $logFile);

        $paymentEsResUpdate = $this->updatePayment($paymentEsRes['body']['_id'], [
            'status' => 'COMPLETE',
        ]);

        error_log("addEnrollment  - handleOfflinePayment -- paymentEsResUpdate " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentEsResUpdate) . "\n\n", 3, $logFile);

        // Proceed if payment updates are successful
        if ($paymentPostUpdateId && $paymentEsResUpdate) {
            // Update enrollment statuses

            $enrollmentPostUpdateId = $this->enrollmentModel->updateCustomPostEnrollment($enrollmentPostId, [
                'status' => 'ACTIVE'
            ]);

            error_log("addEnrollment  - handleOfflinePayment -- enrollmentPostUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentPostUpdateId) . "\n\n", 3, $logFile);

            $enrollmentEsUpdateId = $this->enrollmentModel->updateEnrollment($enrollmentEsRes['body']['_id'], [
                'status' => 'ACTIVE',
            ]);

            error_log("addEnrollment  - handleOfflinePayment -- enrollmentEsUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentEsUpdateId) . "\n\n", 3, $logFile);

            if ($enrollmentPostUpdateId && $enrollmentEsUpdateId) {
                // Perform post-enrollment actions
                return true;
            }
        }

        return false;
    }

    /**
     * Handle online payments.
     */
    public function handleOnlinePayment($reqArgs, $wpBatches, $enrollmentPostId, $paymentPostId)
    {
        // Build payment parameters
        $paymentParams = $this->buildPaymentParams($reqArgs, $paymentPostId);
        error_log(" - handleOnlinePayment -- params call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentParams) . "\n\n", 3, ABSPATH . "error-logs/payment_model.log");

        // Generate payment link
        $paymentLinkResponse = $this->createPaymentLink($paymentParams);

        error_log("handleOnlinePayment - linkk call  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentLinkResponse) . "\n\n", 3, ABSPATH . "error-logs/payment_model.log");

        // Return false if payment link generation failed
        if (empty($paymentLinkResponse)) {
            return false;
        }

        error_log("handleOnlinePayment - linkk call -- end " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentLinkResponse) . "\n\n", 3, ABSPATH . "error-logs/payment_model.log");

        // Return the payment link if processing succeeds, otherwise false
        return $paymentLinkResponse;
    }
    public function buildPaymentParams($reqArgs, $paymentPostId)
    {
        $this->loadModel('user');
        $receiptId = get_post_meta($paymentPostId, 'receipt_id', true);
        return [
            'customer' => [
                'name' => $this->userModel->getUser($reqArgs['user_id'])['full_name'], // Use org_user_name for customer name
                'email' => $this->userModel->getUser($reqArgs['user_id'])['email'], // Use org_user_email for customer email
                'contact' => $this->userModel->getUser($reqArgs['user_id'])['phone'], // Use org_user_phone for customer contact
            ],
            'type' => 'link', // Assuming a fixed type for Razorpay payment link
            'notes' => [
                'org_on_yuno' => $reqArgs['org_id'] ?? 0, // Pass org_id
            ],
            'view_less' => 1, // Enable a minimal view for Razorpay link
            'amount' => $reqArgs['amount'], // Convert amount to smallest currency unit
            'currency' => $reqArgs['currency_code'], // Use currency_code from request
            'description' => $reqArgs['payment_description'], // Add a meaningful description
            'receipt' => $receiptId, // Generate a unique receipt ID
            // 'callback_url' => site_url('/payment-callback'), // Set callback URL for payment status
            //'callback_url' => site_url() . '/wp-json/yuno/v4/enrollment/webhook',
            'expired_at' => RAZORPAY_EXPIRY_IN_DAYS, // Razorpay expiry duration
            'expire_by' => RAZORPAY_EXPIRY_IN_DAYS, // Razorpay expiry duration
            'callback_method' => "post", // Razorpay callback method
            'partial_payment' => $reqArgs['total_instalments'] > 0 ? true : false, // Enable partial payment if instalments exist
        ];
    }
    public function handleDirectOnlinePayment($reqArgs, $wpBatches, $enrollmentPostId, $paymentPostId)
    {
        error_log("handleDirectOnlinePayment - reqArgs start " . date("Y-m-d H:i:s") . " === ID: " . json_encode($reqArgs) . "\n\n", 3, ABSPATH . "error-logs/handleDirectOnlinePayment.log");
        // Generate Order Id From Razorpay
        global $wpdb;
        $razorpayKeyId = esc_attr(get_option('yuno_razorpay_api_key'));
        $razorpaySecretKey = esc_attr(get_option('yuno_razorpay_secret_key'));

        $api = new Api($razorpayKeyId, $razorpaySecretKey);
        $receiptId = $this->generateReceipt($reqArgs['user_id']);

        error_log("handleDirectOnlinePayment - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/handleDirectOnlinePayment.log");
        $amount = $reqArgs['amount'] * 100; // Convert to paise
        $currency = $reqArgs['currency_code'];
        $paymentCapture = 1; // Set to 1 for immediate capture, 0 for authorization only
        $orderRazorResponse = $this->createRazorpayOrder($api, $receiptId, $amount, $currency, $paymentCapture);

        error_log("handleDirectOnlinePayment - createRazorpayOrder call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($orderRazorResponse) . "\n\n", 3, ABSPATH . "error-logs/handleDirectOnlinePayment.log");

        //  Fetch `course_name` from Elasticsearch
        $courseName = $this->es->read('course', 'course-' . $reqArgs['course_id'], ['_source' => 'data.details.title'])['body']['_source']['data']['details']['title'] ?? '';

        $userDetails = $this->es->read('signedup', 'signedup-' . $reqArgs['user_id'], ['_source' => 'data.details.user']);
        $userPhone = $userDetails['body']['_source']['data']['details']['user']['phone'] ?? '';
        $userEmail = $userDetails['body']['_source']['data']['details']['user']['email'] ?? '';

        if ($orderRazorResponse) {
            $checkoutPayload = [
                "status" => "success",
                "receipt_id" => $receiptId,
                "key" => $razorpayKeyId,
                "amount" => $reqArgs['amount'],
                "name" => $courseName ?? 'Course Payment',
                "description" => $courseName ?? 'Course Payment',
                "image" => "/wp-content/uploads/2018/10/yuno_logo-1.png",
                "order_id" => $orderRazorResponse,
                "prefill" => [
                    "email" => $userEmail ?? '',
                    "contact" => $userPhone ?? ''
                ],
                "notes" => [
                    "address" => 'Yunolearning',
                    "org_on_yuno" => $reqArgs['org_id'] ?? 0
                ],
                "theme" => [
                    "color" => "#104378"
                ]
            ];

            error_log("handleDirectOnlinePayment - checkoutPayload " . date("Y-m-d H:i:s") . " === ID: " . json_encode($checkoutPayload) . "\n\n", 3, ABSPATH . "error-logs/handleDirectOnlinePayment.log");
            // Prepare the response
            return $checkoutPayload;
        } else {
            error_log("handleDirectOnlinePayment: Failed to create Razorpay order for user_id: " . ($reqArgs['user_id'] ?? 'UNKNOWN') . " at " . date('Y-m-d H:i:s'), 3, ABSPATH . "error-logs/handleDirectOnlinePayment.log");
            return false;
        }
    }

    public function createRazorpayOrder($api, $receiptId, $amount, $currency, $paymentCapture = 1)
    {
        error_log("createRazorpayOrder - requesttttt receiptId" . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/createRazorpayOrder.log");
        try {
            $orderData = [
                'receipt' => $receiptId,
                'amount' => $amount, // Razorpay accepts amount in paise
                'currency' => $currency,
                'payment_capture' => $paymentCapture
            ];
            error_log("createRazorpayOrder - orderData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($orderData) . "\n\n", 3, ABSPATH . "error-logs/createRazorpayOrder.log");

            $razorpayOrder = $api->order->create($orderData);
            error_log("createRazorpayOrder - razorpayOrder " . date("Y-m-d H:i:s") . " === ID: " . json_encode($razorpayOrder) . "\n\n", 3, ABSPATH . "error-logs/createRazorpayOrder.log");
            return $razorpayOrder['id']; // returns the Razorpay Order ID
        } catch (\Exception $e) {
            // Log the error or handle exception as needed
            error_log("createRazorpayOrder - Exception - Razorpay Order Creation Error" . date("Y-m-d H:i:s") . " === ID: " . json_encode($e->getMessage()) . "\n\n", 3, ABSPATH . "error-logs/createRazorpayOrder.log");
            // error_log("Razorpay Order Creation Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Fetches the receipt associated with a given payment ID from the payment_status table.
     *
     * @param int $paymentId The ID of the payment.
     * @return string|null The receipt number if found, or null if not found.
     */
    public function getReceiptByPaymentId($paymentId)
    {
        global $wpdb;

        // Define the table name
        $tableName = $wpdb->prefix . "payment_status";

        // Query to fetch the payment record by payment ID
        $paymentRecord = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT receipt_id FROM {$tableName} WHERE id = %d",
                $paymentId
            ),
            ARRAY_A
        );

        // Return the receipt if found, or null otherwise
        return $paymentRecord['receipt_id'] ?? '';
    }

    // This function handles inserting payment link data into the database
    public function insertPaymentLinkIntoDatabase($apiResponse, $requestData)
    {
        global $wpdb;

        // Prepare data for insertion into the payment_links table
        $paymentLink = [
            'razerpay_invoice_id' => $apiResponse['id'], // Razorpay invoice ID
            'receipt_id' => $requestData['receipt'], // Receipt ID from the request
            'payment_id' => $apiResponse['payments'][0]['id'] ?? '', // Payment ID from the response
            'payment_link' => $apiResponse['short_url'], // Razorpay short URL
            'amount' => $apiResponse['amount'], // Amount (convert from paise to rupees)
            'link_status' => 'ISSUED', // Initial status
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
            'link_expiry' => date('Y-m-d H:i:s', strtotime("+1 day")), // Example: Expire in 1 day
        ];

        // Define table name
        $table_name = $wpdb->prefix . 'payment_links';

        // Insert data into the database
        $inserted = $wpdb->insert($table_name, $paymentLink);

        // Check if the insertion failed
        if ($inserted === false) {
            error_log("Failed to insert payment link into the database.");
            return false; // Return false if the insertion fails
        }

        return true; // Return true if insertion is successful
    }

    public function handleWebhook()
    {
        // Extract callback data
        $callbackData = $_REQUEST;
        // Log received data for debugging
        error_log("Callback received: " . print_r($callbackData, true), 3, ABSPATH . "error-logs/razorpay_webhook.log");

        // Ensure all required fields are present
        if (
            !isset($callbackData['razorpay_invoice_id']) ||
            !isset($callbackData['razorpay_payment_id']) ||
            !isset($callbackData['razorpay_signature'])
        ) {
            error_log("Invalid callback payload", 3, ABSPATH . "error-logs/razorpay_webhook.log");
            http_response_code(400);
            echo json_encode(['status' => 'error', 'message' => 'Invalid callback payload']);
            exit;
        }

        // Extract parameters
        $invoiceId = $callbackData['razorpay_invoice_id'];
        $paymentId = $callbackData['razorpay_payment_id'];
        $signature = $callbackData['razorpay_signature'];
        $status = $callbackData['razorpay_invoice_status'];
        $receipt = $callbackData['razorpay_invoice_receipt'] ?? 'N/A';

        // Verify signature
        $secretKey = esc_attr(get_option('yuno_razorpay_secret_key'));
        // Process based on invoice status
        switch ($status) {
            case 'paid':
                $this->handleInvoicePaid($invoiceId, $paymentId, $status, $receipt);
                break;

            case 'failed':
                $this->handlePaymentFailed($invoiceId, $paymentId, $status, $receipt);
                break;

            default:
                error_log("Unhandled status: " . $status, 3, ABSPATH . "error-logs/razorpay_webhook.log");
                break;
        }

        // Respond to Razorpay
        http_response_code(200);
        echo json_encode(['status' => 'success']);
        exit;
    }

    /**
     * Handle Invoice Paid Event
     */
    public function handleInvoicePaid($invoiceId, $paymentId, $status, $receipt)
    {
        // Fetch additional details if needed
        $invoiceDetails = $this->fetchInvoiceDetails($invoiceId);
        $amount = $invoiceDetails['amount'] ?? 0;

        // Log event details
        error_log("Invoice Paid: ID = $invoiceId, Payment ID = $paymentId, Amount = $amount, Receipt = $receipt", 3, ABSPATH . "error-logs/razorpay_webhook.log");
        // Update database or perform other logic
        // Example: Update payment status in your database
        return $invoiceDetails;
    }

    /**
     * Handle Payment Failed Event
     */
    public function handlePaymentFailed($invoiceId, $paymentId, $status, $receipt)
    {
        // Log event details
        error_log("Payment Failed: ID = $invoiceId, Payment ID = $paymentId, Status = $status, Receipt = $receipt", 3, ABSPATH . "error-logs/razorpay_webhook.log");
        // Update database or perform other logic
        // Example: Mark payment as failed in your database
    }

    /**
     * Verify Razorpay Signature
     */
    private function verifySignature($invoiceId, $paymentId, $signature, $secret)
    {
        $payload = $invoiceId . "|" . $paymentId;
        $generatedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($generatedSignature, $signature);
    }

    /**
     * Fetch Invoice Details from Razorpay API
     */
    private function fetchInvoiceDetails($invoiceId)
    {
        $apiKey = esc_attr(get_option('yuno_razorpay_api_key'));
        $apiSecret = esc_attr(get_option('yuno_razorpay_secret_key'));
        $url = "https://api.razorpay.com/v1/invoices/$invoiceId";

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "$apiKey:$apiSecret");
        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            error_log('Curl error: ' . curl_error($curl), 3, ABSPATH . "error-logs/razorpay_webhook.log");
            return null;
        }

        curl_close($curl);
        return json_decode($response, true);
    }

    /**
     * Fetch Payment Details from Razorpay API
     */
    public function fetchPaymentDetails($paymentId)
    {
        // Razorpay API credentials
        $apiKey = esc_attr(get_option('yuno_razorpay_api_key'));
        $apiSecret = esc_attr(get_option('yuno_razorpay_secret_key'));
        $url = "https://api.razorpay.com/v1/payments/$paymentId";

        // Initialize cURL
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "$apiKey:$apiSecret");

        // Execute API request
        $response = curl_exec($curl);

        // Check for errors
        if (curl_errno($curl)) {
            error_log('Curl error: ' . curl_error($curl), 3, ABSPATH . "error-logs/razorpay_payment.log");
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        // Decode and return the response
        return json_decode($response, true);
    }
    public function checkIfPaymentExists($user_id, $course_id, $batch_id)
    {
        global $wpdb;
        $tableName = $wpdb->prefix . 'payment_status';

        $query = $wpdb->prepare(
            "SELECT * FROM {$tableName}
         WHERE user_id = %d
           AND course_id = %d
           AND batch_id = %d
           AND total_instalments = 0
         LIMIT 1",
            $user_id,
            $course_id,
            $batch_id
        );

        $paymentRecord = $wpdb->get_results($query);

        return !empty($paymentRecord) ? $paymentRecord : false;
    }

    public function checkIfPaymentExistsInCPT($userId, $courseId, $batchId)
    {
        $args = [
            'post_type'  => 'payment', // Custom post type
            'post_status' => 'publish', // Only check published payments
            'meta_query' => [
                'relation' => 'AND', // All conditions must be true
                [
                    'key' => 'user_id',
                    'value' => $userId,
                    'compare' => '=',
                ],
                [
                    'key' => 'course_id',
                    'value' => $courseId,
                    'compare' => '=',
                ],
                [
                    'key' => 'batch_id',
                    'value' => $batchId,
                    'compare' => '=',
                ]
            ],
            'fields' => 'ids', // Only return post IDs
            'posts_per_page' => 1, // Limit to one result for efficiency
        ];

        $posts = get_posts($args);

        if (!empty($posts)) {
            // Payment exists
            return $posts[0]; // Return the post ID of the first match
        } else {
            // Payment does not exist
            return false;
        }
    }
    public function getPaymentByEs($userId, $courseId, $batchId, $paymentPostId)
    {
        $receiptId = get_post_meta($paymentPostId, 'receipt_id', true);
        // Build the query
        $query = [
            'custom' => [
                "query" => [
                    "bool" => [
                        "must" => [
                            [
                                "match" => [
                                    'data.details.user_id' => $userId
                                ]
                            ],
                            [
                                "match" => [
                                    'data.details.receipt_id' => $receiptId
                                ]
                            ],
                            [
                                "match" => [
                                    'data.details.batch_name' => $batchId
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Call the getPayment method with the query
        return $this->getPayment($query);
    }

    public function getPaymentsCount()
    {
        // Handle the response and return the document count
        $responseData = $this->es->count('paymentsuccessevent');

        if ($responseData['status_code'] == 200) {
            $responseCount = $responseData['body']['count'];
        } else {
            return false;
        }

        if (isset($responseCount)) {
            return (int) $responseCount;
        }
        return 0;
    }

    public function getPaymentIdByReceipt($receiptId)
    {
        global $wpdb;

        // Define the table name
        $tableName = $wpdb->prefix . "payment_status";

        // Query to fetch the payment record by receipt ID
        $paymentRecord = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT id FROM {$tableName} WHERE receipt_id = %s",
                $receiptId
            ),
            ARRAY_A
        );

        // Return the payment ID if found, or null otherwise
        return $paymentRecord['id'] ?? '';
    }

    public function getCptIdByPaymentTblId($paymentTblId)
    {
        $args = [
            'post_type'  => 'payment', // Custom post type
            'post_status' => 'publish', // Only check published payments
            'meta_query' => [
                [
                    'key' => 'payment_id',
                    'value' => $paymentTblId,
                    'compare' => '=', // Exact match
                ],
            ],
            'fields' => 'ids', // Return only the post IDs
            'posts_per_page' => 1, // Limit to one result for efficiency
        ];

        $posts = get_posts($args);

        if (!empty($posts)) {
            $postId = $posts[0]; // Get the CPT ID

            // Specific meta keys to retrieve
            $metaKeys = ['user_id', 'course_id', 'batch_id', 'payment_id'];
            $metaData = [];

            // Retrieve specific meta values
            foreach ($metaKeys as $key) {
                $metaData[$key] = get_post_meta($postId, $key, true);
            }

            // Return the CPT ID and metadata
            return [
                'post_id' => $postId,
                'meta_data' => $metaData,
            ];
        }

        // Return false if no matching CPT is found
        return false;
    }

    public function updatePaymentDetails($updateInfo)
    {
        error_log("updatePaymentDetails - updateInfo " . date("Y-m-d H:i:s") . " === ID: " . json_encode($updateInfo) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

        $status = $updateInfo['status'];
        error_log("updatePaymentDetails - status from webhook" . date("Y-m-d H:i:s") . " === ID: " . json_encode($status) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

        if ($status === 'paid') {
            $status = 'PAID';
            error_log("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            $receiptId =  $updateInfo['receipt'];

            error_log("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            $payments = get_posts([
                'post_type'  => 'payment', // or specify 'your_post_type' if needed
                'meta_key'   => 'receipt_id',
                'meta_value' => $receiptId,
                'fields'     => 'ids', // Only return post IDs
                'numberposts' => 1     // Limit to 1 result
            ]);

            // Check if any posts were found
            error_log("updatePaymentDetails - posts " . date("Y-m-d H:i:s") . " === ID: " . json_encode($payments) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            if (!empty($payments)) {
                $paymentId = $payments[0];
                error_log("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            }

            error_log("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            if ($paymentId) {
                update_post_meta($paymentId, 'payment_status', 'PAID');
                update_post_meta($paymentId, 'invoice_id', $updateInfo['invoice_id']);
                update_post_meta($paymentId, 'razorpay_payment_id', $updateInfo['payment_id']);
            }

            $userId = get_post_meta($paymentId, 'user_id', true);
            $courseId = get_post_meta($paymentId, 'course_id', true);
            $batchId = get_post_meta($paymentId, 'batch_id', true);
            $enrollmentId = get_post_meta($paymentId, 'enrollment_id', true);

            error_log("updatePaymentDetails - userId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($userId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - courseId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($courseId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - batchId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - enrollmentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // update enrollment status
            update_post_meta($enrollmentId, 'enrollment_status', 'ACTIVE');

            error_log("updatePaymentDetails - update_post_meta " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // get es enrollment
            $esQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.course_id" => $courseId]],
                            ["match" => ["data.details.batch_id" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.course_id",
                    "data.details.enrollment_status"
                ]
            ];

            error_log("updatePaymentDetails - esQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esQuery) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->customQuery($esQuery, 'batchenrollmentevent');

            error_log("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollmentESId = $enrollmentDataResponse['body']['hits']['hits'][0]['_id'] ?? '';
                error_log("updatePaymentDetails - enrollmentESId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentESId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            } else {
                error_log("Failed to fetch enrollment data from Elasticsearch.", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                //return false;
            }

            $paymentQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.batch_name" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.receipt_id",
                    "data.details.link_status"
                ]
            ];

            error_log("updatePaymentDetails - paymentQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentQuery) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            // Fetch payment data from Elasticsearch (or any other data source)
            $paymentDataResponse = $this->es->customQuery($paymentQuery, 'paymentsuccessevent');
            error_log("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if ($paymentDataResponse['status_code'] == 200) {
                $paymentEsId = $paymentDataResponse['body']['hits']['hits'][0]['_id'];
                error_log("updatePaymentDetails - paymentEsId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            } else {
                error_log("Failed to fetch payment data from Elasticsearch.", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                //return false;
            }

            error_log("updatePaymentDetails - paymentEsId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // Update Elasticsearch
            $paymentData = [
                "data" => [
                    "details" => [
                        "link_status" => 'PAID',
                        "invoice_id" => $updateInfo['invoice_id'],
                    ]
                ]
            ];

            error_log("updatePaymentDetails - paymentData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentData) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $paymentDataResponse = $this->es->update('paymentsuccessevent', $paymentEsId, $paymentData);
            error_log("updatePaymentDetails - paymentDataResponse after update call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $paymentEsUpdateSuccess = $paymentDataResponse['status_code'] == 200;
            error_log("updatePaymentDetails - paymentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            // Update Elasticsearch for enrollment
            $enrollmentData = [
                "data" => [
                    "details" => [
                        "enrollment_status" => 'ACTIVE'
                    ]
                ]
            ];
            $enrollmentDataResponse = $this->es->update('batchenrollmentevent', $enrollmentESId, $enrollmentData);
            error_log("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $enrollmentEsUpdateSuccess = $enrollmentDataResponse['status_code'] == 200;
            error_log("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            // Return true if all updates were successful
            if ($paymentEsUpdateSuccess && $enrollmentEsUpdateSuccess) {

                error_log("updatePaymentDetails - paymentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                error_log("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

                // If any update fails, return false
                error_log("updatePaymentDetails - update failed " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                $eventDetails = [
                    'user_id'   => $userId,
                    'action'    => 'Payment Success',
                    'target'    => 'payment',
                    'target_id' => $paymentEsId,
                    'timestamp' => $this->dt->currentSystemDT()
                ];
                error_log("updatePaymentDetails - trigger_custom_event " . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                // Trigger custom event
                //trigger_custom_event($eventDetails);
                error_log("updatePaymentDetails - trigger_custom_event after" . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            }

        } else if ($status === 'failed') {
            $status = 'FAILED';
            error_log("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            $receiptId =  $updateInfo['receipt'];
            error_log("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $payments = get_posts([
                'post_type'  => 'payment', // or specify 'your_post_type' if needed
                'meta_key'   => 'receipt_id',
                'meta_value' => $receiptId,
                'fields'     => 'ids', // Only return post IDs
                'numberposts' => 1     // Limit to 1 result
            ]);
            // Check if any posts were found
            error_log("updatePaymentDetails - posts " . date("Y-m-d H:i:s") . " === ID: " . json_encode($payments) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if (!empty($payments)) {
                $paymentId = $payments[0];
                error_log("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            }
            error_log("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if ($paymentId) {
                update_post_meta($paymentId, 'payment_status', 'FAILED');
                update_post_meta($paymentId, 'razorpay_payment_id', $updateInfo['payment_id']);
                update_post_meta($paymentId, 'invoice_id', $updateInfo['invoice_id']);
            }
            $userId = get_post_meta($paymentId, 'user_id', true);
            $courseId = get_post_meta($paymentId, 'course_id', true);
            $batchId = get_post_meta($paymentId, 'batch_id', true);
            $enrollmentId = get_post_meta($paymentId, 'enrollment_id', true);

            error_log("updatePaymentDetails - userId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($userId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - courseId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($courseId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - batchId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - enrollmentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // update enrollment status
            update_post_meta($enrollmentId, 'enrollment_status', 'INACTIVE');
            error_log("updatePaymentDetails - update_post_meta " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // get existing elasticsearch payment
            // get es enrollment
            $esQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.course_id" => $courseId]],
                            ["match" => ["data.details.batch_id" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.course_id",
                    "data.details.enrollment_status"
                ]
            ];

            error_log("updatePaymentDetails - esQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esQuery) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->customQuery($esQuery, 'batchenrollmentevent');
            error_log("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollmentESId = $enrollmentDataResponse['body']['hits']['hits'][0]['_id'] ?? '';
                error_log("updatePaymentDetails - enrollmentESId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentESId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            } else {
                error_log("Failed to fetch enrollment data from Elasticsearch.", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                //return false;
            }
            $paymentQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.batch_name" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.receipt_id",
                    "data.details.link_status"
                ]
            ];
            error_log("updatePaymentDetails - paymentQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentQuery) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            $paymentDataResponse = $this->es->customQuery($paymentQuery, 'paymentsuccessevent');
            error_log("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            if ($paymentDataResponse['status_code'] == 200) {
                $paymentEsId = $paymentDataResponse['body']['hits']['hits'][0]['_id'];
                error_log("updatePaymentDetails - paymentEsId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            } else {
                error_log("Failed to fetch payment data from Elasticsearch.", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
                //return false;
            }
            error_log("updatePaymentDetails - paymentEsId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // Update Elasticsearch
            $paymentData = [
                "data" => [
                    "details" => [
                        "link_status" => 'FAILED',
                        "invoice_id" => $updateInfo['invoice_id'],
                    ]
                ]
            ];
            error_log("updatePaymentDetails - paymentData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentData) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $paymentDataResponse = $this->es->update('paymentsuccessevent', $paymentEsId, $paymentData);
            error_log("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            $esUpdateSuccess = $paymentDataResponse['status_code'] == 200;
            error_log("updatePaymentDetails - esUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");

            // Update Elasticsearch for enrollment
            $enrollmentData = [
                "data" => [
                    "details" => [
                        "enrollment_status" => 'INACTIVE'
                    ]
                ]
            ];
            $enrollmentDataResponse = $this->es->update('batchenrollmentevent', $enrollmentESId, $enrollmentData);
            $enrollmentEsUpdateSuccess = $enrollmentDataResponse['status_code'] == 200;
            error_log("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            error_log("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            // Trigger custom event
            $eventDetails = [
                'user_id'   => $userId,
                'action'    => 'Payment Failed',
                'target'    => 'payment',
                'target_id' => $paymentEsId,
                'timestamp' => $this->dt->currentSystemDT()
            ];

        } else {
            //$status = 'FAILED';
            error_log("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
        }
        error_log("updatePaymentDetails - trigger_custom_event " . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
            // Trigger custom event
        trigger_custom_event($eventDetails);
        error_log("updatePaymentDetails - trigger_custom_event after" . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails) . "\n\n", 3, ABSPATH . "error-logs/updatePaymentDetails.log");
    }
    /**
     * Generates filter options for payment status selection.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @param string $paymentStatus The currently selected payment status.
     * @return array An array containing payment status filter data.
     *
     * The filter options include:
     * - "All": Show all payment statuses.
     * - "Pending": Show payments that are pending.
     * - "Full": Show fully completed payments.
     */
    public function generateEnrollmentPaymentFilters($paymentStatus)
    {
        return [
            'filter' => 'payment_status',
            'title' => 'Payment Status',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Payment Status',
            'ui_control_type' => 'dropdown',
            'selected' => $paymentStatus,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All', 'filter' => 'payment_status'],
                ['slug' => 'pending', 'label' => 'Pending', 'filter' => 'payment_status'],
                ['slug' => 'full', 'label' => 'Full', 'filter' => 'payment_status']
            ]
        ];
    }

    /**
     * Generates filter options for selecting a payment date range.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @return array An array containing date range filter options for payment records.
     *
     * The available date ranges include:
     * - "Last 7 Days"
     * - "Last 15 Days"
     * - "Last 30 Days"
     * - "Last 60 Days"
     */
    public function generatePaymentDaysFilters($paymentDays)
    {
        return [
            'filter' => 'payment_days',
            'title' => 'Payment Date Range',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Date Range',
            'ui_control_type' => 'dropdown',
            'selected' => $paymentDays,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 7, 'label' => 'Last 7 Days', 'filter' => 'payment_days'],
                ['slug' => 15, 'label' => 'Last 15 Days', 'filter' => 'payment_days'],
                ['slug' => 30, 'label' => 'Last 30 Days', 'filter' => 'payment_days'],
                ['slug' => 60, 'label' => 'Last 60 Days', 'filter' => 'payment_days']
            ]
        ];
    }
}
