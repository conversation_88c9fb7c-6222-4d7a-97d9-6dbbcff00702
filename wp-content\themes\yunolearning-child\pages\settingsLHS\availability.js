Vue.component("yuno-availability-tab", {
  props: {
    storage: {
      type: Object,
      required: true,
    },
  },
  template: `
		<div>
		  <yuno-availability-v3 :data="resources" :options="{ tab: resources, isSignup: false }"></yuno-availability-v3>
		</div>
	  `,
  data() {
    return {};
  },
  computed: {
    ...Vuex.mapState([
      "userInfo",
      "user",
      "header",
      "timeSlots",
      "resources",
      "filterResult",
    ]),
    wpThemeURL() {
      return this.$store.state.themeURL;
    },
  },
  async created() {
    this.fetchAvailability();
  },
  methods: {
    formatTime(time) {
      if (!time) return "";
      const [hours, minutes] = time.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const formattedHour = hour % 12 || 12;
      return `${formattedHour}:${minutes} ${ampm}`;
    },
    setStorage() {
      const storage = this.$props.storage;
      const store = {
        currentTab: this.filterResult.tabs.activeTab,
      };

      setTimeout(() => {
        sessionStorage.setItem(
          storage.name + "V" + storage.version,
          JSON.stringify(store)
        );
      }, 100);
    },
    onTabChanged(e) {
      this.resources.data = [];
      this.resources.success = false;
      this.resources.error = null;
      this.setStorage();
    },
    normalizeAvailability(day) {
      // Convert time_slot to time_slots if necessary
      if (day.time_slot && !day.time_slots) {
        day.time_slots = day.time_slot.map((slot) => ({
          id: "0",
          startsAt: this.formatTime(slot.start.time) || "",
          endsAt: this.formatTime(slot.end.time) || "",
          start: slot.start.time || "",
          end: slot.end.time || "",
          isActive: day.is_available,
          slotID: 0,
          isEnds: true,
          isExist: false,
          isOverlapping: false,
          isEndTime: false,
        }));
      }

      day.time_slots.forEach((slot) => {
        slot.isOverlapping = false;
        slot.isEndTime = false;
        slot.isActive = day.is_available;

        if (slot.start && slot.end) {
          const getStart = YUNOCommon.findObjectByKey(
            this.timeSlots.data,
            "slug",
            slot.start
          );
          const getEnd = YUNOCommon.findObjectByKey(
            this.timeSlots.data,
            "slug",
            slot.end
          );

          if (getStart) slot.startsAt = getStart.label;
          if (getEnd) slot.endsAt = getEnd.label;
        }

        slot.isEnds = true;
        slot.isExist = true;
      });

      day.daySlug = day.day?.substring(0, 3).toLowerCase();
    },
    refineTimeSlot() {
      const days = this.resources.data.days;
      days.forEach(this.normalizeAvailability);
    },
    gotTimeSlots(options, isUpdated) {
      if (isUpdated || options?.response?.data?.code === 200) {
        this.refineTimeSlot();
      }
    },
    fetchTimeSlots() {
      const options = {
        apiURL: YUNOCommon.config.timeSlotsAPI(),
        module: "gotData",
        store: "timeSlots",
        callback: true,
        callbackFunc: (options) => this.gotTimeSlots(options, false),
      };

      this.$store.dispatch("fetchData", options);
    },
    gotAvailability(options) {
      const response = options?.response?.data;
      if (response?.code === 200) {
        const getData = response.data;
        const days = getData.days;

        getData.hasRecord = days.some((d) => d?.is_available);

        days.forEach(this.normalizeAvailability);
        this.resources.data = getData;

        if (this.timeSlots.data.length === 0) {
          this.fetchTimeSlots(getData);
        } else {
          this.gotTimeSlots(false, true);
        }
      }
    },
    fetchAvailability() {
      const options = {
        apiURL: YUNOCommon.config.instructor("getInstructorAvailability", {
          id: isLoggedIn,
        }),
        module: "gotData",
        store: "resources",
        callback: true,
        addToModule: false,
        callbackFunc: (options) => this.gotAvailability(options),
      };

      this.$store.dispatch("fetchData", options);
    },
  },
});
