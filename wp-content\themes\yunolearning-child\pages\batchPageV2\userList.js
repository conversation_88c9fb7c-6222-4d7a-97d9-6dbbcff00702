Vue.component('yuno-user-list', {
    props: ["data", "options"],
    template: `
        <div class="userList">
            <figure class="mappedInstructor">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        :src="data.instructor.image" 
                        :alt="data.instructor.name">
                </div>
                <figcaption>
                    <small class="studentCount">instructor</small>
                    <h3 class="insName">{{ data.instructor.name }}</h3>
                </figcaption>
            </figure>
            <figure class="mappedInstructor" v-if="false">
                <div class="imgWrapper">
                    <img 
                        width="44" 
                        height="44" 
                        src="https://lh3.googleusercontent.com/a-/AFdZucqaT_XL5RJsrCMlYly_jIvNtgymihOsjzVEOH2J=s96-c" 
                        alt="asd">
                </div>
                <figcaption>
                    <small class="studentCount">counselor</small>
                    <h3 class="insName"><PERSON><PERSON></h3>
                </figcaption>
            </figure>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});