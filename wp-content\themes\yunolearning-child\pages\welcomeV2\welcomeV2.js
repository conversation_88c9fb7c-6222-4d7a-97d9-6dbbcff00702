window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 15 numbers required ",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-welcome-v2', {
    template: `
        <div>
            <yuno-page-loader v-if="isPageLoading"></yuno-page-loader>
            <div class="pageGrid" v-show="isPageReady">
                <template v-if="isUserAuthorized">
                    <yuno-loader></yuno-loader>
                    <header class="simpleHeader">
                        <img width="106" height="50" :src="logoURL" alt="Yuno Learning">
                    </header>
                    <main id="yunoMain" class="mainBody">
                        <section class="container welcomeWrapper">
                            <div class="mainHeader" v-if="false">
                                <h1 class="title">{{ "Welcome " + userInfo.data.yuno_display_name }}</h1>
                            </div> 
                            <div class="row">
                                <div class="col-12 col-md-8" v-if="false">
                                    <div class="videoLPPlayer">
                                        <iframe width="800" height="450" src="https://www.youtube.com/embed/AThKzwLsdIs?si=3npgIKLuCCPEF1bG" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                    </div>
                                </div>    
                                <div class="col-12 col-md-4 vMiddle offset-md-4" style="text-align: center;">
                                    <div class="mainHeader">
                                        <h1 class="title">{{ "Welcome " + userInfo.data.yuno_display_name }}</h1>
                                    </div> 
                                    <div class="details">
                                        <p>
                                            Welcome to Yuno Learning. You’re an administrator of your organization.
                                        </p>
                                        <b-button
                                            tag="a"
                                            href="/enrollments/all"
                                            class="yunoSecondaryCTA fat">
                                            Go to Dashboard
                                        </b-button>    
                                    </div>
                                </div>    
                            </div>    
                        </section>
                    </main>
                </template>
                <template v-else>
                    <div class="container">
                        <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                    </div>
                </template>
            </div>
        </div>
    `,
    data() {
        return {
            apiURL: null,
            logoURL: "https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",
            isMiniSidebar: false,
            pageHeader: {
                title: "Org Settings",
            },
            authorizedRoles: [
                "org-admin"
            ],
            defaultTaxonomy: 0,
            industry: null
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'loader',
            'form',
        ]),
        isUserAuthorized: {
            get() {
                return YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data);
            }
        },
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading: {
            get() {
                const module =  this.userInfo.loading
                                
                return module
            }
        },
        isPageReady: {
            get() {
                let module = "";

                if (this.user.isLoggedin) {
                    module = this.userInfo.success
                } else {
                    module = true
                }

                return module
            }
        }
    },
    async created() {
        this.init();    
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        init() {
            this.loginStatus();
        },
        gotUserInfo(options) {
            const { response: { data: { code, data } = {} } = {} } = options;
            if (code === 200) {
                this.userRole.data = data.role;

                const { host } = YUNOCommon.config;
                const { has_org, org_id } = this.userInfo.data;

                if (has_org && org_id.length === 1) {
                    sessionStorage.setItem('activeOrg', JSON.stringify(org_id[0].id));
                }; 
            }
        },
        fetchUserInfo() {
            const options = { 
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: this.gotUserInfo
            };
        
            this.$store.dispatch('fetchData', options); 
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                this.fetchUserInfo();
            } else {
                this.user.isLoggedin = false;
            }

            const stepsBegin = YUNOCommon.getQueryParameter("stepsBegin")

            if (this.user.isLoggedin && stepsBegin) {
                this.stepsBegin = true;
            }
        },
    }
});