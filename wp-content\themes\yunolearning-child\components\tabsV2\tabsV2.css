#app .fontColorDark, #app .yunoTabsV5 ul li.is-active a {
  color: #201A19;
}

#app .fontColorDarkVariant {
  color: #534342;
}

#app .fontColorDarkVariant2, #app .yunoTabsV5 ul li a {
  color: rgba(0, 0, 0, 0.38);
}

#app .yunoTabsWrapper {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background-color: #FFF;
  z-index: 2;
}

#app .yunoTabsWrapper.tabsNotAvailable .b-tabs.yunoTabsV5 > .tabs {
  display: none;
}

#app .yunoTabsWrapper.tabsNotAvailable .tab-content {
  padding-top: 0;
}

#app .yunoTabsWrapper.tabsNotAvailable .mainHeader {
  margin-top: 0;
}

#app .yunoTabsWrapper.tabsAvailable .mainHeader {
  margin-top: 0;
}

#app .yunoTabsV5 {
  margin-top: 15px;
}

#app .yunoTabsV5 ul li a {
  font-size: 14px;
}

#app .yunoTabsV5 ul li.is-active a {
  border-bottom-color: #201A19;
}

#app .yunoTabsV5.noContent .tab-content {
  display: none;
}
/*# sourceMappingURL=tabsV2.css.map */