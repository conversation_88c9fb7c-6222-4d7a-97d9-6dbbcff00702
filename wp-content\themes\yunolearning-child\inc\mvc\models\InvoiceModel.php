<?php
namespace V4;

class InvoiceModel extends Model
{
    /**
     * Constructor to initialize the TaxModel
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('locale');
        $this->loadLibary('schema');
    }

    public function getListPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $basePrice = isset($query['basePrice']) ? $query['basePrice'] : 0;
        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;

        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){

            //override tax percentage if passed
            if($taxPer !== null) {
                $tax['percentage'] = $taxPer;
            }

            $tax['amount'] = round($basePrice * $tax['percentage'], 2);

            $inclusiveTax = $basePrice + $tax['amount'];

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => round($basePrice, 2), // The price exclusive of tax
                'inclusive_tax' => round($inclusiveTax, 2), // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
            ];

            return $this->schema->validate($responseData, 'Price_List', $filters);
        }

        return false;
    }

    /**
     * Get the price details based on the query and filters
     *
     * @param array $query The query parameters
     * @param array $filters The filters to apply
     * @return array|false The price details or false if not found
     */
    public function getSellPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $basePrice = isset($query['basePrice']) ? $query['basePrice'] : 0;
        $discount = isset($query['discount']) ? $query['discount'] : 0;
        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;

        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){

            if(str_contains($discount, '.') && preg_match('/^\d+(\.\d{1,2})?$/', $discount)) {
                $discountPer = (float) $discount;
                $discountAmt = $basePrice * $discountPer;
                $discountedPrice = $basePrice - ($basePrice * $discountPer);
            } else {
                if((int) $basePrice == 0){
                    $discountPer = 0;
                }else{
                    $discountPer = $discount / $basePrice * 100;
                }
                $discountAmt = $discount;
                $discountedPrice = $basePrice - $discount;
                $discountedPrice = ($discountedPrice < 0) ? 0 : $discountedPrice;
            }

            // If discounted price is negative, set it to 0
            if($discountedPrice < 0) {
                $discountedPrice = 0;
            }

            //override tax percentage if passed
            if($taxPer !== null) {
                $tax['percentage'] = $taxPer;
            }

            $tax['amount'] = round($discountedPrice * $tax['percentage'], 2);

            $inclusiveTax = $discountedPrice + $tax['amount'];

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => round($discountedPrice, 2), // The price exclusive of tax
                'inclusive_tax' => round($inclusiveTax, 2), // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
                'discount' => [
                    'amount' => round($discountAmt, 2), // The amount of discount, default: 0
                    'percentage' => round($discountPer, 2), // The percentage of discount, default: 0
                ]
            ];

            return $this->schema->validate($responseData, 'Price', $filters);
        }

        return false;
    }

    public function displayPrice($query, $filters = [])
    {
        $this->loadModel('locale');

        $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
        $currency = $this->localeModel->getCurrency($ccCode);
        $tax = $this->localeModel->getTax($ccCode);

        $enclusiveTax = isset($query['enclusiveTax']) ? $query['enclusiveTax'] : 0;
        $inclusiveTax = isset($query['inclusiveTax']) ? $query['inclusiveTax'] : 0;

        $taxPer = isset($query['taxPer']) ? $query['taxPer'] : null;
        $taxAmt = isset($query['taxAmt']) ? $query['taxAmt'] : null;

        

        // If tax and currency details are fetched
        if($tax!==false && $currency!==false){
            
            $tax['percentage'] = $taxPer;
            $tax['amount'] = round($taxAmt, 2);

            $responseData = [
                'currency' => $currency, // Currency details fetched from a reference
                'exclusive_tax' => round($enclusiveTax, 2), // The price exclusive of tax
                'inclusive_tax' => round($inclusiveTax, 2), // The price inclusive of tax
                'tax' => $tax, // Tax details fetched from a reference
            ];

            if(isset($query['discountAmt']) && isset($query['discountPer'])){
                $responseData['discount'] = [
                    'amount' => round($query['discountAmt'] ?? 0, 2), // The amount of discount, default: 0
                    'percentage' => round($query['discountPer'] ?? 0.0, 2), // The percentage of discount, default: 0
                ];
                return $this->schema->validate($responseData, 'Price', $filters);
            }
            return $this->schema->validate($responseData, 'Price_List', $filters);
        }

        return false;
    }
}