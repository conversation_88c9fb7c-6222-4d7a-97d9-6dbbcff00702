const YUNOAvailability = (function ($) {
  const availability = function () {
    Vue.component("yuno-availability-v3", {
      props: ["data", "options"],
      template: `
				<div class="availabilityWrapper">
					<template v-if="isFormLoading">
						<div class="smallLoader"></div>
					</template>
					<template v-if="isFormReady">
						<validation-observer tag="div" ref="availabilityObserver" v-slot="{ handleSubmit }">
							<form id="formNewAddress" @submit.prevent="handleSubmit(availabilityInit)">
								<ul class="hoursWrapper">
									<li class="item" v-for="(item, index) in data.data.days" :key="index">
										<div class="itemWrapper">
											<div class="day">{{item.name}}</div>
											<div class="slots">
												<b-field>
													<b-switch v-model="item.is_available"
														:true-value="true"
														@input="onOpen($event, item)"
														:false-value="false">
														<template v-if="!item.is_available">
															Unavailable
														</template>
														<template v-else>
															Available
														</template>
													</b-switch>
												</b-field>
											</div>
										</div>
										<div class="hours" v-if="item.is_available"> 
											<template v-for="(slot, slotIndex) in item.time_slots">
												<div class="hourWrapper" :key="slotIndex" v-if="slot.isActive">
													<validation-provider 
														:customMessages="{ required: message.required }" 
														tag="div"
														class="chooseHour" 
														:rules="{required:true, isOverlapping: slot.isOverlapping}" 
														v-slot="{ errors, classes }"
													>
														<div :class="{ hasError: errors && errors.length > 0 }">
															<b-dropdown
																:class="classes"
																:key="'start-' + slotIndex"
																v-model="slot.startsAt"
																aria-role="list"
																class="filterMenu"
															>
																<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
																	<span><template v-if="!item.is24Hours">Start</template> {{slot.startsAt}}</span>
																	<b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
																</button>
																<template v-for="(time, timeIndex) in timeSlots.data">
																	<b-dropdown-item 
																		@click="onFilterItemSelect(slot, time, 'start', item)"
																		:value="time.label"
																		:key="timeIndex"
																		aria-role="listitem">
																		<span>{{time.label}}</span>
																	</b-dropdown-item>
																</template>
															</b-dropdown>
															<p class="error" v-if="false">{{errors[0]}}</p>
														</div>
													</validation-provider>
													<validation-provider 
														:customMessages="{ required: message.required, is_not: message.isNot }" 
														tag="div" 
														v-if="slot.isEnds"
														class="chooseHour"
														:rules="{required:true, is_not: slot.startsAt, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" 
														v-slot="{ errors, classes }">
														<div :class="{ hasError: errors && errors.length > 0 }">
															<b-dropdown
																:class="classes"
																v-if="slot.isEnds"
																:key="'end-' + slotIndex"
																v-model="slot.endsAt"
																aria-role="list"
																class="filterMenu"
															>
																<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
																	<span>End {{slot.endsAt}}</span>
																	<b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
																</button>
																<template v-for="(time, timeIndex) in timeSlots.data">
																	<b-dropdown-item 
																		@click="onFilterItemSelect(slot, time, 'end', item)"
																		:value="time.label"
																		:key="timeIndex"
																		aria-role="listitem">
																		<span>{{time.label}}</span>
																	</b-dropdown-item>
																</template>
															</b-dropdown>
															<p class="error">{{errors[0]}}</p>
														</div>
													</validation-provider>
													<b-button 
														@click="removeSlot(item, slot , slotIndex)" 
														class="yunoPrimaryCTA iconOnly removeSlot noBG">
														<span class="material-icons-outlined">close</span>
													</b-button>
													<b-tooltip label="Copy time to all"
														type="is-dark"
														position="is-top">
														<b-button 
															@click="copySlot(item, slot)" 
															v-if="slotIndex === 0"
															class="yunoPrimaryCTA iconOnly copySlot noBG">
															<span class="material-icons-outlined">content_copy</span>
														</b-button>    
													</b-tooltip>
												</div>
												<template v-if="slot.isEnds && slotIndex === item.time_slots.length - 1">
													<div class="addSlotWrapper">
														<b-button 
															v-if="slotIndex === item.time_slots.length - 1"
															@click="addSlot(item)" 
															class="yunoPrimaryCTA addSlot noBG">
															Add Hours
														</b-button>
													</div>
												</template>
											</template>
										</div>
									</li>
								</ul>
								<div class="ctaWrapper">
									<b-button
										native-type="submit"
										:loading="isLoading ? true : false"
										:disabled="isLoading ? true : false"
										class="yunoSecondaryCTA">
										Save
									</b-button>    
									<b-button
										v-if="typeof options.isPreview === 'undefined' || options.isPreview"
										@click="initPreview()"
										:disabled="data.data.hasRecord && preview.isMatched ? false : true" 
										class="yunoSecondaryCTA wired">
										Preview
									</b-button>    
								</div>
							</form>
						</validation-observer>
						<yuno-availability-preview :data="data.data" :options="preview"></yuno-availability-preview>
					</template>
				</div>
			  `,
      data() {
        return {
          isLoading: false,
          message: {
            required: "Required",
            isNot: "Value should not be same as start time",
            error: "",
          },
          slot: {
            id: "0",
            startsAt: "",
            endsAt: "",
            start: "",
            end: "",
            isActive: true,
            slotID: "",
            isEnds: true,
            isExist: false,
            isOverlapping: false,
            isEndTime: false,
          },
          weeks: [],
          preview: {
            modal: false,
            title: "Availability",
            oldData: "",
            isMatched: true,
          },
        };
      },
      computed: {
        ...Vuex.mapState([
          "user",
          "userInfo",
          "userProfile",
          "userRole",
          "timeSlots",
          "instructorAvailability",
          "instructorAvailabilityGrid",
          "settings",
          "resources",
        ]),
        isFormLoading: {
          get() {
            const module = this.$props.data.loading || this.timeSlots.loading;
            return module;
          },
        },
        isFormReady: {
          get() {
            let module = this.$props.data.success && this.timeSlots.success;

            if (module) {
              this.initFormReady();
            }

            return module;
          },
        },
      },
      methods: {
        timeToMinutes(timeStr) {
          let [hours, minutes] = timeStr.split(":").map(Number);
          if (hours === 24 && minutes === 0) {
            return 24 * 60; // Handle 24:00 as end of day
          }
          return hours * 60 + minutes;
        },
        manageEndTime(slot) {
          let startMinutes = this.timeToMinutes(slot.start);
          let endMinutes = this.timeToMinutes(slot.end);

          // Handle times across midnight
          if (endMinutes < startMinutes) {
            endMinutes += 24 * 60; // Add 24 hours worth of minutes
          }

          if (endMinutes <= startMinutes) {
            slot.isEndTime = true;
          } else {
            slot.isEndTime = false;
          }
        },
        manageOverlappingSlot(slot, availablity) {
          const currentStart = this.timeToMinutes(slot.start);
          const currentEnd = this.timeToMinutes(slot.end);
          slot.isOverlapping = false;
          availablity.time_slots.forEach((existingSlot) => {
            if (existingSlot.slotID === slot.slotID) return;
            const existStart = this.timeToMinutes(existingSlot.start);
            const existEnd = this.timeToMinutes(existingSlot.end);
            if (currentStart < existEnd && existStart < currentEnd) {
              slot.isOverlapping = true;
            }
          });
        },
        copySlot(item, slot) {
          const isSignup = this.$props.options.isSignup;
          let tab = "";

          if (isSignup) {
            tab = this.instructorAvailabilityGrid.data;
          } else {
            tab = this.resources.data;
          }

          const days = tab.days;

          days.forEach((day) => {
            if (day.is_available) {
              const copiedTimeSlots = JSON.parse(
                JSON.stringify(item.time_slots)
              );
              const currentTimeSlots = day.time_slots;

              currentTimeSlots.forEach((element) => {
                element.isActive = false;
              });

              copiedTimeSlots.forEach((element) => {
                element.id = "0";
              });

              day.time_slots = copiedTimeSlots;
            }
          });
        },
        initFormReady() {
          if (this.preview.oldData === "") {
            this.preview.oldData = JSON.stringify(this.$props.data.data);
          }

          this.enablePreview(JSON.stringify(this.$props.data.data));
        },
        initPreview() {
          this.preview.modal = true;
        },
        availabilityDone(options) {
          this.isLoading = false;

          if (
            options.response !== undefined &&
            options.response.data !== undefined &&
            options.response.data.code === 201
          ) {
            this.$buefy.toast.open({
              duration: 5000,
              message: `${options.response.data.message}`,
              position: "is-bottom",
            });

            if (
              typeof this.options.isPreview === "undefined" ||
              this.options.isPreview
            ) {
              this.days = [];
              this.preview.oldData = "";
              Event.$emit(
                "initAvailability",
                this.$props.options.tab,
                this.$props.options.tabIndex
              );
            }
          } else {
            this.$buefy.dialog.alert({
              title: "Availability",
              message: `${options.response.data.message}`,
              confirmText: "Ok",
            });
          }
        },
        availabilityInit() {
          this.isLoading = true;

          const payload = JSON.parse(JSON.stringify(this.$props.data.data));
          const days = payload.days || [];
          const instance = this;

          let url = "";
          const props = {
            id: isLoggedIn,
          };
          if (payload.hasRecord) {
            url = YUNOCommon.config.instructor("updateAvailabilityV2", props);
          } else {
            url = YUNOCommon.config.instructor("createAvailabilityV2", props);
          }


          let normalizedPayload = {
            weeks: [],
          };

          for (const day of days) {
            const transformedDay = {
              day: day.name,
              availability: (day.time_slots || [])
                .map((slot) => {
                  // Check if both start and end are empty strings
                  if (slot.start === "" && slot.end === "") {
                    return null; // Return null to filter out this slot
                  }


                  // Format time to ensure it's in HH:MM format without seconds
                  const formatTime = (timeStr, slot) => {
                    if (!timeStr) return "";
                    // Convert 24:00 to 00:00
                    if (slot === "start" && timeStr === "24:00") {
                      return "00:00";
                    }
                    // If time already has seconds, remove them
                    if (timeStr.includes(":")) {
                      const parts = timeStr.split(":");
                      return parts.length > 2
                        ? `${parts[0]}:${parts[1]}`
                        : timeStr;
                    }
                    return timeStr;
                  };

                  return {
                    id: slot.id || "0",
                    start: formatTime(slot.start, "start"),
                    end: formatTime(slot.end, "end"),
                    is24Hours: false,
                    isActive: slot.isActive || false,
                  };
                })
                .filter(Boolean), // Filter out null values
            };
            normalizedPayload.weeks.push(transformedDay);
          }

          const options = {
            apiURL: url,
            module: "gotData",
            store: "instructorAvailability",
            payload: normalizedPayload,
            headers: {
              accept: "application/json",
              "content-type": "application/json",
            },
            callback: true,
            callbackFunc: function (options) {
              return instance.availabilityDone(options);
            },
          };

          this.$store.dispatch("postData", options);
        },
        onFilterItemSelect(slot, data, type, item) {
          slot[type] = data.slug;

          if (data.slug !== "24") {
            slot.isEnds = true;
            item.is24Hours = false;
          } else {
            item.time_slots = [];
            item.time_slots.push(slot);
            item.is24Hours = true;
            slot.isEnds = false;
          }

          this.enablePreview(JSON.stringify(this.$props.data.data));
          this.manageOverlappingSlot(slot, item);
          this.manageEndTime(slot);
        },
        removeSlot(item, slot, index) {
          const timeSlots = item.time_slots;

          timeSlots.splice(index, 1);

          for (let i = 0; i < timeSlots.length; i++) {
            const element = timeSlots[i];
            element.slotID = i;
          }

          if (timeSlots.length === 0) {
            item.is_available = false;
          }

          this.enablePreview(JSON.stringify(this.$props.data.data));
        },
        addSlot(item) {
          let totalItem =
              item.time_slots.length === 1 ? 0 : item.time_slots.length - 1,
            timeSlots = item.time_slots;
          const slotObj = JSON.parse(JSON.stringify(this.slot));

          slotObj.slotID = totalItem + 1;
          item.time_slots.push(slotObj);
          this.enablePreview(JSON.stringify(this.$props.data.data));
        },
        enablePreview(data) {
          if (data !== this.preview.oldData) {
            this.preview.isMatched = false;
          } else {
            this.preview.isMatched = true;
          }
        },
        onOpen(value, day) {
          if (value) {
            // Initialize time_slots array if it doesn't exist
            if (!day.time_slots) {
              day.time_slots = [];
            }

            // Always add a new slot when making available
            const slotObj = {
              id: "0",
              startsAt: "",
              endsAt: "",
              start: "",
              end: "",
              isActive: true,
              slotID: day.time_slots.length,
              isEnds: true,
              isExist: false,
              isOverlapping: false,
              isEndTime: false,
            };

            day.time_slots.push(slotObj);
            day.is_available = true;
          } else {
            // Clear time slots when making unavailable
            day.time_slots = [];
            day.is_available = false;
          }

          // Always update preview
          this.enablePreview(JSON.stringify(this.$props.data.data));
        },
      },
    });
  };

  return {
    availability: availability,
  };
})(jQuery);
