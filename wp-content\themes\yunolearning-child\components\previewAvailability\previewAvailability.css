.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.dark87 {
  color: rgba(0, 0, 0, 0.87);
}

.dark60 {
  color: rgba(0, 0, 0, 0.6);
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

.yunoPreviewAvailability {
  width: 100%;
  padding: 20px;
}
.yunoPreviewAvailability .days-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 5px;
  padding-right: 100px;
  border-bottom: 1px solid #e6e6e6;
}
.yunoPreviewAvailability .days-row .dayHeader {
  text-align: start;
  padding: 8px 0;
}
.yunoPreviewAvailability .grid-container {
  position: relative;
  padding-right: 100px;
}
.yunoPreviewAvailability .grid-container .slots-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-auto-rows: 32px;
  gap: 8px;
}
.yunoPreviewAvailability .grid-container .slots-grid .slot {
  width: auto;
  height: 26px;
  background-color: #E0E0E0;
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .yunoPreviewAvailability .grid-container .slots-grid .slot {
    width: 53px;
  }
}
.yunoPreviewAvailability .grid-container .slots-grid .slot.available {
  background-color: rgb(84, 172, 117);
}
.yunoPreviewAvailability .grid-container .time-labels {
  position: absolute;
  right: 0;
  top: 0;
  width: 90px;
}
.yunoPreviewAvailability .grid-container .time-labels .time-label {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}
@media screen and (max-width: 768px) {
  .yunoPreviewAvailability {
    padding: 10px;
  }
  .yunoPreviewAvailability .days-row {
    padding-right: 80px;
    gap: 4px;
  }
  .yunoPreviewAvailability .days-row .dayHeader {
    font-size: 12px;
    padding: 4px 0;
  }
  .yunoPreviewAvailability .grid-container {
    padding-right: 80px;
  }
  .yunoPreviewAvailability .grid-container .slots-grid {
    gap: 4px;
  }
  .yunoPreviewAvailability .grid-container .time-labels {
    width: 75px;
  }
  .yunoPreviewAvailability .grid-container .time-labels .time-label {
    font-size: 11px;
    margin-bottom: 4px;
  }
}/*# sourceMappingURL=previewAvailability.css.map */